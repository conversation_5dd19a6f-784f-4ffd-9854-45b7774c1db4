package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysSmsTemplate;
import com.zksr.system.controller.sms.vo.SysSmsTemplatePageReqVO;
import com.zksr.system.controller.sms.vo.SysSmsTemplateSaveReqVO;

/**
 * 短信模版配置Service接口
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
public interface ISysSmsTemplateService {

    /**
     * 新增短信模版配置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysSmsTemplate(@Valid SysSmsTemplateSaveReqVO createReqVO);

    /**
     * 修改短信模版配置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysSmsTemplate(@Valid SysSmsTemplateSaveReqVO updateReqVO);

    /**
     * 删除短信模版配置
     *
     * @param smsTemplateId 
     */
    public void deleteSysSmsTemplate(Long smsTemplateId);

    /**
     * 批量删除短信模版配置
     *
     * @param smsTemplateIds 需要删除的短信模版配置主键集合
     * @return 结果
     */
    public void deleteSysSmsTemplateBySmsTemplateIds(Long[] smsTemplateIds);

    /**
     * 获得短信模版配置
     *
     * @param smsTemplateId 
     * @return 短信模版配置
     */
    public SysSmsTemplate getSysSmsTemplate(Long smsTemplateId);

    /**
     * 获得短信模版配置分页
     *
     * @param pageReqVO 分页查询
     * @return 短信模版配置分页
     */
    PageResult<SysSmsTemplate> getSysSmsTemplatePage(SysSmsTemplatePageReqVO pageReqVO);

    /**
     * 获取短信模版
     * @param sysCode   平台编号
     * @param scene     使用场景
     * @param platform  短信平台 alibaba-阿里云
     * @return
     */
    SysSmsTemplate getSysSmsTemplate(Long sysCode, Integer scene, String platform);

    /**
     * 启用
     * @param smsTemplateId
     */
    void disable(Long smsTemplateId);

    /**
     * 停用
     * @param smsTemplateId
     */
    void enable(Long smsTemplateId);
}
