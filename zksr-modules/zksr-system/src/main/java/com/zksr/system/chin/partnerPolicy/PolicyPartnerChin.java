package com.zksr.system.chin.partnerPolicy;

import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyRespVO;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicySaveReqVO;

public abstract class PolicyPartnerChin {
    //下个节点
    PolicyPartnerChin next;

    //设置下一个调用者
    public void setNext(PolicyPartnerChin next) {
        this.next = next;
    }

    //抽象方法 保存配置数据
    public abstract void saveConfig(SysPartnerPolicySaveReqVO sysPartnerPolicySaveReqVO, Long sysCode, Long dcId, Long supplierId);

    // 事务之外清除数据
    public void clean(Long sysCode, Long dcId, Long supplierId) {

    }

    //抽象方法 获取配置信息
    public abstract SysPartnerPolicyRespVO getConfig(SysPartnerPolicyRespVO sysPartnerPolicyRespVO, Long sysCode, Long dcId, Long supplierId, Integer type);
}
