package com.zksr.system.controller.partnerPolicy.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/1/20 14:27
 * @注释
 */
@Data
@ApiModel("平台商搜索推荐配置Resp")
public class SearchConfigPageRespVO {

    /** 平台商政策id */
    @ApiModelProperty(value = "平台商政策id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long partnerPolicyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 配置名 */
    @Excel(name = "配置名")
    @ApiModelProperty(value = "配置名")
    private String policyName;

    /** 配置键名 */
    @Excel(name = "配置键名")
    @ApiModelProperty(value = "配置键名")
    private String policyKey;

    /** 配置值 */
    @Excel(name = "配置值")
    @ApiModelProperty(value = "配置值")
    private String policyValue;

    /** 区域城市ID*/
    @Excel(name = "区域城市ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 一级区域城市名称 */
    @Excel(name = "一级区域城市名称")
    @ApiModelProperty(value = "一级区域城市名称")
    private String areaName;

    /** 二级区域城市名称 */
    @Excel(name = "二级区域城市名称")
    @ApiModelProperty(value = "二级区域城市名称")
    private String areaName2;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 创建时间 */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
