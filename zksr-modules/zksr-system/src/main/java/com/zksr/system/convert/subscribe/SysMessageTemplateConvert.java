package com.zksr.system.convert.subscribe;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.third.message.dto.WechatMessageDTO;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.domain.SysMessageTemplate;
import com.zksr.system.controller.message.vo.SysMessageTemplateRespVO;
import com.zksr.system.controller.message.vo.SysMessageTemplateSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 公众号, 小程序订阅消息模版 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-13
*/
@Mapper
public interface SysMessageTemplateConvert {

    SysMessageTemplateConvert INSTANCE = Mappers.getMapper(SysMessageTemplateConvert.class);

    SysMessageTemplateRespVO convert(SysMessageTemplate sysMessageTemplate);

    SysMessageTemplate convert(SysMessageTemplateSaveReqVO sysMessageTemplateSaveReq);

    PageResult<SysMessageTemplateRespVO> convertPage(PageResult<SysMessageTemplate> sysMessageTemplatePage);

    MessageTemplateDTO convertDTO(SysMessageTemplate sysMessageTemplate);

    WechatMessageDTO convertAppletMessageDTO(CommonMessageDTO messageDTO);
}