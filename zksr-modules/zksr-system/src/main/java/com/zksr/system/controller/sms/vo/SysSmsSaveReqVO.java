package com.zksr.system.controller.sms.vo;

import lombok.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 短信消息对象 sys_sms
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Data
@ApiModel("短信消息 - sys_sms分页 Request VO")
public class SysSmsSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 短信ID */
    @ApiModelProperty(value = "发送结果")
    private Long smsId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 手机号 */
    @Excel(name = "手机号")
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 使用场景 */
    @Excel(name = "使用场景")
    @ApiModelProperty(value = "使用场景")
    private Integer scene;

    /** 发送时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "发送时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "发送时间")
    private Date useTime;

    /** 发送结果 */
    @Excel(name = "发送结果")
    @ApiModelProperty(value = "发送结果")
    private String msg;

}
