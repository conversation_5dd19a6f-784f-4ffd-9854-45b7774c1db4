package com.zksr.system.mapper;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysMessageTemplate;
import com.zksr.system.controller.message.vo.SysMessageTemplatePageReqVO;

import java.util.List;


/**
 * 公众号, 小程序订阅消息模版Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Mapper
public interface SysMessageTemplateMapper extends BaseMapperX<SysMessageTemplate> {
    default PageResult<SysMessageTemplate> selectPage(SysMessageTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysMessageTemplate>()
                    .eqIfPresent(SysMessageTemplate::getMessageTemplateId, reqVO.getMessageTemplateId())
                    .eqIfPresent(SysMessageTemplate::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(SysMessageTemplate::getStatus, reqVO.getStatus())
                    .eqIfPresent(SysMessageTemplate::getTemplateId, reqVO.getTemplateId())
                    .eqIfPresent(SysMessageTemplate::getConfig, reqVO.getConfig())
                    .eqIfPresent(SysMessageTemplate::getScene, reqVO.getScene())
                    .eqIfPresent(SysMessageTemplate::getReceiveMerchant, reqVO.getReceiveMerchant())
                    .in(SysMessageTemplate::getStatus, 0, 1)
                .orderByDesc(SysMessageTemplate::getMessageTemplateId));
    }

    default Long selectCountByScene(Integer scene) {
        return selectCount(new LambdaQueryWrapperX<SysMessageTemplate>()
                .eqIfPresent(SysMessageTemplate::getScene, scene)
                .eq(SysMessageTemplate::getStatus, NumberPool.INT_ONE)
                .orderByDesc(SysMessageTemplate::getMessageTemplateId));
    }

    default List<SysMessageTemplate> selectTemplateBySysCodeAndScene(Long sysCode, Long scene) {
        return selectList(new LambdaQueryWrapperX<SysMessageTemplate>()
                .eq(SysMessageTemplate::getScene, scene)
                .eq(SysMessageTemplate::getSysCode, sysCode)
                .eq(SysMessageTemplate::getStatus, NumberPool.INT_ONE)
                .orderByDesc(SysMessageTemplate::getMessageTemplateId));
    }
}
