package com.zksr.system.controller.column;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.security.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysUserColumn;
import com.zksr.system.service.ISysUserColumnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.column.vo.SysUserColumnPageReqVO;
import com.zksr.system.controller.column.vo.SysUserColumnSaveReqVO;
import com.zksr.system.controller.column.vo.SysUserColumnRespVO;
import com.zksr.system.convert.column.SysUserColumnConvert;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 用户头列配置Controller
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Api(tags = "管理后台 - 用户头列配置接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/column")
public class SysUserColumnController {
    @Autowired
    private ISysUserColumnService sysUserColumnService;

    /**
     * 新增用户头列配置
     */
    @ApiOperation(value = "新增用户头列配置", httpMethod = HttpMethod.POST)
    @Log(title = "用户头列配置", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public CommonResult<List<Long>> addBatch(@Valid @RequestBody List<SysUserColumnSaveReqVO> createReqVOList) {
        return success(sysUserColumnService.insertSysUserColumnBatch(createReqVOList));
    }


    /**
     * 删除用户头列配置
     */
    @ApiOperation(value = "删除用户头列配置", httpMethod = HttpMethod.DEL)
    @Log(title = "用户头列配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userColumnIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] userColumnIds) {
        sysUserColumnService.deleteSysUserColumnByUserColumnIds(userColumnIds);
        return success(true);
    }

    @ApiOperation(value = "根据表Code删除用户头列配置", httpMethod = HttpMethod.DEL)
    @Log(title = "用户头列配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/delByTableCode/{tableCode}")
    public CommonResult<Boolean> removeByTableCode(@PathVariable String tableCode) {
        sysUserColumnService.deleteSysUserColumnByTableCode(tableCode, SecurityUtils.getUsername());
        return success(true);
    }


    /**
     * 查询用户头列配置
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得用户头列配置列表", httpMethod = HttpMethod.GET)
    public CommonResult<List<SysUserColumnRespVO>> getList(@Valid SysUserColumnPageReqVO pageReqVO) {
        pageReqVO.setCreateBy(SecurityUtils.getUsername());
        List<SysUserColumn> list = sysUserColumnService.getSysUserColumnList(pageReqVO);
        return success(SysUserColumnConvert.INSTANCE.convertList(list));
    }
}
