package com.zksr.system.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.controller.dcArea.vo.SysDcAreaRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysDcArea;
import com.zksr.system.controller.dcArea.vo.SysDcAreaPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 运营商-区域城市关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Mapper
public interface SysDcAreaMapper extends BaseMapperX<SysDcArea> {
    default PageResult<SysDcArea> selectPage(SysDcAreaPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysDcArea>()
                    .eqIfPresent(SysDcArea::getDcId, reqVO.getDcId())
                    .eqIfPresent(SysDcArea::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(SysDcArea::getSysCode, reqVO.getSysCode())
                .orderByDesc(SysDcArea::getDcId));
    }

    /**
    * @Description: 获取运营商绑定的区域信息
    * @Author: liuxingyu
    * @Date: 2024/3/6 9:38
    */
    default List<Long> selectByDcId(Long dcId){
        List<SysDcArea> sysDcAreas = selectList(new LambdaQueryWrapper<SysDcArea>().eq(SysDcArea::getDcId, dcId));
        if (ObjectUtil.isEmpty(sysDcAreas)){
            return null;
        }
        return sysDcAreas.stream().map(SysDcArea::getAreaId).collect(Collectors.toList());
    }

    /**
    * @Description: 通过区域集合查询数据
    * @Author: liuxingyu
    * @Date: 2024/3/7 15:23
    */
    List<SysDcArea> selectListByAreas(@Param("sysAreaId") List<Long> sysAreaId);

    /**
    * @Description: 根据运营商ID删除关联关系
    * @Author: liuxingyu
    * @Date: 2024/3/11 16:28
    */
    default Integer deleteByDcId(Long dcId){
       return delete(new LambdaUpdateWrapper<SysDcArea>().eq(SysDcArea::getDcId,dcId));
    }

    /**
     * @Description: 根据区域ID删除关联关系
     * @Author: liuxingyu
     * @Date: 2024/3/11 16:28
     */
    default Integer deleteByAreaId(Long areaId){
        return delete(new LambdaUpdateWrapper<SysDcArea>().eq(SysDcArea::getAreaId,areaId));
    }

    default SysDcArea getDcArea(SysDcAreaRespVO respVO){
        return selectOne(new LambdaQueryWrapperX<SysDcArea>()
                 .eqIfPresent(SysDcArea::getAreaId,respVO.getAreaId())
                 .eqIfPresent(SysDcArea::getDcId,respVO.getDcId())
                 .eqIfPresent(SysDcArea::getSysCode,respVO.getSysCode()));
    }

    /**
    * @Description: 根据区域ID获取绑定关系
    * @Author: liuxingyu
    * @Date: 2024/4/15 19:57
    */
    default SysDcArea selectByAreaId(Long areaId){
        return selectOne(new LambdaUpdateWrapper<SysDcArea>().eq(SysDcArea::getAreaId,areaId));
    }
}
