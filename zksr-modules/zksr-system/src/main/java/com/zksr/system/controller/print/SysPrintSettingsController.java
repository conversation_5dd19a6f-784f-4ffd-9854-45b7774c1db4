package com.zksr.system.controller.print;

import javax.validation.Valid;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysPrintSettings;
import com.zksr.system.service.ISysPrintSettingsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.print.vo.SysPrintSettingsPageReqVO;
import com.zksr.system.controller.print.vo.SysPrintSettingsSaveReqVO;
import com.zksr.system.controller.print.vo.SysPrintSettingsRespVO;
import com.zksr.system.convert.printSettings.SysPrintSettingsConvert;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商打印设置Controller
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
@Api(tags = "管理后台 - 平台商打印设置接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/printSettings")
public class SysPrintSettingsController {
    @Autowired
    private ISysPrintSettingsService sysPrintSettingsService;

    /**
     * 新增平台商打印设置
     */
    @ApiOperation(value = "新增平台商打印设置", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台商打印设置", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysPrintSettingsSaveReqVO createReqVO) {
        return success(sysPrintSettingsService.insertSysPrintSettings(createReqVO));
    }

    /**
     * 修改平台商打印设置
     */
    @ApiOperation(value = "修改平台商打印设置", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台商打印设置", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysPrintSettingsSaveReqVO updateReqVO) {
        sysPrintSettingsService.updateSysPrintSettings(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台商打印设置
     */
    @ApiOperation(value = "删除平台商打印设置", httpMethod = HttpMethod.DEL, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台商打印设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{printSetterIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] printSetterIds) {
        sysPrintSettingsService.deleteSysPrintSettingsByPrintSetterIds(printSetterIds);
        return success(true);
    }

    /**
     * 获取平台商打印设置详细信息
     */
    @ApiOperation(value = "获得平台商打印设置详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{printSetterId}")
    public CommonResult<SysPrintSettingsRespVO> getInfo(@PathVariable("printSetterId") Long printSetterId) {
        SysPrintSettings sysPrintSettings = sysPrintSettingsService.getSysPrintSettings(printSetterId);
        return success(SysPrintSettingsConvert.INSTANCE.convert(sysPrintSettings));
    }

    /**
     * 分页查询平台商打印设置
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商打印设置分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysPrintSettingsRespVO>> getPage(@Valid SysPrintSettingsPageReqVO pageReqVO) {
        PageResult<SysPrintSettings> pageResult = sysPrintSettingsService.getSysPrintSettingsPage(pageReqVO);
        return success(SysPrintSettingsConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 根据打印
     */
    @GetMapping("/getModuleType")
    @ApiOperation(value = "通过模版类型获取平台商打印设置列表", httpMethod = HttpMethod.GET)
    public CommonResult<List<SysPrintSettingsRespVO>> getModuleType(@Valid SysPrintSettingsPageReqVO pageReqVO) {
        List<SysPrintSettings> list = sysPrintSettingsService.getModuleType(pageReqVO);
        return success(SysPrintSettingsConvert.INSTANCE.convertRespListVO(list));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "print:printSettings:add";
        /**
         * 编辑
         */
        public static final String EDIT = "print:printSettings:edit";
        /**
         * 删除
         */
        public static final String DELETE = "print:printSettings:remove";
        /**
         * 列表
         */
        public static final String LIST = "print:printSettings:list";
        /**
         * 查询
         */
        public static final String GET = "print:printSettings:query";
        /**
         * 停用
         */
        public static final String DISABLE = "print:printSettings:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "print:printSettings:enable";
    }
}
