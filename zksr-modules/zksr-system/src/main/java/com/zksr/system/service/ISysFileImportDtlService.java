package com.zksr.system.service;

import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.form.SysFileImportDtlForm;

import java.util.List;

/**
 * 导入记录 业务层
 * 
 * <AUTHOR>
 */
public interface ISysFileImportDtlService
{

    Boolean batchAdd(List<SysFileImportDtl> list);

    List<SysFileImportDtl> getList(SysFileImportDtlForm form);

    SysFileImportDtl getById(Long importTypeDtlId);

}
