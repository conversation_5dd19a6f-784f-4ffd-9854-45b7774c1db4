package com.zksr.system.controller;

import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysDept;
import com.zksr.system.service.ISysDeptService;
import com.zksr.system.service.IWxQrDataService;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@RestController
@RequestMapping("/wxQrData")
@ApiIgnore
public class WxQrDataController extends BaseController
{
    @Autowired
    private IWxQrDataService wxQrDataService;


    @GetMapping("/add")
    @ApiOperation("新增")
    public AjaxResult add(@RequestParam("qrValue") String qrValue)
    {
        return success(wxQrDataService.save(qrValue));
    }

    @GetMapping("/getByQrKey")
    @ApiOperation("查询")
    public AjaxResult getByQrKey(@RequestParam("qrKey") String qrKey)
    {
        return success(wxQrDataService.getByQrKey(qrKey));
    }



}
