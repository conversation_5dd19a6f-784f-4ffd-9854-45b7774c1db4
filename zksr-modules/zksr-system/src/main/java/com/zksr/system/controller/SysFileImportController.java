package com.zksr.system.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.page.TableDataInfo;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysPartnerDictType;
import com.zksr.system.api.form.SysFileImportDtlForm;
import com.zksr.system.api.form.SysFileImportForm;
import com.zksr.system.service.ISysFileImportService;
import com.zksr.system.service.ISysPartnerDictTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 导入记录
 * 
 * <AUTHOR>
 */
@RestController
@Api(tags = "管理后台 - 导入记录接口", produces = "application/json")
@RequestMapping("/sysFileImport")
public class SysFileImportController extends BaseController
{
    @Autowired
    private ISysFileImportService sysFileImportService;

    @PostMapping("/createFileImportTask")
    @ApiOperation("创建导入任务")
    public AjaxResult createFileImportTask(MultipartFile file,String importType) throws Exception {
        SysFileImport fileImportTask = sysFileImportService.createFileImportTask(file, importType);
        return success(fileImportTask);
    }

    @GetMapping("/getList")
//    @RequiresPermissions("system:partnerDict:list")
    @ApiOperation("查询列表")
    public TableDataInfo getList(SysFileImportForm sysFileImportForm)
    {
        Page<SysFileImport> page = PageHelper.startPage(sysFileImportForm.getPageNo(), sysFileImportForm.getPageSize());
        List<SysFileImport> list = sysFileImportService.getList(sysFileImportForm);
        return getDataTable(list);
    }

    @GetMapping(value = "/{importTypeId}")
    @ApiOperation("查询详细")
    public AjaxResult getInfo(@PathVariable Long importTypeId)
    {
        return success(sysFileImportService.getById(importTypeId));
    }

    @GetMapping(value = "/eventRetry")
    @ApiOperation("消息重发")
    public AjaxResult eventRetry(@RequestParam("importTypeId") Long importTypeId)
    {
        sysFileImportService.eventRetry(importTypeId);
        return success();
    }


}
