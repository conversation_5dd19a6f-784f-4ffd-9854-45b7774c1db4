package com.zksr.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.http.MideaApiHttpClient;
import com.zksr.system.api.invoice.dto.InvoiceStandardRecognizeDTO;
import com.zksr.system.api.invoice.dto.TransBlueInvoiceRequest;
import com.zksr.system.api.invoice.dto.TransRedInvoiceRequest;
import com.zksr.system.api.invoice.dto.InvoiceQueryRequest;
import com.zksr.system.api.invoice.dto.InvoiceBaseQueryResDTO;
import com.zksr.system.api.invoice.dto.InvoiceVerifyRequest;
import com.zksr.system.api.invoice.dto.InvoiceVerifyResponse;
import com.zksr.system.config.MideaInvoiceApiConfig;
import com.zksr.system.service.ISysMideaInvoiceService;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.multipart.MultipartFile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @Description: 美的发票服务
 * @Date: 2025/07/16
 */
@Slf4j
@Service
public class SysMideaInvoiceServiceImpl implements ISysMideaInvoiceService {
    @Autowired
    private MideaInvoiceApiConfig mideaInvoiceApiConfig;

    private MideaApiHttpClient httpClient;

    private final Map<String, String> queryParams = Maps.newHashMap();

    @PostConstruct
    public void init() {
        // 验证配置
//        mideaPayApiConfig.validate();

        // 创建RestTemplate
        RestTemplate restTemplate = new RestTemplateBuilder()
                .setConnectTimeout(Duration.ofMillis(mideaInvoiceApiConfig.getConnectTimeout()))
                .setReadTimeout(Duration.ofMillis(mideaInvoiceApiConfig.getReadTimeout()))
                .additionalMessageConverters(new StringHttpMessageConverter(StandardCharsets.UTF_8), new MappingJackson2HttpMessageConverter())
                .build();

        // 创建美的API HTTP客户端
        this.httpClient = MideaApiHttpClient.builder()
                .restTemplate(restTemplate)
                .merchantId(mideaInvoiceApiConfig.getMerId())
                .appId(mideaInvoiceApiConfig.getAppId())
                .version(mideaInvoiceApiConfig.getVersion())
                .privateKey(mideaInvoiceApiConfig.getPrivateKey())
                .publicKey(mideaInvoiceApiConfig.getPublicKey())
                .baseUrl(mideaInvoiceApiConfig.getBaseUrl())
                .build();

        // 设置appId
        queryParams.put("app_id", mideaInvoiceApiConfig.getAppId());
        queryParams.put("merchant_id", mideaInvoiceApiConfig.getMerId());
    }

    /**
     * 蓝票开具
     */
    @SuppressWarnings("all")
    @Override
    public Boolean transBlueInvoice(TransBlueInvoiceRequest request) {
        try {
            log.info("蓝票开具, request: {}", JSON.toJSONString(request));

            MideaApiHttpClient.MideaApiResponse response = httpClient.post(
                    mideaInvoiceApiConfig.getInvoice().getTransBlueInvoicePath(),
                    queryParams,
                    request
            );

            if (response.isOk()) {
//                if (mideaPayApiConfig.isEnableSignatureVerification() && !response.isSignatureValid()) {
//                    log.warn("蓝票开具响应签名验证失败, requestId: {}", response.getRequestId());
//                }

                // 解析标准响应
                MideaApiHttpClient.MideaStandardResponse standardResponse =
                        response.parseStandardResponse();
                log.info("蓝票开具结果, result: {}", JSON.toJSONString(standardResponse));
                if (standardResponse != null && standardResponse.isSuccess()) {
                    // 蓝票开具成功，返回成功响应
                    return true;
                } else if (standardResponse != null) {
                    handleApiError(standardResponse);
                } else {
                    throw new ServiceException("蓝票开具响应为空");
                }
            } else {
                log.error("蓝票开具请求失败: {}", response.getErrorMessage());
                throw new ServiceException("蓝票开具请求失败: " + response.getErrorMessage());
            }

            return false;
        } catch (Exception e) {
            log.error("蓝票开具异常", e);
            throw new ServiceException("蓝票开具异常");
        }
    }

    /**
     * 红票开具/红冲
     */
    @SuppressWarnings("all")
    @Override
    public Boolean transRedInvoice(TransRedInvoiceRequest request) {
        try {
            log.info("红票开具/红冲, request: {}", JSON.toJSONString(request));

            MideaApiHttpClient.MideaApiResponse response = httpClient.post(
                    mideaInvoiceApiConfig.getInvoice().getTransRedInvoicePath(),
                    queryParams,
                    request
            );

            if (response.isOk()) {
                if (mideaInvoiceApiConfig.isEnableSignatureVerification() && !response.isSignatureValid()) {
                    log.warn("红票开具响应签名验证失败, requestId: {}", response.getRequestId());
                }

                // 解析标准响应
                MideaApiHttpClient.MideaStandardResponse standardResponse =
                        response.parseStandardResponse();
                log.info("红票开具结果, result: {}", JSON.toJSONString(standardResponse));
                if (standardResponse != null && standardResponse.isSuccess()) {
                    // 红票开具成功，返回成功响应
                    return true;
                } else if (standardResponse != null) {
                    handleApiError(standardResponse);
                } else {
                    throw new ServiceException("红票开具响应为空");
                }
            } else {
                log.error("红票开具请求失败: {}", response.getErrorMessage());
                throw new ServiceException("红票开具请求失败: " + response.getErrorMessage());
            }

            return false;
        } catch (Exception e) {
            log.error("红票开具异常", e);
            throw new ServiceException("红票开具异常");
        }
    }

    /**
     * 全票种OCR识别
     */
    @Override
    public List<InvoiceStandardRecognizeDTO> recognizeInvoiceOcr(MultipartFile file) {
        try {
            log.info("全票种OCR识别, 文件名: {}, 文件大小: {} bytes",
                    file.getOriginalFilename(), file.getSize());

            // 验证文件
            validateOcrFile(file);

            // 发送OCR识别请求
            MideaApiHttpClient.MideaApiResponse<List<InvoiceStandardRecognizeDTO>> response =
                    httpClient.postFile(mideaInvoiceApiConfig.getInvoice().getOcrRecognizePath(), queryParams, file);

            if (response.isOk()) {
                if (mideaInvoiceApiConfig.isEnableSignatureVerification() && !response.isSignatureValid()) {
                    log.warn("OCR识别响应签名验证失败, requestId: {}", response.getRequestId());
                }

                MideaApiHttpClient.MideaStandardResponse<List<InvoiceStandardRecognizeDTO>> standardResponse = response.parseStandardResponse();
                log.info("OCR识别结果, result: {}", JSON.toJSONString(standardResponse));
                if (standardResponse != null && standardResponse.isSuccess()) {
                    return standardResponse.getData();
                } else if (standardResponse != null) {
                    handleApiError(standardResponse);
                } else {
                    throw new ServiceException("OCR识别响应为空");
                }
                return null;
            } else {
                log.error("OCR识别请求失败: {}", response.getErrorMessage());
                throw new ServiceException("OCR识别请求失败: " + response.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("OCR识别异常", e);
            throw new ServiceException("OCR识别异常");
        }
    }

    /**
     * 发票查询
     */
    @Override
    public InvoiceBaseQueryResDTO queryInvoice(InvoiceQueryRequest request) {
        try {
            log.info("发票查询, bizId: {}, transType: {}", request.getBizId(), request.getTransType());

            // 验证请求参数
            validateQueryRequest(request);

            MideaApiHttpClient.MideaApiResponse<InvoiceBaseQueryResDTO> response = httpClient.post(
                    mideaInvoiceApiConfig.getInvoice().getInvoiceQueryPath(),
                    queryParams,
                    request
            );

            if (response.isOk()) {
                if (mideaInvoiceApiConfig.isEnableSignatureVerification() && !response.isSignatureValid()) {
                    log.warn("发票查询响应签名验证失败, requestId: {}", response.getRequestId());
                }

                MideaApiHttpClient.MideaStandardResponse<InvoiceBaseQueryResDTO> standardResponse = response.parseStandardResponse();
                log.info("发票查询结果, result: {}", JSON.toJSONString(standardResponse));
                if (standardResponse != null && standardResponse.isSuccess()) {
                    // 发票查询成功，返回查询结果
                    return standardResponse.getData();
                } else if (standardResponse != null) {
                    handleApiError(standardResponse);
                } else {
                    throw new ServiceException("发票查询响应为空");
                }
            } else {
                log.error("发票查询请求失败: {}", response.getErrorMessage());
                throw new ServiceException("发票查询请求失败: " + response.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("发票查询异常", e);
            throw new ServiceException("发票查询异常");
        }
        return null;
    }

    /**
     * 发票查验
     */
    @Override
    public InvoiceVerifyResponse verifyInvoice(InvoiceVerifyRequest request) {
        try {
            log.info("发票查验, request: {}", JSON.toJSONString(request));

            // 验证请求参数
            validateVerifyRequest(request);

            MideaApiHttpClient.MideaApiResponse<InvoiceVerifyResponse> response = httpClient.post(
                    mideaInvoiceApiConfig.getInvoice().getInvoiceVerifyPath(),
                    queryParams,
                    request
            );

            if (response.isOk()) {
                if (mideaInvoiceApiConfig.isEnableSignatureVerification() && !response.isSignatureValid()) {
                    log.warn("发票查验响应签名验证失败, requestId: {}", response.getRequestId());
                }

                MideaApiHttpClient.MideaStandardResponse<InvoiceVerifyResponse> standardResponse = response.parseStandardResponse();
                log.info("发票查验结果,result: {}", JSON.toJSONString(standardResponse));
                if (standardResponse != null && standardResponse.isSuccess()) {
                    // 发票查询成功，返回查询结果
                    return standardResponse.getData();
                } else if (standardResponse != null) {
                    handleApiError(standardResponse);
                } else {
                    throw new ServiceException("发票查验响应为空");
                }
            } else {
                log.error("发票查验请求失败: {}", response.getErrorMessage());
                throw new ServiceException("发票查验请求失败: " + response.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("发票查验异常", e);
            throw new ServiceException("发票查验异常");
        }
        return null;
    }

    /**
     * 验证OCR文件
     */
    private void validateOcrFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("文件不能为空");
        }

        // 验证文件大小（最大6M）
        long maxSize = 6 * 1024 * 1024; // 6MB
        if (file.getSize() > maxSize) {
            throw new ServiceException("文件大小不能超过6MB");
        }

        // 验证文件格式
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new ServiceException("文件名不能为空");
        }

        String extension = originalFilename.substring(originalFilename.lastIndexOf(".") + 1).toLowerCase();
        if (!Arrays.asList("jpg", "jpeg", "png", "pdf").contains(extension)) {
            throw new ServiceException("文件格式不支持，仅支持jpg/jpeg/png/pdf格式");
        }

        log.debug("文件验证通过: {}, 大小: {} bytes", originalFilename, file.getSize());
    }

    /**
     * 验证发票查询请求参数
     */
    private void validateQueryRequest(InvoiceQueryRequest request) {
        if (request == null) {
            throw new ServiceException("查询请求不能为空");
        }

        if (StringUtils.isBlank(request.getBizId())) {
            throw new ServiceException("开票请求流水号不能为空");
        }

        if (request.getTransType() == null) {
            throw new ServiceException("发票类型不能为空");
        }

        if (request.getTransType() < 1 || request.getTransType() > 2) {
            throw new ServiceException("发票类型必须为1（蓝票）或2（红票）");
        }

        log.debug("发票查询请求参数验证通过: bizId={}, transType={}", request.getBizId(), request.getTransType());
    }

    /**
     * 验证发票查验请求参数
     */
    private void validateVerifyRequest(InvoiceVerifyRequest request) {
        if (request == null) {
            throw new ServiceException("查验请求不能为空");
        }

        if (StringUtils.isBlank(request.getInvoiceNo())) {
            throw new ServiceException("发票号码不能为空");
        }

        if (StringUtils.isBlank(request.getInvoiceDate())) {
            throw new ServiceException("开票日期不能为空");
        }

        if (StringUtils.isBlank(request.getInvoiceType())) {
            throw new ServiceException("发票类型不能为空");
        }

        // 验证发票类型是否有效
        validateInvoiceType(request.getInvoiceType());

        // 根据发票类型验证必填字段
        validateRequiredFieldsByInvoiceType(request);

        log.debug("发票查验请求参数验证通过: invoiceCode={}, invoiceNo={}, invoiceType={}",
                request.getInvoiceCode(), request.getInvoiceNo(), request.getInvoiceType());
    }

    /**
     * 验证发票类型
     */
    private void validateInvoiceType(String invoiceType) {
        String[] validTypes = {
            "0100", "0101", "0102", "0103", "0107", "0108", "0109", "0110",
            "0200", "0201", "0300", "0313"
        };

        boolean isValid = Arrays.stream(validTypes).anyMatch(type -> type.equals(invoiceType));
        if (!isValid) {
            throw new ServiceException("无效的发票类型: " + invoiceType);
        }
    }

    /**
     * 根据发票类型验证必填字段
     */
    private void validateRequiredFieldsByInvoiceType(InvoiceVerifyRequest request) {
        String invoiceType = request.getInvoiceType();

        // 除全电专票(0107)，全电普票(0108)外必传发票代码
        if (!"0107".equals(invoiceType) && !"0108".equals(invoiceType)) {
            if (StringUtils.isBlank(request.getInvoiceCode())) {
                throw new ServiceException("此类发票代码不能为空");
            }
        }

        // 发票种类为 0100，0103，0200,0109,0110时不含税金额不可为空
        if (Arrays.asList("0100", "0103", "0200", "0109", "0110").contains(invoiceType)) {
            if (StringUtils.isBlank(request.getTaxExclusiveAmount())) {
                throw new ServiceException("此类发票不含税金额不能为空");
            }
        }

        // 发票种类为0201，0107，0108,0109,0110时含税金额不可为空
        if (Arrays.asList("0201", "0107", "0108", "0109", "0110").contains(invoiceType)) {
            if (StringUtils.isBlank(request.getTaxInclusiveAmount())) {
                throw new ServiceException("此类发票含税金额不能为空");
            }
        }

        // 发票种类为 0101，0102，0300，0313 ,0110时校验码不可为空
        if (Arrays.asList("0101", "0102", "0300", "0313", "0110").contains(invoiceType)) {
            if (StringUtils.isBlank(request.getCheckCode())) {
                throw new ServiceException("此类发票校验码不能为空");
            }
        }
    }

    /**
     * 处理API错误
     */
    private void handleApiError(MideaApiHttpClient.MideaStandardResponse<?> response) {
        String errorMessage = "美的付API调用失败: " + response.getCode() + " - " + response.getMessage();

        if (response.isDuplicateRequest()) {
            throw new ServiceException("重复请求: " + response.getMessage());
        } else if (response.isParameterError()) {
            throw new ServiceException("参数错误: " + response.getMessage());
        } else if (response.isQueryFailure()) {
            throw new ServiceException("查询失败: " + response.getMessage());
        } else if (response.isInternalSystemError()) {
            throw new ServiceException("内部系统错误: " + response.getMessage());
        } else if (response.isSignatureVerificationFailure()) {
            throw new ServiceException("验签失败: " + response.getMessage());
        } else {
            throw new ServiceException(errorMessage);
        }
    }
}
