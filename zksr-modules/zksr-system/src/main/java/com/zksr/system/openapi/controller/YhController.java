package com.zksr.system.openapi.controller;

import com.alibaba.fastjson.JSON;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchReqVO;
import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchRespVO;
import com.zksr.common.core.domain.vo.openapi.receive.CreateYhDataReqVO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.product.api.yhdata.YhDataApi;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mq.OpenapiProducer;
import com.zksr.system.openapi.service.ITokenService;
import com.zksr.system.openapi.service.OpenapiProcessTemplate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    要货单模块
 * @date 2024/12/9 10:03
 */
@RestController
@RequestMapping("/openapi/batchYh")
@Api(tags = "OPENAPI - 要货单模块")
@Slf4j
public class YhController {

    @Resource
    private ITokenService tokenService;

    @Autowired
    private OpenapiProducer openapiProducer;

    @Resource
    private YhDataApi yhDataApi;

    @Autowired
    private OpenapiProcessTemplate yhDataProcess;

    @ApiOperation(value = "创建批量要货单", httpMethod = HttpMethod.POST)
    @PostMapping("/createBatchYh")
    @RequiresOpenapiLogin(abilityKey = "submitBatchYh")
    @Log(title = "OPENAPI - 创建批量要货单", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> submitBatchYh(@RequestBody @Valid CreateYhDataReqVO reqVO) {
        //获取日志信息
        SysInterfaceLog interfaceLog = tokenService.getOpenapiInitLog(B2BRequestType.SUBMIT_BATCH_YH.getB2bType(), OperationType.ADD.getCode(), null );
        interfaceLog.setReqData(JSON.toJSONString(reqVO));
        yhDataProcess.invoke(interfaceLog);
        return CommonResult.success(true);
    }

    @ApiOperation(value = "查询要货单数据匹配结果", httpMethod = HttpMethod.POST)
    @PostMapping("/getBatchYhRes")
    @RequiresOpenapiLogin(abilityKey = "getBatchYhRes")
    @Log(title = "OPENAPI - 查询要货单数据匹配结果", businessType = BusinessType.OTHER)
    public CommonResult<CreateYhDataMatchRespVO> getBatchYhRes(@RequestBody @Valid CreateYhDataMatchReqVO reqVO) {
        return yhDataApi.getBatchYhRes(reqVO);
    }
}
