package com.zksr.system.service.impl.message.adapter;

import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.utils.RedisWxTokenUtil;
import com.zksr.common.third.message.dto.WechatMessageDTO;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.common.third.message.dto.CommonMessageRespVO;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.system.api.commonMessage.dto.MessageCertificate;
import com.zksr.system.api.commonMessage.dto.MessageCertificateTable;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.convert.subscribe.SysMessageTemplateConvert;
import com.zksr.system.service.ISysCacheService;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/6 9:50
 */
@Service
@Slf4j
@SuppressWarnings("all")
@ApiModel(description = "微信公众号消息发送器")
public class WxPublishMessageAdapter implements MessageAdapter {

    @Autowired
    private ISysCacheService sysCacheService;

    @Override
    public List<CommonMessageRespVO> sendTo(List<CommonMessageDTO> messageList, MessageTemplateDTO messageTemplate, MessageCertificateTable messageCertificateTable) {
        AppletBaseConfigDTO appletBaseConfigDTO = sysCacheService.getAppletBaseConfigDTO(messageTemplate.getSysCode());
        if (Objects.isNull(appletBaseConfigDTO) || StringUtils.isEmpty(appletBaseConfigDTO.getPublishAppId())) {
            log.info("发送公众号通知, 没有配置公众号appid, messageTemplateId={}", messageTemplate.getMessageTemplateId());
            return fail(messageList, "没有配置小程序appid");
        }
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(appletBaseConfigDTO.getPublishAppId());
        if (StringUtils.isEmpty(accessToken)) {
            log.info("发送公众号通知, 没有accessToken, messageTemplateId={}", messageTemplate.getMessageTemplateId());
            return fail(messageList, "没有查询到accessToken");
        }
        List<CommonMessageRespVO> result = new ArrayList<>();
        for (CommonMessageDTO messageDTO : messageList) {
            CommonMessageRespVO messageRespVO = new CommonMessageRespVO(messageDTO);
            // 获取接受者凭证
            MessageCertificate certificate = messageCertificateTable.get(messageDTO.getMerchantType(), messageDTO.getMerchantId());
            if (Objects.isNull(certificate) || StringUtils.isEmpty(certificate.getAppletOpenid())) {
                messageRespVO.setSuccess(false);
                messageRespVO.setMsg("没有查询到有效的公众号openid");
                result.add(messageRespVO);
                continue;
            }
            // 转换成小程序消息
            WechatMessageDTO appletMessage = SysMessageTemplateConvert.INSTANCE.convertAppletMessageDTO(messageDTO);
            appletMessage.setAccessToken(accessToken);
            appletMessage.setAppid(appletBaseConfigDTO.getPublishAppId());
            appletMessage.setToUser(certificate.getPublishOpenid());
            appletMessage.setTemplateId(messageTemplate.getTemplateId());
            WxUtils.CommonWxAppletMsgResp resp = WxUtils.sendCommonPubMsg(appletMessage);
            // 是否发送成功
            if (resp.isSuccess()) {
                messageRespVO.setSuccess(false);
            }
            messageRespVO.setMsg(resp.getMsg());
            result.add(messageRespVO);
        }
        return result;
    }
}
