package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.sms.dto.SmsCodeReqDTO;
import com.zksr.system.api.sms.dto.SmsCodeRespDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidRespDTO;
import com.zksr.system.controller.sms.vo.SysSmsPageReqVO;
import com.zksr.system.domain.SysSms;

/**
 * 短信消息Service接口
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
public interface ISysSmsService {

    /**
     * 获得短信消息分页
     *
     * @param pageReqVO 分页查询
     * @return 短信消息分页
     */
    PageResult<SysSms> getSysSmsPage(SysSmsPageReqVO pageReqVO);


    /**
     * 发送验证码
     * @param smsCodeReqDTO
     * @return
     */
    SmsCodeRespDTO sendSmsCode(SmsCodeReqDTO smsCodeReqDTO);

    /**
     * 验证验证码
     * @param codeReqDTO
     * @return
     */
    SmsCodeValidRespDTO validateSmsCode(SmsCodeValidDTO codeReqDTO);

    /**
     * 发送单条短信给用户
     * @param sms 短信消息
     * @return 发送日志编号
     */
    Long sendSingleSms(SysSms sms);
    /**
     * 发送通知短信
     * @param sms
     * @param content
     */
    void sendNoticeSms(SysSms sms, String content);
}
