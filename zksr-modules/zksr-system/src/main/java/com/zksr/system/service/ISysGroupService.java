package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.group.vo.SysGroupPageReqVO;
import com.zksr.system.controller.group.vo.SysGroupSaveReqVO;
import com.zksr.system.domain.SysGroup;

import javax.validation.Valid;
import java.util.List;

/**
 * 平台商城市分组Service接口
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
public interface ISysGroupService {

    /**
     * 新增平台商城市分组
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysGroup(@Valid SysGroupSaveReqVO createReqVO);

    /**
     * 修改平台商城市分组
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysGroup(@Valid SysGroupSaveReqVO updateReqVO);

    /**
     * 删除平台商城市分组
     *
     * @param groupId 平台商城市分组id
     */
    public void deleteSysGroup(Long groupId);

    /**
     * 批量删除平台商城市分组
     *
     * @param groupIds 需要删除的平台商城市分组主键集合
     * @return 结果
     */
    public void deleteSysGroupByGroupIds(Long[] groupIds);

    /**
     * 获得平台商城市分组
     *
     * @param groupId 平台商城市分组id
     * @return 平台商城市分组
     */
    public SysGroup getSysGroup(Long groupId);

    /**
     * 获得平台商城市分组分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商城市分组分页
     */
    PageResult<SysGroup> getSysGroupPage(SysGroupPageReqVO pageReqVO);

    /**
    * @Description: 获取所有分组信息
    * @Author: liuxingyu
    * @Date: 2024/3/18 16:31
    */
    List<SysGroup> getGroupList();
}
