package com.zksr.system.controller.column.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 用户头列配置对象 sys_user_column
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@ApiModel("用户头列配置 - sys_user_column分页 Request VO")
public class SysUserColumnSaveReqVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    private Long userColumnId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 表code */
    @Excel(name = "表code")
    @ApiModelProperty(value = "表code", required = true)
    private String tableCode;

    /** 列key */
    @Excel(name = "列key")
    @ApiModelProperty(value = "列key")
    private String filedKey;

    /** 名称 */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String filedName;

    /** 显示 */
    @Excel(name = "显示")
    @ApiModelProperty(value = "显示")
    private Integer visibleFlag;

    /** 是否导出显示列 */
    @ApiModelProperty(value = "是否导出显示列")
    private Integer exportVisibelFlag;

    /** 固定 */
    @Excel(name = "固定方向 left，right")
    @ApiModelProperty(value = "固定")
    private Integer fixedFlag;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 对齐方式 （left，center，right）
     */
    @ApiModelProperty(value = "对齐方式 （left，center，right）")
    private String align;

    /**
     * 列宽度
     */
    @ApiModelProperty(value = "列宽度")
    private Integer width;

    /**
     * 列最小宽度
     */
    @ApiModelProperty(value = "列最小宽度")
    private Integer minWidth;

    /**
     * 是否使用插槽
     */
    @ApiModelProperty(value = "是否使用插槽")
    private String useSlot;

    /**
     * 是否隐藏多行
     */
    @ApiModelProperty(value = "是否隐藏多行")
    private String overflow;

}
