package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.partnerConfig.dto.WxB2bPayAppletStatusDTO;
import com.zksr.system.api.partnerConfig.enums.PartnerConfigEnum;
import com.zksr.system.controller.partnerConfig.vo.*;
import com.zksr.system.domain.SysPartnerConfig;

import javax.validation.Valid;

/**
 * 平台商配置(由软件商设置)Service接口
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
public interface ISysPartnerConfigService {

    /**
     * 新增平台商配置(由软件商设置)
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysPartnerConfig(@Valid SysPartnerConfigSaveReqVO createReqVO);

    /**
     * 修改平台商配置(由软件商设置)
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysPartnerConfig(@Valid SysPartnerConfigSaveReqVO updateReqVO);

    /**
     * 删除平台商配置(由软件商设置)
     *
     * @param partnerConfigId 平台商配置id
     */
    public void deleteSysPartnerConfig(Long partnerConfigId);

    /**
     * 批量删除平台商配置(由软件商设置)
     *
     * @param partnerConfigIds 需要删除的平台商配置(由软件商设置)主键集合
     * @return 结果
     */
    public void deleteSysPartnerConfigByPartnerConfigIds(Long[] partnerConfigIds);

    /**
     * 获得平台商配置(由软件商设置)
     *
     * @param partnerConfigId 平台商配置id
     * @return 平台商配置(由软件商设置)
     */
    public SysPartnerConfig getSysPartnerConfig(Long partnerConfigId);

    /**
     * 获得平台商配置(由软件商设置)分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商配置(由软件商设置)分页
     */
    PageResult<SysPartnerConfig> getSysPartnerConfigPage(SysPartnerConfigPageReqVO pageReqVO);

    /**
     * @Description: 保存配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/12 17:13
     */
    Boolean savePartnerConfig(SysPartnerConfigSaveReqVO createReqVO, Long sysCode);

    /**
     * @Description: 获取配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/13 14:52
     */
    SysPartnerConfigRespVO getPartnerConfig(Long sysCode);

    /**
     * @Description: 获取配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/13 14:52
     */
    SysPartnerConfigRespVO getPartnerConfig(Long sysCode, Integer type);

    /**
     * 获取平台商配置, 适应于当行记录, JSON配置
     * @param configKey 配置key
     * @param sysCode   平台ID
     * @param classz    class
     * @return
     * @param <T>
     */
     <T> T getPartnerConfigObject(String configKey, Long sysCode, Class<T> classz);

    /**
     * 设置json参数
     * @param configKey  配置key
     * @param sysCode    平台商ID
     * @param obj        配置对象
     * @param configEnum 配置类型
     */
     void setPartnerConfigObject(String configKey, Long sysCode, Object obj, PartnerConfigEnum configEnum);

    /**
     * 获取小程序b2b支付服务器授权链接
     * @param appid
     * @return
     */
    String getAppWxB2bAuthUrl(String appid);

    /**
     * 开通微信b2b支付
     * @param reqVO
     * @return
     */
    SysSaveOpenB2bPayRespVO openWxB2bPay(SysSaveOpenB2bPayReqVO reqVO);

    /**
     * 报名b2b技术服务费
     * @param reqVO
     * @return
     */
    SysSaveOpenB2bPayRespVO setB2bPayProfitRate(SysB2bPayProfitRateReqVO reqVO);

    /**
     *
     * @param appid
     * @return
     */
    WxB2bPayAppletStatusDTO getAppWxB2bState(String appid);
}
