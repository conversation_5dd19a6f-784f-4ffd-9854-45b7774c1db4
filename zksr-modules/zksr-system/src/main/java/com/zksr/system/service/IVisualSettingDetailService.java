package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailPageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailSaveReqVO;
import com.zksr.system.domain.VisualSettingDetail;

import javax.validation.Valid;
import java.util.List;

/**
 * 可视化配置详情Service接口
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
public interface IVisualSettingDetailService {

    /**
     * 新增可视化配置详情
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertVisualSettingDetail(@Valid VisualSettingDetailSaveReqVO createReqVO);

    /**
     * 修改可视化配置详情
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateVisualSettingDetail(@Valid VisualSettingDetailSaveReqVO updateReqVO);

    /**
     * 删除可视化配置详情
     *
     * @param visualDetailId id
     */
    public void deleteVisualSettingDetail(Long visualDetailId);

    /**
     * 批量删除可视化配置详情
     *
     * @param visualDetailIds 需要删除的可视化配置详情主键集合
     * @return 结果
     */
    public void deleteVisualSettingDetailByVisualDetailIds(Long[] visualDetailIds);

    /**
     * 获得可视化配置详情
     *
     * @param visualDetailId id
     * @return 可视化配置详情
     */
    public VisualSettingDetail getVisualSettingDetail(Long visualDetailId);

    /**
     * 获得可视化配置详情分页
     *
     * @param pageReqVO 分页查询
     * @return 可视化配置详情分页
     */
    PageResult<VisualSettingDetail> getVisualSettingDetailPage(VisualSettingDetailPageReqVO pageReqVO);

    List<VisualSettingDetailRespVO> list(VisualSettingDetailPageReqVO pageReqVO);

    VisualSettingDetail getVisualSettingDetailByMasterId(Long visualMasterId,Long templateType);

    /**
     * 获得可视化配置详情及模板信息
     *
     * @param visualDetailId id
     * @return 获得可视化配置详情及模板信息
     */
    public VisualSettingDetailDto getVisualSettingDetailAndTemplate(Long visualDetailId);

}
