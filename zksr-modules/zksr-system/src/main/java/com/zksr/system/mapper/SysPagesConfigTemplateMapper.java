package com.zksr.system.mapper;

import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPagesConfigTemplate;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplatePageReqVO;


/**
 * 平台页面配置模版Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Mapper
public interface SysPagesConfigTemplateMapper extends BaseMapperX<SysPagesConfigTemplate> {
    default PageResult<SysPagesConfigTemplate> selectPage(SysPagesConfigTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysPagesConfigTemplate>()
                    .eqIfPresent(SysPagesConfigTemplate::getPageTemplateId, reqVO.getPageTemplateId())
                    .eqIfPresent(SysPagesConfigTemplate::getEnableTime, reqVO.getEnableTime())
                    .likeIfPresent(SysPagesConfigTemplate::getPageName, reqVO.getPageName())
                    .eqIfPresent(SysPagesConfigTemplate::getPageType, reqVO.getPageType())
                    .eqIfPresent(SysPagesConfigTemplate::getPageConfig, reqVO.getPageConfig())
                    .eqIfPresent(SysPagesConfigTemplate::getStatus, reqVO.getStatus())
                    .eqIfPresent(SysPagesConfigTemplate::getPid, reqVO.getPid())
                    .eqIfPresent(SysPagesConfigTemplate::getLevel, reqVO.getLevel())
                    .eqIfPresent(SysPagesConfigTemplate::getHasChild, reqVO.getHasChild())
                    .eq(SysPagesConfigTemplate::getDelFlag, DelFlagConstants.NORMAL)
                .orderByDesc(SysPagesConfigTemplate::getPageTemplateId));
    }
}
