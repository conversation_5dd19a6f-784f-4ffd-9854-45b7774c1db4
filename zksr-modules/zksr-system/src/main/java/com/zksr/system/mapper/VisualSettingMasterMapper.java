package com.zksr.system.mapper;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterPageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterRespVO;
import com.zksr.system.domain.VisualSettingMaster;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 可视化配置主Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Mapper
public interface VisualSettingMasterMapper extends BaseMapperX<VisualSettingMaster> {
    default PageResult<VisualSettingMaster> selectPage(VisualSettingMasterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisualSettingMaster>()
                    .eqIfPresent(VisualSettingMaster::getVisualMasterId, reqVO.getVisualMasterId())
                    .likeIfPresent(VisualSettingMaster::getPlatformName, reqVO.getPlatformName())
                    .eqIfPresent(VisualSettingMaster::getEncryptType, reqVO.getEncryptType())
                    .eqIfPresent(VisualSettingMaster::getSendUrl, reqVO.getSendUrl())
                    .eqIfPresent(VisualSettingMaster::getStatus, reqVO.getStatus())
                    .eqIfPresent(VisualSettingMaster::getPublicKey, reqVO.getPublicKey())
                    .eqIfPresent(VisualSettingMaster::getPrivateKey, reqVO.getPrivateKey())
                    .eqIfPresent(VisualSettingMaster::getCommonSettingType, reqVO.getCommonSettingType())
                    .eqIfPresent(VisualSettingMaster::getMemo, reqVO.getMemo())
                .orderByDesc(VisualSettingMaster::getVisualMasterId));
    }

    List<VisualSettingMasterRespVO> list(@Param("vo") VisualSettingMasterPageReqVO pageReqVO);
}
