package com.zksr.system.controller.partner.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import java.math.BigDecimal;
import java.util.List;

/**
 * 平台商信息对象 sys_partner
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
@ApiModel("平台商信息 - sys_partner分页 Request VO")
public class SysPartnerSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @ApiModelProperty(value = "来源")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 平台名称 */
    @Excel(name = "平台名称")
    @ApiModelProperty(value = "平台名称", required = true)
    private String partnerName;

    /** 关联平台商管理员的账号id */
    @Excel(name = "关联平台商管理员的账号id")
    @ApiModelProperty(value = "关联平台商管理员的账号id", required = true)
    private Long partnerUserId;

    /** 负责人姓名 */
    @Excel(name = "负责人姓名")
    @ApiModelProperty(value = "负责人姓名", required = true)
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话", required = true)
    private String contactPhone;

    /** 公司地址 */
    @Excel(name = "公司地址")
    @ApiModelProperty(value = "公司地址", required = true)
    private String contactAddress;

    /** 状态 数据字典sys_partner_status */
    @Excel(name = "状态 数据字典sys_partner_status")
    @ApiModelProperty(value = "状态 数据字典sys_partner_status", required = true)
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 来源 */
    @Excel(name = "来源")
    @ApiModelProperty(value = "来源")
    private String source;

    /** 城市数量 */
    @ApiModelProperty(value = "城市数量", required = true)
    private Integer areaNum;

    /** 运营商数量 */
    @ApiModelProperty(value = "运营商数量", required = true)
    private Integer dcNum;

    /** 入驻商数量 */
    @ApiModelProperty(value = "入驻商数量", required = true)
    private Integer supplierNum;

    /** 平台编码 */
    @ApiModelProperty(value = "平台编码", required = true)
    private String partnerCode;
    /**
     * 合伙人超级管理员
     */
    @ApiModelProperty(value = "合伙人超级管理员，新增时必填")
    private String partnerAccount;

    @ApiModelProperty(value = "合伙人超级管理员密码，新增时必填")
    private String partnerAccountPwd;

    /** 软件商ID */
    @ApiModelProperty(value = "软件商ID", required = true)
    private Long softwareId;

    /** 软件商分润比例 */
    @Excel(name = "软件商分润比例")
    private BigDecimal softwareRate;

    private String saasTenantCode;

    //软件商支付账号配置
    /** 商户绑定信息 */
    @ApiModelProperty(value = "商户绑定信息")
    private List<PlatformSimpleBindVO> platformSimpleBindList;

    /** 开启零售（0 否 1 是) */
    @ApiModelProperty(value = "开启零售（0 否 1 是)")
    private Integer enableRetail;

    /** 开启O2O（0 否 1 是) */
    @ApiModelProperty(value = "开启O2O（0 否 1 是)")
    private Integer enableO2o;
}
