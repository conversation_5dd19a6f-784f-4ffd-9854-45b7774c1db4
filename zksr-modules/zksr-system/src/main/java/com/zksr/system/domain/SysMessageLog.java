package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 消息发送记录对象 sys_message_log
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@TableName(value = "sys_message_log")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysMessageLog extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long msgId;

    /** 平台商ID */
    @Excel(name = "平台商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 消息模版ID */
    @Excel(name = "消息模版ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long messageTemplateId;

    /** 接受商户ID */
    @Excel(name = "接受商户ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 接受商户类型 */
    @Excel(name = "接受商户类型")
    private String merchantType;

    /** 0-未发送, 1-成功, 2-失败 */
    @Excel(name = "0-未发送, 1-成功, 2-失败")
    private Integer state;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String content;

    /** 跳转路径 */
    @Excel(name = "跳转路径")
    private String path;

    /** 消息备注 */
    @Excel(name = "消息备注")
    private String tips;
}
