package com.zksr.system.convert.dc;

import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.dc.vo.SysDcAccountRespVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.domain.SysDc;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 运营商数据封装
 * @date 2024/4/13 9:07
 */
@Mapper
public interface DcConvert {

    DcConvert INSTANCE = Mappers.getMapper(DcConvert.class);

    SysDcRespVO convert(SysDc sysDc);


    @Mappings({
            @Mapping(source = "sysAreaId", target = "sysAreaId"),
            @Mapping(source = "userName", target = "userName")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget SysDcRespVO sysDcRespVO, List<Long> sysAreaId, String userName);

    PageResult<SysDcAccountRespVO> convert(PageResult<SysDcRespVO> pageResult);

    @Mappings({
            @Mapping(source = "account.platform", target = "payPlatform"),
            @Mapping(source = "account.accountId", target = "accountId"),
            @Mapping(source = "account.withdrawableAmt", target = "withdrawableAmt"),
            @Mapping(source = "account.frozenAmt", target = "frozenAmt"),
            @Mapping(source = "account.creditAmt", target = "creditAmt")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget SysDcAccountRespVO item, AccAccountDTO account);
}
