package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.exception.GlobalException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailPageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailSaveReqVO;
import com.zksr.system.convert.visualSetting.visualSettingDetail.VisualSettingDetailConvert;
import com.zksr.system.domain.*;
import com.zksr.system.mapper.*;
import com.zksr.system.service.IVisualSettingDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 可视化配置详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Service
public class VisualSettingDetailServiceImpl implements IVisualSettingDetailService {
    @Autowired
    private VisualSettingDetailMapper visualSettingDetailMapper;

    @Resource
    private VisualSettingMasterMapper visualSettingMasterMapper;

    @Resource
    private VisualSettingTemplateMapper visualSettingTemplateMapper;

    @Autowired
    @Qualifier("visualSettingDetailCache")
    private Cache<String, VisualSettingDetailDto> visualSettingDetailCache;

    @Autowired
    private SysSupplierMapper sysSupplierMapper;


    @Autowired
    @Qualifier("visualSettingDetailBySupplierIdCache")
    private Cache<String, VisualSettingDetailDto> visualSettingDetailBySupplierIdCache;


    @Autowired
    private SysOpensourceMapper sysOpensourceMapper;

    /**
     * 新增可视化配置详情
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertVisualSettingDetail(VisualSettingDetailSaveReqVO createReqVO) {
        VisualSettingTemplate visualSettingTemplate = visualSettingTemplateMapper.selectById(createReqVO.getVisualTemplateId());
        createReqVO.setTemplateType(visualSettingTemplate.getTemplateType());

        // 插入
        VisualSettingDetail visualSettingDetail = VisualSettingDetailConvert.INSTANCE.convert(createReqVO);
        visualSettingDetailMapper.insert(visualSettingDetail);
        // 返回
        return visualSettingDetail.getVisualDetailId();
    }

    /**
     * 修改可视化配置详情
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateVisualSettingDetail(VisualSettingDetailSaveReqVO updateReqVO) {
        //根据id查询出来模板类型，存在模板则设置模板类型
        if (ObjectUtil.isNotNull(updateReqVO.getVisualTemplateId())){
            VisualSettingTemplate visualSettingTemplate = visualSettingTemplateMapper.selectById(updateReqVO.getVisualTemplateId());
            updateReqVO.setTemplateType(visualSettingTemplate.getTemplateType());
        }
        visualSettingDetailMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, VisualSettingDetail.class));
        //清除与入驻商相关的可视化详情缓存
        removeVisualDetailCacheBySupplier(updateReqVO.getVisualMasterId(),updateReqVO.getTemplateType());
    }

    /**
     * 删除可视化配置详情
     *
     * @param visualDetailId id
     */
    @Override
    public void deleteVisualSettingDetail(Long visualDetailId) {

        VisualSettingDetail visualSettingDetail = visualSettingDetailMapper.selectById(visualDetailId);

        //清除与入驻商相关的可视化详情缓存
        removeVisualDetailCacheBySupplier(visualSettingDetail.getVisualMasterId(),visualSettingDetail.getTemplateType());
        // 删除
        visualSettingDetailMapper.deleteById(visualDetailId);
    }

    /**
     * 批量删除可视化配置详情
     *
     * @param visualDetailIds 需要删除的可视化配置详情主键
     * @return 结果
     */
    @Override
    public void deleteVisualSettingDetailByVisualDetailIds(Long[] visualDetailIds) {
        for(Long visualDetailId : visualDetailIds){
            this.deleteVisualSettingDetail(visualDetailId);
        }
    }

    /**
     * 获得可视化配置详情
     *
     * @param visualDetailId id
     * @return 可视化配置详情
     */
    @Override
    public VisualSettingDetail getVisualSettingDetail(Long visualDetailId) {
        return visualSettingDetailMapper.selectById(visualDetailId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<VisualSettingDetail> getVisualSettingDetailPage(VisualSettingDetailPageReqVO pageReqVO) {
        return visualSettingDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public VisualSettingDetail getVisualSettingDetailByMasterId(Long visualMasterId, Long templateType) {
        return visualSettingDetailMapper.getVisualSettingDetailByMasterIdAndTemplateType(visualMasterId,templateType);
    }

    @Override
    public VisualSettingDetailDto getVisualSettingDetailAndTemplate(Long visualDetailId) {
        //获取可视化详情信息
        VisualSettingDetailDto visualSettingDetailDto = HutoolBeanUtils.toBean(visualSettingDetailMapper.selectById(visualDetailId),VisualSettingDetailDto.class);

        if(Objects.nonNull(visualSettingDetailDto) && Objects.nonNull(visualSettingDetailDto.getVisualTemplateId())){
            //获取可视化模板信息
            VisualSettingTemplate visualSettingTemplate = visualSettingTemplateMapper.selectById(visualSettingDetailDto.getVisualTemplateId());
            if(Objects.nonNull(visualSettingTemplate.getApiTemplate())){
                visualSettingDetailDto.setApiTemplate(visualSettingTemplate.getApiTemplate());
            }
        }

        return visualSettingDetailDto;
    }

    @Override
    public List<VisualSettingDetailRespVO> list(VisualSettingDetailPageReqVO pageReqVO) {
        return visualSettingDetailMapper.list(pageReqVO);
    }

    private void validateVisualSettingDetailExists(Long visualDetailId) {
        if (visualSettingDetailMapper.selectById(visualDetailId) == null) {
            throw new GlobalException("可视化配置主不存在");
        }
    }

    /**
     * 清除与该可视化详情相关的  key：入驻商ID + 模板类型  value：可视化详情  缓存
     * @param visualMasterId
     */
    private void removeVisualDetailCacheBySupplier(Long visualMasterId,Long templateType){
        //获取配置了该可视化主表配置的 开发能力信息
        List<SysOpensource> opensourceList = sysOpensourceMapper.getOpensourceListByVisualMasterId(visualMasterId);

        //获取开发能力对应的入驻商ID + 模板类型
        Set<String> keyList = opensourceList.stream().map(x -> x.getMerchantId() + StringPool.COLON + templateType).collect(Collectors.toSet());

        if(!keyList.isEmpty()){
            visualSettingDetailBySupplierIdCache.removeAll(keyList);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 可视化配置详情 TODO 补充编号 ==========
    // ErrorCode VISUAL_SETTING_DETAIL_NOT_EXISTS = new ErrorCode(TODO 补充编号, "可视化配置详情不存在");


}
