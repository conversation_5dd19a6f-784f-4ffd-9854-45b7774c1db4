package com.zksr.system.service.impl;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPrintSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysPrintTemplateMapper;
import com.zksr.system.convert.print.SysPrintTemplateConvert;
import com.zksr.system.domain.SysPrintTemplate;
import com.zksr.system.controller.print.vo.SysPrintTemplatePageReqVO;
import com.zksr.system.controller.print.vo.SysPrintTemplateSaveReqVO;
import com.zksr.system.service.ISysPrintTemplateService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 打印模版Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Service
public class SysPrintTemplateServiceImpl implements ISysPrintTemplateService {
    @Autowired
    private SysPrintTemplateMapper sysPrintTemplateMapper;

    /**
     * 新增打印模版
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysPrintTemplate(SysPrintTemplateSaveReqVO createReqVO) {
        // 插入
        SysPrintTemplate sysPrintTemplate = SysPrintTemplateConvert.INSTANCE.convert(createReqVO);
        Long count = sysPrintTemplateMapper.selectCountByType(createReqVO.getModuleType(), createReqVO.getPaperType());
        if (count > NumberPool.LONG_ZERO) {
            // 打印模版重复
            throw exception(SYS_PRINTER_TEMPLATE_TYPE_REPEAT);
        }
        sysPrintTemplateMapper.insert(sysPrintTemplate);
        // 返回
        return sysPrintTemplate.getPrintTemplateId();
    }

    /**
     * 修改打印模版
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysPrintTemplate(SysPrintTemplateSaveReqVO updateReqVO) {
        sysPrintTemplateMapper.updateById(SysPrintTemplateConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除打印模版
     *
     * @param printTemplateId 主键
     */
    @Override
    public void deleteSysPrintTemplate(Long printTemplateId) {
        // 删除
        SysPrintTemplate printTemplate = sysPrintTemplateMapper.selectById(printTemplateId);
        printTemplate.setDelFlag(NumberPool.INT_ONE);
        sysPrintTemplateMapper.updateById(printTemplate);
    }

    /**
     * 批量删除打印模版
     *
     * @param printTemplateIds 需要删除的打印模版主键
     * @return 结果
     */
    @Override
    public void deleteSysPrintTemplateByPrintTemplateIds(Long[] printTemplateIds) {
        for(Long printTemplateId : printTemplateIds){
            this.deleteSysPrintTemplate(printTemplateId);
        }
    }

    /**
     * 获得打印模版
     *
     * @param printTemplateId 主键
     * @return 打印模版
     */
    @Override
    public SysPrintTemplate getSysPrintTemplate(Long printTemplateId) {
        return sysPrintTemplateMapper.selectById(printTemplateId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysPrintTemplate> getSysPrintTemplatePage(SysPrintTemplatePageReqVO pageReqVO) {
        return sysPrintTemplateMapper.selectPage(pageReqVO);
    }

}
