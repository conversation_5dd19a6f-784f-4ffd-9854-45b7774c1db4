package com.zksr.system.chin.partnerConfig;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigRespVO;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigSaveReqVO;
import com.zksr.system.domain.SysPartnerConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class ConfigPartnerConfigPipeline {
    @Autowired
    private ConfigAppletBaseHandler appletBaseHandler;

    @Autowired
    private ConfigHeLiBaoHandler heLiBaoHandler;

    @Autowired
    private ConfigMideaPayHandler mideaPayHandler;

    @Autowired
    private ConfigPayHandler configPayHandler;

    @Autowired
    private ConfigPayAccountHandler configPayAccountHandler;

    @Autowired
    private ConfigCourierHandler configCourierHandler;

    @Autowired
    private ConfigDeviceSettingHandler deviceSettingHandler;

    @Autowired
    private ConfigSmsHandler smsHandler;

    @Autowired
    private ConfigWxB2bHandler wxB2bHandler;

    /**
     * @Description: 责任链处理平台商配置
     * @Param: SysPartnerConfigSaveReqVO createReqVO
     * @return:
     * @Author: liuxingyu
     * @Date: 2024/3/12 16:45
     */
    @Transactional(rollbackFor = Exception.class)
    public void partnerSaveConfig(SysPartnerConfigSaveReqVO createReqVO, Long sysCode) {
        //构建链式通道(后续需添加可直接在最后节点添加)
        appletBaseHandler.setNext(heLiBaoHandler);
        heLiBaoHandler.setNext(configPayHandler);
        configPayHandler.setNext(configPayAccountHandler);
        configPayAccountHandler.setNext(configCourierHandler);
        configCourierHandler.setNext(deviceSettingHandler);
        deviceSettingHandler.setNext(smsHandler);
        smsHandler.setNext(wxB2bHandler);
        //smsHandler.setNext(mideaPayHandler);
        //执行方法
        appletBaseHandler.saveConfig(createReqVO, sysCode);
    }

    /**
     * @Description: 通过平台编号获取配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/13 14:54
     */
    public SysPartnerConfigRespVO partnerGetConfig(SysPartnerConfigRespVO sysPartnerConfigRespVO, List<SysPartnerConfig> configList, Long sysCode) {
        if (ObjectUtil.isEmpty(configList)) {
            return sysPartnerConfigRespVO;
        }
        //构建链式通道(后续需添加可直接在最后节点添加)
        appletBaseHandler.setNext(heLiBaoHandler);
        heLiBaoHandler.setNext(configPayHandler);
        configPayHandler.setNext(configPayAccountHandler);
        configPayAccountHandler.setNext(configCourierHandler);
        configCourierHandler.setNext(deviceSettingHandler);
        deviceSettingHandler.setNext(smsHandler);
        smsHandler.setNext(mideaPayHandler);
        mideaPayHandler.setNext(wxB2bHandler);
        //执行方法
        return appletBaseHandler.getConfig(configList, sysPartnerConfigRespVO, sysCode);
    }

}
