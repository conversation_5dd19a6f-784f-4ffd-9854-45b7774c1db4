package com.zksr.system.mapper;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPrintSettings;
import com.zksr.system.controller.print.vo.SysPrintSettingsPageReqVO;

import java.util.List;


/**
 * 打印设置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
@Mapper
public interface SysPrintSettingsMapper extends BaseMapperX<SysPrintSettings> {
    default PageResult<SysPrintSettings> selectPage(SysPrintSettingsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysPrintSettings>()
                    .eqIfPresent(SysPrintSettings::getPrintSetterId, reqVO.getPrintSetterId())
                    .likeIfPresent(SysPrintSettings::getModuleName, reqVO.getModuleName())
                    .eqIfPresent(SysPrintSettings::getModuleType, reqVO.getModuleType())
                    .eqIfPresent(SysPrintSettings::getTemplateWidth, reqVO.getTemplateWidth())
                    .eqIfPresent(SysPrintSettings::getTemplateHeight, reqVO.getTemplateHeight())
                    .eqIfPresent(SysPrintSettings::getPaperWidth, reqVO.getPaperWidth())
                    .eqIfPresent(SysPrintSettings::getPaperHeight, reqVO.getPaperHeight())
                    .eqIfPresent(SysPrintSettings::getPrintContent, reqVO.getPrintContent())
                    .eqIfPresent(SysPrintSettings::getSysCode, reqVO.getSysCode())
                    .eq(SysPrintSettings::getDelFlag, NumberPool.INT_ZERO)
                .orderByDesc(SysPrintSettings::getPrintSetterId));
    }

    default Long selectByType(String moduleType) {
        return selectCount(
                new LambdaQueryWrapperX<SysPrintSettings>()
                        .eq(SysPrintSettings::getModuleType, moduleType)
                        .eq(SysPrintSettings::getDelFlag, NumberPool.INT_ZERO)
        );
    }

    default List<SysPrintSettings> selectModuleList(SysPrintSettingsPageReqVO pageReqVO) {
        return selectList(
                new LambdaQueryWrapperX<SysPrintSettings>()
                        .eq(SysPrintSettings::getModuleType, pageReqVO.getModuleType())
                        .eq(SysPrintSettings::getDelFlag, NumberPool.INT_ZERO)
        );
    }
}
