package com.zksr.system.mq;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.file.api.file.FileApi;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.system.amap.IAmapService;
import com.zksr.system.api.commonMessage.vo.SubscribeEventBodyVO;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.domain.SysSms;
import com.zksr.system.mapper.SysPartnerMapper;
import com.zksr.system.mapper.SysSupplierMapper;
import com.zksr.system.service.ISysFileImportService;
import com.zksr.system.service.ISysSmsService;
import com.zksr.system.service.impl.message.AbstractSubscribeMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;
import java.util.function.Consumer;

/**
 * @Description: 猎鹰服务消息队列消费者
 * @Author: liuxingyu
 * @Date: 2024/3/28 15:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SystemMqConsumer {

    @Autowired
    private SysSupplierMapper sysSupplierMapper;

    @Autowired
    private SysPartnerMapper sysPartnerMapper;

    @Autowired
    private IAmapService amapService;

    @Autowired
    private ISysSmsService smsService;

    @Resource
    private BranchApi branchApi;

    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;

    @Autowired
    @Qualifier("areaClassBranchListDtoCache")
    private Cache<Long, List<AreaClassDTO>> areaClassBranchListDtoCache;

    @Autowired
    @Qualifier("catgorySupplierListDtoCache")
    private Cache<Long, List<CatgoryDTO>> catgorySupplierListDtoCache;
    @Autowired
    private FileApi fileApi;
    @Autowired
    private ISysFileImportService sysFileImportService;

//    /**
//     * @Description: 新增电子围栏(消费者)
//     * @Author: liuxingyu
//     * @Date: 2024/3/28 11:25
//     */
//    @Bean
//    @Transactional(rollbackFor = Exception.class)
//    public Consumer<SupplierDTO> systemGeofenceAddEvent() {
//        return (data) -> {
//            log.info("收到新增电子围栏消息：{} ", data);
//            if (ObjectUtil.isNull(data)) {
//                log.info("新增电子围栏消息对象为空：{} ", data);
//                return;
//            }
//            //同步入驻商门店绑定关系
//            BranchSupplierDTO branchSupplierDTO = new BranchSupplierDTO(data.getBranchIdList(), data.getSupplierId(), data.getSysCode());
//            branchApi.bindBranch(branchSupplierDTO);
//            //保存电子围栏
//            //获取平台商服务ID
//            SysPartner sysPartner = sysPartnerMapper.selectById(data.getSysCode());
//            PolygonGeofenceParam polygonGeofenceParam = new PolygonGeofenceParam();
//            polygonGeofenceParam.setSid(sysPartner.getSid());
//            polygonGeofenceParam.setName(data.getSupplierName());
//            polygonGeofenceParam.setDesc(data.getSupplierName());
//            polygonGeofenceParam.setPoints(data.getDzwlInfo());
//            PolygonGeofenceResult polygonGeofenceResult = amapService.addPolygonGeofence(polygonGeofenceParam);
//            log.info("----------猎鹰平台Id'{}',猎鹰创建围栏返回信息'{}',详细信息'{}'",
//                    sysPartner.getSid(), polygonGeofenceResult.getErrmsg(), polygonGeofenceResult.getErrdetail());
//            if (ObjectUtil.notEqual(10000L, polygonGeofenceResult.getErrcode())) {
//                log.error("==========新增猎鹰围栏失败,入驻商ID:'{}',详细信息:'{}'", data.getSupplierId(), polygonGeofenceResult.getErrdetail());
//                return;
//            }
//            Long gfid = polygonGeofenceResult.getData().getGfid();
//            //修改入驻商电子围栏ID
//            SysSupplier sysSupplier = new SysSupplier();
//            sysSupplier.setSupplierId(data.getSupplierId());
//            sysSupplier.setGfid(gfid);
//            sysSupplierMapper.updateById(sysSupplier);
//            supplierDTOCache.remove(data.getSupplierId());
//            catgorySupplierListDtoCache.remove(data.getSupplierId());
//        };
//    }
//
//    /**
//     * @Description: 修改电子围栏(消费者)
//     * @Author: liuxingyu
//     * @Date: 2024/3/28 14:25
//     */
//    @Bean
//    @Transactional(rollbackFor = Exception.class)
//    public Consumer<SupplierDTO> systemGeofenceUpdateEvent() {
//        return (data) -> {
//            log.info("收到修改电子围栏消息：{} ", data);
//            //同步入驻商门店绑定关系
//            BranchSupplierDTO branchSupplierDTO = new BranchSupplierDTO(data.getBranchIdList(), data.getSupplierId(), data.getSysCode());
//            branchApi.bindBranch(branchSupplierDTO);
//            //猎鹰修改围栏
//            //获取平台商服务ID
//            SysPartner sysPartner = sysPartnerMapper.selectById(data.getSysCode());
//            //获取最新入驻商信息
//            SysSupplier supplier = sysSupplierMapper.selectById(data.getSupplierId());
//            //修改电子围栏
//            PolygonGeofenceParam polygonGeofenceParam = new PolygonGeofenceParam();
//            polygonGeofenceParam.setSid(sysPartner.getSid());
//            polygonGeofenceParam.setGfid(supplier.getGfid());
//            polygonGeofenceParam.setName(supplier.getSupplierName());
//            polygonGeofenceParam.setDesc(supplier.getSupplierName());
//            polygonGeofenceParam.setPoints(supplier.getDzwlInfo());
//            PolygonGeofenceResult polygonGeofenceResult = amapService.updatePolygonGeofence(polygonGeofenceParam);
//            log.info("----------猎鹰平台Id'{}',猎鹰修改围栏返回信息'{}',详细信息'{}'",
//                    sysPartner.getSid(), polygonGeofenceResult.getErrmsg(), polygonGeofenceResult.getErrdetail());
//            if (ObjectUtil.notEqual(10000L, polygonGeofenceResult.getErrcode())) {
//                log.error("==========修改猎鹰围栏失败,入驻商ID:'{}',详细信息:'{}'", supplier.getSupplierId(), polygonGeofenceResult.getErrdetail());
//                return;
//            }
//            supplierDTOCache.remove(supplier.getSupplierId());
//            catgorySupplierListDtoCache.remove(data.getSupplierId());
//        };
//    }
//
//    /**
//     * @Description: 校验电子围栏(消费者)
//     * @Author: liuxingyu
//     * @Date: 2024/3/28 15:23
//     */
//    @Bean
//    public Consumer<CheckGeofenceDTO> systemCheckGeofenceEvent() {
//        return (data) -> {
//            log.info("收到校验电子围栏消息：{} ", data);
//            //获取猎鹰电子围栏不为空的入驻商
//            List<SysSupplier> sysSupplierList = sysSupplierMapper.getGfidIsNotNull();
//            if (ObjectUtil.isEmpty(sysSupplierList)) {
//                return;
//            }
//            //获取sid
//            SysPartner sysPartner = sysPartnerMapper.selectById(data.getSysCode());
//            List<Long> gfids = sysSupplierList.stream().map(SysSupplier::getGfid).collect(Collectors.toList());
//            //根据需求每次最多对比100个围栏
//            List<CheckLocationResult.Data.Results> results = new ArrayList<>();
//            int size = gfids.size();
//            int step = 100;
//            for (int i = 0; i < size; i = i + step) {
//                int perCount = Math.min((size - i), step);
//                List<Long> longs = gfids.subList(i, perCount + i);
//                String gfidsString = StringUtils.join(longs, ",");
//                //封装请求对象
//                CheckLocationParam checkLocationParam = new CheckLocationParam();
//                checkLocationParam.setSid(sysPartner.getSid());
//                checkLocationParam.setLocation(data.getLongitude() + "," + data.getLatitude());
//                checkLocationParam.setGfids(gfidsString);
//                CheckLocationResult checkLocationResult = amapService.checkLocation(checkLocationParam);
//                log.info("----------猎鹰平台Id'{}',猎鹰校验电子围栏返回信息'{}',详细信息'{}'",
//                        sysPartner.getSid(), checkLocationResult.getErrmsg(), checkLocationResult.getErrdetail());
//                if (ObjectUtil.equal(checkLocationResult.getErrcode(), 10000L) && ObjectUtil.notEqual(checkLocationResult.getData().getCount(), 0L)) {
//                    results.addAll(checkLocationResult.getData().getResults());
//                }
//            }
//            if (ObjectUtil.isEmpty(results)) {
//                return;
//            }
//            //所有匹配的电子围栏ID
//            List<Long> amapGfids = results.stream().map(CheckLocationResult.Data.Results::getGfid).collect(Collectors.toList());
//            List<SysSupplier> suppliers = sysSupplierMapper.getByGfid(amapGfids);
//            List<Long> supplierIds = suppliers.stream().map(SysSupplier::getSupplierId).collect(Collectors.toList());
//            //门店绑定入驻商
//            BranchSupplierDTO branchSupplierDTO = new BranchSupplierDTO(data.getBranchId(), supplierIds, data.getSysCode());
//            branchApi.bindSupplier(branchSupplierDTO);
//            //删除缓存
//            areaClassBranchListDtoCache.remove(data.getBranchId());
//        };
//    }
//
//    /**
//     * @Description: 新建服务(消费者)
//     * @Author: liuxingyu
//     * @Date: 2024/4/7 14:43
//     */
//    @Bean
//    public Consumer<SysPartner> systemServiceAddEvent() {
//        return (data) -> {
//            log.info("收到新建服务消息：{} ", data);
//            ServiceParam serviceParam = new ServiceParam();
//            serviceParam.setName(data.getPartnerName());
//            serviceParam.setDesc(data.getSysCode().toString());
//            ServiceResult serviceResult = amapService.addService(serviceParam);
//            log.info("----------平台Id'{}',猎鹰新建服务返回信息'{}',详细信息'{}'",
//                    data.getSysCode(), serviceResult.getErrmsg(), serviceResult.getErrdetail());
//            if (ObjectUtil.notEqual(serviceResult.getErrcode(), 10000L)) {
//                log.error("==========猎鹰新建服务失败,平台商ID:'{}',详细信息:'{}'", data.getSysCode(), serviceResult.getErrdetail());
//                return;
//            }
//            data.setSid(serviceResult.getData().getSid());
//            sysPartnerMapper.updateById(data);
//        };
//    }


    /**
     * 消息消息消费者
     * 来源 {@linkplain SystemMqProducer#sendSms(SysSms)}
     *
     * @return
     */
    @Bean
    public Consumer<SysSms> smsEvent() {
        return (data) -> {
            log.info("收到短信发送消息：{} ", data);
            smsService.sendSingleSms(data);
        };
    }

    @Autowired
    private AbstractSubscribeMessageHandler abstractSubscribeMessageHandler;

    /**
     * 消息消息消费者
     * 来源 {@linkplain SystemMqProducer#sendSubscribe(SubscribeEventBodyVO)}
     * @return
     */
    @Bean
    public Consumer<SubscribeEventBodyVO> subscribeMessageEvent() {
        return (data) -> {
            log.info("收到公众号/小程序订阅消息发送消息：{} ", data);
            abstractSubscribeMessageHandler.processEvent(data);
        };
    }

    /**
     * 消息消息消费者
     * 来源 {@linkplain SystemMqProducer#sendFileImportTaskEvent(SysFileImportVO)}
     * @return
     */
    @Bean
    public Consumer<SysFileImportVO> fileImportTaskEvent(){
        return (data) -> {
            log.info("收到文件导入发送消息：{} ", data);
            SysFileImport byId = sysFileImportService.getById(data.getFileImportId());
            if (byId.getMqSendNum()>byId.getMqReceiveNum()){
                sysFileImportService.addMqReceiveNum(byId);
                new Thread(()->{
                    fileApi.importJobExecute(data);
                }).start();
            }
        };
    }

}
