package com.zksr.system.tenant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * <AUTHOR>
 * @date 2025/03/26 16:18
 */
@Component
public class PwdHelper {
    private static final Logger logger = LoggerFactory.getLogger(PwdHelper.class);
    private static final String ENCRYPTED_PREFIX = "cipher:";
    private static final String SALT = "UAP202010";
    // 加密盐，不能随便改变，改变之后密码全部失效
    private static final String ENCRYPTED_SALT = "Copyright1968-2024MideaGroup,IT,LTC,cc,9394";
    public static final String PWD_FORMAT_PATTERN = "(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d\\W]{8,16}";

    public static String encryptPassword(String pwdStr) {
        String encryptPassword = null;
        try {
            encryptPassword = aesEncrypt(pwdStr, ENCRYPTED_SALT);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return encryptPassword;
    }

    // 解密
    public static String decryptPassword(String encryptPwdStr) {
        String decryptPassword = null;
        if (encryptPwdStr.startsWith(ENCRYPTED_PREFIX)) {
            encryptPwdStr = encryptPwdStr.substring(ENCRYPTED_PREFIX.length());
        }
        try {
            decryptPassword = aesDecrypt(encryptPwdStr, ENCRYPTED_SALT);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return decryptPassword;
    }

    public static String aesEncrypt(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(encryptKey.getBytes("UTF-8"));
        kgen.init(128, secureRandom);
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(1, new SecretKeySpec(kgen.generateKey().getEncoded(), "AES"));
        byte[] result = cipher.doFinal(content.getBytes("utf-8"));
        return base64Encode(result);
    }

    public static String aesDecrypt(String encryptContent, String decryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(decryptKey.getBytes("UTF-8"));
        kgen.init(128, secureRandom);
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(2, new SecretKeySpec(kgen.generateKey().getEncoded(), "AES"));
        byte[] result = cipher.doFinal(base64Decode(encryptContent));
        return new String(result);
    }

    public static String base64Encode(byte[] b) {
        return Base64.getEncoder().encodeToString(b);
    }

    public static String base64Encode(String b) {
        return b == null ? null : Base64.getEncoder().encodeToString(b.getBytes());
    }

    public static byte[] base64Decode(String base64Code) {
        return Base64.getDecoder().decode(base64Code);
    }

    public static String base64Decode2String(String base64Code) {
        byte[] decode = Base64.getDecoder().decode(base64Code);
        return new String(decode);
    }
}