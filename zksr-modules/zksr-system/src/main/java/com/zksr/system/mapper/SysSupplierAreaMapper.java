package com.zksr.system.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO;
import com.zksr.system.domain.SysSupplier;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysSupplierArea;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 入驻商-区域城市关联关系Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Mapper
public interface SysSupplierAreaMapper extends BaseMapperX<SysSupplierArea> {
    default PageResult<SysSupplierArea> selectPage(SysSupplierAreaPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysSupplierArea>()
                    .eqIfPresent(SysSupplierArea::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(SysSupplierArea::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(SysSupplierArea::getAreaId, reqVO.getAreaId())
                .orderByDesc(SysSupplierArea::getSysCode));
    }

    /**
    * @Description: 通过入驻商ID获取所有绑定的区域ID
    * @Author: liuxingyu
    * @Date: 2024/3/6 14:43
    */
    default List<Long> selectListBySupplierId(Long supplierId){
        List<SysSupplierArea> sysSupplierAreaList = selectList(new LambdaUpdateWrapper<SysSupplierArea>().eq(SysSupplierArea::getSupplierId, supplierId));
        if (ObjectUtil.isEmpty(sysSupplierAreaList)){
            return null;
        }
        return sysSupplierAreaList.stream().map(SysSupplierArea::getAreaId).collect(Collectors.toList());
    }

    /**
    * @Description: 通过入驻商ID删除入驻商绑定区域
    * @Param: Long supplierId
    * @return:
    * @Author: liuxingyu
    * @Date: 2024/3/6 17:08
    */
    default void deleteBySupplierId(Long supplierId){
        delete(new LambdaUpdateWrapper<SysSupplierArea>().eq(SysSupplierArea::getSupplierId,supplierId));
    }

    /**
    * @Description: 根据dcId获取绑定区域下所有的入驻商ID
    * @Author: liuxingyu
    * @Date: 2024/3/21 17:00
    */
    List<Long> selectSupplierIds(@Param("dcId") Long dcId);

    /**
    * @Description: 根据区域获取绑定的入驻商ID
    * @Author: liuxingyu
    * @Date: 2024/4/12 15:02
    */
    default List<SysSupplierArea> getByAreaId(Long areaId){
        return selectList(new LambdaQueryWrapper<SysSupplierArea>().eq(SysSupplierArea::getAreaId,areaId));
    }

    /**
     * 获取区域下绑定的电子围栏信息
     * @param areaId
     * @return
     */
    List<SupplierDzwlAreaDTO>  getDzwlSupplierInfoByAreaId(@Param("areaId") Long areaId);

    List<SysSupplier> getSupplierListByIds(@Param("supplierIds") List<Long> supplierIds);
}
