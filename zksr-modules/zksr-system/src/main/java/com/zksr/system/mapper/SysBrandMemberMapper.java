package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.controller.brand.vo.SysBrandMemberRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysBrandMember;
import com.zksr.system.controller.brand.vo.SysBrandMemberPageReqVO;

import java.util.List;


/**
 * 品牌商子账户Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Mapper
public interface SysBrandMemberMapper extends BaseMapperX<SysBrandMember> {
    default PageResult<SysBrandMember> selectPage(SysBrandMemberPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysBrandMember>()
                    .eqIfPresent(SysBrandMember::getBrandMemberId, reqVO.getBrandMemberId())
                    .eqIfPresent(SysBrandMember::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(SysBrandMember::getBrandMerchantId, reqVO.getBrandMerchantId())
                    .eqIfPresent(SysBrandMember::getContactPhone, reqVO.getContactPhone())
                    .eqIfPresent(SysBrandMember::getContactAddress, reqVO.getContactAddress())
                    .eqIfPresent(SysBrandMember::getMemo, reqVO.getMemo())
                    .eqIfPresent(SysBrandMember::getSysUserId, reqVO.getSysUserId())
                    .eqIfPresent(SysBrandMember::getBrandIds, reqVO.getBrandIds())
                    .eqIfPresent(SysBrandMember::getAreaIds, reqVO.getAreaIds())
                    .eqIfPresent(SysBrandMember::getStatus, reqVO.getStatus())
                .orderByDesc(SysBrandMember::getBrandMemberId));
    }

    List<SysBrandMemberRespVO> selectPageExt(SysBrandMemberPageReqVO pageReqVO);

    default SysBrandMember selectByUserId(Long userId) {
        return selectOne(new LambdaQueryWrapperX<SysBrandMember>()
                .eqIfPresent(SysBrandMember::getSysUserId, userId)
        );
    }
}
