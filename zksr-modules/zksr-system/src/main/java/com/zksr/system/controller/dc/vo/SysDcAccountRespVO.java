package com.zksr.system.controller.dc.vo;

import com.zksr.system.api.dc.vo.SysDcRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 运营商对象 sys_dc
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Data
@ApiModel("运营商 - sys_dc account VO")
public class SysDcAccountRespVO extends SysDcRespVO {

    private static final long serialVersionUID = 1L;

    /** 可提现金额 */
    @ApiModelProperty(value = "可提现金额")
    private BigDecimal withdrawableAmt = BigDecimal.ZERO;

    /** 冻结金额 */
    @ApiModelProperty(value = "冻结金额")
    private BigDecimal frozenAmt = BigDecimal.ZERO;

    /** 授信额度 */
    @ApiModelProperty(value = "授信额度")
    private BigDecimal creditAmt = BigDecimal.ZERO;

    /** 账户ID */
    @ApiModelProperty(value = "账户ID")
    private Long accountId;

    /** 支付平台 */
    @ApiModelProperty(value = "支付平台")
    private String payPlatform;
}
