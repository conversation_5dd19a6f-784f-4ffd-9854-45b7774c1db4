package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysUserColumn;
import com.zksr.system.controller.column.vo.SysUserColumnPageReqVO;

import java.util.List;


/**
 * 用户头列配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Mapper
public interface SysUserColumnMapper extends BaseMapperX<SysUserColumn> {

    /**
     * 查询表列配置列表
     * @param reqVO
     * @return
     */
    default List<SysUserColumn> selectList(SysUserColumnPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<SysUserColumn>()
                .eqIfPresent(SysUserColumn::getCreateBy, reqVO.getCreateBy())
                .eqIfPresent(SysUserColumn::getSysCode, reqVO.getSysCode())
                .eqIfPresent(SysUserColumn::getTableCode, reqVO.getTableCode())
                .eqIfPresent(SysUserColumn::getExportVisibelFlag, reqVO.getExportVisibelFlag())
                .orderByAsc(SysUserColumn::getSort));
    }


    /**
     * 查询表列配置列表
     * @param reqVO
     * @return
     */
    default Integer deleteByTableCode(String tableCode, String userName) {
        return delete(new LambdaQueryWrapperX<SysUserColumn>()
                .eq(SysUserColumn::getTableCode, tableCode)
                .eq(SysUserColumn::getCreateBy, userName));
    }
}
