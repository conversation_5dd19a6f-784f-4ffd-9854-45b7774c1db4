package com.zksr.system.service.impl.message.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.ApproveStateEnum;
import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.system.api.commonMessage.dto.IFlowContext;
import com.zksr.system.api.commonMessage.dto.MessageTable;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderAfterFinishVO;
import com.zksr.system.enums.MessageParamEum;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.impl.message.SubscribeMessageHandler;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.dto.SupplierAfterDtlDTO;
import com.zksr.trade.api.after.dto.TrdSupplierAfterDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  售后完成通知
 */
@Slf4j
@Service
public class MessageScene11Handler extends SubscribeMessageHandler<SubscribeOrderAfterFinishVO> {

    @Override
    public Integer scene() {
        return CommonMessageSceneEnum.AFTER_REFUND_FINISH.getScene();
    }

    @Override
    protected void initFlowContext() {
        // 初始化本消息公用变量
        FlowContext context = new FlowContext();

        // 收集参数, 发送通知操作
        SubscribeOrderAfterFinishVO finishVO = getEventData();
        // 入驻商售后详情
        List<SupplierAfterDtlDTO> afterDtlDTOS = afterApi.getAfterDtlListById(finishVO.getSupplierAfterDtlIdList()).getCheckedData();
        SupplierAfterDtlDTO afterDtlDTO = afterDtlDTOS.get(0);
        // 入驻商订单
        TrdSupplierAfterDTO supplierAfter = afterApi.getSupplierAfter(afterDtlDTO.getSupplierAfterId()).getCheckedData();
        // 入驻商
        TrdOrder trdOrder = orderApi.getOrderByOrderId(supplierAfter.getOrderId());
        List<String> spuNames = afterDtlDTOS.stream().map(item -> StringUtils.format("{}*{}", item.getSpuName(), item.getReturnUnitQty())).collect(Collectors.toList());
        // 用户门店信息
        MemberDTO memberDTO = memberApi.getMemBerByMemberId(trdOrder.getMemberId()).getCheckedData();
        BranchDTO branchDTO = sysCacheService.getBranchDTO(trdOrder.getBranchId());
        // 填充参数上下午支持模版自定义参数
        MessageTable messageTable = MessageTable.builder().build();
        messageTable.setOrderNo(trdOrder.getOrderNo())
                .setMemberName(memberDTO.getMemberName())
                .setMemberPhone(memberDTO.getMemberPhone())
                .setBranchName(memberDTO.getMemberPhone())
                .setBranchName(branchDTO.getBranchName())
                .setBranchAddr(branchDTO.getBranchAddr())
                .setSpuName(StringUtils.join(spuNames, StringPool.COMMA))
                .setPayAmt(trdOrder.getPayAmt().toString())
                .put(MessageParamEum.AFTER_NO, supplierAfter.getAfterNo())
                .put(MessageParamEum.AFTER_AUDIT_MEMO, afterDtlDTO.getMemo())
                .put(MessageParamEum.AFTER_FINISH_TIME, DateUtil.formatDateTime(DateUtil.date()))
        ;
        // 设置公共参数
        context.setTrdOrder(trdOrder);
        context.setBranch(branchDTO);
        context.setMember(memberDTO);
        context.setSupplierAfterDtlDTOList(afterDtlDTOS);
        // 设置到流程里面去
        messageContext().setFlowContext(context);
        messageContext().setMessageTable(messageTable);
    }

    @Override
    public List<CommonMessageDTO> memberHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        MessageTable messageTable = messageContext().getMessageTable();
        // 组装消息主体
        Map<String, String> body = messageTemplateDTO.buildSubscribeMessageDTO(messageTable);
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(flowContext.getMember().getMemberId())
                .body(body)
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Override
    public List<CommonMessageDTO> colonelHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        Long colonelId = flowContext.getBranch().getColonelId();
        // 获取业务员
        ColonelDTO colonel = sysCacheService.getColonel(colonelId);
        if (Objects.isNull(colonel)) {
            return ListUtil.empty();
        }
        // 组装消息主体
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(colonel.getColonelId())
                .body(messageTemplateDTO.buildSubscribeMessageDTO(messageContext().getMessageTable()))
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Override
    public List<CommonMessageDTO> supplierHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        List<SupplierAfterDtlDTO> afterDtlDTOList = flowContext.getSupplierAfterDtlDTOList();
        ArrayList<CommonMessageDTO> messageDTOS = new ArrayList<>();
        // 拆分入驻商订单信息
        afterDtlDTOList.stream().collect(Collectors.groupingBy(SupplierAfterDtlDTO::getSupplierId)).forEach((supplierId, supplierDtlList) -> {
            BigDecimal totalAmt = supplierDtlList.stream().map(SupplierAfterDtlDTO::getRefundAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            List<String> supplierSpuNames = supplierDtlList.stream().map(item -> StringUtils.format("{}", item.getSpuName())).collect(Collectors.toList());
            // 复制参数容器
            MessageTable messageTable = ObjectUtil.cloneByStream(messageContext().getMessageTable());
            messageTable.setSpuName(StringUtils.join(supplierSpuNames, StringPool.COMMA));
            messageTable.setPayAmt(totalAmt.toString());
            // 组装消息主体
            messageDTOS.add(
                    CommonMessageDTO.builder()
                    .merchantId(supplierId)
                    .body(messageTemplateDTO.buildSubscribeMessageDTO(messageContext().getMessageTable()))
                    .build()
            );
        });
        return messageDTOS;
    }

    @Data
    @ApiModel(description = "消息内部容器")
    public static class FlowContext implements IFlowContext {

        @ApiModelProperty("交易订单")
        private TrdOrder trdOrder;

        @ApiModelProperty("门店")
        private BranchDTO branch;

        @ApiModelProperty("用户")
        private MemberDTO member;

        @ApiModelProperty("入驻商售后详情列表")
        private List<SupplierAfterDtlDTO> supplierAfterDtlDTOList;
    }
}
