package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 打印设置对象 sys_print_settings
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
@TableName(value = "sys_print_settings")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysPrintSettings extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键 */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long printSetterId;

    /** 模块名称 */
    @Excel(name = "模块名称")
    private String moduleName;

    /** 模块类型 */
    @Excel(name = "模块类型")
    private String moduleType;

    /** 模板宽度 */
    @Excel(name = "模板宽度")
    private BigDecimal templateWidth;

    /** 模板高度 */
    @Excel(name = "模板高度")
    private BigDecimal templateHeight;

    /** 纸张宽度 */
    @Excel(name = "纸张宽度")
    private BigDecimal paperWidth;

    /** 纸张高度 */
    @Excel(name = "纸张高度")
    private BigDecimal paperHeight;

    /** 打印内容 */
    @Excel(name = "打印内容")
    private String printContent;

    /** 平台ID */
    @Excel(name = "平台ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 删除标志(0正常;1删除) */
    private Integer delFlag;

    /** 纸张类型 */
    @Excel(name = "纸张类型")
    private String paperType;

}
