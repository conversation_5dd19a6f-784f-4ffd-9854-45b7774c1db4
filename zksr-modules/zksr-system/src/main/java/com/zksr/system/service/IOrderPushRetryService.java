package com.zksr.system.service;

import com.zksr.system.api.domain.OrderPushRetryParam;
import com.zksr.system.api.domain.OrderPushRetryResult;
import com.zksr.system.domain.SysInterfaceLog;

import java.util.List;

/**
 * @Description: 订单推送重试服务接口
 * @Date: 2025/07/22
 */
public interface IOrderPushRetryService {

    /**
     * 执行订单推送失败重推任务
     * 
     * @param param 重推参数
     * @return 执行结果
     */
    OrderPushRetryResult retryFailedOrderPush(OrderPushRetryParam param);

    /**
     * 查询需要重推的接口日志记录
     * 
     * @param param 查询参数
     * @return 接口日志记录列表
     */
    List<SysInterfaceLog> getFailedPushLogs(OrderPushRetryParam param);

    /**
     * 重推单个订单
     * 
     * @param interfaceLog 接口日志记录
     * @param param 重推参数
     * @return 是否成功
     */
    boolean retryPushSingleOrder(SysInterfaceLog interfaceLog, OrderPushRetryParam param);

}
