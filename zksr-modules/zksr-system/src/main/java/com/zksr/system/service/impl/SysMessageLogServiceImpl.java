package com.zksr.system.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.common.third.message.dto.CommonMessageRespVO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.controller.message.vo.SysMessageLogPageReqVO;
import com.zksr.system.controller.message.vo.SysMessageLogResendReqVO;
import com.zksr.system.controller.message.vo.SysMessageLogRespVO;
import com.zksr.system.convert.subscribe.SysMessageLogConvert;
import com.zksr.system.convert.subscribe.SysMessageTemplateConvert;
import com.zksr.system.domain.SysMessageLog;
import com.zksr.system.domain.SysMessageTemplate;
import com.zksr.system.mapper.SysMessageLogMapper;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysMessageLogService;
import com.zksr.system.service.ISysMessageTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 消息发送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Service
public class SysMessageLogServiceImpl implements ISysMessageLogService {

    @Autowired
    private SysMessageLogMapper sysMessageLogMapper;

    @Autowired
    private ISysCacheService sysCacheService;

    /**
     * 获得消息发送记录
     *
     * @param msgId ${pkColumn.columnComment}
     * @return 消息发送记录
     */
    @Override
    public SysMessageLog getSysMessageLog(Long msgId) {
        return sysMessageLogMapper.selectById(msgId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysMessageLogRespVO> getSysMessageLogPage(SysMessageLogPageReqVO pageReqVO) {
        PageResult<SysMessageLog> pageResult = sysMessageLogMapper.selectPage(pageReqVO);
        PageResult<SysMessageLogRespVO> respVOPageResult = SysMessageLogConvert.INSTANCE.convertPage(pageResult);
        // 渲染数据
        respVOPageResult.getList().forEach(respVO -> {
            switch (MerchantTypeEnum.fromValue(respVO.getMerchantType())) {
                case MEMBER:
                    MemberDTO memberDTO = sysCacheService.getMember(respVO.getMerchantId());
                    respVO.setMerchantName(memberDTO.getMemberName());
                    break;
                case COLONEL:
                    ColonelDTO colonelDTO = sysCacheService.getColonel(respVO.getMerchantId());
                    respVO.setMerchantName(colonelDTO.getColonelName());
                    break;
                case SUPPLIER:
                    SupplierDTO supplierDTO = sysCacheService.getSupplierDTO(respVO.getMerchantId());
                    respVO.setMerchantName(supplierDTO.getSupplierName());
                    break;
                default:
                    break;
            }
        });
        return respVOPageResult;
    }

    @Override
    @Transactional
    public void insertSysMessageLog(List<CommonMessageRespVO> messageRespVOList, MessageTemplateDTO messageTemplate) {
        for (CommonMessageRespVO messageRespVO : messageRespVOList) {
            CommonMessageDTO commonMessage = messageRespVO.getCommonMessage();
            if (Objects.nonNull(commonMessage.getMsgId())) {
                SysMessageLog messageLog = sysMessageLogMapper.selectById(commonMessage.getMsgId());
                if (Objects.nonNull(messageLog)) {
                    messageLog.setTips(messageRespVO.getMsg());
                    messageLog.setState(messageRespVO.isSuccess() ? NumberPool.INT_ONE : NumberPool.INT_TWO);
                    sysMessageLogMapper.updateById(messageLog);
                }
            } else {
                SysMessageLog saveLog = SysMessageLog.builder()
                        .messageTemplateId(messageTemplate.getMessageTemplateId())
                        .sysCode(messageTemplate.getSysCode())
                        .state(messageRespVO.isSuccess() ? NumberPool.INT_ONE : NumberPool.INT_TWO)
                        .merchantType(commonMessage.getMerchantType().getType())
                        .merchantId(commonMessage.getMerchantId())
                        .content(JSON.toJSONString(commonMessage.getBody()))
                        .path(commonMessage.getPath())
                        .tips(messageRespVO.getMsg())
                        .build();
                sysMessageLogMapper.insert(saveLog);
            }
        }
    }

    @Override
    public void resend(SysMessageLogResendReqVO resendReqVO) {
        // 获取消息记录
        SysMessageLog messageLog = sysMessageLogMapper.selectById(resendReqVO.getMsgId());
        MessageTemplateDTO messageTemplateDTO = SysMessageTemplateConvert.INSTANCE.convertDTO(getSysMessageTemplate().getSysMessageTemplate(messageLog.getMessageTemplateId()));

        Map<String, Object> innerMap = JSON.parseObject(messageLog.getContent()).getInnerMap();
        Map<String, String> body = new HashMap<>();
        innerMap.forEach((key, val) -> {
            body.put(key, String.valueOf(val));
        });
        // 获取消息内容, 以及接受者
        List<CommonMessageDTO> messsageList = ListUtil.toList(
                CommonMessageDTO.builder()
                        .msgId(messageLog.getMsgId())
                        .path(messageLog.getPath())
                        .merchantId(messageLog.getMerchantId())
                        .merchantType(MerchantTypeEnum.fromValue(messageLog.getMerchantType()))
                        .body(body)
                        .build()
        );
        // 调用消息模版处理发送消息
        getSysMessageTemplate().sendMsg(messsageList, messageTemplateDTO);
    }

    public ISysMessageTemplateService getSysMessageTemplate() {
        return SpringUtils.getBean(ISysMessageTemplateService.class);
    }
}
