package com.zksr.system.service.impl.message.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.system.api.commonMessage.dto.IFlowContext;
import com.zksr.system.api.commonMessage.dto.MessageTable;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.service.impl.message.SubscribeMessageHandler;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderDetailRespVO;
import com.zksr.trade.api.order.vo.TrdOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:   新订单通知
 * @date 2024/6/13 15:57
 */
@Slf4j
@Service
public class MessageScene0Handler extends SubscribeMessageHandler<Long> {

    @Override
    public Integer scene() {
        return CommonMessageSceneEnum.NEW_ORDER_PUBLISH.getScene();
    }

    @Override
    public void initFlowContext() {
        // 初始化本消息公用变量
        FlowContext context = new FlowContext();
        // 收集参数, 发送通知操作
        Long orderId = super.getEventData();
        TrdOrder trdOrder = orderApi.getOrderByOrderId(orderId);
        // 获取用户
        MemberDTO memberDTO = memberApi.getMemBerByMemberId(trdOrder.getMemberId()).getCheckedData();
        // 获取门店
        BranchDTO branchDTO = sysCacheService.getBranchDTO(trdOrder.getBranchId());
        // 入驻商订单详情
        List<TrdColonelAppOrderDetailRespVO> memColonelAppOrderDetail = orderApi.getMemColonelAppOrderDetail(orderId);
        // 组装商品名称
        List<String> spuNames = memColonelAppOrderDetail.stream().map(item -> StringUtils.format("{}*{}", item.getSpuName(), item.getTotalNum())).collect(Collectors.toList());

        context.setTrdOrder(trdOrder);
        context.setMemColonelAppOrderDetail(memColonelAppOrderDetail);
        context.setBranch(branchDTO);
        context.setMember(memberDTO);
        // 设置到流程里面去
        messageContext().setFlowContext(context);

        // 填充参数上下午支持模版自定义参数
        MessageTable messageTable = MessageTable.builder().build();
        messageTable.setOrderNo(trdOrder.getOrderNo())
                .setMemberName(memberDTO.getMemberName())
                .setMemberPhone(memberDTO.getMemberPhone())
                .setBranchName(memberDTO.getMemberPhone())
                .setBranchName(branchDTO.getBranchName())
                .setBranchAddr(branchDTO.getBranchAddr())
                .setSpuName(StringUtils.join(spuNames, StringPool.COMMA))
                .setPayAmt(trdOrder.getPayAmt().toString())
                .setOrderCreateTime(DateUtil.formatDateTime(trdOrder.getCreateTime()))
        ;
        messageContext().setMessageTable(messageTable);
    }

    @Override
    public List<CommonMessageDTO> memberHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        MessageTable messageTable = messageContext().getMessageTable();
        // 组装消息主体
        Map<String, String> body = messageTemplateDTO.buildSubscribeMessageDTO(messageTable);
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(flowContext.getMember().getMemberId())
                .body(body)
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Override
    public List<CommonMessageDTO> supplierHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        ArrayList<CommonMessageDTO> messageList = new ArrayList<>();
        List<TrdColonelAppOrderDetailRespVO> memColonelAppOrderDetail = flowContext.getMemColonelAppOrderDetail();
        Map<Long, List<TrdColonelAppOrderDetailRespVO>> supplierMap = memColonelAppOrderDetail.stream().collect(Collectors.groupingBy(TrdColonelAppOrderDetailRespVO::getSupplierId));
        for (Long supplierId : supplierMap.keySet()) {
            List<TrdColonelAppOrderDetailRespVO> supplierOrderDetails = supplierMap.get(supplierId);
            BigDecimal totalAmt = supplierOrderDetails.stream().map(TrdColonelAppOrderDetailRespVO::getTotalAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            List<String> supplierSpuNames = supplierOrderDetails.stream().map(item -> StringUtils.format("{}*{}", item.getSpuName(), item.getTotalNum())).collect(Collectors.toList());
            // 复制参数容器
            MessageTable messageTable = ObjectUtil.cloneByStream(messageContext().getMessageTable());
            messageTable.setSpuName(StringUtils.join(supplierSpuNames, StringPool.COMMA));
            messageTable.setPayAmt(totalAmt.toString());
            // 组装消息主体
            CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                    .merchantId(supplierId)
                    .body(messageTemplateDTO.buildSubscribeMessageDTO(messageTable))
                    .build();
            messageList.add(messageDTO);
        }
        return messageList;
    }

    @Override
    public List<CommonMessageDTO> colonelHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        Long colonelId = flowContext.getBranch().getColonelId();
        // 获取业务员
        ColonelDTO colonel = sysCacheService.getColonel(colonelId);
        if (Objects.isNull(colonel)) {
            return ListUtil.empty();
        }
        // 组装消息主体
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(colonel.getColonelId())
                .body(messageTemplateDTO.buildSubscribeMessageDTO(messageContext().getMessageTable()))
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Data
    @ApiModel(description = "消息内部容器")
    public static class FlowContext implements IFlowContext {

        @ApiModelProperty("交易订单")
        private TrdOrder trdOrder;

        @ApiModelProperty("门店")
        private BranchDTO branch;

        @ApiModelProperty("用户")
        private MemberDTO member;

        @ApiModelProperty("入驻商订单")
        private List<TrdColonelAppOrderDetailRespVO> memColonelAppOrderDetail;
    }
}
