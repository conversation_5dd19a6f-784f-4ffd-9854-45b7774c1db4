package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysPrintSettings;
import com.zksr.system.controller.print.vo.SysPrintSettingsPageReqVO;
import com.zksr.system.controller.print.vo.SysPrintSettingsSaveReqVO;

import java.util.List;

/**
 * 打印设置Service接口
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
public interface ISysPrintSettingsService {

    /**
     * 新增打印设置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysPrintSettings(@Valid SysPrintSettingsSaveReqVO createReqVO);

    /**
     * 修改打印设置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysPrintSettings(@Valid SysPrintSettingsSaveReqVO updateReqVO);

    /**
     * 删除打印设置
     *
     * @param printSetterId 主键
     */
    public void deleteSysPrintSettings(Long printSetterId);

    /**
     * 批量删除打印设置
     *
     * @param printSetterIds 需要删除的打印设置主键集合
     * @return 结果
     */
    public void deleteSysPrintSettingsByPrintSetterIds(Long[] printSetterIds);

    /**
     * 获得打印设置
     *
     * @param printSetterId 主键
     * @return 打印设置
     */
    public SysPrintSettings getSysPrintSettings(Long printSetterId);

    /**
     * 获得打印设置分页
     *
     * @param pageReqVO 分页查询
     * @return 打印设置分页
     */
    PageResult<SysPrintSettings> getSysPrintSettingsPage(SysPrintSettingsPageReqVO pageReqVO);

    /**
     * 通过模版类型查询模版集合
     * @param pageReqVO
     * @return
     */
    List<SysPrintSettings> getModuleType(SysPrintSettingsPageReqVO pageReqVO);

}
