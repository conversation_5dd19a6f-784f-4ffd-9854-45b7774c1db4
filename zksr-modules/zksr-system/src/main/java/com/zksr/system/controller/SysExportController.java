package com.zksr.system.controller;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.export.vo.SysExportJob;
import com.zksr.system.api.export.vo.SysExportJobPageReqVO;
import com.zksr.system.api.export.vo.SysExportJobRespVO;
import com.zksr.system.service.ISysExportJobService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 文件导出支持
 * @date 2024/1/15 11:28
 */
@Api(tags = "管理后台 - 导出任务", produces = "application/json")
@RestController
@RequestMapping("/sexport")
public class SysExportController {

    @Autowired
    private ISysExportJobService exportJobService;

    /**
     * 等待执行的任务
     */
    @InnerAuth
    @GetMapping("/waitJobList")
    @ApiOperation(value = "获取需要执行的导出任务", httpMethod = HttpMethod.GET, hidden = true)
    public R<List<SysExportJob>> waitJobList() {
        return R.ok(exportJobService.waitJobList());
    }

    // 创建执行任务
    @Log(title = "导出任务", businessType = BusinessType.EXPORT)
    @PostMapping
    @ApiOperation(value = "新增导出任务", httpMethod = HttpMethod.POST)
    public AjaxResult createdJob(@RequestBody SysExportJob wmsReportJob) {
        exportJobService.createdJob(wmsReportJob);
        return AjaxResult.success();
    }

    @Log(title = "导出任务", businessType = BusinessType.OTHER)
    @ApiOperation(value = "查询导出任务", httpMethod = HttpMethod.GET)
    @GetMapping("/pageList")
    public CommonResult<PageResult<SysExportJobRespVO>> pageList(SysExportJobPageReqVO pageReqVO) {
        return success(exportJobService.jobPageList(pageReqVO));
    }

    // 更新任务
    @InnerAuth
    @Log(title = "更新任务")
    @PostMapping("/updateJob")
    @ApiOperation(value = "更新导出任务", httpMethod = HttpMethod.PUT, hidden = true)
    public R<Boolean> updateJob(@RequestBody SysExportJob wmsReportJob) {
        exportJobService.updateJob(wmsReportJob);
        return R.ok(true);
    }

    @InnerAuth
    @GetMapping("/getById")
    @ApiOperation(value = "获取指定导出任务", httpMethod = HttpMethod.GET, hidden = true)
    public R<SysExportJob> getById(Integer jobId) {
        return R.ok(exportJobService.getById(jobId));
    }
}
