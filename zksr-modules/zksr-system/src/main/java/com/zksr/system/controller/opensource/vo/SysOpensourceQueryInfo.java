package com.zksr.system.controller.opensource.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("开放资源 - sys_opensource 查询条件")
public class SysOpensourceQueryInfo {

    @ApiModelProperty(value = "商户ID, suplierId , sys_code ", required = true)
    @NotBlank(message = "商户ID不能为空")
    private Long merchantId;

    /**
     * 商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型 partner-平台商 supplier-入驻商", required = true)
    @NotBlank(message = "商户类型不能为空")
    private String merchantType;

}
