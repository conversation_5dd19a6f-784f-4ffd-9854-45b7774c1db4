package com.zksr.system.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysOpenlimitMapper;
import com.zksr.system.convert.openlimit.SysOpenlimitConvert;
import com.zksr.system.domain.SysOpenlimit;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitPageReqVO;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitSaveReqVO;
import com.zksr.system.service.ISysOpenlimitService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 开发能力qpsService业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Service
public class SysOpenlimitServiceImpl implements ISysOpenlimitService {
    @Autowired
    private SysOpenlimitMapper sysOpenlimitMapper;

    /**
     * 新增开发能力qps
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysOpenlimit(SysOpenlimitSaveReqVO createReqVO) {
        // 插入
        SysOpenlimit sysOpenlimit = SysOpenlimitConvert.INSTANCE.convert(createReqVO);
        sysOpenlimitMapper.insert(sysOpenlimit);
        // 返回
        return sysOpenlimit.getSysCode();
    }

    /**
     * 修改开发能力qps
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysOpenlimit(SysOpenlimitSaveReqVO updateReqVO) {
        sysOpenlimitMapper.updateById(SysOpenlimitConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除开发能力qps
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deleteSysOpenlimit(Long sysCode) {
        // 删除
        sysOpenlimitMapper.deleteById(sysCode);
    }

    /**
     * 批量删除开发能力qps
     *
     * @param sysCodes 需要删除的开发能力qps主键
     * @return 结果
     */
    @Override
    public void deleteSysOpenlimitBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deleteSysOpenlimit(sysCode);
        }
    }

    /**
     * 获得开发能力qps
     *
     * @param sysCode 平台商id
     * @return 开发能力qps
     */
    @Override
    public SysOpenlimit getSysOpenlimit(Long sysCode) {
        return sysOpenlimitMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysOpenlimit> getSysOpenlimitPage(SysOpenlimitPageReqVO pageReqVO) {
        return sysOpenlimitMapper.selectPage(pageReqVO);
    }

//    private void validateSysOpenlimitExists(Long sysCode) {
//        if (sysOpenlimitMapper.selectById(sysCode) == null) {
//            throw exception(SYS_OPENLIMIT_NOT_EXISTS);
//        }
//    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 开发能力qps TODO 补充编号 ==========
    // ErrorCode SYS_OPENLIMIT_NOT_EXISTS = new ErrorCode(TODO 补充编号, "开发能力qps不存在");


}
