package com.zksr.system.controller.partnerConfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 开通微信b2b门店助手资料
 * @date 2024/8/13 11:20
 */
@Data
@ApiModel(description = "开通微信b2b门店助手资料")
public class SysSaveOpenB2bPayReqVO {

    @ApiModelProperty("小程序appid")
    private String appid;

    @ApiModelProperty(value = "主营商品类型。可选项：\"食品\", \"饮料\", \"乳制品\", \"酒水\", \"生鲜果蔬\", \"调味品\", \"日化个护\", \"文具\", \"鞋服/配饰\", \"家居\", \"母婴/玩具\", \"数码3C\", \"其他\"", required = true)
    private List<String> goods_type_list;

    @ApiModelProperty(value = "主要线下销售渠道。可选项：\"杂货店\", \"便利店\", \"超市\", \"餐饮店\", \"母婴店\", \"烟酒店\", \"其他\"", required = true)
    private List<String> goods_sale_list;

    @ApiModelProperty(value = "门店覆盖数。可选项：\"0-5千\", \"5千-1万\", \"1万-10万\", \"10万-50万\", \"50万以上\"", required = true)
    private String cover_num;

    @ApiModelProperty(value = "所需服务类型。可选项：\"门店订货\", \"门店促销\", \"门店活动执行\", \"门店直播\", \"其他\"", required = true)
    private List<String> service_list;

    @ApiModelProperty(value = "小程序方案概述。长度限制：21-100字", required = true)
    private String description;

    @ApiModelProperty(value = "联系人姓名。长度限制：1-7字。", required = true)
    private String contact_name;

    @ApiModelProperty(value = "联系人手机号", required = true)
    private String contact_phone;

    @ApiModelProperty(value = "联系人邮箱", required = true)
    private String contact_email;
}
