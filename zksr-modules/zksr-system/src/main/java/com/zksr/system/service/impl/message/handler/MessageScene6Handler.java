package com.zksr.system.service.impl.message.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.system.api.commonMessage.dto.IFlowContext;
import com.zksr.system.api.commonMessage.dto.MessageTable;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.commonMessage.vo.SubscribeEventLocalDeliveryVO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.enums.MessageParamEum;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.impl.message.SubscribeMessageHandler;
import com.zksr.trade.api.after.vo.TrdSupplierOrder;
import com.zksr.trade.api.express.dto.OrderExpressResDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.TrdSupplierOrderDtlDTO;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderDetailRespVO;
import com.zksr.trade.api.order.vo.TrdOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  全国订单发货
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class MessageScene6Handler extends SubscribeMessageHandler<Long> {

    @Override
    public Integer scene() {
        return CommonMessageSceneEnum.GLOBAL_ORDER_DELIVER.getScene();
    }

    @Override
    protected void initFlowContext() {
        // 初始化本消息公用变量
        FlowContext context = new FlowContext();

        // 收集参数, 发送通知操作
        Long supplierOrderDtlId = getEventData();
        // 入驻商订单
        TrdSupplierOrderDtlDTO supplierOrderDtlDTO = orderApi.getSupplierOrderDtl(supplierOrderDtlId).getCheckedData();
        if (Objects.isNull(supplierOrderDtlDTO)) {
            log.error("全国订单发货消息推送, 入驻商订单详情获取失败, supplierOrderDtlId={}", supplierOrderDtlId);
        }
        TrdSupplierOrder trdSupplierOrder = orderApi.getOrderBySupplierOrderId(supplierOrderDtlDTO.getSupplierOrderId()).getCheckedData();
        // 入驻商
        TrdOrder trdOrder = orderApi.getOrderByOrderId(trdSupplierOrder.getOrderId());
        // 用户门店信息
        MemberDTO memberDTO = memberApi.getMemBerByMemberId(trdOrder.getMemberId()).getCheckedData();
        BranchDTO branchDTO = sysCacheService.getBranchDTO(trdOrder.getBranchId());
        // 填充参数上下午支持模版自定义参数
        MessageTable messageTable = MessageTable.builder().build();
        messageTable.setOrderNo(trdOrder.getOrderNo())
                .setMemberName(memberDTO.getMemberName())
                .setMemberPhone(memberDTO.getMemberPhone())
                .setBranchName(memberDTO.getMemberPhone())
                .setBranchName(branchDTO.getBranchName())
                .setBranchAddr(branchDTO.getBranchAddr())
                .setSpuName(supplierOrderDtlDTO.getSpuName())
                .setPayAmt(supplierOrderDtlDTO.getTotalAmt().toString())
                .setOrderCreateTime(DateUtil.formatDateTime(trdOrder.getCreateTime()))
        ;
        // 快递信息存在
        if (ObjectUtil.isNotEmpty(supplierOrderDtlDTO.getOrderExpressResDTOList())) {
            OrderExpressResDTO expressResDTO = supplierOrderDtlDTO.getOrderExpressResDTOList().get(0);
            // 发货时间
            messageTable.put(MessageParamEum.ORDER_DELIVERY_TIME, DateUtil.formatDateTime(expressResDTO.getCreateTime()));
            // 快递公司名称
            messageTable.put(MessageParamEum.ORDER_EXPRESS_NAME, expressResDTO.getExpressCom());
            // 快递单号
            messageTable.put(MessageParamEum.ORDER_EXPRESS_NO, expressResDTO.getExpressNo());
        }

        // 设置公共参数
        context.setTrdOrder(trdOrder);
        context.setBranch(branchDTO);
        context.setMember(memberDTO);
        // 设置到流程里面去
        messageContext().setFlowContext(context);
        messageContext().setMessageTable(messageTable);
    }

    @Override
    public List<CommonMessageDTO> memberHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        MessageTable messageTable = messageContext().getMessageTable();
        // 组装消息主体
        Map<String, String> body = messageTemplateDTO.buildSubscribeMessageDTO(messageTable);
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(flowContext.getMember().getMemberId())
                .body(body)
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Override
    public List<CommonMessageDTO> colonelHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        Long colonelId = flowContext.getBranch().getColonelId();
        // 获取业务员
        ColonelDTO colonel = sysCacheService.getColonel(colonelId);
        if (Objects.isNull(colonel)) {
            return ListUtil.empty();
        }
        // 组装消息主体
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(colonel.getColonelId())
                .body(messageTemplateDTO.buildSubscribeMessageDTO(messageContext().getMessageTable()))
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Data
    @ApiModel(description = "消息内部容器")
    public static class FlowContext implements IFlowContext {

        @ApiModelProperty("交易订单")
        private TrdOrder trdOrder;

        @ApiModelProperty("门店")
        private BranchDTO branch;

        @ApiModelProperty("用户")
        private MemberDTO member;

    }
}
