package com.zksr.system.controller.message;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.system.controller.message.vo.SysMessageLogResendReqVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysMessageLog;
import com.zksr.system.service.ISysMessageLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.message.vo.SysMessageLogPageReqVO;
import com.zksr.system.controller.message.vo.SysMessageLogRespVO;
import com.zksr.system.convert.subscribe.SysMessageLogConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 消息发送记录Controller
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Api(tags = "管理后台 - 消息发送记录接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/messageLog")
public class SysMessageLogController {

    @Autowired
    private ISysMessageLogService sysMessageLogService;

    /**
     * 获取消息发送记录详细信息
     */
    @ApiOperation(value = "获得消息发送记录详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{msgId}")
    public CommonResult<SysMessageLogRespVO> getInfo(@PathVariable("msgId") Long msgId) {
        SysMessageLog sysMessageLog = sysMessageLogService.getSysMessageLog(msgId);
        return success(SysMessageLogConvert.INSTANCE.convert(sysMessageLog));
    }

    /**
     * 分页查询消息发送记录
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得消息发送记录分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysMessageLogRespVO>> getPage(@Valid SysMessageLogPageReqVO pageReqVO) {
        return success(sysMessageLogService.getSysMessageLogPage(pageReqVO));
    }

    @PostMapping("/resend")
    @ApiOperation(value = "消息记录重复", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.RESEND)
    @RequiresPermissions(Permissions.RESEND)
    public CommonResult<Boolean> resend(@RequestBody SysMessageLogResendReqVO resendReqVO) {
        sysMessageLogService.resend(resendReqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:message-log:add";
        /** 编辑 */
        public static final String EDIT = "system:message-log:edit";
        /** 删除 */
        public static final String DELETE = "system:message-log:remove";
        /** 列表 */
        public static final String LIST = "system:message-log:list";
        /** 查询 */
        public static final String GET = "system:message-log:query";
        /** 停用 */
        public static final String DISABLE = "system:message-log:disable";
        /** 启用 */
        public static final String ENABLE = "system:message-log:enable";
        /** 重发 */
        public static final String RESEND = "system:message-log:resend";
    }
}
