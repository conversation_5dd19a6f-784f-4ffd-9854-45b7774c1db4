package com.zksr.system.service.impl;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.exception.GlobalException;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplatePageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplateSaveReqVO;
import com.zksr.system.convert.visualSetting.visualSettingTemplate.VisualSettingTemplateConvert;
import com.zksr.system.domain.VisualSettingTemplate;
import com.zksr.system.mapper.VisualSettingTemplateMapper;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.IVisualSettingTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 可视化接口模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Service
public class VisualSettingTemplateServiceImpl implements IVisualSettingTemplateService {
    @Autowired
    private VisualSettingTemplateMapper visualSettingTemplateMapper;

    @Autowired
    private Cache<Long, VisualSettingTemplateDto> visualSettingTemplateCache;

    /**
     * 新增可视化接口模板
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertVisualSettingTemplate(VisualSettingTemplateSaveReqVO createReqVO) {
        // 插入
        VisualSettingTemplate visualSettingTemplate = VisualSettingTemplateConvert.INSTANCE.convert(createReqVO);
        visualSettingTemplateMapper.insert(visualSettingTemplate);
        // 返回
        return visualSettingTemplate.getVisualTemplateId();
    }

    /**
     * 修改可视化接口模板
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateVisualSettingTemplate(VisualSettingTemplateSaveReqVO updateReqVO) {
        visualSettingTemplateMapper.updateById(VisualSettingTemplateConvert.INSTANCE.convert(updateReqVO));

        //清除模板缓存
        visualSettingTemplateCache.remove(updateReqVO.getVisualTemplateId());
    }

    /**
     * 删除可视化接口模板
     *
     * @param visualTemplateId id
     */
    @Override
    public void deleteVisualSettingTemplate(Long visualTemplateId) {
        // 删除
        visualSettingTemplateMapper.deleteById(visualTemplateId);

        //清除模板缓存
        visualSettingTemplateCache.remove(visualTemplateId);
    }

    /**
     * 批量删除可视化接口模板
     *
     * @param visualTemplateIds 需要删除的可视化接口模板主键
     * @return 结果
     */
    @Override
    public void deleteVisualSettingTemplateByVisualTemplateIds(Long[] visualTemplateIds) {
        for(Long visualTemplateId : visualTemplateIds){
            this.deleteVisualSettingTemplate(visualTemplateId);
        }
    }

    /**
     * 获得可视化接口模板
     *
     * @param visualTemplateId id
     * @return 可视化接口模板
     */
    @Override
    public VisualSettingTemplate getVisualSettingTemplate(Long visualTemplateId) {
        return visualSettingTemplateMapper.selectById(visualTemplateId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<VisualSettingTemplate> getVisualSettingTemplatePage(VisualSettingTemplatePageReqVO pageReqVO) {
        return visualSettingTemplateMapper.selectPage(pageReqVO);
    }

    private void validateVisualSettingTemplateExists(Long visualTemplateId) {
        if (visualSettingTemplateMapper.selectById(visualTemplateId) == null) {
            throw new GlobalException("可视化配置主不存在");
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 可视化接口模板 TODO 补充编号 ==========
    // ErrorCode VISUAL_SETTING_TEMPLATE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "可视化接口模板不存在");


}
