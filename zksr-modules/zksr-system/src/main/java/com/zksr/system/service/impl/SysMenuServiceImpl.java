package com.zksr.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.RandomUtil;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.common.core.constant.Constants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.domain.SysMenu;
import com.zksr.system.domain.vo.MetaVo;
import com.zksr.system.domain.vo.RouterVo;
import com.zksr.system.domain.vo.TreeSelect;
import com.zksr.system.mapper.SysMenuMapper;
import com.zksr.system.mapper.SysRoleMapper;
import com.zksr.system.mapper.SysRoleMenuMapper;
import com.zksr.system.service.ISysMenuService;

import static com.zksr.common.core.constant.Constants.BASE_CHAR;

/**
 * 菜单 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysMenuServiceImpl implements ISysMenuService
{
    public static final String PREMISSION_STRING = "perms[\"{0}\"]";

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    /**
     * 根据用户查询系统菜单列表
     *
     * @param userId 用户ID
     * @param roleId
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(Long userId, Long roleId)
    {
        //根据roleId获取 角色信息
        SysRole role = roleMapper.selectRoleById(roleId);

        //设置角色 -- 角色属性
        SysMenu sysMenu = new SysMenu();
        sysMenu.setFuncScop(role.getFuncScop());
        return selectMenuList(sysMenu, userId);
    }

    /**
     * 查询系统菜单列表
     * 
     * @param menu 菜单信息
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(SysMenu menu, Long userId)
    {
        List<SysMenu> menuList = null;
        // 管理员显示所有菜单信息
        if (SysUser.isAdmin(userId))
        {
            menuList = menuMapper.selectMenuList(menu);
        }
        else
        {
            menu.getParams().put("userId", userId);
            menuList = menuMapper.selectMenuListByUserId(menu);
        }

        //设置PID
        setPidByList(menuList);
        return menuList;
    }

    /**
     * 根据用户ID查询权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByUserId(Long userId)
    {
        List<String> perms = menuMapper.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms)
        {
            if (StringUtils.isNotEmpty(perm))
            {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据角色ID查询权限
     * 
     * @param roleId 角色ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByRoleId(Long roleId)
    {
        List<String> perms = menuMapper.selectMenuPermsByRoleId(roleId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms)
        {
            if (StringUtils.isNotEmpty(perm))
            {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单
     * 
     * @param userId 用户名称
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuTreeByUserId(Long userId)
    {
        List<SysMenu> menus = null;
        if (SecurityUtils.isAdmin(userId))
        {
            menus = menuMapper.selectMenuTreeAll();
        }
        else
        {
            menus = menuMapper.selectMenuTreeByUserId(userId);
        }
        return getChildPerms(menus, 0);
    }

    /**
     * 根据角色ID查询菜单树信息
     * 
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<Long> selectMenuListByRoleId(Long roleId)
    {
        SysRole role = roleMapper.selectRoleById(roleId);
        return menuMapper.selectMenuListByRoleId(roleId, role.isMenuCheckStrictly());
    }

    /**
     * 构建前端路由所需要的菜单
     * 
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenu> menus)
    {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenu menu : menus)
        {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(getRouteName(menu));
            router.setPath(getRouterPath(menu));
            router.setComponent(getComponent(menu));
            router.setQuery(menu.getQuery());
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
            List<SysMenu> cMenus = menu.getChildren();
            if (StringUtils.isNotEmpty(cMenus) && UserConstants.TYPE_DIR.equals(menu.getMenuType()))
            {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            }
            else if (isMenuFrame(menu))
            {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
                children.setQuery(menu.getQuery());
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            /*else if (menu.getParentId().intValue() == 0 && isInnerLink(menu))*/
            else if (StringPool.ZERO.equals(menu.getMenuPcode()) && isInnerLink(menu))
            {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                router.setPath("/");
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                String routerPath = innerLinkReplaceEach(menu.getPath());
                children.setPath(routerPath);
                children.setComponent(UserConstants.INNER_LINK);
                children.setName(StringUtils.capitalize(routerPath));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要树结构
     * 
     * @param menus 菜单列表
     * @return 树结构列表
     */
    @Override
    public List<SysMenu> buildMenuTree(List<SysMenu> menus)
    {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        //List<Long> tempList = menus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
        List<String> tempList = menus.stream().map(SysMenu::getMenuCode).collect(Collectors.toList());
        for (Iterator<SysMenu> iterator = menus.iterator(); iterator.hasNext();)
        {
            SysMenu menu = (SysMenu) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            //if (!tempList.contains(menu.getParentId()))
            if (!tempList.contains(menu.getMenuPcode()))
            {
                recursionFn(menus, menu);
                returnList.add(menu);
            }
        }
        if (returnList.isEmpty())
        {
            returnList = menus;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     * 
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildMenuTreeSelect(List<SysMenu> menus)
    {
        List<SysMenu> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据菜单ID查询信息
     * 
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    @Override
    public SysMenu selectMenuById(Long menuId)
    {
        SysMenu sysMenu = menuMapper.selectMenuById(menuId);

        //设置PID
        setPid(sysMenu);

        return sysMenu;
    }

    /**
     * 是否存在菜单子节点
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean hasChildByMenuId(Long menuId)
    {
        //获取该菜单信息
        SysMenu sysMenu = menuMapper.selectMenuById(menuId);
        /*int result = menuMapper.hasChildByMenuId(menuId);*/
        int result = menuMapper.hasChildByMenuCode(sysMenu.getMenuCode());
        return result > 0;
    }

    @Override
    public boolean hasChildByMenuCode(String menuCode) {
        int result = menuMapper.hasChildByMenuCode(menuCode);
        return result > 0;
    }

    /**
     * 查询菜单使用数量
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean checkMenuExistRole(Long menuId)
    {
        int result = roleMenuMapper.checkMenuExistRole(menuId);
        return result > 0;
    }

    /**
     * 新增保存菜单信息
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public int insertMenu(SysMenu menu)
    {
        //设置Code
        setCode(menu);

        return menuMapper.insertMenu(menu);
    }

    /**
     * 修改保存菜单信息
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public int updateMenu(SysMenu menu)
    {
        //设置Code
        setCode(menu);

        return menuMapper.updateMenu(menu);
    }

    /**
     * 删除菜单管理信息
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public int deleteMenuById(Long menuId)
    {
        return menuMapper.deleteMenuById(menuId);
    }

    @Override
    public int deleteMenuByCode(String menuCode)
    {
        return menuMapper.deleteMenuByCode(menuCode);
    }

    /**
     * 校验菜单名称是否唯一
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public boolean checkMenuNameUnique(SysMenu menu)
    {
//        Long menuId = StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();
//        SysMenu info = menuMapper.checkMenuNameUnique(menu.getMenuName(), menu.getParentId());
//        if (StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue())
//        {
//            return UserConstants.NOT_UNIQUE;
//        }

        Long menuId = StringUtils.isNull(menu.getMenuId()) ? -1L : menu.getMenuId();

        SysMenu info = menuMapper.checkMenuNameUnique(menu.getMenuName(), menu.getMenuPcode());
        //校验菜单编号是否已存在
        if (StringUtils.isNotNull(info) && info.getMenuId().longValue() != menuId.longValue())
        {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public boolean checkMenuCodeUnique(SysMenu menu) {
        //校验该menuCode 是否已经存在
        SysMenu checkMenu = menuMapper.selectMenuByCode(menu.getMenuCode());
        if(ToolUtil.isNotEmpty(checkMenu)){
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 获取路由名称
     * 
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenu menu)
    {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu))
        {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     * 
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenu menu)
    {
        String routerPath = menu.getPath();
        // 内链打开外网方式
        if (menu.getParentId().intValue() != 0 && isInnerLink(menu))
        {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 非外链并且是一级目录（类型为目录）
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(menu.getMenuType())
                && UserConstants.NO_FRAME.equals(menu.getIsFrame()))
        {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu))
        {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     * 
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenu menu)
    {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu))
        {
            component = menu.getComponent();
        }
        else if (StringUtils.isEmpty(menu.getComponent()) && menu.getParentId().intValue() != 0 && isInnerLink(menu))
        {
            component = UserConstants.INNER_LINK;
        }
        else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu))
        {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

/*    *//**
     * 是否为菜单内部跳转
     * 
     * @param menu 菜单信息
     * @return 结果
     *//*
    public boolean isMenuFrame(SysMenu menu)
    {
        return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(menu.getMenuType())
                && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }*/

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(SysMenu menu)
    {
        return StringPool.ZERO.equals(menu.getMenuPcode())  && UserConstants.TYPE_MENU.equals(menu.getMenuType())
                && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }

    /**
     * 是否为内链组件
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(SysMenu menu)
    {
        return menu.getIsFrame().equals(UserConstants.NO_FRAME) && StringUtils.ishttp(menu.getPath());
    }

    /**
     * 是否为parent_view组件
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(SysMenu menu)
    {
        return menu.getParentId().intValue() != 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType());
    }

    /**
     * 根据父节点的ID获取所有子节点
     * 
     * @param list 分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<SysMenu> getChildPerms(List<SysMenu> list, int parentId)
    {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        for (Iterator<SysMenu> iterator = list.iterator(); iterator.hasNext();)
        {
            SysMenu t = (SysMenu) iterator.next();
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            /*if (t.getParentId() == parentId)*/
            if (t.getMenuPcode().equals(String.valueOf(parentId)))
            {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     * 
     * @param list 分类表
     * @param t 子节点
     */
    private void recursionFn(List<SysMenu> list, SysMenu t)
    {
        // 得到子节点列表
        List<SysMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysMenu tChild : childList)
        {
            if (hasChild(list, tChild))
            {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t)
    {
        List<SysMenu> tlist = new ArrayList<SysMenu>();
        Iterator<SysMenu> it = list.iterator();
        while (it.hasNext())
        {
            SysMenu n = (SysMenu) it.next();
            //if (n.getParentId().longValue() == t.getMenuId().longValue())
            if (n.getMenuPcode().equals(t.getMenuCode()))
            {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysMenu> list, SysMenu t)
    {
        //return getChildList(list, t).size() > 0;
        return !getChildList(list, t).isEmpty();
    }

    /**
     * 内链域名特殊字符替换
     * 
     * @return 替换后的内链域名
     */
    public String innerLinkReplaceEach(String path)
    {
        return StringUtils.replaceEach(path, new String[] { Constants.HTTP, Constants.HTTPS, Constants.WWW, ".", ":" },
                new String[] { "", "", "", "/", "/" });
    }

    /**
     * 新增或修改 设置父级Code、Code
     * @param menu
     */
    private void setCode(SysMenu menu){
        //获取PId
        Long parentId = menu.getParentId();

        //根据PID查询父级菜单信息
        //如果是目标 则设置PID Pcode 为0
        if(NumberPool.LONG_ZERO == parentId){
            menu.setParentId(NumberPool.LONG_ZERO);
            menu.setMenuPcode(StringPool.ZERO);
        }else{
            //查询父级菜单信息
            SysMenu pMenu = menuMapper.selectMenuById(parentId);

            if(ToolUtil.isEmpty(pMenu)){
                throw new ServiceException("获取父级菜单信息失败，请确认数据准确性");
            }
            //设置父级信息
            menu.setParentId(pMenu.getMenuId());
            menu.setMenuPcode(pMenu.getMenuCode());

        }

        //新增
        if(ToolUtil.isEmpty(menu.getMenuId())){
            //设置MenuCode
            menu.setMenuCode(RandomUtil.randomString(BASE_CHAR,NumberPool.INT_EIGHTEEN));
        }

    }

    /**
     * 数据回显 根据Code设置Pid
     * @param menu
     */
    private void setPid(SysMenu menu){
        //设置当前菜单的实际父类ID
        if(StringPool.ZERO.equals(menu.getMenuPcode())){
            menu.setParentId(NumberPool.LONG_ZERO);
        }else{
            //根据Code获取父级信息
            SysMenu pMenu = menuMapper.selectMenuByCode(menu.getMenuPcode());
            menu.setParentId(pMenu.getMenuId());
        }
    }

    /**
     * 数据回显 根据Code设置Pid
     * @param menuList
     */
    private void setPidByList(List<SysMenu> menuList){
        //获取父级菜单集合
        Set<String> pcodeSet = menuList.stream().map(SysMenu::getMenuPcode).collect(Collectors.toSet());
        //如果不存在
        if(pcodeSet.isEmpty()){
            return;
        }

        //转为父级菜单Map集合
        Map<String, SysMenu> pMenuMap = menuMapper.selectMenuListByCode(ListUtil.toList(pcodeSet)).stream().collect(Collectors.toMap(SysMenu::getMenuCode, x -> x));

        menuList.forEach(menu -> {
            //设置当前菜单的实际父类ID
            if(StringPool.ZERO.equals(menu.getMenuPcode())){
                menu.setParentId(NumberPool.LONG_ZERO);
            }else{
                //根据Code获取父级信息
                SysMenu pMenu =pMenuMap.get(menu.getMenuPcode());
                //如果匹配不到父级ID  则为异常菜单  默认显示为一级
                if(ToolUtil.isNotEmpty(pMenu)){
                    menu.setParentId(pMenu.getMenuId());
                }else{
                    menu.setParentId(NumberPool.LONG_ZERO);
                }

            }
        });
    }
}
