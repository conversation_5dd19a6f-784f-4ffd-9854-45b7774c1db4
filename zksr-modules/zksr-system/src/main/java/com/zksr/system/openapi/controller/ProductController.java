package com.zksr.system.openapi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.openapi.receive.SpuOpenDTO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mq.OpenapiProducer;
import com.zksr.system.openapi.service.ITokenService;
import com.zksr.system.service.ISysInterfaceLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static com.zksr.common.core.constant.OpenApiConstants.LOG_STATUS_FAIN;


/**
 *
 * <AUTHOR>
 * @date 2024/5/30 11:15
 */
@RestController
@RequestMapping("/openapi/product")
@Api(tags = "OPENAPI - 商品相关接口")
@Slf4j
public class ProductController {

    @Autowired
    private OpenapiProducer openapiProducer;

    @Resource
    private ITokenService tokenService;

    @Resource
    private ISysInterfaceLogService sysInterfaceLogService;


    @ApiOperation(value = "新增商品信息")
    @PostMapping(value = "/savePrdt")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "savePrdt")
    //@Log(title = "OPENAPI - 新增商品信息", businessType = BusinessType.INSERT)
    //@ReceiveInterfaceLog(operationType = OperationType.ADD_OR_UPDATE,requestType = B2BRequestType.ADD_OR_UPDATESPU)
    public CommonResult<Boolean> addOrUpdateSpu(@Valid @RequestBody List<SpuOpenDTO> spuOpenDTO) throws CloneNotSupportedException {
        log.info("OPENAPI - 新增商品信息:{}", JSON.toJSONString(spuOpenDTO));
        if(ToolUtil.isEmpty(spuOpenDTO)){
            log.info("OPENAPI - 新增商品信息:入参为空");
            return CommonResult.success(true);
        }
        List<SysInterfaceLog> interfaceLogList = new ArrayList(spuOpenDTO.size());
        SysInterfaceLog interfaceLog = tokenService.getOpenapiInitLog(B2BRequestType.SAVE_PRDT.getB2bType(), OperationType.ADD_OR_UPDATE.getCode(),null );

        for (SpuOpenDTO openDTO : spuOpenDTO) {
            SysInterfaceLog newLog = new SysInterfaceLog();
            BeanUtil.copyProperties(interfaceLog, newLog);
            newLog.setBizData(JSON.toJSONString(openDTO));
            newLog.setReqId(IdUtils.fastSimpleUUID());
            interfaceLogList.add(newLog);
        }
        //boolean res = sysInterfaceLogService.insertBatch(interfaceLogList);
        log.info("ProductController:addOrUpdateSpu:interfaceLogList:{}", JSON.toJSONString(interfaceLogList));
        for (SysInterfaceLog sysInterfaceLog : interfaceLogList) {
            try {
                openapiProducer.sendSaveprdtMsg(sysInterfaceLog);
            } catch (Exception e) {
                log.error("OPENAPI - 发送商品信息消息失败: {}", e.getMessage(), e);
                sysInterfaceLog.setStatus(LOG_STATUS_FAIN);
                sysInterfaceLog.setMessage(e.getMessage());
                sysInterfaceLogService.updateSysInterfaceLog(HutoolBeanUtils.toBean(sysInterfaceLog, SysInterfaceLogSaveReqVO.class));
            }
        }
        return CommonResult.success(true);
    }

}
