package com.zksr.system.openapi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.openapi.receive.AreaItemOpenDTO;
import com.zksr.common.core.domain.vo.openapi.receive.SpuOpenDTO;
import com.zksr.common.core.enums.AreaItemOpenDTOItemUnitEnum;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mq.OpenapiProducer;
import com.zksr.system.openapi.service.ITokenService;
import com.zksr.system.service.ISysInterfaceLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.LOG_STATUS_FAIN;


/**
 *
 * <AUTHOR>
 * @date 2024/5/30 11:15
 */
@RestController
@RequestMapping("/openapi/product")
@Api(tags = "OPENAPI - 商品相关接口")
@Slf4j
public class ProductController {

    @Autowired
    private OpenapiProducer openapiProducer;

    @Resource
    private ITokenService tokenService;

    @Resource
    private ISysInterfaceLogService sysInterfaceLogService;


    @ApiOperation(value = "新增商品信息")
    @PostMapping(value = "/savePrdt")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "savePrdt")
    //!@开放API - 商品同步  - 1、总入口
    //@Log(title = "OPENAPI - 新增商品信息", businessType = BusinessType.INSERT)
    //@ReceiveInterfaceLog(operationType = OperationType.ADD_OR_UPDATE,requestType = B2BRequestType.ADD_OR_UPDATESPU)
    public CommonResult<Boolean> addOrUpdateSpu(@Valid @RequestBody List<SpuOpenDTO> spuOpenDTO) throws CloneNotSupportedException {
        log.info("OPENAPI - 新增商品信息:{}", JSON.toJSONString(spuOpenDTO));
        if(ToolUtil.isEmpty(spuOpenDTO)){
            log.info("OPENAPI - 新增商品信息:入参为空");
            return CommonResult.success(true);
        }
        List<SysInterfaceLog> interfaceLogList = new ArrayList(spuOpenDTO.size());
        SysInterfaceLog interfaceLog = tokenService.getOpenapiInitLog(B2BRequestType.SAVE_PRDT.getB2bType(), OperationType.ADD_OR_UPDATE.getCode(),null );

        for (SpuOpenDTO openDTO : spuOpenDTO) {
            SysInterfaceLog newLog = new SysInterfaceLog();
            BeanUtil.copyProperties(interfaceLog, newLog);
            newLog.setBizData(JSON.toJSONString(openDTO));
            newLog.setReqId(IdUtils.fastSimpleUUID());
            interfaceLogList.add(newLog);
        }
        //boolean res = sysInterfaceLogService.insertBatch(interfaceLogList);
        log.info("ProductController:addOrUpdateSpu:interfaceLogList:{}", JSON.toJSONString(interfaceLogList));
        for (SysInterfaceLog sysInterfaceLog : interfaceLogList) {
            try {
                openapiProducer.sendSaveprdtMsg(sysInterfaceLog);
            } catch (Exception e) {
                log.error("OPENAPI - 发送商品信息消息失败: {}", e.getMessage(), e);
                sysInterfaceLog.setStatus(LOG_STATUS_FAIN);
                sysInterfaceLog.setMessage(e.getMessage());
                sysInterfaceLogService.updateSysInterfaceLog(HutoolBeanUtils.toBean(sysInterfaceLog, SysInterfaceLogSaveReqVO.class));
            }
        }
        return CommonResult.success(true);
    }

    @ApiOperation(value = "商品上下架")
    @PostMapping(value = "/addAreaItem")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "addAreaItem")
    public CommonResult<Boolean> addAreaItem(@Valid @RequestBody List<AreaItemOpenDTO> areaItemOpenDTOList) {
        log.info(" OPENAPI商品上下架信息:{}", JsonUtils.toJsonString(areaItemOpenDTOList));
        if(ToolUtil.isEmpty(areaItemOpenDTOList)){
            log.info(" OPENAPI商品上下架信息:入参为空");
            return CommonResult.success(true);
        }
        
        // 按spuNo维度打平数据
        Map<String, List<AreaItemOpenDTO>> spuNoGroupMap = areaItemOpenDTOList.stream()
                .collect(Collectors.groupingBy(AreaItemOpenDTO::getSpuNo));

        //匹配处理大中小单位上下架属性
        List<AreaItemOpenDTO> flattenedList = new ArrayList<>();
        for (Map.Entry<String, List<AreaItemOpenDTO>> entry : spuNoGroupMap.entrySet()) {
            String spuNo = entry.getKey();
            List<AreaItemOpenDTO> items = entry.getValue();
            
            // 创建打平后的对象
            AreaItemOpenDTO flattenedItem = new AreaItemOpenDTO();
            flattenedItem.setSpuNo(spuNo);
            
            // 设置大中小单位的状态
            for (AreaItemOpenDTO item : items) {
                String itemUnit = item.getItemUnit();
                Integer type = item.getType();
                
                // 根据itemUnit设置对应的状态字段
                if (AreaItemOpenDTOItemUnitEnum.CS.getCode().equals(itemUnit) ) {
                    flattenedItem.setLargeShelfStatus(type);
                } else if (AreaItemOpenDTOItemUnitEnum.IP.getCode().equals(itemUnit)) {
                    flattenedItem.setMidShelfStatus(type);
                } else if (AreaItemOpenDTOItemUnitEnum.EA.getCode().equals(itemUnit)) {
                    flattenedItem.setMinShelfStatus(type);
                }
            }
            
            flattenedList.add(flattenedItem);
        }
        
        List<SysInterfaceLog> interfaceLogList = new ArrayList(flattenedList.size());
        SysInterfaceLog interfaceLog = tokenService.getOpenapiInitLog(B2BRequestType.ADD_AREA_ITEM.getB2bType(), OperationType.ADD_OR_UPDATE.getCode(),null );

        for (AreaItemOpenDTO areaItem : flattenedList) {
            areaItem.setSupplierId(interfaceLog.getSupplierId());
            areaItem.setSysCode(interfaceLog.getSysCode());
            SysInterfaceLog newLog = new SysInterfaceLog();
            BeanUtil.copyProperties(interfaceLog, newLog);
            newLog.setBizData(JsonUtils.toJsonString(areaItem));
            newLog.setReqId(IdUtils.fastSimpleUUID());
            interfaceLogList.add(newLog);
        }
        //boolean res = sysInterfaceLogService.insertBatch(interfaceLogList);
        log.info("ProductController:addOrUpdateSpu:interfaceLogList:{}", JSON.toJSONString(interfaceLogList));
        for (SysInterfaceLog sysInterfaceLog : interfaceLogList) {
            try {
                openapiProducer.sendAddAreaItemMsg(sysInterfaceLog);
            } catch (Exception e) {
                log.error("OPENAPI - 发送商品上下架信息失败: {}", e.getMessage(), e);
                sysInterfaceLog.setStatus(LOG_STATUS_FAIN);
                sysInterfaceLog.setMessage(e.getMessage());
                sysInterfaceLogService.updateSysInterfaceLog(HutoolBeanUtils.toBean(sysInterfaceLog, SysInterfaceLogSaveReqVO.class));
            }
        }
        return CommonResult.success(true);
    }


}
