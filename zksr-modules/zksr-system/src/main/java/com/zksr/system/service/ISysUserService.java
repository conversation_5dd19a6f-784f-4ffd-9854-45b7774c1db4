package com.zksr.system.service;

import com.zksr.common.core.domain.R;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.domain.SysUserImportVO;
import com.zksr.system.api.model.LoginUser;

import java.util.List;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService
{
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser user);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(SysUser user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(SysUser user);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    public void checkUserDataScope(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 新增用户信息(包括saas用户)
     *
     * @param user 用户信息
     * @return 结果
     */
    public int saveUser(SysUser user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserV2(SysUser user);

    /**
     * 用户授权角色
     *
     * @param userId 用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserStatus(SysUser user);


    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar 头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    public int resetPwd(SysUser user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 导入用户数据
     *
     * @param userList 用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importUser(List<SysUserImportVO> userList, Boolean isUpdateSupport, String operName);

    public void insertUserRole(SysUser user);

    /**
     * 根据用户ID获取用户账号
     * @param userId
     * @return
     */
    public String getBySysUserId(Long userId);

    /**
     * 修改账户密码
     * @param sysUserId
     * @param password
     */
    void changePass(Long sysUserId, String password);

    /**
     * 获取sysUser
     * @param userId
     * @return
     */
    SysUser getById(Long userId);

    /**
     * 根据用户手机号获取用户信息
     * @param phone
     * @return
     */
    SysUser getUserByPhone(String phone);

    /**
     * 根据SAAS用户userCode获取用户信息
     * @param saasUserCode
     * @return
     */
    SysUser getUserBySaasUserCode(String saasUserCode);

    /**
     * 获取用户信息
     * @param userCode saas用户编码
     * @param tenantCode saas租户编码
     * @return
     */
    R<LoginUser> userInfoForGateway(String userCode, String tenantCode,String b2bUserCacheKey);

    /**
     * 创建Saas用户
     * @param sysUser
     * @return
     */
    SysUser createSaasUser(SysUser sysUser);

    /**
     * 查询当前运营商下所有用户
     */
    List<SysUser> selectByDcId(Long dcId);

    /**
     * 根据 saas_user_code 查询是否存在对应的业务员 (colonel_id 不为 null)
     *
     * @param saasUserCode saas用户编码
     * @return true: 存在业务员, false: 不存在
     */
    SysUser getSaasUserCodeHasColonel(String saasUserCode);


//    /**
//     * 修改saas用户密码
//     * @param passwordForget
//     */
//    void updateSaasUserPwd(PasswordForget passwordForget);
}
