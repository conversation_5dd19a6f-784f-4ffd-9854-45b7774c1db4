package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import org.checkerframework.checker.units.qual.A;

import java.util.Date;

/**
 * 平台页面配置对象 sys_pages_config
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@TableName(value = "sys_pages_config")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysPagesConfig extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 自定义页面ID */
    @TableId(type = IdType.AUTO)
    private Long pageId;

    /** 平台商id*/
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 运营商ID*/
    @Excel(name = "运营商ID")
    private Long dcId;

    /** 启用时间 */
    @Excel(name = "启用时间")
    private Date enableTime;

    /** 页面名称 */
    @Excel(name = "页面名称")
    private String pageName;

    /** 页面类型;页面类型,index-首页 */
    @Excel(name = "页面类型;页面类型,index-首页")
    private String pageType;

    /** 页面配置JSON */
    @Excel(name = "页面配置JSON")
    private String pageConfig;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    private String channelId;

    /** 区域城市ID */
    @Excel(name = "区域城市ID")
    private Long areaId;

    /** 是否默认 (0非默认 1默认) */
    @Excel(name = "是否默认 (0非默认 1默认)")
    private String defFlag;

    /** 帐号状态 (0正常 1停用) */
    @Excel(name = "帐号状态 (0正常 1停用)")
    private String status;

    /** 删除状态 (0正常 2已删除) */
    private String delFlag;

    /** 配置文件地址 */
    @Excel(name = "配置文件地址")
    private String jsonUrl;

    /** 父级页面ID */
    @Excel(name = "父级页面ID")
    @ApiModelProperty(value = "父级页面ID")
    private Long pid;

    /** 1-一级页面, 2-二级页面 */
    @Excel(name = "1-一级页面, 2-二级页面")
    @ApiModelProperty(value = "1-一级页面, 2-二级页面")
    private Integer level;

    /** 0-没有子页面,1-有子页面 */
    @Excel(name = "0-没有子页面,1-有子页面")
    @ApiModelProperty(value = "0-没有子页面,1-有子页面")
    private Integer hasChild;

    /** 扩展url 限制1024字符*/
    @ApiModelProperty(value = "扩展url, 限制1024字符")
    private String urlDtl;

    @ApiModelProperty("0-固定模版, 1-时效模版")
    private Integer type;

    @ApiModelProperty("有效开始时间")
    private Date startTime;

    @ApiModelProperty("有效结束时间")
    private Date endTime;
}
