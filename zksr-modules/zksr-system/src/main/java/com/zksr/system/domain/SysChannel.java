package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 渠道信息对象 sys_channel
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@TableName(value = "sys_channel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysChannel extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 渠道id */
    @TableId(type = IdType.AUTO)
    private Long channelId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 渠道名 */
    @Excel(name = "渠道名")
    private String channelName;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 是否支持货到付款(0,否 1,是) */
    @Excel(name = "是否支持货到付款(0,否 1,是)")
    private Integer hdfkSupport;

    /**
     * 状态 0-停用 1-启用
     */
    @Excel(name = "状态 0-未删除 1-删除")
    private Integer deleted;

    @Excel(name = "状态 0-停用 1-启用")
    private Integer status;
}
