package com.zksr.system.controller.supplier.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.domain.vo.accInvoiceSetting.AccInvoiceSetttingSimpleBindVO;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 入驻商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("入驻商信息 - sys_supplier分页 Request VO")
public class SysSupplierSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 入驻商编号 */
    @ApiModelProperty(value = "入驻商编号")
    private String supplierCode;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    @NotBlank(message = "入驻商名称不能为空")
    @Size(min = 1, max = 25, message = "入驻商名称长度不能超过25个字符")
    private String supplierName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 状态-数据字典 */
    @Excel(name = "状态-数据字典")
    @ApiModelProperty(value = "状态-数据字典")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否是电子围栏入驻商 */
    @Excel(name = "是否是电子围栏入驻商")
    @ApiModelProperty(value = "是否是电子围栏入驻商")
    private Long dzwlFlag;

    /** 电子围栏 */
    @Excel(name = "电子围栏")
    @ApiModelProperty(value = "电子围栏")
    private String dzwlInfo;

    /** 运营商id;运营商id */
    @Excel(name = "运营商id;运营商id")
    @ApiModelProperty(value = "运营商id;运营商id")
    private Long dcId;

    /** 城市id集合 */
    @Excel(name = "城市id集合")
    @ApiModelProperty(value = "城市id集合")
    private List<Long> areaIds;

    /** 营业执照图片地址*/
    @Excel(name = "营业执照图片地址")
    @ApiModelProperty(value = "营业执照图片地址")
    private String licenceUrl;

    /**
     * 密码
     */
    @Excel(name = "后台管理密码(不可编辑, 只可新增使用)")
    @ApiModelProperty(value = "后台管理密码(不可编辑, 只可新增使用)", example = "123456")
    private String password;

    /**
     * 账号
     */
    @Excel(name = "账号")
    @ApiModelProperty(value = "账号", example = "xxxxx")
    private String userName;

    /**
     * 管理类别id集合
     */
    @Excel(name = "管理类别id集合")
    @ApiModelProperty(value = "管理类别id集合")
    private List<Long> catgoryIds;

    /**
     * 支付平台进件单号
     */
    @ApiModelProperty(value = "支付平台进件单号", required = true, notes = "支付平台进件单号", example = "2024042010100")
    private String payPlatformOrderNo;

    /**
     * 支付账户最小保留金
     */
    @ApiModelProperty(value = "支付账户最小保留金", example = "1000")
    private BigDecimal minSettleAmt;

    /**
     * 起送价
     */
    @ApiModelProperty("本地起送价")
    private BigDecimal minAmt;

    @ApiModelProperty("全国起送价")
    private BigDecimal globalMinAmt;

    @ApiModelProperty("门店ID集合")
    private List<Long> branchIdList;

    @ApiModelProperty(value = "入驻商头像地址")
    private String avatar;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;

    /** 一级管理分润, 销售占比 */
    @Excel(name = "一级管理分润, 销售占比")
    @ApiModelProperty(value = "一级管理分润, 销售占比")
    private List<PrdtSupplierClassRateDTO> supplierClassRateDTOS;

    //支付账号配置
    /** 商户绑定信息 */
    @ApiModelProperty(value = "商户绑定信息")
    private List<PlatformSimpleBindVO> platformSimpleBindList;
    /** 发票绑定信息 */
    @ApiModelProperty(value = "发票绑定信息")
    private List<AccInvoiceSetttingSimpleBindVO> invoceSettingBindList;
}
