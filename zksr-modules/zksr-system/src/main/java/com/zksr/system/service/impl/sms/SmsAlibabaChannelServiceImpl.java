package com.zksr.system.service.impl.sms;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teautil.models.RuntimeOptions;
import com.zksr.common.core.enums.SmsChannelEnum;
import com.zksr.system.api.partnerConfig.dto.SmsConfigDTO;
import com.zksr.system.domain.SysSms;
import com.zksr.system.domain.SysSmsTemplate;
import com.zksr.system.mapper.SysSmsMapper;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysSmsChannelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 阿里云短信服务
 * @date 2024/5/6 8:57
 */
@Slf4j
@Service
public class SmsAlibabaChannelServiceImpl implements ISysSmsChannelService {

    @Autowired
    private SysSmsMapper sysSmsMapper;

    @Autowired
    private ISysCacheService sysCacheService;

    @Override
    public String channel() {
        return SmsChannelEnum.ALIBABA.getChannel();
    }

    @Override
    public void sendSms(SysSms sms, SysSmsTemplate smsTemplate) {
        SmsConfigDTO smsConfig = sysCacheService.getSmsConfig(sms.getSysCode());
        if (Objects.isNull(smsConfig)) {
            log.error("电信发送失败, 短信平台未配置 smsId={}", sms.getSmsId());
            sms.setUseTime(DateUtil.date());
            sms.setMsg("短信平台未配置");
            sysSmsMapper.updateById(sms);
            return;
        }

        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config()
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_ID。
                .setAccessKeyId(smsConfig.getAccessKeyId())
                // 节点
                .setEndpoint("dysmsapi.aliyuncs.com")
                // 必填，请确保代码运行环境设置了环境变量 ALIBABA_CLOUD_ACCESS_KEY_SECRET。
                .setAccessKeySecret(smsConfig.getAccessKeySecret());
        com.aliyun.dysmsapi20170525.models.SendSmsRequest sendSmsRequest = new com.aliyun.dysmsapi20170525.models.SendSmsRequest()
                .setPhoneNumbers(sms.getMobile())
                .setTemplateCode(smsTemplate.getTemplateCode())
                .setTemplateParam(sms.getTemplateParams())
                .setSignName(smsTemplate.getSignName());
        sms.setUseTime(DateUtil.date());
        try {
            com.aliyun.dysmsapi20170525.Client client = new Client(config);
            // 复制代码运行请自行打印 API 的返回值
            SendSmsResponse smsResponse = client.sendSmsWithOptions(sendSmsRequest, new RuntimeOptions());
            log.info("短信发送结果={}", JSON.toJSONString(smsResponse.getBody()));
            sysSmsMapper.updateById(sms);
        } catch (Exception error) {
            sms.setMsg("发送失败");
            sysSmsMapper.updateById(sms);
            log.error("短信发送异常", error);
        }
    }
}
