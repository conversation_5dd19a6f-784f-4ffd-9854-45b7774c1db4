package com.zksr.system.openapi.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OpenapiErrorCodeType;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.portal.api.dto.BranchRegisterReqDTO;
import com.zksr.portal.api.dto.BranchRegisterRespDTO;
import com.zksr.portal.api.member.PortalMemberApi;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.common.core.domain.vo.openapi.receive.PrdInventoryVO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuductOpenDTO;
import com.zksr.product.api.yhdata.YhDataApi;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.convert.brand.SysBrandMemberConvert;
import com.zksr.system.domain.SysArea;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mapper.SysInterfaceLogMapper;
import com.zksr.system.openapi.controller.YhController;
import com.zksr.system.service.ISysAreaCityService;
import com.zksr.system.service.ISysAreaService;
import com.zksr.trade.api.hdfk.HdfkApi;
import com.zksr.trade.api.status.ExpressStatusApi;
import com.zksr.trade.api.supplierAfter.SupplierAfterApi;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.core.constant.OpenApiConstants.*;

@Slf4j
@Component("openapiProcessTemplate")
@SuppressWarnings("all")
public class OpenapiProcessTemplate {

    @Autowired
    private SysInterfaceLogMapper sysInterfaceLogMapper;

    @Autowired
    private  ITokenService tokenService;

    /**
     * 幂等校验
     * @param interfaceLog
     * @return
     */
    private Boolean check(SysInterfaceLog interfaceLog){
        return tokenService.checkReceiveLogAfter(interfaceLog);
    }

    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        return null;
    }

    /**
     * 更新接收日志信息
     * @param sysInterfaceLog
     * @param errMag
     */
    private void updateLog(SysInterfaceLog sysInterfaceLog, boolean result, String errMag) {
        if(B2BRequestType.matchingNotLogType(sysInterfaceLog.getRequestType()) && ToolUtil.isEmpty(sysInterfaceLog.getId())){
            log.error("OpenapiProcessTemplate:updateLog:sysInterfaceLog:{}", JSON.toJSONString(sysInterfaceLog));
            log.error("OpenapiProcessTemplate:updateLog:errMag:{}", errMag);
            return;
        }

        //如果是处理异常
        if(ObjectUtil.isNotNull(errMag)){
            sysInterfaceLog.setStatus(LOG_STATUS_ERR);
            sysInterfaceLog.setMessage(errMag);
        }else{
            if(result){
                sysInterfaceLog.setStatus(LOG_STATUS_SUCCES);
                sysInterfaceLog.setMessage(PROCESS_SUCCESS);
            }else{
                sysInterfaceLog.setStatus(LOG_STATUS_FAIN);
                sysInterfaceLog.setMessage(PROCESS_FAIN);
            }

        }

        sysInterfaceLogMapper.updateById(sysInterfaceLog);
    }

    @DistributedLock(prefix = RedisLockConstants.LOCK_OPENAPI_PROCESS_TEMPLATE, condition = "#sysInterfaceLog.reqId")
    public boolean invoke(SysInterfaceLog sysInterfaceLog) {
        if(!check(sysInterfaceLog)){
            return false;
        }

        //获取接口信息
        B2BRequestType requestType = B2BRequestType.matchingTemplateType(sysInterfaceLog.getRequestType());
        try{
            // 调用子类方法
            Boolean result = process(sysInterfaceLog);
            updateLog(sysInterfaceLog,result,null);
        }catch (ServiceException e){
            log.error("OPENAPI - {}:执行失败，异常信息：", requestType.getInfo(), e);
            updateLog(sysInterfaceLog,false,e.getMessage());
            //校验是否处理异常
            if(OpenapiErrorCodeType.checkErrorCode(e.getCode())){
                throw e;
            }

            return false;
        }

        return true;
    }
}

/**
 * 商品保存
 */
@Service("saveprdt")
@Slf4j
class Saveprdt extends OpenapiProcessTemplate{
    @Resource
    private SpuApi spuApi;
    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        // 获取业务数据并解析
        String bizData = sysInterfaceLog.getBizData();
        SpuOpenDTO spuOpenDTO = JSON.parseObject(bizData, SpuOpenDTO.class);
        List<SpuOpenDTO> spuOpenDTOList = new ArrayList<>();
        spuOpenDTOList.add(spuOpenDTO);
        SpuReceiveVO spuReceiveVO = new SpuReceiveVO();
        spuReceiveVO.setSupplierId(sysInterfaceLog.getSupplierId());
        spuReceiveVO.setSysCode(sysInterfaceLog.getSysCode());
        spuReceiveVO.setSpuOpenDTO(spuOpenDTOList);
        // 调用远程服务新增商品信息
        Boolean checkedData = spuApi.addOrUpdateSpu(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                spuReceiveVO).getCheckedData();
        return checkedData;
    }
}

/**
 * 商品生产日期更新
 */
@Service("saveprdtDate")
@Slf4j
class SaveprdtDate extends OpenapiProcessTemplate{
    @Resource
    private SpuApi spuApi;
    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        // 获取业务数据并解析
        String bizData = sysInterfaceLog.getBizData();
        SpuductOpenDTO spuductOpenDTO = JSON.parseObject(bizData, SpuductOpenDTO.class);
        spuductOpenDTO.setSupplierId(sysInterfaceLog.getSupplierId());
        // 调用远程服务更新商品生产日期信息
        return spuApi.updateProduct(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                spuductOpenDTO).getCheckedData();
    }
}

/**
 * 商品库存更新
 */
@Service("saveprdtStock")
@Slf4j
class SaveprdtStock extends OpenapiProcessTemplate{
    @Resource
    private SkuApi skuApi;
    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        // 获取业务数据并解析
        String bizData = sysInterfaceLog.getBizData();
        PrdInventoryVO prdInventoryVO = JSON.parseObject(bizData, PrdInventoryVO.class);
        prdInventoryVO.setSupplierId(sysInterfaceLog.getSupplierId());
        prdInventoryVO.setSysCode(sysInterfaceLog.getSysCode());
        prdInventoryVO.setSource(SyncSourceType.matchingType(sysInterfaceLog.getSource()));
        // 调用远程服务更新库存信息
        return skuApi.editInventory(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                prdInventoryVO).getCheckedData();
    }
}

/**
 * 订单收货确认
 */
@Service("confirmReceipt")
@Slf4j
class ConfirmReceipt extends OpenapiProcessTemplate{

    @Resource
    private SupplierAfterApi supplierAfterApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        ReceiveOrderTakeDeliveryVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(),ReceiveOrderTakeDeliveryVO.class);

        //执行订单收货确认操作  需要getCheckedData  不然获取不到异常
        return supplierAfterApi.receiveOrderTakeDelivery(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}

/**
 * 售后退货确认
 */
@Service("confirmReturn")
@Slf4j
class ConfirmReturn extends OpenapiProcessTemplate{

    @Resource
    private SupplierAfterApi supplierAfterApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        AfterSalesReturnVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(),AfterSalesReturnVO.class);

        //执行订单发货操作  需要getCheckedData  不然获取不到异常
        return supplierAfterApi.receiveSalesReturn(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}


/**
 * 订单发货
 */
@Service("delivery")
@Slf4j
class Delivery extends OpenapiProcessTemplate{
    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        OrderOutboundReturnVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(),OrderOutboundReturnVO.class);
        String s = SecurityContextHolder.get(OpenapiSecurityConstants.LOGIN_OPENSOURCE);
        //执行订单发货操作  需要getCheckedData  不然获取不到异常
        return supplierOrderApi.receiveOrderOutboundReturn(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}

/**
 * 订单状态
 */
@Service("orderlog")
@Slf4j
class Orderlog extends OpenapiProcessTemplate{

    @Resource
    private ExpressStatusApi expressStatusApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        OrderStateReturnVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(), OrderStateReturnVO.class);

        //执行订单状态操作  需要getCheckedData  不然获取不到异常
        return expressStatusApi.saveExpressStatus(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}

/**
 * 订单接收成功通知
 */
@Service("orderReceiveCallback")
@Slf4j
class OrderReceiveCallback extends OpenapiProcessTemplate{

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        OrderReceiveCallbackVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(), OrderReceiveCallbackVO.class);

        //执行订单状态操作  需要getCheckedData  不然获取不到异常
        return supplierOrderApi.orderReceiveCallback(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}

/**
 * 订单取消接收通知
 */
@Service("orderCancelReceiveCallback")
@Slf4j
class OrderCancelReceiveCallback extends OpenapiProcessTemplate {

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        OrderCancelReceiveCallbackVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(), OrderCancelReceiveCallbackVO.class);

        //执行订单状态操作  需要getCheckedData  不然获取不到异常
        return supplierOrderApi.orderCancelReceiveCallback(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}

/**
 * 订单发货前取消
 */
@Service("orderCancel")
@Slf4j
class OrderCancel extends OpenapiProcessTemplate{

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        OrderCancelVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(), OrderCancelVO.class);

        //执行订单状态操作  需要getCheckedData  不然获取不到异常
        return supplierOrderApi.orderCancel(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}

/**
 * 退货确认前取消(售后取消)
 */
@Service("afterCancel")
@Slf4j
class AfterCancel extends OpenapiProcessTemplate{

    @Resource
    private SupplierAfterApi supplierAfterApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        AfterCancelVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(), AfterCancelVO.class);

        //执行订单状态操作  需要getCheckedData  不然获取不到异常
        return supplierAfterApi.afterCancel(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}


/**
 * 退单接收成功通知
 */
@Service("afterOrderReceiveCallback")
@Slf4j
class AfterOrderReceiveCallback extends OpenapiProcessTemplate {

    @Resource
    private SupplierAfterApi supplierAfterApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        AfterOrderReceiveCallbackVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(), AfterOrderReceiveCallbackVO.class);

        //执行订单状态操作  需要getCheckedData  不然获取不到异常
        return supplierAfterApi.afterOrderReceiveCallback(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }

}

/**
 * 售后状态
 */
@Service("afterlog")
@Slf4j
class Afterlog extends OpenapiProcessTemplate{

    @Resource
    private ExpressStatusApi expressStatusApi;

    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        //获取请求数据
        AfterStateVO vo = JSONObject.parseObject(sysInterfaceLog.getBizData(), AfterStateVO.class);

        //执行订单状态操作  需要getCheckedData  不然获取不到异常
        return expressStatusApi.saveExpressStatus(sysInterfaceLog.getSysCode(),
                sysInterfaceLog.getOpensourceId(),
                vo).getCheckedData();
    }
}


/**
 * 入驻商保存
 */
@Service("savesupplier")
class Savesupplier extends OpenapiProcessTemplate{
    @Resource
    private SupplierApi supplierApi;
    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        // 获取业务数据并解析
        String bizData = sysInterfaceLog.getBizData();
        SysSupplierDTO sysSupplierDTO = JSON.parseObject(bizData, SysSupplierDTO.class);
        sysSupplierDTO.setSysCode(sysInterfaceLog.getSysCode());
        // 调用远程服务新增入驻商信息
        Boolean checkedData = supplierApi.addSupplier(sysInterfaceLog.getSupplierId(),
                sysInterfaceLog.getOpensourceId(),
                sysSupplierDTO).getCheckedData();
        return checkedData;
    }
}
/**
 * 新增货到付款清账能力
 */
@Service("addHdfkSettle")
class addHdfkSettle extends OpenapiProcessTemplate{
    @Resource
    private HdfkApi hdfkApi;
    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        // 获取业务数据并解析
        String bizData = sysInterfaceLog.getBizData();
        OrderHdfkSettleVO orderHdfkSettleVO = JSON.parseObject(bizData, OrderHdfkSettleVO.class);
        // 调用远程服务
        Boolean checkedData = hdfkApi.addHdfkSettle(sysInterfaceLog.getSysCode(),orderHdfkSettleVO).getCheckedData();
        return checkedData;
    }
}



/**
 * 要货数据, 补货单数据
 */
@Service("yhDataProcess")
@Slf4j
class YhDataProcess extends OpenapiProcessTemplate{

    @Resource
    private YhDataApi yhDataApi;

    @Autowired
    private RedisService redisService;

    /**
     * 来自 {@link YhController#submitBatchYh(CreateYhDataReqVO)}
     * @param sysInterfaceLog   接口日志
     * @return  是否成功
     */
    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        CreateYhDataReqVO yhDataReqVO = JSON.parseObject(sysInterfaceLog.getReqData(), CreateYhDataReqVO.class);
        // 保存到redis 速度更快
        String redisKey = RedisConstants.YH_TEMP_LIST + yhDataReqVO.getYhBatchNo();
        redisService.setCacheList(redisKey, yhDataReqVO.getItemList());
        redisService.expire(redisKey, 86400, TimeUnit.SECONDS);
        yhDataReqVO.setItemList(null);
        //执行订单发货操作  需要getCheckedData  不然获取不到异常
        return yhDataApi.saveYhData(yhDataReqVO).getCheckedData();
    }
}

/**
 * ERP app注册门店
 */
@Service("registerBranch")
@Slf4j
class RegisterBranch extends OpenapiProcessTemplate{

    @Resource
    private ISysAreaCityService iSysAreaCityService;

    @Resource
    private PortalMemberApi portalMemberApi;

    @Autowired
    private RedisService redisService;

    @Resource
    private ISysAreaService iSysAreaService;

    /**
     * ERP app注册门店
     * @param sysInterfaceLog   接口日志
     * @return  是否成功
     */
    @Override
    protected Boolean process(SysInterfaceLog sysInterfaceLog) {
        if(null== sysInterfaceLog || StringUtils.isEmpty(sysInterfaceLog.getBizData())){
            log.warn(" 消费处理注册门店消息业务数据为空!");
            return false;
        }

        MemBranchRegisterReqDTO reqDTO = JSON.parseObject(sysInterfaceLog.getBizData(), MemBranchRegisterReqDTO.class);

        BranchRegisterReqDTO branchRegisterReqDTO = SysBrandMemberConvert.INSTANCE.convert2BranchRegisterReqDTO(reqDTO);
        log.info(" 消费处理注册门店消息,{}", JsonUtils.toJsonString(branchRegisterReqDTO));
        if(null == branchRegisterReqDTO){
            return false;
        }

        //匹配省市区，找城市区域
        if(StringUtils.isNotEmpty(branchRegisterReqDTO.getProvinceName()) &&
                StringUtils.isNotEmpty(branchRegisterReqDTO.getCityName()) &&
                StringUtils.isNotEmpty(branchRegisterReqDTO.getDistrictName())){

            SysAreaCity sysAreaCity = iSysAreaCityService.queryByName(branchRegisterReqDTO.getDistrictName(),branchRegisterReqDTO.getCityName(),branchRegisterReqDTO.getProvinceName());
            if(null == sysAreaCity){
                log.warn(" 没有查询到对应的城市信息，省[{}]市[{}]区[{}]",branchRegisterReqDTO.getProvinceName(),branchRegisterReqDTO.getCityName(),branchRegisterReqDTO.getDistrictName());
            }else {
                branchRegisterReqDTO.setThreeAreaCityId(sysAreaCity.getAreaCityId());

                if (branchRegisterReqDTO.getAreaId() == null) {
                    //查找区域城市
                    SysArea sysArea = iSysAreaService.getSysAreaByThreeAreaCityId(sysAreaCity.getAreaCityId());
                    if(null != sysArea){
                        branchRegisterReqDTO.setAreaId(sysArea.getAreaId());
                    }else {
                        log.warn(" 没有查询到对应的区域城市，省[{}]市[{}]区[{}]",branchRegisterReqDTO.getProvinceName(),branchRegisterReqDTO.getCityName(),branchRegisterReqDTO.getDistrictName());
                    }
                }
            }

        }
        CommonResult<BranchRegisterRespDTO> result = null;
        try {
            result = portalMemberApi.registerBranch(branchRegisterReqDTO);
        } catch (ServiceException e) {
            log.warn(" portalMemberApi.registerBranch校验不通过，", e);
            throw e;
        } catch (Exception e){
            log.error(" portalMemberApi.registerBranch异常，",e);
            throw new RuntimeException(e.getMessage());
        } finally {
        }

        if(null == result || result.isError()){
            log.error(" portalMemberApi.registerBranch门店注册失败,{}",JsonUtils.toJsonString(result));
            throw new ServiceException(String.format("门店注册失败[%s]",null == result ? "接口调用异常" : result.getMsg()));
//            return false;
        }
        return true;
    }
}