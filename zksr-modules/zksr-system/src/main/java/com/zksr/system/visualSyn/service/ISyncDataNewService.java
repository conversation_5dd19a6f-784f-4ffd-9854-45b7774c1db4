package com.zksr.system.visualSyn.service;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.visualSyn.dto.SyncDataResult;

/**
*  对外系统数据组装Service
* @date 2024/10/24 9:59
* <AUTHOR>
*/
public interface ISyncDataNewService {

    String getName();

    /**
     * 组装门店信息
     *
     * @param data
     * @param master
     * @param opensourceDto
     * @return
     */
    SyncDataResult syncBranch(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto);

    /**
     * 组装销售订单信息
     *
     * @param data
     * @param master
     * @param opensourceDto
     * @return
     */
    SyncDataResult syncOrder(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto);

    /**
     * 组装售后订单信息
     *
     * @param data
     * @param master
     * @param opensourceDto
     * @return
     */
    SyncDataResult syncAfter(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto);

    /**
     * 组装收款订单信息
     *
     * @param data
     * @param master
     * @param opensourceDto
     * @return
     */
    SyncDataResult syncReceipt(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto);

    /**
     * 组装门店储值充值、提现信息
     *
     * @param data
     * @param master
     * @param opensourceDto
     * @return
     */
    SyncDataResult syncBranchValueInfo(SyncDataDTO data, VisualSettingMasterDto master, OpensourceDto opensourceDto);
}
