package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.controller.openability.vo.SysOpenabilityReqVO;
import com.zksr.system.domain.SysOpenability;
import com.zksr.system.controller.openability.vo.SysOpenabilityPageReqVO;
import com.zksr.system.controller.openability.vo.SysOpenabilitySaveReqVO;

import java.util.List;

/**
 * 开放能力Service接口
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
public interface ISysOpenabilityService {

    /**
     * 新增开放能力
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysOpenability(@Valid SysOpenabilitySaveReqVO createReqVO);

    /**
     * 修改开放能力
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysOpenability(@Valid SysOpenabilitySaveReqVO updateReqVO);

    /**
     * 删除开放能力
     *
     * @param openabilityId 开放能力id
     */
    public void deleteSysOpenability(Long openabilityId);

    /**
     * 批量删除开放能力
     *
     * @param openabilityIds 需要删除的开放能力主键集合
     * @return 结果
     */
    public void deleteSysOpenabilityByOpenabilityIds(Long[] openabilityIds);

    /**
     * 获得开放能力
     *
     * @param openabilityId 开放能力id
     * @return 开放能力
     */
    public SysOpenability getSysOpenability(Long openabilityId);

    /**
     * 获得开放能力分页
     *
     * @param pageReqVO 分页查询
     * @return 开放能力分页
     */
    PageResult<SysOpenability> getSysOpenabilityPage(SysOpenabilityPageReqVO pageReqVO);

    /**
     * 获得开放能力列表
     *
     * @param pageReqVO 查询
     * @return 开放能力列表
     */
    List<SysOpenability> getSysOpenabilityList(SysOpenabilityReqVO pageReqVO);

}
