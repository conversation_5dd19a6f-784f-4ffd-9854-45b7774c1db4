package com.zksr.system.amap.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @Description: 多边形围栏返回实体类
* @Author: liuxingyu
* @Date: 2024/3/27 16:13
*/
@Data
public class PolygonGeofenceResult {

    /**
     * 0代表成功，其他参考错误码说明
     */
    @ApiModelProperty("返回状态码")
    private Long errcode;

    @ApiModelProperty("返回状态描述")
    private String errmsg;

    @ApiModelProperty("返回详细描述")
    private String errdetail;

    @ApiModelProperty("返回对象")
    private Data data;

    @lombok.Data
    public static class Data {
        @ApiModelProperty("围栏的唯一标识")
        private Long gfid;
    }
}
