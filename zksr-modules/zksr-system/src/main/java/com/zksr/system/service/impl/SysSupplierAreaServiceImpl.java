package com.zksr.system.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.supplierArea.dto.SupplierAreaDTO;
import com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO;
import com.zksr.system.domain.SysSupplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysSupplierAreaMapper;
import com.zksr.system.domain.SysSupplierArea;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaPageReqVO;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaSaveReqVO;
import com.zksr.system.service.ISysSupplierAreaService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 入驻商-区域城市关联关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Service
public class SysSupplierAreaServiceImpl implements ISysSupplierAreaService {
    @Autowired
    private SysSupplierAreaMapper sysSupplierAreaMapper;

    /**
     * 新增入驻商-区域城市关联关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysSupplierArea(SysSupplierAreaSaveReqVO createReqVO) {
        // 插入
        SysSupplierArea sysSupplierArea = HutoolBeanUtils.toBean(createReqVO, SysSupplierArea.class);
        sysSupplierAreaMapper.insert(sysSupplierArea);
        // 返回
        return sysSupplierArea.getSysCode();
    }

    /**
     * 修改入驻商-区域城市关联关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysSupplierArea(SysSupplierAreaSaveReqVO updateReqVO) {
        sysSupplierAreaMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysSupplierArea.class));
    }

    /**
     * 删除入驻商-区域城市关联关系
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deleteSysSupplierArea(Long sysCode) {
        // 删除
        sysSupplierAreaMapper.deleteById(sysCode);
    }

    /**
     * 批量删除入驻商-区域城市关联关系
     *
     * @param sysCodes 需要删除的入驻商-区域城市关联关系主键
     * @return 结果
     */
    @Override
    public void deleteSysSupplierAreaBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deleteSysSupplierArea(sysCode);
        }
    }

    /**
     * 获得入驻商-区域城市关联关系
     *
     * @param sysCode 平台商id
     * @return 入驻商-区域城市关联关系
     */
    @Override
    public SysSupplierArea getSysSupplierArea(Long sysCode) {
        return sysSupplierAreaMapper.selectById(sysCode);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysSupplierArea> getSysSupplierAreaPage(SysSupplierAreaPageReqVO pageReqVO) {
        return sysSupplierAreaMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SupplierAreaDTO> getSupplierAreaByAreaId(Long areaId) {
        return HutoolBeanUtils.toBean(sysSupplierAreaMapper.getByAreaId(areaId),SupplierAreaDTO.class);
    }

    @Override
    public List<SupplierDzwlAreaDTO> getDzwlSupplierInfoByAreaId(Long areaId) {
        return sysSupplierAreaMapper.getDzwlSupplierInfoByAreaId(areaId);
    }

    private void validateSysSupplierAreaExists(Long sysCode) {
        if (sysSupplierAreaMapper.selectById(sysCode) == null) {
            throw exception(SYS_SUPPLIER_AREA_NOT_EXISTS);
        }
    }

    public List<SysSupplier> getSupplierListByIds(List<Long> supplierIds) {
        return sysSupplierAreaMapper.getSupplierListByIds(supplierIds);
    }

    public List<Long> selectListBySupplierId(Long supplierId){
        return sysSupplierAreaMapper.selectListBySupplierId(supplierId);
    }
}
