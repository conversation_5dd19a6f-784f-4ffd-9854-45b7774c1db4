package com.zksr.system.service;

import javax.validation.*;

import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.system.domain.SysMessageTemplate;
import com.zksr.system.controller.message.vo.SysMessageTemplatePageReqVO;
import com.zksr.system.controller.message.vo.SysMessageTemplateSaveReqVO;

import java.util.List;

/**
 * 公众号, 小程序订阅消息模版Service接口
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
public interface ISysMessageTemplateService {

    /**
     * 新增公众号, 小程序订阅消息模版
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysMessageTemplate(@Valid SysMessageTemplateSaveReqVO createReqVO);

    /**
     * 修改公众号, 小程序订阅消息模版
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysMessageTemplate(@Valid SysMessageTemplateSaveReqVO updateReqVO);

    /**
     * 删除公众号, 小程序订阅消息模版
     *
     * @param messageTemplateId 消息模版id
     */
    public void deleteSysMessageTemplate(Long messageTemplateId);

    /**
     * 批量删除公众号, 小程序订阅消息模版
     *
     * @param messageTemplateIds 需要删除的公众号, 小程序订阅消息模版主键集合
     * @return 结果
     */
    public void deleteSysMessageTemplateByMessageTemplateIds(Long[] messageTemplateIds);

    /**
     * 获得公众号, 小程序订阅消息模版
     *
     * @param messageTemplateId 消息模版id
     * @return 公众号, 小程序订阅消息模版
     */
    public SysMessageTemplate getSysMessageTemplate(Long messageTemplateId);

    /**
     * 获得公众号, 小程序订阅消息模版分页
     *
     * @param pageReqVO 分页查询
     * @return 公众号, 小程序订阅消息模版分页
     */
    PageResult<SysMessageTemplate> getSysMessageTemplatePage(SysMessageTemplatePageReqVO pageReqVO);

    /**
     * 停用
     * @param messageTemplateId
     */
    void disable(Long messageTemplateId);

    /**
     * 启用
     * @param messageTemplateId
     */
    void enable(Long messageTemplateId);

    /**
     * 发送消息
     * @param messageList
     */
    void sendMsg(List<CommonMessageDTO> messageList, MessageTemplateDTO messageTemplate);

    /**
     * 获取消息模版
     * @param sysCode   大区
     * @param scene     场景
     * @return
     */
    List<MessageTemplateDTO> getTemplateBySysCodeAndScene(Long sysCode, Long scene);
}
