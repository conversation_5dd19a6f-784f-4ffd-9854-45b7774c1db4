package com.zksr.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplatePageReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplateSaveReqVO;
import com.zksr.system.convert.pageConfig.SysPagesConfigTemplateConvert;
import com.zksr.system.domain.SysPagesConfig;
import com.zksr.system.domain.SysPagesConfigTemplate;
import com.zksr.system.mapper.SysPagesConfigTemplateMapper;
import com.zksr.system.service.ISysPagesConfigTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 平台页面配置模版Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Service
public class SysPagesConfigTemplateServiceImpl implements ISysPagesConfigTemplateService {
    @Autowired
    private SysPagesConfigTemplateMapper sysPagesConfigTemplateMapper;

    /**
     * 新增平台页面配置模版
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysPagesConfigTemplate(SysPagesConfigTemplateSaveReqVO createReqVO) {
        // 插入
        SysPagesConfigTemplate sysPagesConfigTemplate = SysPagesConfigTemplateConvert.INSTANCE.convert(createReqVO);
        sysPagesConfigTemplateMapper.insert(sysPagesConfigTemplate);
        // 返回
        return sysPagesConfigTemplate.getPageTemplateId();
    }

    /**
     * 修改平台页面配置模版
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysPagesConfigTemplate(SysPagesConfigTemplateSaveReqVO updateReqVO) {
        sysPagesConfigTemplateMapper.updateById(SysPagesConfigTemplateConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除平台页面配置模版
     *
     * @param pageId 自定义页面ID
     */
    @Override
    public void deleteSysPagesConfigTemplate(Long pageId) {
        SysPagesConfigTemplate pagesConfig = sysPagesConfigTemplateMapper.selectById(pageId);
        // 删除
        pagesConfig.setDelFlag(DelFlagConstants.DISABLE);
        pagesConfig.setUpdateTime(DateUtil.date());
        pagesConfig.setUpdateBy(SecurityUtils.getUsername());
        sysPagesConfigTemplateMapper.updateById(pagesConfig);
    }

    /**
     * 批量删除平台页面配置模版
     *
     * @param pageIds 需要删除的平台页面配置模版主键
     * @return 结果
     */
    @Override
    public void deleteSysPagesConfigTemplateByPageIds(Long[] pageIds) {
        for(Long pageId : pageIds){
            this.deleteSysPagesConfigTemplate(pageId);
        }
    }

    /**
     * 获得平台页面配置模版
     *
     * @param pageId 自定义页面ID
     * @return 平台页面配置模版
     */
    @Override
    public SysPagesConfigTemplate getSysPagesConfigTemplate(Long pageId) {
        return sysPagesConfigTemplateMapper.selectById(pageId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysPagesConfigTemplate> getSysPagesConfigTemplatePage(SysPagesConfigTemplatePageReqVO pageReqVO) {
        return sysPagesConfigTemplateMapper.selectPage(pageReqVO);
    }

    @Override
    public void updateStatus(SysPagesConfigTemplateSaveReqVO updateReqVO) {
        // 如果是启用, 则需要停用其他的
        SysPagesConfigTemplate sysPagesConfig = new SysPagesConfigTemplate();
        sysPagesConfig.setPageTemplateId(updateReqVO.getPageTemplateId());
        sysPagesConfig.setStatus(updateReqVO.getStatus());
        sysPagesConfig.setEnableTime(DateUtil.date());
        sysPagesConfigTemplateMapper.updateById(sysPagesConfig);
    }
}
