package com.zksr.system.hisense;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.zksr.common.core.enums.HisenseApiEnum;
import com.zksr.system.config.HisenseApiConfig;
import com.zksr.system.hisense.utils.HmacSignatureUtil;
import com.zksr.common.core.utils.OkHttp3Util;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public abstract class BaseApiClient {

    @Autowired
    protected HisenseApiConfig config;

    protected ObjectMapper objectMapper = new ObjectMapper();

//    public BaseApiClient(String baseUrl, String appId, String appKey) {
//        this.baseUrl = baseUrl;
//        this.appId = appId;
//        this.appKey = appKey;
//    }

    /**
     * 发送带签名的POST请求
     */
    protected <T> T postWithSignature(HisenseApiEnum apiEnum, Object requestBody, Class<T> responseClass) throws IOException {
        return postWithSignature(apiEnum, null, requestBody, responseClass);
    }

    /**
     * 发送带签名的POST请求（带查询参数）
     */
    protected <T> T postWithSignature(HisenseApiEnum apiEnum, Map<String, String> queryParams,
                                      Object requestBody, Class<T> responseClass) throws IOException {
        HisenseApiConfig.UriConfig uriConfig = config.getUriConfig(apiEnum.getCode());
        if (queryParams == null) {
            queryParams = Maps.newHashMapWithExpectedSize(2);
        }
        queryParams.put("app_id", uriConfig.getAppId());
        queryParams.put("app_key", uriConfig.getAppKey());

        String url = config.getBaseUrl() + "/" + apiEnum.getCode();
        url += "?" + buildQueryString(queryParams);

        // 获取当前GMT时间
        String gmtDate = HmacSignatureUtil.getCurrentGMTDate();

        // 需要签名的请求头
        Map<String, String> signedHeaders = new LinkedHashMap<>();

        // 生成签名
        String signature = HmacSignatureUtil.generateSignature(
                uriConfig.getAppKey(), "POST", uriConfig.getUri(), queryParams, uriConfig.getAppId(), gmtDate, signedHeaders);

        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("X-HMAC-SIGNATURE", signature);
        headers.put("X-HMAC-ALGORITHM", "hmac-sha256");
        headers.put("X-HMAC-ACCESS-KEY", uriConfig.getAppId());
        headers.put("Date", gmtDate);
        headers.put("Content-Type", "application/json");

        // 发送请求
        String response = OkHttp3Util.post(url, headers, requestBody);

        // 解析响应
        return objectMapper.readValue(response, responseClass);
    }

    /**
     * 发送带签名的GET请求
     */
    protected <T> T getWithSignature(HisenseApiEnum apiEnum, Map<String, String> queryParams,
                                     Class<T> responseClass) throws IOException {
        HisenseApiConfig.UriConfig uriConfig = config.getUriConfig(apiEnum.getCode());
        if (queryParams == null) {
            queryParams = Maps.newHashMapWithExpectedSize(2);
        }
        queryParams.put("app_id", uriConfig.getAppId());
        queryParams.put("app_key", uriConfig.getAppKey());

        String url = config.getBaseUrl() + "/" + apiEnum.getCode();
        url += "?" + buildQueryString(queryParams);

        // 获取当前GMT时间
        String gmtDate = HmacSignatureUtil.getCurrentGMTDate();

        // 需要签名的请求头
        Map<String, String> signedHeaders = new LinkedHashMap<>();
//        signedHeaders.put("User-Agent", "ECPX-Java-Client/1.0");

        // 生成签名
        String signature = HmacSignatureUtil.generateSignature(
                uriConfig.getAppKey(), "GET", uriConfig.getUri(), queryParams, uriConfig.getAppId(), gmtDate, signedHeaders);

        // 构建请求头
        Map<String, String> headers = new HashMap<>();
        headers.put("X-HMAC-SIGNATURE", signature);
        headers.put("X-HMAC-ALGORITHM", "hmac-sha256");
        headers.put("X-HMAC-ACCESS-KEY", uriConfig.getAppId());
        headers.put("Date", gmtDate);
//        headers.put("X-HMAC-SIGNED-HEADERS", "User-Agent");
//        headers.put("User-Agent", "ECPX-Java-Client/1.0");

        // 发送请求
        String response = OkHttp3Util.get(url, headers);

        // 解析响应
        return objectMapper.readValue(response, responseClass);
    }

    private String buildQueryString(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return sb.toString();
    }
}
