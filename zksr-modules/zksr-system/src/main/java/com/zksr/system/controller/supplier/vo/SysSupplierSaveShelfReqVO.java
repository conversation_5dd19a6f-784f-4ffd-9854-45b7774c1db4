package com.zksr.system.controller.supplier.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 入驻商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("入驻商信息 - sys_supplier分页 Request VO")
public class SysSupplierSaveShelfReqVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    @ApiModelProperty(value = "入驻商头像地址")
    private String avatar;

    @Excel(name = "本地起送价")
    @ApiModelProperty(value = "本地起送价")
    private BigDecimal minAmt;

    @Excel(name = "全国起送价")
    @ApiModelProperty(value = "全国起送价")
    private BigDecimal globalMinAmt;

    @ApiModelProperty("入驻商其他配置")
    private SupplierOtherSettingPolicyDTO supplierOtherSettingPolicyDTO;
}
