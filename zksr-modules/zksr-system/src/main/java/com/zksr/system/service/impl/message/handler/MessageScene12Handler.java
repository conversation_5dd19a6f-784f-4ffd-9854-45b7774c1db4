package com.zksr.system.service.impl.message.handler;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.system.api.commonMessage.dto.IFlowContext;
import com.zksr.system.api.commonMessage.dto.MessageTable;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderAfterFinishVO;
import com.zksr.system.enums.MessageParamEum;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.impl.message.SubscribeMessageHandler;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.dto.SupplierAfterDtlDTO;
import com.zksr.trade.api.after.dto.TrdSupplierAfterDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *  售后完成通知
 */
@Slf4j
@Service
public class MessageScene12Handler extends SubscribeMessageHandler<Long> {

    @Override
    public Integer scene() {
        return CommonMessageSceneEnum.BRANCH_REGISTER.getScene();
    }

    @Override
    protected void initFlowContext() {
        // 初始化本消息公用变量
        FlowContext context = new FlowContext();
        // 收集参数, 发送通知操作
        Long branchId = getEventData();// 用户门店信息
        BranchDTO branchDTO = sysCacheService.getBranchDTO(branchId);
        // 填充参数上下午支持模版自定义参数
        MessageTable messageTable = MessageTable.builder().build();
        messageTable.setBranchName(branchDTO.getBranchName())
                .setBranchAddr(branchDTO.getBranchAddr())
                .put(MessageParamEum.BRANCH_CREATE_TIME, DateUtil.formatDateTime(DateUtil.date()))
                .put(MessageParamEum.BRANCH_CONTRACT_PHONE, branchDTO.getContactPhone())
        ;
        context.setBranch(branchDTO);;
        // 设置到流程里面去
        messageContext().setFlowContext(context);
        messageContext().setMessageTable(messageTable);
    }

    @Override
    public List<CommonMessageDTO> colonelHandler(MessageTemplateDTO messageTemplateDTO) {
        FlowContext flowContext = messageContext().getFlowContext(FlowContext.class);
        Long colonelId = flowContext.getBranch().getColonelId();
        // 获取业务员
        ColonelDTO colonel = sysCacheService.getColonel(colonelId);
        if (Objects.isNull(colonel)) {
            return ListUtil.empty();
        }
        // 组装消息主体
        CommonMessageDTO messageDTO = CommonMessageDTO.builder()
                .merchantId(colonel.getColonelId())
                .body(messageTemplateDTO.buildSubscribeMessageDTO(messageContext().getMessageTable()))
                .build();
        return ListUtil.toList(messageDTO);
    }

    @Data
    @ApiModel(description = "消息内部容器")
    public static class FlowContext implements IFlowContext {

        @ApiModelProperty("门店")
        private BranchDTO branch;
    }
}
