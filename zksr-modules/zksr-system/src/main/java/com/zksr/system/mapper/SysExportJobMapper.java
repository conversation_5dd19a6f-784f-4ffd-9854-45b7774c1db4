package com.zksr.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.export.vo.SysExportJob;
import com.zksr.system.api.export.vo.SysExportJobPageReqVO;
import com.zksr.system.api.export.vo.SysExportJobRespVO;
import com.zksr.system.domain.SysDc;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/1/15
 * @desc 导出表 字典层
 */
@Mapper
public interface SysExportJobMapper extends BaseMapperX<SysExportJob> {

    /**
     * 获取等待执行导出列表
     * @return
     */
    List<SysExportJob> selectWaitJobList();

    void insertJob(SysExportJob exportJob);

    List<SysExportJobRespVO> selectPageExt(SysExportJobPageReqVO pageReqVO);
}
