package com.zksr.system.api.fileImport;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.service.ISysFileImportDtlService;
import com.zksr.system.service.ISysFileImportService;
import io.swagger.models.auth.In;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

@RestController
@ApiIgnore
public class FileImportApiImpl implements FileImportApi{

    @Resource
    private ISysFileImportService sysFileImportService;

    @Resource
    private ISysFileImportDtlService sysFileImportDtlService;


    public CommonResult<SysFileImport> getFileImportById(Long fileImportId){
        return CommonResult.success(sysFileImportService.getById(fileImportId));
    }

    public CommonResult<Integer> updateFileImport(SysFileImport sysFileImport){
        return CommonResult.success(sysFileImportService.updateSysFileImport(sysFileImport));
    }

    public CommonResult<Integer> updateImportStatus(Long fileImportId, Integer importStatus){
        return CommonResult.success(sysFileImportService.updateImportStatus(fileImportId,importStatus));
    }

    public CommonResult<Boolean> batchAddFileImportDtl(List<SysFileImportDtl> list){
        return CommonResult.success(sysFileImportDtlService.batchAdd(list));
    }

    public CommonResult<List<SysFileImport>> findGoingEvent(){
        return CommonResult.success(sysFileImportService.findGoingEvent());
    }

}
