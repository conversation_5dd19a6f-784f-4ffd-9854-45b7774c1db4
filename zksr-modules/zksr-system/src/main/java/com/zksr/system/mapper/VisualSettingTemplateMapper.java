package com.zksr.system.mapper;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplatePageReqVO;
import com.zksr.system.domain.VisualSettingTemplate;
import org.apache.ibatis.annotations.Mapper;


/**
 * 可视化接口模板Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Mapper
public interface VisualSettingTemplateMapper extends BaseMapperX<VisualSettingTemplate> {
    default PageResult<VisualSettingTemplate> selectPage(VisualSettingTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisualSettingTemplate>()
                    .eqIfPresent(VisualSettingTemplate::getVisualTemplateId, reqVO.getVisualTemplateId())
                    .eqIfPresent(VisualSettingTemplate::getStatus, reqVO.getStatus())
                    .likeIfPresent(VisualSettingTemplate::getTemplateName, reqVO.getTemplateName())
                    .eqIfPresent(VisualSettingTemplate::getApiTemplate, reqVO.getApiTemplate())
                    .eqIfPresent(VisualSettingTemplate::getTemplateType, reqVO.getTemplateType())
                    .eqIfPresent(VisualSettingTemplate::getSourceType, reqVO.getSourceType())
                .orderByDesc(VisualSettingTemplate::getVisualTemplateId));
    }
}
