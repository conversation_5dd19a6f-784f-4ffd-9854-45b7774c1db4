package com.zksr.system.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.member.api.member.vo.MemMemberPageReqVO;
import com.zksr.system.controller.partnerPolicy.vo.SearchConfigPageRespVO;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyPageReqVO;
import com.zksr.system.domain.SysPartnerPolicy;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 平台商政策(由平台商设置)Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Mapper
public interface SysPartnerPolicyMapper extends BaseMapperX<SysPartnerPolicy> {
    default PageResult<SysPartnerPolicy> selectPage(SysPartnerPolicyPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysPartnerPolicy>()
                .eqIfPresent(SysPartnerPolicy::getPartnerPolicyId, reqVO.getPartnerPolicyId())
                .eqIfPresent(SysPartnerPolicy::getSysCode, reqVO.getSysCode())
                .likeIfPresent(SysPartnerPolicy::getPolicyName, reqVO.getPolicyName())
                .eqIfPresent(SysPartnerPolicy::getPolicyKey, reqVO.getPolicyKey())
                .eqIfPresent(SysPartnerPolicy::getPolicyValue, reqVO.getPolicyValue())
                .eqIfPresent(SysPartnerPolicy::getPolicyType, reqVO.getPolicyType())
                .orderByDesc(SysPartnerPolicy::getPartnerPolicyId));
    }

    /**
     * @Description: 通过平台编号获取配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/21 19:13
     */
    default List<SysPartnerPolicy> selectBySysCode(Long sysCode, Long dcId, Long supplierId) {
        return selectList(new LambdaQueryWrapper<SysPartnerPolicy>()
                .eq(ObjectUtil.isNotNull(sysCode), SysPartnerPolicy::getSysCode, sysCode)
                .eq(ObjectUtil.isNotNull(dcId), SysPartnerPolicy::getDcId, dcId)
                .eq(ObjectUtil.isNotNull(supplierId), SysPartnerPolicy::getSupplierId, supplierId));
    }

    Page<SearchConfigPageRespVO> selectSearchConfigPage(@Param("reqVO") SysPartnerPolicyPageReqVO reqVO, @Param("page") Page<SysPartnerPolicyPageReqVO> page);


    /**
     * 根据区域城市ID获取搜索配置
     * @param areaId
     * @return
     */
    default SysPartnerPolicy getSearchConfig(Long areaId) {
        return selectOne(new LambdaQueryWrapper<SysPartnerPolicy>()
                .eq(SysPartnerPolicy::getAreaId, areaId)
                .eq(SysPartnerPolicy::getPolicyType, 19));
    }
}
