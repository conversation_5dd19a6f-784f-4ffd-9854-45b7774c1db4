package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysBankChannel;
import com.zksr.system.api.bank.vo.SysBankChannelPageReqVO;


/**
 * 联行号信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Mapper
public interface SysBankChannelMapper extends BaseMapperX<SysBankChannel> {
    default PageResult<SysBankChannel> selectPage(SysBankChannelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysBankChannel>()
                    .eqIfPresent(SysBankChannel::getBankChannelId, reqVO.getBankChannelId())
                    .eqIfPresent(SysBankChannel::getBankNo, reqVO.getBankNo())
                    .likeIfPresent(SysBankChannel::getBankName, reqVO.getBankName())
                    .likeIfPresent(SysBankChannel::getBranchName, reqVO.getBranchName())
                    .eqIfPresent(SysBankChannel::getBankChannelNo, reqVO.getBankChannelNo())
                    .eqIfPresent(SysBankChannel::getStatus, reqVO.getStatus())
                    .likeIfPresent(SysBankChannel::getProvinceName, reqVO.getProvinceName())
                    .likeIfPresent(SysBankChannel::getCityName, reqVO.getCityName())
                .orderByDesc(SysBankChannel::getBankChannelId));
    }

    default SysBankChannel selectByChannelNo(String bankChannelNo) {
        return selectOne(new LambdaQueryWrapperX<SysBankChannel>()
                .eqIfPresent(SysBankChannel::getBankChannelNo, bankChannelNo)
        );
    }
}
