package com.zksr.system.controller.sms;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysSms;
import com.zksr.system.service.ISysSmsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.sms.vo.SysSmsPageReqVO;
import com.zksr.system.controller.sms.vo.SysSmsSaveReqVO;
import com.zksr.system.controller.sms.vo.SysSmsRespVO;
import com.zksr.system.convert.sms.SysSmsConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 短信消息Controller
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Api(tags = "管理后台 - 短信消息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/sms")
public class SysSmsController {
    @Autowired
    private ISysSmsService sysSmsService;

    /**
     * 分页查询短信消息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得短信消息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysSmsRespVO>> getPage(@Valid SysSmsPageReqVO pageReqVO) {
        PageResult<SysSms> pageResult = sysSmsService.getSysSmsPage(pageReqVO);
        return success(SysSmsConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:sms:add";
        /** 编辑 */
        public static final String EDIT = "system:sms:edit";
        /** 删除 */
        public static final String DELETE = "system:sms:remove";
        /** 列表 */
        public static final String LIST = "system:sms:list";
        /** 查询 */
        public static final String GET = "system:sms:query";
        /** 停用 */
        public static final String DISABLE = "system:sms:disable";
        /** 启用 */
        public static final String ENABLE = "system:sms:enable";
    }
}
