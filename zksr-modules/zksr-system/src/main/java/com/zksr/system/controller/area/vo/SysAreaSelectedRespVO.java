package com.zksr.system.controller.area.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 区域城市对象 sys_area
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@Data
@ApiModel("区域城市 - sys_area selected Response VO")
@AllArgsConstructor
@NoArgsConstructor
public class SysAreaSelectedRespVO {
    private static final long serialVersionUID = 1L;

    /** 区域城市id */
    @Excel(name = "区域城市id")
    @ApiModelProperty(value = "区域城市id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 区域城市名 */
    @Excel(name = "区域城市名")
    @ApiModelProperty(value = "区域城市名", example = "示例值")
    private String areaName;
}
