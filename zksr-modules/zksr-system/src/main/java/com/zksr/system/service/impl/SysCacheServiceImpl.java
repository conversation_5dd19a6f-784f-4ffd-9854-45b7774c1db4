package com.zksr.system.service.impl;

import com.alicp.jetcache.Cache;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.SmsConfigDTO;
import com.zksr.system.api.partnerConfig.enums.PartnerConfigEnum;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigRespVO;
import com.zksr.system.convert.supplier.SupplierConvert;
import com.zksr.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/11 19:42
 */
@Service
public class SysCacheServiceImpl implements ISysCacheService {

    @Autowired
    private Cache<Long, SmsConfigDTO> smsConfigDTOCache;

    @Autowired
    private ISysPartnerConfigService partnerConfigService;

    @Autowired
    private Cache<Long, OpensourceDto> opensourceDtoByMerchantIdCache;

    @Autowired
    private ISysOpensourceService sysOpensourceService;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;

    @Autowired
    private Cache<Long, SpuDTO> spuDTOCache;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;

    @Autowired
    private Cache<Long, AppletBaseConfigDTO> appletBaseConfigDTOCache;

    @Autowired
    private Cache<String, List<MessageTemplateDTO>> messageTemplateCache;

    @Autowired
    @Qualifier("visualSettingDetailBySupplierIdCache")
    private Cache<String, VisualSettingDetailDto> visualSettingDetailBySupplierIdCache;

    @Autowired
    @Qualifier("visualSettingMasterCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterCache;

    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;

    @Autowired
    @Qualifier("visualSettingMasterBySupplierIdCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterBySupplierIdCache;

    @Autowired
    private Cache<Long, VisualSettingTemplateDto> visualSettingTemplateCache;

    @Autowired
    private Cache<Long, MemberDTO> memberDTOCache;

    @Autowired
    private Cache<Long, PayConfigDTO> payConfigDTOCache;

    @Resource
    private SpuApi spuApi;

    @Resource
    private ChannelApi channelApi;

    @Resource
    private SkuApi skuApi;

    @Resource
    private BranchApi branchApi;

    @Resource
    private BrandApi brandApi;

    @Resource
    private ColonelApi colonelApi;

    @Resource
    private AreaApi areaApi;

    @Autowired
    private ISysPartnerConfigService sysPartnerConfigService;

    @Autowired
    private IVisualSettingMasterService visualSettingMasterService;

    @Autowired
    private IVisualSettingDetailService visualSettingDetailService;

    @Autowired
    private IVisualSettingTemplateService visualSettingTemplateService;

    @Autowired
    private ISysAreaService areaService;

    @Autowired
    private Cache<Long, ChannelDTO> channelDTOCache;

    @Autowired
    private Cache<Long, AreaDTO> areaDtoCache;

    @Autowired
    private Cache<Long, BrandDTO> brandDtoCache;

    @Resource
    private MemberApi memberApi;

    @PostConstruct
    public void init() {
        //自动load（read through）
        smsConfigDTOCache.config().setLoader(this::loadSmsConfigFromApi);
        opensourceDtoByMerchantIdCache.config().setLoader(this::loadOpensourceByMerchantIdFromApi);
        spuDTOCache.config().setLoader(this::loadSpuDtoFromApi);
        skuDTOCache.config().setLoader(this::loadSkuDtoFromApi);
        branchDTOCache.config().setLoader(this::loadBranchDtoFromApi);
        supplierDTOCache.config().setLoader(this::loadBranchDtoFromService);
        appletBaseConfigDTOCache.config().setLoader(this::loadAppletBaseConfigDtoFromService);
        messageTemplateCache.config().setLoader(this::loadMessageTemplateDtoFromService);
        visualSettingMasterCache.config().setLoader(this::loadVisualSettingMasterService);
        visualSettingDetailBySupplierIdCache.config().setLoader(this::loadVisualSettingDetailBySupplierIdCacheService);
        channelDTOCache.config().setLoader(this::loadChannelDTOFromApi);
        areaDtoCache.config().setLoader(this::loadAreaDtoFromApi);
        colonelDTOCache.config().setLoader(this::loadColonelDtoFromApi);
        brandDtoCache.config().setLoader(this::loadBrandDTOFromApi);
        visualSettingMasterBySupplierIdCache.config().setLoader(this::loadVisualSettingMasterBySupplierIdFromService);
        visualSettingTemplateCache.config().setLoader(this::loadVisualSettingTemplateFromService);
        memberDTOCache.config().setLoader(this::loadMemberFromService);
        payConfigDTOCache.config().setLoader(this::loadPayConfigDTO);
    }

    private PayConfigDTO loadPayConfigDTO(Long sysCode){
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.PAY_CONFIG.getType());
        return partnerConfig.getPayConfigDTO();
    }

    private MemberDTO loadMemberFromService(Long memberId) {
        return memberApi.getMemBerByMemberId(memberId).getCheckedData();
    }

    private List<MessageTemplateDTO> loadMessageTemplateDtoFromService(String string) {
        String[] split = string.split(StringPool.COLON);
        Long sysCode = Long.parseLong(split[0]);
        Long scene = Long.parseLong(split[1]);
        return SpringUtils.getBean(ISysMessageTemplateService.class).getTemplateBySysCodeAndScene(sysCode, scene);
    }

    private VisualSettingMasterDto loadVisualSettingMasterService(Long visualMasterId){
       return HutoolBeanUtils.toBean(visualSettingMasterService.getVisualSettingMaster(visualMasterId),VisualSettingMasterDto.class);
    }
    private BrandDTO loadBrandDTOFromApi(Long brandId) {
        return brandApi.getBrandByBrandId(brandId).getCheckedData();
    }
    
    private ChannelDTO loadChannelDTOFromApi(Long channelId) {
        return channelApi.getByChannelId(channelId).getCheckedData();
    }

    private AreaDTO loadAreaDtoFromApi(Long areaId) {
        return areaApi.getAreaByAreaId(areaId).getCheckedData();
    }

    
    private VisualSettingDetailDto loadVisualSettingDetailBySupplierIdCacheService(String key){
        VisualSettingDetailDto visualSettingDetailDto = sysOpensourceService.getVisualSettingDetailByMerchantId(key);
        if (Objects.isNull(visualSettingDetailDto)) {
            return new VisualSettingDetailDto();
        }
        return visualSettingDetailDto;
    }

    private AppletBaseConfigDTO loadAppletBaseConfigDtoFromService(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = sysPartnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.APPLET_BASE_CONFIG.getType());
        return partnerConfig.getAppletBaseConfigDto();
    }

    private SupplierDTO loadBranchDtoFromService(Long supplierId) {
        return SupplierConvert.INSTANCE.convertDTO(SpringUtils.getBean(ISysSupplierService.class).getBySupplierId(supplierId));
    }

    private BranchDTO loadBranchDtoFromApi(Long branchId) {
        return branchApi.getByBranchId(branchId).getCheckedData();
    }

    private SmsConfigDTO loadSmsConfigFromApi(Long sysCode) {
        SysPartnerConfigRespVO partnerConfig = partnerConfigService.getPartnerConfig(sysCode, PartnerConfigEnum.SMS_CONFIG.getType());
        if (Objects.isNull(partnerConfig)) {
            return null;
        }
        return partnerConfig.getSmsConfigDTO();
    }

    private OpensourceDto loadOpensourceByMerchantIdFromApi(Long merchantId) {
        OpensourceDto opensourceDto = sysOpensourceService.getOpensourceByMerchantId(merchantId);
        if (Objects.isNull(opensourceDto)) {
            return new OpensourceDto();
        }
        return opensourceDto;
    }

    private SpuDTO loadSpuDtoFromApi(Long spuId){return spuApi.getBySpuId(spuId).getCheckedData();}

    private SkuDTO loadSkuDtoFromApi(Long skuId){return skuApi.getBySkuId(skuId).getCheckedData();}

    private ColonelDTO loadColonelDtoFromApi(Long colonelId){return colonelApi.getByColonelId(colonelId).getCheckedData();}

    private VisualSettingMasterDto loadVisualSettingMasterBySupplierIdFromService(Long supplierId){
        VisualSettingMasterDto visualSettingMasterDto = sysOpensourceService.getVisualSettingMasterByMerchantId(supplierId);
        if (Objects.isNull(visualSettingMasterDto)) {
            return new VisualSettingMasterDto();
        }
        return visualSettingMasterDto;
    }

    private VisualSettingTemplateDto loadVisualSettingTemplateFromService(Long visualTemplateId){
        return HutoolBeanUtils.toBean(visualSettingTemplateService.getVisualSettingTemplate(visualTemplateId),VisualSettingTemplateDto.class);
    }
    @Override
    public SmsConfigDTO getSmsConfig(Long sysCode) {
        return smsConfigDTOCache.get(sysCode);
    }

    @Override
    public OpensourceDto getOpensourceByMerchantId(Long merchantId) {
        return opensourceDtoByMerchantIdCache.get(merchantId);
    }

    @Override
    public SpuDTO getSpuDTO(Long spuId) {
        return spuDTOCache.get(spuId);
    }

    @Override
    public SkuDTO getSkuDTO(Long skuId) {
        return skuDTOCache.get(skuId);
    }

    @Override
    public BranchDTO getBranchDTO(Long branchId) {
        return branchDTOCache.get(branchId);
    }

    @Override
    public List<MessageTemplateDTO> getMessageTemplateDTO(Long sysCode, Integer scene) {
        return messageTemplateCache.get(StringUtils.format("{}:{}", sysCode, scene));
    }

    @Override
    public SupplierDTO getSupplierDTO(Long supplierId) {
        return supplierDTOCache.get(supplierId);
    }

    @Override
    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode) {
        return appletBaseConfigDTOCache.get(sysCode);
    }

    @Override
    public VisualSettingDetailDto getVisualDetailBySupplier(String key) {
        return visualSettingDetailBySupplierIdCache.get(key);
    }

    @Override
    public VisualSettingMasterDto getVisualMaster(Long visualMasterId) {
        return visualSettingMasterCache.get(visualMasterId);
    }

    @Override
    public ColonelDTO getColonel(Long colonelId) {
        if (Objects.isNull(colonelId)) {
            return null;
        }
        return colonelDTOCache.get(colonelId);
    }

    @Override
    public ChannelDTO getChannelDto(Long channelId) {
        return channelDTOCache.get(channelId);
    }

    @Override
    public VisualSettingMasterDto getVisualMasterBySupplierId(Long supplierId) {
        return visualSettingMasterBySupplierIdCache.get(supplierId);
    }

    @Override
    public VisualSettingTemplateDto getVisualTemplate(Long visualTemplateId) {
        return visualSettingTemplateCache.get(visualTemplateId);
    }

    @Override
    public MemberDTO getMember(Long merchantId) {
        return memberDTOCache.get(merchantId);
    }

    @Override
    public PayConfigDTO getPayConfigDTO(Long sysCode) {
        return payConfigDTOCache.get(sysCode);
    }

    @Override
    public AreaDTO getAreaDTO(Long areaId) {
        return areaDtoCache.get(areaId);
    }
    @Override
    public BrandDTO getBrandDTO(Long brandId) {
        if (Objects.isNull(brandId)) {
            return null;
        }
        return brandDtoCache.get(brandId);
    }
}
