package com.zksr.system.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysSmsTemplate;
import com.zksr.system.controller.sms.vo.SysSmsTemplatePageReqVO;


/**
 * 短信模版配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Mapper
public interface SysSmsTemplateMapper extends BaseMapperX<SysSmsTemplate> {
    default PageResult<SysSmsTemplate> selectPage(SysSmsTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysSmsTemplate>()
                    .eqIfPresent(SysSmsTemplate::getSmsTemplateId, reqVO.getSmsTemplateId())
                    .eqIfPresent(SysSmsTemplate::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(SysSmsTemplate::getScene, reqVO.getScene())
                    .eqIfPresent(SysSmsTemplate::getTemplateCode, reqVO.getTemplateCode())
                    .eqIfPresent(SysSmsTemplate::getContent, reqVO.getContent())
                    .likeIfPresent(SysSmsTemplate::getSignName, reqVO.getSignName())
                    .eqIfPresent(SysSmsTemplate::getHalfhourCount, reqVO.getHalfhourCount())
                .orderByDesc(SysSmsTemplate::getSmsTemplateId));
    }

    default SysSmsTemplate selectTemplate(Long sysCode, Integer scene, String platform) {
        return selectOne(new LambdaQueryWrapperX<SysSmsTemplate>()
                .eqIfPresent(SysSmsTemplate::getSysCode, sysCode)
                .eqIfPresent(SysSmsTemplate::getScene, scene)
                .eqIfPresent(SysSmsTemplate::getPlatform, platform)
                .eq(SysSmsTemplate::getStatus, NumberPool.INT_ZERO)
                .last(StringPool.LIMIT_ONE)
        );
    }

    default void disableSceneAndPlatform(SysSmsTemplate sysSmsTemplate) {
        SysSmsTemplate update = new SysSmsTemplate();
        update.setStatus(NumberPool.INT_ONE);
        this.update(update,
                Wrappers.lambdaUpdate(SysSmsTemplate.class)
                        .eq(SysSmsTemplate::getScene, sysSmsTemplate.getScene())
                        .eq(SysSmsTemplate::getPlatform, sysSmsTemplate.getPlatform())
                        .eq(SysSmsTemplate::getSysCode, sysSmsTemplate.getSysCode())
        );
    }

}
