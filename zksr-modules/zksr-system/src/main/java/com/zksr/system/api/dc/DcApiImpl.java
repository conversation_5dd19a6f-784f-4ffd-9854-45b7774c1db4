package com.zksr.system.api.dc;

import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.dc.dto.ColonelUserDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.service.ISysDcAreaService;
import com.zksr.system.service.ISysDcService;
import com.zksr.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@RestController
@ApiIgnore
public class DcApiImpl implements DcApi{

    @Autowired
    private ISysDcService dcService;

    @Autowired
    private ISysDcAreaService sysDcAreaService;

    @Autowired
    private ISysUserService userService;

    /**
     * @Description: 根据Id获取运营商信息
     * @Param: Long dcId
     * @return: CommonResult<DcDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 11:24
     */
    @Override
    @InnerAuth
    public CommonResult<DcDTO> getDcById(Long dcId) {
        return CommonResult.success(HutoolBeanUtils.toBean(dcService.getDcById(dcId), DcDTO.class));
    }

    @Override
    public CommonResult<List<DcDTO>> getDcBySysCode(Long sysCode) {
        return CommonResult.success(HutoolBeanUtils.toBean(dcService.getDcBySysCode(sysCode), DcDTO.class));
    }

    @Override
    public CommonResult<Long> insertColonelUser(@RequestBody ColonelUserDTO colonelDTO) {
        Long userId = null;
        try {
            userId = dcService.insertColonelUser(colonelDTO);
        } catch (Exception e) {
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR.getCode(), e.getMessage());
        }
        return CommonResult.success(userId);
    }
    @Override
    public CommonResult<Boolean> updateColonelUser(ColonelUserDTO colonelDTO) {
        dcService.updateColonelUser(colonelDTO);
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> updateColonelUserStatus(Long userId,String status) {
        dcService.updateColonelUserStatus(userId,status);
        return CommonResult.success(true);
    }



    @Override
    public CommonResult<List<Long>> getDcSupplierList(Long dcId) {
        return CommonResult.success(dcService.getDcSupplierList(dcId));
    }

    @Override
    public CommonResult<List<Long>> getDcAreaList(Long dcId) {
        DcAreaGroupDTO areaGroup = sysDcAreaService.getDcAreaGroup(dcId);
        return CommonResult.success(areaGroup.getAreaIds());
    }

    @Override
    public CommonResult<Boolean> checkColonelUser(String userName, String colonelPhone) {
        SysUser saveUser = new SysUser();
        saveUser.setUserName(userName);
        saveUser.setPhonenumber(colonelPhone);
        if (!userService.checkUserNameUnique(saveUser)) {
            return CommonResult.success(Boolean.TRUE);
        } else if (ToolUtil.isNotEmpty(saveUser.getPhonenumber())
                && !userService.checkPhoneUnique(saveUser)) {
            return CommonResult.success(Boolean.TRUE);
        }
        return CommonResult.success(Boolean.FALSE);
    }

    @Override
    @DataScope(dcAlias = "sd", dcFieldAlias = SystemConstants.DC_ID)
    public CommonResult<PageResult<SysDcRespVO>> getPage(SysDcPageReqVO pageReqVO) {
        return CommonResult.success(dcService.getSysAreaPageExt(pageReqVO));
    }
}
