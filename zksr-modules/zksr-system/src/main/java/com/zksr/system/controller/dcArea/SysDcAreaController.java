package com.zksr.system.controller.dcArea;

import javax.validation.Valid;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysDcArea;
import com.zksr.system.service.ISysDcAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.dcArea.vo.SysDcAreaPageReqVO;
import com.zksr.system.controller.dcArea.vo.SysDcAreaSaveReqVO;
import com.zksr.system.controller.dcArea.vo.SysDcAreaRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 运营商-区域城市关联关系Controller
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
@Api(tags = "管理后台 - 运营商-区域城市关联关系接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/dcArea")
public class SysDcAreaController {
    @Autowired
    private ISysDcAreaService sysDcAreaService;

    /**
     * 新增运营商-区域城市关联关系
     */
    @ApiOperation(value = "新增运营商-区域城市关联关系", httpMethod = "POST")
    @RequiresPermissions("system:dcArea:add")
    @Log(title = "运营商-区域城市关联关系", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysDcAreaSaveReqVO createReqVO) {
        Long aLong = sysDcAreaService.insertSysDcArea(createReqVO);
        sysDcAreaService.reloadCache(createReqVO.getDcId());
        return success(aLong);
    }

    /**
     * 修改运营商-区域城市关联关系
     */
    @ApiOperation(value = "修改运营商-区域城市关联关系", httpMethod = "PUT")
    @RequiresPermissions("system:dcArea:edit")
    @Log(title = "运营商-区域城市关联关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysDcAreaSaveReqVO updateReqVO) {
        sysDcAreaService.updateSysDcArea(updateReqVO);
        sysDcAreaService.reloadCache(updateReqVO.getDcId());
        return success(true);
    }

    /**
     * 删除运营商-区域城市关联关系
     */
    @ApiOperation(value = "删除运营商-区域城市关联关系", httpMethod = "GET")
    @RequiresPermissions("system:dcArea:remove")
    @Log(title = "运营商-区域城市关联关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dcIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] dcIds) {
        sysDcAreaService.deleteSysDcAreaByDcIds(dcIds);
        return success(true);
    }

    /**
     * 获取运营商-区域城市关联关系详细信息
     */
    @ApiOperation(value = "获得运营商-区域城市关联关系详情", httpMethod = "GET")
    @RequiresPermissions("system:dcArea:query")
    @GetMapping(value = "/{dcId}")
    public CommonResult<SysDcAreaRespVO> getInfo(@PathVariable("dcId") Long dcId) {
        SysDcArea sysDcArea = sysDcAreaService.getSysDcArea(dcId);
        return success(HutoolBeanUtils.toBean(sysDcArea, SysDcAreaRespVO.class));
    }

    /**
     * 分页查询运营商-区域城市关联关系
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得运营商-区域城市关联关系分页列表", httpMethod = "GET")
    @RequiresPermissions("system:dcArea:list")
    public CommonResult<PageResult<SysDcAreaRespVO>> getPage(@Valid SysDcAreaPageReqVO pageReqVO) {
        PageResult<SysDcArea> pageResult = sysDcAreaService.getSysDcAreaPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, SysDcAreaRespVO.class));
    }

    /**
     * 根据运营商获取运营商所拥有的城市组
     * @param dcId
     * @return
     */
    @GetMapping("/getDcAreaGroup")
    @ApiOperation(value = "获得运营商-区域城市关联关系分组", httpMethod = "GET")
    public CommonResult<DcAreaGroupDTO> getDcAreaGroup(@ApiParam(value = "dcId", name = "运营商ID") Long dcId) {
        DcAreaGroupDTO getDcAreaGroup = sysDcAreaService.getDcAreaGroup(dcId);
        return CommonResult.success(getDcAreaGroup);
    }
}
