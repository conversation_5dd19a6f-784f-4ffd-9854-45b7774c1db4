package com.zksr.system.controller.openlimit;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysOpenlimit;
import com.zksr.system.service.ISysOpenlimitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.openlimit.vo.SysOpenlimitPageReqVO;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitSaveReqVO;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitRespVO;
import com.zksr.system.convert.openlimit.SysOpenlimitConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 开发能力qpsController
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Api(tags = "管理后台 - 开发能力qps接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/openlimit")
public class SysOpenlimitController {
    @Autowired
    private ISysOpenlimitService sysOpenlimitService;

    /**
     * 新增开发能力qps
     */
    @ApiOperation(value = "新增开发能力qps", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "开发能力qps", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysOpenlimitSaveReqVO createReqVO) {
        return success(sysOpenlimitService.insertSysOpenlimit(createReqVO));
    }

    /**
     * 修改开发能力qps
     */
    @ApiOperation(value = "修改开发能力qps", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "开发能力qps", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysOpenlimitSaveReqVO updateReqVO) {
            sysOpenlimitService.updateSysOpenlimit(updateReqVO);
        return success(true);
    }

    /**
     * 删除开发能力qps
     */
    @ApiOperation(value = "删除开发能力qps", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "开发能力qps", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        sysOpenlimitService.deleteSysOpenlimitBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取开发能力qps详细信息
     */
    @ApiOperation(value = "获得开发能力qps详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<SysOpenlimitRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        SysOpenlimit sysOpenlimit = sysOpenlimitService.getSysOpenlimit(sysCode);
        return success(SysOpenlimitConvert.INSTANCE.convert(sysOpenlimit));
    }

    /**
     * 分页查询开发能力qps
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得开发能力qps分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysOpenlimitRespVO>> getPage(@Valid SysOpenlimitPageReqVO pageReqVO) {
        PageResult<SysOpenlimit> pageResult = sysOpenlimitService.getSysOpenlimitPage(pageReqVO);
        return success(SysOpenlimitConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:openlimit:add";
        /** 编辑 */
        public static final String EDIT = "system:openlimit:edit";
        /** 删除 */
        public static final String DELETE = "system:openlimit:remove";
        /** 列表 */
        public static final String LIST = "system:openlimit:list";
        /** 查询 */
        public static final String GET = "system:openlimit:query";
        /** 停用 */
        public static final String DISABLE = "system:openlimit:disable";
        /** 启用 */
        public static final String ENABLE = "system:openlimit:enable";
    }
}
