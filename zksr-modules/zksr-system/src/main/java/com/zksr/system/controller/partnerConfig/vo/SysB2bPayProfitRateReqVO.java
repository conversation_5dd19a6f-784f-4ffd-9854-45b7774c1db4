package com.zksr.system.controller.partnerConfig.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 报名b2b支付手续费活动
 * @date 2024/8/13 14:53
 */
@ApiModel(description = "报名b2b支付手续费活动")
@Data
public class SysB2bPayProfitRateReqVO {
    @ApiModelProperty("appid")
    private String appid;

    @ApiModelProperty("微信支付商户号")
    private String sub_mchid;

    @ApiModelProperty("费率, 万分比，比如 40 指的是 0.40%, 不得低于22, 即0.22%")
    private String profit_rate;
}
