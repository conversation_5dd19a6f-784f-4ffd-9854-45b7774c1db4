package com.zksr.system.api.partnerPolicy;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.api.partnerPolicy.enums.PartnerPolicyEnum;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyRespVO;
import com.zksr.system.service.ISysPartnerPolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@ApiIgnore
public class partnerPolicyImpl implements PartnerPolicyApi {

    @Autowired
    private ISysPartnerPolicyService sysPartnerPolicyService;

    /**
     * @Description: 获取小程序配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:42
     */
    @Override
    @InnerAuth
    public CommonResult<AppletAgreementPolicyDTO> getAppletAgreementPolicy(Long sysCode) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(sysCode, null, null, PartnerPolicyEnum.APPLET_AGREEMENT_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getAppletAgreementPolicyDto());
    }

    /**
     * @Description: 获取基础设置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:42
     */
    @Override
    @InnerAuth
    public CommonResult<BasicSettingPolicyDTO> getBasicSettingPolicy(Long dcId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, dcId, null, PartnerPolicyEnum.BASIC_SETTING_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getBasicSettingPolicyDto());
    }

    /**
     * @Description: 获取订单参数配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:43
     */
    @Override
    public CommonResult<OrderSettingPolicyDTO> getOrderSettingPolicy(Long dcId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, dcId, null, PartnerPolicyEnum.ORDER_SETTING_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getOrderSettingPolicyDto());
    }

    /**
     * @Description: 获取提现配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:43
     */
    @Override
    @InnerAuth
    public CommonResult<WithdrawalSettingPolicyDTO> getWithdrawalSettingPolicy(Long sysCode) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(sysCode, null, null, PartnerPolicyEnum.WITHDRAWAL_SETTING_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getWithdrawalSettingPolicyDTO());
    }

    /**
    * @Description: 获取芯烨配置信息
    * @Author: liuxingyu
    * @Date: 2024/4/22 8:46
    */
    @Override
    @InnerAuth
    public CommonResult<XpYunSettingPolicyDTO> getXpYunSettingPolicy(Long supplierId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, null, supplierId, PartnerPolicyEnum.XPYUN_SETTING_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getXpYunSettingPolicyDTO());
    }

    /**
    * @Description: 获取入驻商售后配置信息
    * @Author: liuxingyu
    * @Date: 2024/4/24 15:52
    */
    @Override
    @InnerAuth
    public CommonResult<AfterSaleSettingPolicyDTO> getAfterSaleSettingPolicy(Long supplierId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, null, supplierId, PartnerPolicyEnum.AFTER_SALE_SETTING.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getAfterSaleSettingPolicyDTO());
    }

    /**
    * @Description: 获取飞鹅配置信息
    * @Author: liuxingyu
    * @Date: 2024/4/25 11:32
    */
    @Override
    @InnerAuth
    public CommonResult<FeieYunSettingPolicyDTO> getFeieYunSettingPolicy(Long supplierId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, null, supplierId, PartnerPolicyEnum.FEIEYUN_SETTING_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getFeieYunSettingPolicyDTO());
    }

    /**
    * @Description: 获取运营商业务员配置
    * @Author: liuxingyu
    * @Date: 2024/4/25 17:26
    */
    @Override
    @InnerAuth
    public CommonResult<ColonelSettingPolicyDTO> getColonelSettingPolicy(Long dcId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, dcId, null, PartnerPolicyEnum.COLONEL_SETTING_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getColonelSettingPolicyDTO());
    }

    /**
     * @Description: 获取入驻商对外系统配置
     * @Author: liuxingyu
     * @Date: 2024/5/28 17:26
     */
    @Override
    @InnerAuth
    public CommonResult<OpenSettingPolicyDTO> getOpenSettingPolicy(Long supplierId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, null, supplierId, PartnerPolicyEnum.OPEN_SETTING_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getOpenSettingPolicyDTO());
    }

    @Override
    public CommonResult<PartnerMiniSettingPolicyDTO> getPartnerMiniSettingPolicy(Long sysCode) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(sysCode, null, null, PartnerPolicyEnum.APPLET_AGREEMENT_POLICY.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getPartnerMiniSettingPolicyDTO());
    }

    @Override
    public CommonResult<SupplierOtherSettingPolicyDTO> getPartnerSupplierOtherSettingPolicy(Long supplierId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, null, supplierId, PartnerPolicyEnum.SUPPLIER_OTHER_SETTING.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getSupplierOtherSettingPolicyDTO());
    }

    @Override
    public CommonResult<SearchConfigDTO> getSearchConfig(Long areaId) {
        return CommonResult.success(sysPartnerPolicyService.getSearchConfig(areaId));
    }

    @Override
    public CommonResult<BranchLifecycleSettingPolicyDTO> getBranchLifecycleSettingPolicy(Long dcId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, dcId, null, PartnerPolicyEnum.BRANCH_LIFECYCLE_SETTING.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getBranchLifecycleSettingPolicyDTO());
    }

    @Override
    public CommonResult<DcOtherSettingPolicyDTO> getDcOtherSettingDTO(Long dcId) {
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO =
                sysPartnerPolicyService.getSysPartnerPolicy(null, dcId, null, PartnerPolicyEnum.DC_OTHER_SETTING.getType());
        return CommonResult.success(sysPartnerPolicyRespVO.getDcOtherSettingPolicyDTO());
    }
}
