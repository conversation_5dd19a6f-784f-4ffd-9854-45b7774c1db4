package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

/**
 * 区域城市入驻商关系拉链表 sys_area_supplier_zip
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@TableName(value = "sys_area_supplier_zip")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysAreaSupplierZip extends BaseEntity{
    private static final long serialVersionUID=1L;
    @TableId(type = IdType.ASSIGN_ID)
    private Long areaSupplierZipId;

    @Excel(name = "平台商ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode; // 平台商ID

    @Excel(name = "区域城市ID")
    private Long areaId; // 区域城市ID

    @Excel(name = "入驻商ID")
    private Long supplierId; // 入驻商ID

    @Excel(name = "开始日期")
    private Date startDate;

    @Excel(name = "结束日期")
    private Date endDate;

}
