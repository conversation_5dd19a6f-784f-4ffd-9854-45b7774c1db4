package com.zksr.system.convert.bank;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysBankChannel;
import com.zksr.system.api.bank.vo.SysBankChannelRespVO;
import com.zksr.system.controller.bank.vo.SysBankChannelSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 联行号信息 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-07-18
*/
@Mapper
public interface SysBankChannelConvert {

    SysBankChannelConvert INSTANCE = Mappers.getMapper(SysBankChannelConvert.class);

    SysBankChannelRespVO convert(SysBankChannel sysBankChannel);

    SysBankChannel convert(SysBankChannelSaveReqVO sysBankChannelSaveReq);

    PageResult<SysBankChannelRespVO> convertPage(PageResult<SysBankChannel> sysBankChannelPage);
}