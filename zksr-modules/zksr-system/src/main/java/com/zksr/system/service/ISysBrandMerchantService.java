package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysBrandMerchant;
import com.zksr.system.controller.brand.vo.SysBrandMerchantPageReqVO;
import com.zksr.system.controller.brand.vo.SysBrandMerchantSaveReqVO;

/**
 * 品牌商资料Service接口
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
public interface ISysBrandMerchantService {

    /**
     * 新增品牌商资料
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysBrandMerchant(@Valid SysBrandMerchantSaveReqVO createReqVO);

    /**
     * 修改品牌商资料
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysBrandMerchant(@Valid SysBrandMerchantSaveReqVO updateReqVO);

    /**
     * 删除品牌商资料
     *
     * @param brandMerchantId ${pkColumn.columnComment}
     */
    public void deleteSysBrandMerchant(Long brandMerchantId);

    /**
     * 批量删除品牌商资料
     *
     * @param brandMerchantIds 需要删除的品牌商资料主键集合
     * @return 结果
     */
    public void deleteSysBrandMerchantByBrandMerchantIds(Long[] brandMerchantIds);

    /**
     * 获得品牌商资料
     *
     * @param brandMerchantId ${pkColumn.columnComment}
     * @return 品牌商资料
     */
    public SysBrandMerchant getSysBrandMerchant(Long brandMerchantId);

    /**
     * 获得品牌商资料分页
     *
     * @param pageReqVO 分页查询
     * @return 品牌商资料分页
     */
    PageResult<SysBrandMerchant> getSysBrandMerchantPage(SysBrandMerchantPageReqVO pageReqVO);

    /**
     * 通过userid 获取品牌商信息
     * @param sysUserId
     * @return
     */
    SysBrandMerchant getSysBrandMerchantByUserId(Long sysUserId);

    /**
     * 停用品牌商
     * @param brandMerchantId   brandMerchantId
     */
    void disable(Long brandMerchantId);

    /**
     * 启用品牌商
     * @param brandMerchantId   brandMerchantId
     */
    void enable(Long brandMerchantId);
}
