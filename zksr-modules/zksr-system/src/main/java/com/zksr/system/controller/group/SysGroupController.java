package com.zksr.system.controller.group;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.group.vo.SysGroupPageReqVO;
import com.zksr.system.controller.group.vo.SysGroupRespVO;
import com.zksr.system.controller.group.vo.SysGroupSaveReqVO;
import com.zksr.system.domain.SysGroup;
import com.zksr.system.service.ISysGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商城市分组Controller
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Api(tags = "管理后台 - 平台商城市分组接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/group")
public class SysGroupController {
    @Autowired
    private ISysGroupService sysGroupService;

    /**
    * @Description: 获取所有分组信息
    * @Param: CommonResult<List<SysGroupRespVO>>
    * @return:
    * @Author: liuxingyu
    * @Date: 2024/3/18 16:29
    */
    @ApiOperation(value = "获取所有分组信息",httpMethod = HttpMethod.GET)
    @GetMapping("/getGroupList")
    public CommonResult<List<SysGroupRespVO>> getGroupList(){
        return success(HutoolBeanUtils.toBean(sysGroupService.getGroupList(),SysGroupRespVO.class));
    }

    /**
     * 新增平台商城市分组
     */
    @ApiOperation(value = "新增平台商城市分组", httpMethod = "POST", notes = "system:group:add")
    @RequiresPermissions("system:group:add")
    @Log(title = "平台商城市分组", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysGroupSaveReqVO createReqVO) {
        return success(sysGroupService.insertSysGroup(createReqVO));
    }

    /**
     * 修改平台商城市分组
     */
    @ApiOperation(value = "修改平台商城市分组", httpMethod = "PUT", notes = "system:group:edit")
    @RequiresPermissions("system:group:edit")
    @Log(title = "平台商城市分组", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysGroupSaveReqVO updateReqVO) {
        sysGroupService.updateSysGroup(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台商城市分组
     */
    @ApiOperation(value = "删除平台商城市分组", httpMethod = "GET", notes = "system:group:remove")
    @RequiresPermissions("system:group:remove")
    @Log(title = "平台商城市分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{groupIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] groupIds) {
        sysGroupService.deleteSysGroupByGroupIds(groupIds);
        return success(true);
    }

    /**
     * 获取平台商城市分组详细信息
     */
    @ApiOperation(value = "获得平台商城市分组详情", httpMethod = "GET", notes = "system:group:query")
    @RequiresPermissions("system:group:query")
    @GetMapping(value = "/{groupId}")
    public CommonResult<SysGroupRespVO> getInfo(@PathVariable("groupId") Long groupId) {
        SysGroup sysGroup = sysGroupService.getSysGroup(groupId);
        return success(HutoolBeanUtils.toBean(sysGroup, SysGroupRespVO.class));
    }

    /**
     * 分页查询平台商城市分组
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商城市分组分页列表", httpMethod = "GET", notes = "system:group:list")
    @RequiresPermissions("system:group:list")
    public CommonResult<PageResult<SysGroupRespVO>> getPage(@Valid SysGroupPageReqVO pageReqVO) {
        PageResult<SysGroup> pageResult = sysGroupService.getSysGroupPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, SysGroupRespVO.class));
    }

    /**
     * 分页查询平台商城市分组（客户通APP）
     */
    @GetMapping("/app-list")
    public CommonResult<PageResult<SysGroupRespVO>> getAppGroupList(@ModelAttribute SysGroupPageReqVO pageReqVO) {
        PageResult<SysGroup> pageResult = sysGroupService.getSysGroupPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, SysGroupRespVO.class));
    }

}
