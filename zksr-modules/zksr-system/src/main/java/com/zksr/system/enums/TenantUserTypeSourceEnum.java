package com.zksr.system.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/03/26 17:04
 */
public enum TenantUserTypeSourceEnum {
    SAAS_PLATFORM(1, "saas租户创建"),

    OPERATE_PLATFORM(2, "运营平台创建"),

    PRODUCE_LOGISTICS(3, "生产物流扫码注册"),

    INVITE_REGISTER(4, "邀请码注册"),

    OFFICIAL_WEBSITE_REGISTER(5, "官网注册"),

    CSP_WEB_REGISTER(6, "web平台注册"),

    ERP_REGISTER(7,"ERP注册"),
    B2B_REGISTER(8,"B2B注册"),
    ;


    TenantUserTypeSourceEnum(Integer key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    private Integer key;
    private String desc;

    public Integer getKey() {
        return key;
    }


    public String getDesc() {
        return desc;
    }


    public static String getDesc(Integer key) {
        if (key == null) {
            return null;
        }
        TenantUserTypeSourceEnum[] values = values();
        for (TenantUserTypeSourceEnum value : values) {
            if (Objects.equals(value.getKey(), key)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
