package com.zksr.system.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.RemoteFileService;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigCopyReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigPageReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigRespVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigSaveReqVO;
import com.zksr.system.convert.page.PageConfigConvert;
import com.zksr.system.domain.SysArea;
import com.zksr.system.domain.SysChannel;
import com.zksr.system.domain.SysPagesConfig;
import com.zksr.system.mapper.SysChannelMapper;
import com.zksr.system.mapper.SysPagesConfigMapper;
import com.zksr.system.service.ISysAreaService;
import com.zksr.system.service.ISysPagesConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;


/**
 * 平台页面配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Service
@SuppressWarnings("all")
public class SysPagesConfigServiceImpl implements ISysPagesConfigService {

    @Autowired
    private ISysAreaService areaService;

    @Autowired
    private SysPagesConfigMapper sysPagesConfigMapper;

    @Autowired
    private Cache<String, List<PagesConfigDTO>> pagesConfigCache;

    @Autowired
    private SysChannelMapper channelMapper;

    /**
     * 新增平台页面配置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertSysPagesConfig(SysPagesConfigSaveReqVO createReqVO) {
        // 插入
        SysPagesConfig sysPagesConfig = HutoolBeanUtils.toBean(createReqVO, SysPagesConfig.class);
        sysPagesConfig.setCreateTime(DateUtil.date());
        sysPagesConfig.setUpdateTime(DateUtil.date());
        sysPagesConfig.setCreateBy(SecurityUtils.getUsername());
        sysPagesConfig.setUpdateBy(SecurityUtils.getUsername());
        sysPagesConfig.setDcId(SecurityUtils.getLoginUser().getDcId());
        sysPagesConfig.setDelFlag(DelFlagConstants.NORMAL);
        sysPagesConfig.setStatus(StringPool.ZERO);
        sysPagesConfig.setDefFlag(StringPool.ZERO);
        sysPagesConfigMapper.insert(sysPagesConfig);
        // 检查是否有其他渠道是启用的
        // 是否重复的就停用
        Long cnt = sysPagesConfigMapper.selectEnableByAreaIdAndChannelId(sysPagesConfig.getPageId(), sysPagesConfig.getAreaId(), sysPagesConfig.getChannelId());
        if (cnt > 0 && Objects.nonNull(sysPagesConfig.getLevel()) && sysPagesConfig.getLevel() == 1) {
            sysPagesConfig.setStatus(StringPool.ONE);
            sysPagesConfigMapper.updateById(sysPagesConfig);
        } else {
            sysPagesConfig.setEnableTime(new Date());
            sysPagesConfigMapper.updateById(sysPagesConfig);
        }

        // 更新父节点标记
        this.updatePid(createReqVO.getPid());

        // 返回
        return sysPagesConfig.getPageId();
    }

    /**
     * 修改平台页面配置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysPagesConfig(SysPagesConfigSaveReqVO updateReqVO) {
        SysPagesConfig sysPagesConfig = HutoolBeanUtils.toBean(updateReqVO, SysPagesConfig.class);
        sysPagesConfig.setUpdateTime(DateUtil.date());
        sysPagesConfig.setUpdateBy(SecurityUtils.getUsername());
        sysPagesConfigMapper.updateById(sysPagesConfig);
        // 验证时间冲突
        SysPagesConfig sourceConfig = sysPagesConfigMapper.selectById(updateReqVO.getPageId());
        if (sourceConfig.getStatus().equals(StringPool.ZERO)) {
            // 检查是否有其他渠道是启用的
            Long cnt = sysPagesConfigMapper.selectEnableByAreaIdAndChannelId(sourceConfig.getPageId(), sourceConfig.getAreaId(), sourceConfig.getChannelId());
            if (cnt > 0) {
                throw new ServiceException("请检查渠道是否已经有启用或者时间冲突的首页配置");
            }
        }
    }

    /**
     * 删除平台页面配置
     *
     * @param pageId 自定义页面ID
     */
    @Override
    public void deleteSysPagesConfig(Long pageId) {
        SysPagesConfig pagesConfig = sysPagesConfigMapper.selectById(pageId);
        if (StringPool.ONE.equals(pagesConfig.getDefFlag())) {
            throw exception(PAGES_DEFAULT_CAN_NOT_DEL);
        }
        // 删除
        pagesConfig.setDelFlag(DelFlagConstants.DISABLE);
        pagesConfig.setUpdateTime(DateUtil.date());
        pagesConfig.setUpdateBy(SecurityUtils.getUsername());
        sysPagesConfigMapper.updateById(pagesConfig);
        // 更新父页面标记状态
        this.updatePid(pagesConfig.getPid());
    }

    /**
     * 批量删除平台页面配置
     *
     * @param pageIds 需要删除的平台页面配置主键
     * @return 结果
     */
    @Override
    public void deleteSysPagesConfigByPageIds(Long[] pageIds) {
        for(Long pageId : pageIds){
            this.deleteSysPagesConfig(pageId);
            reloadCache(pageId);
        }
    }

    /**
     * 获得平台页面配置
     *
     * @param pageId 自定义页面ID
     * @return 平台页面配置
     */
    @Override
    public SysPagesConfig getSysPagesConfig(Long pageId) {
        return sysPagesConfigMapper.selectById(pageId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysPagesConfig> getSysPagesConfigPage(SysPagesConfigPageReqVO pageReqVO) {
        return sysPagesConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<SysPagesConfigRespVO> getSysPagesConfigPageExt(SysPagesConfigPageReqVO pageReqVO) {
        // 转换返回
        Page<SysPagesConfigRespVO> page = PageUtils.startPage(pageReqVO);
        List<SysPagesConfigRespVO> respVOS = sysPagesConfigMapper.selectPageExt(pageReqVO);
        for (SysPagesConfigRespVO respVO : respVOS) {
            if (StringUtils.isNotEmpty(respVO.getChannelId())) {
                List<String> list = Arrays.stream(respVO.getChannelId().split(StringPool.COMMA))
                        .map(Long::parseLong)
                        .map(channelMapper::selectById)
                        .filter(Objects::nonNull)
                        .map(SysChannel::getChannelName)
                        .collect(Collectors.toList());
                respVO.setChannelName(StringUtils.join(list, StringPool.COMMA));
            }
            // 过期了显示停用
            if (Objects.nonNull(respVO.getEndTime()) && respVO.getEndTime().getTime() < System.currentTimeMillis()) {
                respVO.setStatus(StringPool.ONE);
            }
        }
        return new PageResult<>(respVOS, page.getTotal());
    }

    /**
     * 更新状态
     * @param updateReqVO
     */
    @Override
    @Transactional
    public void updateStatus(SysPagesConfigSaveReqVO updateReqVO) {
        // 如果是启用, 则需要停用其他的
        SysPagesConfig sourceConfig = sysPagesConfigMapper.selectById(updateReqVO.getPageId());
        SysPagesConfig sysPagesConfig = new SysPagesConfig();
        sysPagesConfig.setPageId(updateReqVO.getPageId());
        sysPagesConfig.setStatus(updateReqVO.getStatus());
        // 只有一级页面才需要去验证时间
        if (updateReqVO.getStatus().equals(StringPool.ZERO) && sourceConfig.getLevel() == 1) {
            // 检查是否有其他渠道是启用的
            Long cnt = sysPagesConfigMapper.selectEnableByAreaIdAndChannelId(sourceConfig.getPageId(), sourceConfig.getAreaId(), sourceConfig.getChannelId());
            if (cnt > 0) {
                throw exception(PAGES_DEFAULT_REPEAT);
            }
            if (sourceConfig.getEndTime().getTime() < System.currentTimeMillis()) {
                throw exception(PAGES_PRE_TIME_EXPIRE);
            }
            sysPagesConfig.setEnableTime(DateUtil.date());
        }
        sysPagesConfigMapper.updateById(sysPagesConfig);
    }

    @Override
    @Transactional
    public void updateDefFlag(SysPagesConfigSaveReqVO updateReqVO) {
        SysPagesConfig pagesConfig = sysPagesConfigMapper.selectById(updateReqVO.getPageId());
        if (StringPool.ONE.equals(updateReqVO.getDefFlag()) && pagesConfig.getLevel() != 1) {
            throw exception(PAGES_SECOND_NOT_DEFAULT);
        }
        if (pagesConfig.getType() != 0) {
            throw exception(PAGES_PRE_TEMPLATE_NOT_DEFAULT);
        }
        // 把当前城市 + 渠道的首页配置全部置成非默认
        if (StringPool.ONE.equals(updateReqVO.getDefFlag())) {
            SysPagesConfig update = new SysPagesConfig();
            update.setDefFlag(StringPool.ZERO);
            Wrapper<SysPagesConfig> uw = Wrappers.lambdaQuery(SysPagesConfig.class).eq(SysPagesConfig::getSysCode, pagesConfig.getSysCode());
            sysPagesConfigMapper.update(update, uw);
            // 设置成启用
            updateReqVO.setStatus(com.zksr.common.core.pool.StringPool.ZERO);
            this.updateStatus(updateReqVO);
        }
        // 当前记录修改成默认
        SysPagesConfig update = SysPagesConfig.builder()
                        .pageId(pagesConfig.getPageId())
                        .defFlag(updateReqVO.getDefFlag())
                        .build();
        sysPagesConfigMapper.updateById(update);
    }

    @Override
    public List<PagesConfigDTO> getByAreaChannel(Long sysCode, Long areaId, Long channelId) {
        Set<SysPagesConfig> pagesConfigSet = new LinkedHashSet<>();
        // 城市 + 渠道优先第一
        if (Objects.nonNull(channelId)) {
            List<SysPagesConfig> pagesConfigs = sysPagesConfigMapper.selectByEnableAreaIdAndChannelId(areaId, channelId);
            // 预设模版在前,然后最近时间优先
            pagesConfigs.sort(Comparator.comparing(SysPagesConfig::getType).reversed().thenComparing(SysPagesConfig::getStartTime));
            pagesConfigSet.addAll(pagesConfigs);
        }
        // 仅城市第二
        {
            List<SysPagesConfig> pagesConfigs = sysPagesConfigMapper.selectByEnableAreaIdAndChannelId(areaId, null);
            // 预设模版在前,然后最近时间优先
            pagesConfigs.sort(Comparator.comparing(SysPagesConfig::getType).reversed().thenComparing(SysPagesConfig::getStartTime));
            pagesConfigSet.addAll(pagesConfigs);
        }
        // 获取平台默认第三
        {
            pagesConfigSet.add(sysPagesConfigMapper.selectDefaultBySysCode(sysCode));
        }
        // 转换数据结构
        return pagesConfigSet.stream().filter(Objects::nonNull).map(pagesConfig -> {
            PagesConfigDTO configDTO = PageConfigConvert.INSTANCE.convert(pagesConfig);
            // 查询子页面
            List<PagesConfigDTO> childList = PageConfigConvert.INSTANCE.convertDTOList(sysPagesConfigMapper.selectByPid(pagesConfig.getPageId()));
            configDTO.setChildList(childList);
            return configDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public void reloadCache(Long pageId) {
        SysPagesConfig pagesConfig = sysPagesConfigMapper.selectById(pageId);
        pagesConfigCache.remove(RedisConstants.getPageConfigKey(pagesConfig.getSysCode(), NumberPool.LOWER_GROUND_LONG, NumberPool.LOWER_GROUND_LONG));
        pagesConfigCache.remove(RedisConstants.getPageConfigKey(pagesConfig.getSysCode(), NumberPool.LOWER_GROUND_LONG, pagesConfig.getAreaId()));
        // 刷新全部渠道
        List<SysChannel> sysChannels = channelMapper.selectList();
        sysChannels.stream().map(SysChannel::getChannelId).forEach(channelId -> {
            pagesConfigCache.remove(RedisConstants.getPageConfigKey(pagesConfig.getSysCode(), channelId, pagesConfig.getAreaId()));
        });
        // 如果是默认模版
        // 需要删除所有的城市 + 渠道数据
        if (StringPool.ONE.equals(pagesConfig.getDefFlag())) {
            List<SysArea> sysAreaList = areaService.getAreaListBySysCode(SecurityUtils.getLoginUser().getSysCode());
            sysAreaList.forEach(area -> {
                pagesConfigCache.remove(RedisConstants.getPageConfigKey(pagesConfig.getSysCode(), NumberPool.LOWER_GROUND_LONG, area.getAreaId()));
                sysChannels.stream().map(SysChannel::getChannelId).forEach(channelId -> {
                    pagesConfigCache.remove(RedisConstants.getPageConfigKey(pagesConfig.getSysCode(), channelId, area.getAreaId()));
                });
            });
        }
    }

    @Override
    @Transactional
    public void copy(SysPagesConfigCopyReqVO configCopyReqVO) {

        // 查询被复制的模版
        SysPagesConfig pagesConfig = sysPagesConfigMapper.selectById(configCopyReqVO.getPageId());
        pagesConfig.setPageId(null);
        pagesConfig.setPageName(configCopyReqVO.getPageName());
        pagesConfig.setAreaId(configCopyReqVO.getAreaId());
        pagesConfig.setChannelId(configCopyReqVO.getChannelId());
        pagesConfig.setStartTime(configCopyReqVO.getStartTime());
        pagesConfig.setEndTime(configCopyReqVO.getEndTime());
        pagesConfig.setCreateTime(new Date());
        pagesConfig.setUpdateTime(new Date());
        pagesConfig.setEnableTime(null);
        pagesConfig.setStatus(StringPool.ONE);
        pagesConfig.setDefFlag(StringPool.ZERO);
        /**
         * 前端要求, 在pageConfig, urlDtl 对象中增加一个copyPageId参数, 使用被复制的模版ID
         */
        if (StringUtils.isNotEmpty(pagesConfig.getPageConfig())) {
            JSONObject config = JSON.parseObject(pagesConfig.getPageConfig());
            config.put("copyPageId", configCopyReqVO.getPageId());
            pagesConfig.setPageConfig(config.toJSONString());
        }
        if (StringUtils.isNotEmpty(pagesConfig.getUrlDtl())) {
            JSONObject config = JSON.parseObject(pagesConfig.getUrlDtl());
            config.put("copyPageId", configCopyReqVO.getPageId());
            pagesConfig.setUrlDtl(config.toJSONString());
        }
        sysPagesConfigMapper.insert(pagesConfig);

        // 复制子页面
        List<SysPagesConfig> pagesConfigs = sysPagesConfigMapper.selectByPid(configCopyReqVO.getPageId());
        for (SysPagesConfig childConfig : pagesConfigs) {
            Long pageId = childConfig.getPageId();
            childConfig.setPageId(null);
            childConfig.setPid(pagesConfig.getPageId());
            childConfig.setAreaId(configCopyReqVO.getAreaId());
            childConfig.setChannelId(configCopyReqVO.getChannelId());
            childConfig.setCreateTime(new Date());
            childConfig.setUpdateTime(new Date());
            childConfig.setEnableTime(null);
            childConfig.setStatus(StringPool.ONE);
            childConfig.setDefFlag(StringPool.ZERO);
            if (StringUtils.isNotEmpty(childConfig.getPageConfig())) {
                JSONObject config = JSON.parseObject(childConfig.getPageConfig());
                config.put("copyPageId", pageId);
                childConfig.setPageConfig(config.toJSONString());
            }
            if (StringUtils.isNotEmpty(childConfig.getUrlDtl())) {
                JSONObject config = JSON.parseObject(childConfig.getUrlDtl());
                config.put("copyPageId", pageId);
                childConfig.setUrlDtl(config.toJSONString());
            }
            sysPagesConfigMapper.insert(childConfig);
        }
    }

    /**
     * 更新父页面标记数据
     * @param pid   父页面ID
     */
    private void updatePid(Long pid) {
        if (Objects.isNull(pid)) {
            return;
        }
        // 查询子页面
        List<SysPagesConfig> pagesConfigs = sysPagesConfigMapper.selectByPid(pid);

        // 更新检查有没有子节点
        SysPagesConfig pagesConfig = new SysPagesConfig();
        pagesConfig.setPageId(pid);
        pagesConfig.setHasChild(pagesConfigs.isEmpty() ? 0 : 1);
        sysPagesConfigMapper.updateById(pagesConfig);
    }
    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private SysPagesConfigServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
