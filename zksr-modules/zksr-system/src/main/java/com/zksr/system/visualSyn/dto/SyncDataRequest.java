package com.zksr.system.visualSyn.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import lombok.*;
import lombok.experimental.Accessors;

/**
* 同步数据实体
* @date 2024/7/18 15:32
* <AUTHOR>
*/
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncDataRequest {

    /** 请求头 */
    private String header;

    /** 请求体 */
    private String body;

    /** 验签 加密后的数据*/
    private String verify;

    /** 响应结果 */
    private String response;
}
