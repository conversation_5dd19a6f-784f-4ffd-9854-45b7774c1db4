package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.mapper.SysPartnerDictDataMapper;
import com.zksr.system.service.ISysPartnerDictDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 字典 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysPartnerDictDataServiceImpl implements ISysPartnerDictDataService
{

    @Resource
    private SysPartnerDictDataMapper sysPartnerDictDataMapper;

    public List<SysPartnerDictData> getList(SysPartnerDictData sysPartnerDictData){
        LambdaQueryWrapper<SysPartnerDictData> queryWrapper = new LambdaQueryWrapper<>();
        Long sysCode = SecurityContextHolder.getSysCode();
        queryWrapper.eq(SysPartnerDictData::getSysCode,sysCode);
        queryWrapper.like(StringUtils.isNotEmpty(sysPartnerDictData.getDictLabel()),SysPartnerDictData::getDictLabel,sysPartnerDictData.getDictLabel());
        queryWrapper.eq(StringUtils.isNotEmpty(sysPartnerDictData.getStatus()),SysPartnerDictData::getStatus,sysPartnerDictData.getStatus());
        queryWrapper.eq(StringUtils.isNotEmpty(sysPartnerDictData.getDictType()),SysPartnerDictData::getDictType,sysPartnerDictData.getDictType());

        return sysPartnerDictDataMapper.selectList(queryWrapper);

    }

    /**
     * 根据字典id查询
     * @param dictCode
     * @return
     */
    public SysPartnerDictData selectDictDataById(Long dictCode)
    {
        return sysPartnerDictDataMapper.selectOne(new LambdaQueryWrapper<SysPartnerDictData>().eq(SysPartnerDictData::getDictCode,dictCode));
    }

    /**
     * 根据字典类型查询字典数据
     * @param dictType
     * @return
     */
    public List<SysPartnerDictData> selectDictDataByType(String dictType){
        Long sysCode = SecurityContextHolder.getSysCode();
        return selectDictDataByType(dictType,sysCode);
    }

    public List<SysPartnerDictData> selectDictDataByType(String dictType,Long sysCode){
        LambdaQueryWrapper<SysPartnerDictData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysPartnerDictData::getSysCode,sysCode);
        queryWrapper.eq(SysPartnerDictData::getDictType,dictType);
        queryWrapper.eq(SysPartnerDictData::getStatus,"0");
        queryWrapper.orderByAsc(SysPartnerDictData::getDictSort);
        return sysPartnerDictDataMapper.selectList(queryWrapper);
    }

    /**
     * 根据字典类型与字典键值查询字典数据
     * @param dictType
     * @param value
     * @return
     */
    public SysPartnerDictData selectDictDataByTypeAndValue(String dictType,String value,Long dictCode){
        LambdaQueryWrapper<SysPartnerDictData> queryWrapper = new LambdaQueryWrapper<>();
        Long sysCode = SecurityContextHolder.getSysCode();
        queryWrapper.eq(SysPartnerDictData::getSysCode,sysCode);
        queryWrapper.eq(SysPartnerDictData::getDictType,dictType);
        queryWrapper.eq(SysPartnerDictData::getDictValue,value);
        queryWrapper.ne(ObjectUtil.isNotNull(dictCode),SysPartnerDictData::getDictCode,dictCode);
        return sysPartnerDictDataMapper.selectOne(queryWrapper);
    }


    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    public void deleteDictDataByIds(Long[] dictCodes)
    {
        for (Long dictCode : dictCodes)
        {
            SysPartnerDictData data = selectDictDataById(dictCode);
            sysPartnerDictDataMapper.delete(new LambdaQueryWrapper<SysPartnerDictData>().eq(SysPartnerDictData::getDictCode, dictCode));

            List<SysPartnerDictData> dictDatas = selectDictDataByType(data.getDictType());
            DictUtils.setPartnerDictCache(data.getDictType(), dictDatas,null);
        }
    }


    /**
     * 新增保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    public int insertDictData(SysPartnerDictData data)
    {
        data.setCreateTime(new Date());
//        data.setCreateBy(String.valueOf(SecurityContextHolder.getUserId()));
        SysPartnerDictData sysPartnerDictData = selectDictDataByTypeAndValue(data.getDictType(), data.getDictValue(),null);
        if (ObjectUtil.isNotNull(sysPartnerDictData)){
            throw new RuntimeException(data.getDictType()+"类型下已存在键值"+data.getDictValue());
        }
        Long sysCode = SecurityContextHolder.getSysCode();
        data.setSysCode(sysCode);

        int row = sysPartnerDictDataMapper.insert(data);
        if (row > 0)
        {
            List<SysPartnerDictData> dictDatas = selectDictDataByType(data.getDictType());
            DictUtils.setPartnerDictCache(data.getDictType(), dictDatas,null);
        }
        return row;
    }


    /**
     * 修改保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    public int updateDictData(SysPartnerDictData data)
    {
        data.setUpdateTime(new Date());
//        data.setCreateBy(String.valueOf(SecurityContextHolder.getUserId()));
        SysPartnerDictData sysPartnerDictData = selectDictDataByTypeAndValue(data.getDictType(), data.getDictValue(),data.getDictCode());
        if (ObjectUtil.isNotNull(sysPartnerDictData)){
            throw new RuntimeException(data.getDictType()+"类型下已存在键值"+data.getDictValue());
        }
        int row = sysPartnerDictDataMapper.update(data,new LambdaQueryWrapper<SysPartnerDictData>().eq(SysPartnerDictData::getDictCode, data.getDictCode()));
        if (row > 0)
        {
            List<SysPartnerDictData> dictDatas = selectDictDataByType(data.getDictType());
            DictUtils.setPartnerDictCache(data.getDictType(), dictDatas,null);
        }
        return row;
    }

}
