package com.zksr.system.controller.software;

import javax.annotation.Resource;
import javax.validation.Valid;

import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.partner.vo.SysPartnerPageReqVO;
import com.zksr.system.controller.partner.SysPartnerController;
import com.zksr.system.controller.partner.vo.SysPartnerAccountRespVO;
import com.zksr.system.controller.software.vo.SysSoftwareAccountRespVO;
import com.zksr.system.convert.partner.PartnerConvert;
import com.zksr.system.domain.SysPartner;
import com.zksr.system.service.ISysPartnerService;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysSoftware;
import com.zksr.system.service.ISysSoftwareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.software.vo.SysSoftwarePageReqVO;
import com.zksr.system.controller.software.vo.SysSoftwareSaveReqVO;
import com.zksr.system.controller.software.vo.SysSoftwareRespVO;
import com.zksr.system.convert.software.SysSoftwareConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 软件商信息Controller
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Api(tags = "管理后台 - 软件商信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/software")
public class SysSoftwareController {

    @Autowired
    private ISysSoftwareService sysSoftwareService;

    @Autowired
    private ISysPartnerService sysPartnerService;

    @Resource
    private AccountApi accountApi;

    /**
     * 新增软件商信息
     */
    @ApiOperation(value = "新增软件商信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "软件商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysSoftwareSaveReqVO createReqVO) {
        return success(sysSoftwareService.insertSysSoftware(createReqVO));
    }

    /**
     * 修改软件商信息
     */
    @ApiOperation(value = "修改软件商信息", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "软件商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysSoftwareSaveReqVO updateReqVO) {
            sysSoftwareService.updateSysSoftware(updateReqVO);
        return success(true);
    }

    /**
     * 删除软件商信息
     */
    @ApiOperation(value = "删除软件商信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "软件商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{softwareIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] softwareIds) {
        sysSoftwareService.deleteSysSoftwareBySoftwareIds(softwareIds);
        return success(true);
    }

    /**
     * 获取软件商信息详细信息
     */
    @ApiOperation(value = "获得软件商信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{softwareId}")
    public CommonResult<SysSoftwareRespVO> getInfo(@PathVariable("softwareId") Long softwareId) {
        SysSoftware sysSoftware = sysSoftwareService.getSysSoftware(softwareId);
        SysSoftwareRespVO sysSoftwareRespVO = HutoolBeanUtils.toBean(sysSoftware, SysSoftwareRespVO.class);
        return success(sysSoftwareRespVO);
    }

    /**
     * 分页查询软件商信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得软件商信息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysSoftwareRespVO>> getPage(@Valid SysSoftwarePageReqVO pageReqVO) {
        PageResult<SysSoftware> pageResult = sysSoftwareService.getSysSoftwarePage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, SysSoftwareRespVO.class));
    }

    /**
     * 分页软件商账户余额
     */
    @GetMapping("/accountList")
    @ApiOperation(value = "获得软件商账户分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysSoftwareAccountRespVO>> getAccountPage(@Valid SysPartnerPageReqVO pageReqVO) {
        SysSoftware userSoftware = sysSoftwareService.getSysSoftwareByUserId(SecurityUtils.getUserId());
        if (Objects.nonNull(userSoftware)) {
            pageReqVO.setSoftwareId(userSoftware.getSoftwareId());
        }

        PageResult<SysPartner> pageResult = sysPartnerService.getSysPartnerPage(pageReqVO);
        // 转换成账户列表
        PageResult<SysSoftwareAccountRespVO> accountResult = SysSoftwareConvert.INSTANCE.convert(pageResult);
        // 获取账户余额
        accountResult.getList().forEach(item -> {
            AccAccountDTO account = accountApi.getAccount( item.getSysCode(), item.getSoftwareId(), MerchantTypeEnum.SOFTWARE.getType()).getCheckedData();
            // 写入金额
            PartnerConvert.INSTANCE.convert(item, account);
            SysSoftware software = sysSoftwareService.getSysSoftware(item.getSoftwareId());
            if (Objects.nonNull(software)) {
                item.setSoftwareName(software.getSoftwareName());
            }
        });
        return success(accountResult);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:software:add";
        /** 编辑 */
        public static final String EDIT = "system:software:edit";
        /** 删除 */
        public static final String DELETE = "system:software:remove";
        /** 列表 */
        public static final String LIST = "system:software:list";
        /** 查询 */
        public static final String GET = "system:software:query";
        /** 停用 */
        public static final String DISABLE = "system:software:disable";
        /** 启用 */
        public static final String ENABLE = "system:software:enable";
    }
}
