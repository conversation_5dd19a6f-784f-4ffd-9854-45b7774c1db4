package com.zksr.system.service.impl;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysUserColumnMapper;
import com.zksr.system.convert.column.SysUserColumnConvert;
import com.zksr.system.domain.SysUserColumn;
import com.zksr.system.controller.column.vo.SysUserColumnPageReqVO;
import com.zksr.system.controller.column.vo.SysUserColumnSaveReqVO;
import com.zksr.system.service.ISysUserColumnService;

import java.util.ArrayList;
import java.util.List;


/**
 * 用户头列配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Service
public class SysUserColumnServiceImpl implements ISysUserColumnService {
    @Autowired
    private SysUserColumnMapper sysUserColumnMapper;

    /**
     * 新增用户头列配置
     *
     * @param createReqVOList 创建信息
     * @return 结果
     */
    @Override
    public List<Long> insertSysUserColumnBatch(List<SysUserColumnSaveReqVO> createReqVOList) {
        List<Long> userColumnIds = new ArrayList<>();
        for (SysUserColumnSaveReqVO createReqVO : createReqVOList) {
            if (ToolUtil.isNotEmpty(createReqVO.getUserColumnId())) {
                sysUserColumnMapper.deleteById(createReqVO.getUserColumnId());
            }

            SysUserColumn sysUserColumn = SysUserColumnConvert.INSTANCE.convert(createReqVO);
            sysUserColumn.setUserColumnId(null);
            sysUserColumn.setSysCode(null);
            sysUserColumnMapper.insert(sysUserColumn);
            userColumnIds.add(sysUserColumn.getUserColumnId());
        }
        return userColumnIds;
    }


    /**
     * 批量删除用户头列配置
     *
     * @param userColumnIds 需要删除的用户头列配置主键
     * @return 结果
     */
    @Override
    public void deleteSysUserColumnByUserColumnIds(Long[] userColumnIds) {
        for(Long userColumnId : userColumnIds){
            sysUserColumnMapper.deleteById(userColumnId);
        }
    }

    /**
     * 查询所有用户头列配置数据
     * @param reqVO 查询条件
     * @return 查询结果列表
     */
    @Override
    public List<SysUserColumn> getSysUserColumnList(SysUserColumnPageReqVO reqVO) {
        return sysUserColumnMapper.selectList(reqVO);
    }

    @Override
    public void deleteSysUserColumnByTableCode(String tableCode, String userName) {
        sysUserColumnMapper.deleteByTableCode(tableCode, userName);
    }
// TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 用户头列配置 TODO 补充编号 ==========
    // ErrorCode SYS_USER_COLUMN_NOT_EXISTS = new ErrorCode(TODO 补充编号, "用户头列配置不存在");


}
