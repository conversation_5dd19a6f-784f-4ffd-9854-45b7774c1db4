package com.zksr.system.visualSyn.model;

import cn.hutool.http.HttpException;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.core.erpUtils.SecretUtil;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.core.enums.request.VisualSettingConstants.APPLICATION_URLENCODED;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS_SSS;
import static com.zksr.common.redis.enums.RedisSyncConstants.SYNC_HAO_BANG_NI_TOKEN;
import static com.zksr.common.redis.enums.RedisSyncConstants.SYNC_HAO_BANG_NI_TOKEN_TIME;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_HEADER_DATA_ERR;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_VERIFY_DATA_ERR;

/**
*  对接《好帮你》业务抽象子类
* @date 2024/10/25 15:42
* <AUTHOR>
*/
@Slf4j
public class HbnSyncRequestModel<SYNCDTO> extends SyncRequestModel<SYNCDTO>{
    public HbnSyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMasterDto, VisualSettingDetailDto visualDetailDto, VisualSettingTemplateDto visualTemplateDto, OpensourceDto opensourceDto, SYNCDTO syncdto) {
        super(data, visualMasterDto, visualDetailDto,visualTemplateDto,opensourceDto, syncdto);
    }

    /**
     * 2.1.获取openToken 请求URL
     */
    private static final String TOKEN_API_URL =  "/api/platform/openApi/getToken";

    @Override
    public String assembleHeader(){
        try{
            Map<String, Object> header = new HashMap<>();
            //获取时间戳
            long timestamp = System.currentTimeMillis();

            //鉴权token
            header.put("openToken", getToken(opensourceDto));
            //校验请求是否重复提交
            header.put("nonce", timestamp + IdUtils.simpleUUID());
            //时间戳，毫秒数表示
            header.put("timestamp", timestamp+"");
            header.put("Content-Type", HttpUtils.JSON);
            return JSON.toJSONString(header);
        }catch (Exception e){
            log.error(" HbnSyncRequestModel.assembleHeader异常，", e);
            throw exception(SYNC_ASSEMBLE_HEADER_DATA_ERR,e.getMessage());
        }
    }

    @DistributedLock(prefix = RedisLockConstants.LOCK_SYNC_HAO_BANG_NI_TOKEN, condition = "#opensourceDto.merchantId")
    private String getToken(OpensourceDto opensourceDto){
        //获取缓存中的token
        RedisService redisService = SpringUtils.getBean(RedisService.class);

        //设置key
        String redisKey = SYNC_HAO_BANG_NI_TOKEN + opensourceDto.getMerchantId();
        String token = redisService.getCacheObject(redisKey);

        //缓存已过期/不存在 重新获取
        if(ToolUtil.isEmpty(token)){
            //设置请求参数
            //公钥
            String publicKey = opensourceDto.getPublicKey();

            //私钥/密钥
            String privateKey = opensourceDto.getPrivateKey();

            if(ToolUtil.isEmpty(publicKey) || ToolUtil.isEmpty(privateKey)){
                throw new RuntimeException(StringUtils.format("获取token信息失败，不存在公钥/私钥，公钥：{},私钥：{}",publicKey,privateKey));
            }

            //设置请求参数
            Map<String,String> map = new HashMap<>();
            map.put("appId",publicKey);
            map.put("appSecret",privateKey);

            //请求token接口 获取token信息
            String result = HttpUtils.simplePost(opensourceDto.getSendUrl() + TOKEN_API_URL, null, JSON.toJSONString(map),APPLICATION_URLENCODED);

            //获取最新的token信息
            Map resultMap = JSONUtil.toBean(result, Map.class);
            Map data = (Map) resultMap.get("data");
            if(ToolUtil.isEmpty(data)){
                throw new RuntimeException(StringUtils.format("获取token信息失败，响应结果不存在token信息：{}",result));
            }

            token = (String) data.get("token");
            if(ToolUtil.isEmpty(token)){
                throw new RuntimeException(StringUtils.format("获取token信息失败，响应结果不存在token信息：{}",result));
            }

            //更新token缓存
            redisService.setCacheObject(redisKey,token,SYNC_HAO_BANG_NI_TOKEN_TIME, TimeUnit.DAYS);

        }

        return token;
    }

}
