package com.zksr.system.controller.software.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 软件商信息对象 sys_software
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
@ApiModel("软件商信息 - sys_software Response VO")
public class SysSoftwareRespVO {
    private static final long serialVersionUID = 1L;

    /** 软件商id */
    @ApiModelProperty(value = "软件商分润比例")
    private String softwareId;

    /** 软件商名 */
    @Excel(name = "软件商名")
    @ApiModelProperty(value = "软件商名")
    private String softwareName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 关联软件商管理员的账号id */
    @Excel(name = "关联软件商管理员的账号id")
    @ApiModelProperty(value = "关联软件商管理员的账号id")
    private Long softwareUserId;

    /** 软件商分润比例 */
    @Excel(name = "软件商分润比例")
    @ApiModelProperty(value = "软件商分润比例")
    private BigDecimal softwareRate;

    /** 用户名 */
    @Excel(name = "软件商账号名")
    @ApiModelProperty(value = "软件商账号名")
    private String userName;

}
