package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.form.SysFileImportDtlForm;
import com.zksr.system.mapper.SysFileImportDtlMapper;
import com.zksr.system.service.ISysFileImportDtlService;
import com.zksr.system.service.ISysFileImportService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 导入记录 业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SysFileImportDtlServiceImpl implements ISysFileImportDtlService
{

    @Resource
    private SysFileImportDtlMapper sysFileImportDtlMapper;

    public Boolean batchAdd(List<SysFileImportDtl> list){
        return sysFileImportDtlMapper.insertBatch(list);
    }

    public List<SysFileImportDtl> getList(SysFileImportDtlForm form){
        return sysFileImportDtlMapper.selectList(new LambdaQueryWrapper<SysFileImportDtl>().eq(SysFileImportDtl::getFileImportId,form.getFileImportId())
                .eq(ObjectUtil.isNotNull(form.getStatus()),SysFileImportDtl::getStatus,form.getStatus()));
    }

    public SysFileImportDtl getById(Long importTypeDtlId){
        return sysFileImportDtlMapper.selectById(importTypeDtlId);
    }

}
