package com.zksr.system.controller.area.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 区域城市对象 sys_area
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@Data
@ApiModel("区域城市 - sys_area分页 Request VO")
public class SysAreaSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 区域城市id */
    @Excel(name = "区域城市id")
    @ApiModelProperty(value = "区域城市id")
    private Long areaId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id")
    private Long pid;

    /** 区域城市名 */
    @Excel(name = "区域城市名")
    @ApiModelProperty(value = "区域城市名")
    private String areaName;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 运营商id */
    @Excel(name = "运营商id")
    @ApiModelProperty(value = "运营商id")
    private Long dcId;

    /** 是否开通本地配送业务 1-是 0-否 */
    @Excel(name = "是否开通本地配送业务 1-是 0-否")
    @ApiModelProperty(value = "是否开通本地配送业务 1-是 0-否")
    private String localFlag;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id")
    private Long groupId;

    /** 级别 */
    @Excel(name = "级别")
    @ApiModelProperty(value = "级别")
    private Integer level;

    /** 排序号 */
    @Excel(name = "排序号")
    @ApiModelProperty(value = "排序号")
    private Integer sortNum;

    @ApiModelProperty("三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;
}
