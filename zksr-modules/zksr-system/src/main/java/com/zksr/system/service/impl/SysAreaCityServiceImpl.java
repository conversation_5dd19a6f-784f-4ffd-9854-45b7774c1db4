package com.zksr.system.service.impl;

import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysAreaCityMapper;
import com.zksr.system.convert.area.SysAreaCityConvert;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;
import com.zksr.system.service.ISysAreaCityService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 省市区Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class SysAreaCityServiceImpl implements ISysAreaCityService {
    @Autowired
    private SysAreaCityMapper sysAreaCityMapper;

    /**
     * 新增省市区
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysAreaCity(SysAreaCitySaveReqVO createReqVO) {
        // 插入
        SysAreaCity sysAreaCity = SysAreaCityConvert.INSTANCE.convert(createReqVO);
        sysAreaCityMapper.insert(sysAreaCity);
        // 返回
        return sysAreaCity.getAreaCityId();
    }

    /**
     * 修改省市区
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysAreaCity(SysAreaCitySaveReqVO updateReqVO) {
        sysAreaCityMapper.updateById(SysAreaCityConvert.INSTANCE.convert(updateReqVO));
    }

    @Override
    public void updateBatch(List<SysAreaCitySaveReqVO> updateReqVOList) {
        sysAreaCityMapper.updateBatch(SysAreaCityConvert.INSTANCE.convert(updateReqVOList));
    }

    /**
     * 删除省市区
     *
     * @param areaCityId ${pkColumn.columnComment}
     */
    @Override
    public void deleteSysAreaCity(Long areaCityId) {
        // 删除
        sysAreaCityMapper.deleteById(areaCityId);
    }

    /**
     * 批量删除省市区
     *
     * @param areaCityIds 需要删除的省市区主键
     * @return 结果
     */
    @Override
    public void deleteSysAreaCityByAreaCityIds(Long[] areaCityIds) {
        for(Long areaCityId : areaCityIds){
            this.deleteSysAreaCity(areaCityId);
        }
    }

    /**
     * 获得省市区
     *
     * @param areaCityId ${pkColumn.columnComment}
     * @return 省市区
     */
    @Override
    public SysAreaCity getSysAreaCity(Long areaCityId) {
        return sysAreaCityMapper.selectById(areaCityId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysAreaCity> getSysAreaCityPage(SysAreaCityPageReqVO pageReqVO) {
        return sysAreaCityMapper.selectPage(pageReqVO);
    }

    /**
     * 根据 城市区域名称、级别 、父城市区域  查询信息
     * @param name 城市区域名称
     * @param pid 父城市区域
     * @param deep 级别
     * @return
     */
    @Override
    public SysAreaCity getByNameAndParent(String name, Long pid, Integer deep) {
        return sysAreaCityMapper.getByNameAndParent(name,pid,deep);
    }

    @Override
    public Long insert(SysAreaCitySaveReqVO insertVO) {
        SysAreaCity sysAreaCity = SysAreaCityConvert.INSTANCE.convert(insertVO);
        sysAreaCityMapper.insert(sysAreaCity);
        return sysAreaCity.getAreaCityId();
    }

    @Override
    public SysAreaCity queryByName(String districtName, String cityName, String provinceName) {
        return sysAreaCityMapper.queryByName(districtName, cityName, provinceName);
    }

}
