package com.zksr.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailPageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailRespVO;
import com.zksr.system.domain.VisualSettingDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

import static com.zksr.common.core.constant.StatusConstants.STATE_ENABLE;


/**
 * 可视化配置详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Mapper
public interface VisualSettingDetailMapper extends BaseMapperX<VisualSettingDetail> {
    default PageResult<VisualSettingDetail> selectPage(VisualSettingDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<VisualSettingDetail>()
                    .eqIfPresent(VisualSettingDetail::getVisualDetailId, reqVO.getVisualDetailId())
                    .eqIfPresent(VisualSettingDetail::getStatus, reqVO.getStatus())
                    .eqIfPresent(VisualSettingDetail::getVisualMasterId, reqVO.getVisualMasterId())
                    .eqIfPresent(VisualSettingDetail::getVisualTemplateId, reqVO.getVisualTemplateId())
                    .eqIfPresent(VisualSettingDetail::getTemplateType, reqVO.getTemplateType())
                    .eqIfPresent(VisualSettingDetail::getReqType, reqVO.getReqType())
                    .eqIfPresent(VisualSettingDetail::getApiUrl, reqVO.getApiUrl())
                    .eqIfPresent(VisualSettingDetail::getContentType, reqVO.getContentType())
                    .likeIfPresent(VisualSettingDetail::getRespName, reqVO.getRespName())
                    .eqIfPresent(VisualSettingDetail::getRespCode, reqVO.getRespCode())
                    .eqIfPresent(VisualSettingDetail::getDebugResultStatus, reqVO.getDebugResultStatus())
                    .eqIfPresent(VisualSettingDetail::getDebugResultMessage, reqVO.getDebugResultMessage())
                    .eqIfPresent(VisualSettingDetail::getMemo, reqVO.getMemo())
                .orderByDesc(VisualSettingDetail::getVisualDetailId));
    }

    List<VisualSettingDetailRespVO> list(@Param("vo") VisualSettingDetailPageReqVO pageReqVO);

    default VisualSettingDetail getVisualSettingDetailByMasterIdAndTemplateType(Long visualMasterId, Long templateType){
        return selectOne(new LambdaQueryWrapper<VisualSettingDetail>()
                .eq(VisualSettingDetail::getVisualMasterId,visualMasterId)
                .eq(VisualSettingDetail::getTemplateType,templateType)
                .eq(VisualSettingDetail::getStatus,STATE_ENABLE));

    }
}
