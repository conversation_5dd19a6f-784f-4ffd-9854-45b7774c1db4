package com.zksr.system.controller.pageConfig.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 平台页面配置对象 sys_pages_config
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Data
@ApiModel("平台页面配置 - sys_pages_config Response VO")
public class SysPagesConfigRespVO {
    private static final long serialVersionUID = 1L;

    /** 自定义页面ID */
    @ApiModelProperty(value = "自定义页面ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pageId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 运营商ID;运营商ID */
    @Excel(name = "运营商ID;")
    @ApiModelProperty(value = "运营商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 启用时间 */
    @Excel(name = "启用时间")
    @ApiModelProperty(value = "启用时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date enableTime;

    /** 创建时间 */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date createTime;

    /** 页面名称 */
    @Excel(name = "页面名称")
    @ApiModelProperty(value = "页面名称")
    private String pageName;

    /** 页面类型;页面类型,index-首页 */
    @Excel(name = "页面类型;页面类型,index-首页")
    @ApiModelProperty(value = "页面类型;页面类型,index-首页")
    private String pageType;

    /** 页面配置JSON */
    @Excel(name = "页面配置JSON")
    @ApiModelProperty(value = "页面配置JSON")
    private String pageConfig;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    @ApiModelProperty(value = "渠道ID")
    private String channelId;

    /** 区域城市ID */
    @Excel(name = "区域城市ID")
    @ApiModelProperty(value = "区域城市ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 是否默认 (0非默认 1默认) */
    @Excel(name = "是否默认 (0非默认 1默认)")
    @ApiModelProperty(value = "是否默认 (0非默认 1默认)")
    private String defFlag;

    /** 状态 (0正常 1停用) */
    @Excel(name = "状态 (0正常 1停用)")
    @ApiModelProperty(value = "状态 (0正常 1停用)")
    private String status;

    /** 城市名称 */
    @Excel(name = "城市名称")
    @ApiModelProperty(value = "城市名称")
    private String areaName;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /** 父级页面ID */
    @Excel(name = "父级页面ID")
    @ApiModelProperty(value = "父级页面ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 1-一级页面, 2-二级页面 */
    @Excel(name = "1-一级页面, 2-二级页面")
    @ApiModelProperty(value = "1-一级页面, 2-二级页面")
    private Integer level;

    /** 0-没有子页面,1-有子页面 */
    @Excel(name = "0-没有子页面,1-有子页面")
    @ApiModelProperty(value = "0-没有子页面,1-有子页面")
    private Integer hasChild;

    @Excel(name = "配置文件地址")
    @ApiModelProperty(value = "配置文件地址")
    private String jsonUrl;

    /** 扩展url 限制1024字符*/
    @ApiModelProperty(value = "扩展url, 限制1024字符")
    private String urlDtl;

    @ApiModelProperty("0-固定模版, 1-时效模版")
    private Integer type;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("有效开始时间")
    private Date startTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("有效结束时间")
    private Date endTime;
}
