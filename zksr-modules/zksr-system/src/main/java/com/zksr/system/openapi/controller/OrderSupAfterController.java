package com.zksr.system.openapi.controller;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.domain.vo.openapi.receive.AfterCancelVO;
import com.zksr.common.core.domain.vo.openapi.receive.AfterSalesReturnVO;
import com.zksr.common.core.domain.vo.openapi.receive.AfterStateVO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mq.OpenapiProducer;
import com.zksr.system.openapi.service.ISysTokenService;
import com.zksr.system.service.ISysInterfaceLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.OPENAPI_CHECK_REQVO_EXISTS;

@Api(tags = "OPENAPI - 订单")
@RestController
@RequestMapping("/openapi/after")
@Slf4j
public class OrderSupAfterController {

    @Autowired
    private OpenapiProducer openapiProducer;

    @Resource
    private ISysTokenService sysTokenService;

    @Resource
    private ISysInterfaceLogService sysInterfaceLogService;
    @ApiOperation("退货确认")
    @PostMapping(value = "/confirmReturn")
    @RequiresOpenapiLogin(abilityKey = "confirmReturn")
    @Log(title = "OPENAPI - 退货确认", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> confirmReturn(@RequestBody @Valid AfterSalesReturnVO vo){

        //获取日志信息
        SysInterfaceLog interfaceLog = afterSaveLog(vo, B2BRequestType.CONFIRM_RETURN, vo.getSupplierAfterNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendConfirmReturnMsg(interfaceLog);
        }
        return CommonResult.success(true);

    }


    @ApiOperation("退货确认前取消(售后取消)")
    @PostMapping(value = "/afterCancel")
    @RequiresOpenapiLogin(abilityKey = "afterCancel")
    @Log(title = "OPENAPI - 售后取消", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> afterCancel(@RequestBody @Valid AfterCancelVO vo){

        //获取日志信息
        SysInterfaceLog interfaceLog = afterSaveLog(vo, B2BRequestType.AFTER_CANCEL, vo.getSupplierAfterNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendAfterCancelMsg(interfaceLog);
        }
        return CommonResult.success(true);

    }


    @ApiOperation(value = "售后状态", httpMethod = HttpMethod.POST)
    @PostMapping("/afterLog")
    @RequiresOpenapiLogin(abilityKey = "afterLog")
    @Log(title = "OPENAPI - 售后状态", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> afterLog(@RequestBody @Valid AfterStateVO vo)  {

        //获取日志信息
        SysInterfaceLog interfaceLog = afterSaveLog(vo, B2BRequestType.AFTER_LOG, vo.getSupplierOrderNo(),vo.getLogisticsStatus() + StringPool.UNDERSCORE + vo.getLogisticsStatusInfo());
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendAfterlogMsg(interfaceLog);
        }
        return CommonResult.success(true);

    }



    public SysInterfaceLog afterSaveLog(Object vo, B2BRequestType requestType, String dataId,String code){
        SysInterfaceLog interfaceLog = new SysInterfaceLog();

        if(ToolUtil.isEmpty(vo)){
            log.info("OPENAPI - {}:入参为空",requestType.getInfo());
            throw exception(OPENAPI_CHECK_REQVO_EXISTS,requestType.getInfo());
        }

        //设置reqId
        String reqId = dataId + StringPool.UNDERSCORE + requestType.getB2bType();
        if(B2BRequestType.AFTER_LOG.equals(requestType)){
            reqId = dataId + StringPool.UNDERSCORE + requestType.getB2bType() + StringPool.UNDERSCORE + code;
        }
        //TODO 这里应该也要根据业务有幂等校验
        //如果是已经接收成功并且处理成功的 则不需要向下继续执行
        if(sysTokenService.checkReceiveLogBefore(reqId)){
            return interfaceLog;
        }

        //组装日志公共参数
        interfaceLog = sysTokenService.getOpenapiInitLog(requestType.getB2bType(), OperationType.UPDATE.getCode(), reqId);
        log.info("OPENAPI - {}:{}",requestType.getInfo(), JSON.toJSONString(vo));

        //组装业务参数
        //新增或更新请求参数
        interfaceLog.setBizData(JSON.toJSONString(vo));
        interfaceLog.setReqId(reqId);

        //新增日志信息
        sysInterfaceLogService.insertLog(interfaceLog);
        return interfaceLog;
    }

}
