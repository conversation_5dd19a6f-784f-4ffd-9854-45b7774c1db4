package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;
import com.zksr.system.api.wx.dto.WxQrData;
import com.zksr.system.domain.SysAreaCity;

import javax.validation.Valid;
public interface IWxQrDataService {

    WxQrData save(String qrValue);

    WxQrData getByQrKey(String qrKey);

}
