package com.zksr.system.service.impl;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPrintTemplate;
import com.zksr.system.mapper.SysPrintTemplateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysPrintSettingsMapper;
import com.zksr.system.convert.printSettings.SysPrintSettingsConvert;
import com.zksr.system.domain.SysPrintSettings;
import com.zksr.system.controller.print.vo.SysPrintSettingsPageReqVO;
import com.zksr.system.controller.print.vo.SysPrintSettingsSaveReqVO;
import com.zksr.system.service.ISysPrintSettingsService;

import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_PRINTER_SETTER_TYPE_REPEAT;

/**
 * 打印设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
@Service
public class SysPrintSettingsServiceImpl implements ISysPrintSettingsService {

    @Autowired
    private SysPrintSettingsMapper sysPrintSettingsMapper;

    @Autowired
    private SysPrintTemplateMapper sysPrintTemplateMapper;

    /**
     * 新增打印设置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysPrintSettings(SysPrintSettingsSaveReqVO createReqVO) {
        // 插入
        SysPrintSettings sysPrintSettings = SysPrintSettingsConvert.INSTANCE.convert(createReqVO);
        /*Long count = sysPrintSettingsMapper.selectByType(createReqVO.getModuleType());
        if (count > NumberPool.LONG_ZERO) {
            // 打印模版重复
            throw exception(SYS_PRINTER_SETTER_TYPE_REPEAT);
        }*/
        SysPrintTemplate printTemplate = sysPrintTemplateMapper.selectByType(createReqVO.getModuleType(), createReqVO.getPaperType());
        if (Objects.nonNull(printTemplate)) {
            // 从软件商模版获取数据
           sysPrintSettings.setPrintContent(printTemplate.getPrintContent());
        }
        sysPrintSettingsMapper.insert(sysPrintSettings);
        // 返回
        return sysPrintSettings.getPrintSetterId();
    }

    /**
     * 修改打印设置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysPrintSettings(SysPrintSettingsSaveReqVO updateReqVO) {
        sysPrintSettingsMapper.updateById(SysPrintSettingsConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除打印设置
     *
     * @param printSetterId 主键
     */
    @Override
    public void deleteSysPrintSettings(Long printSetterId) {
        // 删除
        SysPrintSettings printSettings = sysPrintSettingsMapper.selectById(printSetterId);
        printSettings.setDelFlag(NumberPool.INT_ONE);
        sysPrintSettingsMapper.updateById(printSettings);
    }

    /**
     * 批量删除打印设置
     *
     * @param printSetterIds 需要删除的打印设置主键
     * @return 结果
     */
    @Override
    public void deleteSysPrintSettingsByPrintSetterIds(Long[] printSetterIds) {
        for(Long printSetterId : printSetterIds){
            this.deleteSysPrintSettings(printSetterId);
        }
    }

    /**
     * 获得打印设置
     *
     * @param printSetterId 主键
     * @return 打印设置
     */
    @Override
    public SysPrintSettings getSysPrintSettings(Long printSetterId) {
        return sysPrintSettingsMapper.selectById(printSetterId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysPrintSettings> getSysPrintSettingsPage(SysPrintSettingsPageReqVO pageReqVO) {
        return sysPrintSettingsMapper.selectPage(pageReqVO);
    }

    @Override
    public List<SysPrintSettings> getModuleType(SysPrintSettingsPageReqVO pageReqVO) {
        return sysPrintSettingsMapper.selectModuleList(pageReqVO);
    }

}
