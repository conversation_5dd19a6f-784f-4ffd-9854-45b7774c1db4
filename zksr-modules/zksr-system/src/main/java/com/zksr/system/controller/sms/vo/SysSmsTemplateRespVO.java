package com.zksr.system.controller.sms.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 短信模版配置对象 sys_sms_template
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Data
@ApiModel("短信模版配置 - sys_sms_template Response VO")
public class SysSmsTemplateRespVO {
    private static final long serialVersionUID = 1L;

    /**  */
    @ApiModelProperty(value = "半小时内发送上限")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long smsTemplateId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 使用场景 */
    @Excel(name = "使用场景")
    @ApiModelProperty(value = "使用场景")
    private Integer scene;

    /** 模版编码 */
    @Excel(name = "模版编码")
    @ApiModelProperty(value = "模版编码")
    private String templateCode;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "状态0=正常,1=停用")
    private Integer status;

    /** 短信内容 */
    @Excel(name = "短信内容")
    @ApiModelProperty(value = "短信内容")
    private String content;

    /** 签名名称 */
    @Excel(name = "签名名称")
    @ApiModelProperty(value = "签名名称")
    private String signName;

    /** 半小时内发送上限 */
    @Excel(name = "半小时内发送上限")
    @ApiModelProperty(value = "半小时内发送上限")
    private Integer halfhourCount;

    /** 短信平台 */
    @Excel(name = "短信平台, alibaba-阿里云")
    @ApiModelProperty(value = "短信平台, alibaba-阿里云")
    private String platform;
}
