package com.zksr.system.amap.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* @Description: 猎鹰服务请求实体类
* @Author: liuxingyu
* @Date: 2024/3/27 16:10
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceParam {
    /**
     * 用户在高德地图官网申请Web服务API类型KEY
     */
    @ApiModelProperty("请求服务权限标识")
    private String key;

    /**
     * 不可修改，此参数用于查找 Service
     * 即便填入此参数，用户也不可修改sid。
     */
    @ApiModelProperty("服务的唯一编号")
    private Long sid;

    /**
     * Service 的名字，名字在同一个 Key 下不可重复，不可为空。
     * 命名规则：仅支持中文、英文大小字母、英文下划线"_"、英文横线"-"和数字,不能以"_"开头，最长不得超过128个字符。
     */
    @ApiModelProperty("服务名称")
    private String name;

    /**
     * 针对此 Service 的文字描述，方便用户对 Service 进行记忆。
     * 命名规则：仅支持中文、英文大小字母、英文下划线"_"、英文横线"-"和数字, 不能以"_"开头，最长不得超过128个字符。
     */
    @ApiModelProperty("服务描述")
    private String desc;
}
