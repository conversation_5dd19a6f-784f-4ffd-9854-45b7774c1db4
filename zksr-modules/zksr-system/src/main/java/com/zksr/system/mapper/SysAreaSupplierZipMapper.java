package com.zksr.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.system.domain.SysAreaSupplierZip;
import com.zksr.system.domain.SysSupplier;
import org.apache.ibatis.annotations.Mapper;


/**
 * 区域城市入驻商关系拉链表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Mapper
public interface SysAreaSupplierZipMapper extends BaseMapperX<SysAreaSupplierZip> {
    default SysAreaSupplierZip selectSysAreaSupplierZip(SysSupplier sysSupplier,Long areaId){
        LambdaQueryWrapper<SysAreaSupplierZip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysAreaSupplierZip::getSysCode, sysSupplier.getSysCode())
                    .eq(SysAreaSupplierZip::getAreaId, areaId)
                    .orderByDesc(SysAreaSupplierZip::getStartDate)
                    .last("LIMIT 1");
        return selectOne(queryWrapper);
    }

    void updateSysAreaSupplierZip(SysAreaSupplierZip currentRelation);
}
