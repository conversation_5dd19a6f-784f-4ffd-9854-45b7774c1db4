package com.zksr.system.api.supplier;

import com.zksr.common.core.domain.vo.openapi.receive.SysSupplierDTO;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.service.ISysSupplierService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
public class SupplierApiImpl implements SupplierApi {

    @Autowired
    private ISysSupplierService sysSupplierService;

    @Resource
    private OpensourceApi opensourceApi;


    @Override
    public CommonResult<SupplierDTO> getBySupplierId(Long supplierId) {
        SysSupplier sysSupplier = sysSupplierService.getBySupplierId(supplierId);
        return CommonResult.success(HutoolBeanUtils.toBean(sysSupplier, SupplierDTO.class));
    }

    @Override
    public CommonResult<List<String>> getSupplierNames(List<Long> supplierIds) {
        return success(sysSupplierService.getSupplierNames(supplierIds));
    }

    @Override
    public CommonResult<Map<Long, SupplierDTO>> getUserInfoByMap(@RequestParam(value = "dcId", required = false) Long dcId, @RequestParam(value = "supplierNo", required = false) Long supplierNo) {
        return success(sysSupplierService.getUserInfoByMap(dcId, supplierNo));
    }

    @Override
    public CommonResult<List<SupplierDTO>> getUserInfoByList(@RequestParam(value = "dcId", required = false) Long dcId, @RequestParam(value = "supplierNo", required = false) Long supplierNo) {
        return success(sysSupplierService.getUserInfoByList(dcId, supplierNo));
    }

    /**
     * 根据运营商、平台商查询入驻商信息
     */
    @Override
    public CommonResult<Map<Long, SupplierDTO>> getUserInfo(@RequestParam(value = "dcId", required = false) Long dcId) {
        return success(sysSupplierService.getlistByUser(dcId));
    }

    /**
     * @Description: 根据运营商ID获取区域下所有的入驻商
     * @Author: liuxingyu
     * @Date: 2024/4/9 17:32
     */
    @Override
    public CommonResult<List<Long>> getByOrder(Long dcId) {
        return success(sysSupplierService.getByOrder(dcId));
    }

    @Override
    public CommonResult<PageResult<SysSupplierRespVO>> getPage(SysSupplierPageReqVO pageReqVO) {
        return success(sysSupplierService.getSysSupplierPage(pageReqVO));
    }

    @Override
    public CommonResult<Boolean> addSupplier(Long supplierId, Long opensourceId, SysSupplierDTO sysSupplierDTO) {
        Long aLong = sysSupplierService.saveSysSupplier(supplierId, sysSupplierDTO);
        if(ToolUtil.isNotEmpty(aLong)){
            OpensourceDto opensourceDto = HutoolBeanUtils.toBean(sysSupplierDTO, OpensourceDto.class);
            opensourceDto.setMerchantId(aLong);
            opensourceDto.setSuperSupplierId(supplierId);
            opensourceApi.insertOpensourceSupplierByApi(opensourceDto);
        }
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> checkSyncConfig(Long supplierId) {
        return success(sysSupplierService.checkSyncConfig(supplierId));
    }

    @Override
    public CommonResult<PageResult<SupplierDTO>> getSupplierPage(SysSupplierPageReqVO pageReqVO) {
        return success(sysSupplierService.getSupplierPage(pageReqVO));
    }


}
