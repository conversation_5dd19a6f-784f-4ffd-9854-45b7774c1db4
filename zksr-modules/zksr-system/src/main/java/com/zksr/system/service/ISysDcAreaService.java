package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.domain.SysDcArea;
import com.zksr.system.controller.dcArea.vo.SysDcAreaPageReqVO;
import com.zksr.system.controller.dcArea.vo.SysDcAreaSaveReqVO;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 运营商-区域城市关联关系Service接口
 *
 * <AUTHOR>
 * @date 2024-03-05
 */
public interface ISysDcAreaService {

    /**
     * 新增运营商-区域城市关联关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysDcArea(@Valid SysDcAreaSaveReqVO createReqVO);

    /**
     * 修改运营商-区域城市关联关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysDcArea(@Valid SysDcAreaSaveReqVO updateReqVO);

    /**
     * 删除运营商-区域城市关联关系
     *
     * @param dcId 运营商id;运营商id
     */
    public void deleteSysDcArea(Long dcId);

    /**
     * 批量删除运营商-区域城市关联关系
     *
     * @param dcIds 需要删除的运营商-区域城市关联关系主键集合
     * @return 结果
     */
    public void deleteSysDcAreaByDcIds(Long[] dcIds);

    /**
     * 获得运营商-区域城市关联关系
     *
     * @param dcId 运营商id;运营商id
     * @return 运营商-区域城市关联关系
     */
    public SysDcArea getSysDcArea(Long dcId);

    /**
     * 获得运营商-区域城市关联关系分页
     *
     * @param pageReqVO 分页查询
     * @return 运营商-区域城市关联关系分页
     */
    PageResult<SysDcArea> getSysDcAreaPage(SysDcAreaPageReqVO pageReqVO);

    /**
     *
     * @param dcId
     */
    DcAreaGroupDTO reloadCache(Long dcId);

    /**
     * 获取运营商所拥有的城市组
     * @param dcId  运营商ID
     * @return
     */
    DcAreaGroupDTO getDcAreaGroup(Long dcId);

    /**
     * 根据运营商id或区域城市id 获取运营商--区域城市关系信息
     * @param dcId
     * @param areaId
     * @param sysCode
     * @return
     */
    public SysDcArea getDcAreaByDcIdOrAreaId(Long dcId,Long areaId,Long sysCode);
}
