package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportExcel;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.controller.log.vo.SysInterfaceLogPageReqVO;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;

import java.util.List;

/**
 * 同步接口日志Service接口
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface ISysInterfaceLogService {

    /**
     * 新增同步接口日志
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysInterfaceLog(@Valid SysInterfaceLogSaveReqVO createReqVO);

    /**
     * 修改同步接口日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysInterfaceLog(@Valid SysInterfaceLogSaveReqVO updateReqVO);

    /**
     * 删除同步接口日志
     *
     * @param id id
     */
    public void deleteSysInterfaceLog(Long id);

    /**
     * 批量删除同步接口日志
     *
     * @param ids 需要删除的同步接口日志主键集合
     * @return 结果
     */
    public void deleteSysInterfaceLogByIds(Long[] ids);

    /**
     * 获得同步接口日志
     *
     * @param id id
     * @return 同步接口日志
     */
    public SysInterfaceLog getSysInterfaceLog(Long id);

    /**
     * 获得同步接口日志分页
     *
     * @param pageReqVO 分页查询
     * @return 同步接口日志分页
     */
    PageResult<SysInterfaceLogPageReqVO> getSysInterfaceLogPage(SysInterfaceLogPageReqVO pageReqVO);

    /**
     * 获得同步接口日志
     *
     * @param reqId
     * @return 同步接口日志
     */
    public SysInterfaceLog getSysInterfaceLogByReqId(String reqId);

    /**
     * 根据返回结果修改同步接口日志
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateLogByResult(SysInterfaceLogSaveReqVO updateReqVO);

    /**
     * 数据消息重发
     * @param reqId
     */
    public void messageRetry(String reqId);

    public boolean insertBatch(List<SysInterfaceLog> list);

    public SysInterfaceLog getById(Long logId);

    int insert(SysInterfaceLog log);

    public boolean insertLog(SysInterfaceLog log);

    /**
     * 每天同步第三方数据情况汇总统计(日同步报告)
     * @param sysCode
     * @param date
     * @return
     */
    List<SyncEmailReportExcel> getLogByDaySyncReport(Long sysCode,String date);
}
