package com.zksr.system.openapi.controller;

import com.alibaba.fastjson.JSON;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.exception.enums.GlobalErrorCodeConstants;
import com.zksr.common.core.utils.signature.MideaPayApiSignatureUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.invoice.dto.InvoiceCallbackDTO;
import com.zksr.system.config.MideaInvoiceApiConfig;
import com.zksr.trade.api.invoice.InvoiceApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/openapi/invoice")
@Api(tags = "OPENAPI - 发票接口")
@Slf4j
public class InvoiceController {


    @Autowired
    private InvoiceApi invoiceApi;

    @Autowired
    private MideaInvoiceApiConfig mideaInvoiceApiConfig;

    @ApiOperation("开票回传")
    @PostMapping("/invoiceCallback")
    @ResponseBody
    public CommonResult<Boolean> invoiceCallback(HttpServletResponse response, @Valid @RequestBody InvoiceCallbackDTO invoiceCallbackDTO) {
        if (!verifySignature(JSON.toJSONString(invoiceCallbackDTO), response)) {
            log.error("验证签名失败");
            return CommonResult.error(GlobalErrorCodeConstants.SIGN_ERROR);
        }
        log.info("开票回传参数：{}", invoiceCallbackDTO);
        if (invoiceCallbackDTO == null) {
            throw new ServiceException("参数错误");
        }
        return invoiceApi.invoiceCallback(invoiceCallbackDTO);
    }

    /**
     * 验证签名
     *
     * @param requestBody 明文
     * @param response    respond
     * @return true/false
     */
    private boolean verifySignature(String requestBody, HttpServletResponse response) {
        return MideaPayApiSignatureUtils.verifyResponseSignature(requestBody, response.getHeader("response-signature"), mideaInvoiceApiConfig.getPublicKey());
    }
}
