package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 平台商配置(由软件商设置)对象 sys_partner_config
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@TableName(value = "sys_partner_config")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysPartnerConfig extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商配置id */
    @TableId(type = IdType.AUTO)
    private Long partnerConfigId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 配置名 */
    @Excel(name = "配置名")
    private String configName;

    /** 配置键名 */
    @Excel(name = "配置键名")
    private String configKey;

    /** 配置值 */
    @Excel(name = "配置值")
    private String configValue;

    /** 配置类型（数据字典：sys_partner_config_type） */
    @Excel(name = "配置类型", readConverterExp = "数=据字典：sys_partner_config_type")
    private String configType;

}
