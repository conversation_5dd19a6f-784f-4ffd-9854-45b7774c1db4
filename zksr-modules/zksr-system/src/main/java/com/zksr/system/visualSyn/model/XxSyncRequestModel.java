package com.zksr.system.visualSyn.model;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import lombok.extern.slf4j.Slf4j;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_VERIFY_DATA_ERR;

/**
*  对接小象业务抽象子类
* @date 2024/10/25 15:43
* <AUTHOR>
*/
@Slf4j
public class XxSyncRequestModel<SYNCDTO> extends SyncRequestModel<SYNCDTO>{
    public XxSyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMasterDto, VisualSettingDetailDto visualDetailDto, VisualSettingTemplateDto visualTemplateDto, OpensourceDto opensourceDto, SYNCDTO syncdto) {
        super(data, visualMasterDto, visualDetailDto,visualTemplateDto,opensourceDto, syncdto);
    }

}
