package com.zksr.system.chin.partnerPolicy;

import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyRespVO;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicySaveReqVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
public class PolicyPartnerPolicyPipeline {

    @Autowired
    private List<PolicyPartnerChin> policyPartnerChins;

    /**
     * 平台公共配置保存
     */
    @Transactional(rollbackFor = Exception.class)
    public void partnerSaveConfig(SysPartnerPolicySaveReqVO createReqVO, Long sysCode, Long dcId, Long supplierId) {
        // 保存信息
        for (PolicyPartnerChin partnerChin : policyPartnerChins) {
            partnerChin.saveConfig(createReqVO, sysCode, dcId, supplierId);
        }
    }

    /**
     * 构建平台配置
     */
    public SysPartnerPolicyRespVO partnerGetConfig(SysPartnerPolicyRespVO sysPartnerPolicyRespVO, Long sysCode, Long dcId, Long supplierId, Integer type) {
        //构建链式通道(后续需添加可直接在最后节点添加)
        for (PolicyPartnerChin partnerChin : policyPartnerChins) {
            partnerChin.getConfig(sysPartnerPolicyRespVO, sysCode, dcId, supplierId, type);
        }
        //执行方法
        return sysPartnerPolicyRespVO;
    }

    /**
     * 事务之外移除cache
     */
    public void clean(Long sysCode, Long dcId, Long supplierId) {
        // 批量清除缓存
        for (PolicyPartnerChin partnerChin : policyPartnerChins) {
            partnerChin.clean(sysCode, dcId, supplierId);
        }
    }
}
