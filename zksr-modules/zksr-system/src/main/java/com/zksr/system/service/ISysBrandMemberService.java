package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.controller.brand.vo.SysBrandMemberRespVO;
import com.zksr.system.controller.supplier.vo.SysSupplierSavePasswordReqVO;
import com.zksr.system.domain.SysBrandMember;
import com.zksr.system.controller.brand.vo.SysBrandMemberPageReqVO;
import com.zksr.system.controller.brand.vo.SysBrandMemberSaveReqVO;

/**
 * 品牌商子账户Service接口
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
public interface ISysBrandMemberService {

    /**
     * 新增品牌商子账户
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysBrandMember(@Valid SysBrandMemberSaveReqVO createReqVO);

    /**
     * 修改品牌商子账户
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysBrandMember(@Valid SysBrandMemberSaveReqVO updateReqVO);

    /**
     * 删除品牌商子账户
     *
     * @param brandMemberId ${pkColumn.columnComment}
     */
    public void deleteSysBrandMember(Long brandMemberId);

    /**
     * 批量删除品牌商子账户
     *
     * @param brandMemberIds 需要删除的品牌商子账户主键集合
     * @return 结果
     */
    public void deleteSysBrandMemberByBrandMemberIds(Long[] brandMemberIds);

    /**
     * 获得品牌商子账户
     *
     * @param brandMemberId ${pkColumn.columnComment}
     * @return 品牌商子账户
     */
    public SysBrandMember getSysBrandMember(Long brandMemberId);

    /**
     * 获得品牌商子账户分页
     *
     * @param pageReqVO 分页查询
     * @return 品牌商子账户分页
     */
    PageResult<SysBrandMember> getSysBrandMemberPage(SysBrandMemberPageReqVO pageReqVO);

    /**
     * 获取品牌商子账户SQL分页对象
     * @param pageReqVO
     * @return
     */
    PageResult<SysBrandMemberRespVO> getSysBrandMemberPageExt(SysBrandMemberPageReqVO pageReqVO);

    /**
     * 停用子账户
     * @param brandMemberId   brandMemberId
     */
    void disable(Long brandMemberId);

    /**
     * 启用子账户
     * @param brandMemberId   brandMemberId
     */
    void enable(Long brandMemberId);

    /**
     * 通过userId 获取账户信息
     * @param userId
     * @return
     */
    SysBrandMember getSysBrandMemberByUserId(Long userId);

    /**
     * 修改密码信息
     * @param updateReqVO
     */
    void updateSysBrandMemberPassword(SysBrandMemberSaveReqVO updateReqVO);
}
