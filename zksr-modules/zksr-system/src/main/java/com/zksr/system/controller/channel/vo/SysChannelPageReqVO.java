package com.zksr.system.controller.channel.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 渠道信息对象 sys_channel
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@ApiModel("渠道信息 - sys_channel分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysChannelPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 渠道id */
    @ApiModelProperty(value = "备注")
    private Long channelId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 渠道名 */
    @Excel(name = "渠道名")
    @ApiModelProperty(value = "渠道名")
    private String channelName;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否支持货到付款(0,否 1,是) */
    @Excel(name = "是否支持货到付款(0,否 1,是)")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)", example = "0")
    private Integer hdfkSupport;

    @Excel(name = "启用状态 0 停用 1 启用")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)", example = "0")
    private Integer status;
}
