package com.zksr.system.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.page.TableDataInfo;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.form.SysFileImportDtlForm;
import com.zksr.system.api.form.SysFileImportForm;
import com.zksr.system.service.ISysFileImportDtlService;
import com.zksr.system.service.ISysFileImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 导入记录
 * 
 * <AUTHOR>
 */
@RestController
@Api(tags = "管理后台 - 导入详情接口", produces = "application/json")
@RequestMapping("/sysFileImportDtl")
public class SysFileImportDtlController extends BaseController
{
    @Autowired
    private ISysFileImportDtlService sysFileImportDtlService;


    @GetMapping("/getList")
//    @RequiresPermissions("system:partnerDict:list")
    @ApiOperation("查询列表")
    public TableDataInfo getList(SysFileImportDtlForm sysFileImportForm)
    {
        Page<SysFileImport> page = PageHelper.startPage(sysFileImportForm.getPageNo(), sysFileImportForm.getPageSize());
        List<SysFileImportDtl> list = sysFileImportDtlService.getList(sysFileImportForm);
        return getDataTable(list);
    }

    @GetMapping(value = "/{importTypeDtlId}")
    @ApiOperation("查询详细")
    public AjaxResult getInfo(@PathVariable Long importTypeDtlId)
    {
        return success(sysFileImportDtlService.getById(importTypeDtlId));
    }


}
