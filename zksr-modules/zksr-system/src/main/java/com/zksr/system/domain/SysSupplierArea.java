package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 入驻商-区域城市关联关系对象 sys_supplier_area
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@TableName(value = "sys_supplier_area")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysSupplierArea{
    private static final long serialVersionUID=1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

}
