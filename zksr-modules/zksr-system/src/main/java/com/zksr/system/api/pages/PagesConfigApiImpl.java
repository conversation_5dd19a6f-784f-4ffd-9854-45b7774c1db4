package com.zksr.system.api.pages;

import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.page.PagesConfigApi;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.service.ISysPagesConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/13 8:27
 */
@RestController
@ApiIgnore
@InnerAuth
public class PagesConfigApiImpl implements PagesConfigApi {

    @Autowired
    private ISysPagesConfigService pagesConfigService;
    @Override
    public CommonResult<List<PagesConfigDTO>> getPagesConfig(String pageKey) {
        String[] data = pageKey.split(":");
        Long sysCode = Long.parseLong(data[0]);
        Long channelId = Long.parseLong(data[1]);
        Long areaId = Long.parseLong(data[2]);
        return CommonResult.success(pagesConfigService.getByAreaChannel(sysCode, areaId, channelId));
    }
}
