package com.zksr.system.api.wxQr;

import com.zksr.common.core.domain.vo.openapi.receive.SysSupplierDTO;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.api.wx.WxQrApi;
import com.zksr.system.api.wx.dto.WxQrData;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.service.ISysSupplierService;
import com.zksr.system.service.IWxQrDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
public class WxQrApiImpl implements WxQrApi {

    @Resource
    private IWxQrDataService wxQrDataService;

    public CommonResult<WxQrData> add(String qrValue){
        return CommonResult.success(wxQrDataService.save(qrValue));
    }

    public CommonResult<WxQrData> getByQrKey(String qrKey){
        return CommonResult.success(wxQrDataService.getByQrKey(qrKey));
    }

}
