package com.zksr.system.chin.partnerPolicy;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.system.api.partnerPolicy.dto.XpYunSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.enums.PartnerPolicyEnum;
import com.zksr.system.chin.general.PartnerConfigUtil;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyRespVO;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicySaveReqVO;
import com.zksr.system.domain.SysPartnerPolicy;
import com.zksr.system.mapper.SysPartnerPolicyMapper;
import com.zksr.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class PolicyXpYunSettingHandler extends PolicyPartnerChin {
    @Autowired
    private SysPartnerPolicyMapper sysPartnerPolicyMapper;

    @Autowired
    private Cache<Long, XpYunSettingPolicyDTO> xpYunSettingPolicyCacheBean;

    @Autowired
    private ISysConfigService sysConfigService;

    private static final String key = "xpYunSetting.";

    @Override
    public void saveConfig(SysPartnerPolicySaveReqVO sysPartnerPolicySaveReqVO, Long sysCode, Long dcId, Long supplierId) {
        XpYunSettingPolicyDTO xpYunSettingPolicyDTO = sysPartnerPolicySaveReqVO.getXpYunSettingPolicyDTO();
        if (ObjectUtil.isNotNull(xpYunSettingPolicyDTO)) {
            //填充配置参数
            xpYunSettingPolicyDTO.setKey(sysConfigService.selectConfigByKey("xpYunSetting.key"));
            xpYunSettingPolicyDTO.setUser(sysConfigService.selectConfigByKey("xpYunSetting.user"));
            if (ObjectUtil.isNotNull(xpYunSettingPolicyDTO.getSupplierId())) {
                supplierId = Long.valueOf(xpYunSettingPolicyDTO.getSupplierId());
            }
            //获取参数数据集合
            List<SysPartnerPolicy> sysPartnerPolicys =
                    PartnerConfigUtil.jointPolicyParam(sysCode, dcId, supplierId, key, xpYunSettingPolicyDTO);
            //根据平台ID 获取所有配置信息
            List<SysPartnerPolicy> policyList = sysPartnerPolicyMapper.selectBySysCode(null, null, supplierId);
            //需要修改的数据
            List<SysPartnerPolicy> updateList = sysPartnerPolicys
                    .stream()
                    .filter(a -> policyList.stream().anyMatch(b -> ObjectUtil.equal(a.getPolicyKey(), b.getPolicyKey())))
                    .map(a -> {
                        for (SysPartnerPolicy sysPartnerPolicy : policyList) {
                            if (ObjectUtil.equal(sysPartnerPolicy.getPolicyKey(), a.getPolicyKey())) {
                                a.setPartnerPolicyId(sysPartnerPolicy.getPartnerPolicyId());
                                return a;
                            }
                        }
                        return a;
                    })
                    .collect(Collectors.toList());
            //需要新增的数据
            List<SysPartnerPolicy> insertList = sysPartnerPolicys
                    .stream()
                    .filter(a -> policyList.stream().noneMatch(b -> ObjectUtil.equal(a.getPolicyKey(), b.getPolicyKey())))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(updateList)) {
                sysPartnerPolicyMapper.updateBatch(updateList);
            }
            if (ObjectUtil.isNotEmpty(insertList)) {
                sysPartnerPolicyMapper.insertBatch(insertList);
            }
            //删除缓存
            xpYunSettingPolicyCacheBean.remove(supplierId);
        }
        if (ObjectUtil.isNotNull(next)) {
            next.saveConfig(sysPartnerPolicySaveReqVO, sysCode, dcId, supplierId);
        }
    }

    @Override
    public SysPartnerPolicyRespVO getConfig(SysPartnerPolicyRespVO sysPartnerPolicyRespVO, Long sysCode, Long dcId, Long supplierId, Integer type) {
        if (ObjectUtil.isNull(sysPartnerPolicyRespVO.getXpYunSettingPolicyDTO()) && (ObjectUtil.isNull(type) || ObjectUtil.equal(PartnerPolicyEnum.XPYUN_SETTING_POLICY.getType(), type))) {
            List<SysPartnerPolicy> configList = sysPartnerPolicyMapper.selectBySysCode(null, null, supplierId);
            List<SysPartnerPolicy> partnerList = PartnerConfigUtil.matchingPolicyKey(configList, key);
            if (ObjectUtil.isNotEmpty(partnerList)) {
                XpYunSettingPolicyDTO xpYunSettingPolicyDto = PartnerConfigUtil.encapsulationPolicyObj(partnerList, key, XpYunSettingPolicyDTO.class);
                sysPartnerPolicyRespVO.setXpYunSettingPolicyDTO(xpYunSettingPolicyDto);
                //同步缓存
                xpYunSettingPolicyCacheBean.put(supplierId, xpYunSettingPolicyDto);
            }
        }
        if (ObjectUtil.isNotNull(next)) {
            next.getConfig(sysPartnerPolicyRespVO, sysCode, dcId, supplierId, type);
        }
        return sysPartnerPolicyRespVO;
    }

}
