package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 开放能力对象 sys_openability
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@TableName(value = "sys_openability")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysOpenability extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 开放能力id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long openabilityId;

    /** 父id */
    @Excel(name = "父id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 资源类型（数据字典 supplier-入驻商等） */
    @Excel(name = "资源类型", readConverterExp = "数=据字典,s=upplier-入驻商等")
    private String merchantType;

    /** 接口名 */
    @Excel(name = "接口名")
    private String abilityName;

    /** 接口key(唯一) */
    @Excel(name = "接口key(唯一)")
    private String abilityKey;

    /** 菜单状态（0正常 1停用） */
    @Excel(name = "菜单状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 单个用户qps限制 */
    @Excel(name = "单个用户qps限制")
    private Integer rateLimit;

}
