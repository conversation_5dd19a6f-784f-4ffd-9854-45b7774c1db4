package com.zksr.system.controller.bank.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 联行号信息对象 sys_bank_channel
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Data
@ApiModel("联行号信息 - sys_bank_channel分页 Request VO")
public class SysBankChannelSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(value = "市")
    private Long bankChannelId;

    /** 银行编号 */
    @Excel(name = "银行编号")
    @ApiModelProperty(value = "银行编号")
    private String bankNo;

    /** 银行简称 */
    @Excel(name = "银行简称")
    @ApiModelProperty(value = "银行简称")
    private String bankName;

    /** 支行名称 */
    @Excel(name = "支行名称")
    @ApiModelProperty(value = "支行名称")
    private String branchName;

    /** 联行号 */
    @Excel(name = "联行号")
    @ApiModelProperty(value = "联行号")
    private String bankChannelNo;

    /** 0正常 1异常 */
    @Excel(name = "0正常 1异常")
    @ApiModelProperty(value = "0正常 1异常")
    private Integer status;

    /** 省 */
    @Excel(name = "省")
    @ApiModelProperty(value = "省")
    private String provinceName;

    /** 市 */
    @Excel(name = "市")
    @ApiModelProperty(value = "市")
    private String cityName;

}
