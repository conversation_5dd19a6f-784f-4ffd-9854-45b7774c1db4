package com.zksr.system.controller.group.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台商城市分组对象 sys_group
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@ApiModel("平台商城市分组 - sys_group分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysGroupPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id")
    private Long groupId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 平台商城市分组名 */
    @Excel(name = "平台商城市分组名")
    @ApiModelProperty(value = "平台商城市分组名")
    private String groupName;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;


}
