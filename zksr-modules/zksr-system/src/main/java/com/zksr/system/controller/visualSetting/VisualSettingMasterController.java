package com.zksr.system.controller.visualSetting;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.page.TableDataInfo;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterPageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterSaveReqVO;
import com.zksr.system.convert.visualSetting.visualSettingMaster.VisualSettingMasterConvert;
import com.zksr.system.domain.VisualSettingMaster;
import com.zksr.system.service.IVisualSettingMasterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 可视化配置主Controller
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Api(tags = "管理后台 - 可视化配置主接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/visualapi/master")
public class VisualSettingMasterController {
    @Autowired
    private IVisualSettingMasterService visualSettingMasterService;

    /**
     * 新增可视化配置主
     */
    @ApiOperation(value = "新增可视化配置主", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "可视化配置主", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody VisualSettingMasterSaveReqVO createReqVO) {
        return success(visualSettingMasterService.insertVisualSettingMaster(createReqVO));
    }

    /**
     * 修改可视化配置主
     */
    @ApiOperation(value = "修改可视化配置主", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "可视化配置主", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody VisualSettingMasterSaveReqVO updateReqVO) {
        visualSettingMasterService.updateVisualSettingMaster(updateReqVO);
        return success(true);
    }

    /**
     * 删除可视化配置主
     */
    @ApiOperation(value = "删除可视化配置主", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "可视化配置主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{visualMasterIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] visualMasterIds) {
        visualSettingMasterService.deleteVisualSettingMasterByVisualMasterIds(visualMasterIds);
        return success(true);
    }

    /**
     * 获取可视化配置主详细信息
     */
    @ApiOperation(value = "获得可视化配置主详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{visualMasterId}")
    public CommonResult<VisualSettingMasterRespVO> getInfo(@PathVariable("visualMasterId") Long visualMasterId) {
        VisualSettingMaster visualSettingMaster = visualSettingMasterService.getVisualSettingMaster(visualMasterId);
        return success(HutoolBeanUtils.toBean(visualSettingMaster, VisualSettingMasterRespVO.class));
    }

    /**
     * 分页查询可视化配置主l
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得可视化配置主分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public R getPage(@Valid VisualSettingMasterPageReqVO pageReqVO) {
        PageUtils.startPage();
        List<VisualSettingMasterRespVO> list = visualSettingMasterService.list(pageReqVO);
        return R.tableData(list);
//        PageResult<VisualSettingMaster> pageResult = visualSettingMasterService.getVisualSettingMasterPage(pageReqVO);
//        return success(VisualSettingMasterConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:visualSettingMaster:add";
        /** 编辑 */
        public static final String EDIT = "system:visualSettingMaster:edit";
        /** 删除 */
        public static final String DELETE = "system:visualSettingMaster:remove";
        /** 列表 */
        public static final String LIST = "system:visualSettingMaster:list";
        /** 查询 */
        public static final String GET = "system:visualSettingMaster:query";
        /** 停用 */
        public static final String DISABLE = "system:visualSettingMaster:disable";
        /** 启用 */
        public static final String ENABLE = "system:visualSettingMaster:enable";
    }
}
