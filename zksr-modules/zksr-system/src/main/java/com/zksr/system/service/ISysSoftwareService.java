package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysSoftware;
import com.zksr.system.controller.software.vo.SysSoftwarePageReqVO;
import com.zksr.system.controller.software.vo.SysSoftwareSaveReqVO;

import java.util.List;

/**
 * 软件商信息Service接口
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
public interface ISysSoftwareService {

    /**
     * 新增软件商信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysSoftware(@Valid SysSoftwareSaveReqVO createReqVO);

    /**
     * 修改软件商信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysSoftware(@Valid SysSoftwareSaveReqVO updateReqVO);

    /**
     * 删除软件商信息
     *
     * @param softwareId 软件商id
     */
    public void deleteSysSoftware(Long softwareId);

    /**
     * 批量删除软件商信息
     *
     * @param softwareIds 需要删除的软件商信息主键集合
     * @return 结果
     */
    public void deleteSysSoftwareBySoftwareIds(Long[] softwareIds);

    /**
     * 获得软件商信息
     *
     * @param softwareId 软件商id
     * @return 软件商信息
     */
    public SysSoftware getSysSoftware(Long softwareId);

    /**
     * 获得软件商信息分页
     *
     * @param pageReqVO 分页查询
     * @return 软件商信息分页
     */
    PageResult<SysSoftware> getSysSoftwarePage(SysSoftwarePageReqVO pageReqVO);

    /**
     * 根据用户ID获取软件商信息
     *
     * @param userId 用户ID
     * @return 软件商信息
     */
    SysSoftware getSysSoftwareByUserId(Long userId);


}
