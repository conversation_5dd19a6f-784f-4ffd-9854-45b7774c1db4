package com.zksr.system.mq;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alicp.jetcache.Cache;
import com.zksr.account.api.account.AccountFlowApi;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.domain.vo.openapi.*;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.SysYesNoEnum;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.VisualReceiptType;
import com.zksr.common.core.enums.request.VisualTemplateType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.supplierArea.dto.SupplierAreaDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysSupplierAreaService;
import com.zksr.system.visualSyn.SyncDataTemplate;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.vo.TrdAfter;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.supplierAfter.SupplierAfterApi;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.enums.request.VisualReceiptType.getVisualReceiptType;
import static com.zksr.member.constant.MemberConstant.BRANCH_LIFECYCLE_OPERATION_TYPE_1;

/**
* 第三方系统同步数据消息队列消费者
* @date 2024/7/12 10:10
* <AUTHOR>
*/
@Slf4j
@Configuration
public class SyncDataConsumer {

    @Autowired
    private SyncDataTemplate syncBranch;

    @Autowired
    private SyncDataTemplate syncOrder;

    @Autowired
    private SyncDataTemplate syncAfter;

    @Autowired
    private SyncDataTemplate syncReceipt;

    @Autowired
    private SyncDataTemplate syncBranchValueInfo;

    @Autowired
    private ISysSupplierAreaService sysSupplierAreaService;

    @Autowired
    private SyncDataProducer syncDataProducer;

    @Autowired
    private ISysCacheService sysCacheService;

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Resource
    private SupplierAfterApi supplierAfterApi;

    @Resource
    private OrderApi orderApi;

    @Resource
    private AfterApi afterApi;

    @Autowired
    private BranchApi branchApi;

    @Autowired
    private AccountFlowApi accountFlowApi;

    /**
     * 门店同步信息消费者
     *
     * <AUTHOR>
     * @date 2024/07/12 10:54
     */
    @Bean
    public Consumer<SyncBranchSendDTO> syncDataBranchEvent() {
        return (data) -> {
            log.info("收到待处理的门店同步信息：{} ", data);

            //同步类型
            Integer syncType = data.getSyncType();
            if(ToolUtil.isEmpty(syncType) || Objects.equals(syncType, NumberPool.INT_ONE)){
                //按该门店所在区域 推送门店信息给该区域下各个入驻商
                //获取门店信息
                BranchDTO branchDTO = sysCacheService.getBranchDTO(data.getBranchId());

                //门店生命周期 新增门店注册信息
                if(Objects.equals(data.getOperationTypeCode(), OperationType.ADD.getCode())){
                    try{
                        branchApi.updateBranchLifecycle(data.getBranchId(),BRANCH_LIFECYCLE_OPERATION_TYPE_1,null,null);
                    }catch (Exception e){
                        //如果新增门店生命周期失败了  不影响门店发送
                        log.error("门店生命周期---新增门店注册信息异常",e);
                    }
                }

                //根据门店信息所关联的城市获取管理范围的入驻商
                List<SupplierAreaDTO> supplierAreaDTOS = sysSupplierAreaService.getSupplierAreaByAreaId(branchDTO.getAreaId());
                if (!supplierAreaDTOS.isEmpty()) {
                    //组装需要发送的数据信息
                    SyncDataDTO syncDataDTO = new SyncDataDTO();
                    syncDataDTO.setDataId(String.valueOf(data.getBranchId()))
                            .setTemplateType(VisualTemplateType.BRANCH_TYPE.getType())
                            .setOperationTypeCode(data.getOperationTypeCode())
                            .setSysCode(branchDTO.getSysCode());

                    Set<Long> supplierIds = supplierAreaDTOS.stream().map(SupplierAreaDTO::getSupplierId).collect(Collectors.toSet());
                    supplierIds.forEach(supplierId -> {
                        //根据入驻商ID及发送类型校验是否同步数据
                        if (checkSyncData(supplierId, VisualTemplateType.BRANCH_TYPE.getType())) {
                            //设置需要发送的数据信息
                            syncDataDTO.setSupplierId(supplierId)
                                    .setReqId(IdUtils.fastSimpleUUID());

                            //异步处理需要同步的门店数据
                            syncDataProducer.sendSupplierSyncDataBranchEvent(syncDataDTO);

                        }
                    });
                }

            }else{
                //区域门店信息初始化 将某个区域下的所有门店 推送给某个入驻商
                //入驻商ID
                Long supplierId = data.getSupplierId();
                //获取门店信息
                List<BranchDTO> branchList = branchApi.getBranchListByArea(data.getAreaId()).getCheckedData();

                //组装门店推送信息
                if (!branchList.isEmpty() && checkSyncData(supplierId, VisualTemplateType.BRANCH_TYPE.getType())) {
                    log.info("区域门店信息初始化：{} ", data);
                    //公共参数
                    SyncDataDTO syncDataDTO = new SyncDataDTO();
                    syncDataDTO.setTemplateType(VisualTemplateType.BRANCH_TYPE.getType())
                               .setOperationTypeCode(data.getOperationTypeCode())
                               .setSupplierId(supplierId);

                    //循环推送门店信息
                    branchList.forEach(brnach -> {
                        //组装需要发送的数据信息
                        syncDataDTO.setDataId(String.valueOf(brnach.getBranchId()))
                                   .setSysCode(brnach.getSysCode())
                                   .setReqId(IdUtils.fastSimpleUUID());

                        //异步处理需要同步的门店数据
                        syncDataProducer.sendSupplierSyncDataBranchEvent(syncDataDTO);
                    });
                }
            }
        };
    }

    /**
     * 销售订单同步信息消费者
     *
     * <AUTHOR>
     * @date 2024/07/12 10:54
     */
    @Bean
    public Consumer<TrdOrder> syncDataOrderEvent(){
        return (data) -> {
            log.info("收到待处理的销售订单同步信息：{} ", data);

            if (Objects.isNull(data)) {
                log.info("该销售订单同步信息不存在订单号data:{}", data);
                return;
            }

            //获取入驻商订单信息
            List<OrderOpenDTO> supplierOrderList = supplierOrderApi.getSupplierOrderListByOrderNo(data.getOrderNo()).getCheckedData();

            //根据入驻商ID组装入驻商销售订单信息 (过滤掉已经推送的数据)
            Map<Long, String> supplierOrderMap = supplierOrderList.stream()
                    .filter(x -> !ORDER_SYNC_FLAG_1.equals(x.getPushStatus()) && !ORDER_SYNC_FLAG_2.equals(x.getPushStatus()))
                    .collect(Collectors.toMap(OrderOpenDTO::getSupplierId, OrderOpenDTO::getSupplierOrderNo));

            //校验
            if (supplierOrderMap.isEmpty()) {
                log.info("该销售订单入驻商订单信息查询失败/订单已推送:{},supplierOrderList:{}", data, supplierOrderList);
                return;
            }


            //组装需要发送的数据信息
            SyncDataDTO syncDataDTO = new SyncDataDTO();
            syncDataDTO.setTemplateType(VisualTemplateType.ORDER_TYPE.getType())
                    .setSysCode(supplierOrderList.get(0).getSysCode());

            //循环处理各个入驻商销售订单
            for (Map.Entry<Long, String> entry : supplierOrderMap.entrySet()) {
                Long supplierId = entry.getKey();
                String supplierOrderNo = entry.getValue();
                //根据入驻商ID及发送类型校验是否同步数据
                if (checkSyncData(supplierId, VisualTemplateType.ORDER_TYPE.getType())) {
                    //设置需要发送的数据信息
                    syncDataDTO.setSupplierId(supplierId)
                            .setReqId(IdUtils.fastSimpleUUID())
                            .setDataId(supplierOrderNo);


                    /**
                     *延时消息等级分为18个：1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h
                     *例如 setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL,9)将等级设置3，对应等级的秒数是5m
                     */
                    //默认延时五分钟
                    int propertyDelayTimeLevel = 9;

                    //获取开发平台配置
                    OpensourceDto opensourceDto = sysCacheService.getOpensourceByMerchantId(supplierId);
                    //如果 订单未配置是否自动推送第三方获取 不自动推送第三方 则不推送 需要用户在后台订单模块手动推送
                    if (ObjectUtil.isEmpty(opensourceDto)
                            || ObjectUtil.isEmpty(opensourceDto.getOrderAutoPush())
                            || SysYesNoEnum.NO.getCode().equals(opensourceDto.getOrderAutoPush())) {
                        log.info("该入驻商订单未配置第三方信息或未配置自动推送，不进行推送入驻商订单，入驻商ID：{},入驻商销售订单号:{}", supplierId, supplierOrderNo);
                        continue;
                    }
                    //校验是否设置了延时推送时间 未设置则 默认五分钟
                    if (ObjectUtil.isNotEmpty(opensourceDto.getOrderDelayPushTime())) {
                        propertyDelayTimeLevel = opensourceDto.getOrderDelayPushTime();
                    }
                    //设置延时推送等级
                    syncDataDTO.setPropertyDelayTimeLevel(propertyDelayTimeLevel);
                    //异步处理需要同步的入驻商订单数据
                    syncDataProducer.sendSupplierSyncDataOrderEvent(syncDataDTO);

                }
            }
        };
    }


    /**
     * 售后订单同步消费者
     *
     * @return
     */
    @Bean
    public Consumer<String> syncDataAfterEvent() {
        return (data) -> {
            log.info("收到待处理的售后订单同步信息：{} ", data);

            if (Objects.isNull(data)) {
                log.info("该售后订单同步信息不存在订单号data:{}", data);
                return;
            }
            //获取入驻商订单信息
            List<AfterPushDTO> supplierAfterList = supplierAfterApi.getSupplierAfterListByAfterNo(data).getCheckedData();
            //根据入驻商ID组装入驻商售后订单信息(过滤掉已经推送的数据)
            Map<Long, String> supplierAfterMap = supplierAfterList.stream()
                    .filter(x -> !ORDER_SYNC_FLAG_1.equals(x.getPushStatus()) && !ORDER_SYNC_FLAG_2.equals(x.getPushStatus()))
                    .collect(Collectors.toMap(AfterPushDTO::getSupplierId, AfterPushDTO::getSupplierAfterNo));

            //校验
            if (supplierAfterMap.isEmpty()) {
                log.info("该售后订单入驻商订单信息查询失败/订单已推送:{},supplierAfterList:{}", data, supplierAfterList);
                return;
            }
            //组装需要发送的数据信息
            SyncDataDTO syncDataDTO = new SyncDataDTO();
            syncDataDTO.setTemplateType(VisualTemplateType.AFTER_TYPE.getType());
            syncDataDTO.setSysCode(supplierAfterList.get(0).getSysCode());

            supplierAfterMap.forEach((supplierId, supplierAfterNo) -> {
                //根据入驻商ID及发送类型校验是否同步数据
                if (checkSyncData(supplierId, VisualTemplateType.AFTER_TYPE.getType())) {
                    //设置需要发送的数据信息
                    syncDataDTO.setSupplierId(supplierId)
                            .setReqId(IdUtils.fastSimpleUUID())
                            .setDataId(supplierAfterNo);

                    //异步处理需要同步的入驻商售后数据
                    syncDataProducer.sendSupplierSyncDataAfterEvent(syncDataDTO);

                }
            });
        };
    }

    /**
     * 收款单同步消费者
     *
     * @return
     */
    @Bean
    public Consumer<SyncReceiptSendDTO> syncDataReceiptEvent() {
        return (data) -> {
            log.info("收到待处理的收款单同步信息：{} ", data);

            if (Objects.isNull(data)) {
                log.info("该收款单同步信息不存在，data:{}", data);
                return;
            }

            //单据类型
            String sheetType = data.getSheetType();

            //销售入驻商订单号/售后入驻商订单
            String supplierSheetNo = data.getSupplierSheetNo();

            //平台商id
            Long sysCode = null;

            //入驻商编号
            Long supplierId = null;

            //销售订单号/售后订单号
            String sheetNo = null;

            //单据类型
            String transNo = null;

            //获取订单信息
            if (SheetTypeConstants.XSS.equals(sheetType)) {
                //查询入驻商订单信息
                OrderOpenDTO orderOpenDTO = supplierOrderApi.getSupplierOrderBySupplierOrderNo(supplierSheetNo).getCheckedData();
                if (ToolUtil.isEmpty(orderOpenDTO)) {
                    log.info("推送收款单未查询到入驻商销售订单信息，data:{}", data);
                    return;
                }
/*                if(!PayStateEnum.PAY_ALREADY_HDFK.getCode().equals(orderOpenDTO.getPayState())){
                    log.info("推送收款单:查询到的入驻商销售订单信息的支付状态不是4-货到付款已收款，data:{},当前支付状态:{}",data,orderOpenDTO.getPayState());
                    return;
                }*/

                //设置推送信息
                sysCode = orderOpenDTO.getSysCode();
                supplierId = orderOpenDTO.getSupplierId();

                //如果是货到收款单 需要校验是否已完成收款
                TrdOrder order = orderApi.getOrderByOrderId(orderOpenDTO.getOrderId());
                if (OrderPayWayEnum.HDFK.getPayWay().equals(order.getPayWay())) {
                    //校验订单数据  如果是销售订单 需要校验该订单是否已经全部收款
                    List<OrderDetailOpenDTO> orderDetailOpenDTOList = supplierOrderApi.getSupplierOrderDtlBySupplierOrderNo(supplierSheetNo).getCheckedData();

                    //筛选订单详情是已收款或已取消的订单
                    List<OrderDetailOpenDTO> receiptList = orderDetailOpenDTOList.stream().filter(
                                    x -> DeliveryStatusEnum.CANCEL.getCode().equals(x.getDeliveryState())
                                            || Objects.nonNull(x.getHdfkPayId()))
                            .collect(Collectors.toList());

                    if (orderDetailOpenDTOList.size() != receiptList.size()) {
                        log.info("推送收款单:该入驻商销售订单货到收款未完全收款，暂不推送，data:{}", data);
                        return;
                    }
                }

                //设置订单号
                sheetNo = orderOpenDTO.getOrderNo();
                transNo = SheetTypeConstants.XSS;
            } else {
                //查询入驻商订单信息
                AfterPushDTO afterPushDTO = supplierAfterApi.getSupAfterBySupAfterNo(supplierSheetNo).getCheckedData();
                if (ToolUtil.isEmpty(afterPushDTO)) {
                    log.info("推送收款单未查询到入驻商售后订单信息，data:{}", data);
                    return;
                }

                //设置推送信息
                sysCode = afterPushDTO.getSysCode();
                supplierId = afterPushDTO.getSupplierId();

                //设置订单号
                sheetNo = afterPushDTO.getAfterNo();
                transNo = afterPushDTO.getTransNo();
            }

            //设置收款单的收款类型
            String receilptType = setVisualReceiptType(supplierSheetNo, sheetNo, transNo, data.getIsProceeds());
            //校验收款单类型
            if (ToolUtil.isEmpty(receilptType)) {
                log.info("推送收款单未匹配到收款单类型，data:{}", data);
                return;
            }

            //根据入驻商ID及发送类型校验是否同步数据
            if (checkSyncData(supplierId, VisualTemplateType.RECEIPT_TYPE.getType(), receilptType)) {
                //组装需要发送的数据信息
                SyncDataDTO syncDataDTO = new SyncDataDTO();
                syncDataDTO.setTemplateType(VisualTemplateType.RECEIPT_TYPE.getType())
                        .setSysCode(sysCode)
                        .setSheetType(data.getSheetType())
                        .setSupplierId(supplierId)
                        .setReqId(IdUtils.fastSimpleUUID())
                        .setDataId(supplierSheetNo)
                        .setIsProceeds(data.getIsProceeds())
                        .setReceilptType(receilptType);

                //异步处理需要同步的入驻商收款单数据
                syncDataProducer.sendSupplierSyncDataReceiptEvent(syncDataDTO);
            }

        };
    }


    /**
     * 门店储值充值、提现同步信息消费者
     * @return
     */
    @Bean
    public Consumer<List<Long>> syncDataBranchValueInfoEvent() {
        return (data) -> {
            log.info("收到待处理的门店储值充值、提现同步信息：{} ", data);

            //由于MQ消息接收后 data中的值变成了Integer
            List<Long> accountFlowIds = JSONArray.parseArray(JSON.toJSONString(data),Long.class);

            //获取账号资金流水信息
            List<AccAccountFlowDTO> accountFlowList = accountFlowApi.getAccAccountFlowListByIds(accountFlowIds).getCheckedData();
            if(ToolUtil.isEmpty(accountFlowList)){
                log.info("门店储值充值、提现同步信息未查询到对应的资金流水信息，data:{}", data);
                return;
            }

            //按该门店所在区域 推送门店信息给该区域下各个入驻商
            //获取门店信息
            BranchDTO branchDTO = sysCacheService.getBranchDTO(accountFlowList.get(0).getMerchantId());
            if(ToolUtil.isEmpty(branchDTO)){
                log.info("门店储值充值、提现同步信息根据流水信息未查询到对应的门店信息，accountFlowList:{}", accountFlowList);
            }

            //根据门店信息所关联的城市获取管理范围的入驻商
            List<SupplierAreaDTO> supplierAreaDTOS = sysSupplierAreaService.getSupplierAreaByAreaId(branchDTO.getAreaId());
            if (!supplierAreaDTOS.isEmpty()) {
                //组装需要发送的数据信息
                SyncDataDTO syncDataDTO = new SyncDataDTO();
                syncDataDTO.setDataId(String.valueOf(branchDTO.getBranchId()))
                        .setTemplateType(VisualTemplateType.BRANCH_VALUE_INFO_TYPE.getType())
                        .setSysCode(branchDTO.getSysCode())
                        .setJson(JSON.toJSONString(data));
                Set<Long> supplierIds = supplierAreaDTOS.stream().map(SupplierAreaDTO::getSupplierId).collect(Collectors.toSet());
                supplierIds.forEach(supplierId -> {
                    //根据入驻商ID及发送类型校验是否同步数据
                    if (checkSyncData(supplierId, VisualTemplateType.BRANCH_VALUE_INFO_TYPE.getType())) {
                        //设置需要发送的数据信息
                        syncDataDTO.setSupplierId(supplierId)
                                .setReqId(IdUtils.fastSimpleUUID());
                        //异步处理需要同步的门店数据
                        syncDataProducer.sendSupplierSyncDataBranchValueInfoEvent(syncDataDTO);
                    }
                });
            }
        };
    }

    /**
     * 校验是否同步数据
     *
     * @param supplierId
     * @param templateType
     * @return
     */
    private Boolean checkSyncData(Long supplierId, Integer templateType) {
        VisualSettingDetailDto visualSettingDetailDto = sysCacheService.getVisualDetailBySupplier(supplierId + StringPool.COLON + templateType);
        return ToolUtil.isNotEmpty(visualSettingDetailDto)
                && ToolUtil.isNotEmpty(visualSettingDetailDto.getVisualDetailId());
    }

    /**
     * 收款单校验是否同步数据
     *
     * @param supplierId
     * @param templateType
     * @param visualReceiptType
     * @return
     */
    private Boolean checkSyncData(Long supplierId, Integer templateType, String visualReceiptType) {
        VisualSettingDetailDto visualSettingDetailDto = sysCacheService.getVisualDetailBySupplier(supplierId + StringPool.COLON + templateType);
        return ToolUtil.isNotEmpty(visualSettingDetailDto)
                && ToolUtil.isNotEmpty(visualSettingDetailDto.getVisualDetailId())
                && ToolUtil.isNotEmpty(visualSettingDetailDto.getVisualReceiptType())
                && visualSettingDetailDto.getVisualReceiptType().contains(visualReceiptType);
    }


    /**
     * 设置收款单的收款类型
     *
     * @param supplierSheetNo 入驻商订单号（销售/售后）
     * @param sheetNo         订单号（销售/售后）
     * @param transNo         单据类型
     * @param isProceeds      是否收款（0否 1是）
     */
    private String setVisualReceiptType(String supplierSheetNo, String sheetNo, String transNo, Integer isProceeds) {
        //获取支付方式
        String payWay = null;

        //需要获取订单信息 来匹配是否支付方式
        if (SheetTypeConstants.XSS.equals(transNo)) {
            TrdOrder trdOrder = orderApi.getOrderByOrderNo(sheetNo).getCheckedData();
            payWay = trdOrder.getPayWay();
        } else {
            TrdAfter trdAfter = afterApi.getAfterByAfterNo(sheetNo).getCheckedData();
            payWay = trdAfter.getPayWay();

        }
        return getVisualReceiptType(payWay, transNo, isProceeds);
    }

    /**
     * 入驻商同步数据 -- 推送门店 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SyncDataDTO> supplierSyncDataBranchEvent(){
        return (data -> {
            log.info("收到入驻商同步推送数据--门店数据信息:{}",data);
            //将需要同步的数据发送给指挥者
            syncBranch.invoke(data);
        });
    }

    /**
     * 入驻商同步数据 -- 推送销售订单消息入口  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SyncDataDTO> supplierSyncDataOrderEvent(){
        return (data -> {
            log.info("收到入驻商同步推送数据--销售订单数据信息:{}",data);
            //将需要同步的数据发送给指挥者
            try {
                syncOrder.invoke(data);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    /**
     * 入驻商同步数据 -- 推送售后订单消息入口  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SyncDataDTO> supplierSyncDataAfterEvent(){
        return (data -> {
            log.info("收到入驻商同步推送数据--售后订单数据信息:{}",data);
            //将需要同步的数据发送给指挥者
            syncAfter.invoke(data);
        });
    }

    /**
     * 入驻商同步数据 -- 推送收款单消息入口  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SyncDataDTO> supplierSyncDataReceiptEvent(){
        return (data -> {
            log.info("收到入驻商同步推送数据--收款单数据信息:{}",data);
            //将需要同步的数据发送给指挥者
            syncReceipt.invoke(data);
        });
    }


    /**
    * 入驻商同步数据 -- 推送门店储值充值、提现信息消息入口  消费者
    * @date 2025/3/10 14:48
    * <AUTHOR>
    */
    @Bean
    public Consumer<SyncDataDTO> supplierSyncDataBranchValueInfoEvent(){
        return (data -> {
            log.info("收到入驻商同步数据 -- 推送门店储值充值、提现信息:{}",data);
            //将需要同步的数据发送给指挥者
            syncBranchValueInfo.invoke(data);
        });
    }
}
