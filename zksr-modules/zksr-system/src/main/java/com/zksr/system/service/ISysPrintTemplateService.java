package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysPrintTemplate;
import com.zksr.system.controller.print.vo.SysPrintTemplatePageReqVO;
import com.zksr.system.controller.print.vo.SysPrintTemplateSaveReqVO;

/**
 * 打印模版Service接口
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
public interface ISysPrintTemplateService {

    /**
     * 新增打印模版
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysPrintTemplate(@Valid SysPrintTemplateSaveReqVO createReqVO);

    /**
     * 修改打印模版
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysPrintTemplate(@Valid SysPrintTemplateSaveReqVO updateReqVO);

    /**
     * 删除打印模版
     *
     * @param printTemplateId 主键
     */
    public void deleteSysPrintTemplate(Long printTemplateId);

    /**
     * 批量删除打印模版
     *
     * @param printTemplateIds 需要删除的打印模版主键集合
     * @return 结果
     */
    public void deleteSysPrintTemplateByPrintTemplateIds(Long[] printTemplateIds);

    /**
     * 获得打印模版
     *
     * @param printTemplateId 主键
     * @return 打印模版
     */
    public SysPrintTemplate getSysPrintTemplate(Long printTemplateId);

    /**
     * 获得打印模版分页
     *
     * @param pageReqVO 分页查询
     * @return 打印模版分页
     */
    PageResult<SysPrintTemplate> getSysPrintTemplatePage(SysPrintTemplatePageReqVO pageReqVO);

}
