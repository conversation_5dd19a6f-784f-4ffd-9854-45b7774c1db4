package com.zksr.system.controller;

import com.alibaba.fastjson.JSON;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.file.FileTypeUtils;
import com.zksr.common.core.utils.file.MimeTypeUtils;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.service.TokenService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.RemoteFileService;
import com.zksr.system.api.domain.SysFile;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.service.ISysSupplierService;
import com.zksr.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;

/**
 * 个人信息 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user/profile")
@Api(tags = "OPENAPI - 个人信息 业务处理接口")
public class SysProfileController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private ISysSupplierService supplierService;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;


    /**
    * @Description: 入驻商获取个人信息
    * @Param:
    * @return: AjaxResult
    * @Author: liuxingyu
    * @Date: 2024/3/26 16:31
    */
    @GetMapping("/getSupplierInfo")
    @ApiOperation(value = "入驻商获取个人信息")
    public AjaxResult getSupplierInfo(){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = userService.selectUserByUserName(loginUser.getUsername());
        SysSupplierRespVO supplierRespVO = supplierService.getSysSupplier(user.getSupplierId());
        AjaxResult ajax = AjaxResult.success(user);
        ajax.put("roleGroup", userService.selectUserRoleGroup(loginUser.getUsername()));
        ajax.put("postGroup", userService.selectUserPostGroup(loginUser.getUsername()));
        ajax.put("supplier", supplierRespVO);
        return ajax;
    }


    /**
     * 个人信息
     */
    @ApiOperation(value = "个人信息")
    @GetMapping
    public AjaxResult profile()
    {
        String username = SecurityUtils.getUsername();
        logger.info("获取到的用户账号为:"+ username);
        SysUser user = userService.selectUserByUserName(username);
        AjaxResult ajax = AjaxResult.success(user);
        ajax.put("roleGroup", userService.selectUserRoleGroup(username));
        ajax.put("postGroup", userService.selectUserPostGroup(username));
        logger.info(String.format("用户账号%s的响应结果为%s", username, JSON.toJSONString(ajax)));
        return ajax;
    }

    /**
     * 修改用户
     */
    @ApiOperation(value = "修改用户")
    @Log(title = "修改用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysUser user)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser currentUser = loginUser.getSysUser();
        currentUser.setPhonenumber(user.getPhonenumber());
        currentUser.setVerificationCode(user.getVerificationCode());
        //更改头像
        if (org.apache.commons.lang3.StringUtils.isNotBlank(user.getAvatar())){
            currentUser.setAvatar(user.getAvatar());
            //更新缓存
            loginUser.getSysUser().setAvatar(user.getAvatar());
        }
        if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(currentUser))
        {
            return error(String.format("修改用户%s失败,手机号码%s已存在", loginUser.getUsername(), user.getPhonenumber()));
        }
        if(!saasAuthSwitch){ //安得的B2B不更新昵称、邮箱、性别
            currentUser.setNickName(user.getNickName());
            currentUser.setEmail(user.getEmail());
            currentUser.setSex(user.getSex());
            if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser))
            {
                return error(String.format("修改用户%s失败,邮箱账号%s已存在", loginUser.getUsername(), user.getEmail()));
            }
        }

        if (userService.updateUserProfile(currentUser) > 0)
        {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

//    /**
//     * 重置密码
//     */
//    @ApiOperation(value = "重置密码-saas用户")
//    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
//    @PutMapping("/updateSaasUserPwd")
//    public AjaxResult updateSaasUserPwd(@Validated @RequestBody PasswordForget passwordForget)
//    {
//        userService.updateSaasUserPwd(passwordForget);
//        return success("修改密码成功");
//    }

    /**
     * 重置密码
     */
    @ApiOperation(value = "重置密码")
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping("/updatePwd")
    public AjaxResult updatePwd(String oldPassword, String newPassword)
    {
        String username = SecurityUtils.getUsername();
        SysUser user = userService.selectUserByUserName(username);
        String password = user.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password))
        {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password))
        {
            return error("新密码不能与旧密码相同");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (userService.resetUserPwd(username, newPassword) > 0)
        {
            // 更新缓存用户密码
            LoginUser loginUser = SecurityUtils.getLoginUser();
            loginUser.getSysUser().setPassword(newPassword);
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }

    /**
     * 头像上传
     */
    @ApiOperation(value = "头像上传")
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file)
    {
        if (!file.isEmpty())
        {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            String extension = FileTypeUtils.getExtension(file);
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION))
            {
                return error("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
            }
            R<SysFile> fileResult = remoteFileService.upload(file);
            if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData()))
            {
                return error("文件服务异常，请联系管理员");
            }
            String url = fileResult.getData().getUrl();
            if (userService.updateUserAvatar(loginUser.getUsername(), url))
            {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", url);
                // 更新缓存用户头像
                loginUser.getSysUser().setAvatar(url);
                tokenService.setLoginUser(loginUser);
                return ajax;
            }
        }
        return error("上传图片异常，请联系管理员");
    }
}
