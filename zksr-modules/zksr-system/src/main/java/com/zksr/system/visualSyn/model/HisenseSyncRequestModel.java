package com.zksr.system.visualSyn.model;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.enums.HisenseApiEnum;
import com.zksr.common.core.enums.request.VisualTemplateType;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import com.zksr.system.config.HisenseApiConfig;
import com.zksr.system.hisense.utils.HmacSignatureUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 对接海信业务抽象子类
 * @param <SYNCDTO>
 */
@Slf4j
public class HisenseSyncRequestModel<SYNCDTO> extends SyncRequestModel<SYNCDTO> {

    private final HisenseApiConfig.UriConfig config;

    public HisenseSyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMasterDto, VisualSettingDetailDto visualDetailDto, VisualSettingTemplateDto visualTemplateDto, OpensourceDto opensourceDto, SYNCDTO syncdto) {
        super(data, visualMasterDto, visualDetailDto, visualTemplateDto, opensourceDto, syncdto);

        Map<String, HisenseApiConfig.UriConfig> uris = SpringUtils.getBean(HisenseApiConfig.class).getUris();
        if (Long.valueOf(VisualTemplateType.ORDER_TYPE.getType()).equals(visualDetailDto.getTemplateType())) {
            config = uris.get(HisenseApiEnum.PUSH_ORDER.getCode());
        } else if (Long.valueOf(VisualTemplateType.AFTER_TYPE.getType()).equals(visualDetailDto.getTemplateType())) {
            config = uris.get(HisenseApiEnum.PUSH_AFTER.getCode());
        } else {
            config = null;
        }
    }

    @Override
    public String assembleHeader() {

        // 获取当前GMT时间
        String gmtDate = HmacSignatureUtil.getCurrentGMTDate();

        // 需要签名的请求头
        Map<String, String> signedHeaders = new LinkedHashMap<>();

        Map<String, String> queryParams = assembleQueryParms();

        // 生成签名
        String signature = HmacSignatureUtil.generateSignature(
                config.getAppKey(), "POST", config.getUri(), queryParams, config.getAppId(), gmtDate, signedHeaders);

        // 构建请求头
        Map<String, Object> headers = new HashMap<>();
        headers.put("X-HMAC-SIGNATURE", signature);
        headers.put("X-HMAC-ALGORITHM", "hmac-sha256");
        headers.put("X-HMAC-ACCESS-KEY", config.getAppId());
        headers.put("Date", gmtDate);
        headers.put("Content-Type", "application/json");
        return JSON.toJSONString(headers);
    }

    @Override
    public Map<String, String> assembleQueryParms() {
        Map<String, String> queryParams = Maps.newHashMapWithExpectedSize(2);
        queryParams.put("app_id", config.getAppId());
        queryParams.put("app_key", config.getAppKey());
        return queryParams;
    }
}
