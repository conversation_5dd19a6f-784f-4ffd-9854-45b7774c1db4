package com.zksr.system.mq;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.rocketmq.constant.MessageConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.messaging.support.MessageBuilder;

import java.util.HashMap;
import java.util.Map;

/**
* 第三方系统同步数据消息队列消费者
* @date 2024/7/12 10:10
* <AUTHOR>
*/
@Slf4j
@Configuration
public class SyncDataProducer {

    @Autowired
    private StreamBridge streamBridge;

    /**
     * 入驻商同步数据 -- 推送门店消息入口
     * 消费 {@link SyncDataConsumer#supplierSyncDataBranchEvent()}
     * @param syncDataDTO
     */
    public void sendSupplierSyncDataBranchEvent(SyncDataDTO syncDataDTO){
        log.info("入驻商同步数据 -- 推送门店数据:{}",syncDataDTO.toString());
        streamBridge.send(
                MessageConstant.SUPPLIER_SYNC_DATA_BRANCH_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(syncDataDTO).build());

    }


    /**
     * 入驻商同步数据 -- 推送销售订单消息入口
     * {@link SyncDataConsumer#supplierSyncDataOrderEvent()}
     * @param syncDataDTO
     */
    public void sendSupplierSyncDataOrderEvent(SyncDataDTO syncDataDTO){
        log.info("入驻商同步数据 -- 推送销售订单数据:{}",syncDataDTO.toString());
        Map<String, Object> headers = new HashMap<>();
        headers.put("for", "这是一个请求头～");
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, syncDataDTO.getPropertyDelayTimeLevel());
        Message<SyncDataDTO> msg = new GenericMessage(syncDataDTO, headers);
        streamBridge.send(
                MessageConstant.SUPPLIER_SYNC_DATA_ORDER_TOPIC_OUT_PUT,
                msg);
    }


    /**
     * 入驻商同步数据 -- 推送售后订单消息入口
     * {@link SyncDataConsumer#supplierSyncDataAfterEvent()}
     * @param syncDataDTO
     */
    public void sendSupplierSyncDataAfterEvent(SyncDataDTO syncDataDTO){
        log.info("入驻商同步数据 -- 推送售后订单数据:{}",syncDataDTO.toString());
        streamBridge.send(
                MessageConstant.SUPPLIER_SYNC_DATA_AFTER_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(syncDataDTO).build());

    }

    /**
     * 入驻商同步数据 -- 推送收款单消息入口
     * {@link SyncDataConsumer#supplierSyncDataReceiptEvent()}
     * @param syncDataDTO
     */
    public void sendSupplierSyncDataReceiptEvent(SyncDataDTO syncDataDTO){
        log.info("入驻商同步数据 -- 推送收款单数据:{}",syncDataDTO.toString());
        streamBridge.send(
                MessageConstant.SUPPLIER_SYNC_DATA_RECEIPT_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(syncDataDTO).build());

    }

    /**
     * 入驻商同步数据 -- 推送门店储值充值、提现信息消息入口
     * {@link SyncDataConsumer#supplierSyncDataBranchValueInfoEvent()}
     * @param syncDataDTO
     */
    public void sendSupplierSyncDataBranchValueInfoEvent(SyncDataDTO syncDataDTO){
        log.info("入驻商同步数据 -- 推送门店储值充值、提现信息信息数据:{}",syncDataDTO.toString());
        streamBridge.send(
                MessageConstant.SUPPLIER_SYNC_DATA_BRANCH_VALUE_INFO_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(syncDataDTO).build());

    }


}
