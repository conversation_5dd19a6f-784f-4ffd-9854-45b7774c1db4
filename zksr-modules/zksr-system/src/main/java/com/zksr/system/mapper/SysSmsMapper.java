package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysSms;
import com.zksr.system.controller.sms.vo.SysSmsPageReqVO;


/**
 * 短信消息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Mapper
public interface SysSmsMapper extends BaseMapperX<SysSms> {
    default PageResult<SysSms> selectPage(SysSmsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysSms>()
                    .eqIfPresent(SysSms::getSmsId, reqVO.getSmsId())
                    .eqIfPresent(SysSms::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(SysSms::getMobile, reqVO.getMobile())
                    .eqIfPresent(SysSms::getScene, reqVO.getScene())
                    .eqIfPresent(SysSms::getUseTime, reqVO.getUseTime())
                    .eqIfPresent(SysSms::getMsg, reqVO.getMsg())
                .orderByDesc(SysSms::getSmsId));
    }
}
