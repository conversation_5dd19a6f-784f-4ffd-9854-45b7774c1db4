package com.zksr.system.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.system.api.opensource.dto.OpenlimitDto;
import com.zksr.system.controller.openability.vo.SysOpenabilityPageReqVO;
import com.zksr.system.controller.openability.vo.SysOpenabilityReqVO;
import com.zksr.system.domain.SysOpenability;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;


/**
 * 开放能力Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Mapper
public interface SysOpenabilityMapper extends BaseMapperX<SysOpenability> {
    default PageResult<SysOpenability> selectPage(SysOpenabilityPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysOpenability>()
                .eqIfPresent(SysOpenability::getOpenabilityId, reqVO.getOpenabilityId())
                .eqIfPresent(SysOpenability::getPid, reqVO.getPid())
                .eqIfPresent(SysOpenability::getMerchantType, reqVO.getMerchantType())
                .likeIfPresent(SysOpenability::getAbilityName, reqVO.getAbilityName())
                .eqIfPresent(SysOpenability::getAbilityKey, reqVO.getAbilityKey())
                .eqIfPresent(SysOpenability::getStatus, reqVO.getStatus())
                .eqIfPresent(SysOpenability::getRateLimit, reqVO.getRateLimit())
                .orderByDesc(SysOpenability::getOpenabilityId));
    }

    default List<SysOpenability> selectList(SysOpenabilityReqVO reqVO) {
        return selectList(new LambdaQueryWrapper<SysOpenability>()
                .like(ObjectUtil.isNotEmpty(reqVO.getAbilityName()), SysOpenability::getAbilityName, reqVO.getAbilityName())
                .eq(ObjectUtil.isNotEmpty(reqVO.getStatus()), SysOpenability::getStatus, reqVO.getStatus()));
    }

    Set<String> getMerchantAbilityKeys(@Param("merchantType") String merchantType);

    Set<String> getMerchantAbilityKeysByList(@Param("typeList") String[] typeList);

    Set<OpenlimitDto> getOpenlimitList(@Param("opensourceId") Long opensourceId);

    Set<OpenlimitDto> getOpenlimitListByList(@Param("typeList") String[] typeList);

    /**
     * 是否存在菜单子节点
     *
     * @param openabilityId 开放能力id
     * @return 结果
     */
    int hasChildByOpenabilityId(Long openabilityId);
}
