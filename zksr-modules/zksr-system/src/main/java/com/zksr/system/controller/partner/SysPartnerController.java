package com.zksr.system.controller.partner;

import cn.hutool.core.collection.ListUtil;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRespVO;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.JwtUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.auth.AuthUtil;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.controller.partner.vo.SysPartnerAccountRespVO;
import com.zksr.system.api.partner.vo.SysPartnerPageReqVO;
import com.zksr.system.api.partner.vo.SysPartnerRespVO;
import com.zksr.system.controller.partner.vo.SysPartnerSaveReqVO;
import com.zksr.system.convert.partner.PartnerConvert;
import com.zksr.system.domain.SysPartner;
import com.zksr.system.service.ISysPartnerService;
import com.zksr.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商信息Controller
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Api(tags = "管理后台 - 平台商信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/partner")
public class SysPartnerController {
    @Autowired
    private ISysPartnerService sysPartnerService;

    @Autowired
    private ISysUserService sysUserService;

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private AccountApi accountApi;

    /**
     * 新增平台商信息
     */
    @ApiOperation(value = "新增平台商信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysPartnerSaveReqVO createReqVO) {
        Long res = sysPartnerService.insertSysPartner(createReqVO);
        sysPartnerService.reloadPartnerDtoCache(res);
        return success(res);
    }

    /**
     * 修改平台商信息
     */
    @ApiOperation(value = "修改平台商信息", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysPartnerSaveReqVO updateReqVO) {
        sysPartnerService.updateSysPartner(updateReqVO);
        sysPartnerService.reloadPartnerDtoCache(updateReqVO.getSysCode());
        return success(true);
    }

    /**
     * 删除平台商信息
     */
    @ApiOperation(value = "删除平台商信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        sysPartnerService.deleteSysPartnerBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取平台商信息详细信息
     */
    @ApiOperation(value = "获得平台商信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{sysCode}")
    public CommonResult<SysPartnerRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        SysPartner sysPartner = sysPartnerService.getSysPartner(sysCode);
        SysUser sysUser = sysUserService.selectUserById(sysPartner.getPartnerUserId());
        sysPartner.setPartnerAccount(sysUser.getUserName());
        // 转换实体
        SysPartnerRespVO respVO = PartnerConvert.INSTANCE.convert(sysPartner);
        // 获取进件信息
        List<AccPlatformMerchantRespVO> merchantRespVOS = platformMerchantApi.getMerchantList(
                AccPlatformMerchantPageReqVO.builder()
                        .sysCode(sysPartner.getSysCode())
                        .merchantTypes(ListUtil.toList(MerchantTypeEnum.PARTNER.getType(), MerchantTypeEnum.SOFTWARE.getType(), MerchantTypeEnum.PARTNER_COLONEL.getType()))
                        .build()
        ).getCheckedData();
        respVO.setPlatformSimpleBindList(BeanUtils.toBean(merchantRespVOS, PlatformSimpleBindVO.class));
        return success(respVO);
    }

    /**
     * 分页查询平台商信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商信息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysPartnerRespVO>> getPage(@Valid SysPartnerPageReqVO pageReqVO) {
        return success(sysPartnerService.getSysPartnerList(pageReqVO));
    }

    /**
     * 停用平台商
     */
    @ApiOperation(value = "停用平台商", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "平台商", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public CommonResult<Boolean> disable(@ApiParam(name = "sysCode", value = "平台商ID", required = true) @RequestParam("sysCode") Long sysCode) {
        sysPartnerService.disable(sysCode);
        sysPartnerService.reloadPartnerDtoCache(sysCode);
        return success(true);
    }

    /**
     * 启用平台商
     */
    @ApiOperation(value = "启用平台商", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "平台商", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public CommonResult<Boolean> enable(@ApiParam(name = "sysCode", value = "平台商ID", required = true) @RequestParam("sysCode") Long sysCode) {
        sysPartnerService.enable(sysCode);
        sysPartnerService.reloadPartnerDtoCache(sysCode);
        return success(true);
    }

    /**
     * 分页查询门店商账户
     */
    @GetMapping("/accountList")
    @ApiOperation(value = "获得平台商账户分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.ACCOUNT_LIST)
    @RequiresPermissions(Permissions.ACCOUNT_LIST)
    public CommonResult<PageResult<SysPartnerAccountRespVO>> getAccountPage(@Valid SysPartnerPageReqVO pageReqVO) {
        PageResult<SysPartner> pageResult = sysPartnerService.getSysPartnerPage(pageReqVO);
        // 转换成账户列表
        PageResult<SysPartnerAccountRespVO> accountResult = PartnerConvert.INSTANCE.convert(pageResult);
        // 获取账户余额
        accountResult.getList().forEach(item -> {
            AccAccountDTO account = accountApi.getAccount(item.getSysCode(), item.getSysCode(), MerchantTypeEnum.PARTNER.getType()).getCheckedData();
            // 写入金额
            PartnerConvert.INSTANCE.convert(item, account);
        });
        return success(accountResult);
    }

     /**
     * 获取当前平台商的软件商分润比例
     */
    @ApiOperation(value = "获得平台商信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @GetMapping(value = "/getSoftwareProportion")
    public CommonResult<String> getSoftwareProportion() {
        SysPartner sysPartner = sysPartnerService.getSysPartner(SecurityUtils.getLoginUser().getSysCode());
        //历史代码没判空处理
        return success((null == sysPartner || null == sysPartner.getSoftwareRate()) ? "" : sysPartner.getSoftwareRate().toString());
    }


    /**
     * 修改平台商信息
     */
    @ApiOperation(value = "修改平台商密码", httpMethod = HttpMethod.PUT, notes = "system:partner:edit-password")
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "修改平台商密码", businessType = BusinessType.UPDATE)
    @PutMapping("/editPartnerPassword")
    public CommonResult<Boolean> editPartnerPassword(@Valid @RequestBody SysPartnerSaveReqVO updateReqVO) {
        sysPartnerService.updateSysPartnerPassword(updateReqVO);
        sysPartnerService.reloadPartnerDtoCache(updateReqVO.getSysCode());
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:partner:add";
        /** 编辑 */
        public static final String EDIT = "system:partner:edit";
        /** 删除 */
        public static final String DELETE = "system:partner:remove";
        /** 列表 */
        public static final String LIST = "system:partner:list";
        /** 查询 */
        public static final String GET = "system:partner:query";
        /** 停用 */
        public static final String DISABLE = "system:partner:disable";
        /** 启用 */
        public static final String ENABLE = "system:partner:enable";
        /** 账户列表 */
        public static final String ACCOUNT_LIST = "system:partner:accountList";
    }
}
