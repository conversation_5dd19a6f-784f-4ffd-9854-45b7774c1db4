package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.channel.vo.SysChannelPageReqVO;
import com.zksr.system.controller.channel.vo.SysChannelSaveReqVO;
import com.zksr.system.controller.channel.vo.SysChannelSelectedRespVO;
import com.zksr.system.domain.SysChannel;

import javax.validation.Valid;
import java.util.List;

/**
 * 渠道信息Service接口
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
public interface ISysChannelService {

    /**
     * 新增渠道信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysChannel(@Valid SysChannelSaveReqVO createReqVO);

    /**
     * 修改渠道信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysChannel(@Valid SysChannelSaveReqVO updateReqVO);

    /**
     * 删除渠道信息
     *
     * @param channelId 渠道id
     */
    public void deleteSysChannel(Long channelId);

    /**
     * 批量删除渠道信息
     *
     * @param channelIds 需要删除的渠道信息主键集合
     * @return 结果
     */
    public void deleteSysChannelByChannelIds(Long[] channelIds);

    /**
     * 获得渠道信息
     *
     * @param channelId 渠道id
     * @return 渠道信息
     */
    public SysChannel getSysChannel(Long channelId);

    /**
     * 获得渠道信息分页
     *
     * @param pageReqVO 分页查询
     * @return 渠道信息分页
     */
    PageResult<SysChannel> getSysChannelPage(SysChannelPageReqVO pageReqVO);

    /**
    * @Description: 获取所有渠道信息
    * @Author: liuxingyu
    * @Date: 2024/3/22 14:40
    */
    List<SysChannel> getChannelList();

    /**
     * 获取选中渠道信息
     * @param channelIds    渠道ID集合
     * @return
     */
    List<SysChannelSelectedRespVO> getSelectedSysChannel(List<Long> channelIds);

    /**
     * @Description: 获取平台下所有渠道信息
     * @Author: chenmingqing
     * @Date: 2025/3/01 14:40
     */
    List<SysChannel> getChannelListBySysCode(Long sysCode, String channelName);
}
