package com.zksr.system.hisense;


import com.zksr.common.core.enums.HisenseApiEnum;
import com.zksr.system.api.openapi.dto.hisense.*;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
public class EcpxApiClient extends BaseApiClient {

//    public EcpxApiClient(String baseUrl, String appId, String appKey) {
//        super(baseUrl, appId, appKey);
//    }

    /**
     * 订单推送接口
     */
    public OrderPushResponse pushOrder(OrderPushRequest request) throws IOException {
        return postWithSignature(HisenseApiEnum.PUSH_ORDER, request, OrderPushResponse.class);
    }

    /**
     * 订单取消接口
     */
    public OrderCancelResponse cancelOrder(OrderCancelRequest request) throws IOException {
        return postWithSignature(HisenseApiEnum.CANCEL_ORDER, request, OrderCancelResponse.class);
    }

    /**
     * 修改开票信息接口
     */
    public InvoiceUpdateResponse updateInvoice(InvoiceUpdateRequest request) throws IOException {
        return postWithSignature(HisenseApiEnum.UPDATE_INVOICE, request, InvoiceUpdateResponse.class);
    }

    /**
     * 售后退货单接口
     */
    public ReturnOrderResponse createReturnOrder(ReturnOrderRequest request) throws IOException {
        return postWithSignature(HisenseApiEnum.PUSH_AFTER, request, ReturnOrderResponse.class);
    }
}
