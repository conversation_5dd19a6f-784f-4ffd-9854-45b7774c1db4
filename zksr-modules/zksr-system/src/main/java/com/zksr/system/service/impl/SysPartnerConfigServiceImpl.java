package com.zksr.system.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.partnerConfig.dto.*;
import com.zksr.system.api.partnerConfig.enums.PartnerConfigEnum;
import com.zksr.system.chin.partnerConfig.ConfigPartnerConfigPipeline;
import com.zksr.system.controller.partnerConfig.vo.*;
import com.zksr.system.domain.SysPartnerConfig;
import com.zksr.system.mapper.SysPartnerConfigMapper;
import com.zksr.system.service.ISysPartnerConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 平台商配置(由软件商设置)Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Slf4j
@Service
public class SysPartnerConfigServiceImpl implements ISysPartnerConfigService {
    @Autowired
    private SysPartnerConfigMapper sysPartnerConfigMapper;

    @Autowired
    private ConfigPartnerConfigPipeline partnerConfigPipeline;

    @Autowired
    private Cache<Long, AppletBaseConfigDTO> appletBaseCache;

    @Autowired
    private Cache<Long, HeLiBaoPayConfigDTO> heLiBaoPayCache;

    @Autowired
    private Cache<Long, PayConfigDTO> payConfigCache;

    @Autowired
    private Cache<Long, PayAccountConfigDTO> payAccountConfigCache;

    @Autowired
    private Cache<Long, CourierConfigDTO> courierConfigDTOCache;

    @Autowired
    private Cache<Long, DeviceSettingConfigDTO> deviceSettingConfigDTOCache;

    @Autowired
    private Cache<Long, SmsConfigDTO> smsConfigDTOCache;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private RedisService redisService;

    /**
     * 新增平台商配置(由软件商设置)
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysPartnerConfig(SysPartnerConfigSaveReqVO createReqVO) {
        // 插入
        SysPartnerConfig sysPartnerConfig = HutoolBeanUtils.toBean(createReqVO, SysPartnerConfig.class);
        sysPartnerConfigMapper.insert(sysPartnerConfig);
        // 返回
        return sysPartnerConfig.getPartnerConfigId();
    }

    /**
     * 修改平台商配置(由软件商设置)
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysPartnerConfig(SysPartnerConfigSaveReqVO updateReqVO) {
        sysPartnerConfigMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysPartnerConfig.class));
    }

    /**
     * 删除平台商配置(由软件商设置)
     *
     * @param partnerConfigId 平台商配置id
     */
    @Override
    public void deleteSysPartnerConfig(Long partnerConfigId) {
        // 删除
        sysPartnerConfigMapper.deleteById(partnerConfigId);
    }

    /**
     * 批量删除平台商配置(由软件商设置)
     *
     * @param partnerConfigIds 需要删除的平台商配置(由软件商设置)主键
     * @return 结果
     */
    @Override
    public void deleteSysPartnerConfigByPartnerConfigIds(Long[] partnerConfigIds) {
        for (Long partnerConfigId : partnerConfigIds) {
            this.deleteSysPartnerConfig(partnerConfigId);
        }
    }

    /**
     * 获得平台商配置(由软件商设置)
     *
     * @param partnerConfigId 平台商配置id
     * @return 平台商配置(由软件商设置)
     */
    @Override
    public SysPartnerConfig getSysPartnerConfig(Long partnerConfigId) {
        return sysPartnerConfigMapper.selectById(partnerConfigId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SysPartnerConfig> getSysPartnerConfigPage(SysPartnerConfigPageReqVO pageReqVO) {
        return sysPartnerConfigMapper.selectPage(pageReqVO);
    }

    /**
     * @Description: 保存配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/12 17:13
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean savePartnerConfig(SysPartnerConfigSaveReqVO createReqVO, Long sysCode) {
        //调用责任链保存方法
        partnerConfigPipeline.partnerSaveConfig(createReqVO, sysCode);
        return true;
    }

    /**
     * @Description: 根据平台编号获取所有配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/13 14:53
     */
    @Override
    public SysPartnerConfigRespVO getPartnerConfig(Long sysCode) {
        boolean isExist = true;
        //返回对象
        SysPartnerConfigRespVO sysPartnerConfigRespVO = new SysPartnerConfigRespVO();
        //校验app配置
        if (ObjectUtil.isNotNull(appletBaseCache.get(sysCode))) {
            sysPartnerConfigRespVO.setAppletBaseConfigDto(appletBaseCache.get(sysCode));
        } else {
            isExist = false;
        }
        //校验合利宝配置
        if (ObjectUtil.isNotNull(heLiBaoPayCache.get(sysCode))) {
            sysPartnerConfigRespVO.setHeLiBaoPayConfigDto(heLiBaoPayCache.get(sysCode));
        } else {
            isExist = false;
        }
        //校验支付配置
        if (ObjectUtil.isNotNull(payConfigCache.get(sysCode))) {
            sysPartnerConfigRespVO.setPayConfigDTO(payConfigCache.get(sysCode));
        } else {
            isExist = false;
        }
        //校验支付账号配置
        if (ObjectUtil.isNotNull(payAccountConfigCache.get(sysCode))) {
            sysPartnerConfigRespVO.setPayAccountConfigDTO(payAccountConfigCache.get(sysCode));
        } else {
            isExist = false;
        }
        //校验快递配置
        if (ObjectUtil.isNotNull(courierConfigDTOCache.get(sysCode))) {
            sysPartnerConfigRespVO.setCourierConfigDTO(courierConfigDTOCache.get(sysCode));
        } else {
            isExist = false;
        }
        //校验平台商设备配置
        if (ObjectUtil.isNotNull(deviceSettingConfigDTOCache.get(sysCode))) {
            sysPartnerConfigRespVO.setDeviceSettingConfigDTO(deviceSettingConfigDTOCache.get(sysCode));
        } else {
            isExist = false;
        }
        //校验平台商短信配置
        if (ObjectUtil.isNotNull(smsConfigDTOCache.get(sysCode))) {
            sysPartnerConfigRespVO.setSmsConfigDTO(smsConfigDTOCache.get(sysCode));
        } else {
            isExist = false;
        }
        //不为空直接返回
        if (isExist) {
            return sysPartnerConfigRespVO;
        }
        List<SysPartnerConfig> configList = sysPartnerConfigMapper.selectBySysCode(sysCode);
        return partnerConfigPipeline.partnerGetConfig(sysPartnerConfigRespVO, configList, sysCode);
    }

    /**
     * @Description: 获取配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:32
     */
    @Override
    public SysPartnerConfigRespVO getPartnerConfig(Long sysCode, Integer type) {
        //返回对象
        SysPartnerConfigRespVO sysPartnerConfigRespVO = new SysPartnerConfigRespVO();
        List<SysPartnerConfig> configList = sysPartnerConfigMapper.selectBySysCode(sysCode);
        return partnerConfigPipeline.partnerGetConfig(sysPartnerConfigRespVO, configList, sysCode);
    }

    /**
     * 获取平台商配置, 适应于1行记录, JSON配置
     * @param configKey 配置key
     * @param sysCode
     * @param classz
     * @return
     * @param <T>
     */
    @Override
    public <T> T getPartnerConfigObject(String configKey, Long sysCode, Class<T> classz) {
        SysPartnerConfig sysPartnerConfig = sysPartnerConfigMapper.selectByConfigKey(configKey, sysCode);
        if (Objects.isNull(sysPartnerConfig)) {
            return null;
        }
        return JSON.parseObject(sysPartnerConfig.getConfigValue(), classz);
    }

    @Override
    public void setPartnerConfigObject(String configKey, Long sysCode, Object obj, PartnerConfigEnum configEnum) {
        SysPartnerConfig sysPartnerConfig = sysPartnerConfigMapper.selectByConfigKey(configKey, sysCode);
        if (Objects.nonNull(sysPartnerConfig)) {
            sysPartnerConfig.setConfigValue(JSON.toJSONString(obj));
            sysPartnerConfigMapper.updateById(sysPartnerConfig);
        } else {
            sysPartnerConfig = new SysPartnerConfig();
            sysPartnerConfig.setSysCode(sysCode);
            sysPartnerConfig.setConfigKey(configKey);
            sysPartnerConfig.setConfigValue(JSON.toJSONString(obj));
            sysPartnerConfig.setConfigType(String.valueOf(configEnum.getType()));
        }
    }

    /**
     * 获取小程序b2b支付第三方平台服务商授权链接
     * @param appid appid
     * @return  授权url
     */
    @Override
    public String getAppWxB2bAuthUrl(String appid) {
        // 获取第三方应用, 服务商componentAccessToken
        // 如果系统配置了调用连接则使用调用连接获取
        String providerId = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_ID);
        String url = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_COMPONENT_ACCESS_TOKEN_URL);
        String componentAccessToken = HttpUtil.get(url);
        if (StringUtils.isEmpty(componentAccessToken)) {
            log.info("url={}", url);
            throw exception(SYS_PARTNER_CONFIG_COMPONENT_URL_GET_ERR);
        }

        // 获取preAuthCode
        String preAuthCodeUrl = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_PRE_AUTH_CODE_URL);
        String preAuthCode = HttpUtil.get(preAuthCodeUrl);
        if (StringUtils.isEmpty(preAuthCode)) {
            log.info("url={}", url);
            throw exception(SYS_PARTNER_CONFIG_COMPONENT_PRE_AUTH_CODE_ERR);
        }

        // 组装授权链接
        String authUrl = URLUtil.encodeAll(StringUtils.format(redisService.getCacheObject(CacheConstants.WECHAT_SERVICE_AUTH_URL), appid));
        StringBuilder param = new StringBuilder();
        param.append("component_appid=").append(providerId).append(StringPool.AMPERSAND);
        param.append("pre_auth_code=").append(preAuthCode).append(StringPool.AMPERSAND);
        param.append("redirect_uri=").append(authUrl).append(StringPool.AMPERSAND);
        param.append("auth_type=").append("2").append(StringPool.AMPERSAND);
        param.append("biz_appid=").append(appid);
        return StringUtils.format("https://open.weixin.qq.com/wxaopen/safe/bindcomponent?action=bindcomponent&no_scan=1&{}", param.toString());
    }

    @Override
    public SysSaveOpenB2bPayRespVO openWxB2bPay(SysSaveOpenB2bPayReqVO reqVO) {
        String appid = reqVO.getAppid();
        reqVO.setAppid(null);
        //  从中心服务器获取第三方应用小程序代理code
        String authCodeUrl = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_COMPONENT_AUTH_CODE_URL);
        if (StringUtils.isEmpty(authCodeUrl)) {
            throw exception(SYS_PARTNER_CONFIG_COMPONENT_AUTHORIZER_ACCESS_TOKEN_URL_ERR);
        }
        String authorizerAccessToken = HttpUtil.get(StringUtils.format(authCodeUrl, appid));
        if (StringUtils.isEmpty(authorizerAccessToken)) {
            // 让小程序重新授权
            return SysSaveOpenB2bPayRespVO.builder().state(202).build();
        }
        String retailbusinessapplyUrl = StringUtils.format("https://api.weixin.qq.com/wxa/business/retailbusinessapply?access_token={}", authorizerAccessToken);
        String requestData = JSON.toJSONString(reqVO);
        log.info("开通b2b门店助手, request={}", requestData);
        String result = HttpUtil.post(retailbusinessapplyUrl, requestData);
        log.info("开通b2b门店助手, response={}", result);
        JSONObject resObj = JSON.parseObject(result);
        if ("ok".equals(resObj.getString("errmsg"))) {
            // 更新门店开通状态
            WxB2bPayAppletStatusDTO wxB2bState = getAppWxB2bState(appid);
            wxB2bState.setOpenB2bPayStatus(StringPool.ONE);
            this.setPartnerConfigObject(
                    PartnerConfigEnum.WXB2B_PAY_STATE.getKey() + appid,
                    null,
                    wxB2bState,
                    PartnerConfigEnum.WXB2B_PAY_STATE
            );
        }
        return new SysSaveOpenB2bPayRespVO(resObj.getInteger("errcode"), resObj.getString("errmsg"));
    }

    @Override
    public SysSaveOpenB2bPayRespVO setB2bPayProfitRate(SysB2bPayProfitRateReqVO reqVO) {
        String appid = reqVO.getAppid();
        reqVO.setAppid(null);
        //  从中心服务器获取第三方应用小程序代理code
        String authCodeUrl = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_COMPONENT_AUTH_CODE_URL);
        if (StringUtils.isEmpty(authCodeUrl)) {
            throw exception(SYS_PARTNER_CONFIG_COMPONENT_AUTHORIZER_ACCESS_TOKEN_URL_ERR);
        }
        String authorizerAccessToken = HttpUtil.get(StringUtils.format(authCodeUrl, appid));
        if (StringUtils.isEmpty(authorizerAccessToken)) {
            // 让小程序重新授权
            return SysSaveOpenB2bPayRespVO.builder().state(202).build();
        }
        String setmchprofitrateUrl = StringUtils.format("https://api.weixin.qq.com/retail/B2b/setmchprofitrate?access_token={}", authorizerAccessToken);
        String requestData = JSON.toJSONString(reqVO);
        log.info("设置技术服务费, request={}", requestData);
        String result = HttpUtil.post(setmchprofitrateUrl, requestData);
        log.info("设置技术服务费, response={}", result);
        JSONObject resObj = JSON.parseObject(result);
        if (0 == resObj.getInteger("errcode")) {
            // 更新服务费报名状态
            WxB2bPayAppletStatusDTO wxB2bState = getAppWxB2bState(appid);
            wxB2bState.setProfitRate(reqVO.getProfit_rate());
            wxB2bState.setSubMchid(reqVO.getSub_mchid());
            this.setPartnerConfigObject(
                    PartnerConfigEnum.WXB2B_PAY_STATE.getKey() + appid,
                    null,
                    wxB2bState,
                    PartnerConfigEnum.WXB2B_PAY_STATE
            );
        }
        return new SysSaveOpenB2bPayRespVO(resObj.getInteger("errcode"), resObj.getString("errmsg"));
    }

    @Override
    public WxB2bPayAppletStatusDTO getAppWxB2bState(String appid) {
        WxB2bPayAppletStatusDTO statusDTO = this.getPartnerConfigObject(PartnerConfigEnum.WXB2B_PAY_STATE.getKey() + appid, null, WxB2bPayAppletStatusDTO.class);
        if (Objects.isNull(statusDTO)) {
            statusDTO = new WxB2bPayAppletStatusDTO();
        }
        //  从中心服务器获取第三方应用小程序代理code
        String authCodeUrl = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_COMPONENT_AUTH_CODE_URL);
        if (StringUtils.isNotEmpty(authCodeUrl)) {
            String authorizerAccessToken = HttpUtil.get(StringUtils.format(authCodeUrl, appid));
            statusDTO.setAuthComponentStatus(StringUtils.isEmpty(authorizerAccessToken) ? StringPool.ZERO : StringPool.ONE);
        }
        return statusDTO;
    }

    private void validateSysPartnerConfigExists(Long partnerConfigId) {
        if (sysPartnerConfigMapper.selectById(partnerConfigId) == null) {
            throw exception(SYS_PARTNER_CONFIG_NOT_EXISTS);
        }
    }
}
