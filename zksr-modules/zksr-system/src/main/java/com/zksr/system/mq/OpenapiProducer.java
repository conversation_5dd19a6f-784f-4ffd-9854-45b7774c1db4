package com.zksr.system.mq;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.system.domain.SysInterfaceLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.support.MessageBuilder;

/**
* 第三方系统同步数据消息队列消费者
* @date 2024/7/12 10:10
* <AUTHOR>
*/
@Slf4j
@Configuration
public class OpenapiProducer {

    @Autowired
    private StreamBridge streamBridge;


    /**
     * 发送保存商品消息
     * 消费 {@link OpenapiConsumer#openapi_saveprdt()}
     * @param sysInterfaceLog
     */
    public void sendSaveprdtMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送保存商品消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_SAVEPRDT_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        .build());
        log.info("发送保存商品消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 商品生产日期更新 消息
     * 消费 {@link OpenapiConsumer#openapi_saveprdt_date()}
     * @param sysInterfaceLog
     */
    public void sendSaveprdtDateMsg(SysInterfaceLog sysInterfaceLog){
        //log.info("发送商品生产日期更新消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_SAVEPRDT_DATE_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        //log.info("发送商品生产日期更新消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 商品库存更新 消息
     * 消费 {@link OpenapiConsumer#openapi_saveprdt_stock()}
     * @param sysInterfaceLog
     */
    public void sendSaveprdtStockMsg(SysInterfaceLog sysInterfaceLog){
        //log.info("发送商品库存更新消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_SAVEPRDT_STOCK_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        //log.info("发送商品库存更新消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 订单收货确认 消息
     * 消费 {@link OpenapiConsumer#openapi_confirm_receipt()}
     * @param sysInterfaceLog
     */
    public void sendConfirmReceiptMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送订单收货确认消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_CONFIRM_RECEIPT_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送订单收货确认消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 售后退货确认 消息
     * 消费 {@link OpenapiConsumer#openapi_confirm_return()}
     * @param sysInterfaceLog
     */
    public void sendConfirmReturnMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送售后退货确认消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_CONFIRM_RETURN_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送售后退货确认消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 订单发货 消息
     * 消费 {@link OpenapiConsumer#openapi_delivery()}
     * @param sysInterfaceLog
     */
    public void sendDeliveryMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送订单发货消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_DELIVERY_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送订单发货消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 订单状态 消息
     * 消费 {@link OpenapiConsumer#openapi_orderlog()}
     * @param sysInterfaceLog
     */
    public void sendOrderlogMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送订单状态消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_ORDERLOG_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送订单状态消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }


    /**
     * 发送 订单接收成功通知 消息
     * 消费 {@link OpenapiConsumer#openapi_order_receive_callback()}
     * @param sysInterfaceLog
     */
    public void sendOrderReceiveCallbackMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送订单接收成功通知消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_ORDER_RECEIVE_CALLBACK_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送订单接收成功通知消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 订单取消接收通知 消息
     * 消费 {@link OpenapiConsumer#openapi_order_cancel_receive_callback()}
     * @param sysInterfaceLog
     */
    public void sendOrderCancelReceiveCallbackMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送订单取消接收通知消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_ORDER_CANCEL_RECEIVE_CALLBACK_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送订单取消接收通知消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 订单发货前取消 消息
     * 消费 {@link OpenapiConsumer#openapi_order_cancel()}
     * @param sysInterfaceLog
     */
    public void sendOrderCancelMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送订单发货前取消消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_ORDER_CANCEL_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送订单发货前取消消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 退货确认前取消(售后取消) 消息
     * 消费 {@link OpenapiConsumer#openapi_after_cancel()}
     * @param sysInterfaceLog
     */
    public void sendAfterCancelMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送退货确认前取消(售后取消)消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_AFTER_CANCEL_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送退货确认前取消(售后取消)消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 退单接收成功通知 消息
     * 消费 {@link OpenapiConsumer#openapi_after_order_receive_callback()}
     * @param sysInterfaceLog
     */
    public void sendAfterOrderReceiveCallbackMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送退单接收成功通知消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_AFTER_ORDER_RECEIVE_CALLBACK_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送退单接收成功通知消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送 售后状态 消息
     * 消费 {@link OpenapiConsumer#openapi_afterlog()}
     * @param sysInterfaceLog
     */
    public void sendAfterlogMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送售后状态消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_AFTERLOG_TOPIC_OUT_PUT ,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送售后状态消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送入驻商消息
     * 消费 {@link OpenapiConsumer#openapi_savesupplier()}
     * @param sysInterfaceLog
     */
    public void sendSaveSupplierMsg(SysInterfaceLog sysInterfaceLog){
        log.info("发送保存入驻商消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_SAVESUPPLIER_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        .build());
        log.info("发送保存入驻商消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 新增货到付款清账能力
     * 消费 {@link OpenapiConsumer#openapi_add_hdfk_settle()}
     * @param sysInterfaceLog
     */
    public void sendAddHdfkSettle(SysInterfaceLog sysInterfaceLog) {
        log.info("发送新增货到付款清账能力消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_ADD_HDFK_SETTLE_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        //.setHeader("for", "这是一个请求头2～")
                        .build());
        log.info("发送新增货到付款清账能力结束:{}:{}", sysInterfaceLog.getId(), result);
    }

    /**
     * 发送批量要货单数据
     * 消息消费 {@link OpenapiConsumer#openapi_batch_create_yh()}
     * @param interfaceLog  要货单日志
     */
    public void sendSubmitBatchYh(SysInterfaceLog interfaceLog) {
        log.info("发送批量要货单数据:{}", JSON.toJSONString(interfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_BATCH_CREATE_YH_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(interfaceLog)
                        .build());
        log.info("发送批量要货单数据:{}:{}", interfaceLog.getLogType(), result);
    }



    /**
     * 发送接收门店注册消息
     * 消费 {@link OpenapiConsumer#openapi_registerBranch()}
     * @param sysInterfaceLog
     */
    public void sendRegisterBranchMsg(SysInterfaceLog sysInterfaceLog){
        log.info(" 发送接收门店注册消息:{}", JSON.toJSONString(sysInterfaceLog));
        boolean result = streamBridge.send(
                MessageConstant.OPENAPI_REGISTER_BRANCH_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(sysInterfaceLog)
                        .build());
        log.info(" 发送接收门店注册消息结束:{}:{}", sysInterfaceLog.getId(), result);
    }
}
