package com.zksr.system.service.impl.sms;

import cn.hutool.core.date.DateUtil;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import com.zksr.common.core.enums.SmsChannelEnum;
import com.zksr.common.core.utils.HttpClientPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ResultHandler;
import com.zksr.system.api.partnerConfig.dto.SmsConfigDTO;
import com.zksr.system.api.sms.dto.request.SmsReqDTO;
import com.zksr.system.api.sms.dto.response.SmsRespDTO;
import com.zksr.system.domain.SysSms;
import com.zksr.system.domain.SysSmsTemplate;
import com.zksr.system.mapper.SysSmsMapper;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysSmsChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.http.Consts;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 腾讯云短信服务
 */
@Slf4j
@Service
public class SmsTencentChannelServiceImpl implements ISysSmsChannelService {

    @Autowired
    private SysSmsMapper sysSmsMapper;

    @Autowired
    private ISysCacheService sysCacheService;

    private static final String ANNTO_SUCCESS_CODE = "0";
    private static final String ANNTO_FAIL_MSG = "发送失败";
    private static final String ANNTO_SUCCESS_MSG = "success";
    @Override
    public String channel() {
        return SmsChannelEnum.TENCENT.getChannel();
    }

    @Override
    public void sendSms(SysSms sms, SysSmsTemplate smsTemplate) {
        SmsConfigDTO smsConfig = sysCacheService.getSmsConfig(sms.getSysCode());
        sms.setUseTime(DateUtil.date());

        if (Objects.isNull(smsConfig)) {
            log.error("电信发送失败, 短信平台未配置 smsId={}", sms.getSmsId());
            sms.setMsg("短信平台未配置");
            sysSmsMapper.updateById(sms);
            return;
        }
        String msg = ANNTO_SUCCESS_MSG;
        String req = null;
        Map<String, String> headers = null;
        SmsRespDTO resp = null;
        try {
            Map<String,String> codeMap = JsonUtils.toJavaClass(sms.getTemplateParams(),Map.class);
            String content = smsTemplate.getContent().replace("${code}", codeMap.get("code"));

            req = buildSendReq(smsConfig, sms, smsTemplate, content);
            headers = buildHeaders(smsConfig, req);
            //调用安得腾讯云发送短信
            resp = sendMsgCenter(headers, req, smsConfig.getBopUrl());
            if(null == resp || !ANNTO_SUCCESS_CODE.equals(resp.getCode())){
                msg = ANNTO_FAIL_MSG;
            }
        } catch (Exception e) {
            log.error("短信发送异常", e);
            msg = ANNTO_FAIL_MSG;
            throw new RuntimeException(e);
        } finally {
            log.info("{}调用安得消息中心,入参[{},{}]，出参[{}]",sms.getMobile(),JsonUtils.toJsonString(headers),req,JsonUtils.toJsonString(resp));
            sms.setMsg(msg);
            sysSmsMapper.updateById(sms);
        }

    }

    /**
     * 调用安得消息中心发送短信
     * @param headers
     * @param req
     * @param url
     * @return
     */
    private SmsRespDTO sendMsgCenter(Map<String, String> headers, String req, String url) {
        ContentType contentType = ContentType.APPLICATION_JSON;//ContentType.create(ContentType.APPLICATION_JSON.getMimeType(), Consts.UTF_8);
        ResultHandler result = HttpClientPool.post(url, req, headers, contentType);
        log.info("post result:{}",JsonUtils.toJsonString(result.getMap()));
        return result.toJavaObject(SmsRespDTO.class);
    }

    private Map<String, String> buildHeaders(SmsConfigDTO smsConfig, String content) throws NoSuchAlgorithmException {
        long timestamp = System.currentTimeMillis()/1000;
        Map<String, String> headers = new HashMap<>();
        headers.put("clientId",smsConfig.getBopClientId());
        headers.put("accessToken",smsConfig.getBopAccessToken());
        headers.put("timestamp",String.valueOf(timestamp));
        headers.put("sign",getSign(smsConfig,timestamp,content));
        return headers;
    }
    private String buildSendReq(SmsConfigDTO smsConfig, SysSms sms, SysSmsTemplate smsTemplate, String content) {
        SmsReqDTO req = new SmsReqDTO();
        req.setAppSystem("SaasB2b");
        req.setContent(content);
        List<String> mobiles = Lists.newArrayList();
        mobiles.add(sms.getMobile());
        req.setMobiles(mobiles);
        req.setNodeName(sms.getSysCode()+"");
        req.setSignName(smsConfig.getSignName());
        req.setTenantCode(smsConfig.getTenantCode());

        return JsonUtils.toJsonString(req);
    }

    /**
     * 鹊桥签名
     * @param smsConfig
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String getSign(SmsConfigDTO smsConfig, long timestamp, String content) throws NoSuchAlgorithmException {
        String accessToken = smsConfig.getBopAccessToken();
        String secret = smsConfig.getBopClientSecret();
        String value = content+timestamp+accessToken+secret;
        Base64.Encoder encoder = Base64.getEncoder();
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        String sign = encoder.encodeToString(md5.digest(value.getBytes(StandardCharsets.UTF_8)));
        return sign;
    }

    private void send(SmsConfigDTO smsConfig, SysSms sms, SysSmsTemplate smsTemplate) throws Exception {
        // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId，SecretKey。
        // SecretId、SecretKey 查询: https://console.cloud.tencent.com/cam/capi
        Credential cred = new Credential(smsConfig.getAccessKeySecret(), smsConfig.getAccessKeyId());

        // 实例化一个http选项，可选的，没有特殊需求可以跳过
        HttpProfile httpProfile = new HttpProfile();
        // 从3.0.96版本开始, 单独设置 HTTP 代理（无需要直接忽略）
        // httpProfile.setProxyHost("真实代理ip");
        // httpProfile.setProxyPort(真实代理端口);
//        httpProfile.setReqMethod("GET"); // get请求(默认为post请求)
        httpProfile.setConnTimeout(10); // 请求连接超时时间，单位为秒(默认60秒)
        httpProfile.setWriteTimeout(10);  // 设置写入超时时间，单位为秒(默认0秒)
        httpProfile.setReadTimeout(10);  // 设置读取超时时间，单位为秒(默认0秒)

        /* 指定接入地域域名，默认就近地域接入域名为 sms.tencentcloudapi.com ，也支持指定地域域名访问，例如广州地域的域名为 sms.ap-guangzhou.tencentcloudapi.com */
        httpProfile.setEndpoint("sms.tencentcloudapi.com");

        /* 非必要步骤:
         * 实例化一个客户端配置对象，可以指定超时时间等配置 */
        ClientProfile clientProfile = new ClientProfile();
        /* SDK默认用TC3-HMAC-SHA256进行签名
         * 非必要请不要修改这个字段 */
        clientProfile.setSignMethod("HmacSHA256");
        clientProfile.setHttpProfile(httpProfile);
        /* 实例化要请求产品(以sms为例)的client对象
         * 第二个参数是地域信息，可以直接填写字符串ap-guangzhou，支持的地域列表参考 https://cloud.tencent.com/document/api/382/52071#.E5.9C.B0.E5.9F.9F.E5.88.97.E8.A1.A8 */
        SmsClient client = new SmsClient(cred, "ap-guangzhou",clientProfile);

        SendSmsRequest req = new SendSmsRequest();

        /*
         * 帮助链接：
         * 短信控制台: https://console.cloud.tencent.com/smsv2
         * 腾讯云短信小助手: https://cloud.tencent.com/document/product/382/3773#.E6.8A.80.E6.9C.AF.E4.BA.A4.E6.B5.81 */
        /* 短信应用ID: 短信SdkAppId在 [短信控制台] 添加应用后生成的实际SdkAppId，示例如1400006666 */
        // 应用 ID 可前往 [短信控制台](https://console.cloud.tencent.com/smsv2/app-manage) 查看
        String sdkAppId = "1400009099";//TODO
        req.setSmsSdkAppId(sdkAppId);

        /* 短信签名内容: 使用 UTF-8 编码，必须填写已审核通过的签名 */
        // 签名信息可前往 [国内短信](https://console.cloud.tencent.com/smsv2/csms-sign) 或 [国际/港澳台短信](https://console.cloud.tencent.com/smsv2/isms-sign) 的签名管理查看
        String signName = "腾讯云";
        req.setSignName(smsTemplate.getSignName());

        /* 模板 ID: 必须填写已审核通过的模板 ID */
        // 模板 ID 可前往 [国内短信](https://console.cloud.tencent.com/smsv2/csms-template) 或 [国际/港澳台短信](https://console.cloud.tencent.com/smsv2/isms-template) 的正文模板管理查看
        String templateId = "449739";
        req.setTemplateId(smsTemplate.getTemplateCode());

        /* 模板参数: 模板参数的个数需要与 TemplateId 对应模板的变量个数保持一致，若无模板参数，则设置为空 */
        String[] templateParamSet = {sms.getTemplateParams()};
        req.setTemplateParamSet(templateParamSet);

        /* 下发手机号码，采用 E.164 标准，+[国家或地区码][手机号]
         * 示例如：+8613711112222， 其中前面有一个+号 ，86为国家码，13711112222为手机号，最多不要超过200个手机号 */
        String[] phoneNumberSet = {sms.getMobile()};
        req.setPhoneNumberSet(phoneNumberSet);

        /* 用户的 session 内容（无需要可忽略）: 可以携带用户侧 ID 等上下文信息，server 会原样返回 */
        String sessionContext = "";
        req.setSessionContext(sessionContext);

        /* 短信码号扩展号（无需要可忽略）: 默认未开通，如需开通请联系 [腾讯云短信小助手] */
        String extendCode = "";
        req.setExtendCode(extendCode);

        /* 国内短信无需填写该项；国际/港澳台短信已申请独立 SenderId 需要填写该字段，默认使用公共 SenderId，无需填写该字段。注：月度使用量达到指定量级可申请独立 SenderId 使用，详情请联系 [腾讯云短信小助手](https://cloud.tencent.com/document/product/382/3773#.E6.8A.80.E6.9C.AF.E4.BA.A4.E6.B5.81)。*/
        String senderid = "";
        req.setSenderId(senderid);

        sms.setUseTime(DateUtil.date());
        SendSmsResponse res = client.SendSms(req);

        /* 当出现以下错误码时，快速解决方案参考
         * [FailedOperation.SignatureIncorrectOrUnapproved](https://cloud.tencent.com/document/product/382/9558#.E7.9F.AD.E4.BF.A1.E5.8F.91.E9.80.81.E6.8F.90.E7.A4.BA.EF.BC.9Afailedoperation.signatureincorrectorunapproved-.E5.A6.82.E4.BD.95.E5.A4.84.E7.90.86.EF.BC.9F)
         * [FailedOperation.TemplateIncorrectOrUnapproved](https://cloud.tencent.com/document/product/382/9558#.E7.9F.AD.E4.BF.A1.E5.8F.91.E9.80.81.E6.8F.90.E7.A4.BA.EF.BC.9Afailedoperation.templateincorrectorunapproved-.E5.A6.82.E4.BD.95.E5.A4.84.E7.90.86.EF.BC.9F)
         * [UnauthorizedOperation.SmsSdkAppIdVerifyFail](https://cloud.tencent.com/document/product/382/9558#.E7.9F.AD.E4.BF.A1.E5.8F.91.E9.80.81.E6.8F.90.E7.A4.BA.EF.BC.9Aunauthorizedoperation.smssdkappidverifyfail-.E5.A6.82.E4.BD.95.E5.A4.84.E7.90.86.EF.BC.9F)
         * [UnsupportedOperation.ContainDomesticAndInternationalPhoneNumber](https://cloud.tencent.com/document/product/382/9558#.E7.9F.AD.E4.BF.A1.E5.8F.91.E9.80.81.E6.8F.90.E7.A4.BA.EF.BC.9Aunsupportedoperation.containdomesticandinternationalphonenumber-.E5.A6.82.E4.BD.95.E5.A4.84.E7.90.86.EF.BC.9F)
         * 更多错误，可咨询[腾讯云助手](https://tccc.qcloud.com/web/im/index.html#/chat?webAppId=8fa15978f85cb41f7e2ea36920cb3ae1&title=Sms)
         */

        log.info("短信发送结果={}", SendSmsResponse.toJsonString(res));
        sysSmsMapper.updateById(sms);
    }

}
