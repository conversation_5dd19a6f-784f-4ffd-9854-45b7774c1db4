package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysMessageLog;
import com.zksr.system.controller.message.vo.SysMessageLogPageReqVO;

import java.util.Objects;


/**
 * 消息发送记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Mapper
public interface SysMessageLogMapper extends BaseMapperX<SysMessageLog> {
    default PageResult<SysMessageLog> selectPage(SysMessageLogPageReqVO reqVO) {
        LambdaQueryWrapperX<SysMessageLog> queryWrapperX = new LambdaQueryWrapperX<SysMessageLog>()
                .eqIfPresent(SysMessageLog::getMsgId, reqVO.getMsgId())
                .eqIfPresent(SysMessageLog::getSysCode, reqVO.getSysCode())
                .eqIfPresent(SysMessageLog::getMessageTemplateId, reqVO.getMessageTemplateId())
                .eqIfPresent(SysMessageLog::getMerchantId, reqVO.getMerchantId())
                .eqIfPresent(SysMessageLog::getMerchantType, reqVO.getMerchantType())
                .eqIfPresent(SysMessageLog::getState, reqVO.getState())
                .likeIfPresent(SysMessageLog::getContent, reqVO.getContent())
                .orderByDesc(SysMessageLog::getMsgId);

        if (Objects.nonNull(reqVO.getStartTime())) {
            queryWrapperX.between(SysMessageLog::getCreateTime, reqVO.getStartTime(), reqVO.getEndTime());
        }
        return selectPage(reqVO, queryWrapperX);
    }
}
