package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.dto.AreaIsExistDTO;
import com.zksr.system.controller.area.excel.SysAreaImportExcel;
import com.zksr.system.controller.area.vo.*;
import com.zksr.system.domain.SysArea;

import javax.validation.Valid;
import java.util.List;

/**
 * 区域城市Service接口
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
public interface ISysAreaService {

    /**
     * 新增区域城市
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysArea(@Valid SysAreaSaveReqVO createReqVO);

    /**
     * 修改区域城市
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysArea(@Valid SysAreaSaveReqVO updateReqVO);

    /**
     * 删除区域城市
     *
     * @param areaId 区域城市id
     */
    public void deleteSysArea(Long areaId);

    /**
     * 批量删除区域城市
     *
     * @param areaIds 需要删除的区域城市主键集合
     * @return 结果
     */
    public AreaIsExistDTO deleteSysAreaByAreaIds(Long[] areaIds);

    /**
     * 获得区域城市
     *
     * @param areaId 区域城市id
     * @return 区域城市
     */
    public SysArea getSysArea(Long areaId);

    /**
     * 获得区域城市分页
     *
     * @param pageReqVO 分页查询
     * @return 区域城市分页
     */
    PageResult<SysAreaRespVO> getSysAreaPage(SysAreaPageReqVO pageReqVO);

    /**
    * @Description: 运营商获取可绑定的区域城市
    * @Author: liuxingyu
    * @Date: 2024/3/7 14:47
    */
    List<SysAreaRespVO> getDcNotBindArea(Long dcId);

    /**
    * @Description: 获取所有区域城市
    * @Author: liuxingyu
    * @Date: 2024/3/15 17:51
    */
    List<SysArea> getAreaList(AreaListReqVO reqVO);

    List<SysArea> getAreaListBySysCode(Long sysCode);

    public void reloadAreaDtoCache(Long areaId);

    public void removeAreaDtoCache(Long areaId);

    /**
     * 批量获取城市数据, 用于选中回显
     * @param areaIds
     * @return
     */
    List<SysAreaSelectedRespVO> getSelectedSysArea(List<Long> areaIds);

    /**
     * @Description: 根据入驻商获取区域城市
     * @Author: liuxingyu
     * @Date: 2024/4/18 9:37
     */
    List<SysArea> getBySupplierId(Long supplierId);

    /**
     * 校验当前城市 是否是二级城市
     * @param areaId
     * @return
     */
    boolean checkAreaByAreaId(Long areaId);

    SysArea getDefaultBySyscode(Long sysCode);

    /**
     * 获取区域城市下拉列表(只获取二级)
     * @param areaId
     * @return
     */
    List<SysArea> getAreaDownList(Long areaId);

    List<AreaDTO> getAreaBySyscodeAndDcId(Long sysCode, Long dcId);

    List<SysArea> getAreaListByDcId(Long DcId);

    String impordData(List<SysAreaImportExcel> sysAreaList);

    /**
     * 根据三级地址id获取城市区域
     *
     * @param threeAreaCityId 级地址id
     * @return 区域城市
     */
    public SysArea getSysAreaByThreeAreaCityId(Long threeAreaCityId);
}
