package com.zksr.system.visualSyn.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
* 邮件消息通知 Nacos配置
* @date 2025/2/13 17:34
* <AUTHOR>
*/
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.mail")
@AllArgsConstructor
@NoArgsConstructor
public class EmailMessageSendConfig {

    /** 邮箱主机 发送方的服务器  网易: smtp.163.com  QQ:smtp.qq.com */
    private String host;

    /** 用户名 */
    private String username;

    /** 密码 注这个不是真实的密码，是在邮箱设置里面生成的授权码 */
    private String password;
}
