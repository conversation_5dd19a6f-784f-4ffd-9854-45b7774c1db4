package com.zksr.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.enums.CommonMessagePushModeEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.common.third.message.dto.CommonMessageRespVO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.system.api.commonMessage.dto.MessageCertificate;
import com.zksr.system.api.commonMessage.dto.MessageCertificateTable;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.commonMessage.vo.SubscribeMsgConfigVO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.controller.message.vo.SysMessageTemplatePageReqVO;
import com.zksr.system.controller.message.vo.SysMessageTemplateSaveReqVO;
import com.zksr.system.convert.subscribe.SysMessageTemplateConvert;
import com.zksr.system.domain.SysMessageTemplate;
import com.zksr.system.mapper.SysMessageTemplateMapper;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysMessageLogService;
import com.zksr.system.service.ISysMessageTemplateService;
import com.zksr.system.service.impl.message.adapter.MessageAdapter;
import com.zksr.system.service.impl.message.adapter.WxAppletMessageAdapter;
import com.zksr.system.service.impl.message.adapter.WxPublishMessageAdapter;
import com.zksr.system.service.impl.message.adapter.WxToBMessageAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_MESSAGE_PUS_MODE_NONE;

/**
 * 公众号, 小程序订阅消息模版Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Service
@Slf4j
public class SysMessageTemplateServiceImpl implements ISysMessageTemplateService {

    @Autowired
    private SysMessageTemplateMapper sysMessageTemplateMapper;

    @Autowired
    private Cache<String, List<MessageTemplateDTO>> messageTemplateCache;

    @Resource
    private MemberApi memberApi;

    @Autowired
    private ISysMessageLogService messageLogService;

    /**
     * 新增公众号, 小程序订阅消息模版
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysMessageTemplate(SysMessageTemplateSaveReqVO createReqVO) {
        // 插入
        SysMessageTemplate sysMessageTemplate = SysMessageTemplateConvert.INSTANCE.convert(createReqVO);
        // 新增的数据默认是停用的
        if (Objects.nonNull(sysMessageTemplate.getStatus())) {
            validateSysMessageTemplateExists(createReqVO.getScene());
        } else {
            sysMessageTemplate.setStatus(NumberPool.INT_ZERO);
        }
        sysMessageTemplate.setConfig(JSON.toJSONString(createReqVO.getMsgConfigVO()));
        sysMessageTemplate.setUpdateTime(DateUtil.date());
        sysMessageTemplateMapper.insert(sysMessageTemplate);
        // 返回
        return sysMessageTemplate.getMessageTemplateId();
    }

    /**
     * 修改公众号, 小程序订阅消息模版
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysMessageTemplate(SysMessageTemplateSaveReqVO updateReqVO) {
        // 验证数据
        SysMessageTemplate update = SysMessageTemplateConvert.INSTANCE.convert(updateReqVO);
        update.setConfig(JSON.toJSONString(updateReqVO.getMsgConfigVO()));
        sysMessageTemplateMapper.updateById(update);
        // 移除缓存
        SysMessageTemplate messageTemplate = sysMessageTemplateMapper.selectById(updateReqVO.getMessageTemplateId());
        messageTemplateCache.remove(StringUtils.format("{}:{}", messageTemplate.getSysCode(), messageTemplate.getScene()));
    }

    /**
     * 删除公众号, 小程序订阅消息模版
     *
     * @param messageTemplateId 消息模版id
     */
    @Override
    public void deleteSysMessageTemplate(Long messageTemplateId) {
        SysMessageTemplate messageTemplate = sysMessageTemplateMapper.selectById(messageTemplateId);
        messageTemplate.setStatus(NumberPool.INT_TWO);
        // 删除
        sysMessageTemplateMapper.updateById(messageTemplate);
        messageTemplateCache.remove(StringUtils.format("{}:{}", messageTemplate.getSysCode(), messageTemplate.getScene()));
    }

    /**
     * 批量删除公众号, 小程序订阅消息模版
     *
     * @param messageTemplateIds 需要删除的公众号, 小程序订阅消息模版主键
     * @return 结果
     */
    @Override
    public void deleteSysMessageTemplateByMessageTemplateIds(Long[] messageTemplateIds) {
        for(Long messageTemplateId : messageTemplateIds){
            this.deleteSysMessageTemplate(messageTemplateId);
        }
    }

    /**
     * 获得公众号, 小程序订阅消息模版
     *
     * @param messageTemplateId 消息模版id
     * @return 公众号, 小程序订阅消息模版
     */
    @Override
    public SysMessageTemplate getSysMessageTemplate(Long messageTemplateId) {
        return sysMessageTemplateMapper.selectById(messageTemplateId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysMessageTemplate> getSysMessageTemplatePage(SysMessageTemplatePageReqVO pageReqVO) {
        return sysMessageTemplateMapper.selectPage(pageReqVO);
    }

    /**
     * 停用模版
     * @param messageTemplateId
     */
    @Override
    public void disable(Long messageTemplateId) {
        SysMessageTemplate messageTemplate = sysMessageTemplateMapper.selectById(messageTemplateId);
        messageTemplate.setStatus(NumberPool.INT_ZERO);
        sysMessageTemplateMapper.updateById(messageTemplate);
        messageTemplateCache.remove(StringUtils.format("{}:{}", messageTemplate.getSysCode(), messageTemplate.getScene()));
    }

    /**
     * 启用模版
     * @param messageTemplateId
     */
    @Override
    public void enable(Long messageTemplateId) {
        SysMessageTemplate messageTemplate = sysMessageTemplateMapper.selectById(messageTemplateId);
        messageTemplate.setStatus(NumberPool.INT_ONE);
        // 验证是否有重复启用的
        validateSysMessageTemplateExists(messageTemplate.getScene());
        sysMessageTemplateMapper.updateById(messageTemplate);
        messageTemplateCache.remove(StringUtils.format("{}:{}", messageTemplate.getSysCode(), messageTemplate.getScene()));
    }

    @Override
    public void sendMsg(List<CommonMessageDTO> messageList, MessageTemplateDTO messageTemplate) {
        // 匹配消息处理器
        MessageAdapter messageAdapter = getMessageAdapter(messageTemplate.pushMode());
        // 构建用户凭证信息
        MessageCertificateTable certificateTable = builderCertificate(messageList, messageTemplate);
        // 发送消息
        List<CommonMessageRespVO> messageRespVOList = messageAdapter.sendTo(messageList, messageTemplate, certificateTable);
        // 保存发送记录
        messageLogService.insertSysMessageLog(messageRespVOList, messageTemplate);
    }

    private MessageCertificateTable builderCertificate(List<CommonMessageDTO> messageList, MessageTemplateDTO messageTemplate) {
        MessageCertificateTable certificateTable = new MessageCertificateTable();
        for (CommonMessageDTO messageDTO : messageList) {
            switch (messageDTO.getMerchantType()) {
                case MEMBER:
                case BRANCH:
                    MemberDTO member = memberApi.getMemBerByMemberId(messageDTO.getMerchantId()).getCheckedData();
                    if (Objects.nonNull(member)) {
                        MessageCertificate certificate = new MessageCertificate();
                        certificate.setAppletOpenid(member.getXcxOpenid());
                        certificate.setPublishOpenid(member.getPublishOpenid());
                        certificate.setMerchantType(MerchantTypeEnum.MEMBER);
                        certificate.setMerchantId(messageDTO.getMerchantId());
                        certificateTable.add(certificate);
                    }
                    break;
                case COLONEL:
                    ColonelDTO colonelDTO = getCache().getColonel(messageDTO.getMerchantId());
                    if (Objects.nonNull(colonelDTO)) {
                        MessageCertificate certificate = new MessageCertificate();
                        certificate.setPublishOpenid(colonelDTO.getPublishOpenid());
                        certificate.setMerchantType(MerchantTypeEnum.COLONEL);
                        certificate.setMerchantId(messageDTO.getMerchantId());
                        certificateTable.add(certificate);
                    }
                    break;
                case SUPPLIER:
                    SupplierDTO supplierDTO = getCache().getSupplierDTO(messageDTO.getMerchantId());
                    if (Objects.nonNull(supplierDTO)) {
                        MessageCertificate certificate = new MessageCertificate();
                        certificate.setPublishOpenid(supplierDTO.getPublishOpenid());
                        certificate.setMerchantId(messageDTO.getMerchantId());
                        certificate.setMerchantType(MerchantTypeEnum.SUPPLIER);
                        certificateTable.add(certificate);
                    }
                    break;
            }
        }
        return certificateTable;
    }

    private MessageAdapter getMessageAdapter(CommonMessagePushModeEnum pushMode) {
        switch (pushMode) {
            case APPLET:
                return SpringUtils.getBean(WxAppletMessageAdapter.class);
            case PUBLISH:
                return SpringUtils.getBean(WxPublishMessageAdapter.class);
            case WX_MERCHANT:
                return SpringUtils.getBean(WxToBMessageAdapter.class);
        }
        throw exception(SYS_MESSAGE_PUS_MODE_NONE);
    }

    @Override
    public List<MessageTemplateDTO> getTemplateBySysCodeAndScene(Long sysCode, Long scene) {
        List<SysMessageTemplate> messageTemplateList = sysMessageTemplateMapper.selectTemplateBySysCodeAndScene(sysCode, scene);
        if (ObjectUtil.isEmpty(messageTemplateList)) {
            return null;
        }
        ArrayList<MessageTemplateDTO> resList = new ArrayList<>();
        for (SysMessageTemplate messageTemplate : messageTemplateList) {
            MessageTemplateDTO templateDTO = SysMessageTemplateConvert.INSTANCE.convertDTO(messageTemplate);
            templateDTO.setMsgConfigVO(JSON.parseObject(messageTemplate.getConfig(), SubscribeMsgConfigVO.class));
            resList.add(templateDTO);
        }
        return resList;
    }

    private void validateSysMessageTemplateExists(Integer scene) {
        Long countByScene = sysMessageTemplateMapper.selectCountByScene(scene);
        if (countByScene > NumberPool.LONG_ZERO) {
            //throw exception(SYS_MESSAGE_SCENE_REPEAT);
        }
    }

    public ISysCacheService getCache() {
        return SpringUtils.getBean(ISysCacheService.class);
    }
}
