package com.zksr.system.controller.message.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

/**
 * 公众号, 小程序订阅消息模版对象 sys_message_template
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@ApiModel("公众号, 小程序订阅消息模版 - sys_message_template分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysMessageTemplatePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 消息模版id */
    @ApiModelProperty(value = "接收对象 branch-门店,colonel-业务员")
    private Long messageTemplateId;

    /** 平台商ID */
    @Excel(name = "平台商ID")
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    /** 0-停用,1-启用 */
    @Excel(name = "0-停用,1-启用")
    @ApiModelProperty(value = "0-停用,1-启用")
    private Integer status;

    /** 模版ID */
    @Excel(name = "模版ID")
    @ApiModelProperty(value = "模版ID")
    private String templateId;

    /** 模版配置 */
    @Excel(name = "模版配置")
    @ApiModelProperty(value = "模版配置")
    private String config;

    /** 消息场景 0-用户下单,1-订单开始配送 */
    @Excel(name = "消息场景 0-用户下单,1-订单开始配送")
    @ApiModelProperty(value = "消息场景 0-用户下单,1-订单开始配送")
    private Integer scene;

    /** 接收对象 branch-门店,colonel-业务员 */
    @Excel(name = "接收对象 branch-门店,colonel-业务员")
    @ApiModelProperty(value = "接收对象 branch-门店,colonel-业务员")
    private String receiveMerchant;


}
