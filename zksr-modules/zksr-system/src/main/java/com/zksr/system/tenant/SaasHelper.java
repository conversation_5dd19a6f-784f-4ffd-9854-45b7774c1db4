package com.zksr.system.tenant;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.constant.TokenConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.domain.JsonResponse;
import com.zksr.system.domain.SysPartner;
import com.zksr.system.domain.Tenant;
import com.zksr.system.domain.dto.SaasLoginUser;
import com.zksr.system.domain.dto.SaasUserInfo;
import com.zksr.system.enums.TenantUserTypeSourceEnum;
import com.zksr.system.mapper.SysPartnerMapper;
import com.zksr.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/03/26 16:17
 */
@Component
@Slf4j(topic = "调用saas服务")
public class SaasHelper {
    //新增用户
    @Value("${saas.auth.user.addUrl:https://icloudapi-sit.annto.com/api-saas-auth/core/userInfo/createUserAndAccount}")
    private String saasAuthUserAddUrl;

    //更新用户
    @Value("${saas.auth.user.updateUrl:https://icloudapi-sit.annto.com/api-saas-auth/core/userInfo/updateUserAndAccount}")
    private String saasAuthUserUpdateUrl;

    // 更新状态
    @Value("${saas.auth.user.updateEnableFlagUrl:https://icloudapi-sit.annto.com/api-saas-auth/core/tenantUser/enableFlagForSaas}")
    private String saasAuthUserUpdateEnableFlagUrl;

    // 查询租户是否存在
    @Value("${saas.auth.tenant.queryUrl:https://icloudapi-sit.annto.com/api-saas-auth/core/tenant/selectOneTenant}")
    private String saasSelectOneTenantUrl;

    // p密码登录
    @Value("${saas.auth.user.pwdLoginUrl:https://icloudapi-sit.annto.com/api-saas-auth/core/public/pwd/login}")
    private String saasPwdLoginUrl;

    // 忘记密码
    @Value("${saas.auth.user.forgetAndResetUrl:https://icloudapi-sit.annto.com/api-saas-auth/core/public/pwd/forgetAndReset}")
    private String saasForgetAndResetUrl;

    // 更换手机号
    @Value("${saas.auth.user.updateUserMobileUrl:https://icloudapi-sit.annto.com/api-saas-auth/core/userInfo/updateUserMobile}")
    private String saasUpdateUserMobileUrl;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    @Resource
    private PwdHelper pwdHelper;

    @Resource
    private SysPartnerMapper sysPartnerMapper;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 新增saas用户
     * @return
     */
    public void saasAuthUserAdd(String mobile, String userName, String tenantCode, SysUser sysUser) {
        if(!saasAuthSwitch) return ;
        // header信息
        Map<String, String> header = new HashMap<>();
        header.put(TokenConstants.SAAS_ACCESS_TOKEN, SecurityUtils.getSaasToken());
        header.put(SecurityConstants.SAAS_HEADER_TENANT_CODE, tenantCode);

        // body信息
        Map<String, Object> body = new HashMap<>();
        body.put("tenantCode", tenantCode);
        body.put("mobile",mobile);
        body.put("userName",userName);
        body.put("source", TenantUserTypeSourceEnum.B2B_REGISTER.getKey());


        log.info("调用saas平台创建用户接口请求入参:{},请求头信息:{},请求地址:{}", JSONUtil.toJsonStr(body), JSONUtil.toJsonStr(header), saasAuthUserAddUrl);
        String responseStr = HttpUtils.simplePost(saasAuthUserAddUrl, header, JSONUtil.toJsonStr(body), HttpUtils.TIME_OUT_TEN);
        log.info("调用saas平台创建用户接口响应数据:{}", responseStr);
        JsonResponse<SaasUserInfo> saasUserInfoJsonResponse = JSONUtil.toBean(responseStr, new TypeReference<JsonResponse<SaasUserInfo>>() {
        }, false);
        if (!Objects.equals(saasUserInfoJsonResponse.getCode(),"0")) {
            throw new RuntimeException("创建saas租户用户失败："+saasUserInfoJsonResponse.getMsg());
        }
        sysUser.setSaasUserCode(saasUserInfoJsonResponse.getData().getUserCode());
    }


    public String getTenantCode(Long sysCode){
        if(sysCode == null) return SecurityContextHolder.getSaasTenantCode();
        SysPartner sysPartner = sysPartnerMapper.selectById(sysCode);
        if(sysPartner == null)   return SecurityContextHolder.getSaasTenantCode();
        return StrUtil.isBlank(sysPartner.getSaasTenantCode()) ? SecurityContextHolder.getSaasTenantCode() : sysPartner.getSaasTenantCode();
    }

    /**
     * 更新saas用户
     * @return
     */
    public void saasAuthUserUpdate(String userCode, Long sysCode, String userName) {
        if(!saasAuthSwitch) return ;
        String tenantCode = getTenantCode(sysCode);
        // header信息
        Map<String, String> header = new HashMap<>();
        header.put(TokenConstants.SAAS_ACCESS_TOKEN, SecurityUtils.getSaasToken());
        header.put(SecurityConstants.SAAS_HEADER_TENANT_CODE, tenantCode);

        // body信息
        Map<String, Object> body = new HashMap<>();
        body.put("userName",userName);
        body.put("userCode",userCode);

        log.info("调用saas平台更新用户接口请求入参:{},请求头信息:{},请求地址:{}", JSONUtil.toJsonStr(body), JSONUtil.toJsonStr(header), saasAuthUserUpdateUrl);
        String responseStr = HttpUtils.simplePost(saasAuthUserUpdateUrl, header, JSONUtil.toJsonStr(body), HttpUtils.TIME_OUT_TEN);
        log.info("调用saas平台更新用户接口响应数据:{}", responseStr);
        JsonResponse<SaasUserInfo> saasUserInfoJsonResponse = JSONUtil.toBean(responseStr, new TypeReference<JsonResponse<SaasUserInfo>>() {
        }, false);
        if (!Objects.equals(saasUserInfoJsonResponse.getCode(),"0")) {
            throw new RuntimeException("更新saas租户用户失败："+saasUserInfoJsonResponse.getMsg());
        }
    }


    /**
     * 更新saas用户启用禁用状态 1启用 0停用
     * @return
     */
    public void saasAuthUserUpdateEnableFlag(String userCode, Long sysCode, Integer enableFlag) {
        if(!saasAuthSwitch) return ;
        String tenantCode = getTenantCode(sysCode);
        // header信息
        Map<String, String> header = new HashMap<>();
        header.put(TokenConstants.SAAS_ACCESS_TOKEN, SecurityUtils.getSaasToken());
        header.put(SecurityConstants.SAAS_HEADER_TENANT_CODE, tenantCode);

        // body信息
        Map<String, Object> body = new HashMap<>();
        body.put("tenantCode", tenantCode);
        body.put("enableFlag",enableFlag);
        body.put("userCode",userCode);

        log.info("调用saas平台禁用启用接口请求入参:{},请求头信息:{},请求地址:{}", JSONUtil.toJsonStr(body), JSONUtil.toJsonStr(header), saasAuthUserAddUrl);
        String responseStr = HttpUtils.simplePost(saasAuthUserUpdateEnableFlagUrl, header, JSONUtil.toJsonStr(body), HttpUtils.TIME_OUT_TEN);
        log.info("调用saas平台禁用启用接口响应数据:{}", responseStr);
        JsonResponse<String> saasUserInfoJsonResponse = JSONUtil.toBean(responseStr, new TypeReference<JsonResponse<String>>() {
        }, false);
        if (!Objects.equals(saasUserInfoJsonResponse.getCode(),"0")) {
            throw new RuntimeException("更新saas租户用户状态失败，原因："+saasUserInfoJsonResponse.getMsg());
        }
    }


    /**
     * 更新saas用户状态 启用停用
     * @param userId 用户id
     * @param status 状态 2启用 3停用
     */
    public void updateSaasUser(Long userId, String userName, String status) {
        if (saasAuthSwitch) {
            SysUser userDb = userMapper.selectUserById(userId);
            if(userDb == null || StrUtil.isBlank(userDb.getSaasUserCode())) return;
            //更新用户状态
            if(StrUtil.isNotBlank(userName)){
                saasAuthUserUpdate(userDb.getSaasUserCode(), userDb.getSysCode(), userName);
            }
            //更新用户状态
            if(StrUtil.isNotBlank(status)){
                int saasEnableFlag = Objects.equals(status, StringPool.ZERO) ? 1 : 0; //1启用 0停用
                saasAuthUserUpdateEnableFlag(userDb.getSaasUserCode(), userDb.getSysCode(), saasEnableFlag);
            }
        }
    }


    /**
     * 查询用户信息
     * @param saasTenantCode
     */
    public Tenant saasSelectOneTenant(String saasTenantCode) {
        // header信息
        Map<String, String> header = new HashMap<>();
        header.put(TokenConstants.SAAS_ACCESS_TOKEN, SecurityUtils.getSaasToken());

        // body信息
        Map<String, Object> body = new HashMap<>();
        body.put("tenantCode", saasTenantCode);

        log.info("调用saas平台查询租户接口请求入参:{},请求头信息:{},请求地址:{}", JSONUtil.toJsonStr(body), JSONUtil.toJsonStr(header), saasSelectOneTenantUrl);
        String responseStr = HttpUtils.simplePost(saasSelectOneTenantUrl, header, JSONUtil.toJsonStr(body), HttpUtils.TIME_OUT_TEN);
        log.info("调用saas平台查询租户接口响应数据:{}", responseStr);
        JsonResponse<Tenant> saasTenantInfoJsonResponse = JSONUtil.toBean(responseStr, new TypeReference<JsonResponse<Tenant>>() {
        }, false);
        if (!Objects.equals(saasTenantInfoJsonResponse.getCode(),"0")) {
            throw new RuntimeException("查询saas租户失败，原因："+saasTenantInfoJsonResponse.getMsg());
        }
        return saasTenantInfoJsonResponse.getData();
    }


    /**
     * 密码登录
     * @return
     */
    public List<SaasLoginUser> saasPwdLogin(String mobile, String password) {
        // body信息
        Map<String, Object> body = new HashMap<>();
        body.put("mobile", mobile);
        body.put("password",pwdHelper.encryptPassword(password));


        log.info("调用saas平台密码登录接口请求入参:{},请求地址:{}", JSONUtil.toJsonStr(body), saasPwdLoginUrl);
        String responseStr = HttpUtils.simplePost(saasPwdLoginUrl, null ,JSONUtil.toJsonStr(body), HttpUtils.TIME_OUT_TEN);
        log.info("调用saas平台密码登录接口响应数据:{}", responseStr);
        JsonResponse<List<SaasLoginUser>> saasUserInfoJsonResponse = JSONUtil.toBean(responseStr, new TypeReference<JsonResponse<List<SaasLoginUser>>>() {
        }, false);
        if (!Objects.equals(saasUserInfoJsonResponse.getCode(),"0")) {
            throw new RuntimeException("密码登录失败："+saasUserInfoJsonResponse.getMsg());
        }

        return saasUserInfoJsonResponse.getData();
    }

    /**
     * 忘记密码
     * @return
     */
    public void saasForgetAndReset(String mobile,String verificationCode,String newPassword) {
        // body信息
        Map<String, Object> body = new HashMap<>();
        body.put("mobile", mobile);
        body.put("verificationCode",verificationCode);
        body.put("newPassword",pwdHelper.encryptPassword(newPassword));

        log.info("调用saas平台忘记密码接口请求入参:{}, 请求地址:{}", JSONUtil.toJsonStr(body), saasForgetAndResetUrl);
        String responseStr = HttpUtils.simplePost(saasForgetAndResetUrl, null, JSONUtil.toJsonStr(body), HttpUtils.TIME_OUT_TEN);
        log.info("调用saas平台忘记密码接口响应数据:{}", responseStr);
        JsonResponse<String> saasUserInfoJsonResponse = JSONUtil.toBean(responseStr, new TypeReference<JsonResponse<String>>() {
        }, false);
        if (!Objects.equals(saasUserInfoJsonResponse.getCode(),"0")) {
            throw new RuntimeException("忘记密码失败："+saasUserInfoJsonResponse.getMsg());
        }
    }


    /**
     * 修改用户手机号
     * @return
     */
    public void saasUpdateUserMobile(String oldMobile, String mobile,String verificationCode) {
        // header信息
        Map<String, String> header = new HashMap<>();
        header.put(TokenConstants.SAAS_ACCESS_TOKEN, SecurityUtils.getSaasToken());

        // body信息
        Map<String, Object> body = new HashMap<>();
        body.put("oldMobile", oldMobile);
        body.put("mobile", mobile);
        body.put("code",verificationCode);

        log.info("调用saas平台修改用户手机号接口请求入参:{},请求头信息:{},请求地址:{}", JSONUtil.toJsonStr(body), JSONUtil.toJsonStr(header), saasUpdateUserMobileUrl);
        String responseStr = HttpUtils.simplePost(saasUpdateUserMobileUrl, header, JSONUtil.toJsonStr(body), HttpUtils.TIME_OUT_TEN);
        log.info("调用saas平台修改用户手机号接口响应数据:{}", responseStr);
        JsonResponse<String> saasUserInfoJsonResponse = JSONUtil.toBean(responseStr, new TypeReference<JsonResponse<String>>() {
        }, false);
        if (!Objects.equals(saasUserInfoJsonResponse.getCode(),"0")) {
            throw new RuntimeException("修改saas系统用户手机号失败："+saasUserInfoJsonResponse.getMsg());
        }
    }

}
