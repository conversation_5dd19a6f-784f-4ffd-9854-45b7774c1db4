package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.partnerPolicy.dto.SearchConfigDTO;
import com.zksr.system.controller.partnerPolicy.vo.*;
import com.zksr.system.domain.SysPartnerPolicy;

import javax.validation.Valid;

/**
 * 平台商政策(由平台商设置)Service接口
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
public interface ISysPartnerPolicyService {

    /**
     * 新增平台商政策(由平台商设置)
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Boolean saveSysPartnerPolicy(@Valid SysPartnerPolicySaveReqVO createReqVO);

    /**
     * 修改平台商政策(由平台商设置)
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysPartnerPolicy(@Valid SysPartnerPolicySaveReqVO updateReqVO);

    /**
     * 删除平台商政策(由平台商设置)
     *
     * @param partnerPolicyId 平台商政策id
     */
    public void deleteSysPartnerPolicy(Long partnerPolicyId);

    /**
     * 批量删除平台商政策(由平台商设置)
     *
     * @param partnerPolicyIds 需要删除的平台商政策(由平台商设置)主键集合
     * @return 结果
     */
    public void deleteSysPartnerPolicyByPartnerPolicyIds(Long[] partnerPolicyIds);

    /**
     * 获得平台商政策(由平台商设置)
     *
     * @param SupplierId 平台商政策id
     * @return 平台商政策(由平台商设置)
     */
    public SysPartnerPolicyRespVO getSysPartnerPolicy(Long SupplierId);

    /**
     * 获得平台商政策(由平台商设置)分页
     *
     * @param pageReqVO 分页查询
     * @return 平台商政策(由平台商设置)分页
     */
    PageResult<SysPartnerPolicy> getSysPartnerPolicyPage(SysPartnerPolicyPageReqVO pageReqVO);

    /**
    * @Description: 获取配置信息
    * @Author: liuxingyu
    * @Date: 2024/3/26 15:48
    */
    SysPartnerPolicyRespVO getSysPartnerPolicy(Long sysCode, Long dcId, Long supplierId, Integer type);


    /**
     * 获得平台商搜索推荐分页列表
     *
     * @param pageReqVO 分页查询
     * @return 平台商搜索推荐分页分页
     */
    PageResult<SearchConfigPageRespVO> selectSearchConfigPage(SysPartnerPolicyPageReqVO pageReqVO);


    /**
     * 新增平台商搜索推荐
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    Boolean addSearchConfig(SearchConfigReqVO createReqVO);


    /**
     * 编辑平台商搜索推荐
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    void updateSearchConfig(SearchConfigReqVO createReqVO);

    /**
     * 根据区域城市ID获取搜索配置
     * @param areaId
     * @return
     */
    SearchConfigDTO getSearchConfig(Long areaId);

    SysPartnerPolicyRespVO getDcConfig(Long dcId);
}
