package com.zksr.system.openapi.controller;


import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.member.api.member.MemberApi;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.openapi.controller.vo.GetTokenRequest;
import com.zksr.system.openapi.controller.vo.GetTokenResponse;
import com.zksr.system.openapi.service.ITokenService;
import com.zksr.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@RequestMapping("/openapi/token")
@Api(tags = "OPENAPI - 登录接口")
public class TokenController {

    @Resource
    private MemberApi memberApi;

    @Autowired
    private ITokenService tokenService;

    @Autowired
    private ISysUserService userService;
    /**
     * getAccessToken 接口
     *
     */
    @ApiOperation("getToken接口")
    @PostMapping(value = "/getToken")
    @ResponseBody
    @Log(title = "OPENAPI - 登录接口", businessType = BusinessType.INSERT)
    @DistributedLock(prefix = RedisLockConstants.LOCK_OPENAPI_GET_TOKEN, condition = "#request.sourceKey")
    public CommonResult<GetTokenResponse> getToken(@RequestBody @Valid GetTokenRequest request) {
        GetTokenResponse response = tokenService.getToken(request);
        return success(response);
    }


    @GetMapping("/getColonelBySaasUserCode")
    public CommonResult<SysUser>  getSaasUserCodeHasColonel(@RequestParam("saasUserCode") String saasUserCode)
    {
        return CommonResult.success(userService.getSaasUserCodeHasColonel(saasUserCode));
    }

}
