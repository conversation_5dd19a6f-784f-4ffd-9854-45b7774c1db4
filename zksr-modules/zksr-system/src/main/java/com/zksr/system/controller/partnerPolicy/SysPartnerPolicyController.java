package com.zksr.system.controller.partnerPolicy;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.chin.partnerPolicy.PolicyPartnerPolicyPipeline;
import com.zksr.system.controller.partnerPolicy.vo.*;
import com.zksr.system.domain.SysPartnerPolicy;
import com.zksr.system.service.ISysPartnerPolicyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商政策(由平台商设置)Controller
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Api(tags = "管理后台 - 平台商政策(由平台商设置)接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/partnerPolicy")
public class SysPartnerPolicyController {

    @Autowired
    private ISysPartnerPolicyService sysPartnerPolicyService;

    @Autowired
    private PolicyPartnerPolicyPipeline partnerPolicyPipeline;

    /**
     * 新增平台商政策(由平台商设置)
     */
    @ApiOperation(value = "新增平台商政策(由平台商设置)", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @Log(title = "平台商政策(由平台商设置)", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Boolean> add(@Valid @RequestBody SysPartnerPolicySaveReqVO createReqVO) {
        return success(sysPartnerPolicyService.saveSysPartnerPolicy(createReqVO));
    }

    /**
     * 修改平台商政策(由平台商设置)
     */
    @ApiOperation(value = "修改平台商政策(由平台商设置)", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台商政策(由平台商设置)", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysPartnerPolicySaveReqVO updateReqVO) {
        sysPartnerPolicyService.updateSysPartnerPolicy(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台商政策(由平台商设置)
     */
    @ApiOperation(value = "删除平台商政策(由平台商设置)", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台商政策(由平台商设置)", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    public CommonResult<Boolean> remove(@RequestBody List<String> partnerPolicyIds) {
        Long[] longPartnerPolicyIds = partnerPolicyIds.stream()
                .map(Long::parseLong)
                .toArray(Long[]::new);
        sysPartnerPolicyService.deleteSysPartnerPolicyByPartnerPolicyIds(longPartnerPolicyIds);
        return success(true);
    }

    /**
     * 获取平台商政策(由平台商设置)详细信息
     */
    @ApiOperation(value = "获得平台商政策(由平台商设置)详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @GetMapping(value = "/getInfo")
    public CommonResult<SysPartnerPolicyRespVO> getInfo(@RequestParam(required = false) Long SupplierId) {
        return success(sysPartnerPolicyService.getSysPartnerPolicy(SupplierId));
    }

    /**
     * 分页查询平台商政策(由平台商设置)
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商政策(由平台商设置)分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysPartnerPolicyRespVO>> getPage(@Valid SysPartnerPolicyPageReqVO pageReqVO) {
        PageResult<SysPartnerPolicy> pageResult = sysPartnerPolicyService.getSysPartnerPolicyPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, SysPartnerPolicyRespVO.class));
    }

    /**
     * 分页查询平台商搜索推荐分页列表
     */
    @GetMapping("/searchConfigList")
    @ApiOperation(value = "分页查询平台商搜索推荐分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    //@RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SearchConfigPageRespVO>> getSearchConfigPage(@Valid SysPartnerPolicyPageReqVO pageReqVO) {
        PageResult<SearchConfigPageRespVO> pageResult = sysPartnerPolicyService.selectSearchConfigPage(pageReqVO);
        return success(pageResult);
    }


    /**
     * 新增平台商搜索推荐
     */
    @ApiOperation(value = "新增平台商搜索推荐", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @Log(title = "平台商搜索推荐", businessType = BusinessType.INSERT)
    @PostMapping("/searchConfig")
    public CommonResult<Boolean> addSearchConfig(@Valid @RequestBody SearchConfigReqVO createReqVO) {
        return success(sysPartnerPolicyService.addSearchConfig(createReqVO));
    }

    /**
     * 编辑平台商搜索推荐
     */
    @ApiOperation(value = "编辑平台商搜索推荐", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台商搜索推荐", businessType = BusinessType.UPDATE)
    @PutMapping("/searchConfig")
    public CommonResult<Boolean> editSearchConfig(@Valid @RequestBody SearchConfigReqVO updateReqVO) {
        sysPartnerPolicyService.updateSearchConfig(updateReqVO);
        return success(true);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:partnerPolicy:add";
        /** 编辑 */
        public static final String EDIT = "system:partnerPolicy:edit";
        /** 删除 */
        public static final String DELETE = "system:partnerPolicy:remove";
        /** 列表 */
        public static final String LIST = "system:partnerPolicy:list";
        /** 查询 */
        public static final String GET = "system:partnerPolicy:query";
        /** 查询 */
        public static final String GET_DC_CONFIG = "system:partnerPolicy:query-dc";
        /** 停用 */
        public static final String DISABLE = "system:partnerPolicy:disable";
        /** 启用 */
        public static final String ENABLE = "system:partnerPolicy:enable";
    }
}
