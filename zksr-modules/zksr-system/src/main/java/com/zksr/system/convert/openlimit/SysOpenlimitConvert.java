package com.zksr.system.convert.openlimit;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysOpenlimit;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitRespVO;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 开发能力qps 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-27
*/
@Mapper
public interface SysOpenlimitConvert {

    SysOpenlimitConvert INSTANCE = Mappers.getMapper(SysOpenlimitConvert.class);

    SysOpenlimitRespVO convert(SysOpenlimit sysOpenlimit);

    SysOpenlimit convert(SysOpenlimitSaveReqVO sysOpenlimitSaveReq);

    PageResult<SysOpenlimitRespVO> convertPage(PageResult<SysOpenlimit> sysOpenlimitPage);
}
