package com.zksr.system.controller.openlimit.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 开发能力qps对象 sys_openlimit
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Data
@ApiModel("开发能力qps - sys_openlimit分页 Request VO")
public class SysOpenlimitSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 开放资源id */
    @Excel(name = "开放资源id")
    @ApiModelProperty(value = "开放资源id", required = true)
    private Long opensourceId;

    /** 开放能力id */
    @Excel(name = "开放能力id")
    @ApiModelProperty(value = "开放能力id", required = true)
    private Long openabilityId;

    /** qps限制（每秒多少次请求） */
    @Excel(name = "qps限制", readConverterExp = "每=秒多少次请求")
    @ApiModelProperty(value = "qps限制")
    private Integer rateLimit;

}
