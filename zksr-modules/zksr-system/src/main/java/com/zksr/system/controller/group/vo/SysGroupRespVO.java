package com.zksr.system.controller.group.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 平台商城市分组对象 sys_group
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Data
@ApiModel("平台商城市分组 - sys_group Response VO")
public class SysGroupRespVO {
    private static final long serialVersionUID = 1L;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long groupId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 平台商城市分组名 */
    @Excel(name = "平台商城市分组名")
    @ApiModelProperty(value = "平台商城市分组名", example = "示例值")
    private String groupName;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;

}
