package com.zksr.system.controller.supplierArea.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 入驻商-区域城市关联关系对象 sys_supplier_area
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("入驻商-区域城市关联关系 - sys_supplier_area分页 Request VO")
public class SysSupplierAreaSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true,   example = "示例值")
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id", required = true,   example = "示例值")
    private Long supplierId;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id", required = true,   example = "示例值")
    private Long areaId;

}
