package com.zksr.system.controller.sms;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysSmsTemplate;
import com.zksr.system.service.ISysSmsTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.sms.vo.SysSmsTemplatePageReqVO;
import com.zksr.system.controller.sms.vo.SysSmsTemplateSaveReqVO;
import com.zksr.system.controller.sms.vo.SysSmsTemplateRespVO;
import com.zksr.system.convert.template.SysSmsTemplateConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 短信模版配置Controller
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@Api(tags = "管理后台 - 短信模版配置接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/template")
public class SysSmsTemplateController {
    @Autowired
    private ISysSmsTemplateService sysSmsTemplateService;

    /**
     * 新增短信模版配置
     */
    @ApiOperation(value = "新增短信模版配置", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "短信模版配置", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysSmsTemplateSaveReqVO createReqVO) {
        return success(sysSmsTemplateService.insertSysSmsTemplate(createReqVO));
    }

    /**
     * 修改短信模版配置
     */
    @ApiOperation(value = "修改短信模版配置", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "短信模版配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysSmsTemplateSaveReqVO updateReqVO) {
            sysSmsTemplateService.updateSysSmsTemplate(updateReqVO);
        return success(true);
    }

    /**
     * 删除短信模版配置
     */
    @ApiOperation(value = "删除短信模版配置", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "短信模版配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{smsTemplateIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] smsTemplateIds) {
        sysSmsTemplateService.deleteSysSmsTemplateBySmsTemplateIds(smsTemplateIds);
        return success(true);
    }

    /**
     * 获取短信模版配置详细信息
     */
    @ApiOperation(value = "获得短信模版配置详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{smsTemplateId}")
    public CommonResult<SysSmsTemplateRespVO> getInfo(@PathVariable("smsTemplateId") Long smsTemplateId) {
        SysSmsTemplate sysSmsTemplate = sysSmsTemplateService.getSysSmsTemplate(smsTemplateId);
        return success(SysSmsTemplateConvert.INSTANCE.convert(sysSmsTemplate));
    }

    /**
     * 分页查询短信模版配置
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得短信模版配置分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysSmsTemplateRespVO>> getPage(@Valid SysSmsTemplatePageReqVO pageReqVO) {
        PageResult<SysSmsTemplate> pageResult = sysSmsTemplateService.getSysSmsTemplatePage(pageReqVO);
        return success(SysSmsTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 停用短信模版
     */
    @ApiOperation(value = "停用短信模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "短信模版", businessType = BusinessType.UPDATE)
    @PutMapping(Permissions.DISABLE)
    public CommonResult<Boolean> disable(@ApiParam(name = "smsTemplateId", value = "短信模版ID", required = true) @RequestParam("smsTemplateId") Long smsTemplateId) {
        sysSmsTemplateService.disable(smsTemplateId);
        return success(true);
    }

    /**
     * 启用短信模版
     */
    @ApiOperation(value = "启用短信模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "短信模版", businessType = BusinessType.UPDATE)
    @PutMapping(Permissions.ENABLE)
    public CommonResult<Boolean> enable(@ApiParam(name = "smsTemplateId", value = "短信模版ID", required = true) @RequestParam("smsTemplateId") Long smsTemplateId) {
        sysSmsTemplateService.enable(smsTemplateId);
        return success(true);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:sms-template:add";
        /** 编辑 */
        public static final String EDIT = "system:sms-template:edit";
        /** 删除 */
        public static final String DELETE = "system:sms-template:remove";
        /** 列表 */
        public static final String LIST = "system:sms-template:list";
        /** 查询 */
        public static final String GET = "system:sms-template:query";
        /** 停用 */
        public static final String DISABLE = "system:sms-template:disable";
        /** 启用 */
        public static final String ENABLE = "system:sms-template:enable";
    }
}
