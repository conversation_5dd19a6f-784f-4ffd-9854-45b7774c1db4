package com.zksr.system.controller.brand;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.controller.brand.vo.SysBrandMerchantPageReqVO;
import com.zksr.system.api.brand.vo.SysBrandMerchantRespVO;
import com.zksr.system.controller.brand.vo.SysBrandMerchantSaveReqVO;
import com.zksr.system.convert.brand.SysBrandMerchantConvert;
import com.zksr.system.domain.SysBrandMember;
import com.zksr.system.domain.SysBrandMerchant;
import com.zksr.system.service.ISysBrandMemberService;
import com.zksr.system.service.ISysBrandMerchantService;
import com.zksr.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.Arrays;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 品牌商资料Controller
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Api(tags = "管理后台 - 品牌商资料接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/brandMerchant")
public class SysBrandMerchantController {
    @Autowired
    private ISysBrandMerchantService sysBrandMerchantService;

    @Autowired
    private ISysBrandMemberService sysBrandMemberService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 新增品牌商资料
     */
    @ApiOperation(value = "新增品牌商资料", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "品牌商资料", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysBrandMerchantSaveReqVO createReqVO) {
        return success(sysBrandMerchantService.insertSysBrandMerchant(createReqVO));
    }

    /**
     * 修改品牌商资料
     */
    @ApiOperation(value = "修改品牌商资料", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "品牌商资料", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysBrandMerchantSaveReqVO updateReqVO) {
        sysBrandMerchantService.updateSysBrandMerchant(updateReqVO);
        return success(true);
    }

    /**
     * 删除品牌商资料
     */
    @ApiOperation(value = "删除品牌商资料", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "品牌商资料", businessType = BusinessType.DELETE)
    @DeleteMapping("/{brandMerchantIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] brandMerchantIds) {
        sysBrandMerchantService.deleteSysBrandMerchantByBrandMerchantIds(brandMerchantIds);
        return success(true);
    }

    /**
     * 获取品牌商资料详细信息
     */
    @ApiOperation(value = "获得品牌商资料详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{brandMerchantId}")
    public CommonResult<SysBrandMerchantRespVO> getInfo(@PathVariable("brandMerchantId") Long brandMerchantId) {
        SysBrandMerchant sysBrandMerchant = sysBrandMerchantService.getSysBrandMerchant(brandMerchantId);
        SysBrandMerchantRespVO respVO = SysBrandMerchantConvert.INSTANCE.convert(sysBrandMerchant);
        respVO.setUsername(sysUserService.getBySysUserId(sysBrandMerchant.getSysUserId()));
        return success(respVO);
    }

    /**
     * 分页查询品牌商资料
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得品牌商资料分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysBrandMerchantRespVO>> getPage(@Valid SysBrandMerchantPageReqVO pageReqVO) {
        PageResult<SysBrandMerchant> pageResult = sysBrandMerchantService.getSysBrandMerchantPage(pageReqVO);
        return success(SysBrandMerchantConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 获取当前账号品牌商信息
     * @return
     */
    @GetMapping("userinfo")
    @ApiOperation(value = "获取当前账号品牌商信息", httpMethod = HttpMethod.GET)
    public CommonResult<SysBrandMerchantRespVO> brandInfo() {
        SysBrandMerchant sysBrandMerchant = sysBrandMerchantService.getSysBrandMerchantByUserId(SecurityUtils.getUserId());
        SysBrandMember sysBrandMember = sysBrandMemberService.getSysBrandMemberByUserId(SecurityUtils.getUserId());
        if (Objects.isNull(sysBrandMerchant) && Objects.nonNull(sysBrandMember)) {
            sysBrandMerchant = sysBrandMerchantService.getSysBrandMerchant(sysBrandMember.getBrandMerchantId());
        }
        SysBrandMerchantRespVO respVO = SysBrandMerchantConvert.INSTANCE.convert(sysBrandMerchant);
        if (Objects.nonNull(sysBrandMember)){
            respVO.setContactName(sysBrandMember.getContactName());
        }
        SysUser sysUser = sysUserService.getById(SecurityUtils.getUserId());
        respVO.setUsername(sysUser.getUserName());
        respVO.setAvatar(sysUser.getAvatar());
        return success(respVO);
    }

    /**
     * 停用品牌商
     */
    @ApiOperation(value = "停用品牌商", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "停用品牌商", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public CommonResult<Boolean> disable(@ApiParam(name = "brandMerchantId", value = "品牌商ID", required = true) @RequestParam("brandMerchantId") Long brandMerchantId) {
        sysBrandMerchantService.disable(brandMerchantId);
        return success(true);
    }

    /**
     * 启用品牌商
     */
    @ApiOperation(value = "启用品牌商", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "启用品牌商", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public CommonResult<Boolean> enable(@ApiParam(name = "brandMerchantId", value = "品牌商ID", required = true) @RequestParam("brandMerchantId") Long brandMerchantId) {
        sysBrandMerchantService.enable(brandMerchantId);
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "system:brand-merchant:add";
        /**
         * 编辑
         */
        public static final String EDIT = "system:brand-merchant:edit";
        /**
         * 删除
         */
        public static final String DELETE = "system:brand-merchant:remove";
        /**
         * 列表
         */
        public static final String LIST = "system:brand-merchant:list";
        /**
         * 查询
         */
        public static final String GET = "system:brand-merchant:query";
        /**
         * 停用
         */
        public static final String DISABLE = "system:brand-merchant:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "system:brand-merchant:enable";
    }
}
