package com.zksr.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partner.vo.SysPartnerPageReqVO;
import com.zksr.system.api.partner.vo.SysPartnerRespVO;
import com.zksr.system.controller.partner.vo.SysPartnerSaveReqVO;
import com.zksr.system.domain.SysPartner;
import com.zksr.system.domain.SysSoftware;
import com.zksr.system.domain.Tenant;
import com.zksr.system.mapper.SysPartnerMapper;
import com.zksr.system.mapper.SysSoftwareMapper;
import com.zksr.system.mapper.SysUserMapper;
import com.zksr.system.mq.SystemMqProducer;
import com.zksr.system.service.ISysPartnerService;
import com.zksr.system.service.ISysRoleService;
import com.zksr.system.service.ISysUserService;
import com.zksr.system.tenant.SaasHelper;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 平台商信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Service
public class SysPartnerServiceImpl implements ISysPartnerService {
    @Autowired
    private SysPartnerMapper sysPartnerMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private Cache<String, PartnerDto> partnerDtoCache;

    @Autowired
    private SystemMqProducer systemAmapMqProducer;

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private SysSoftwareMapper sysSoftwareMapper;

    @Autowired
    private RedisService redisService;

    @Resource
    private SaasHelper saasHelper;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        //自动加载
        partnerDtoCache.config().setLoader(key -> getByCacheKey(key));
        //全量加载
        //loadingPartnerDtoCache();
    }

    /**
     * 新增平台商信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertSysPartner(SysPartnerSaveReqVO createReqVO) {
        // 新增的时候由自动键生成器生成
        createReqVO.setSysCode(null);

        //校验saas租户是否存在 且该租户在b2b唯一
        checkSaasTenantCode(createReqVO);

        //绑定软件商
        SysSoftware sysSoftware =sysSoftwareMapper.getBySysUserId(SecurityUtils.getUserId());
        if(ToolUtil.isNotEmpty(sysSoftware)){
            createReqVO.setSoftwareId(sysSoftware.getSoftwareId());
        }
        // 插入
        SysPartner sysPartner = HutoolBeanUtils.toBean(createReqVO, SysPartner.class);

        // 查看编号是否冲突
        Integer count = sysPartnerMapper.selectByPartnerCode(createReqVO.getPartnerCode());
        if (count > 0) {
            throw exception(PARTNER_CODE_EXISTS);
        }
        // 新建合伙人超级管理员账号
        String partnerAccount = createReqVO.getPartnerAccount();
        String partnerAccountPwd = createReqVO.getPartnerAccountPwd();

        SysUser user = new SysUser();
        // 完善账号信息
        user.setNickName(saasAuthSwitch ? createReqVO.getContactName() : createReqVO.getPartnerName());
        user.setUserName(partnerAccount);
        user.setStatus("0");
        user.setPassword(partnerAccountPwd);
        user.setPhonenumber(sysPartner.getContactPhone());
        user.setRemark(sysPartner.getPartnerName()+"默认管理员用户");

        if (!userService.checkUserNameUnique(user)) {
            throw exception(USERNAME_EXISTS);
        } else if (ToolUtil.isNotEmpty(user.getPhonenumber())
                && !userService.checkPhoneUnique(user)) {
            throw exception(MOBILE_EXISTS);
        }
        else if (ToolUtil.isNotEmpty(user.getEmail())
                && !userService.checkEmailUnique(user)) {
            throw exception(EMAIL_EXISTS);
        } else if (!saasAuthSwitch &&!Pattern.matches(UserConstants.PASSWORD_PATTERN, user.getPassword())) {
            throw exception(PASSWORD_FIAL);
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(saasAuthSwitch ? "" : SecurityUtils.encryptPassword(user.getPassword())); //兼容原中科
        saasHelper.saasAuthUserAdd(sysPartner.getContactPhone(), user.getNickName(), createReqVO.getSaasTenantCode() ,user);
        int rows = userMapper.insertUser(user);

        // 新建合伙人
        sysPartner.setPartnerUserId(user.getUserId());
        sysPartner.setSource(this.generateSysSource(8));
        //生产source

        this.sysPartnerMapper.insert(sysPartner);

        Long sysCode = sysPartner.getSysCode();

        //新建自己的管理员角色
        Long roleId = roleService.createPartnerAdminRole(sysCode, sysPartner.getPartnerName());

        Long[] roleIds = {roleId};// 自动赋管理员权限
        user.setRoleIds(roleIds);
        user.setSysCode(sysCode);
        userMapper.updateUser(user);
        userService.insertUserRole(user);

        //发送mq 新建猎鹰服务
        //systemAmapMqProducer.addService(sysPartner);
        // 保存平台商商户信息
        savePayPlatformMerchant(createReqVO, sysPartner.getSoftwareId(), sysPartner.getSysCode());

        if(ToolUtil.isNotEmpty(createReqVO.getSoftwareId())){
            //刷新sysCode
            LoginUser loginUser = SecurityUtils.getLoginUser();
            String userkey = CacheConstants.LOGIN_TOKEN_KEY+ SecurityUtils.getUserKey();
            loginUser.getSysCodeList().add(sysPartner.getSysCode());
            redisService.setCacheObject(userkey, loginUser, CacheConstants.EXPIRATION, TimeUnit.MINUTES);
        }

        // 返回
        return sysPartner.getSysCode();
    }

    /**
     * 校验租户编码是否存在
     * @param createReqVO
     */
    private void checkSaasTenantCode(SysPartnerSaveReqVO createReqVO) {
        if(saasAuthSwitch){ //开启开关后判断是saas租户
            if(!Objects.equals(createReqVO.getPartnerAccount(), createReqVO.getContactPhone())){
                throw new ServiceException("联系电话和平台商账号必须相同");
            }
            String saasTenantCode = createReqVO.getSaasTenantCode();
            if(StrUtil.isBlank(saasTenantCode)) throw exception(SAAS_TENANT_CODE_IS_NULL);
            LambdaQueryWrapper<SysPartner> query = new LambdaQueryWrapper<>();
            query.eq(SysPartner::getSaasTenantCode, saasTenantCode);
            if(CollectionUtil.isNotEmpty(sysPartnerMapper.selectList(query))) throw exception(SAAS_TENANT_CODE_EXISTS);

            //判断查询tenantCode是否存在
            Tenant saasTenant = saasHelper.saasSelectOneTenant(saasTenantCode);
            if (Objects.isNull(saasTenant)) {
                throw new ServiceException(String.format("SaaS系统不存在该租户%s，请先到安链云SaaS运营平台创建租户！",saasTenantCode));
            }
            if (!Objects.equals(saasTenant.getEnableFlag(),1)) {
                throw new ServiceException(String.format("SaaS系统该租户%s不是启用状态，请先到安链云SaaS运营平台启用租户！",saasTenantCode));
            }
        }
    }

    /**
     * 修改平台商信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional
    public void updateSysPartner(SysPartnerSaveReqVO updateReqVO) {
        sysPartnerMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysPartner.class));
        SysPartner sysPartner =sysPartnerMapper.selectById(updateReqVO.getSysCode());
        // 保存平台商商户信息
        savePayPlatformMerchant(updateReqVO, sysPartner.getSoftwareId(), updateReqVO.getSysCode());
        //修改用户信息
        if(ToolUtil.isNotEmpty(updateReqVO.getPartnerUserId())) {
            SysUser sysUser = userMapper.selectUserById(updateReqVO.getPartnerUserId());
            if (ToolUtil.isEmpty(sysUser)) {
                throw exception(USERNAME_NOT_EXISTS);
            }
            if(!saasAuthSwitch){ //只有原中科的允许更改手机号和密码
                if (org.apache.commons.lang3.StringUtils.isNotBlank(updateReqVO.getPartnerAccountPwd())) {
                    if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, updateReqVO.getPartnerAccountPwd())) {
                        throw exception(PASSWORD_FIAL);
                    }
                    sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPartnerAccountPwd().trim()));
                }
                sysUser.setPhonenumber(updateReqVO.getContactPhone());
            }
            sysUser.setNickName(saasAuthSwitch ? updateReqVO.getContactName() : updateReqVO.getPartnerName());
            sysUser.setStatus(Objects.equals(NumberPool.INT_TWO, updateReqVO.getStatus()) ? StringPool.ZERO : StringPool.ONE);
            sysUser.setUpdateBy(SecurityUtils.getUsername());
            userService.updateUserV2(sysUser);
        }
    }

    /**
     * 删除平台商信息
     *
     * @param sysCode 平台商id
     */
    @Override
    public void deleteSysPartner(Long sysCode) {
        // 删除
            sysPartnerMapper.deleteById(sysCode);
    }

    /**
     * 批量删除平台商信息
     *
     * @param sysCodes 需要删除的平台商信息主键
     * @return 结果
     */
    @Override
    public void deleteSysPartnerBySysCodes(Long[] sysCodes) {
        for(Long sysCode : sysCodes){
            this.deleteSysPartner(sysCode);
        }
    }

    /**
     * 获得平台商信息
     *
     * @param sysCode 平台商id
     * @return 平台商信息
     */
    @Override
    public SysPartner getSysPartner(Long sysCode) {
        if(null == sysCode){
            return null;
        }
        SysPartner sysPartner = sysPartnerMapper.selectById(sysCode);
        if(null == sysPartner){
            return null;
        }
        sysPartner.setUserName(userMapper.getBySysUserId(sysPartner.getPartnerUserId()));
        if(sysPartner.getSoftwareRate() == BigDecimal.ZERO) {
            if (ToolUtil.isNotEmpty(sysPartner.getSoftwareId()) && sysPartner.getSoftwareId() > 0) {
                sysPartner.setSoftwareRate(sysSoftwareMapper.selectById(sysPartner.getSoftwareId()).getSoftwareRate());
            }
        }
        return sysPartner;
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysPartner> getSysPartnerPage(SysPartnerPageReqVO pageReqVO) {
        return sysPartnerMapper.selectPage(pageReqVO);
    }

    /**
     * 查询数据
     * @param pageReqVO 分页查询
     * @return
     */
    @Override
    public PageResult<SysPartnerRespVO> getSysPartnerList(SysPartnerPageReqVO pageReqVO){
        PageResult<SysPartnerRespVO> result = new PageResult<>();

        // 分页参数
        int pageNo = pageReqVO.getPageNo();
        int pageSize = pageReqVO.getPageSize();
        int offset = (pageNo - 1) * pageSize;

        pageReqVO.setPageNo(offset);
        pageReqVO.setPageSize(pageSize);

        // 查询总记录数
        Long total = sysPartnerMapper.countSelectSysPartnerList(pageReqVO);
        if (total == 0L) {
            result.setList(Collections.emptyList());
            result.setTotal(0L);
            return result;
        }

        List<SysPartner> sysPartnerList = sysPartnerMapper.selectSysPartnerList(pageReqVO);
        List<SysPartnerRespVO> sysPartnerRespList = HutoolBeanUtils.toBean(sysPartnerList, SysPartnerRespVO.class);

        result.setList(sysPartnerRespList);
        result.setTotal(total);
        return result;
    }

    @Override
    @Transactional
    public void disable(Long sysCode) {
        SysPartner sysPartner = sysPartnerMapper.selectById(sysCode);
        sysPartner.setUpdateBy(SecurityUtils.getUsername());
        sysPartner.setStatus(3);
        sysPartnerMapper.updateById(sysPartner);

        SysUser sysUser = userService.selectUserById(sysPartner.getPartnerUserId());
        if (Objects.nonNull(sysUser)) {
            sysUser.setStatus(StringPool.ONE);
            userService.updateUserStatus(sysUser);
        }
    }

    @Override
    @Transactional
    public void enable(Long sysCode) {
        SysPartner sysPartner = sysPartnerMapper.selectById(sysCode);
        sysPartner.setUpdateBy(SecurityUtils.getUsername());
        sysPartner.setStatus(2);
        sysPartnerMapper.updateById(sysPartner);

        SysUser sysUser = userService.selectUserById(sysPartner.getPartnerUserId());
        if (Objects.nonNull(sysUser)) {
            sysUser.setStatus(StringPool.ONE);
            userService.updateUserStatus(sysUser);
        }
    }

    @Override
    public void reloadPartnerDtoCache(Long cacheKey) {
        SysPartner sysPartner = sysPartnerMapper.getByCacheKey(cacheKey+"");
        PartnerDto partnerDto = HutoolBeanUtils.toBean(sysPartner, PartnerDto.class);
        partnerDtoCache.put(cacheKey+"", partnerDto);
        partnerDtoCache.put(sysPartner.getSource(), partnerDto);
    }

    @Override
    public PartnerDto getByCacheKey(String cacheKey) {
        SysPartner sysPartner = sysPartnerMapper.getByCacheKey(cacheKey);
        PartnerDto partnerDto = HutoolBeanUtils.toBean(sysPartner, PartnerDto.class);
        return partnerDto;
    }

    @Override
    public List<SysPartner> getPartnerInfo() {
        return sysPartnerMapper.selectList();
    }

    @Override
    public List<Long> getSysCodeListBySoftwareId(Long softwareId) {
        return sysPartnerMapper.getSysCodeListBySoftwareId(softwareId);
    }

    private void loadingPartnerDtoCache() {
        List<SysPartner> partnerList = sysPartnerMapper.selectList();
        for (SysPartner sysPartner : partnerList) {
            PartnerDto partnerDto = HutoolBeanUtils.toBean(sysPartner, PartnerDto.class);
            partnerDtoCache.put(sysPartner.getSysCode()+"", partnerDto);
            partnerDtoCache.put(sysPartner.getSource(), partnerDto);
        }
    }

    public String generateSysSource(int i) {
        String sysSource = RandomStringUtils.random(i, true, true);
        return sysSource;

        //TODO
//        while (true){
//            String sysSource = RandomStringUtils.random(i, true, true);
//            QueryWrapper<SysPartner> queryWrapper = new QueryWrapper();
//            queryWrapper.eq("source", sysSource);
//            List<SysPartner> list = this.list(queryWrapper);
//            if(ToolUtil.isEmpty(list)){
//                return sysSource;
//            }
//        }
    }

    /**
     * 保存平台商户信息
     *
     * @param saveReqVO  保存数据
     * @param sysCode    平台商ID
     */
    private void savePayPlatformMerchant(SysPartnerSaveReqVO saveReqVO, Long softwareId, Long sysCode) {
        if (ObjectUtil.isNotEmpty(saveReqVO.getPlatformSimpleBindList())) {
            for (PlatformSimpleBindVO simpleBindVO : saveReqVO.getPlatformSimpleBindList()) {
                PlatformMerchantDTO merchant = BeanUtils.toBean(simpleBindVO, PlatformMerchantDTO.class);
                merchant.setMerchantId(sysCode)
                        .setMerchantType(simpleBindVO.getMerchantType().getType())
                        .setSysCode(sysCode);
                // 如果是软件商调整使用软件商ID
                if (simpleBindVO.getMerchantType() == MerchantTypeEnum.SOFTWARE) {
                    merchant.setMerchantId(softwareId);
                }
                platformMerchantApi.savePlatformMerchant(merchant).checkError();
            }
        }
    }

    public SysPartner getSysPartnerBySource(String source) {
        return sysPartnerMapper.selectOne(new LambdaQueryWrapper<SysPartner>().eq(SysPartner::getSource,source));
    }

    @Override
    public void updateSysPartnerPassword(SysPartnerSaveReqVO updateReqVO) {
        if(ToolUtil.isNotEmpty(updateReqVO.getPartnerUserId())) {
            SysUser sysUser = userMapper.selectUserById(updateReqVO.getPartnerUserId());
            if (ToolUtil.isEmpty(sysUser)) {
                throw exception(USERNAME_NOT_EXISTS);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(updateReqVO.getPartnerAccountPwd())) {
                if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, updateReqVO.getPartnerAccountPwd())) {
                    throw exception(PASSWORD_FIAL);
                }
                sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPartnerAccountPwd().trim()));
            }
            sysUser.setUpdateBy(SecurityUtils.getUsername());
            userService.updateUserV2(sysUser);
        }
    }

    public SysPartner getBySaasTenantCode(String saasTenantCode) {
        return sysPartnerMapper.selectOne(new LambdaQueryWrapper<SysPartner>().eq(SysPartner::getSaasTenantCode,saasTenantCode));
    }
}
