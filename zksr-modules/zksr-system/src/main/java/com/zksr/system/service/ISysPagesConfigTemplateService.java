package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysPagesConfigTemplate;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplatePageReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplateSaveReqVO;

/**
 * 平台页面配置模版Service接口
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
public interface ISysPagesConfigTemplateService {

    /**
     * 新增平台页面配置模版
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysPagesConfigTemplate(@Valid SysPagesConfigTemplateSaveReqVO createReqVO);

    /**
     * 修改平台页面配置模版
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysPagesConfigTemplate(@Valid SysPagesConfigTemplateSaveReqVO updateReqVO);

    /**
     * 删除平台页面配置模版
     *
     * @param pageId 自定义页面ID
     */
    public void deleteSysPagesConfigTemplate(Long pageId);

    /**
     * 批量删除平台页面配置模版
     *
     * @param pageIds 需要删除的平台页面配置模版主键集合
     * @return 结果
     */
    public void deleteSysPagesConfigTemplateByPageIds(Long[] pageIds);

    /**
     * 获得平台页面配置模版
     *
     * @param pageId 自定义页面ID
     * @return 平台页面配置模版
     */
    public SysPagesConfigTemplate getSysPagesConfigTemplate(Long pageId);

    /**
     * 获得平台页面配置模版分页
     *
     * @param pageReqVO 分页查询
     * @return 平台页面配置模版分页
     */
    PageResult<SysPagesConfigTemplate> getSysPagesConfigTemplatePage(SysPagesConfigTemplatePageReqVO pageReqVO);

    /**
     * 修改状态
     * @param updateReqVO
     */
    void updateStatus(SysPagesConfigTemplateSaveReqVO updateReqVO);
}
