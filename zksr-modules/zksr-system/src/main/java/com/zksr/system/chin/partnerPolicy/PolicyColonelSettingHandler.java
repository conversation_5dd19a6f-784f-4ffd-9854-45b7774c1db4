package com.zksr.system.chin.partnerPolicy;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.system.api.partnerPolicy.dto.ColonelSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.enums.PartnerPolicyEnum;
import com.zksr.system.chin.general.PartnerConfigUtil;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyRespVO;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicySaveReqVO;
import com.zksr.system.domain.SysPartnerPolicy;
import com.zksr.system.mapper.SysPartnerPolicyMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class PolicyColonelSettingHandler extends PolicyPartnerChin {
    @Autowired
    private SysPartnerPolicyMapper sysPartnerPolicyMapper;

    @Autowired
    private Cache<Long, ColonelSettingPolicyDTO> colonelSettingPolicyDTOCache;

    private static final String key = "colonelSetting.";

    @Override
    public void saveConfig(SysPartnerPolicySaveReqVO sysPartnerPolicySaveReqVO, Long sysCode, Long dcId, Long supplierId) {
        if (ObjectUtil.isNotNull(sysPartnerPolicySaveReqVO.getColonelSettingPolicyDTO())) {
            //获取参数数据集合
            List<SysPartnerPolicy> sysPartnerPolicys =
                    PartnerConfigUtil.jointPolicyParam(sysCode, dcId, supplierId, key, sysPartnerPolicySaveReqVO.getColonelSettingPolicyDTO());
            //根据平台ID 获取所有配置信息
            List<SysPartnerPolicy> policyList = sysPartnerPolicyMapper.selectBySysCode(sysCode, dcId, supplierId);
            //需要修改的数据
            List<SysPartnerPolicy> updateList = sysPartnerPolicys
                    .stream()
                    .filter(a -> policyList.stream().anyMatch(b -> ObjectUtil.equal(a.getPolicyKey(), b.getPolicyKey())))
                    .map(a -> {
                        for (SysPartnerPolicy sysPartnerPolicy : policyList) {
                            if (ObjectUtil.equal(sysPartnerPolicy.getPolicyKey(), a.getPolicyKey())) {
                                a.setPartnerPolicyId(sysPartnerPolicy.getPartnerPolicyId());
                                return a;
                            }
                        }
                        return a;
                    })
                    .collect(Collectors.toList());
            //需要新增的数据
            List<SysPartnerPolicy> insertList = sysPartnerPolicys
                    .stream()
                    .filter(a -> policyList.stream().noneMatch(b -> ObjectUtil.equal(a.getPolicyKey(), b.getPolicyKey())))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(updateList)) {
                sysPartnerPolicyMapper.updateBatch(updateList);
            }
            if (ObjectUtil.isNotEmpty(insertList)) {
                sysPartnerPolicyMapper.insertBatch(insertList);
            }
            //删除缓存
            colonelSettingPolicyDTOCache.remove(dcId);
        }
        if (ObjectUtil.isNotNull(next)) {
            next.saveConfig(sysPartnerPolicySaveReqVO, sysCode, dcId, supplierId);
        }
    }

    @Override
    public SysPartnerPolicyRespVO getConfig(SysPartnerPolicyRespVO sysPartnerPolicyRespVO, Long sysCode, Long dcId, Long supplierId, Integer type) {
        if (ObjectUtil.isNull(sysPartnerPolicyRespVO.getColonelSettingPolicyDTO()) && (ObjectUtil.isNull(type) || ObjectUtil.equal(PartnerPolicyEnum.COLONEL_SETTING_POLICY.getType(), type))) {
            List<SysPartnerPolicy> configList = sysPartnerPolicyMapper.selectBySysCode(null, dcId, null);
            List<SysPartnerPolicy> partnerList = PartnerConfigUtil.matchingPolicyKey(configList, key);
            if (ObjectUtil.isNotEmpty(partnerList)) {
                ColonelSettingPolicyDTO colonelSettingPolicyDto = PartnerConfigUtil.encapsulationPolicyObj(partnerList, key, ColonelSettingPolicyDTO.class);
                sysPartnerPolicyRespVO.setColonelSettingPolicyDTO(colonelSettingPolicyDto);
                //同步缓存
                colonelSettingPolicyDTOCache.put(dcId, colonelSettingPolicyDto);
            }
        }
        if (ObjectUtil.isNotNull(next)) {
            next.getConfig(sysPartnerPolicyRespVO, sysCode, dcId, supplierId, type);
        }
        return sysPartnerPolicyRespVO;
    }

}
