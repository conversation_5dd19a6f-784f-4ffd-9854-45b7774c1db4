package com.zksr.system.controller.area;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.dto.AreaIsExistDTO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.controller.area.excel.SysAreaImportExcel;
import com.zksr.system.controller.area.vo.*;
import com.zksr.system.domain.SysArea;
import com.zksr.system.service.ISysAreaService;
import com.zksr.system.service.ISysDcAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 区域城市Controller
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@Api(tags = "管理后台 - 区域城市接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/area")
public class SysAreaController {
    @Autowired
    private ISysAreaService sysAreaService;

    @Autowired
    private ISysDcAreaService sysDcAreaService;

    @Resource
    private AreaCityApi areaCityApi;

    /**
     * @Description: 获取所有区域城市
     * @Param: Long areaId 非必传 传入后返回列表会剔除当前ID
     * @return: CommonResult<List < SysAreaRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/15 17:48
     */
    @ApiOperation(value = "获取所有区域城市", httpMethod = HttpMethod.GET)
    @GetMapping("/getAreaList")
    @DataScope(dcAlias = "sa")
    public CommonResult<List<SysAreaRespVO>> getAreaList(AreaListReqVO reqVO) {
        return success(HutoolBeanUtils.toBean(sysAreaService.getAreaList(reqVO), SysAreaRespVO.class));
    }

    /**
     * @Description: 获取可绑定的区域城市
     * @Param: Long dcId
     * @return: CommonResult<List < SysAreaRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/7 14:40
     */
    @ApiOperation(value = "运营商获取可绑定的区域城市", httpMethod = HttpMethod.GET)
    @GetMapping("/getNotBindArea")
    public CommonResult<List<SysAreaRespVO>> getDcNotBindArea(@ApiParam(name = "dcId", value = "运营商ID")
                                                                  @RequestParam(required = false) Long dcId) {
        return success(sysAreaService.getDcNotBindArea(dcId));
    }

    /**
     * 新增区域城市
     */
    @ApiOperation(value = "新增区域城市", httpMethod = "POST", notes = "system:area:add")
    @RequiresPermissions("system:area:add")
    @Log(title = "区域城市", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysAreaSaveReqVO createReqVO) {

        Long res = sysAreaService.insertSysArea(createReqVO);
        sysAreaService.reloadAreaDtoCache(res);
        sysDcAreaService.reloadCache(createReqVO.getDcId());
        return success(res);
    }

    /**
     * 修改区域城市
     */
    @ApiOperation(value = "修改区域城市", httpMethod = "PUT", notes = "system:area:edit")
    @RequiresPermissions("system:area:edit")
    @Log(title = "区域城市", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysAreaSaveReqVO updateReqVO) {
        sysAreaService.updateSysArea(updateReqVO);
        sysAreaService.reloadAreaDtoCache(updateReqVO.getAreaId());
        sysDcAreaService.reloadCache(updateReqVO.getDcId());
        return success(true);
    }

    /**
     * 删除区域城市
     */
    @ApiOperation(value = "删除区域城市", httpMethod = "DELETE", notes = "system:area:remove")
    @RequiresPermissions("system:area:remove")
    @Log(title = "区域城市", businessType = BusinessType.DELETE)
    @DeleteMapping("/{areaIds}")
    public CommonResult<AreaIsExistDTO> remove(@PathVariable Long[] areaIds) {
        AreaIsExistDTO areaIsExistDTO = sysAreaService.deleteSysAreaByAreaIds(areaIds);
        for (Long areaId : areaIds) {
            sysAreaService.removeAreaDtoCache(areaId);
        }
        return success(areaIsExistDTO);
    }

    /**
     * 获取区域城市详细信息
     */
    @ApiOperation(value = "获得区域城市详情", httpMethod = "GET", notes = "system:area:query")
    @RequiresPermissions("system:area:query")
    @GetMapping(value = "/{areaId}")
    public CommonResult<SysAreaRespVO> getInfo(@PathVariable("areaId") Long areaId) {
        SysArea sysArea = sysAreaService.getSysArea(areaId);
        SysAreaRespVO respVO = HutoolBeanUtils.toBean(sysArea, SysAreaRespVO.class);
        if (Objects.nonNull(sysArea) && Objects.nonNull(sysArea.getThreeAreaCityId())) {
            SysAreaCityRespVO three = areaCityApi.getById(sysArea.getThreeAreaCityId()).getCheckedData();
            if (Objects.nonNull(three)) {
                SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
                SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();
                respVO.setSecondAreaCityId(second.getAreaCityId());
                respVO.setFirstAreaCityId(first.getAreaCityId());
            }
        }
        return success(respVO);
    }

    /**
     * 分页查询区域城市
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得区域城市分页列表", httpMethod = "GET", notes = "system:area:list")
    @RequiresPermissions("system:area:list")
    public CommonResult<PageResult<SysAreaRespVO>> getPage(@Valid SysAreaPageReqVO pageReqVO) {
        return success(sysAreaService.getSysAreaPage(pageReqVO));
    }

    /**
     * 获取区域选中数据 (用于选中回显)
     * @param areaIds    区域ID集合
     * @return  获取区域选中数据
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取城市简略信息", httpMethod = "POST")
    public CommonResult<List<SysAreaSelectedRespVO>> getSelectedBatchInfo(@Valid @Size(min = NumberPool.INT_ONE) @RequestBody List<Long> areaIds) {
        return success(sysAreaService.getSelectedSysArea(areaIds));
    }

    /**
     * 获取区域城市下拉列表
     * @param areaId 非必传 传入后返回列表会剔除当前ID
     * @return CommonResult<List < SysAreaRespVO>>
     */
    @ApiOperation(value = "获取所有区域城市", httpMethod = HttpMethod.GET)
    @GetMapping("/getAreaDownList")
    @DataScope(dcAlias = "sys_area")
    public CommonResult<List<SysAreaRespVO>> getAreaDownList(@ApiParam(name = "areaId", value = "区域城市ID, 非必传")
                                                         @RequestParam(value = "areaId", required = false) Long areaId) {
        return success(HutoolBeanUtils.toBean(sysAreaService.getAreaDownList(areaId), SysAreaRespVO.class));
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "区域城市导入模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<SysAreaImportExcel> util = new ExcelUtil<>(SysAreaImportExcel.class);
        String instructions = "填表说明：\n" +
                "1、省/市/区：必填项，格式要求：XXX省/XXX市/XXX区（县），不填或则数据没有和系统的数据字典值不一致则无法导入；\n" +
                "2、区域名称：必填项，在系统中展示的区域名称，不填则报错；\n" +
                "3、上级区域ID：非必填项，不填则为一级城市，填则需要和系统中的一级城市ID值保持一致，允许一次导入；\n" +
                "4、排序：非必填项，仅限数字，不填则按照导入自动生成排序；";
        util.importTemplateExcel(response, "区域城市导入", StringUtils.EMPTY, instructions);
    }
    @ApiOperation(value = "区域城市导入", httpMethod = HttpMethod.POST, notes = "system:area:list")
    @Log(title = "区域城市导入", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:area:import")
    @PostMapping("/importData")
    public CommonResult<String> importData(MultipartFile file) throws Exception {
        ExcelUtil<SysAreaImportExcel> util = new ExcelUtil<>(SysAreaImportExcel.class);
        List<SysAreaImportExcel> sysAreaList = util.importExcel(file.getInputStream(), 1);
        String message =  sysAreaService.impordData(sysAreaList);
        return success(message);
    }
}
