package com.zksr.system.mapper;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.database.query.QueryWrapperX;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigRespVO;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPagesConfig;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 平台页面配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Mapper
public interface SysPagesConfigMapper extends BaseMapperX<SysPagesConfig> {
    default PageResult<SysPagesConfig> selectPage(SysPagesConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysPagesConfig>()
                    .eqIfPresent(SysPagesConfig::getPageId, reqVO.getPageId())
                    .eqIfPresent(SysPagesConfig::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(SysPagesConfig::getPageType, reqVO.getPageType())
                    .eqIfPresent(SysPagesConfig::getPageConfig, reqVO.getPageConfig())
                    .eqIfPresent(SysPagesConfig::getChannelId, reqVO.getChannelId())
                    .eqIfPresent(SysPagesConfig::getAreaId, reqVO.getAreaId())
                    .eqIfPresent(SysPagesConfig::getDefFlag, reqVO.getDefFlag())
                    .eqIfPresent(SysPagesConfig::getStatus, reqVO.getStatus())
                    .eqIfPresent(SysPagesConfig::getDelFlag, DelFlagConstants.NORMAL)
                    .eqIfPresent(SysPagesConfig::getType, reqVO.getType())
                .orderByDesc(SysPagesConfig::getPageId));
    }

    List<SysPagesConfigRespVO> selectPageExt(@Param("req") SysPagesConfigPageReqVO pageReqVO);

    default List<SysPagesConfig> selectByEnableAreaIdAndChannelId(Long areaId, Long channelId) {
        LambdaQueryWrapper<SysPagesConfig> lambdaQueryWrapper = Wrappers.lambdaQuery(SysPagesConfig.class)
                .eq(SysPagesConfig::getAreaId, areaId)
                .eq(SysPagesConfig::getStatus, StringPool.ZERO)
                // 只验证一级页面就OK
                .eq(SysPagesConfig::getLevel, NumberPool.INT_ONE)
                .eq(SysPagesConfig::getDelFlag, NumberPool.INT_ZERO)
                .gt(SysPagesConfig::getEndTime, DateUtil.date())
                ;
        if (Objects.nonNull(channelId)) {
            lambdaQueryWrapper.last(StringUtils.format("AND FIND_IN_SET({}, channel_id)", channelId));
        } else {
            lambdaQueryWrapper.and(or -> or.isNull(SysPagesConfig::getChannelId).or().eq(SysPagesConfig::getChannelId, StringPool.EMPTY));
        }
        return selectList(lambdaQueryWrapper);
    }

    default SysPagesConfig selectDefaultBySysCode(Long sysCode) {
        return selectOne(Wrappers.lambdaQuery(SysPagesConfig.class)
                .eq(SysPagesConfig::getSysCode, sysCode)
                .eq(SysPagesConfig::getStatus, StringPool.ZERO)
                .eq(SysPagesConfig::getDefFlag, StringPool.ONE)
                .eq(SysPagesConfig::getDelFlag, NumberPool.INT_ZERO)
                .gt(SysPagesConfig::getEndTime, DateUtil.date())
                .last(StringPool.LIMIT_ONE)
        );
    }

    default Long selectEnableByAreaIdAndChannelId(Long pageId, Long areaId, String channelIdStr) {
        Long total = NumberPool.LONG_ZERO;
        SysPagesConfig pagesConfig = this.selectById(pageId);
        // 如果type=1, 需要验证, type=1的数据, 有没有时间重合的, 请调整下面的代码完善需求
        Integer type = pagesConfig.getType();
        Date startTime = pagesConfig.getStartTime();
        Date endTime = pagesConfig.getEndTime();
        //
        LambdaQueryWrapper<SysPagesConfig> lambdaQueryWrapper = new QueryWrapperX<SysPagesConfig>()
                .lambda()
                .ne(SysPagesConfig::getPageId, pageId)
                .eq(SysPagesConfig::getStatus, StringPool.ZERO)
                .eq(SysPagesConfig::getDelFlag, DelFlagConstants.NORMAL)
                .eq(SysPagesConfig::getLevel, NumberPool.INT_ONE)
                .eq(SysPagesConfig::getAreaId, areaId);
        // 验证预设模版是否有重叠的
        if (type == 1) {
            lambdaQueryWrapper.and(wrapper -> wrapper
                    .and(w -> w
                            .ge(SysPagesConfig::getStartTime, startTime)
                            .le(SysPagesConfig::getEndTime, endTime)
                    )
                    .or(w -> w
                            .le(SysPagesConfig::getStartTime, startTime)
                            .ge(SysPagesConfig::getEndTime, endTime)
                    )
                    .or(w -> w
                            .le(SysPagesConfig::getStartTime, startTime)
                            .ge(SysPagesConfig::getStartTime, endTime)
                    )
                    .or(w -> w
                            .le(SysPagesConfig::getEndTime, startTime)
                            .ge(SysPagesConfig::getEndTime, endTime)
                    )
            );
        }
        // 渠道不为空不验证渠道
        if (StringUtils.isNotEmpty(channelIdStr)) {
            for (String channelId : channelIdStr.split(StringPool.COMMA)) {
                LambdaQueryWrapper<SysPagesConfig> queryWrapper = lambdaQueryWrapper
                        .eq(SysPagesConfig::getType, pagesConfig.getType())
                        // 只验证一级页面就OK
                        .last("AND FIND_IN_SET(channel_id, " + Long.parseLong(channelId) + ")");
                total += this.selectCount(queryWrapper);
            }
        } else{
            // 渠道为空, 只验证城市
            LambdaQueryWrapper<SysPagesConfig> queryWrapper = lambdaQueryWrapper
                    .eq(SysPagesConfig::getType, pagesConfig.getType())
                    // 只验证一级页面就OK
                    .eq(SysPagesConfig::getLevel, NumberPool.INT_ONE)
                    .and(or -> or.isNull(SysPagesConfig::getChannelId).or().eq(SysPagesConfig::getChannelId, StringPool.EMPTY));
            total += this.selectCount(queryWrapper);
        }
        return total;
    }

    default List<SysPagesConfig> selectByPid(Long pageId) {
        return selectList(
                new LambdaQueryWrapperX<SysPagesConfig>()
                        .eq(SysPagesConfig::getPid, pageId)
                        .eq(SysPagesConfig::getDelFlag, DelFlagConstants.NORMAL)
        );
    }
}
