package com.zksr.system.chin.partnerConfig;

import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigRespVO;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigSaveReqVO;
import com.zksr.system.domain.SysPartnerConfig;

import java.util.List;

public abstract class ConfigPartnerChin {
    //下个节点
    ConfigPartnerChin next;

    //设置下一个调用者
    public void setNext(ConfigPartnerChin next) {
        this.next = next;
    }

    //抽象方法 保存配置数据
    public abstract void saveConfig(SysPartnerConfigSaveReqVO sysPartnerConfigSaveReqVO, Long sysCode);

    //抽象方法 获取配置信息
    public abstract SysPartnerConfigRespVO getConfig(List<SysPartnerConfig> configList, SysPartnerConfigRespVO sysPartnerConfigRespVO, Long sysCode);
}
