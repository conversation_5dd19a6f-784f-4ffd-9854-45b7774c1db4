package com.zksr.system.controller.column.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 用户头列配置对象 sys_user_column
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@ApiModel("用户头列配置 - sys_user_column分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysUserColumnPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 表code */
    @Excel(name = "表code")
    @ApiModelProperty(value = "表code", required = true)
    private String tableCode;

    /** 是否导出显示列 */
    @ApiModelProperty(value = "是否导出显示列")
    private Integer exportVisibelFlag;
}
