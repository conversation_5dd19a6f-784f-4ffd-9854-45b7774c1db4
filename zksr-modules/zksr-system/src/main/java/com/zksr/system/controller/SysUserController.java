package com.zksr.system.controller;

import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.page.TableDataInfo;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.*;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.partnerPolicy.enums.PartnerPolicyEnum;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyRespVO;
import com.zksr.system.domain.SysSoftware;
import com.zksr.system.domain.vo.SystemInfoVO;
import com.zksr.system.service.*;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@ApiIgnore
public class SysUserController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysPostService postService;

    @Autowired
    private ISysPermissionService permissionService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private ISysPartnerPolicyService partnerPolicyService;

    @Autowired
    private ISysPartnerService partnerService;

    @Autowired
    private ISysSoftwareService sysSoftwareService;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;
    /**
     * 获取用户列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/list")
    @DataScope(dcAlias = "u", dcFieldAlias = SystemConstants.DC_ID, supplierAlias = "u")
    public TableDataInfo list(SysUser user)
    {
        startPage();
        //MemberDTO memberDto = memberApi.getInfoByMobileAndOpenid(null, null, "omxeH4kzox8uqp2ygu3WRg4PQ3A4").getCheckedData();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @RequiresPermissions("system:user:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user)
    {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @GetMapping("/exportTest")
    @InnerAuth
    public R<List<SysUser>> exportTest()
    {
        return R.ok(userService.selectUserList(new SysUser()));
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @RequiresPermissions("system:user:import")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysUserImportVO> util = new ExcelUtil<SysUserImportVO>(SysUserImportVO.class);
        List<SysUserImportVO> userList = util.importExcel(file.getInputStream());
        String operName = SecurityUtils.getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<SysUserImportVO> util = new ExcelUtil<SysUserImportVO>(SysUserImportVO.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 获取当前用户信息
     */
    @InnerAuth
    @GetMapping("/info/{username}")
    public R<LoginUser> info(@PathVariable("username") String username)
    {
        SysUser sysUser = userService.selectUserByUserName(username);
        if (StringUtils.isNull(sysUser))
        {
            return R.fail("用户名或密码错误");
        }
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysCode(sysUser.getSysCode());
        String funcScop = null;
        SysSoftware sysSoftware = null;
        if(ToolUtil.isNotEmpty(sysUser.getRoles())){
            for (SysRole role : sysUser.getRoles()) {
                if(ToolUtil.isNotEmpty(role.getFuncScop())){
                    funcScop = role.getFuncScop();
                    break;
                }
            }
            // 排查是否有软件商身份
            for (SysRole role : sysUser.getRoles()) {
                // 如果是软件商
                if (SystemConstants.FUNC_SCOPE_SOFTWARE.equals(role.getFuncScop())) {
                    // 需要查询有没有关联的平台商
                    sysSoftware =sysSoftwareService.getSysSoftwareByUserId(sysUser.getUserId());
                    if(ToolUtil.isNotEmpty(sysSoftware)){
                         List<Long> sysCodeList =partnerService.getSysCodeListBySoftwareId(sysSoftware.getSoftwareId());
                        sysUserVo.setSysCodeList(sysCodeList);
                    }
                }
            }
        }
        sysUser.setFuncScop(funcScop);

        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser);

        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(sysUser);

        sysUserVo.setSysUser(sysUser);

       if(ToolUtil.isEmpty(sysUserVo.getSysCodeList()) && ToolUtil.isNotEmpty(sysSoftware)){
            sysUser.setSysCode(99999L);
        }
        sysUserVo.setRoles(roles);
        sysUserVo.setPermissions(permissions);
        sysUserVo.setDcId(sysUser.getDcId());
        sysUserVo.setColonelId(sysUser.getColonelId());
        sysUserVo.setFuncScop(funcScop);
        return R.ok(sysUserVo);
    }

    /**
     * 注册用户信息
     */
    @InnerAuth
    @PostMapping("/register")
    public R<Boolean> register(@RequestBody SysUser sysUser)
    {
        String username = sysUser.getUserName();
        if (!("true".equals(configService.selectConfigByKey("sys.account.registerUser"))))
        {
            return R.fail("当前系统没有开启注册功能！");
        }
        if (!userService.checkUserNameUnique(sysUser))
        {
            return R.fail("保存用户'" + username + "'失败，注册账号已存在");
        }
        return R.ok(userService.registerUser(sysUser));
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = userService.selectUserById(SecurityUtils.getUserId());
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        SysPartnerPolicyRespVO partnerPolicy = partnerPolicyService.getSysPartnerPolicy(
                user.getSysCode(),
                user.getDcId(),
                SecurityUtils.getSupplierId(),
                PartnerPolicyEnum.APPLET_AGREEMENT_POLICY.getType()
        );
        SystemInfoVO infoVO = new SystemInfoVO();
        if (Objects.nonNull(partnerPolicy) && Objects.nonNull(partnerPolicy.getAppletAgreementPolicyDto())) {
            infoVO.setPartnerLog(partnerPolicy.getAppletAgreementPolicyDto().getLogo());
            infoVO.setPartnerName(partnerPolicy.getAppletAgreementPolicyDto().getPartnerName());
        }
        ajax.put("systemInfo", infoVO);
        return ajax;
    }


    @InnerAuth
    @GetMapping("/inner/userInfoForGateway")
    public R<LoginUser> userInfoForGateway(@RequestParam("userCode") String userCode,@RequestParam("tenantCode") String tenantCode, @RequestParam("b2bUserCacheKey") String b2bUserCacheKey){
        return userService.userInfoForGateway(userCode, tenantCode, b2bUserCacheKey);
    }


    /**
     * 根据用户编号获取详细信息
     */
    @RequiresPermissions("system:user:query")
    @GetMapping(value = { "/", "/{userId}" })
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        userService.checkUserDataScope(userId);
        SysUser sysUser = userService.selectUserById(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        if (Objects.nonNull(sysUser.getRoles())) {
            roles.addAll(sysUser.getRoles());
        }
        roles = roles.stream().distinct().collect(Collectors.toList());
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", postService.selectPostAll());
        if (StringUtils.isNotNull(userId))
        {
            ajax.put(AjaxResult.DATA_TAG, sysUser);
            ajax.put("postIds", postService.selectPostListByUserId(userId));
            ajax.put("roleIds", sysUser.getRoles().stream().map(role -> role.getRoleId()+"").collect(Collectors.toList()));
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @RequiresPermissions("system:user:add")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user)
    {
        if (!userService.checkUserNameUnique(user)) {
            return error(String.format("新增用户%s失败,登录账号%s已存在", user.getUserName(), user.getUserName()));
        } else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user)) {
            return error(String.format("新增用户%s失败,手机号码%s已存在", user.getUserName(), user.getPhonenumber()));
        } else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
            return error(String.format("新增用户%s失败,邮箱账号%s已存在", user.getUserName(), user.getEmail()));
        } else if (!saasAuthSwitch &&!Pattern.matches(UserConstants.PASSWORD_PATTERN, user.getPassword())) {
            return error("新增用户'" + user.getUserName() + "'失败，密码必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
        }
        user.setCreateBy(SecurityUtils.getUsername());
        user.setPassword(saasAuthSwitch ? "" :SecurityUtils.encryptPassword(user.getPassword()));
        user.setDcId(SecurityUtils.getLoginUser().getDcId());
        user.setSupplierId(SecurityUtils.getSupplierId());
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        if (!userService.checkUserNameUnique(user))
        {
            return error(String.format("修改用户%s失败,登录账号%s已存在", user.getUserName(), user.getUserName()));
        }
        else if (StringUtils.isNotEmpty(user.getPhonenumber()) && !userService.checkPhoneUnique(user))
        {
            return error(String.format("修改用户%s失败,手机号码%s已存在", user.getUserName(), user.getPhonenumber()));
            //return error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user))
        {
            return error( String.format("修改用户%s失败,邮箱账号%s已存在", user.getUserName(), user.getEmail()));
            //return error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @RequiresPermissions("system:user:remove")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        if (ArrayUtils.contains(userIds, SecurityUtils.getUserId()))
        {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user)
    {
        if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, user.getPassword())) {
            return error("必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
        }
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        userService.checkUserDataScope(user.getUserId());
        user.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @RequiresPermissions("system:user:query")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId)
    {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @RequiresPermissions("system:user:edit")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds)
    {
        userService.checkUserDataScope(userId);
        userService.insertUserAuth(userId, roleIds);
        return success();
    }

    /**
     * 获取部门树列表
     */
    @RequiresPermissions("system:user:list")
    @GetMapping("/deptTree")
    public AjaxResult deptTree(SysDept dept)
    {
        return success(deptService.selectDeptTreeList(dept));
    }


    /**
     * 注册用户信息
     */
    @InnerAuth
    @GetMapping("/getBySysUserId")
    public CommonResult<String> getBySysUserId(@RequestParam("userId") Long userId)
    {
      return CommonResult.success(userService.getBySysUserId(userId));
    }

    /**
     * 根据 userId 获取用户信息
     */
    @InnerAuth
    @GetMapping("/getSysUserApi")
    public CommonResult<SysUser> getSysUserApi(@RequestParam("userId") Long userId)
    {
      return CommonResult.success(userService.getById(userId));
    }

    @InnerAuth
    @GetMapping("/getSysUserByPhone")
    public CommonResult<SysUser> getSysUserByPhone(@RequestParam("phone") String phone)
    {
        return CommonResult.success(userService.getUserByPhone(phone));
    }

    @InnerAuth
    @PostMapping("/updateUserPwd")
    public CommonResult<Integer> updateUserPwd(@RequestBody SysUserUpdatePwdVO updatePwdVO)
    {
        return CommonResult.success(userService.resetUserPwd(updatePwdVO.getUserName(), updatePwdVO.getPassword()));
    }

    /**
     * 删除用户
     */
    @InnerAuth
    @GetMapping("/innerMove")
    public AjaxResult innerMove(@RequestParam("userId") Long userId)
    {
        return toAjax(userService.deleteUserByIds(new Long[]{userId}));
    }
}
