package com.zksr.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.controller.brand.vo.SysBrandMemberPageReqVO;
import com.zksr.system.controller.brand.vo.SysBrandMemberRespVO;
import com.zksr.system.controller.brand.vo.SysBrandMemberSaveReqVO;
import com.zksr.system.convert.brand.SysBrandMemberConvert;
import com.zksr.system.domain.SysBrandMember;
import com.zksr.system.domain.SysBrandMerchant;
import com.zksr.system.enums.SysRoleKeyEnum;
import com.zksr.system.mapper.SysBrandMemberMapper;
import com.zksr.system.mapper.SysRoleMapper;
import com.zksr.system.service.ISysBrandMemberService;
import com.zksr.system.service.ISysBrandMerchantService;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_DC_ROLE_NULL;

/**
 * 品牌商子账户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@Service
public class SysBrandMemberServiceImpl implements ISysBrandMemberService {

    @Autowired
    private SysBrandMemberMapper sysBrandMemberMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private ISysCacheService sysCacheService;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    /**
     * 新增品牌商子账户
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertSysBrandMember(SysBrandMemberSaveReqVO createReqVO) {
        // 插入
        SysBrandMember sysBrandMember = SysBrandMemberConvert.INSTANCE.convert(createReqVO);
        sysBrandMemberMapper.insert(sysBrandMember);
        this.createAdminUser(createReqVO, sysBrandMember);
        // 返回
        return sysBrandMember.getBrandMemberId();
    }

    /**
     * 修改品牌商子账户
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysBrandMember(SysBrandMemberSaveReqVO updateReqVO) {
        SysBrandMember source = sysBrandMemberMapper.selectById(updateReqVO.getBrandMemberId());
        SysBrandMember sysBrandMember = SysBrandMemberConvert.INSTANCE.convert(updateReqVO);
        // 修改密码
//        userService.changePass(source.getSysUserId(), updateReqVO.getPassword());
        if (Objects.nonNull(source.getSysUserId())) {
            SysUser sysUser = userService.getById(source.getSysUserId());
            sysUser.setBrandId(sysBrandMember.getBrandIds());
            sysUser.setAreaId(sysBrandMember.getAreaIds());
            if (!saasAuthSwitch && StringUtils.isNotEmpty(updateReqVO.getPassword())) {
                sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPassword()));
            }
            sysUser.setStatus(updateReqVO.getStatus()+"");
            sysUser.setPhonenumber(updateReqVO.getContactPhone());
            sysUser.setUpdateBy(SecurityUtils.getUsername());
            userService.updateUserStatus(sysUser);
        }
        sysBrandMemberMapper.updateById(sysBrandMember);
    }

    /**
     * 删除品牌商子账户
     *
     * @param brandMemberId ${pkColumn.columnComment}
     */
    @Override
    public void deleteSysBrandMember(Long brandMemberId) {
        // 删除
        sysBrandMemberMapper.deleteById(brandMemberId);
    }

    /**
     * 批量删除品牌商子账户
     *
     * @param brandMemberIds 需要删除的品牌商子账户主键
     * @return 结果
     */
    @Override
    public void deleteSysBrandMemberByBrandMemberIds(Long[] brandMemberIds) {
        for(Long brandMemberId : brandMemberIds){
            this.deleteSysBrandMember(brandMemberId);
        }
    }

    /**
     * 获得品牌商子账户
     *
     * @param brandMemberId ${pkColumn.columnComment}
     * @return 品牌商子账户
     */
    @Override
    public SysBrandMember getSysBrandMember(Long brandMemberId) {
        return sysBrandMemberMapper.selectById(brandMemberId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysBrandMember> getSysBrandMemberPage(SysBrandMemberPageReqVO pageReqVO) {
        return sysBrandMemberMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<SysBrandMemberRespVO> getSysBrandMemberPageExt(SysBrandMemberPageReqVO pageReqVO) {
        SysUser user = SecurityUtils.getLoginUser().getSysUser();
        if (StringUtils.isNotEmpty(user.getBrandId())) {
            SysBrandMerchant brandMerchant = getSysBrandMerchant().getSysBrandMerchantByUserId(SecurityUtils.getUserId());
            SysBrandMember brandMember = this.getSysBrandMemberByUserId(SecurityUtils.getUserId());
            if (Objects.nonNull(brandMerchant)) {
                pageReqVO.setBrandMerchantId(brandMerchant.getBrandMerchantId());
            }
            if (Objects.nonNull(brandMember)) {
                pageReqVO.setBrandMemberId(brandMember.getBrandMemberId());
            }
        }
        Page<SysBrandMemberRespVO> page = PageUtils.startPage(pageReqVO);
        List<SysBrandMemberRespVO> list = sysBrandMemberMapper.selectPageExt(pageReqVO);

        list.forEach(item -> {
            // 渲染品牌数据
            if (StringUtils.isNotEmpty(item.getBrandIds())) {
                List<String> brandList = Arrays.stream(item.getBrandIds().split(StringPool.COMMA))
                        .map(Long::parseLong)
                        .map(sysCacheService::getBrandDTO)
                        .filter(Objects::nonNull)
                        .map(BrandDTO::getBrandName)
                        .collect(Collectors.toList());
                item.setBrandList(brandList);
            }

            // 城市数据
            if (StringUtils.isNotEmpty(item.getAreaIds())) {
                List<String> areaList = Arrays.stream(item.getAreaIds().split(StringPool.COMMA))
                        .map(Long::parseLong)
                        .map(sysCacheService::getAreaDTO)
                        .filter(Objects::nonNull)
                        .map(AreaDTO::getAreaName)
                        .collect(Collectors.toList());
                item.setAreaList(areaList);
            }
        });
        return PageResult.result(page, list);
    }

    @Override
    @Transactional
    public void disable(Long brandMemberId) {
        // 修改品牌资料状态
        SysBrandMember brandMember = sysBrandMemberMapper.selectById(brandMemberId);
        brandMember.setStatus(NumberPool.INT_ONE);
        sysBrandMemberMapper.updateById(brandMember);

        // 修改管理员账户
        SysUser sysUser = userService.selectUserById(brandMember.getSysUserId());
        sysUser.setStatus(StringPool.ONE);
        userService.updateUserStatus(sysUser);
    }

    @Override
    @Transactional
    public void enable(Long brandMemberId) {
        // 修改品牌资料状态
        SysBrandMember brandMember = sysBrandMemberMapper.selectById(brandMemberId);
        brandMember.setStatus(NumberPool.INT_ZERO);
        sysBrandMemberMapper.updateById(brandMember);

        // 修改管理员账户
        SysUser sysUser = userService.selectUserById(brandMember.getSysUserId());
        sysUser.setStatus(StringPool.ZERO);
        userService.updateUserStatus(sysUser);
    }

    @Override
    public SysBrandMember getSysBrandMemberByUserId(Long userId) {
        return sysBrandMemberMapper.selectByUserId(userId);
    }

    private void createAdminUser(SysBrandMemberSaveReqVO createReqVO, SysBrandMember brandMember) {
        SysBrandMerchant brandMerchant = getSysBrandMerchant().getSysBrandMerchant(createReqVO.getBrandMerchantId());

        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        SysUser saveUser = new SysUser();
        // 完善账号信息
        saveUser.setNickName(saasAuthSwitch ? brandMerchant.getContactName() : String.format("【%s】%s", brandMerchant.getSimpleName(), createReqVO.getContactPhone()));
        saveUser.setUserName(createReqVO.getUsername());
        saveUser.setStatus(StringPool.ZERO);
        saveUser.setPassword(saasAuthSwitch ? "" : SecurityUtils.encryptPassword(createReqVO.getPassword()));
        saveUser.setPhonenumber(createReqVO.getContactPhone());
        saveUser.setRemark(String.format("%s默认品牌商管理员用户", brandMerchant.getSimpleName()));
        saveUser.setCreateBy(SecurityUtils.getUsername());
        saveUser.setCreateTime(DateUtil.date());
        if (!userService.checkUserNameUnique(saveUser)) {
            throw new RuntimeException(String.format("新增用户%s失败,登录账号%s已存在", saveUser.getUserName(), saveUser.getUserName()));
        } else if (ToolUtil.isNotEmpty(saveUser.getPhonenumber())
                && !userService.checkPhoneUnique(saveUser)) {
            throw new RuntimeException(String.format("新增用户%s失败,手机号码%s已存在", saveUser.getUserName(),saveUser.getPhonenumber()));
        }
        // 角色在创建平台商的时候已经创建好了
        // 见 com.zksr.system.service.ISysRoleService.createPartnerAdminRole
        // 现在只需要查询到角色具体ID, 赋权即可
        SysRole sysRole = sysRoleMapper.selectBySysCodeAndScope(sysCode, SysRoleKeyEnum.BRAND_ADMIN_ROLE.getRoleKey() + "-" + sysCode);
        if (Objects.isNull(sysRole)) {
            // 权限不存在
            throw exception(SYS_DC_ROLE_NULL);
        }
        Long roleId = sysRole.getRoleId();
        Long[] roleIds = {roleId};// 自动赋管理员权限
        saveUser.setRoleIds(roleIds);
        saveUser.setSysCode(sysCode);
        saveUser.setAreaId(createReqVO.getAreaIds());
        saveUser.setBrandId(createReqVO.getBrandIds());
        userService.insertUser(saveUser);
        //保存用户id字段
        brandMember.setSysUserId(saveUser.getUserId());
        sysBrandMemberMapper.updateById(brandMember);
    }

    public ISysBrandMerchantService getSysBrandMerchant() {
        return SpringUtils.getBean(ISysBrandMerchantService.class);
    }

    @Override
    public void updateSysBrandMemberPassword(SysBrandMemberSaveReqVO updateReqVO) {
        SysBrandMember source = sysBrandMemberMapper.selectById(updateReqVO.getBrandMemberId());
        SysBrandMember sysBrandMember = SysBrandMemberConvert.INSTANCE.convert(updateReqVO);
        if (Objects.nonNull(source.getSysUserId())) {
            SysUser sysUser = userService.getById(source.getSysUserId());
            if (!saasAuthSwitch && StringUtils.isNotEmpty(updateReqVO.getPassword())) {
                sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPassword()));
            }
            userService.updateUserStatus(sysUser);
        }
        sysBrandMemberMapper.updateById(sysBrandMember);
    }
}
