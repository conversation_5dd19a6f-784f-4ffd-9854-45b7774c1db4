package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.*;

/**
 * 短信模版配置对象 sys_sms_template
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@TableName(value = "sys_sms_template")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysSmsTemplate extends BaseEntity{
    private static final long serialVersionUID=1L;

    /**  */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long smsTemplateId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 使用场景 */
    @Excel(name = "使用场景")
    private Integer scene;

    /** 模版编码 */
    @Excel(name = "模版编码")
    private String templateCode;

    /** 短信内容 */
    @Excel(name = "短信内容")
    private String content;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "状态0=正常,1=停用")
    private Integer status;

    /** 签名名称 */
    @Excel(name = "签名名称")
    private String signName;

    /** 半小时内发送上限 */
    @Excel(name = "半小时内发送上限")
    private Integer halfhourCount;

    /** 短信平台 */
    @Excel(name = "短信平台, alibaba-阿里云")
    private String platform;
}
