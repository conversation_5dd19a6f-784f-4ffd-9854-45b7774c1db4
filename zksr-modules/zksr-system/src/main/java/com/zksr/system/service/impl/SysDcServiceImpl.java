package com.zksr.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRespVO;
import com.zksr.common.core.constant.DelFlagConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.dto.ColonelUserDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.chin.partnerPolicy.PolicyPartnerPolicyPipeline;
import com.zksr.system.controller.area.vo.SysAreaRespVO;
import com.zksr.system.controller.dc.vo.SysDcSaveReqVO;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyRespVO;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicySaveReqVO;
import com.zksr.system.convert.dc.DcConvert;
import com.zksr.system.domain.SysDc;
import com.zksr.system.domain.SysDcArea;
import com.zksr.system.enums.SysRoleKeyEnum;
import com.zksr.system.mapper.*;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysDcService;
import com.zksr.system.service.ISysPartnerPolicyService;
import com.zksr.system.service.ISysUserService;
import com.zksr.system.tenant.SaasHelper;
import io.seata.common.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.*;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 运营商Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Service
public class SysDcServiceImpl implements ISysDcService {
    @Autowired
    private SysDcMapper sysDcMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysDcAreaMapper sysDcAreaMapper;

    @Autowired
    private SysAreaMapper sysAreaMapper;

    @Autowired
    private Cache<Long, DcDTO> dcDtoCache;

    @Autowired
    private PlatformMerchantApi platformMerchantApi;

    @Autowired
    private Cache<Long, AreaDTO> areaDTOCache;

    @Autowired
    private PolicyPartnerPolicyPipeline partnerPolicyPipeline;

    @Autowired
    private SaasHelper saasHelper;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    /**
     * 新增运营商
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized Long insertSysDc(SysDcSaveReqVO createReqVO) {
        // 插入
        SysDc sysDc = HutoolBeanUtils.toBean(createReqVO, SysDc.class);
        LoginUser user = SecurityUtils.getLoginUser();
        sysDc.setSysCode(user.getSysCode());
        sysDc.setCreateBy(user.getUsername());
        sysDc.setUpdateBy(sysDc.getCreateBy());
        sysDc.setCreateTime(DateUtil.date());
        sysDc.setUpdateTime(DateUtil.date());
        // 重复值校验
        validateSysDcRept(sysDc);
        sysDcMapper.insert(sysDc);

        // 绑定运营商-区域城市关联关系
        //校验传过来的区域城市是否已经被其运营商绑定
        verifyAreasNotBind(createReqVO.getSysAreaId());
        //新增运营商-区域城市关联关系表
        List<Long> sysAreaId = createReqVO.getSysAreaId();
        List<SysDcArea> sysDcAreaList = sysAreaId.stream().map(x -> new SysDcArea(sysDc.getDcId(), x, user.getSysCode())).collect(Collectors.toList());
        sysDcAreaMapper.insertBatch(sysDcAreaList);
        //修改区域城市表
        sysAreaMapper.bindDcId(sysAreaId,sysDc.getDcId());
        // 清空区域缓存
        sysAreaId.forEach(areaId -> areaDTOCache.remove(areaId));

        SysUser saveUser = new SysUser();
        // 完善账号信息
        saveUser.setNickName(saasAuthSwitch ? sysDc.getContractName() :  String.format("【%s】%s", sysDc.getDcName(), sysDc.getContractName()));
        saveUser.setUserName(createReqVO.getUserName());
        saveUser.setStatus(StringPool.ZERO);
        saveUser.setPassword(saasAuthSwitch ? "" :SecurityUtils.encryptPassword(createReqVO.getPassword())); //兼容原中科
        saveUser.setPhonenumber(sysDc.getContractPhone());
        saveUser.setRemark(String.format("%s默认运营商管理员用户", sysDc.getDcName()));
        saveUser.setCreateBy(SecurityUtils.getUsername());
        saveUser.setCreateTime(DateUtil.date());
        if (!userService.checkUserNameUnique(saveUser)) {
            throw new ServiceException(String.format("新增用户%s失败,登录账号%s已存在", saveUser.getUserName(), saveUser.getUserName()));
        } else if (ToolUtil.isNotEmpty(saveUser.getPhonenumber())
                && !userService.checkPhoneUnique(saveUser)) {
            throw new ServiceException(String.format("新增用户%s失败,手机号码%s已存在", saveUser.getUserName(),saveUser.getPhonenumber()));
        }
        else if (ToolUtil.isNotEmpty(saveUser.getEmail())
                && !userService.checkEmailUnique(saveUser)) {
            throw new ServiceException(String.format("新增用户%s失败,邮箱账号%s已存在", saveUser.getUserName(),saveUser.getEmail()));
        }

        // 运营商角色在创建平台商的时候已经创建好了
        // 见 com.zksr.system.service.ISysRoleService.createPartnerAdminRole
        // 现在只需要查询到角色具体ID, 赋权即可
        SysRole sysRole = sysRoleMapper.selectBySysCodeAndScope(user.getSysCode(), SysRoleKeyEnum.DC_ADMIN_ROLE.getRoleKey() + "-" + user.getSysCode());
        if (Objects.isNull(sysRole)) {
            // 权限不存在
            throw exception(SYS_DC_ROLE_NULL);
        }
        Long roleId = sysRole.getRoleId();
        Long[] roleIds = {roleId};// 自动赋管理员权限
        saveUser.setRoleIds(roleIds);
        saveUser.setSysCode(user.getSysCode());
        saveUser.setDcId(sysDc.getDcId());
        userService.saveUser(saveUser);
        userService.insertUserRole(saveUser);
        //保存用户id字段
        sysDc.setUserId(saveUser.getUserId());
        sysDcMapper.updateById(sysDc);
        // 保存支付平台商户信息
        savePayPlatformMerchant(createReqVO, sysDc);
        // 返回
        return sysDc.getDcId();
    }

    /**
     * 修改运营商
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSysDc(SysDcSaveReqVO updateReqVO) {
//        if (StringUtils.isNotEmpty(updateReqVO.getContractPhone())) {
//            // 手机号不可修改
//            throw exception(SYS_DC_EDIT_PHONE);
//        } else {
//            updateReqVO.setContractPhone(null);
//        }
        SysDc sysDc = HutoolBeanUtils.toBean(updateReqVO, SysDc.class);
        validateSysDcRept(sysDc);
        // 数据库数据
        SysDc source = sysDcMapper.selectById(updateReqVO.getDcId());
        if(null == sysDc.getMinAmt()){
            sysDc.setMinAmt(BigDecimal.ZERO);
        }
        if(null == sysDc.getGlobalMinAmt()){
            sysDc.setGlobalMinAmt(BigDecimal.ZERO);
        }
        //修改运营商表
        sysDcMapper.updateById(sysDc);
        //修改运营商与区域城市关联表
        sysDcAreaMapper.deleteByDcId(sysDc.getDcId());
        LoginUser user = SecurityUtils.getLoginUser();
        List<Long> sysAreaId = updateReqVO.getSysAreaId();
        if (ToolUtil.isEmpty(sysAreaId)){
            throw new ServiceException("区域信息不能为空，请先添加区域信息");
        }
        List<SysDcArea> sysDcAreaList = sysAreaId.stream().map(x -> new SysDcArea(sysDc.getDcId(), x, user.getSysCode())).collect(Collectors.toList());
        sysDcAreaMapper.insertBatch(sysDcAreaList);
        //修改区域城市表
        sysAreaMapper.removeBindByDcId(sysDc.getDcId());
        sysAreaMapper.bindDcId(sysAreaId,sysDc.getDcId());
        //修改用户账号
        SysUser sysUser = userMapper.selectUserById(source.getUserId());
        if (ToolUtil.isEmpty(sysUser)) {
            throw exception(USERNAME_NOT_EXISTS);
        }
        if(!saasAuthSwitch){
            if (org.apache.commons.lang3.StringUtils.isNotBlank(updateReqVO.getPassword())){
                if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, updateReqVO.getPassword())) {
                    throw exception(PASSWORD_FIAL);
                }
                sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPassword().trim()));
            }
            sysUser.setPhonenumber(sysDc.getContractPhone());
        }
        sysUser.setNickName(saasAuthSwitch ? updateReqVO.getContractName() :String.format("【%s】%s", updateReqVO.getDcName(), updateReqVO.getContractName()));
        sysUser.setStatus(sysDc.getStatus());
        sysUser.setUpdateBy(SecurityUtils.getUsername());
        userService.updateUserV2(sysUser);
        //删除缓存
        dcDtoCache.remove(updateReqVO.getDcId());
        // 更新平台商户信息
        savePayPlatformMerchant(updateReqVO, sysDc);

        // 保存运营商配置
        SysPartnerPolicySaveReqVO policySaveReqVO = new SysPartnerPolicySaveReqVO();
        policySaveReqVO.setDcOtherSettingPolicyDTO(updateReqVO.getDcOtherSettingPolicyDTO());
        partnerPolicyPipeline.partnerSaveConfig(policySaveReqVO, sysDc.getSysCode(), sysDc.getDcId(), null);
    }

    /**
     * 删除运营商
     *
     * @param dcId 运营商编号
     */
    @Override
    public void deleteSysDc(Long dcId) {
        SysDc sysDc = sysDcMapper.selectById(dcId);
        sysDc.setUpdateTime(DateUtil.date());
        sysDc.setUpdateBy(SecurityUtils.getUsername());
        sysDc.setDelFlag(DelFlagConstants.DISABLE);
        sysDcMapper.updateById(sysDc);
    }

    /**
     * 批量删除运营商
     *
     * @param dcIds 需要删除的运营商主键
     * @return 结果
     */
    @Override
    public void deleteSysDcByDcIds(Long[] dcIds) {
        for(Long dcId : dcIds){
            this.deleteSysDc(dcId);
        }
    }

    /**
     * 获得运营商
     *
     * @param dcId 运营商编号
     * @return 运营商
     */
    @Override
    public SysDcRespVO getSysDc(Long dcId) {
        //获取运营商基本信息
        SysDc sysDc = sysDcMapper.selectById(dcId);
        if (ObjectUtil.isNull(sysDc)){
            throw exception(SYS_DC_IS_NULL);
        }
        // 转换返回实体
        SysDcRespVO sysDcRespVO = DcConvert.INSTANCE.convert(sysDc);
        //获取运营商与区域绑定信息
        List<Long> sysAreaId = sysDcAreaMapper.selectByDcId(sysDcRespVO.getDcId());
        // 设置信息
        DcConvert.INSTANCE.convert(sysDcRespVO, sysAreaId, userMapper.getBySysUserId(sysDc.getUserId()));
        // 获取进件信息
        List<AccPlatformMerchantRespVO> merchantRespVOS = platformMerchantApi.getMerchantList(
                AccPlatformMerchantPageReqVO.builder()
                        .merchantId(sysDc.getDcId())
                        .build()
        ).getCheckedData();
        sysDcRespVO.setPlatformSimpleBindList(BeanUtils.toBean(merchantRespVOS, PlatformSimpleBindVO.class));

        // 软件商充值分润信息
        PayConfigDTO payConfigDTO = SpringUtils.getBean(ISysCacheService.class).getPayConfigDTO(sysDc.getSysCode());
        if (Objects.nonNull(payConfigDTO)) {
            sysDcRespVO.setWalletSoftwareRate(payConfigDTO.getWalletSoftwareRate());
        }

        // 运营商其他配置
        SysPartnerPolicyRespVO dcConfig = SpringUtils.getBean(ISysPartnerPolicyService.class).getDcConfig(dcId);
        if (Objects.nonNull(dcConfig)) {
            sysDcRespVO.setDcOtherSettingPolicyDTO(dcConfig.getDcOtherSettingPolicyDTO());
        }
        return sysDcRespVO;
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysDc> getSysDcPage(SysDcPageReqVO pageReqVO) {
        return sysDcMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<SysDcRespVO> getSysAreaPageExt(SysDcPageReqVO pageReqVO) {
        // 转换返回
        Page<SysAreaRespVO> page = PageUtils.startPage(pageReqVO);
        return new PageResult<>(sysDcMapper.selectPageExt(pageReqVO), page.getTotal());
    }

    /**
    * @Description: 获取可绑定的运营商
    * @Author: liuxingyu
    * @Date: 2024/3/18 16:19
    */
    @Override
    public List<SysDc> getNotBindDcList(Long areaId) {
        return sysDcMapper.getNotBindDcList(areaId);
    }

    /**
    * @Description: 根据Id获取运营商信息
    * @Author: liuxingyu
    * @Date: 2024/3/25 14:52
    */
    @Override
    public SysDc getDcById(Long dcId) {
        return sysDcMapper.selectById(dcId);
    }

    @Override
    public List<SysDc> getDcBySysCode(Long sysCode) {
        return sysDcMapper.getDcBySysCode(sysCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertColonelUser(ColonelUserDTO colonelDTO) {
        LoginUser user = SecurityUtils.getLoginUser();
        Long sysCode;
        if (user!=null){
             sysCode = user.getSysCode();
        }else {
             sysCode = SecurityContextHolder.getSysCode();
        }

        SysUser saveUser = new SysUser();
        // 完善账号信息
        saveUser.setNickName(saasAuthSwitch ? colonelDTO.getColonelName() :  String.format("【%s】%s", "业务员", colonelDTO.getColonelName()));
        saveUser.setUserName(colonelDTO.getUserName());
        saveUser.setStatus(StringPool.ZERO);
        saveUser.setPassword(saasAuthSwitch ? "" : SecurityUtils.encryptPassword(colonelDTO.getPassword()));
        saveUser.setPhonenumber(colonelDTO.getColonelPhone());
        saveUser.setRemark(String.format("%s默认业务员管理员用户", colonelDTO.getColonelName()));
        saveUser.setCreateBy(SecurityUtils.getUsername());
        saveUser.setCreateTime(DateUtil.date());
        if (!userService.checkUserNameUnique(saveUser)) {
            throw new ServiceException("新增用户'" + saveUser.getPhonenumber() + "'失败，登录账号已存在");
        } else if (ToolUtil.isNotEmpty(saveUser.getPhonenumber())
                && !userService.checkPhoneUnique(saveUser)) {
            throw new ServiceException("新增用户'" + saveUser.getPhonenumber() + "'失败，手机号码已存在");
        }
        else if (ToolUtil.isNotEmpty(saveUser.getEmail())
                && !userService.checkEmailUnique(saveUser)) {
            throw new ServiceException("新增用户'" + saveUser.getPhonenumber() + "'失败，邮箱账号已存在");
        }
        // 赋权
        SysRole sysRole = sysRoleMapper.selectBySysCodeAndScope(sysCode, SysRoleKeyEnum.COLONEL_ADMIN_ROLE.getRoleKey() + "-" + sysCode);
        if (Objects.isNull(sysRole)) {
            // 权限不存在
            throw exception(MEM_COLONEL_DC_COLONEL_ROLE_IS_NULL);
        }
        Long roleId = sysRole.getRoleId();
        Long[] roleIds = {roleId};// 自动赋管理员权限
        saveUser.setRoleIds(roleIds);
        saveUser.setSysCode(sysCode);
        //saveUser.setDcId(user.getDcId());
        //用户绑定业务员ID
        saveUser.setColonelId(colonelDTO.getColonelId());
        userService.saveUser(saveUser);
        userService.insertUserRole(saveUser);
        //保存用户id字段
        return saveUser.getUserId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateColonelUser(ColonelUserDTO colonelDTO) {
        //校验该用户是否存在
        SysUser sysUser = userMapper.selectUserById(colonelDTO.getUserId());
        if(Objects.isNull(sysUser)){
            //用户不存在
            throw exception(MEM_COLONEL_DC_COLONEL_IS_NULL);
        }
        if(!sysUser.getUserName().equals(colonelDTO.getUserName())){
            throw exception(MEM_COLONEL_DC_COLONEL_NOT_MATE);
        }
        if (!saasAuthSwitch && ToolUtil.isNotEmpty(colonelDTO.getPassword())) {
            sysUser.setPassword(SecurityUtils.encryptPassword(colonelDTO.getPassword()));
        }
        sysUser.setNickName(saasAuthSwitch ? colonelDTO.getColonelName() : String.format("【%s】%s", "业务员", colonelDTO.getColonelName()));
        sysUser.setStatus(Objects.equals(NumberPool.INT_ONE, colonelDTO.getStatus()) ? StringPool.ZERO : StringPool.ONE);
        sysUser.setPhonenumber(colonelDTO.getColonelPhone());
        sysUser.setUpdateBy(SecurityUtils.getUsername());
        userService.updateUserV2(sysUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateColonelUserStatus(Long userId, String status) {
        SysUser sysUser = userMapper.selectUserById(userId);
        if (Objects.isNull(sysUser)) {
            throw exception(MEM_COLONEL_DC_COLONEL_IS_NULL);
        }
        sysUser.setStatus(status);
        userService.updateUserStatus(sysUser);
    }

    @Override
    public List<Long> getDcSupplierList(Long dcId) {
        return sysDcMapper.selectDcSupplierIdList(dcId);
    }

    @Override
    @Transactional
    public void disable(Long dcId) {
        SysDc sysDc = sysDcMapper.selectById(dcId);
        sysDc.setUpdateTime(DateUtil.date());
        sysDc.setStatus(StringPool.ONE);
        sysDcMapper.updateById(sysDc);
        // 查询当前运营商下所有账号
        List<SysUser> sysUserList = userService.selectByDcId(sysDc.getDcId());
        if (CollectionUtils.isNotEmpty(sysUserList)) {
            sysUserList.forEach(sysUser -> {
                // 启用的账号去停用
                if (Objects.nonNull(sysUser) && StringUtils.equals(sysUser.getStatus(), StringPool.ZERO)) {
                    sysUser.setStatus(StringPool.ONE);
                    userService.updateUserStatus(sysUser);
                }
            });
        }
    }

    @Override
    @Transactional
    public void enable(Long dcId) {
        SysDc sysDc = sysDcMapper.selectById(dcId);
        sysDc.setUpdateTime(DateUtil.date());
        sysDc.setStatus(StringPool.ZERO);
        sysDcMapper.updateById(sysDc);
        // 查询当前运营商下所有账号
        List<SysUser> sysUserList = userService.selectByDcId(sysDc.getDcId());
        if (CollectionUtils.isNotEmpty(sysUserList)) {
            sysUserList.forEach(sysUser -> {
                // 停用的账号去启用
                if (Objects.nonNull(sysUser) && StringUtils.equals(sysUser.getStatus(), StringPool.ONE)) {
                    sysUser.setStatus(StringPool.ZERO);
                    userService.updateUserStatus(sysUser);
                }
            });
        }
    }

    /**
    * @Description: 校验运营商区域
    * @Author: liuxingyu
    * @Date: 2024/3/7 15:12
    */
    private void verifyAreasNotBind(List<Long> sysAreaId){
        if (ObjectUtil.isEmpty(sysAreaId)){
            throw exception(SYS_DC_AREA_IS_NULL);
        }
        //校验区域城市
        List<SysDcArea> areaList = sysDcAreaMapper.selectListByAreas(sysAreaId);
        if (ObjectUtil.notEqual(NumberPool.INT_ZERO,areaList.size())){
            throw exception(SYS_DC_AREA_IS_BIND);
        }
    }


    private void validateSysDcRept(SysDc sysDc) {
        {
            Long count = sysDcMapper.selectCount(
                    Wrappers.lambdaQuery(SysDc.class)
                            .eq(SysDc::getDcName, sysDc.getDcName())
                            .eq(SysDc::getDelFlag, DelFlagConstants.NORMAL)
                            .ne(Objects.nonNull(sysDc.getDcId()), SysDc::getDcId, sysDc.getDcId())
            );
            // 运营商名称重复
            if (count > 0) {
                throw exception(SYS_DC_REPT_NAME);
            }
        }
        {
            Long count = sysDcMapper.selectCount(
                    Wrappers.lambdaQuery(SysDc.class)
                            .eq(SysDc::getDcCode, sysDc.getDcCode())
                            .ne(Objects.nonNull(sysDc.getDcId()), SysDc::getDcId, sysDc.getDcId())
            );
            // 运营商编号重复
            if (count > 0) {
                throw exception(SYS_DC_REPT_CODE);
            }
        }
        {
            Long count = sysDcMapper.selectCount(
                    Wrappers.lambdaQuery(SysDc.class)
                            .eq(SysDc::getContractPhone, sysDc.getContractPhone())
                            .ne(Objects.nonNull(sysDc.getDcId()), SysDc::getDcId, sysDc.getDcId())
            );
            // 运营商编号重复
            if (count > 0) {
                throw exception(SYS_DC_REPT_PHONE);
            }
        }
    }

    /**
     * 保存平台商户信息
     * @param saveReqVO
     * @param sysDc
     */
    private void savePayPlatformMerchant(SysDcSaveReqVO saveReqVO, SysDc sysDc) {
        if (ObjectUtil.isNotEmpty(saveReqVO.getPlatformSimpleBindList())) {
            for (PlatformSimpleBindVO simpleBindVO : saveReqVO.getPlatformSimpleBindList()) {
                PlatformMerchantDTO merchant = BeanUtils.toBean(simpleBindVO, PlatformMerchantDTO.class);
                merchant.setMerchantId(sysDc.getDcId())
                        .setMerchantType(MerchantTypeEnum.DC.getType())
                        .setSysCode(SecurityUtils.getLoginUser().getSysCode());
                platformMerchantApi.savePlatformMerchant(merchant).checkError();
            }
        }
    }

    @Override
    public void updateSysDcPassword(SysDcSaveReqVO updateReqVO) {
        //修改用户账号
        SysDc sysDc = HutoolBeanUtils.toBean(updateReqVO, SysDc.class);
        SysDc source = sysDcMapper.selectById(updateReqVO.getDcId());
        SysUser sysUser = userMapper.selectUserById(source.getUserId());
        if (ToolUtil.isEmpty(sysUser)) {
            throw exception(USERNAME_NOT_EXISTS);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(updateReqVO.getPassword())){
            if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, updateReqVO.getPassword())) {
                throw exception(PASSWORD_FIAL);
            }
            sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPassword().trim()));
        }
        sysUser.setNickName(saasAuthSwitch ? sysDc.getContractName() : String.format("【%s】%s", sysDc.getDcName(), sysDc.getContractName()));
        sysUser.setStatus(sysDc.getStatus());
        sysUser.setPhonenumber(sysDc.getContractPhone());
        sysUser.setUpdateBy(SecurityUtils.getUsername());
        userService.updateUserV2(sysUser);
        //删除缓存
        dcDtoCache.remove(updateReqVO.getDcId());
        // 更新平台商户信息
        savePayPlatformMerchant(updateReqVO, sysDc);
    }
}
