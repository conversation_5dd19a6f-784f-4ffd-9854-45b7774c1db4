package com.zksr.system.visualSyn.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.stereotype.Component;

import java.io.StringWriter;
import java.util.Map;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_SYNC_EMAIL_MESSAGE_DATA_ERR;

@Slf4j
@Component("emailAssembleTemplate")
@SuppressWarnings("all")
public class EmailAssembleTemplate {

    /**
     * 同步日志通知处理异常 模板
     * @return
     */
    public String SyncErrTemplate() {
        String template = "<p><strong><em>推送异常告警：</em></strong></p><p>" +
                "<em>尊敬的客户您好</em></p>               " +
                "<p>${templateName}至第三方数据异常。</p>      " +
                "<p>具体信息如下：</p>                       " +
                "<p><strong>组装的请求头数据：</strong></p>    " +
                "<p>#if($!{header})\"${header}\",#else\"\"#end</p>                          " +
                "<p><strong>组装的请求体数据：</strong></p>     " +
                "<p>#if($!{body})\"${body}\",#else\"\"#end</p>                             " +
                "<p><strong>组装的鉴权信息数据：</strong></p>   " +
                "<p>#if($!{verify})\"${verify}\",#else\"\"#end</p>                           " +
                "<p><strong>错误信息数据：</strong></p>  " +
                "<p>#if($!{errMessage})\"${errMessage}\",#else\"\"#end</p>" +
                "<p></p>" +
                "<p>同步日志操作教程：https://docs.qq.com/doc/DS1NuYUZJQ3ZPcm1i</p>" ;

        return template;
    }

    /**
     * 同步日志日同步报告 模板
     * @return
     */
    public String DaySyncReportDataTemplate() {
        String template = "<em>尊敬的客户您好</em></p>" +
                "<p><strong><em>同步日志日同步报告已生成，请注意查收</em></strong></p><p>" +
                "<p><strong><em>文件链接地址： ${fileUrl}</em></strong></p><p>" +
                "<p></p>" +
                "<p>同步日志操作教程：https://docs.qq.com/doc/DS1NuYUZJQ3ZPcm1i</p>" ;
        return template;
    }


    /**
     * 根据模版内容，替换参数，返回替换后的内容
     * @param data 参数信息
     * @param velocityTemplate 模板
     * @return
     */
    public String assembleEmailSendData(Object data,String velocityTemplate){
        try{

            if (ObjectUtil.isEmpty(data)) {
                throw new RuntimeException("匹配Email模板信息，参数数据对象不能为空");
            }
            if (ObjectUtil.isEmpty(velocityTemplate)) {
                throw new RuntimeException("匹配Email模板信息，模版内容不能为空");
            }
            // 初始化VelocityEngine
            // 配置VelocityEngine（可选），这里直接使用默认配置
            VelocityEngine velocityEngine = new VelocityEngine();

            // 准备数据模型
            VelocityContext context = new VelocityContext();
            // 将数据对象先转换为json，再将json转换为Map对象
            String jsonStr = JSONUtil.toJsonStr(data);
            log.info("匹配Email模板信息,开始替换参数: 参数：{}，转换模版：{}", jsonStr, velocityTemplate);
            Map paramMap = JSONUtil.toBean(jsonStr, Map.class);
            for (Object o : paramMap.keySet()) {
                context.put(o.toString(), paramMap.get(o));
            }
            // 合成输出
            StringWriter writer = new StringWriter();
            // 直接使用字符串模板内容，而非通过getTemplate方法
            boolean result = velocityEngine.evaluate(context, writer, "TemplateName", velocityTemplate);
            if (!result) {
                throw new RuntimeException("匹配Email模板信息,参数转换失败：使用velocity模板替换参数时出错");
            }
            log.info("匹配Email模板信息数据：{}", writer.toString());
            // 输出结果
            return writer.toString();

        }catch (Exception e){
            log.error(" EmailAssembleTemplate.assembleEmailSendData失败,", e);
            throw exception(SYS_SYNC_EMAIL_MESSAGE_DATA_ERR,e.getMessage());
        }
    }
}
