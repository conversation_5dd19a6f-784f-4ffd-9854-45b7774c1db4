package com.zksr.system.controller.supplierArea;

import javax.validation.Valid;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysSupplierArea;
import com.zksr.system.service.ISysSupplierAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaPageReqVO;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaSaveReqVO;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入驻商-区域城市关联关系Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Api(tags = "管理后台 - 入驻商-区域城市关联关系接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/supplierArea")
public class SysSupplierAreaController {
    @Autowired
    private ISysSupplierAreaService sysSupplierAreaService;

    /**
     * 新增入驻商-区域城市关联关系
     */
    @ApiOperation(value = "新增入驻商-区域城市关联关系", httpMethod = "POST", notes = "system:supplierArea:add")
    @RequiresPermissions("system:supplierArea:add")
    @Log(title = "入驻商-区域城市关联关系", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysSupplierAreaSaveReqVO createReqVO) {
        return success(sysSupplierAreaService.insertSysSupplierArea(createReqVO));
    }

    /**
     * 修改入驻商-区域城市关联关系
     */
    @ApiOperation(value = "修改入驻商-区域城市关联关系", httpMethod = "PUT", notes = "system:supplierArea:edit")
    @RequiresPermissions("system:supplierArea:edit")
    @Log(title = "入驻商-区域城市关联关系", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysSupplierAreaSaveReqVO updateReqVO) {
        sysSupplierAreaService.updateSysSupplierArea(updateReqVO);
        return success(true);
    }

    /**
     * 删除入驻商-区域城市关联关系
     */
    @ApiOperation(value = "删除入驻商-区域城市关联关系", httpMethod = "GET", notes = "system:supplierArea:remove")
    @RequiresPermissions("system:supplierArea:remove")
    @Log(title = "入驻商-区域城市关联关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{sysCodes}")
    public CommonResult<Boolean> remove(@PathVariable Long[] sysCodes) {
        sysSupplierAreaService.deleteSysSupplierAreaBySysCodes(sysCodes);
        return success(true);
    }

    /**
     * 获取入驻商-区域城市关联关系详细信息
     */
    @ApiOperation(value = "获得入驻商-区域城市关联关系详情", httpMethod = "GET", notes = "system:supplierArea:query")
    @RequiresPermissions("system:supplierArea:query")
    @GetMapping(value = "/{sysCode}")
    public CommonResult<SysSupplierAreaRespVO> getInfo(@PathVariable("sysCode") Long sysCode) {
        SysSupplierArea sysSupplierArea = sysSupplierAreaService.getSysSupplierArea(sysCode);
        return success(HutoolBeanUtils.toBean(sysSupplierArea, SysSupplierAreaRespVO.class));
    }

    /**
     * 分页查询入驻商-区域城市关联关系
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入驻商-区域城市关联关系分页列表", httpMethod = "GET", notes = "system:supplierArea:list")
    @RequiresPermissions("system:supplierArea:list")
    public CommonResult<PageResult<SysSupplierAreaRespVO>> getPage(@Valid SysSupplierAreaPageReqVO pageReqVO) {
        PageResult<SysSupplierArea> pageResult = sysSupplierAreaService.getSysSupplierAreaPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, SysSupplierAreaRespVO.class));
    }

}
