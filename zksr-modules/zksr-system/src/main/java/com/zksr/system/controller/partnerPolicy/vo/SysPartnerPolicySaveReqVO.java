package com.zksr.system.controller.partnerPolicy.vo;

import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.chin.partnerPolicy.PolicyOpenSettingHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台商政策(由平台商设置)对象 sys_partner_policy
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Data
@ApiModel("平台商政策(由平台商设置) - sys_partner_policy分页 Request VO")
public class SysPartnerPolicySaveReqVO {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("小程序配置")
    private AppletAgreementPolicyDTO appletAgreementPolicyDto;

    @ApiModelProperty("基本配置")
    private BasicSettingPolicyDTO basicSettingPolicyDto;

    @ApiModelProperty("订单配置")
    private OrderSettingPolicyDTO orderSettingPolicyDto;

    @ApiModelProperty("提款配置")
    private WithdrawalSettingPolicyDTO withdrawalSettingPolicyDTO;

    @ApiModelProperty("芯烨配置")
    private XpYunSettingPolicyDTO xpYunSettingPolicyDTO;

    @ApiModelProperty("入驻商售后配置")
    private AfterSaleSettingPolicyDTO afterSaleSettingPolicyDTO;

    @ApiModelProperty("飞鹅配置")
    private FeieYunSettingPolicyDTO feieYunSettingPolicyDTO;

    @ApiModelProperty("业务员配置")
    private ColonelSettingPolicyDTO colonelSettingPolicyDTO;

    @ApiModelProperty("入驻商对接配置")
    private OpenSettingPolicyDTO openSettingPolicyDTO;

    @ApiModelProperty("平台商小程序配置")
    private PartnerMiniSettingPolicyDTO partnerMiniSettingPolicyDTO;

    @ApiModelProperty("入驻商其他配置")
    private SupplierOtherSettingPolicyDTO supplierOtherSettingPolicyDTO;

    @ApiModelProperty("运营商门店生命周期配置")
    private BranchLifecycleSettingPolicyDTO branchLifecycleSettingPolicyDTO;

    @ApiModelProperty("运营商其他配置")
    private DcOtherSettingPolicyDTO dcOtherSettingPolicyDTO;
}
