package com.zksr.system.controller.supplier.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 入驻商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("入驻商信息 - 修改入驻商状态")
public class SysSupplierStatusReqVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 状态-数据字典 */
    @Excel(name = "状态-数据字典")
    @ApiModelProperty(value = "状态-数据字典")
    private Long status;
}
