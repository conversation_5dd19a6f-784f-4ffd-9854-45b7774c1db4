package com.zksr.system.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysOpenlimit;
import com.zksr.system.controller.openlimit.vo.SysOpenlimitPageReqVO;


/**
 * 开发能力qpsMapper接口
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Mapper
public interface SysOpenlimitMapper extends BaseMapperX<SysOpenlimit> {
    default PageResult<SysOpenlimit> selectPage(SysOpenlimitPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysOpenlimit>()
                    .eqIfPresent(SysOpenlimit::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(SysOpenlimit::getOpensourceId, reqVO.getOpensourceId())
                    .eqIfPresent(SysOpenlimit::getOpenabilityId, reqVO.getOpenabilityId())
                    .eqIfPresent(SysOpenlimit::getRateLimit, reqVO.getRateLimit())
                .orderByDesc(SysOpenlimit::getSysCode));
    }
}
