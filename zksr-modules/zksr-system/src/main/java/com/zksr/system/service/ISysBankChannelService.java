package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysBankChannel;
import com.zksr.system.api.bank.vo.SysBankChannelPageReqVO;
import com.zksr.system.controller.bank.vo.SysBankChannelSaveReqVO;

/**
 * 联行号信息Service接口
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface ISysBankChannelService {

    /**
     * 新增联行号信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysBankChannel(@Valid SysBankChannelSaveReqVO createReqVO);

    /**
     * 修改联行号信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysBankChannel(@Valid SysBankChannelSaveReqVO updateReqVO);

    /**
     * 删除联行号信息
     *
     * @param bankChannelId id
     */
    public void deleteSysBankChannel(Long bankChannelId);

    /**
     * 批量删除联行号信息
     *
     * @param bankChannelIds 需要删除的联行号信息主键集合
     * @return 结果
     */
    public void deleteSysBankChannelByBankChannelIds(Long[] bankChannelIds);

    /**
     * 获得联行号信息
     *
     * @param bankChannelId id
     * @return 联行号信息
     */
    public SysBankChannel getSysBankChannel(Long bankChannelId);

    /**
     * 获得联行号信息分页
     *
     * @param pageReqVO 分页查询
     * @return 联行号信息分页
     */
    PageResult<SysBankChannel> getSysBankChannelPage(SysBankChannelPageReqVO pageReqVO);

    /**
     * 通过联行号查询银行信息
     * @param bankChannelNo
     * @return
     */
    SysBankChannel getChannelByNo(String bankChannelNo);
}
