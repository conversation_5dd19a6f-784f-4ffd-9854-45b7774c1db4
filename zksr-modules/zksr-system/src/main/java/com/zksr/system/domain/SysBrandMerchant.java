package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 品牌商资料对象 sys_brand_merchant
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@TableName(value = "sys_brand_merchant")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysBrandMerchant extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long brandMerchantId;

    /** 运营商编号 */
    @Excel(name = "运营商编号")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系人手机号 */
    @Excel(name = "联系人手机号")
    private String contactPhone;

    /** 品牌商全称 */
    @Excel(name = "品牌商全称")
    private String name;

    /** 品牌商简称 */
    @Excel(name = "品牌商简称")
    private String simpleName;

    /** 联系地址 */
    @Excel(name = "联系地址")
    private String contactAddress;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 关联品牌集合 */
    @Excel(name = "关联品牌集合")
    private String brandIds;

    /** 关联管理员ID */
    @Excel(name = "关联管理员ID")
    private Long sysUserId;
}
