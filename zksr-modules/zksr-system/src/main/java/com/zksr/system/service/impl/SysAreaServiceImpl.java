package com.zksr.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.area.dto.AreaIsExistDTO;
import com.zksr.system.api.supplierArea.dto.SupplierAreaDTO;
import com.zksr.system.controller.area.excel.SysAreaImportExcel;
import com.zksr.system.controller.area.vo.*;
import com.zksr.system.domain.SysArea;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.domain.SysDcArea;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.mapper.SysAreaMapper;
import com.zksr.system.mapper.SysDcAreaMapper;
import com.zksr.system.service.ISysAreaCityService;
import com.zksr.system.service.ISysAreaService;
import com.zksr.system.service.ISysDcAreaService;
import com.zksr.system.service.ISysDcAreaZipService;
import com.zksr.trade.api.order.OrderApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.pool.NumberPool.INT_TWO;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_AREA_CLASS_SUB_LEVEL_MAX;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 区域城市Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@Service
public class SysAreaServiceImpl implements ISysAreaService {
    @Autowired
    private SysAreaMapper sysAreaMapper;

    @Autowired
    private Cache<Long, AreaDTO> areaDtoCache;

    @Autowired
    private SysDcAreaMapper sysDcAreaMapper;

    @Autowired
    private ISysDcAreaZipService sysdcAreaZipService;
    @Autowired
    private ISysAreaCityService sysAreaCityService;
    @Autowired
    private ISysDcAreaService sysDcAreaService;
    @Autowired
    private SysSupplierAreaServiceImpl sysSupplierAreaService;
    @Resource
    private OrderApi orderApi;
    @Resource
    private ColonelApi colonelApi;
    @Resource
    private BranchApi branchApi;
    @Resource
    private SysDcServiceImpl sysDcService;
    @Resource
    private SpuApi spuApi;
    @Resource
    private SkuApi skuApi;
    /**
     * 新增区域城市
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertSysArea(SysAreaSaveReqVO createReqVO) {
        //校验区域城市级别
        checkAreaLevel(createReqVO);
        //同步子集
        //synSublevel(createReqVO);
        //校验名称
        checkAreaName(createReqVO.getLevel(), createReqVO.getAreaName(), null);
        SysArea sysArea = HutoolBeanUtils.toBean(createReqVO, SysArea.class);
        sysAreaMapper.insert(sysArea);
        //保存区域城市与运营商关联表
        if (ObjectUtil.isNotNull(createReqVO.getDcId())) {
            SysDcArea sysDcArea = new SysDcArea();
            sysDcArea.setAreaId(sysArea.getAreaId());
            sysDcArea.setDcId(createReqVO.getDcId());
            sysDcArea.setSysCode(SecurityUtils.getLoginUser().getSysCode());
            sysDcAreaMapper.insert(sysDcArea);
        }
        //新增 运营商区域城市拉链表
        if(ToolUtil.isNotEmpty(createReqVO.getDcId())){
              createReqVO.setAreaId(sysArea.getAreaId());
              sysdcAreaZipService.insertSysDcAreaZip(createReqVO,sysArea);
        }
        // 返回
        return sysArea.getAreaId();
    }

    /**
     * 修改区域城市
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSysArea(SysAreaSaveReqVO updateReqVO) {
        //校验区域城市级别
        checkAreaLevel(updateReqVO);
        //校验名称
        checkAreaName(updateReqVO.getLevel(), updateReqVO.getAreaName(), updateReqVO.getAreaId());
        //如果区域绑定了运营商且修改了级别则提示不允许修改
        SysArea sysArea = sysAreaMapper.selectById(updateReqVO.getAreaId());
        if (ObjectUtil.isNull(sysArea)) {
            throw exception(SYS_AREA_NOT_EXISTS);
        }
        // 原来有运营商, 现在要删除
        if (Objects.nonNull(sysArea.getDcId()) && (Objects.isNull(updateReqVO.getDcId()) || !sysArea.getDcId().equals(updateReqVO.getDcId()))) {
            throw exception(SYS_DC_CAN_NOT_CHANGE);
        }
        // 销售城市不允许修改运营商 2025年3月7, 因为门店是基于运营商做的余额
        if (ObjectUtil.notEqual(sysArea.getLevel(), updateReqVO.getLevel())) {
            SysDcArea sysDcArea = sysDcAreaMapper.selectByAreaId(updateReqVO.getAreaId());
            if (ObjectUtil.isNotNull(sysDcArea)) {
                throw exception(SYS_AREA_NOT_UPDATE);
            }
        }
        sysAreaMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysArea.class));
        //保存区域城市与运营商关联表
        sysDcAreaMapper.deleteByAreaId(updateReqVO.getAreaId());
        if (ObjectUtil.isNotNull(updateReqVO.getDcId())) {
            SysDcArea sysDcArea = new SysDcArea();
            sysDcArea.setAreaId(updateReqVO.getAreaId());
            sysDcArea.setDcId(updateReqVO.getDcId());
            sysDcArea.setSysCode(SecurityUtils.getLoginUser().getSysCode());
            sysDcAreaMapper.insert(sysDcArea);
        }
        // 记录 运营商区域城市拉链表
        if (ToolUtil.isNotEmpty(updateReqVO.getDcId()) && !ObjectUtil.equals(sysArea.getDcId(), updateReqVO.getDcId())) {
            sysdcAreaZipService.insertSysDcAreaZip(updateReqVO,sysArea);
        }
    }

    /**
     * 删除区域城市
     *
     * @param areaId 区域城市id
     */
    @Override
    public void deleteSysArea(Long areaId) {
        // 删除
        sysAreaMapper.deleteById(areaId);
    }

    /**
     * 批量删除区域城市
     *
     * @param areaIds 需要删除的区域城市主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AreaIsExistDTO deleteSysAreaByAreaIds(Long[] areaIds) {
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        List<SysArea> sysAreaList = sysAreaMapper.selectedSysAreaList(Arrays.asList(areaIds));
        // 组装数据
        AreaIsExistDTO areaIsExistDTO =  new AreaIsExistDTO();
        List<AreaIsExistDTO> downAreaIsExistDTOS = new ArrayList<>();
        ArrayList<String> downAreaNameData = new ArrayList<>();
        ArrayList<Long> downAreaIdData = new ArrayList<>();
        HashSet<String> dcNameData = new HashSet<>();
        HashSet<Long> dcIdData = new HashSet<>();
        HashSet<String> supplierNameData = new HashSet<>();
        HashSet<Long> supplierIdData = new HashSet<>();
        HashSet<Long> colonelIds = new HashSet<>();
        HashSet<String> colonelNames = new HashSet<>();
        HashSet<Long> branchIds = new HashSet<>();
        HashSet<String> branchNames = new HashSet<>();

        areaIsExistDTO.setColonelData(false);
        areaIsExistDTO.setBranchData(false);
        areaIsExistDTO.setOrderData(false);
        areaIsExistDTO.setSkuData(false);

        if (ToolUtil.isNotEmpty(sysAreaList)){
            for (SysArea sysArea : sysAreaList) {
                // 父级区域
                if (Objects.nonNull(sysArea.getLevel()) && sysArea.getLevel() == 1){
                    List<SysArea> areaList = sysAreaMapper.getAreaListByAreaId(sysArea.getAreaId(),sysCode);
                    areaList.add(sysArea);

                    // 如果父级有存在子级
                    if (ToolUtil.isNotEmpty(areaList)){
                        for (SysArea downSysArea : areaList) {
                            AreaIsExistDTO downAreaIsExistDTO = areaIdExist(downSysArea, downSysArea.getAreaId(), sysCode);
                            // 删除失败的区域
                            if (!downAreaIsExistDTO.getIsDelete()){
                                downAreaIsExistDTOS.add(downAreaIsExistDTO);
                                downAreaNameData.add(downSysArea.getAreaName());
                                downAreaIdData.add(downSysArea.getAreaId());
                            }
                        }
                        for (AreaIsExistDTO downAreaIsExistDTO : downAreaIsExistDTOS) {
                            List<String> dcIdNames = downAreaIsExistDTO.getDcIdNames();
                            List<Long> dcIds = downAreaIsExistDTO.getDcIds();
                            List<String> supplierNames = downAreaIsExistDTO.getSupplierNames();
                            List<Long> supplierIds = downAreaIsExistDTO.getSupplierIds();
                            List<Long> colonelDataIds = downAreaIsExistDTO.getColonelDataIds();
                            List<String> colonelDataNames = downAreaIsExistDTO.getColonelDataNames();
                            List<Long> branchDataIds = downAreaIsExistDTO.getBranchDataIds();
                            List<String> branchDataNames = downAreaIsExistDTO.getBranchDataNames();
                            if (ToolUtil.isNotEmpty(dcIdNames)){
                                dcNameData.addAll(dcIdNames);
                            }
                            if (ToolUtil.isNotEmpty(dcIds)){
                                dcIdData.addAll(dcIds);
                            }
                            if (ToolUtil.isNotEmpty(supplierNames)){
                                supplierNameData.addAll(supplierNames);
                            }
                            if (ToolUtil.isNotEmpty(supplierIds)){
                                supplierIdData.addAll(supplierIds);
                            }
                            if (ToolUtil.isNotEmpty(colonelDataIds)){
                                colonelIds.addAll(colonelDataIds);
                            }
                            if (ToolUtil.isNotEmpty(colonelDataNames)){
                                colonelNames.addAll(colonelDataNames);
                            }
                            if (ToolUtil.isNotEmpty(branchDataIds)){
                                branchIds.addAll(branchDataIds);
                            }
                            if (ToolUtil.isNotEmpty(branchDataNames)){
                                branchNames.addAll(branchDataNames);
                            }

                            areaIsExistDTO.setIsDelete(Boolean.FALSE);
                            if (downAreaIsExistDTO.getOrderData())
                                areaIsExistDTO.setOrderData(Boolean.TRUE);
                            if (downAreaIsExistDTO.getColonelData())
                                areaIsExistDTO.setColonelData(Boolean.TRUE);
                            if (downAreaIsExistDTO.getBranchData())
                                areaIsExistDTO.setBranchData(Boolean.TRUE);
                            if (downAreaIsExistDTO.getSkuData())
                                areaIsExistDTO.setSkuData(Boolean.TRUE);
                        }
                        areaIsExistDTO.setDownAreaName(downAreaNameData);
                        areaIsExistDTO.setDownAreaId(downAreaIdData);
                        areaIsExistDTO.setDcIdNames( new ArrayList<>(dcNameData));
                        areaIsExistDTO.setDcIds(new ArrayList<>(dcIdData));
                        areaIsExistDTO.setSupplierNames(new ArrayList<>( supplierNameData));
                        areaIsExistDTO.setSupplierIds(new ArrayList<>(supplierIdData));
                        areaIsExistDTO.setColonelDataIds(new ArrayList<>(colonelIds));
                        areaIsExistDTO.setColonelDataNames(new ArrayList<>(colonelNames));
                        areaIsExistDTO.setBranchDataIds(new ArrayList<>( branchIds));
                        areaIsExistDTO.setBranchDataNames(new ArrayList<>( branchNames));
                        areaIsExistDTO.setIsDelete(Boolean.FALSE);
                        // 如果没有不能删除的区域
                        if (ToolUtil.isEmpty(downAreaIsExistDTOS)){
                            for (SysArea area : areaList) {
                                sysAreaMapper.updateDeleted(area.getAreaId(), sysCode, 1);
                            }
                            areaIsExistDTO.setIsDelete(Boolean.TRUE);
                        }
                        return areaIsExistDTO;
                    }else {
                        // 如果没有子集就把他当作子级处理
                        sysArea.setLevel(2);
                    }
                }
                // 子级区域
                if (Objects.nonNull(sysArea.getLevel()) && sysArea.getLevel() == 2){
                  areaIsExistDTO = areaIdExist(sysArea, sysArea.getAreaId(), sysCode);
                  if (areaIsExistDTO.getIsDelete()){
                      sysAreaMapper.updateDeleted(sysArea.getAreaId(), sysCode, 1);
                  }
                }
            }
        }
       return areaIsExistDTO;
    }

    /**
     * 获得区域城市
     *
     * @param areaId 区域城市id
     * @return 区域城市
     */
    @Override
    public SysArea getSysArea(Long areaId) {
        return sysAreaMapper.selectById(areaId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SysAreaRespVO> getSysAreaPage(SysAreaPageReqVO pageReqVO) {
        Page<SysAreaPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<SysAreaRespVO> sysAreaRespVOPage = sysAreaMapper.selectAreaPage(page, pageReqVO, SecurityUtils.getLoginUser().getDcId());
        return new PageResult<>(sysAreaRespVOPage.getRecords(), sysAreaRespVOPage.getTotal());
    }

    /**
     * @Description: 运营商获取可绑定的区域城市
     * @Author: liuxingyu
     * @Date: 2024/3/7 14:47
     */
    @Override
    public List<SysAreaRespVO> getDcNotBindArea(Long dcId) {
        List<SysArea> aresList = sysAreaMapper.getDcNotBindArea(dcId);
        return HutoolBeanUtils.toBean(aresList, SysAreaRespVO.class);
    }

    /**
     * @Description: 获取所有的区域城市
     * @Author: liuxingyu
     * @Date: 2024/3/15 17:51
     */
    @Override
    public List<SysArea> getAreaList(AreaListReqVO reqVO) {
        if (ObjectUtil.isNotNull(SecurityUtils.getSupplierId())) {
            List<SysArea> supplierAreaList = sysAreaMapper.getAreaListBySupplierId(SecurityUtils.getSupplierId());
            return supplierAreaList.stream().distinct().collect(Collectors.toList());
        }
        if (ObjectUtil.isNotNull(reqVO.getSupplierId())) { // 查询指定入驻商关联的城市
            List<SysArea> supplierAreaList = sysAreaMapper.getAreaListBySupplierId(reqVO.getSupplierId());
            return supplierAreaList.stream().distinct().collect(Collectors.toList());
        }
        List<SysArea> areaAllList = sysAreaMapper.getAreaAllList(reqVO);
        return areaAllList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<SysArea> getAreaListBySysCode(Long sysCode) {
        return sysAreaMapper.getAreaList(new SysAreaRespVO(sysCode));
    }

    @Override
    public void reloadAreaDtoCache(Long areaId) {
        SysArea sysArea = sysAreaMapper.selectById(areaId);
        AreaDTO areaDto = HutoolBeanUtils.toBean(sysArea, AreaDTO.class);
        areaDtoCache.put(areaId, areaDto);
    }

    @Override
    public void removeAreaDtoCache(Long areaId) {
        areaDtoCache.remove(areaId);
    }

    @Override
    public List<SysAreaSelectedRespVO> getSelectedSysArea(List<Long> areaIds) {
        return BeanUtil.copyToList(sysAreaMapper.selectedSelectedSysArea(areaIds), SysAreaSelectedRespVO.class);
    }

    /**
     * @Description: 根据入驻商获取区域城市
     * @Author: liuxingyu
     * @Date: 2024/4/18 9:37
     */
    @Override
    public List<SysArea> getBySupplierId(Long supplierId) {
        return sysAreaMapper.getAreaListBySupplierId(supplierId).stream().distinct().collect(Collectors.toList());
    }

    @Override
    public boolean checkAreaByAreaId(Long areaId) {
        if (areaId == null) {
            throw exception(SYS_AREA_IS_NULL);
        }
        SysArea sysArea = sysAreaMapper.selectById(areaId);
        if (ToolUtil.isEmpty(sysArea)) {
            throw exception(SYS_AREA_NOT_EXISTS);
        }
        if (sysArea.getLevel() != INT_TWO) {
            throw exception(SYS_AREA_LEVEL_NOT_TWO);
        }

        return true;
    }

    @Override
    public SysArea getDefaultBySyscode(Long sysCode) {
        return sysAreaMapper.getDefaultBySyscode(sysCode);
    }

    @Override
    public List<SysArea> getAreaDownList(Long areaId) {
        List<SysArea> areaAllList = sysAreaMapper.getAreaDownList(areaId, SecurityUtils.getLoginUser().getDcId());
        return areaAllList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<AreaDTO> getAreaBySyscodeAndDcId(Long sysCode, Long dcId) {
        return sysAreaMapper.getAreaBySyscodeAndDcId(sysCode, dcId);
    }

    /**
     * @Description: 校验区域城市级别
     * @Author: liuxingyu
     * @Date: 2024/4/1 15:56
     */
    private void checkAreaLevel(SysAreaSaveReqVO createReqVO) {
        //ID不为空则修改
        if (ObjectUtil.isNotNull(createReqVO.getAreaId())) {
            //如果当前分类非末级则不允许改变层级(存在子级)
            SysArea sysArea = sysAreaMapper.selectById(createReqVO.getAreaId());
            List<SysArea> list = sysAreaMapper.getListByPid(createReqVO.getAreaId());
            if (ObjectUtil.notEqual(nullToZero(createReqVO.getPid()), nullToZero(sysArea.getPid())) && ObjectUtil.isNotEmpty(list)) {
                throw exception(SYS_AREA_IS_SUB_EXISTS);
            }
        }
        if (ObjectUtil.isNotNull(createReqVO.getPid()) && ObjectUtil.notEqual(createReqVO.getPid(), NumberPool.LONG_ZERO)) {
            SysArea sysArea = sysAreaMapper.selectById(createReqVO.getPid());
            if (ObjectUtil.isNull(sysArea) || ObjectUtil.isNull(sysArea.getLevel())) {
                throw exception(SYS_AREA_PID_ERROR);
            }
            //校验级别
            if (ObjectUtil.equal(sysArea.getLevel(), INT_TWO)) {
                throw exception(SYS_AREA_LEVEL_EXCEED_MAX);
            }
            createReqVO.setLevel(sysArea.getLevel() + NumberPool.INT_ONE);
        } else {
            createReqVO.setLevel(NumberPool.INT_ONE);
        }
        if (ObjectUtil.isNotNull(createReqVO.getDcId()) && ObjectUtil.notEqual(createReqVO.getLevel(), NumberPool.INT_TWO)) {
            throw exception(SYS_AREA_LEVEL_NOT_TWO);
        }
    }

    /**
     * @Description: 同步子集级别
     * @Author: liuxingyu
     * @Date: 2024/6/6 14:49
     */
    private void synSublevel(SysAreaSaveReqVO createReqVO) {
        Integer level = createReqVO.getLevel();
        //同步二级
        List<SysArea> areaList = sysAreaMapper.getListByPid(createReqVO.getAreaId());
        if (ObjectUtil.isEmpty(areaList)) {
            return;
        }
        ++level;
        if (level > NumberPool.INT_ONE) {
            throw exception(PRDT_AREA_CLASS_SUB_LEVEL_MAX);
        }
        //分装数据执行修改
        Integer level2 = level;
        areaList.forEach(x -> {
            //校验子级 是否绑定了运营商
            if (ObjectUtil.isNotNull(x.getDcId()) && ObjectUtil.notEqual(level2, NumberPool.INT_TWO)) {
                throw exception(SYS_SUB_AREA_LEVEL_NOT_TWO);
            }
            x.setLevel(level2);
        });
        sysAreaMapper.updateBatch(areaList);
    }

    /**
     * @Description: 校验区域城市名称
     * @Author: liuxingyu
     * @Date: 2024/5/31 10:14
     */
    private void checkAreaName(Integer level, String areaName, Long areaId) {
        Long count = sysAreaMapper.getByAreaName(level, areaName, areaId);
        if (ObjectUtil.notEqual(NumberPool.LONG_ZERO, count)) {
            throw exception(SYS_AREA_NAME_COLLIDE);
        }
    }

    private void validateSysAreaExists(Long areaId) {
        if (sysAreaMapper.selectById(areaId) == null) {
            throw exception(SYS_AREA_NOT_EXISTS);
        }
    }

    /**
     * @Description: 如果为null 转换成0
     * @Author: liuxingyu
     * @Date: 2024/6/7 11:19
     */
    private Long nullToZero(Long pid) {
        return ObjectUtil.isNull(pid) ? 0L : pid;
    }

    @Override
        public List<SysArea> getAreaListByDcId(Long dcId) {
        SysAreaRespVO sysAreaRespVO = new SysAreaRespVO();
        sysAreaRespVO.setDcId(dcId);
        return sysAreaMapper.getAreaList(sysAreaRespVO);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String impordData(List<SysAreaImportExcel> sysAreaList) {
        String resultMessage = "";
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        List<SysArea> result = new ArrayList<>();
        ArrayList<SysAreaImportExcel> addList = new ArrayList<>();

        try {
            if (sysAreaList.isEmpty()) {
                // 如果导入数据为空，则不进行数据导入
                throw exception(500, "导入数据为空");
            }
            Map<String, Long> prdtSupplierMap = new HashMap<>(); // 存储已插入的类别，避免重复插入
            // 数据校验
            for (int line = 0; line < sysAreaList.size(); line++) {
                if (failureMsg.length() > 2000) {
                    break;
                }
                int cellNumber = line + 3;
                SysAreaImportExcel itemData = sysAreaList.get(line);
                // 导入数据校验
                if (ToolUtil.isEmpty(itemData.getAreaS())){
                    failureMsg.append(StringUtils.format("<br/>第{}行数据省/市/区不能为空。</span>", cellNumber));
                    failureNum++;
                    continue;
                }
                if (!(itemData.getAreaS().matches("^.*省/.*市/.*区$") || itemData.getAreaS().matches("^.*省/.*市/.*县$") || itemData.getAreaS().matches("^.*省/.*市/.*市$"))){
                    failureMsg.append(StringUtils.format("<br/>第{}行数据省/市/区格式不正确。</span>", cellNumber));
                    failureNum++;
                    continue;
                }
                if (ToolUtil.isEmpty(itemData.getAreaName())){
                    failureMsg.append(StringUtils.format("<br/>第{}行数据sku不能为空。</span>", cellNumber));
                    failureNum++;
                    continue;
                }
                SysAreaImportExcel sysAreaImportExcel = sysAreaList.get(line);
                String[] areaName = sysAreaImportExcel.getAreaS().split("/");
                SysAreaCity firstArea = sysAreaCityService.getByNameAndParent(areaName[0], 0L, 0);
                if (ToolUtil.isEmpty(firstArea)){
                    failureMsg.append(StringUtils.format("<br/>第{}行一级区域数据不存在。</span>", cellNumber));
                    failureNum++;
                    continue;
                }
                SysAreaCity secondArea = sysAreaCityService.getByNameAndParent(areaName[1], firstArea.getAreaCityId(), 1);
                if (ToolUtil.isEmpty(secondArea)){
                    failureMsg.append(StringUtils.format("<br/>第{}行二级区域数据不存在。</span>", cellNumber));
                    failureNum++;
                    continue;
                }
                SysAreaCity threeArea = sysAreaCityService.getByNameAndParent(areaName[2], secondArea.getAreaCityId(), 2);
                if (ToolUtil.isEmpty(threeArea)){
                    failureMsg.append(StringUtils.format("<br/>第{}行三级区域数据不存在。</span>", cellNumber));
                    failureNum++;
                    continue;
                }
                if (ToolUtil.isNotEmpty(sysAreaImportExcel.getPid())){
                    SysArea sysArea = sysAreaMapper.selectById(sysAreaImportExcel.getPid());
                    if (ToolUtil.isEmpty(sysArea)){
                        failureMsg.append(StringUtils.format("<br/>第{}行上级城市数据不存在。</span>", cellNumber));
                        failureNum++;
                        continue;
                    }
                    if (ObjectUtil.equal(sysArea.getLevel(), INT_TWO)) {
                        failureMsg.append(StringUtils.format("<br/>第{}行区域城市级别超出最大值。</span>", cellNumber));
                        failureNum++;
                        continue;
                    }
                }
                sysAreaImportExcel.setThreeAreaCityId(threeArea.getAreaCityId());
                addList.add(itemData);
            }

            for (int line = 0; line < addList.size(); line++) {
                int cellNumber = line + 3;

                SysAreaImportExcel sysAreaImportExcel = addList.get(line);
                // 上级城市不为空的话
                if (ToolUtil.isNotEmpty(sysAreaImportExcel.getPid())){
                    SysArea sysArea = sysAreaMapper.selectById(sysAreaImportExcel.getPid());
                    if (ToolUtil.isNotEmpty(sysArea)){
                        sysAreaImportExcel.setPid(sysArea.getAreaId());
                        sysAreaImportExcel.setLevel((sysArea.getLevel()==null?1:sysArea.getLevel())+1);
                    }
                }else {
                    sysAreaImportExcel.setLevel(1);
                }
                // 排序为空的话
                if (ToolUtil.isEmpty(sysAreaImportExcel.getSortNum())){
                    sysAreaImportExcel.setSortNum("99999");
                }
            }
            List<SysArea> addSysAreaList = HutoolBeanUtils.toBean(addList, SysArea.class);

            for (SysArea sysArea : addSysAreaList) {
                Long dcId = SecurityUtils.getLoginUser().getDcId();
                sysArea.setStatus(1L);
                sysArea.setLocalFlag("0");
                sysArea.setDcId(dcId);
                if (ToolUtil.isEmpty(sysArea.getSortNum())) sysArea.setSortNum(1);
                sysAreaMapper.insert(sysArea);
                //保存区域城市与运营商关联表
                if (ToolUtil.isNotEmpty(dcId)) {
                    SysDcArea sysDcArea = new SysDcArea();
                    sysDcArea.setAreaId(sysArea.getAreaId());
                    sysDcArea.setDcId(dcId);
                    sysDcArea.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                    sysDcAreaMapper.insert(sysDcArea);
                    sysDcAreaService.reloadCache(sysArea.getDcId());
                }
                //新增 运营商区域城市拉链表
                if(ToolUtil.isNotEmpty(dcId)){
                    SysAreaSaveReqVO createReqVO = HutoolBeanUtils.toBean(sysArea, SysAreaSaveReqVO.class);
                    if (ToolUtil.isNotEmpty(createReqVO)){
                        createReqVO.setDcId(dcId);
                        createReqVO.setAreaId(sysArea.getAreaId());
                        sysdcAreaZipService.insertSysDcAreaZip(createReqVO,sysArea);
                        sysDcAreaService.reloadCache(sysArea.getDcId());
                    }
                }
                this.reloadAreaDtoCache(sysArea.getAreaId());
                successNum++;
            }
        }catch (Exception e){
            failureNum++;
            failureMsg.append(StringUtils.format("<br/>区域数据导入失败，错误信息：{}。", e.getMessage()));
        }
        if (failureNum > 0) {
            resultMessage = String.format("共导入%d条，成功%d条，失败%d条。失败原因如下：", sysAreaList.size(), successNum, failureNum)
                    + failureMsg.toString();
        } else {
            resultMessage = String.format("恭喜您，数据已全部导入成功！共 %d 条", successNum);
        }
        return resultMessage;
    }

    @Override
    public SysArea getSysAreaByThreeAreaCityId(Long threeAreaCityId) {
        return sysAreaMapper.getSysAreaByThreeAreaCityId(threeAreaCityId);
    }

    public AreaIsExistDTO areaIdExist(SysArea sysArea, Long areaId, Long sysCode) {
        AreaIsExistDTO areaIsExistDTO = new AreaIsExistDTO();
        areaIsExistDTO.setIsDelete(Boolean.FALSE);
        // 运营商是否引用
        SysDcArea dcArea = sysDcAreaService.getDcAreaByDcIdOrAreaId(sysArea.getDcId(), sysArea.getAreaId(), sysCode);
        if (ObjectUtil.isNotNull(dcArea)){
            areaIsExistDTO.setDcIds(Collections.singletonList(dcArea.getDcId()));
            String dcName = sysDcService.getSysDc(dcArea.getDcId()).getDcName();
            areaIsExistDTO.setDcIdNames(Collections.singletonList(dcName)); ;
        }
        // 入驻商是否引用
        List<SupplierAreaDTO> supplierAreaByAreaId = sysSupplierAreaService.getSupplierAreaByAreaId(sysArea.getAreaId());
        if (ToolUtil.isNotEmpty(supplierAreaByAreaId)){
            List<Long> supplierIds = supplierAreaByAreaId.stream().map(SupplierAreaDTO::getSupplierId).collect(Collectors.toList());
            List<SysSupplier> supplierList = sysSupplierAreaService.getSupplierListByIds(supplierIds);
            areaIsExistDTO.setSupplierIds(supplierIds);
            areaIsExistDTO.setSupplierNames(supplierList.stream().map(SysSupplier::getSupplierName).collect(Collectors.toList()));
        }
        // 是否有生成订单数据
        Boolean orderData = orderApi.getAreaIdExistOrder(sysArea.getAreaId(), sysCode).getCheckedData();
        areaIsExistDTO.setOrderData(orderData);
        // 业务员关联
        List<MemColonelRespVO> colonelData = colonelApi.getAreaIdExistColone(sysArea.getAreaId(), sysCode).getCheckedData();
        areaIsExistDTO.setColonelData(ToolUtil.isNotEmpty(colonelData)?Boolean.TRUE:Boolean.FALSE);
        if (ToolUtil.isNotEmpty(colonelData)){
            List<Long> colonelIds = colonelData.stream().map(MemColonelRespVO::getColonelId).collect(Collectors.toList());
            List<String> colonelNames = colonelData.stream().map(MemColonelRespVO::getColonelName).collect(Collectors.toList());
            areaIsExistDTO.setColonelDataIds(colonelIds);
            areaIsExistDTO.setColonelDataNames(colonelNames);
        }
        // 门店是否使用
        List<BranchDTO> branchData = branchApi.getAreaIdExistBranch(sysArea.getAreaId(), sysCode).getCheckedData();
        areaIsExistDTO.setBranchData(ToolUtil.isNotEmpty(branchData)?Boolean.TRUE:Boolean.FALSE);
        if (ToolUtil.isNotEmpty(branchData)){
            List<Long> branchIds = branchData.stream().map(BranchDTO::getBranchId).collect(Collectors.toList());
            List<String> branchNames = branchData.stream().map(BranchDTO::getBranchName).collect(Collectors.toList());
            areaIsExistDTO.setBranchDataIds(branchIds);
            areaIsExistDTO.setBranchDataNames(branchNames);
        }
        // 商品是否使用
        Boolean skuData = skuApi.getAreaIdExistSku(sysArea.getAreaId(), sysCode).getCheckedData();
        areaIsExistDTO.setSkuData(skuData);



        // 当该区域没有产生数据时就可以删除
        if (Objects.isNull(dcArea) && ToolUtil.isEmpty(supplierAreaByAreaId) && ToolUtil.isEmpty(colonelData) && ToolUtil.isEmpty(branchData)
                && !orderData
                && !skuData ){
            areaIsExistDTO.setIsDelete(Boolean.TRUE);
        }
        return areaIsExistDTO;
    }
}
