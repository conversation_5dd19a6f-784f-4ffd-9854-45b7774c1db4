package com.zksr.system.mq;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.openapi.service.OpenapiProcessTemplate;
import com.zksr.system.service.ISysCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.function.Consumer;

/**
 * 第三方系统同步数据消息队列消费者
 * @date 2024/7/12 10:10
 * <AUTHOR>
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class OpenapiConsumer {

    @Autowired
    private OpenapiProcessTemplate saveprdt;

    @Autowired
    private OpenapiProcessTemplate saveprdtDate;

    @Autowired
    private OpenapiProcessTemplate saveprdtStock;

    @Autowired
    private OpenapiProcessTemplate confirmReceipt;

    @Autowired
    private OpenapiProcessTemplate confirmReturn;

    @Autowired
    private OpenapiProcessTemplate delivery;

    @Autowired
    private OpenapiProcessTemplate orderlog;

    @Autowired
    private OpenapiProcessTemplate orderReceiveCallback;

    @Autowired
    private OpenapiProcessTemplate orderCancelReceiveCallback;

    @Autowired
    private OpenapiProcessTemplate orderCancel;

    @Autowired
    private OpenapiProcessTemplate afterCancel;

    @Autowired
    private OpenapiProcessTemplate afterOrderReceiveCallback;

    @Autowired
    private OpenapiProcessTemplate savesupplier;

    @Autowired
    private  OpenapiProcessTemplate addHdfkSettle;

    @Autowired
    private  OpenapiProcessTemplate yhDataProcess;

    @Autowired
    private  OpenapiProcessTemplate afterlog;

    @Autowired
    private ISysCacheService sysCacheService;

    @Autowired
    private OpenapiProcessTemplate registerBranch;

    /**
     * 商品保存 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_saveprdt(){
        return (data -> {
            log.info("收到商品保存推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            saveprdt.invoke(data);
        });

    }

    /**
     * 通过日志中的开放能力ID获取对应的登录信息
     * @param data
     */
    private void processHeader(SysInterfaceLog data) {
        LoginOpensource loginOpensource = new LoginOpensource();

        //获取开放能力信息
        OpensourceDto opensourceDto = sysCacheService.getOpensourceByMerchantId(data.getSupplierId());
        loginOpensource.setOpensourceDto(opensourceDto);
        loginOpensource.setSysCode(opensourceDto.getSysCode());
        loginOpensource.setOpensourceId(opensourceDto.getOpensourceId());


        //设置
        SecurityContextHolder.setSysCode(loginOpensource.getSysCode() + "");
        SecurityContextHolder.setOpensourceId(loginOpensource.getOpensourceId()+"");
        SecurityContextHolder.set(OpenapiSecurityConstants.LOGIN_OPENSOURCE, loginOpensource);
    }

    /**
     * 商品生产日期更新 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_saveprdt_date(){
        return (data -> {
            log.info("收到商品生产日期更新推送信息:{}", JsonUtils.toJsonString(data));
            //将需要同步的数据发送给指挥者
            saveprdtDate.invoke(data);
        });
    }

    /**
     * 商品库存更新 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_saveprdt_stock(){
        return (data -> {
            //log.info("收到商品库存更新推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            saveprdtStock.invoke(data);
        });
    }

    /**
     * 订单收货确认 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_confirm_receipt(){
        return (data -> {
            log.info("收到订单收货确认推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            confirmReceipt.invoke(data);
        });
    }

    /**
     * 售后退货确认 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_confirm_return(){
        return (data -> {
            log.info("收到售后退货确认推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            confirmReturn.invoke(data);
        });
    }

    /**
     * 订单发货 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_delivery(){
        return (data -> {
            log.info("收到订单发货推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            delivery.invoke(data);
        });
    }

    /**
     * 订单状态 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_orderlog(){
        return (data -> {
            log.info("收到订单状态推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            orderlog.invoke(data);
        });
    }

    /**
     * 订单接收成功通知 消息  消费者
     * <AUTHOR>
     * @date 2024/07/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_order_receive_callback(){
        return (data -> {
            log.info("收到订单接收成功通知推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            orderReceiveCallback.invoke(data);
        });
    }

    /**
     * 订单取消接收通知 消息  消费者
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_order_cancel_receive_callback(){
        return (data -> {
            log.info("收到订单取消接收通知推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            orderCancelReceiveCallback.invoke(data);
        });
    }


    /**
    * 订单发货前取消通知 消息  消费者
    * @date 2024/12/4 15:12
    * <AUTHOR>
    */
    @Bean
    public Consumer<SysInterfaceLog> openapi_order_cancel(){
        return (data -> {
            log.info("收到订单发货前取消推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            orderCancel.invoke(data);
        });
    }

    /**
     * 退货确认前取消(售后取消) 消息  消费者
     * @date 2024/12/4 15:12
     * <AUTHOR>
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_after_cancel(){
        return (data -> {
            log.info("收到退货确认前取消(售后取消)推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            afterCancel.invoke(data);
        });
    }


    /**
     * 售后(退单)接收成功通知 消息  消费者
     * <AUTHOR>
     * @date 2024/10/12 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_after_order_receive_callback(){
        return (data -> {
            log.info("收到售后接收成功通知推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            afterOrderReceiveCallback.invoke(data);
        });
    }

    @Bean
    public Consumer<SysInterfaceLog> openapi_afterlog(){
        return (data -> {
            log.info("收到售后状态推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            afterlog.invoke(data);
        });
    }

    /**
     * 入驻商保存 消息  消费者
     * <AUTHOR>
     * @date 2024/10/18 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_savesupplier(){
        return (data -> {
            log.info("收到入驻商保存推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            savesupplier.invoke(data);
        });

    }

    /**
     * 新增货到付款清账能力 消息  消费者
     * <AUTHOR>
     * @date 2024/10/23 16:42
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_add_hdfk_settle(){
        return (data -> {
            log.info("收到新增货到付款清账能力推送信息:{}",data);
            //将需要同步的数据发送给指挥者
            addHdfkSettle.invoke(data);
        });

    }

    /**
     * 处理批量补货单
     * 消息来源 {@link OpenapiProducer#sendSubmitBatchYh(SysInterfaceLog)}
     * 最终消费 {@link YhDataProcess#process(SysInterfaceLog)}
     */
    public Consumer<SysInterfaceLog> openapi_batch_create_yh() {
        return (data -> {
            log.info("批量补货单数据:{}",data);
            //将需要同步的数据发送给指挥者
            yhDataProcess.invoke(data);
        });
    }

    /**
     * ERP app注册门店
     * {@link OpenapiProducer#sendRegisterBranchMsg(SysInterfaceLog)}
     * @return
     */
    @Bean
    public Consumer<SysInterfaceLog> openapi_registerBranch(){
        return (data -> {
            log.info(" 收到门店注册消息:{}",data);
            //将需要同步的数据发送给指挥者
            registerBranch.invoke(data);
        });

    }


}
