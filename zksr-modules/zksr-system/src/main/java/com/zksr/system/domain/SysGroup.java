package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 平台商城市分组对象 sys_group
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@TableName(value = "sys_group")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysGroup extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @TableId(type = IdType.AUTO)
    private Long groupId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 平台商城市分组名 */
    @Excel(name = "平台商城市分组名")
    private String groupName;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

}
