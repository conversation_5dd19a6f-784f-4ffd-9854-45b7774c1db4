package com.zksr.system.controller.partnerConfig.vo;

import com.zksr.system.api.partnerConfig.dto.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台商配置(由软件商设置)对象 sys_partner_config
 *
 * <AUTHOR>
 * @date 2024-03-12
 */
@Data
@ApiModel("平台商配置(由软件商设置) - sys_partner_config分页 Request VO")
public class SysPartnerConfigSaveReqVO {

    @ApiModelProperty("小程序配置")
    private AppletBaseConfigDTO appletBaseConfigDto;

    @ApiModelProperty("合利宝支付配置")
    private HeLiBaoPayConfigDTO heLiBaoPayConfigDto;

    @ApiModelProperty("支付配置")
    private PayConfigDTO payConfigDTO;

    @ApiModelProperty("支付账号配置")
    private PayAccountConfigDTO payAccountConfigDTO;

    @ApiModelProperty("快递查询配置")
    private CourierConfigDTO courierConfigDTO;

    @ApiModelProperty("平台商设备配置")
    private DeviceSettingConfigDTO deviceSettingConfigDTO;

    @ApiModelProperty("短信配置")
    private SmsConfigDTO smsConfigDTO;

    @ApiModelProperty("微信b2b支付配置")
    private WxB2bPayConfigDTO wxB2bPayConfigDTO;
}
