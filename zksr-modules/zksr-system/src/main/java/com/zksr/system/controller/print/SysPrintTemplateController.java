package com.zksr.system.controller.print;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysPrintTemplate;
import com.zksr.system.service.ISysPrintTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.print.vo.SysPrintTemplatePageReqVO;
import com.zksr.system.controller.print.vo.SysPrintTemplateSaveReqVO;
import com.zksr.system.controller.print.vo.SysPrintTemplateRespVO;
import com.zksr.system.convert.print.SysPrintTemplateConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 软件商打印模版Controller
 *
 * <AUTHOR>
 * @date 2024-09-11
 */
@Api(tags = "管理后台 - 软件商打印模版接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/print")
public class SysPrintTemplateController {
    @Autowired
    private ISysPrintTemplateService sysPrintTemplateService;

    /**
     * 新增软件商打印模版
     */
    @ApiOperation(value = "新增软件商打印模版", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "软件商打印模版", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysPrintTemplateSaveReqVO createReqVO) {
        return success(sysPrintTemplateService.insertSysPrintTemplate(createReqVO));
    }

    /**
     * 修改软件商打印模版
     */
    @ApiOperation(value = "修改软件商打印模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "软件商打印模版", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysPrintTemplateSaveReqVO updateReqVO) {
        sysPrintTemplateService.updateSysPrintTemplate(updateReqVO);
        return success(true);
    }

    /**
     * 删除软件商打印模版
     */
    @ApiOperation(value = "删除软件商打印模版", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "软件商打印模版", businessType = BusinessType.DELETE)
    @DeleteMapping("/{printTemplateIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] printTemplateIds) {
        sysPrintTemplateService.deleteSysPrintTemplateByPrintTemplateIds(printTemplateIds);
        return success(true);
    }

    /**
     * 获取软件商打印模版详细信息
     */
    @ApiOperation(value = "获得软件商打印模版详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{printTemplateId}")
    public CommonResult<SysPrintTemplateRespVO> getInfo(@PathVariable("printTemplateId") Long printTemplateId) {
        SysPrintTemplate sysPrintTemplate = sysPrintTemplateService.getSysPrintTemplate(printTemplateId);
        return success(SysPrintTemplateConvert.INSTANCE.convert(sysPrintTemplate));
    }

    /**
     * 分页查询软件商打印模版
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得软件商打印模版分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysPrintTemplateRespVO>> getPage(@Valid SysPrintTemplatePageReqVO pageReqVO) {
        PageResult<SysPrintTemplate> pageResult = sysPrintTemplateService.getSysPrintTemplatePage(pageReqVO);
        return success(SysPrintTemplateConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "print:printTemplate:add";
        /** 编辑 */
        public static final String EDIT = "print:printTemplate:edit";
        /** 删除 */
        public static final String DELETE = "print:printTemplate:remove";
        /** 列表 */
        public static final String LIST = "print:printTemplate:list";
        /** 查询 */
        public static final String GET = "print:printTemplate:query";
        /** 停用 */
        public static final String DISABLE = "print:printTemplate:disable";
        /** 启用 */
        public static final String ENABLE = "print:printTemplate:enable";
    }
}
