package com.zksr.system.convert.visualSetting.visualSettingTemplate;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplateRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplateSaveReqVO;
import com.zksr.system.domain.VisualSettingTemplate;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 可视化接口模板 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-29
*/
@Mapper
public interface VisualSettingTemplateConvert {

    VisualSettingTemplateConvert INSTANCE = Mappers.getMapper(VisualSettingTemplateConvert.class);

    VisualSettingTemplateRespVO convert(VisualSettingTemplate visualSettingTemplate);

    VisualSettingTemplate convert(VisualSettingTemplateSaveReqVO visualSettingTemplateSaveReq);

    PageResult<VisualSettingTemplateRespVO> convertPage(PageResult<VisualSettingTemplate> visualSettingTemplatePage);
}