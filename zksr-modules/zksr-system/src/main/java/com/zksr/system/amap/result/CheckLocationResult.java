package com.zksr.system.amap.result;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 多表现围栏返回实体类
 * @Author: liuxingyu
 * @Date: 2024/3/27 16:43
 */
@Data
public class CheckLocationResult {
    /**
     * 0代表成功，其他参考错误码说明
     */
    @ApiModelProperty("返回状态码")
    private Long errcode;

    @ApiModelProperty("返回状态描述")
    private String errmsg;

    @ApiModelProperty("返回详细描述")
    private String errdetail;

    @ApiModelProperty("返回对象")
    private Data data;

    @lombok.Data
    public static class Data {
        @ApiModelProperty("数据总数量")
        private Long count;

        @ApiModelProperty("围栏返回对象")
        private List<Results> results;

        @lombok.Data
        public static class Results {
            @ApiModelProperty("围栏的唯一标识")
            private Long gfid;

            @ApiModelProperty("地理围栏名称")
            private String gfname;

            @ApiModelProperty("指定坐标是否在围栏中 1：在，0：不在；")
            private Integer in;
        }
    }
}
