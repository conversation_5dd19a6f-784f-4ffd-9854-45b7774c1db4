package com.zksr.system.controller.supplier;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.config.AnntoProxyConfig;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.common.third.wx.dto.WxCode2SessionResponse;
import com.zksr.system.api.supplier.vo.SupplierDropdownReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.controller.supplier.vo.*;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaRespVO;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.service.ISysSupplierService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.system.enums.ErrorCodeConstants.JUST_SUPPLIER_ROLE;

/**
 * 入驻商信息Controller
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Api(tags = "管理后台 - 入驻商信息接口", produces = "application/json")
@Validated
@RestController
@Slf4j
@RequestMapping("/supplier")
public class SysSupplierController {
    @Autowired
    private ISysSupplierService sysSupplierService;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Resource
    private AnntoProxyConfig anntoProxyConfig;

    /**
    * @Description: 根据区域获取绑定的入驻商ID
    * @Param: Long areaId
    * @return: List<Long>
    * @Author: liuxingyu
    * @Date: 2024/4/12 14:57
    */
    @ApiOperation(value = "根据区域获取绑定的入驻商ID",httpMethod = HttpMethod.GET)
    @GetMapping("/getSupplierByAreaId")
    public CommonResult<List<SysSupplierAreaRespVO>> getSupplierByAreaId(@RequestParam("areaId") Long areaId){
        return success(sysSupplierService.sysSupplierService(areaId));
    }

    /**
     * @Description: 获取入驻商下拉选
     * @Param: String supplier
     * @return:
     * @Author: liuxingyu
     * @Date: 2024/4/12 9:29
     */
    @ApiOperation(value = "获取入驻商下拉选", httpMethod = HttpMethod.GET)
    @GetMapping("/getSupplierDropdown")
    public CommonResult<PageResult<SysSupplierRespVO>> getSupplierDropdown(SupplierDropdownReqVO reqVO) {
        return success(sysSupplierService.getSupplierDropdown(reqVO));
    }


    /**
     * @Description: 获取所有入驻商
     * @Param:
     * @return: CommonResult<List < SysSupplierRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/22 14:33
     */
    @ApiOperation(value = "获取所有入驻商", httpMethod = HttpMethod.GET)
    @GetMapping("/getSupplierList")
    @DataScope(supplierAlias = "`sys_supplier`")
    public CommonResult<List<SysSupplierRespVO>> getSupplierList(SysSupplier sysSupplier) {
        return success(HutoolBeanUtils.toBean(sysSupplierService.getSupplierList(sysSupplier), SysSupplierRespVO.class));
    }

    /**
     * 新增入驻商信息
     */
    @ApiOperation(value = "新增入驻商信息", httpMethod = "POST", notes = "system:supplier:add")
    @RequiresPermissions("system:supplier:add")
    @Log(title = "入驻商信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysSupplierSaveReqVO createReqVO) {
        return success(sysSupplierService.insertSysSupplier(createReqVO));
    }

    /**
     * 修改入驻商信息
     */
    @ApiOperation(value = "修改入驻商信息", httpMethod = "PUT", notes = "system:supplier:edit")
    @RequiresPermissions("system:supplier:edit")
    @Log(title = "入驻商信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysSupplierSaveReqVO updateReqVO) {
        sysSupplierService.updateSysSupplier(updateReqVO);
        return success(true);
    }

    /**
     * 修改入驻商信息(入驻商PC自己改) 仅支持基础信息
     */
    @ApiOperation(value = "修改入驻商信息(入驻商PC自己改)", httpMethod = "PUT", notes = "system:supplier:shelf-edit")
    @RequiresPermissions("system:supplier:shelf-edit")
    @Log(title = "入驻商信息", businessType = BusinessType.UPDATE)
    @PutMapping("/editShelf")
    public CommonResult<Boolean> editShelf(@RequestBody SysSupplierSaveShelfReqVO updateReqVO) {
        sysSupplierService.updateSysSupplierShelf(updateReqVO);
        return success(true);
    }

    /**
     * 修改入驻商状态
     */
    @ApiOperation(value = "修改入驻商状态", httpMethod = "PUT", notes = "system:supplier:changeStatus")
    @RequiresPermissions("system:supplier:changeStatus")
    @Log(title = "入驻商信息", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public CommonResult<Boolean> changeStatus(@Valid @RequestBody SysSupplierStatusReqVO updateReqVO) {
        sysSupplierService.changeStatus(updateReqVO);
        return success(true);
    }

    /**
     * 删除入驻商信息
     */
    @ApiOperation(value = "删除入驻商信息", httpMethod = HttpMethod.GET, notes = "system:supplier:remove")
    @RequiresPermissions("system:supplier:remove")
    @Log(title = "入驻商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{supplierIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] supplierIds) {
        //sysSupplierService.deleteSysSupplierBySupplierIds(supplierIds);
        return success(true);
    }

    /**
     * 获取入驻商信息详细信息
     */
    @ApiOperation(value = "获得入驻商信息详情", httpMethod = HttpMethod.GET, notes = "system:supplier:query")
    @RequiresPermissions("system:supplier:query")
    @GetMapping(value = "/{supplierId}")
    public CommonResult<SysSupplierRespVO> getInfo(@PathVariable("supplierId") Long supplierId) {
        Long sessionSupplierId = SecurityUtils.getSupplierId();
        if (Objects.nonNull(sessionSupplierId)) {
            supplierId = sessionSupplierId;
        }
        return success(sysSupplierService.getSysSupplier(supplierId));
    }

    /**
     * 分页查询入驻商信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入驻商信息分页列表", httpMethod = HttpMethod.GET, notes = "system:supplier:list")
    @RequiresPermissions("system:supplier:list")
    public CommonResult<PageResult<SysSupplierRespVO>> getPage(@Valid SysSupplierPageReqVO pageReqVO) {
        return success(sysSupplierService.getSysSupplierPage(pageReqVO));
    }


    /**
     * @Description: 根据运营商ID获取入驻商信息
     * @Param:
     * @return: CommonResult<List < SysSupplierRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/22 14:33
     */
    @ApiOperation(value = "根据运营商ID获取入驻商信息", httpMethod = HttpMethod.GET)
    @GetMapping("/getSupplierListByDcId")
    public CommonResult<List<SysSupplierRespVO>> getSupplierListByDcId() {
        return success(sysSupplierService.getSupplierListByDcId());
    }

    /**
     * 获取选中数据 (用于选中回显)
     *
     * @param supplierIds 入驻商ID集合
     * @return 入驻商集合
     */
    @PostMapping("/getSelectedBatchInfo")
    @ApiOperation(value = "批量获取简略信息", httpMethod = "POST")
    public CommonResult<List<SysSupplierRespVO>> getSelectedBatchInfo(@Valid @Size(min = NumberPool.INT_ONE) @RequestBody List<Long> supplierIds) {
        return success(sysSupplierService.getSelectedSysSupplier(supplierIds));
    }


    /**
     * 分页查询入驻商账户
     */
    @GetMapping("/accountList")
    @ApiOperation(value = "获得入驻商账户分页列表", httpMethod = HttpMethod.GET, notes = "system:supplier:accountList")
    @RequiresPermissions("system:supplier:accountList")
    public CommonResult<PageResult<SysSupplierAccountPageReqVO>> getAccountPage(@Valid SysSupplierPageReqVO pageReqVO) {
        return success(sysSupplierService.getSysSupplierAccountPage(pageReqVO));
    }

    /**
     * 获取入驻商openid
     * @param code
     * @return
     */
    @GetMapping("/getOpenId")
    @ApiOperation(value = "获取入驻商openid", httpMethod = HttpMethod.GET)
    public CommonResult<WxCode2SessionResponse> getOpenId(@RequestParam("code") @ApiParam(name = "code", value = "微信code") String code) {
        WxCode2SessionResponse code2SessionResponse = null;
        try {
            Proxy proxy = null;
            //是否使用代理
            if(anntoProxyConfig.isEnable() && StringUtils.isNotEmpty(anntoProxyConfig.getHost())) {
                log.warn(" 使用代理请求...");
                // 设置代理
                proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(anntoProxyConfig.getHost(), anntoProxyConfig.getPort()));

            }
            code2SessionResponse = WxUtils.getOpenId(redisSysConfigService.getSupplierAppid(), redisSysConfigService.getSupplierKey(), code, proxy);
        } catch (IOException e) {
            String errMsg = String.format("获取入驻商openid失败，appid[%s],SupplierKey[%s],code[%s],原因：",redisSysConfigService.getSupplierAppid(),redisSysConfigService.getSupplierKey(), code);
            log.error(errMsg,e);
            throw new RuntimeException(e);
        }
        return CommonResult.success(code2SessionResponse);
    }

    /**
     * 入驻商绑定公众号openid
     * @param code
     * @return
     */
    @GetMapping("/bindPublishCode")
    @ApiOperation(value = "绑定入驻商公众号openid", httpMethod = HttpMethod.GET, notes = "system:supplier:bind-publish-openid")
    @RequiresPermissions("system:supplier:bind-publish-openid")
    public CommonResult<Boolean> bindPublishCode(@RequestParam("code") @ApiParam(name = "code", value = "微信code") String code) {
        sysSupplierService.bindPublishCode(code);
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 入驻商解绑公众号openid
     * @return
     */
    @ApiOperation(value = "解绑入驻商公众号openid", httpMethod = HttpMethod.GET, notes = "system:supplier:unbind-publish-openid")
    @GetMapping("/unbindPublishCode")
    @RequiresPermissions("system:supplier:unbind-publish-openid")
    public CommonResult<Boolean> unbindPublishCode() {
        Long supplierId = SecurityUtils.getSupplierId();
        if (Objects.isNull(supplierId)) {
            // 仅入驻商权限可操作
            throw exception(JUST_SUPPLIER_ROLE);
        }
        sysSupplierService.unbindPublishCode(supplierId);
        return CommonResult.success(Boolean.TRUE);
    }


    /**
     * 获取入驻商app配置
     * @return 配置
     */
    @ApiOperation(value = "获取入驻商APP配置", httpMethod = HttpMethod.GET, notes = "system:supplier:app-config")
    @GetMapping("/getAppConfig")
    @RequiresPermissions("system:supplier:app-config")
    public CommonResult<SysSupplierAppConfigVO> getAppConfig() {
        return success(new SysSupplierAppConfigVO(
                redisSysConfigService.getSupplierPublishAppid(),
                redisSysConfigService.getSupplierPublishAppName()
        ));
    }

    /**
     * 校验该入驻商是否对接第三方系统
     * @param supplierId
     * @return
     */
    @GetMapping("/checkSyncConfig")
    @ApiOperation(value = "校验该入驻商是否对接第三方系统", httpMethod = HttpMethod.GET)
    public CommonResult<Boolean> checkSyncConfig(@RequestParam("supplierId")Long supplierId) {
        return CommonResult.success(sysSupplierService.checkSyncConfig(supplierId));
    }


    /**
     * 修改入驻商信息(入驻商PC自己改) 仅支持基础信息
     */
    @ApiOperation(value = "修改入驻商密码", httpMethod = "PUT", notes = "system:supplier:edit-password")
    @RequiresPermissions("system:supplier:edit-password")
    @Log(title = "修改入驻商密码", businessType = BusinessType.UPDATE)
    @PutMapping("/editSupplierPassword")
    public CommonResult<Boolean> editPassword(@Valid @RequestBody SysSupplierSavePasswordReqVO updateReqVO) {
        sysSupplierService.updateSysSupplierPassword(BeanUtils.toBean(updateReqVO, SysSupplierSaveReqVO.class));
        return success(true);
    }

}
