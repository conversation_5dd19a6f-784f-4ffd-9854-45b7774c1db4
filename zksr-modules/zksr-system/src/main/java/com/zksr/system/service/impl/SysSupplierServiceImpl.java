package com.zksr.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRespVO;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.common.core.domain.vo.openapi.receive.SysSupplierDTO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.common.third.wx.dto.WxCode2SessionResponse;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchSupplierDTO;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.supplierClass.SupplierClassApi;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.DcOtherSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplier.vo.SupplierDropdownReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.chin.partnerPolicy.PolicyPartnerPolicyPipeline;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicyRespVO;
import com.zksr.system.controller.partnerPolicy.vo.SysPartnerPolicySaveReqVO;
import com.zksr.system.controller.supplier.vo.SysSupplierAccountPageReqVO;
import com.zksr.system.controller.supplier.vo.SysSupplierSaveReqVO;
import com.zksr.system.controller.supplier.vo.SysSupplierSaveShelfReqVO;
import com.zksr.system.controller.supplier.vo.SysSupplierStatusReqVO;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaRespVO;
import com.zksr.system.convert.supplier.SupplierConvert;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.domain.SysSupplierArea;
import com.zksr.system.enums.SysRoleKeyEnum;
import com.zksr.system.mapper.SysRoleMapper;
import com.zksr.system.mapper.SysSupplierAreaMapper;
import com.zksr.system.mapper.SysSupplierMapper;
import com.zksr.system.mapper.SysUserMapper;
import com.zksr.system.mq.SystemMqProducer;
import com.zksr.system.service.*;
import com.zksr.system.tenant.SaasHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 入驻商信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Slf4j
@Service
public class SysSupplierServiceImpl implements ISysSupplierService {
    @Autowired
    private SysSupplierMapper sysSupplierMapper;

    @Autowired
    private SysSupplierAreaMapper sysSupplierAreaMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SupplierClassApi supplierClassApi;

    @Autowired
    private SysSupplierAreaMapper supplierAreaMapper;

    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;

    @Autowired
    @Qualifier("catgorySupplierListDtoCache")
    private Cache<Long, List<CatgoryDTO>> catgorySupplierListDtoCache;

    @Autowired
    @Qualifier("areaClassBranchListDtoCache")
    private Cache<Long, List<AreaClassDTO>> areaClassBranchListDtoCache;

    @Autowired
    private SystemMqProducer amapMqProducer;

    @Autowired
    private PlatformMerchantApi platformMerchantApi;

    @Autowired
    private AccountApi accountApi;

    @Autowired
    private ISysCacheService sysCacheService;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Resource
    private BranchApi branchApi;

    @Autowired
    private ISysAreaSupplierZipService sysAreaSupZipService;

    @Autowired
    private PolicyPartnerPolicyPipeline partnerPolicyPipeline;

    @Autowired
    private ISysPartnerPolicyService sysPartnerPolicyService;

    @Resource
    private SaasHelper saasHelper;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    /**
     * 新增入驻商信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @DistributedLock(lockName = RedisLockConstants.LOCK_SUPPLIER, condition = "#createReqVO.contactPhone")
    public Long insertSysSupplier(SysSupplierSaveReqVO createReqVO) {
        //校验入驻商名称唯一
        checkSupplierNameUnique(createReqVO.getSupplierName());
        //获取当前登录账号信息
        LoginUser user = SecurityUtils.getLoginUser();

        // 插入入驻商信息
        SysSupplier sysSupplier = HutoolBeanUtils.toBean(createReqVO, SysSupplier.class);
        sysSupplier.setDcId(user.getDcId());
        sysSupplier.setSupplierId(null);
        sysSupplierMapper.insert(sysSupplier);
        //新增入驻商与城市关联信息
        List<Long> areaIds = createReqVO.getAreaIds();
        if(ObjectUtil.isNotEmpty(areaIds)){
             List<SysSupplierArea> sysSupplierAreaList = areaIds.stream().map(x -> {
                //新增区域城市入驻商关系拉链表
                createReqVO.setSupplierId(sysSupplier.getSupplierId());
                sysAreaSupZipService.insertSysAreaSupplierZip(x,createReqVO,sysSupplier);
                SysSupplierArea sysSupplierArea = new SysSupplierArea();
                sysSupplierArea.setSupplierId(sysSupplier.getSupplierId());
                sysSupplierArea.setAreaId(x);
                sysSupplierArea.setSysCode(user.getSysCode());
                return sysSupplierArea;
            }).collect(Collectors.toList());
            sysSupplierAreaMapper.insertBatch(sysSupplierAreaList);
        }
        // 创建管理员用户
        createAdminUser(createReqVO.getUserName(), createReqVO.getPassword(), sysSupplier, user);

        // 保存平台进件信息
        savePayPlatformMerchant(createReqVO, sysSupplier.getSupplierId());

        //远程调用 插入入驻商绑定管理类别
        PrdtSupplierClassRespDTO prdtSupplierClassRespDto = new PrdtSupplierClassRespDTO(
                createReqVO.getCatgoryIds(),
                sysSupplier.getSupplierId(),
                user.getSysCode(),
                createReqVO.getSupplierClassRateDTOS()
        );
        supplierClassApi.insetBath(prdtSupplierClassRespDto).checkError();
        // 返回
        return sysSupplier.getSupplierId();
    }

    /**
     * 第三方调用新增入驻商信息（湘无界对接）
     *
     * @param sysSupplierDTO 创建信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveSysSupplier(Long supplierId,SysSupplierDTO sysSupplierDTO) {
        //校验入驻商名称唯一
        checkSupplierNameUnique(sysSupplierDTO.getSupplierName());
        SupplierDTO supplierDTO = sysCacheService.getSupplierDTO(supplierId);
        // 插入入驻商信息
        SysSupplier sysSupplier = HutoolBeanUtils.toBean(sysSupplierDTO, SysSupplier.class);
        sysSupplier.setDcId(supplierDTO.getDcId());
        sysSupplier.setSupplierId(null);
        sysSupplierMapper.insert(sysSupplier);
        LoginUser user = new LoginUser();
        user.setSysCode(supplierDTO.getSysCode());
        //统配入驻商的城市区域绑定给直配入驻商
        List<Long> areaIds = supplierAreaMapper.selectList(Wrappers.<SysSupplierArea>lambdaQuery().eq(SysSupplierArea::getSupplierId, supplierId)).stream().map(SysSupplierArea::getAreaId).collect(Collectors.toList());
        List<SysSupplierArea> sysSupplierAreaList = areaIds.stream().map(x -> {
            SysSupplierArea sysSupplierArea = new SysSupplierArea();
            sysSupplierArea.setSupplierId(sysSupplier.getSupplierId());
            sysSupplierArea.setAreaId(x);
            sysSupplierArea.setSysCode(user.getSysCode());
            return sysSupplierArea;
        }).collect(Collectors.toList());
        sysSupplierAreaMapper.insertBatch(sysSupplierAreaList);
        // 创建管理员用户
        createAdminUser(sysSupplierDTO.getUserName(), sysSupplierDTO.getPassword(), sysSupplier, user);

        List<Long> catgoryIds = supplierClassApi.getCatgoryIdListBySupplierId(supplierId).getCheckedData();
        List<PrdtSupplierClassRateDTO> supplierClassRateDTOS = supplierClassApi.getCatgoryRateListBySupplierId(supplierId).getCheckedData();
        //远程调用 插入入驻商绑定管理类别
        PrdtSupplierClassRespDTO prdtSupplierClassRespDto = new PrdtSupplierClassRespDTO(
                catgoryIds,
                sysSupplier.getSupplierId(),
                user.getSysCode(),
                supplierClassRateDTOS
        );
        supplierClassApi.insetBath(prdtSupplierClassRespDto).checkError();
        // 返回
        return sysSupplier.getSupplierId();
    }

    @Override
    public List<String> getSupplierNames(List<Long> supplierIds) {
        return sysSupplierMapper.getSupplierNames(supplierIds);
    }

    private void createAdminUser(String username, String pass, SysSupplier sysSupplier, LoginUser user) {
        // 用户已经绑定了的, 就不去创建用户了
        if (Objects.nonNull(sysSupplier.getUserId())) {
            return;
        }
        // 完善账号信息
        SysUser saveUser = new SysUser();
        saveUser.setNickName(saasAuthSwitch ? sysSupplier.getContactName() : sysSupplier.getSupplierName());
        saveUser.setUserName(username);
        saveUser.setStatus(StringPool.ZERO);
        saveUser.setPassword(saasAuthSwitch ? "" : SecurityUtils.encryptPassword(pass));
        saveUser.setPhonenumber(sysSupplier.getContactPhone());
        saveUser.setRemark(String.format("%s默认入驻商管理员用户", sysSupplier.getSupplierName()));
        saveUser.setCreateBy(SecurityUtils.getUsername());
        saveUser.setCreateTime(DateUtil.date());
        if (!userService.checkUserNameUnique(saveUser)) {
            throw new ServiceException(String.format("新增用户%s失败,登录账号%s已存在", saveUser.getUserName(), saveUser.getUserName()));
        } else if (ToolUtil.isNotEmpty(saveUser.getPhonenumber())
                && !userService.checkPhoneUnique(saveUser)) {
            throw new ServiceException(String.format("新增用户%s失败,手机号码%s已存在", saveUser.getUserName(),saveUser.getPhonenumber()));
        } else if (ToolUtil.isNotEmpty(saveUser.getEmail())
                && !userService.checkEmailUnique(saveUser)) {
            throw new ServiceException(String.format("新增用户%s失败,邮箱账号%s已存在", saveUser.getUserName(),saveUser.getEmail()));
        }
        // 入驻商角色在创建平台商的时候已经创建好了
        // 见 com.zksr.system.service.ISysRoleService.createPartnerAdminRole
        // 现在只需要查询到角色具体ID, 赋权即可
        SysRole sysRole = sysRoleMapper.selectBySysCodeAndScope(user.getSysCode(), SysRoleKeyEnum.SUPPLIER_ADMIN_ROLE.getRoleKey() + "-" + user.getSysCode());
        if (Objects.isNull(sysRole)) {
            // 权限不存在
            throw exception(SYS_SUPPLIER_SUPPLIER_ROLE_NULL);
        }
        Long roleId = sysRole.getRoleId();
        Long[] roleIds = {roleId};// 自动赋管理员权限
        saveUser.setRoleIds(roleIds);
        saveUser.setSysCode(user.getSysCode());
        saveUser.setSupplierId(sysSupplier.getSupplierId());
        userService.saveUser(saveUser);
        userService.insertUserRole(saveUser);
        //保存用户id字段
        sysSupplier.setUserId(saveUser.getUserId());
        sysSupplierMapper.updateById(sysSupplier);
    }

    /**
     * 修改入驻商信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSysSupplier(SysSupplierSaveReqVO updateReqVO) {
        // 验证修改入驻商名称重复
        checkSupplierNameUnique(updateReqVO.getSupplierId(), updateReqVO.getSupplierName());
        //获取原数据
        SysSupplier sysSupplier = sysSupplierMapper.selectById(updateReqVO.getSupplierId());
        // 创建管理员用户
        createAdminUser(sysSupplier.getContactPhone(), sysSupplier.getContactPhone(), sysSupplier, SecurityUtils.getLoginUser());
        //修改入驻商基本信息
        sysSupplierMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysSupplier.class));
        //修改入驻商绑定区域
        sysSupplierAreaMapper.deleteBySupplierId(updateReqVO.getSupplierId());
        //新增入驻商与城市关联信息
        List<Long> areaIds = updateReqVO.getAreaIds();
        if (ObjectUtil.isNotEmpty(areaIds)) {
            List<SysSupplierArea> sysSupplierAreaList = areaIds.stream().map(x -> {
                sysAreaSupZipService.insertSysAreaSupplierZip(x, updateReqVO,sysSupplier);
                SysSupplierArea sysSupplierArea = new SysSupplierArea();
                sysSupplierArea.setSupplierId(updateReqVO.getSupplierId());
                sysSupplierArea.setAreaId(x);
                sysSupplierArea.setSysCode(SecurityUtils.getLoginUser().getSysCode());
                return sysSupplierArea;
            }).collect(Collectors.toList());
            sysSupplierAreaMapper.insertBatch(sysSupplierAreaList);
        }
        // 保存平台进件信息
        savePayPlatformMerchant(updateReqVO, updateReqVO.getSupplierId());
        //修改入驻商绑定管理类别
        Long sysCode = SecurityContextHolder.getSysCode();
        PrdtSupplierClassRespDTO respDto = new PrdtSupplierClassRespDTO(
                updateReqVO.getCatgoryIds(),
                updateReqVO.getSupplierId(),
                sysCode,
                updateReqVO.getSupplierClassRateDTOS()
        );
        supplierClassApi.updateBySupplierId(respDto).checkError();
        //修改账号信息
        SysUser sysUser = userMapper.selectUserById(sysSupplier.getUserId());
        if (ToolUtil.isEmpty(sysUser)) {
            throw exception(USERNAME_NOT_EXISTS);
        }
        if (!saasAuthSwitch && org.apache.commons.lang3.StringUtils.isNotBlank(updateReqVO.getPassword())){
            sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPassword().trim()));
        }
        sysUser.setNickName(saasAuthSwitch ? updateReqVO.getContactName() : updateReqVO.getSupplierName());
        sysUser.setStatus(Objects.equals(NumberPool.LONG_ONE, updateReqVO.getStatus()) ? StringPool.ZERO : StringPool.ONE);
        //sysUser.setPhonenumber(updateReqVO.getContactPhone());
        sysUser.setUpdateBy(SecurityUtils.getUsername());
        userService.updateUserV2(sysUser);
        // mq修改电子围栏
        if (ObjectUtil.equal(NumberPool.LONG_ONE, updateReqVO.getDzwlFlag())
                // && ObjectUtil.notEqual(updateReqVO.getDzwlInfo(),sysSupplier.getDzwlInfo())
        ) {
            SupplierDTO supplierDTO = HutoolBeanUtils.toBean(updateReqVO, SupplierDTO.class);
            supplierDTO.setSysCode(sysCode);

            //
            BranchSupplierDTO branchSupplierDTO = new BranchSupplierDTO( updateReqVO.getBranchIdList(), supplierDTO.getSupplierId(), supplierDTO.getSysCode());
            branchApi.bindBranch(branchSupplierDTO);
        }
        //删除缓存
        supplierDTOCache.remove(updateReqVO.getSupplierId());
        catgorySupplierListDtoCache.remove(updateReqVO.getSupplierId());
    }


    @Override
    public void updateSysSupplierShelf(SysSupplierSaveShelfReqVO updateReqVO) {
        updateReqVO.setSupplierId(SecurityUtils.getSupplierId());
        //修改入驻商基本信息
        sysSupplierMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysSupplier.class));
        supplierDTOCache.remove(updateReqVO.getSupplierId());

        if (Objects.nonNull(updateReqVO.getSupplierOtherSettingPolicyDTO())) {
            SysPartnerPolicySaveReqVO partnerPolicySaveReqVO = new SysPartnerPolicySaveReqVO();
            partnerPolicySaveReqVO.setSupplierOtherSettingPolicyDTO(updateReqVO.getSupplierOtherSettingPolicyDTO());
            LoginUser user = SecurityUtils.getLoginUser();
            partnerPolicyPipeline.partnerSaveConfig(partnerPolicySaveReqVO, user.getSysCode(), user.getDcId(), updateReqVO.getSupplierId());
        }
    }

    /**
     * 删除入驻商信息
     *
     * @param supplierId 入驻商id
     */
    @Override
    public void deleteSysSupplier(Long supplierId) {
        // 删除
        sysSupplierMapper.deleteById(supplierId);
        supplierDTOCache.remove(supplierId);
    }

    /**
     * 批量删除入驻商信息
     *
     * @param supplierIds 需要删除的入驻商信息主键
     * @return 结果
     */
    @Override
    public void deleteSysSupplierBySupplierIds(Long[] supplierIds) {
        for (Long supplierId : supplierIds) {
            this.deleteSysSupplier(supplierId);
        }
    }

    /**
     * 获得入驻商信息
     *
     * @param supplierId 入驻商id
     * @return 入驻商信息
     */
    @Override
    public SysSupplierRespVO getSysSupplier(Long supplierId) {
        if (ObjectUtil.isNull(supplierId)) {
            return null;
        }
        //获取入驻商基本信息
        SysSupplierRespVO sysSupplierRespVO = sysSupplierMapper.selectSysSupplierById(supplierId);
        if (ObjectUtil.isNull(sysSupplierRespVO)) {
            throw exception(SYS_SUPPLIER_NOT_EXISTS);
        }
        //获取入驻商绑定管理类别信息
        List<Long> catgoryIdList = supplierClassApi.getCatgoryIdListBySupplierId(supplierId).getCheckedData();

        // 获取入驻商一级分类, 销售占比设置
        List<PrdtSupplierClassRateDTO> supplierClassRateList = supplierClassApi.getCatgoryRateListBySupplierId(supplierId).getCheckedData();

        //获取入驻商绑定区域信息
        List<Long> areaIds = sysSupplierAreaMapper.selectListBySupplierId(supplierId);
        //获取账号信息
        sysSupplierRespVO.setUserName(userMapper.getBySysUserId(sysSupplierRespVO.getUserId()));
        SupplierConvert.INSTANCE.buildSetRespVO(
                sysSupplierRespVO,
                catgoryIdList,
                areaIds,
                supplierClassRateList
        );
        // 使用入驻商详情下发入驻商其他配置, 查询其他的接口, 风险有点高
        SysPartnerPolicyRespVO partnerPolicy = sysPartnerPolicyService.getSysPartnerPolicy(supplierId);
        sysSupplierRespVO.setSupplierOtherSettingPolicyDTO(partnerPolicy.getSupplierOtherSettingPolicyDTO());
        // 获取进件信息
        List<AccPlatformMerchantRespVO> merchantRespVOS = platformMerchantApi.getMerchantList(
                AccPlatformMerchantPageReqVO.builder()
                        .merchantId(supplierId)
                        .merchantType(MerchantTypeEnum.SUPPLIER.getType())
                        .build()
        ).getCheckedData();
        sysSupplierRespVO.setPlatformSimpleBindList(BeanUtils.toBean(merchantRespVOS, PlatformSimpleBindVO.class));


        // 平台商支付配置
        PayConfigDTO payConfigDTO = sysCacheService.getPayConfigDTO(sysSupplierRespVO.getSysCode());
        Long dcId = sysSupplierRespVO.getDcId();
        if (Objects.nonNull(dcId)) {
            SysPartnerPolicyRespVO dcConfig = sysPartnerPolicyService.getDcConfig(dcId);
            if (Objects.nonNull(dcConfig) && Objects.nonNull(dcConfig.getDcOtherSettingPolicyDTO())) {
                DcOtherSettingPolicyDTO settingPolicyDTO = dcConfig.getDcOtherSettingPolicyDTO();
                if (StringUtils.isNotEmpty(settingPolicyDTO.getWalletPartnerRate())) {
                    // 门店储值平台商分佣比例
                    sysSupplierRespVO.setWalletPartnerRate(new BigDecimal(settingPolicyDTO.getWalletPartnerRate()));
                }
            }
        }
        if (Objects.nonNull(payConfigDTO) && StringUtils.isNotEmpty(payConfigDTO.getWalletSoftwareRate())) {
            // 门店储值软件商分佣比例
            sysSupplierRespVO.setWalletSoftwareRate(new BigDecimal(payConfigDTO.getWalletSoftwareRate()));
        }
        return sysSupplierRespVO;
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SysSupplierRespVO> getSysSupplierPage(SysSupplierPageReqVO pageReqVO) {
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        //根据dcId获取绑定区域下所有的入驻商ID
        List<Long> supplierIds = null;
        if (ObjectUtil.isNotNull(dcId)) {
            supplierIds = supplierAreaMapper.selectSupplierIds(dcId);
        }
        pageReqVO.setDcId(dcId);
        Page<SysSupplierPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        //page 转换
        Page<SysSupplierRespVO> sysSupplierPage = sysSupplierMapper.selectPage(pageReqVO, page, supplierIds);
        return new PageResult<>(sysSupplierPage.getRecords(), sysSupplierPage.getTotal());
    }

    @Override
    public Map<Long, SupplierDTO> getUserInfoByMap(Long dcId, Long supplierNo) {
        List<SupplierDTO> dtoList = getUserInfoByList(dcId, supplierNo);
        if (ToolUtil.isNotEmpty(dtoList)) {
            Map<Long, SupplierDTO> dtoMap = dtoList.stream().collect(Collectors.toMap(SupplierDTO::getSupplierId, it -> it));
            return dtoMap;
        } else {
            return null;
        }
    }

    /**
     * @Description: 获取所有入驻商
     * @Author: liuxingyu
     * @Date: 2024/3/22 14:36
     */
    @Override
    public List<SysSupplier> getSupplierList(SysSupplier sysSupplier) {
        List<Long> supplierList = DcUtils.getSupplierList(SecurityUtils.getDcId());
        LambdaQueryWrapperX<SysSupplier> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.in( Objects.nonNull(supplierList) , SysSupplier::getSupplierId, supplierList );
        wrapperX.applyScope(sysSupplier.getParams());
        return sysSupplierMapper.selectList(wrapperX);
    }

    @Override
    public SysSupplier getBySupplierId(Long supplierId) {
        return sysSupplierMapper.selectById(supplierId);
    }

    @Override
    public List<SysSupplierRespVO> getSupplierListByDcId() {
        return sysSupplierMapper.selectSupplierListByUser(SecurityUtils.getLoginUser().getDcId(), SecurityUtils.getSupplierId());
    }

    @Override
    public List<SysSupplierRespVO> getSelectedSysSupplier(List<Long> supplierIds) {
        return BeanUtil.copyToList(sysSupplierMapper.selectBatchIds(supplierIds), SysSupplierRespVO.class);
    }

    /**
     * @Description: 根据运营商ID获取区域下所有的入驻商
     * @Author: liuxingyu
     * @Date: 2024/4/9 17:32
     */
    @Override
    public List<Long> getByOrder(Long dcId) {
        return sysSupplierMapper.getByDcId(dcId);
    }

    /**
    * @Description: 获取入驻商下拉选
    * @Author: liuxingyu
    * @Date: 2024/4/12 9:35
    */
    @Override
    public PageResult<SysSupplierRespVO> getSupplierDropdown(SupplierDropdownReqVO reqVO) {
        List<Long> areaSupplierIds = new ArrayList<>();
        if (ObjectUtil.isNotNull(SecurityUtils.getLoginUser().getDcId())){
            areaSupplierIds = getByOrder(SecurityUtils.getLoginUser().getDcId());
            if (ObjectUtil.isEmpty(areaSupplierIds)){
                return PageResult.empty();
            }
        }
        if (ObjectUtil.isNotNull(SecurityUtils.getSupplierId())){
            SysSupplier sysSupplier = sysSupplierMapper.selectById(SecurityUtils.getSupplierId());
            if (ObjectUtil.isNull(sysSupplier)){
                return PageResult.empty();
            }
            areaSupplierIds.add(sysSupplier.getSupplierId());
        }
        if (Objects.nonNull(reqVO.getPageNo())) {
            com.github.pagehelper.Page<Object> page = PageUtils.startPage(reqVO);
            List<SysSupplierRespVO> respVOS = HutoolBeanUtils.toBean(sysSupplierMapper.getByOrderParam(areaSupplierIds, reqVO.getSupplier()), SysSupplierRespVO.class);
            return new PageResult<SysSupplierRespVO>( respVOS, page.getTotal());
        } else {
            return new PageResult<SysSupplierRespVO>( HutoolBeanUtils.toBean(sysSupplierMapper.getByOrderParam(areaSupplierIds, reqVO.getSupplier()), SysSupplierRespVO.class), NumberPool.LONG_ONE);
        }
    }

    /**
    * @Description: 根据区域获取绑定的入驻商ID
    * @Author: liuxingyu
    * @Date: 2024/4/12 15:01
    */
    @Override
    public List<SysSupplierAreaRespVO> sysSupplierService(Long areaId) {
        return HutoolBeanUtils.toBean(sysSupplierAreaMapper.getByAreaId(areaId).stream().distinct().collect(Collectors.toList()), SysSupplierAreaRespVO.class);
    }

    @Override
    public PageResult<SysSupplierAccountPageReqVO> getSysSupplierAccountPage(SysSupplierPageReqVO pageReqVO) {
        PageResult<SysSupplierRespVO> supplierPage = getSysSupplierPage(pageReqVO);
        PageResult<SysSupplierAccountPageReqVO> accountPage = SupplierConvert.INSTANCE.convert(supplierPage);
        // 或者平台商默认支付平台
        accountPage.getList().forEach(item -> {
           // 获取账户余额
            AccAccountDTO account = accountApi.getSupplierAccount(item.getSupplierId()).getCheckedData();
            // 写入账户金额
            SupplierConvert.INSTANCE.convert(item, account);
        });
        return accountPage;
    }

    @Override
    public CustomApiMaster getCustomApiMaster(CustomApiMaster customApiMaster) {
        return sysSupplierMapper.getCustomApiMaster(customApiMaster);
    }

    @Override
    public List<CustomApiDetail> getCustomApiDetail(String apiNo) {
        return sysSupplierMapper.getCustomApiDetail(apiNo);
    }

    @Override
    public void bindPublishCode(String code) {
        String supplierPublishAppid = redisSysConfigService.getSupplierPublishAppid();
        String supplierPublishAppSecret = redisSysConfigService.getSupplierPublishAppSecret();
        if (StringUtils.isEmpty(supplierPublishAppid)) {
            throw exception(SYS_MESSAGE_PUBLISH_ID_ERR);
        }
        if (StringUtils.isEmpty(supplierPublishAppSecret)) {
            throw exception(SYS_MESSAGE_PUBLISH_SECRET_ERR);
        }
        Long supplierId = SecurityUtils.getSupplierId();
        if (Objects.isNull(supplierId)) {
            throw new ServiceException("绑定公众号失败");
        }

        try {
            WxCode2SessionResponse sessionResponse = WxUtils.getPublishOpenId(supplierPublishAppid, supplierPublishAppSecret, code);
            if (StringUtils.isEmpty(sessionResponse.getOpenid())) {
                throw new ServiceException("绑定公众号失败");
            }

            SysSupplier supplier = new SysSupplier();
            supplier.setSupplierId(supplierId);
            supplier.setPublishOpenid(sessionResponse.getOpenid());
            sysSupplierMapper.updateById(supplier);
            supplierDTOCache.remove(supplierId);
        } catch (IOException e) {
            log.error(" bindPublishCode异常,", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void unbindPublishCode(Long supplierId) {
        SysSupplier supplier = new SysSupplier();
        supplier.setSupplierId(supplierId);
        supplier.setPublishOpenid(StringPool.EMPTY);
        sysSupplierMapper.updateById(supplier);
        supplierDTOCache.remove(supplierId);
    }

    @Override
    @Transactional
    public void changeStatus(SysSupplierStatusReqVO updateReqVO) {
        SysSupplier supplier = new SysSupplier();
        supplier.setSupplierId(updateReqVO.getSupplierId());
        supplier.setStatus(updateReqVO.getStatus());
        sysSupplierMapper.updateById(supplier);
        supplierDTOCache.remove(updateReqVO.getSupplierId());

        SysSupplier sysSupplier = sysSupplierMapper.selectById(updateReqVO.getSupplierId());
        SysUser sysUser = userService.selectUserById(sysSupplier.getUserId());
        if (Objects.nonNull(sysUser)) {
            sysUser.setStatus(Objects.equals(NumberPool.LONG_ONE, updateReqVO.getStatus()) ? StringPool.ZERO : StringPool.ONE);
            userService.updateUserStatus(sysUser);
        }
    }

    @Override
    public List<SupplierDTO> getUserInfoByList(Long dcId, Long supplierNo) {
        List<SysSupplierRespVO> sysSupplierList = sysSupplierMapper.selectSupplierListByUser(dcId, supplierNo);
        return HutoolBeanUtils.toBean(sysSupplierList, SupplierDTO.class);
    }

    @Override
    public Map<Long, SupplierDTO> getlistByUser(Long dcId) {
        List<SysSupplier> sysSupplierList = sysSupplierMapper.selectSupplierList(dcId);
        if (ToolUtil.isNotEmpty(sysSupplierList)) {
            List<SupplierDTO> dtoList = HutoolBeanUtils.toBean(sysSupplierList, SupplierDTO.class);
            Map<Long, SupplierDTO> dtoMap = dtoList.stream().collect(Collectors.toMap(SupplierDTO::getSupplierId, it -> it));
            return dtoMap;
        } else {
            return null;
        }
    }



    @Override
    public Boolean checkSyncConfig(Long supplierId) {
        //获取开放能力信息
        OpensourceDto opensourceDto = sysCacheService.getOpensourceByMerchantId(supplierId);

        //如果该入驻商存在开发能力信息 则对接了第三方
        return ToolUtil.isNotEmpty(opensourceDto) && ToolUtil.isNotEmpty(opensourceDto.getOpensourceId());
    }


    private void validateSysSupplierExists(Long supplierId) {
        if (sysSupplierMapper.selectById(supplierId) == null) {
            throw exception(SYS_SUPPLIER_NOT_EXISTS);
        }
    }

    /**
     * @Description: 校验入驻商名称唯一
     * @Param: String name 入驻商名称
     * @Author: liuxingyu
     * @Date: 2024/2/28 16:18
     */
    private void checkSupplierNameUnique(String supplierName) {
        Long count = sysSupplierMapper.selectSupplierNameCount(supplierName);
        if (0 != count) {
            throw exception(SYS_SUPPLIER_SUPPLIER_NAME_NOT_UNIQUE);
        }
    }

    private void checkSupplierNameUnique(Long supplierId, String supplierName) {
        Long count = sysSupplierMapper.selectSupplierNameCountNotSupplierId(supplierId, supplierName);
        if (0 != count) {
            throw exception(SYS_SUPPLIER_SUPPLIER_NAME_NOT_UNIQUE);
        }
    }


    /**
     * 保存平台商户信息
     *
     * @param saveReqVO  保存数据
     * @param supplierId 入驻商ID
     */
    private void savePayPlatformMerchant(SysSupplierSaveReqVO saveReqVO, Long supplierId) {
        if (ObjectUtil.isNotEmpty(saveReqVO.getPlatformSimpleBindList())) {
            for (PlatformSimpleBindVO simpleBindVO : saveReqVO.getPlatformSimpleBindList()) {
                PlatformMerchantDTO merchant = BeanUtils.toBean(simpleBindVO, PlatformMerchantDTO.class);
                merchant.setMerchantId(supplierId)
                        .setMerchantType(MerchantTypeEnum.SUPPLIER.getType())
                        .setSysCode(SecurityUtils.getLoginUser().getSysCode());
                platformMerchantApi.savePlatformMerchant(merchant).checkError();
            }
        }
    }

    public List<SysSupplier> getSupplierListByIds(List<Long> supplierIds) {
        return sysSupplierMapper.getSupplierListByIds(supplierIds);
    }

    @Override
    public void updateSysSupplierPassword(SysSupplierSaveReqVO updateReqVO) {
        SysSupplier sysSupplier = sysSupplierMapper.selectById(updateReqVO.getSupplierId());
        if (Objects.isNull(sysSupplier)){
            throw new ServiceException("入驻商信息不存在");
        }
        SysUser sysUser = userMapper.selectUserById(sysSupplier.getUserId());
        if (ToolUtil.isEmpty(sysUser)) {
            throw exception(USERNAME_NOT_EXISTS);
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(updateReqVO.getPassword())){
            sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPassword().trim()));
        }
        sysUser.setNickName(saasAuthSwitch ? sysSupplier.getContactName() : sysSupplier.getSupplierName());
        sysUser.setUpdateBy(SecurityUtils.getUsername());
        userService.updateUserV2(sysUser);
    }

    @Override
    public PageResult<SupplierDTO> getSupplierPage(SysSupplierPageReqVO pageReqVO) {
        PageHelper.startPage(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        List<SysSupplier> sysSuppliers = sysSupplierMapper.selectList(new LambdaQueryWrapperX<SysSupplier>()
                .eqIfPresent(SysSupplier::getSysCode, pageReqVO.getSysCode())
                .eqIfPresent(SysSupplier::getSupplierId, pageReqVO.getSupplierId())
                .eqIfPresent(SysSupplier::getStatus, pageReqVO.getStatus())
                .eqIfPresent(SysSupplier::getIsNegativeStock, pageReqVO.getIsNegativeStock()));
        PageInfo<SysSupplier> pageInfo = new PageInfo<>(sysSuppliers);
        List<SupplierDTO> supplierDTOS = HutoolBeanUtils.toBean(pageInfo.getList(), SupplierDTO.class);
        return new PageResult<>(supplierDTOS, pageInfo.getTotal());
    }
}
