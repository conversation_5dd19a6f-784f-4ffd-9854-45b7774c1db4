package com.zksr.system.api.commonMessage;

import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.commonMessage.vo.*;
import com.zksr.system.mq.SystemMqProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订阅消息通知
 * @date 2024/6/13 15:19
 */
@Slf4j
@InnerAuth
@ApiIgnore
@RestController
public class SubscribeApiImpl implements SubscribeApi {

    @Autowired
    private SystemMqProducer systemMqProducer;

    @Override
    public CommonResult<Boolean> sendNewOrder(Long orderId, Long sysCode) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(orderId)
                .sysCode(sysCode)
                .scene(CommonMessageSceneEnum.NEW_ORDER_PUBLISH.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> sendWxMerchantDeliveryOrder(Long orderId, Long supplierId, Long sysCode) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(
                        SubscribeEventLocalDeliveryVO.builder()
                                .orderId(orderId)
                                .supplierId(supplierId)
                                .build()
                )
                .sysCode(sysCode)
                .scene(CommonMessageSceneEnum.LOCAL_DELIVERY_ORDER.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> orderCancel(SubscribeOrderCancelVO cancelVO) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(cancelVO)
                .sysCode(cancelVO.getSysCode())
                .scene(CommonMessageSceneEnum.ORDER_CANCEL.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> globalOrderDelivery(Long supplierOrderDtlId, Long sysCode) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(supplierOrderDtlId)
                .sysCode(sysCode)
                .scene(CommonMessageSceneEnum.GLOBAL_ORDER_DELIVER.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> orderReceive(SubscribeOrderReceiveVO receiveVO) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(receiveVO)
                .sysCode(receiveVO.getSysCode())
                .scene(CommonMessageSceneEnum.ORDER_RECEIVE.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> afterCreate(SubscribeOrderAfterCreateVO afterVO) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(afterVO)
                .sysCode(afterVO.getSysCode())
                .scene(CommonMessageSceneEnum.AFTER_CREATED.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> afterAudit(Long supplierAfterId, Long sysCode) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(supplierAfterId)
                .sysCode(sysCode)
                .scene(CommonMessageSceneEnum.AFTER_AUDIT.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> afterFinish(SubscribeOrderAfterFinishVO afterVO) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(afterVO)
                .sysCode(afterVO.getSysCode())
                .scene(CommonMessageSceneEnum.AFTER_REFUND_FINISH.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> branchCreate(Long branchId, Long sysCode) {
        SubscribeEventBodyVO<Object> eventBodyVO = SubscribeEventBodyVO.builder()
                .eventData(branchId)
                .sysCode(sysCode)
                .scene(CommonMessageSceneEnum.BRANCH_REGISTER.getScene())
                .build();
        systemMqProducer.sendSubscribe(eventBodyVO);
        return success(Boolean.TRUE);
    }
}
