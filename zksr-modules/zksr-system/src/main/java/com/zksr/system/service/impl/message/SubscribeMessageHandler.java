package com.zksr.system.service.impl.message;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.system.api.commonMessage.dto.CommonMessageContext;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.commonMessage.vo.SubscribeEventBodyVO;
import com.zksr.system.service.ISubscribeMessageHandler;
import com.zksr.system.service.ISysCacheService;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/6/14 15:52
 */
@Slf4j
@SuppressWarnings("all")
public abstract class SubscribeMessageHandler<T> implements ISubscribeMessageHandler<T> {

    public static ThreadLocal<CommonMessageContext<?>> threadContext = new ThreadLocal<>();

    @Autowired
    protected ISysCacheService sysCacheService;

    @Resource
    protected MemberApi memberApi;

    @Resource
    protected OrderApi orderApi;

    @Resource
    protected AfterApi afterApi;

    @Override
    public final List<CommonMessageDTO> processConvert(SubscribeEventBodyVO<T> subscribeEventBodyVO, MessageTemplateDTO messageTemplateDTO) {
        if (subscribeEventBodyVO.getEventData() instanceof JSONObject) {
            // 转换JSONObject
            subscribeEventBodyVO.setEventData(((JSONObject) subscribeEventBodyVO.getEventData()).toJavaObject(getClassType()));
        }
        // 初始化上下文对象
        CommonMessageContext<T> context = new CommonMessageContext<T>(subscribeEventBodyVO, messageTemplateDTO);
        threadContext.set(context);
        try {
            // 初始化上下文
            this.initFlowContext();
            // 开始收集消息
            List<CommonMessageDTO> messageList = new ArrayList<>();
            List<MerchantTypeEnum> merchantList = messageTemplateDTO.merchantList();
            for (MerchantTypeEnum merchantType : merchantList) {
                switch (merchantType) {
                    case BRANCH:
                    case MEMBER:
                        // 获取用户消息
                        messageList.addAll( this.memberHandler(messageTemplateDTO).stream().map(item -> item.setMerchantType(MerchantTypeEnum.MEMBER)).collect(Collectors.toList()) );
                        break;
                    case SUPPLIER:
                        // 获取入驻商消息
                        messageList.addAll( this.supplierHandler(messageTemplateDTO).stream().map(item -> item.setMerchantType(MerchantTypeEnum.SUPPLIER)).collect(Collectors.toList()) );
                        break;
                    case COLONEL:
                        // 获取业务员消息
                        messageList.addAll( this.colonelHandler(messageTemplateDTO).stream().map(item -> item.setMerchantType(MerchantTypeEnum.COLONEL)).collect(Collectors.toList()) );
                        break;
                    default:
                        log.warn("消息中心未匹配到可以处理的消息, messageTemplateId={}", messageTemplateDTO.getMessageTemplateId());
                        break;
                }
            }
            return messageList;
        } finally {
            threadContext.remove();
        }
    }

    /**
     * 初始化流程容器, 上下文数据
     * 这一步需要, 把整个消息进行封装, 组装消息容器, 以便于数据匹配
     */
    protected abstract void initFlowContext();

    /**
     * 组装用户消息
     * 消息已经通过 {@link SubscribeMessageHandler#initFlowContext()} 进件过基础封装,
     * 这里只需要返回需推送者的信息, 已经对多位推送者进行简单的数据拆分
     * @param messageTemplateDTO    消息模版
     * @return  消息体
     */
    public List<CommonMessageDTO> memberHandler(MessageTemplateDTO messageTemplateDTO) {
        return ListUtil.empty();
    };

    /**
     * 组装业务员消息,
     * 消息已经通过 {@link SubscribeMessageHandler#initFlowContext()} 进件过基础封装,
     * 这里只需要返回需推送者的信息, 已经对多位推送者进行简单的数据拆分
     * @param messageTemplateDTO    消息模版
     * @return  消息体
     */
    public List<CommonMessageDTO> colonelHandler(MessageTemplateDTO messageTemplateDTO) {
        return ListUtil.empty();
    };

    /**
     * 组装入驻商消息
     * 消息已经通过 {@link SubscribeMessageHandler#initFlowContext()} 进件过基础封装,
     * 这里只需要返回需推送者的信息, 已经对多位推送者进行简单的数据拆分
     * @param messageTemplateDTO    消息模版
     * @return  消息体
     */
    public List<CommonMessageDTO> supplierHandler(MessageTemplateDTO messageTemplateDTO) {
        return ListUtil.empty();
    };


    /**
     * 获取事件内容, 从埋点处, 消息mq发送过来的数据
     * @return 事件内容
     */
    protected final T getEventData() {
        return (T) messageContext().getEventData();
    }

    /**
     * 获取消息上下文
     * @return 消息上下文
     */
    public final CommonMessageContext<?> messageContext() {
        return threadContext.get();
    }

    public final Class<T> getClassType() {
        // 获取当前类的 Class 对象
        Class<?> clazz = this.getClass();
        // 获取泛型超类
        Type genericSuperclass = clazz.getGenericSuperclass();
        // 将泛型超类转换为 ParameterizedType
        if (genericSuperclass instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
            // 从 ParameterizedType 中获取实际类型参数
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            if (actualTypeArguments.length > 0) {
                return (Class<T>) actualTypeArguments[0];
            }
        }
        return null;
    }
}
