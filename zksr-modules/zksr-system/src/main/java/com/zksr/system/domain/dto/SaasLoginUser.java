package com.zksr.system.domain.dto;

import com.zksr.system.domain.TenantCore;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/03/26 17:30
 */
@Data
public class SaasLoginUser {
    /**
     * 用户额外信息
     */
    private String extendInfo;
    /**
     * masToken
     */
    private String masToken;
    /**
     * 用户帐号：mip账号
     */
    private String account;
    /**
     * 账户名
     */
    private String accountName;
    /**
     * 账户密码
     */
    private String secrectKey;
    /**
     * 账号平台类型	1.集团用户 2.微信小程序用户 3. app用户 4.租户 5.第三方授权appkey用户
     */
    private int accountType;
    /**
     * 头像
     */
    private String picUrl;
    /**
     * 国家
     */
    private String countryCode;
    /**
     * 省
     */
    private String provinceCode;
    /**
     * 市
     */
    private String cityCode;
    /**
     * 区县
     */
    private String districtCode;
    /**
     * 镇
     */
    private String townCode;
    private String userUid;
    // 员工编号
    private String positionCode;
    // 职位
    private String positionName;
    // 员工名称
    private String name;
    // 员工详情
    private String desc;
    // 部门全路径
    private String fullpath;
    // 父类id
    private String parentId;
    // 层级
    private String hierarchyId;
    // 部门名称
    private String departmentName;
    // 语言类型
    private String langType;
    // 邮箱系统标识
    private String mailSystem;
    // 是否已收藏
    private String isCollection;
    //头像url
    private String portraitUrl;
    //工号
    private String employeeNumber;
    //精简后的层级ID
    private String simpleHierarchyId;
    //4A邮箱系统标识
    private String mailSystemFrom4A;
    //时区
    private String timeZone;
    //职级
    private String rank;
    //英文名称
    private String englishName;
    //是否灰度发布管控
    private boolean enableGatedLaunchControl;
    //部门英文路径
    private String departmentEnglishName;
    // 公司邮箱
    private String businessMail;
    // 个人邮箱
    private String personalMail;
    // 邮箱别名
    private String anotherNameMail;
    //入司时间
    private String hiredate;
    //数据来源
    private String source;
    private Long expiresIn;
    /**
     * 登录后的accessToken
     */
    private String accessToken;
    private String refreshToken;
    private String verifyCode;
    /**
     * 当前租户
     */
    private TenantCore currentTenant;

    private String weakMsg;

    private String mobile;

    private String userCode;

    private String tenantCode;

    private String userName;
}
