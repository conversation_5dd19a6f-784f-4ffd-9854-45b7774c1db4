package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.chin.partnerPolicy.PolicyPartnerPolicyPipeline;
import com.zksr.system.controller.partnerPolicy.vo.*;
import com.zksr.system.domain.SysPartnerPolicy;
import com.zksr.system.mapper.SysDcAreaMapper;
import com.zksr.system.mapper.SysPartnerPolicyMapper;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysDcService;
import com.zksr.system.service.ISysPartnerPolicyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_PARTNER_POLICY_NONE_EXITS;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_PARTNER_POLICY_NOT_EXISTS;

/**
 * 平台商政策(由平台商设置)Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@Service
public class SysPartnerPolicyServiceImpl implements ISysPartnerPolicyService {
    @Autowired
    private SysPartnerPolicyMapper sysPartnerPolicyMapper;

    @Autowired
    private PolicyPartnerPolicyPipeline partnerPolicyPipeline;

    @Autowired
    private Cache<Long, AppletAgreementPolicyDTO> appletAgreementPolicyDtoCache;

    @Autowired
    private Cache<Long, BasicSettingPolicyDTO> basicSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, OrderSettingPolicyDTO> orderSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, WithdrawalSettingPolicyDTO> withdrawalSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, XpYunSettingPolicyDTO> xpYunSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, AfterSaleSettingPolicyDTO> afterSaleSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, FeieYunSettingPolicyDTO> feieYunSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, ColonelSettingPolicyDTO> colonelSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, OpenSettingPolicyDTO> openSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, PartnerMiniSettingPolicyDTO> partnerMiniSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, SupplierOtherSettingPolicyDTO> supplierOtherSettingPolicyDTOCache;

    @Autowired
    private ISysCacheService sysCacheService;

    @Autowired
    private Cache<Long, SearchConfigDTO> searchConfigDtoCache;

    @Autowired
    private SysDcAreaMapper sysDcAreaMapper;

    @Autowired
    private Cache<Long,BranchLifecycleSettingPolicyDTO> branchLifecycleSettingPolicyDTOCache;

    /**
     * 新增平台商政策(由平台商设置)
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override

    public Boolean saveSysPartnerPolicy(SysPartnerPolicySaveReqVO createReqVO) {
        // 插入
        LoginUser user = SecurityUtils.getLoginUser();
        partnerPolicyPipeline.partnerSaveConfig(createReqVO, user.getSysCode(), user.getDcId(), SecurityUtils.getSupplierId());
        partnerPolicyPipeline.clean(user.getSysCode(), user.getDcId(), SecurityUtils.getSupplierId());
        // 返回
        return true;
    }

    /**
     * 修改平台商政策(由平台商设置)
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysPartnerPolicy(SysPartnerPolicySaveReqVO updateReqVO) {
        if (ToolUtil.isEmpty(updateReqVO.getPartnerMiniSettingPolicyDTO().getIsShowCommission())) {
            updateReqVO.getPartnerMiniSettingPolicyDTO().setIsShowCommission("0");
        }
        sysPartnerPolicyMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysPartnerPolicy.class));
    }

    /**
     * 删除平台商政策(由平台商设置)
     *
     * @param partnerPolicyId 平台商政策id
     */
    @Override
    public void deleteSysPartnerPolicy(Long partnerPolicyId) {
        // 删除
        sysPartnerPolicyMapper.deleteById(partnerPolicyId);
    }

    /**
     * 批量删除平台商政策(由平台商设置)
     *
     * @param partnerPolicyIds 需要删除的平台商政策(由平台商设置)主键
     * @return 结果
     */
    @Override
    public void deleteSysPartnerPolicyByPartnerPolicyIds(Long[] partnerPolicyIds) {
        for (Long partnerPolicyId : partnerPolicyIds) {
            this.deleteSysPartnerPolicy(partnerPolicyId);
        }
    }

    /**
     * 获得平台商政策(由平台商设置)
     *
     * @return 平台商政策(由平台商设置)
     */
    @Override
    public SysPartnerPolicyRespVO getSysPartnerPolicy(Long supplierId) {
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        boolean isExist = true;
        //返回对象
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO = new SysPartnerPolicyRespVO();
        //校验小程序配置信息
        if (ObjectUtil.isNotNull(appletAgreementPolicyDtoCache.get(sysCode))) {
            sysPartnerPolicyRespVO.setAppletAgreementPolicyDto(appletAgreementPolicyDtoCache.get(sysCode));
        } else {
            isExist = false;
        }
        //校验基础设置信息
        if (ObjectUtil.isNotNull(basicSettingPolicyDTOCache.get(dcId))) {
            sysPartnerPolicyRespVO.setBasicSettingPolicyDto(basicSettingPolicyDTOCache.get(dcId));
        } else {
            isExist = false;
        }
        //校验订单参数配置信息
        if (ObjectUtil.isNotNull(orderSettingPolicyDTOCache.get(dcId))) {
            sysPartnerPolicyRespVO.setOrderSettingPolicyDto(orderSettingPolicyDTOCache.get(dcId));
        } else {
            isExist = false;
        }

        //校验提现配置信息
        if (ObjectUtil.isNotNull(withdrawalSettingPolicyDTOCache.get(sysCode))) {
            sysPartnerPolicyRespVO.setWithdrawalSettingPolicyDTO(withdrawalSettingPolicyDTOCache.get(sysCode));
        } else {
            isExist = false;
        }

        //校验芯烨配置信息
        if (ObjectUtil.isNotNull(xpYunSettingPolicyDTOCache.get(supplierId))) {
            sysPartnerPolicyRespVO.setXpYunSettingPolicyDTO(xpYunSettingPolicyDTOCache.get(supplierId));
        } else {
            isExist = false;
        }

        //校验入驻商售后配置信息
        if (ObjectUtil.isNotNull(afterSaleSettingPolicyDTOCache.get(supplierId))) {
            sysPartnerPolicyRespVO.setAfterSaleSettingPolicyDTO(afterSaleSettingPolicyDTOCache.get(supplierId));
        } else {
            isExist = false;
        }

        //校验飞鹅配置信息
        if (ObjectUtil.isNotNull(feieYunSettingPolicyDTOCache.get(supplierId))) {
            sysPartnerPolicyRespVO.setFeieYunSettingPolicyDTO(feieYunSettingPolicyDTOCache.get(supplierId));
        } else {
            isExist = false;
        }

        //校验业务员配置信息
        if (ObjectUtil.isNotNull(colonelSettingPolicyDTOCache.get(dcId))) {
            sysPartnerPolicyRespVO.setColonelSettingPolicyDTO(colonelSettingPolicyDTOCache.get(dcId));
        } else {
            isExist = false;
        }

        //校验业务员配置信息
        if (ObjectUtil.isNotNull(openSettingPolicyDTOCache.get(supplierId))) {
            sysPartnerPolicyRespVO.setOpenSettingPolicyDTO(openSettingPolicyDTOCache.get(supplierId));
        } else {
            isExist = false;
        }

        //校验平台商小程序配置信息
        if (ObjectUtil.isNotNull(partnerMiniSettingPolicyDTOCache.get(sysCode))) {
            sysPartnerPolicyRespVO.setPartnerMiniSettingPolicyDTO(partnerMiniSettingPolicyDTOCache.get(sysCode));
        } else {
            isExist = false;
        }

        //获取入驻商其他配置信息
        if (ObjectUtil.isNotNull(supplierOtherSettingPolicyDTOCache.get(supplierId))) {
            sysPartnerPolicyRespVO.setSupplierOtherSettingPolicyDTO(supplierOtherSettingPolicyDTOCache.get(supplierId));
        } else {
            isExist = false;
        }

        //校验运营商门店生命周期配置信息
        if(ObjectUtil.isNotNull(branchLifecycleSettingPolicyDTOCache.get(dcId))){
            sysPartnerPolicyRespVO.setBranchLifecycleSettingPolicyDTO(branchLifecycleSettingPolicyDTOCache.get(dcId));
        } else {
            isExist = false;
        }

        //不为空直接返回
        if (isExist) {
            return sysPartnerPolicyRespVO;
        }
        return partnerPolicyPipeline.partnerGetConfig(sysPartnerPolicyRespVO, sysCode, dcId, supplierId, null);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SysPartnerPolicy> getSysPartnerPolicyPage(SysPartnerPolicyPageReqVO pageReqVO) {
        return sysPartnerPolicyMapper.selectPage(pageReqVO);
    }

    /**
     * @Description: 获取配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/26 15:49
     */
    @Override
    public SysPartnerPolicyRespVO getSysPartnerPolicy(Long sysCode, Long dcId, Long supplierId, Integer type) {
        //返回对象
        SysPartnerPolicyRespVO sysPartnerPolicyRespVO = new SysPartnerPolicyRespVO();
        return partnerPolicyPipeline.partnerGetConfig(sysPartnerPolicyRespVO, sysCode, dcId, supplierId, type);
    }

    @Override
    public PageResult<SearchConfigPageRespVO> selectSearchConfigPage(SysPartnerPolicyPageReqVO pageReqVO) {
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        if(ToolUtil.isNotEmpty(dcId)){
            List<Long> sysAreaId = sysDcAreaMapper.selectByDcId(dcId);
            if(ToolUtil.isNotEmpty(sysAreaId)){
                pageReqVO.setAreaIds(sysAreaId);
            }
        }
        Page<SysPartnerPolicyPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<SearchConfigPageRespVO> searchConfigPage = sysPartnerPolicyMapper.selectSearchConfigPage(pageReqVO, page);
        if (ToolUtil.isEmpty(searchConfigPage)) {
            return new PageResult<>(searchConfigPage.getRecords(), searchConfigPage.getTotal());
        }

        searchConfigPage.getRecords().forEach(searchConfigPageRespVO -> {
            if (ToolUtil.isNotEmpty(searchConfigPageRespVO.getAreaId())) {
                AreaDTO areaDTO = sysCacheService.getAreaDTO(searchConfigPageRespVO.getAreaId());
                if (ToolUtil.isNotEmpty(areaDTO)) {
                    searchConfigPageRespVO.setAreaName(areaDTO.getAreaName());
                    if (ToolUtil.isNotEmpty(areaDTO.getPid())) {
                        AreaDTO areaDTO2 = sysCacheService.getAreaDTO(areaDTO.getPid());
                        if (ToolUtil.isNotEmpty(areaDTO2)) {
                            searchConfigPageRespVO.setAreaName2(areaDTO2.getAreaName());
                        }
                    }
                }
            }
        });
        return new PageResult<>(searchConfigPage.getRecords(), searchConfigPage.getTotal());
    }

    @Override
    public Boolean addSearchConfig(SearchConfigReqVO createReqVO) {
        if (ToolUtil.isNotEmpty(createReqVO.getCustomKey()) && ToolUtil.isNotEmpty(createReqVO.getCustomValue())) {

            //校验城市是否唯一  同一个平台商 在一个城市 只能有一个搜索推荐配置
            if (ToolUtil.isNotEmpty(sysPartnerPolicyMapper.getSearchConfig(createReqVO.getAreaId()))) {
                throw exception(SYS_PARTNER_POLICY_NONE_EXITS);
            }

            SysPartnerPolicy sysPartnerPolicy = new SysPartnerPolicy();
            sysPartnerPolicy.setSysCode(SecurityUtils.getLoginUser().getSysCode());
            sysPartnerPolicy.setPolicyName("搜索推荐设置");
            sysPartnerPolicy.setPolicyKey("partner.areaSearchRecommendJsonUrl_"+createReqVO.getAreaId()+"_"+createReqVO.getCustomKey());
            sysPartnerPolicy.setPolicyValue(createReqVO.getCustomValue());
            sysPartnerPolicy.setPolicyType("19");
            sysPartnerPolicy.setAreaId(createReqVO.getAreaId());
            sysPartnerPolicy.setCreateBy(SecurityUtils.getLoginUser().getUsername());
            sysPartnerPolicyMapper.insert(sysPartnerPolicy);

            searchConfigDtoCache.put(createReqVO.getAreaId(), new SearchConfigDTO(createReqVO.getCustomKey()));
            return true;
        }
        return null;
    }

    @Override
    public void updateSearchConfig(SearchConfigReqVO createReqVO) {
        SysPartnerPolicy sysPartnerPolicy =sysPartnerPolicyMapper.selectById(createReqVO.getPartnerPolicyId());
        if (ToolUtil.isNotEmpty(sysPartnerPolicy)) {
            sysPartnerPolicy.setAreaId(createReqVO.getAreaId());
            sysPartnerPolicy.setPolicyKey("partner.areaSearchRecommendJsonUrl_"+createReqVO.getAreaId()+"_"+createReqVO.getCustomKey());
            sysPartnerPolicy.setPolicyValue(createReqVO.getCustomValue());
            sysPartnerPolicyMapper.updateById(sysPartnerPolicy);
            searchConfigDtoCache.remove(sysPartnerPolicy.getAreaId());
        }

    }

    @Override
    public SearchConfigDTO getSearchConfig(Long areaId) {
        SearchConfigDTO searchConfigDTO = new SearchConfigDTO();
        SysPartnerPolicy sysPartnerPolicy = sysPartnerPolicyMapper.getSearchConfig(areaId);
        if(ToolUtil.isNotEmpty(sysPartnerPolicy)){
            // 将PolicyKey中的jsonUrl提取出来
            int startIndex = sysPartnerPolicy.getPolicyKey().indexOf("http");
            int endIndex = sysPartnerPolicy.getPolicyKey().lastIndexOf(".json") + ".json".length();
            String result = sysPartnerPolicy.getPolicyKey().substring(startIndex, endIndex);
            searchConfigDTO.setJsonUrl(result);
        }
        return searchConfigDTO;
    }

    @Override
    public SysPartnerPolicyRespVO getDcConfig(Long dcId) {
        return getSysPartnerPolicy(null, dcId, null, null);
    }

    private void validateSysPartnerPolicyExists(Long partnerPolicyId) {
        if (sysPartnerPolicyMapper.selectById(partnerPolicyId) == null) {
            throw exception(SYS_PARTNER_POLICY_NOT_EXISTS);
        }
    }
}
