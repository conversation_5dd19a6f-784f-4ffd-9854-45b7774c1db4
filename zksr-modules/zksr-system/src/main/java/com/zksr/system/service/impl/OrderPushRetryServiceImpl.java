package com.zksr.system.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.system.api.domain.OrderPushRetryParam;
import com.zksr.system.api.domain.OrderPushRetryResult;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mapper.SysInterfaceLogMapper;
import com.zksr.system.mq.SyncDataProducer;
import com.zksr.system.service.IOrderPushRetryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.constant.OpenApiConstants.LOG_IS_RETRY_1;

/**
 * @Description: 订单推送重试服务实现类
 * @Date: 2025/07/22
 */
@Slf4j
@Service
public class OrderPushRetryServiceImpl implements IOrderPushRetryService {

    @Autowired
    private SysInterfaceLogMapper sysInterfaceLogMapper;

    @Autowired
    private SyncDataProducer syncDataProducer;

    @Override
    public OrderPushRetryResult retryFailedOrderPush(OrderPushRetryParam param) {
        OrderPushRetryResult result = new OrderPushRetryResult();
        result.setStartTime(System.currentTimeMillis());
        
        log.info("开始执行订单推送重试任务，参数：批量大小{}，最大重试次数{}",
                param.getBatchSize(), param.getMaxRetryCount());

        try {
            // 查询需要重推的接口日志记录
            List<SysInterfaceLog> failedLogs = getFailedPushLogs(param);
            result.setScannedCount(failedLogs.size());
            
            if (failedLogs.isEmpty()) {
                log.info("未找到需要重推的订单推送记录");
                return result;
            }

            log.info("找到{}个需要重推的订单推送记录", failedLogs.size());

            // 分批处理记录
            int batchSize = param.getBatchSize();
            for (int i = 0; i < failedLogs.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, failedLogs.size());
                List<SysInterfaceLog> batch = failedLogs.subList(i, endIndex);
                
                log.info("处理第{}批记录，共{}个", (i / batchSize) + 1, batch.size());
                
                // 处理当前批次
                processBatch(batch, param, result);
            }

            log.info("订单推送重试任务完成，扫描{}个，成功{}个，失败{}个，跳过{}个", 
                    result.getScannedCount(), result.getSuccessCount(), 
                    result.getFailedCount(), result.getSkippedCount());

        } catch (Exception e) {
            log.error("执行订单推送重试任务失败", e);
            result.setErrorMessage(e.getMessage());
            throw e;
        } finally {
            result.setEndTime(System.currentTimeMillis());
            result.calculateDuration();
        }

        return result;
    }

    @Override
    public List<SysInterfaceLog> getFailedPushLogs(OrderPushRetryParam param) {
        // 计算时间范围：30分钟到2小时前
        LocalDateTime endTime = LocalDateTime.now().minusMinutes(param.getMinMinutes());
        LocalDateTime startTime = LocalDateTime.now().minusMinutes(param.getMaxMinutes());
        
        log.debug("查询时间范围：{} 到 {}", startTime, endTime);
        
        try {
            return sysInterfaceLogMapper.selectFailedOrderPushLogs(
                    B2BRequestType.SYNC_B2B_ORDER.getB2bType(),
                    OpenApiConstants.LOG_STATUS_FAIN,
                    param.getMaxRetryCount(),
                    startTime,
                    endTime,
                    param.getBatchSize()
            );
        } catch (Exception e) {
            log.error("查询需要重推的订单推送记录失败", e);
            throw e;
        }
    }

    @Override
    public boolean retryPushSingleOrder(SysInterfaceLog interfaceLog, OrderPushRetryParam param) {
        Long logId = interfaceLog.getId();
        
        try {
            log.info("开始重推订单推送记录：ID={}, 当前重试次数：{}",
                    logId, interfaceLog.getRetryCount());

            // 解析请求数据
            SyncDataDTO dataDTO = JSONObject.parseObject(interfaceLog.getReqData(), SyncDataDTO.class);
            if (dataDTO == null) {
                log.error("解析请求数据失败，记录ID：{}", logId);
                return false;
            }

            // 设置为实时重发
            dataDTO.setPropertyDelayTimeLevel(1);

            // 设置日志重发标识
            updateLogRetry(interfaceLog);
            
            // 发送重推消息
            syncDataProducer.sendSupplierSyncDataOrderEvent(dataDTO);
            
            log.info("订单推送记录重推成功，ID：{}", logId);
            return true;
            
        } catch (Exception e) {
            log.error("订单推送记录重推异常，ID：{}", logId, e);
            return false;
        }
    }

    /**
     * 更新接口日志重试信息
     * @param interfaceLog
     */
    private void updateLogRetry(SysInterfaceLog interfaceLog) {
        try {
            SysInterfaceLog updateLog = new SysInterfaceLog();
            updateLog.setId(interfaceLog.getId());
            updateLog.setIsRetry(LOG_IS_RETRY_1);
            updateLog.setUpdateTime(new Date());

            int updateCount = sysInterfaceLogMapper.updateById(updateLog);

            if (updateCount == 0) {
                log.warn("更新接口日志重试状态失败，记录可能不存在，ID：{}", interfaceLog.getId());
            }

        } catch (Exception e) {
            log.error("更新接口日志重试状态失败，ID：{}", interfaceLog.getId(), e);
            throw e;
        }
    }

    /**
     * 处理一批记录
     */
    private void processBatch(List<SysInterfaceLog> batch, OrderPushRetryParam param, OrderPushRetryResult result) {
        for (SysInterfaceLog interfaceLog : batch) {
            try {
                // 检查是否已达到最大重试次数
                Long retryCount = interfaceLog.getRetryCount() != null ? interfaceLog.getRetryCount() : 0L;
                if (retryCount >= param.getMaxRetryCount()) {
                    result.incrementSkippedCount();
                    log.debug("跳过记录ID：{}，已达到最大重试次数：{}", interfaceLog.getId(), retryCount);
                    continue;
                }

                // 重推订单
                boolean success = retryPushSingleOrder(interfaceLog, param);
                
                if (success) {
                    result.incrementSuccessCount();
                } else {
                    result.incrementFailedCount();
                }

            } catch (Exception e) {
                log.error("处理接口日志记录{}时发生异常", interfaceLog.getId(), e);
                result.incrementFailedCount();
            }
        }
    }
}
