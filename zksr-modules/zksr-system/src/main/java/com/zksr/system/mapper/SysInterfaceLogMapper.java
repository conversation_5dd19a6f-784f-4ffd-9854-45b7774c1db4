package com.zksr.system.mapper;

import cn.hutool.Hutool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.product.api.spu.vo.PrdtSpuPageReqVO;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportExcel;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.controller.log.vo.SysInterfaceLogPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 同步接口日志Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
@Mapper
public interface SysInterfaceLogMapper extends BaseMapperX<SysInterfaceLog> {
    default PageResult<SysInterfaceLog> selectPage(SysInterfaceLogPageReqVO reqVO) {
        String reqBeginTime = null;
        String reqEndTime = null;

        if(StringUtils.isNotNull(reqVO.getReqTime())){
            reqBeginTime = reqVO.getReqTime()+" 00:00:00";
            reqEndTime = reqVO.getReqTime()+" 23:59:59";
        }

        return selectPage(reqVO, new LambdaQueryWrapperX<SysInterfaceLog>()
                    .eqIfPresent(SysInterfaceLog::getId, reqVO.getId())
                    .likeIfPresent(SysInterfaceLog::getReqId, reqVO.getReqId())
                    .eqIfPresent(SysInterfaceLog::getSource, reqVO.getSource())
                    .eqIfPresent(SysInterfaceLog::getReceive, reqVO.getReceive())
                    .likeIfPresent(SysInterfaceLog::getBizData, reqVO.getBizData())
                    .betweenIfPresent(SysInterfaceLog::getReqTime, reqBeginTime, reqEndTime)
                    .eqIfPresent(SysInterfaceLog::getStatus, reqVO.getStatus())
                    .eqIfPresent(SysInterfaceLog::getMessage, reqVO.getMessage())
                    .eqIfPresent(SysInterfaceLog::getRequestType, reqVO.getRequestType())
                    .eqIfPresent(SysInterfaceLog::getSupplierId, reqVO.getSupplierId())
                    .eqIfPresent(SysInterfaceLog::getOperationType, reqVO.getOperationType())
                    .eqIfPresent(SysInterfaceLog::getRetryCount, reqVO.getRetryCount())
                    .eqIfPresent(SysInterfaceLog::getIsRetry, reqVO.getIsRetry())
                    .eqIfPresent(SysInterfaceLog::getResendMessage, reqVO.getResendMessage())
                .orderByDesc(SysInterfaceLog::getId));
    }

    default SysInterfaceLog selectByReqId(String reqId){
        return selectOne(new LambdaQueryWrapper<SysInterfaceLog>().eq(SysInterfaceLog::getReqId,reqId));
    }

    Page<SysInterfaceLogPageReqVO> selectPageLog(@Param("reqVO") SysInterfaceLogPageReqVO reqVO, @Param("page") Page<SysInterfaceLogPageReqVO> page);

    /**
     * 每天同步第三方数据情况汇总统计(日同步报告)
     * @param sysCode
     * @param date
     * @return
     */
    List<SyncEmailReportExcel> getLogByDaySyncReport(@Param("sysCode")Long sysCode, @Param("date")String date);
}
