package com.zksr.system.controller.openability.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.Date;


/**
 * 开放能力对象 sys_openability
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Data
@ApiModel("开放能力 - sys_openability Response VO")
public class SysOpenabilityRespVO {
    private static final long serialVersionUID = 1L;

    /** 开放能力id */
    @ApiModelProperty(value = "菜单状态")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long openabilityId;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 资源类型（数据字典 supplier-入驻商，partner-平台商） */
    @Excel(name = "资源类型", readConverterExp = "supplier=入驻商")
    @ApiModelProperty(value = "资源类型")
    private String merchantType;

    /** 接口名 */
    @Excel(name = "接口名")
    @ApiModelProperty(value = "接口名")
    private String abilityName;

    /** 接口key(唯一) */
    @Excel(name = "接口key(唯一)")
    @ApiModelProperty(value = "接口key(唯一)")
    private String abilityKey;

    /** 菜单状态（0正常 1停用） */
    @Excel(name = "菜单状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "菜单状态")
    private String status;

    /** 单个用户qps限制 */
    @Excel(name = "单个用户qps限制")
    @ApiModelProperty(value = "单个用户qps限制")
    private Integer rateLimit;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
