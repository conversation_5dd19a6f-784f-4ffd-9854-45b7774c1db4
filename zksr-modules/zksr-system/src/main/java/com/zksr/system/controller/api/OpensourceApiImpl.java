package com.zksr.system.controller.api;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.domain.SysOpensource;
import com.zksr.system.service.ISysOpensourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@InnerAuth
public class OpensourceApiImpl implements OpensourceApi {

    @Autowired
    private ISysOpensourceService sysOpensourceService;

    @Override
    public CommonResult<OpensourceDto> getInfoByOpensouceIdAndSourceSecret(String opensourceId, String sourceSecret) {
        SysOpensource sysOpensource = sysOpensourceService.getInfoByOpensouceIdAndSourceSecret(opensourceId, sourceSecret);
        return success(HutoolBeanUtils.toBean(sysOpensource, OpensourceDto.class));
    }

    @Override
    public CommonResult<LoginOpensource> getOpensourceInfo(String sourceKey, String sourceSecret) {
        return success(sysOpensourceService.getOpensourceInfo(sourceKey, sourceSecret));
    }

    @Override
    public void updateTokenUuid(@RequestParam(name = "opensourceId")Long opensourceId,
                               @RequestParam(name = "token")String token) {
        sysOpensourceService.updateTokenUuid(opensourceId, token);
    }

    @Override
    public CommonResult<OpensourceDto> getOpensourceByMerchantId(Long merchantId) {
        return success(sysOpensourceService.getOpensourceByMerchantId(merchantId));
    }

    @Override
    public CommonResult<VisualSettingMasterDto> getVisualSettingMasterByMerchantId(Long merchantId) {
        return success(sysOpensourceService.getVisualSettingMasterByMerchantId(merchantId));
    }

    @Override
    public CommonResult<VisualSettingDetailDto> getVisualSettingDetailByMerchantId(String key) {
        return success(sysOpensourceService.getVisualSettingDetailByMerchantId(key));
    }

    @Override
    public CommonResult<OpensourceDto> insertOpensourceSupplierByApi(OpensourceDto data) {
        return success(sysOpensourceService.insertOpensourceSupplierByApi(data));
    }

    @Override
    public CommonResult<List<OpensourceDto>> getOpenSourceBySysCode(Long sysCode) {
        return success(sysOpensourceService.getOpenSourceBySysCode(sysCode));
    }
}
