package com.zksr.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.controller.area.vo.SysAreaPageReqVO;
import com.zksr.system.controller.area.vo.SysAreaRespVO;
import com.zksr.system.controller.channel.vo.SysChannelSelectedRespVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.system.mapper.SysChannelMapper;
import com.zksr.system.domain.SysChannel;
import com.zksr.system.controller.channel.vo.SysChannelPageReqVO;
import com.zksr.system.controller.channel.vo.SysChannelSaveReqVO;
import com.zksr.system.service.ISysChannelService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_BRANCH_CHANNEL_NOT_STOP;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 渠道信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Service
public class SysChannelServiceImpl implements ISysChannelService {
    @Autowired
    private SysChannelMapper sysChannelMapper;

    @Autowired
    private Cache<Long, ChannelDTO> channelDTOCache;
    @Resource
    private BranchApi branchApi;

    /**
     * 新增渠道信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_CHANNEL)
    public Long insertSysChannel(SysChannelSaveReqVO createReqVO) {
        // 数据验证
        validateSysChannel(createReqVO);
        // 插入
        SysChannel sysChannel = HutoolBeanUtils.toBean(createReqVO, SysChannel.class);
        sysChannelMapper.insert(sysChannel);
        // 返回
        return sysChannel.getChannelId();
    }

    /**
     * 修改渠道信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysChannel(SysChannelSaveReqVO updateReqVO) {
        // 数据验证
        validateSysChannel(updateReqVO);

        // 停用判断
        if (ToolUtil.isNotEmpty(updateReqVO.getStatus()) && updateReqVO.getStatus() == 0){
            List<BranchDTO> branchDTOList = branchApi.getBranchListByChannelId(updateReqVO.getChannelId(),updateReqVO.getSysCode()).getData();
            if (ToolUtil.isNotEmpty(branchDTOList)){
                List<String> collect = branchDTOList.stream().map(BranchDTO::getBranchName).collect(Collectors.toList());
                throw exception(MEM_BRANCH_CHANNEL_NOT_STOP,collect);
            }
        }

        sysChannelMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysChannel.class));
        channelDTOCache.remove(updateReqVO.getChannelId());
    }

    /**
     * 删除渠道信息
     *
     * @param channelId 渠道id
     */
    @Override
    public void deleteSysChannel(Long channelId) {
        // 删除
        sysChannelMapper.deleteById(channelId);
        channelDTOCache.remove(channelId);
    }

    /**
     * 批量删除渠道信息
     *
     * @param channelIds 需要删除的渠道信息主键
     * @return 结果
     */
    @Override
    public void deleteSysChannelByChannelIds(Long[] channelIds) {
        for (Long channelId : channelIds) {
            this.deleteSysChannel(channelId);
        }
    }

    /**
     * 获得渠道信息
     *
     * @param channelId 渠道id
     * @return 渠道信息
     */
    @Override
    public SysChannel getSysChannel(Long channelId) {
        return sysChannelMapper.selectById(channelId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SysChannel> getSysChannelPage(SysChannelPageReqVO pageReqVO) {
        Page<SysAreaPageReqVO> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        Page<SysChannel> sysChannelRespVOPage = sysChannelMapper.selectPage(page, pageReqVO, SecurityUtils.getLoginUser().getSysCode());
        return new PageResult<>(sysChannelRespVOPage.getRecords(), sysChannelRespVOPage.getTotal());
    }

    /**
    * @Description: 获取所有渠道信息
    * @Author: liuxingyu
    * @Date: 2024/3/22 14:40
    */
    @Override
    public List<SysChannel> getChannelList() {
        return sysChannelMapper.selectList("status",1);
    }

    @Override
    public List<SysChannelSelectedRespVO> getSelectedSysChannel(List<Long> channelIds) {
        return BeanUtil.copyToList(sysChannelMapper.selectSelectedSysChannel(channelIds), SysChannelSelectedRespVO.class);
    }

    @Override
    public List<SysChannel> getChannelListBySysCode(Long sysCode, String channelName) {
        return sysChannelMapper.getChannelListBySysCode(sysCode, channelName);
    }

    private void removeChannelDtoCache(Long channelId) {
        channelDTOCache.remove(channelId);
    }

    private void validateSysChannel(SysChannelSaveReqVO saveReqVO) {
        if (sysChannelMapper.selectExists(saveReqVO)> NumberPool.LONG_ZERO) {
            throw exception(SYS_CHANNEL_EXISTS);
        }
    }

}
