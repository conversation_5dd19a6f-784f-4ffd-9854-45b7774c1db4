package com.zksr.system.controller.print.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

/**
 * 打印设置对象 sys_print_settings
 *
 * <AUTHOR>
 * @date 2024-09-02
 */
@Data
@ApiModel("打印设置 - sys_print_settings分页 Request VO")
public class SysPrintSettingsSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @ApiModelProperty(value = "平台ID")
    private Long printSetterId;

    /** 模块名称 */
    @Excel(name = "模块名称")
    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    /** 模块类型 */
    @Excel(name = "模块类型")
    @ApiModelProperty(value = "模块类型")
    private String moduleType;

    /** 模板宽度 */
    @Excel(name = "模板宽度")
    @ApiModelProperty(value = "模板宽度")
    private BigDecimal templateWidth;

    /** 模板高度 */
    @Excel(name = "模板高度")
    @ApiModelProperty(value = "模板高度")
    private BigDecimal templateHeight;

    /** 纸张宽度 */
    @Excel(name = "纸张宽度")
    @ApiModelProperty(value = "纸张宽度")
    private BigDecimal paperWidth;

    /** 纸张高度 */
    @Excel(name = "纸张高度")
    @ApiModelProperty(value = "纸张高度")
    private BigDecimal paperHeight;

    /** 打印内容 */
    @Excel(name = "打印内容")
    @ApiModelProperty(value = "打印内容")
    private String printContent;

    /** 平台ID */
    @Excel(name = "平台ID")
    @ApiModelProperty(value = "平台ID")
    private Long sysCode;

    /** 删除标志(0正常;1删除) */
    @ApiModelProperty(value = "平台ID")
    private Integer delFlag;

    /** 纸张类型 */
    @Excel(name = "纸张类型")
    @ApiModelProperty(value = "纸张类型")
    private String paperType;
}
