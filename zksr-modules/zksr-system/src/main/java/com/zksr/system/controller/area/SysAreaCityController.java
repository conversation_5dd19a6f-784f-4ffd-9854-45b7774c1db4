package com.zksr.system.controller.area;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.service.ISysAreaCityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.convert.area.SysAreaCityConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 省市区Controller
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Api(tags = "管理后台 - 省市区接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/areaCity")
public class SysAreaCityController {
    @Autowired
    private ISysAreaCityService sysAreaCityService;

    /**
     * 新增省市区
     */
    @ApiOperation(value = "新增省市区", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "省市区", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysAreaCitySaveReqVO createReqVO) {
        return success(sysAreaCityService.insertSysAreaCity(createReqVO));
    }

    /**
     * 修改省市区
     */
    @ApiOperation(value = "修改省市区", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "省市区", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysAreaCitySaveReqVO updateReqVO) {
            sysAreaCityService.updateSysAreaCity(updateReqVO);
        return success(true);
    }

    /**
     * 删除省市区
     */
    @ApiOperation(value = "删除省市区", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "省市区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{areaCityIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] areaCityIds) {
        sysAreaCityService.deleteSysAreaCityByAreaCityIds(areaCityIds);
        return success(true);
    }

    /**
     * 获取省市区详细信息
     */
    @ApiOperation(value = "获得省市区详情", httpMethod = HttpMethod.GET)
    @GetMapping(value = "/{areaCityId}")
    public CommonResult<SysAreaCityRespVO> getInfo(@PathVariable("areaCityId") Long areaCityId) {
        SysAreaCity sysAreaCity = sysAreaCityService.getSysAreaCity(areaCityId);
        return success(SysAreaCityConvert.INSTANCE.convert(sysAreaCity));
    }

    /**
     * 分页查询省市区
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得省市区分页列表", httpMethod = HttpMethod.GET)
    public CommonResult<PageResult<SysAreaCityRespVO>> getPage(SysAreaCityPageReqVO pageReqVO) {
        PageResult<SysAreaCity> pageResult = sysAreaCityService.getSysAreaCityPage(pageReqVO);
        return success(SysAreaCityConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:area-city:add";
        /** 编辑 */
        public static final String EDIT = "system:area-city:edit";
        /** 删除 */
        public static final String DELETE = "system:area-city:remove";
        /** 列表 */
        public static final String LIST = "system:area-city:list";
        /** 查询 */
        public static final String GET = "system:area-city:query";
        /** 停用 */
        public static final String DISABLE = "system:area-city:disable";
        /** 启用 */
        public static final String ENABLE = "system:area-city:enable";
    }
}
