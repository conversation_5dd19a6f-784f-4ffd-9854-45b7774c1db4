package com.zksr.system.openapi.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.domain.vo.openapi.IncreaseUpdateStockDTO;
import com.zksr.common.core.domain.vo.openapi.receive.PrdInventoryVO;
import com.zksr.common.core.enums.SourceType;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.spu.dto.SpuductOpenDTO;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mq.OpenapiProducer;
import com.zksr.system.openapi.service.ITokenService;
import com.zksr.system.service.ISysInterfaceLogService;
import io.seata.common.util.CollectionUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static com.zksr.common.core.constant.OpenApiConstants.LOG_STATUS_FAIN;
import static com.zksr.common.core.constant.OpenApiConstants.LOG_STATUS_SUCCES;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.OPENAPI_CHECK_REQVO_EXISTS;

@RestController
@RequestMapping("/openapi/commodity")
@Api(tags = "OPENAPI - 商品模块接口")
@Slf4j
public class CommodityController {

    @Autowired
    private OpenapiProducer openapiProducer;

    @Resource
    private ITokenService tokenService;

    @Resource
    private ISysInterfaceLogService sysInterfaceLogService;

    @Resource
    private SkuApi skuApi;


    @ApiOperation("接收商品库存")
    @PostMapping("/savePrdtStock")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "savePrdtStock")
//    @Log(title = "OPENAPI - 接收商品库存", businessType = BusinessType.INSERT)
//    @ReceiveInterfaceLog(operationType = OperationType.UPDATE,requestType = B2BRequestType.RECEIVE_SPU_INVENTORY)
    CommonResult<Boolean> receivePrdInventory(@Valid @RequestBody List<PrdInventoryVO> prdInventoryVOList) {
        log.info("OPENAPI - 新增商品库存信息:{}", JSON.toJSONString(prdInventoryVOList));
        if (ToolUtil.isEmpty(prdInventoryVOList)) {
            log.info("OPENAPI - 新增商品库存信息:入参为空");
            return CommonResult.success(true);
        }

        List<SysInterfaceLog> interfaceLogList = new ArrayList(prdInventoryVOList.size());
        SysInterfaceLog interfaceLog = tokenService.getOpenapiInitLog(B2BRequestType.SAVE_PRDT_STOCK.getB2bType(), OperationType.ADD_OR_UPDATE.getCode(), null);
        //log.info("CommodityController receivePrdInventory interfaceLogList:{}", JSON.toJSONString(interfaceLogList));
        for (PrdInventoryVO prdInventoryVO : prdInventoryVOList) {
/*            //校验库存更新时间
            if(ToolUtil.isEmpty(prdInventoryVO.getLastUpdateTime())){
                log.info("OPENAPI - 新增商品库存信息:最后库存更新时间为空，不更新库存信息。商品数据：{}",prdInventoryVO);
                return CommonResult.success(true);
            }*/

            SysInterfaceLog newLog = new SysInterfaceLog();
            BeanUtil.copyProperties(interfaceLog, newLog);
            newLog.setBizData(JSON.toJSONString(prdInventoryVO));
            newLog.setReqId(IdUtils.fastSimpleUUID());
            interfaceLogList.add(newLog);
        }
        //boolean res = sysInterfaceLogService.insertBatch(interfaceLogList);
        for (SysInterfaceLog sysInterfaceLog : interfaceLogList) {
            try {
                openapiProducer.sendSaveprdtStockMsg(sysInterfaceLog);
            } catch (Exception e) {
                log.error("OPENAPI - 发送库存信息消息失败: {}", e.getMessage(), e);
//                sysInterfaceLog.setStatus(LOG_STATUS_FAIN);
//                sysInterfaceLog.setMessage(e.getMessage());
//                sysInterfaceLogService.updateSysInterfaceLog(HutoolBeanUtils.toBean(sysInterfaceLog, SysInterfaceLogSaveReqVO.class));
            }
        }
        return CommonResult.success(true);
    }

    @ApiOperation("接收商品商品生产日期")
    @PostMapping("/savePrdtDate")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "savePrdtDate")
//    @Log(title = "OPENAPI - 接收商品商品生产日期", businessType = BusinessType.INSERT)
//    @ReceiveInterfaceLog(operationType = OperationType.UPDATE,requestType = B2BRequestType.RECEIVE_SPU_DATE)
    CommonResult<Boolean> receivePrdProductionDate(@RequestBody List<SpuductOpenDTO> spuductOpenDTOList) {
        log.info("OPENAPI - 新增商品生产日期信息:{}", JSON.toJSONString(spuductOpenDTOList));
        if (ToolUtil.isEmpty(spuductOpenDTOList)) {
            log.info("OPENAPI - 新增商品生产日期信息:入参为空");
            return CommonResult.success(true);
        }
        List<SysInterfaceLog> interfaceLogList = new ArrayList(spuductOpenDTOList.size());
        SysInterfaceLog interfaceLog = tokenService.getOpenapiInitLog(B2BRequestType.SAVE_PRDT_DATE.getB2bType(), OperationType.ADD_OR_UPDATE.getCode(), null);
        for (SpuductOpenDTO spuductOpenDTO : spuductOpenDTOList) {
            SysInterfaceLog newLog = new SysInterfaceLog();
            BeanUtil.copyProperties(interfaceLog, newLog);
            newLog.setBizData(JSON.toJSONString(spuductOpenDTO));
            newLog.setReqId(IdUtils.fastSimpleUUID());
            interfaceLogList.add(newLog);
        }
        // boolean res = sysInterfaceLogService.insertBatch(interfaceLogList);
        //log.info("CommodityController:receivePrdProductionDate:interfaceLogList:{}", JSON.toJSONString(interfaceLogList));
        for (SysInterfaceLog sysInterfaceLog : interfaceLogList) {
            try {
                openapiProducer.sendSaveprdtDateMsg(sysInterfaceLog);
            } catch (Exception e) {
                log.error("OPENAPI - 发送生产日期消息失败: {}", e.getMessage(), e);
//                sysInterfaceLog.setStatus(LOG_STATUS_FAIN);
//                sysInterfaceLog.setMessage(e.getMessage());
//                sysInterfaceLogService.updateSysInterfaceLog(HutoolBeanUtils.toBean(sysInterfaceLog, SysInterfaceLogSaveReqVO.class));
            }
        }
        return CommonResult.success(true);
    }


    @ApiOperation("增量库存调整接口")
    @PostMapping("/increaseUpdateStock")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "increaseUpdateStock")
    CommonResult<Boolean> increaseUpdateStock(@Valid @RequestBody IncreaseUpdateStockDTO data) {
        log.info("OPENAPI - 增量库存调整接口:{}", JSON.toJSONString(data));
        if (data == null || CollectionUtils.isEmpty(data.getDetails())) {
            return CommonResult.success(true);
        }

        SysInterfaceLog interfaceLog = saveInterfaceLog(data, B2BRequestType.SYNC_ERP_INCREMENT_UPDATE_STOCK, OperationType.ADD_OR_UPDATE, data.getRequestId());
        if (ObjectUtil.isNull(interfaceLog) || ObjectUtil.isNull(interfaceLog.getId())) {
            log.warn("幂等校验，reqId={}", data.getRequestId());
            return CommonResult.success(true);
        }
        data.setSupplierId(interfaceLog.getSupplierId());
        log.debug("CommodityController:increaseUpdateStock:interfaceLog:{}", JSON.toJSONString(interfaceLog));

        try {
            //更新库存要实时，不走MQ那套逻辑
            CommonResult<Boolean> commonResult = skuApi.increaseUpdateStock(data);
            if (commonResult != null && commonResult.isSuccess() && commonResult.getCheckedData()) {
                updateLogStatus(interfaceLog, LOG_STATUS_SUCCES);
            } else {
                updateLogStatus(interfaceLog, LOG_STATUS_FAIN);
            }
            return commonResult;
        } catch (Exception e) {
            log.error("增量库存更新失败：", e);
            updateLogStatus(interfaceLog, LOG_STATUS_FAIN);
            throw e;
        }
    }

    public SysInterfaceLog saveInterfaceLog(Object data, B2BRequestType requestType, OperationType operationType, String reqId) {
        SysInterfaceLog interfaceLog = new SysInterfaceLog();

        if (ToolUtil.isEmpty(data)) {
            log.info("OPENAPI - {}:入参为空", requestType.getInfo());
            throw exception(OPENAPI_CHECK_REQVO_EXISTS, requestType.getInfo());
        }

        //幂等校验：如果已存在该reqId的日志记录 则拦截不往下执行
        if (tokenService.checkReceiveLogBefore(reqId)) {
            return interfaceLog;
        }

        //组装日志公共参数
        interfaceLog = tokenService.getOpenapiInitLog(requestType.getB2bType(), operationType.getCode(), reqId);
        log.info("OPENAPI - {}:{}", requestType.getInfo(), JSON.toJSONString(data));

        //组装业务参数
        //新增或更新请求参数
        interfaceLog.setBizData(JSON.toJSONString(data));
        interfaceLog.setReqId(reqId);

        //新增日志信息
        sysInterfaceLogService.insertLog(interfaceLog);
        return interfaceLog;
    }

    public void updateLogStatus(SysInterfaceLog interfaceLog, Integer status) {
        interfaceLog.setStatus(status);
        sysInterfaceLogService.updateLog(interfaceLog);
    }
}
