package com.zksr.system.controller.visualSetting;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplatePageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplateRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplateSaveReqVO;
import com.zksr.system.convert.visualSetting.visualSettingTemplate.VisualSettingTemplateConvert;
import com.zksr.system.domain.VisualSettingTemplate;
import com.zksr.system.service.IVisualSettingTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 可视化接口模板Controller
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Api(tags = "管理后台 - 可视化接口模板接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/visualapi/template")
public class VisualSettingTemplateController {
    @Autowired
    private IVisualSettingTemplateService visualSettingTemplateService;

    /**
     * 新增可视化接口模板
     */
    @ApiOperation(value = "新增可视化接口模板", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "可视化接口模板", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody VisualSettingTemplateSaveReqVO createReqVO) {
        return success(visualSettingTemplateService.insertVisualSettingTemplate(createReqVO));
    }

    /**
     * 修改可视化接口模板
     */
    @ApiOperation(value = "修改可视化接口模板", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "可视化接口模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody VisualSettingTemplateSaveReqVO updateReqVO) {
            visualSettingTemplateService.updateVisualSettingTemplate(updateReqVO);
        return success(true);
    }

    /**
     * 删除可视化接口模板
     */
    @ApiOperation(value = "删除可视化接口模板", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "可视化接口模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{visualTemplateIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] visualTemplateIds) {
        visualSettingTemplateService.deleteVisualSettingTemplateByVisualTemplateIds(visualTemplateIds);
        return success(true);
    }

    /**
     * 获取可视化接口模板详细信息
     */
    @ApiOperation(value = "获得可视化接口模板详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{visualTemplateId}")
    public CommonResult<VisualSettingTemplateRespVO> getInfo(@PathVariable("visualTemplateId") Long visualTemplateId) {
        VisualSettingTemplate visualSettingTemplate = visualSettingTemplateService.getVisualSettingTemplate(visualTemplateId);
        return success(VisualSettingTemplateConvert.INSTANCE.convert(visualSettingTemplate));
    }

    /**
     * 分页查询可视化接口模板
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得可视化接口模板分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<VisualSettingTemplateRespVO>> getPage(@Valid VisualSettingTemplatePageReqVO pageReqVO) {
        PageResult<VisualSettingTemplate> pageResult = visualSettingTemplateService.getVisualSettingTemplatePage(pageReqVO);
        return success(VisualSettingTemplateConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:visualSettingTemplate:add";
        /** 编辑 */
        public static final String EDIT = "system:visualSettingTemplate:edit";
        /** 删除 */
        public static final String DELETE = "system:visualSettingTemplate:remove";
        /** 列表 */
        public static final String LIST = "system:visualSettingTemplate:list";
        /** 查询 */
        public static final String GET = "system:visualSettingTemplate:query";
        /** 停用 */
        public static final String DISABLE = "system:visualSettingTemplate:disable";
        /** 启用 */
        public static final String ENABLE = "system:visualSettingTemplate:enable";
    }
}
