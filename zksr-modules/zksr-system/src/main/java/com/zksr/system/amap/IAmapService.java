package com.zksr.system.amap;

import com.zksr.system.amap.param.CheckLocationParam;
import com.zksr.system.amap.param.PolygonGeofenceParam;
import com.zksr.system.amap.param.ServiceParam;
import com.zksr.system.amap.result.CheckLocationResult;
import com.zksr.system.amap.result.PolygonGeofenceResult;
import com.zksr.system.amap.result.ServiceResult;
import com.zksr.system.api.amap.dto.LongitudeAndLatitudeResult;
import com.zksr.system.api.amap.vo.LongitudeAndLatitudeParam;

/**
* @Description: 高德猎鹰Api
* @Author: liuxingyu
* @Date: 2024/3/27 16:07
*/
public interface IAmapService {
    /**
    * @Description: 新建服务
    * @Param: ServiceParam(新增不需要sid)
    * @return: ServiceResult
    * @Author: liuxingyu
    * @Date: 2024/3/27 16:07
    */
    ServiceResult addService(ServiceParam serviceParam);

    /**
    * @Description: 创建多边形围栏
    * @Param: PolygonGeofenceParam
    * @return: PolygonGeofenceResult
    * @Author: liuxingyu
    * @Date: 2024/3/27 16:09
    */
    PolygonGeofenceResult addPolygonGeofence(PolygonGeofenceParam polygonGeofenceParam);

    /**
    * @Description: 修改多边形围栏
    * @Param: PolygonGeofenceParam(修改不需要gfid)
    * @return: PolygonGeofenceResult
    * @Author: liuxingyu
    * @Date: 2024/3/27 16:16
    */
    PolygonGeofenceResult updatePolygonGeofence(PolygonGeofenceParam polygonGeofenceParam);

    /**
    * @Description: 查询指定坐标与围栏关系
    * @Param: CheckLocationParam
    * @return: CheckLocationResult
    * @Author: liuxingyu
    * @Date: 2024/3/27 16:47
    */
    CheckLocationResult checkLocation(CheckLocationParam checkLocationParam);

    /**
     * @Description: 根据经纬度查询地址
     * @param longitudeAndLatitudeParam
     * @return
     */
    LongitudeAndLatitudeResult selectLongitudeAndLatitude(LongitudeAndLatitudeParam longitudeAndLatitudeParam);
}
