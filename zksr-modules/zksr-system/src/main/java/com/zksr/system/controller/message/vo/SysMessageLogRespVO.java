package com.zksr.system.controller.message.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;


/**
 * 消息发送记录对象 sys_message_log
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Data
@ApiModel("消息发送记录 - sys_message_log Response VO")
public class SysMessageLogRespVO {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "消息内容")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long msgId;

    /** 平台商ID */
    @Excel(name = "平台商ID")
    @ApiModelProperty(value = "平台商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 消息模版ID */
    @Excel(name = "消息模版ID")
    @ApiModelProperty(value = "消息模版ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long messageTemplateId;

    /** 接受商户ID */
    @Excel(name = "接受商户ID")
    @ApiModelProperty(value = "接受商户ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 接受商户类型 */
    @Excel(name = "接受商户类型")
    @ApiModelProperty(value = "接受商户类型")
    private String merchantType;

    /** 商户名称 */
    @Excel(name = "商户名称")
    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    /** 0-未发送, 1-成功, 2-失败 */
    @Excel(name = "0-未发送, 1-成功, 2-失败")
    @ApiModelProperty(value = "0-未发送, 1-成功, 2-失败")
    private Integer state;

    /** 消息内容 */
    @Excel(name = "消息内容")
    @ApiModelProperty(value = "消息内容")
    private String content;

    /** 消息备注 */
    @Excel(name = "消息备注")
    @ApiModelProperty(value = "消息备注")
    private String tips;
}
