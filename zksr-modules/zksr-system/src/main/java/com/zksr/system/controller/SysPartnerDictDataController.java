package com.zksr.system.controller;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.controller.BaseController;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.page.TableDataInfo;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.domain.vo.SysDictReqVO;
import com.zksr.system.service.ISysDictDataService;
import com.zksr.system.service.ISysDictTypeService;
import com.zksr.system.service.ISysPartnerDictDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 数据字典信息
 * 
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 平台商字典接口", produces = "application/json")
@RestController
@RequestMapping("/partnerDict/data")
public class SysPartnerDictDataController extends BaseController
{
    @Autowired
    private ISysPartnerDictDataService dictDataService;

    @GetMapping("/list")
    @ApiOperation("查询列表")
    public TableDataInfo list(SysPartnerDictData dictData)
    {
        startPage();
        List<SysPartnerDictData> list = dictDataService.getList(dictData);
        return getDataTable(list);
    }

    @GetMapping("/selectDictDataById")
    @ApiOperation("根据字典id查询")
    public AjaxResult selectDictDataById(Long dictCode){
        return AjaxResult.success(dictDataService.selectDictDataById(dictCode));
    }


    /**
     * 新增字典类型
     */
    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增字典类型")
    public AjaxResult add(@Validated @RequestBody SysPartnerDictData dict)
    {
        dict.setCreateBy(SecurityUtils.getUsername());
        return toAjax(dictDataService.insertDictData(dict));
    }

    /**
     * 修改保存字典类型
     */
    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改保存字典类型")
    public AjaxResult edit(@Validated @RequestBody SysPartnerDictData dict)
    {
        dict.setUpdateBy(SecurityUtils.getUsername());
        return toAjax(dictDataService.updateDictData(dict));
    }

    /**
     * 删除字典类型
     */
    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    @ApiOperation("删除字典类型")
    public AjaxResult remove(@PathVariable Long[] dictCodes)
    {
        dictDataService.deleteDictDataByIds(dictCodes);
        return success();
    }

}
