package com.zksr.system.api.bank;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.bank.vo.SysBankChannelPageReqVO;
import com.zksr.system.api.bank.vo.SysBankChannelRespVO;
import com.zksr.system.convert.bank.SysBankChannelConvert;
import com.zksr.system.domain.SysBankChannel;
import com.zksr.system.service.ISysBankChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 银联号查询
 * @date 2024/7/24 9:55
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class BankChannelApiImpl implements BankChannelApi{

    @Autowired
    private ISysBankChannelService sysBankChannelService;

    @Override
    public CommonResult<PageResult<SysBankChannelRespVO>> getPage(SysBankChannelPageReqVO pageReqVO) {
        PageResult<SysBankChannel> pageResult = sysBankChannelService.getSysBankChannelPage(pageReqVO);
        return success(SysBankChannelConvert.INSTANCE.convertPage(pageResult));
    }

    @Override
    public CommonResult<SysBankChannelRespVO> getChannelByNo(String bankChannelNo) {
        return success(SysBankChannelConvert.INSTANCE.convert(sysBankChannelService.getChannelByNo(bankChannelNo)));
    }
}
