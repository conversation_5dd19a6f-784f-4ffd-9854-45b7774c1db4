package com.zksr.system.service.impl;

import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.system.controller.area.vo.SysAreaSaveReqVO;
import com.zksr.system.domain.SysArea;
import com.zksr.system.domain.SysDcAreaZip;
import com.zksr.system.mapper.SysDcAreaZipMapper;
import com.zksr.system.service.ISysDcAreaZipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.Objects;

/**
 * 运营商区域城市拉链表 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class SysDcAreaZipServiceImpl implements ISysDcAreaZipService {

    @Autowired
    private SysDcAreaZipMapper sysDcAreaZipMapper;

    @Override
    @Transactional
    public void insertSysDcAreaZip(SysAreaSaveReqVO reqVO, SysArea sysArea) {
        //获取时间
        Date nowDate = DateUtils.getNowDate();
        Date endOfYearDate = DateUtils.toDate(LocalDateTime.of(2099, 12, 30, 23, 59, 59));
        SysDcAreaZip currentRelation = sysDcAreaZipMapper.selectSysDcAreaZip(sysArea);
        if(ToolUtil.isEmpty(currentRelation)){
            // 插入新的运营商区域城市拉链记录
            SysDcAreaZip newRelation = new SysDcAreaZip();
            newRelation.setSysCode(reqVO.getSysCode());
            newRelation.setAreaId(reqVO.getAreaId());
            newRelation.setDcId(reqVO.getDcId());
            newRelation.setStartDate(nowDate);
            newRelation.setEndDate(endOfYearDate);
            sysDcAreaZipMapper.insert(newRelation);
        }else{
            if(Objects.equals(currentRelation.getSysCode(), reqVO.getSysCode())
            && Objects.equals(currentRelation.getAreaId(), reqVO.getAreaId())
            && !Objects.equals(currentRelation.getDcId(), reqVO.getDcId())){

               currentRelation.setEndDate(nowDate);
               sysDcAreaZipMapper.updateSysDcAreaZip(currentRelation);

               SysDcAreaZip newRelation = HutoolBeanUtils.toBean(currentRelation, SysDcAreaZip.class);
               newRelation.setDcAreaZipId(null);
               newRelation.setCreateTime(nowDate);
               newRelation.setStartDate(nowDate);
               newRelation.setEndDate(endOfYearDate);
               newRelation.setDcId(reqVO.getDcId());
               sysDcAreaZipMapper.insert(newRelation);
            }
        }
    }
}
