package com.zksr.system.convert.template;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysSmsTemplate;
import com.zksr.system.controller.sms.vo.SysSmsTemplateRespVO;
import com.zksr.system.controller.sms.vo.SysSmsTemplateSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 短信模版配置 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-30
*/
@Mapper
public interface SysSmsTemplateConvert {

    SysSmsTemplateConvert INSTANCE = Mappers.getMapper(SysSmsTemplateConvert.class);

    SysSmsTemplateRespVO convert(SysSmsTemplate sysSmsTemplate);

    SysSmsTemplate convert(SysSmsTemplateSaveReqVO sysSmsTemplateSaveReq);

    PageResult<SysSmsTemplateRespVO> convertPage(PageResult<SysSmsTemplate> sysSmsTemplatePage);
}