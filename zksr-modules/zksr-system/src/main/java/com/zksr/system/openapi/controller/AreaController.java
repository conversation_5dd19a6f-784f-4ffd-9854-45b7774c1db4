package com.zksr.system.openapi.controller;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.system.controller.area.vo.AreaListReqVO;
import com.zksr.system.controller.area.vo.SysAreaRespVO;
import com.zksr.system.service.ISysAreaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@RequestMapping("/openapi/area")
@Api(tags = "OPENAPI - 区域模块接口")
@Slf4j
public class AreaController {

    @Autowired
    private ISysAreaService sysAreaService;

    @ApiOperation("获取区域城市")
    @GetMapping("/getAreaList")
    @RequiresOpenapiLogin(abilityKey = "getAreaList")
    public CommonResult<List<SysAreaRespVO>> getAreaList(AreaListReqVO reqVO) {
        Long supplierId = OpenapiSecurityUtils.getLoginOpensource().getOpensourceDto().getMerchantId();
        reqVO.setSupplierId(supplierId);
        return success(HutoolBeanUtils.toBean(sysAreaService.getAreaList(reqVO), SysAreaRespVO.class));
    }
}
