package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.web.domain.BaseEntity;

import javax.print.attribute.standard.MediaSize;

/**
 * 平台商政策(由平台商设置)对象 sys_partner_policy
 *
 * <AUTHOR>
 * @date 2024-03-21
 */
@TableName(value = "sys_partner_policy")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysPartnerPolicy extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 平台商政策id */
    @TableId
    private Long partnerPolicyId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 配置名 */
    @Excel(name = "配置名")
    private String policyName;

    /** 配置键名 */
    @Excel(name = "配置键名")
    private String policyKey;

    /** 配置值 */
    @Excel(name = "配置值")
    private String policyValue;

    /** 配置类型（数据字典：sys_partner_policy_type） */
    @Excel(name = "配置类型", readConverterExp = "数=据字典：sys_partner_policy_type")
    private String policyType;

    /** 运营商ID*/
    @Excel(name = "运营商ID")
    private Long dcId;

    /** 入驻商ID*/
    @Excel(name = "入驻商ID")
    private Long supplierId;

    /** 区域城市ID*/
    @Excel(name = "区域城市ID")
    private Long areaId;


}
