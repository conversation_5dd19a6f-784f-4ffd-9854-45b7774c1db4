package com.zksr.system.controller.opensource;

import javax.validation.Valid;

import cn.hutool.core.collection.ListUtil;
import com.zksr.account.api.account.vo.AccAccountRespVO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.system.controller.opensource.vo.SysOpensourceQueryInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysOpensource;
import com.zksr.system.service.ISysOpensourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.opensource.vo.SysOpensourcePageReqVO;
import com.zksr.system.controller.opensource.vo.SysOpensourceSaveReqVO;
import com.zksr.system.controller.opensource.vo.SysOpensourceRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 开放资源Controller
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Api(tags = "管理后台 - 开放资源接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/opensource")
public class SysOpensourceController {
    @Autowired
    private ISysOpensourceService sysOpensourceService;

    /**
     * 新增开放资源
     */
    @ApiOperation(value = "新增开放资源", httpMethod = "POST", notes = "system:opensource:add")
    @RequiresPermissions("system:opensource:add")
    @Log(title = "开放资源", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysOpensourceSaveReqVO createReqVO) throws UnsupportedEncodingException {
        return success(sysOpensourceService.insertSysOpensource(createReqVO));
    }

    /**
     * 重置sourceSecret
     */
    @ApiOperation(value = "重置sourceSecret", httpMethod = "PUT", notes = "system:opensource:edit")
    @RequiresPermissions("system:opensource:edit")
    @Log(title = "开放资源", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysOpensourceSaveReqVO updateReqVO) throws UnsupportedEncodingException {
            sysOpensourceService.updateSysOpensource(updateReqVO);
        return success(true);
    }
//
//    /**
//     * 删除开放资源
//     */
//    @ApiOperation(value = "删除开放资源", httpMethod = "GET")
//    @RequiresPermissions("system:opensource:remove")
//    @Log(title = "开放资源", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{opensourceIds}")
//    public CommonResult<Boolean> remove(@PathVariable Long[] opensourceIds) {
//        sysOpensourceService.deleteSysOpensourceByOpensourceIds(opensourceIds);
//        return success(true);
//    }

    /**
     * 获取开放资源详细信息
     */
    @ApiOperation(value = "获取开放资源详细信息", httpMethod = HttpMethod.POST, notes = "system:opensource:query")
    @RequiresPermissions("system:opensource:query")
    @PostMapping(value = "/query")
    public CommonResult<SysOpensourceRespVO> query(@RequestBody SysOpensourceQueryInfo opensourceQueryInfo) {
        SysOpensource sysOpensource = sysOpensourceService.getAccountByMerchantIdAndTypeByDefPlatform(opensourceQueryInfo.getMerchantId(), opensourceQueryInfo.getMerchantType());
        return success(HutoolBeanUtils.toBean(sysOpensource, SysOpensourceRespVO.class));
    }

    /**
     * 获取开放资源详细信息
     */
    @ApiOperation(value = "获得开放资源详情", httpMethod = "GET", notes = "system:opensource:query")
    @RequiresPermissions("system:opensource:query")
    @GetMapping(value = "/{opensourceId}")
    public CommonResult<SysOpensourceRespVO> getInfo(@PathVariable("opensourceId") Long opensourceId) {
        SysOpensource sysOpensource = sysOpensourceService.getSysOpensource(opensourceId);
        return success(HutoolBeanUtils.toBean(sysOpensource, SysOpensourceRespVO.class));
    }

//    /**
//     * 分页查询开放资源
//     */
//    @GetMapping("/list")
//    @ApiOperation(value = "获得开放资源分页列表", httpMethod = "GET")
//    @RequiresPermissions("system:opensource:list")
//    public CommonResult<PageResult<SysOpensourceRespVO>> getPage(@Valid SysOpensourcePageReqVO pageReqVO) {
//        PageResult<SysOpensource> pageResult = sysOpensourceService.getSysOpensourcePage(pageReqVO);
//        return success(HutoolBeanUtils.toBean(pageResult, SysOpensourceRespVO.class));
//    }

    /**
     * 修改ERP资源配置
     */
    @ApiOperation(value = "修改ERP资源配置", httpMethod = "PUT", notes = "system:opensource:edit")
    @RequiresPermissions("system:opensource:edit")
    @Log(title = "ERP资源配置", businessType = BusinessType.UPDATE)
    @PutMapping("/editErpSetting")
    public CommonResult<Boolean> editErpSetting(@Valid @RequestBody SysOpensourceSaveReqVO updateReqVO) throws UnsupportedEncodingException {
        sysOpensourceService.updateSysERPOpensource(updateReqVO);
        return success(true);
    }

}
