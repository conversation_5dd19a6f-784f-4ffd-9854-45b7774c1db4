package com.zksr.system.openapi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.rateLimit.RateLimitConfig;
import com.zksr.common.security.service.OpenapiTokenService;
import com.zksr.common.security.utils.OpenapiSecurityUtils;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpenlimitDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.openapi.controller.vo.GetTokenRequest;
import com.zksr.system.openapi.controller.vo.GetTokenResponse;
import com.zksr.system.openapi.service.ITokenService;
import com.zksr.system.openapi.service.OpenapiRateLimitInit;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysInterfaceLogService;
import com.zksr.system.service.ISysOpensourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

import static com.zksr.common.core.constant.OpenApiConstants.*;

@Service
@Slf4j
public class TokenServiceImpl implements ITokenService {

    @Autowired
    private OpenapiTokenService openapiTokenService;
    @Resource
    private OpensourceApi opensourceApi;
    @Resource
    private OpenapiRateLimitInit openapiRateLimitInit;

    @Autowired
    private ISysOpensourceService sysOpensourceService;

    @Autowired
    private ISysInterfaceLogService sysInterfaceLogService;

    @Autowired
    private ISysCacheService sysCacheService;


    //!@token  - 获取
    @Override
    public GetTokenResponse getToken(GetTokenRequest request) {
        /** 根据微信号登录 */
        //OpensourceDto opensourceDto = opensourceApi.getInfoByOpensouceIdAndSourceSecret(request.getOpensourceId(), request.getSourceSecret()).getCheckedData();
        LoginOpensource loginOpensource = opensourceApi.getOpensourceInfo(request.getSourceKey(), request.getSourceSecret()).getCheckedData();

        if (ToolUtil.isEmpty(loginOpensource)) {
            throw new ServiceException("无效的sourceId");
        }
        Map<String, Object> tokenMap = openapiTokenService.createToken(loginOpensource);
        String newTokenUuid = (String) tokenMap.get("token");
        String token = (String) tokenMap.get("access_token");
        Long expiresIn = (Long) tokenMap.get("expires_in");

        if (ToolUtil.isNotEmpty(loginOpensource.getOpensourceDto())) {
            opensourceApi.updateTokenUuid(loginOpensource.getOpensourceDto().getOpensourceId(), newTokenUuid);
        }

        //构建限流对象
        List<RateLimitConfig> configs = new ArrayList<>();
        Set<OpenlimitDto> openlimitList = loginOpensource.getOpenlimitList();
        if (ToolUtil.isNotEmpty(openlimitList)) {
            for (OpenlimitDto openlimitDto : openlimitList) {
                RateLimitConfig config = new RateLimitConfig();
                config.setAbilityKey(openlimitDto.getAbilityKey());
                String rateLimiterKey = StrUtil.format("openapilimiters:{}:{}", loginOpensource.getOpensourceId(), openlimitDto.getAbilityKey());
                config.setRateLimiterKey(rateLimiterKey);
                config.setSize(openlimitDto.getRateLimit());
                configs.add(config);
            }
            openapiRateLimitInit.redisLimitConfig(configs);
        }

        GetTokenResponse response = new GetTokenResponse(token, expiresIn);
        return response;
    }

    @Override
    public SysInterfaceLog getOpenapiInitLog(Integer requestType, String operationType, String reqId) {
        SysInterfaceLog logVO = null;

        //如果是根据reqID为唯一标识
        if (ObjectUtil.isNotNull(reqId)) {
            logVO = sysInterfaceLogService.getSysInterfaceLogByReqId(reqId);
        }

        //校验
        if (ToolUtil.isEmpty(logVO)) {
            logVO = new SysInterfaceLog();
            //获取入驻商编号
            Long supplierId = OpenapiSecurityUtils.getLoginOpensource().getOpensourceDto().getMerchantId();

            //获取系统编码
            Long syscode = OpenapiSecurityUtils.getLoginOpensource().getOpensourceDto().getSysCode();

            //获取开发能力ID
            Long opensourceId = OpenapiSecurityUtils.getLoginOpensource().getOpensourceDto().getOpensourceId();

            //设置系统来源 如果获取不到系统来源 默认B2B
            Integer source = SyncSourceType.B2B.getCode();

            //从缓存中获取入驻商开放配置、可视化配置
            VisualSettingMasterDto visualMaster = sysCacheService.getVisualMasterBySupplierId(supplierId);
            //设置系统来源
            if (Objects.nonNull(visualMaster) && Objects.nonNull(visualMaster.getSourceType())) {
                source = visualMaster.getSourceType();
            }

            //设置开发能力ID
            logVO.setOpensourceId(opensourceId);

            logVO.setSysCode(syscode);
//        logVO.setReqId(reqId);
            logVO.setSource(source);
            logVO.setReceive(SyncSourceType.B2B.getCode());
//        logVO.setBizData(data);
//        logVO.setReqTime(new Date());
            logVO.setStatus(LOG_STATUS_WAIT);
            logVO.setRequestType(requestType);
            logVO.setSupplierId(supplierId);
            logVO.setOperationType(operationType);
            logVO.setLogType(LOG_TYPE_RECEIVE);
            logVO.setReqTime(new Date());
            //异步处理的接收接口  所以默认接收完成
            logVO.setReqStatus(REQ_STATUS_2);
            logVO.setMessage(RECEIVE_SUCCESS);
//        logVO.setReqData(data);
        } else {
            //如果是根据reqId 获取日志信息 能查询到时 说明已经异步处理过的 重新执行需要通过幂等校验
            // 则查询到信息后更改日志状态、（后续更新请求参数）  异步重新处理
            logVO.setStatus(LOG_STATUS_WAIT);
        }

        return logVO;
    }

    @Override
    public boolean checkReceiveLogBefore(String reqId) {
        SysInterfaceLog sysInterfaceLog = sysInterfaceLogService.getSysInterfaceLogByReqId(reqId);
        return ToolUtil.isNotEmpty(sysInterfaceLog) && !LOG_STATUS_FAIN.equals(sysInterfaceLog.getStatus());
    }

    @Override
    public boolean checkReceiveLogAfter(SysInterfaceLog interfaceLog) {

        //校验  日志信息不存在或 处理异常的接口信息  不继续执行
        if (ObjectUtil.isNull(interfaceLog) || LOG_STATUS_ERR.equals(interfaceLog.getStatus())) {
            return false;
        }

        return true;
    }
}
