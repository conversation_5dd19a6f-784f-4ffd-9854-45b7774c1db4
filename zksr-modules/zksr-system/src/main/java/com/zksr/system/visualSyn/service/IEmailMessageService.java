package com.zksr.system.visualSyn.service;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.EmailMessage.dto.SyncEmailData;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportData;

/**
* 发送邮件
* @date 2025/2/16 17:47
* <AUTHOR>
*/
public interface IEmailMessageService {

    /**
     * 推送第三方数据 响应异常 发送邮件通知
     * @param data
     * @param opensourceDto
     * @param emailData
     */

    void sendSyncEmail(SyncDataDTO data, OpensourceDto opensourceDto, SyncEmailData emailData);

    /**
     * 发送同步日志日同步报告
     * @param emailReportData
     */
    void sendDaySyncReportDataEmail(SyncEmailReportData emailReportData);

}
