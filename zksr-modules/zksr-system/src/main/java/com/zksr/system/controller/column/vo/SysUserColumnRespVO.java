package com.zksr.system.controller.column.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 用户头列配置对象 sys_user_column
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
@Data
@ApiModel("用户头列配置 - sys_user_column Response VO")
public class SysUserColumnRespVO {
    private static final long serialVersionUID = 1L;

    /** 用户表头列配置id */
    @ApiModelProperty(value = "用户表头列配置id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long userColumnId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 表code */
    @Excel(name = "表code")
    @ApiModelProperty(value = "表code")
    private String tableCode;

    /** 列key */
    @Excel(name = "列key")
    @ApiModelProperty(value = "列key")
    private String filedKey;

    /** 名称 */
    @Excel(name = "名称")
    @ApiModelProperty(value = "名称")
    private String filedName;

    /** 显示 */
    @Excel(name = "显示")
    @ApiModelProperty(value = "显示")
    private Integer visibleFlag;

    /** 是否导出显示列 */
    @ApiModelProperty(value = "显示")
    private Integer exportVisibelFlag;

    /** 固定 */
    @Excel(name = "固定")
    @ApiModelProperty(value = "固定")
    private Integer fixedFlag;

    /** 排序 */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 对齐方式 （left，center，right）
     */
    @ApiModelProperty(value = "对齐方式 （left，center，right）")
    private String align;

    /**
     * 列宽度
     */
    @ApiModelProperty(value = "列宽度")
    private Integer width;

    /**
     * 列最小宽度
     */
    @ApiModelProperty(value = "列最小宽度")
    private Integer minWidth;

    /**
     * 是否使用插槽
     */
    @ApiModelProperty(value = "是否使用插槽")
    private String useSlot;

    /**
     * 是否隐藏多行
     */
    @ApiModelProperty(value = "是否隐藏多行")
    private String overflow;

}
