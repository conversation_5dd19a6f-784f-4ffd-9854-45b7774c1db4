package com.zksr.system.controller.visualSetting.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 可视化配置详情对象 visual_setting_detail
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Data
@ApiModel("可视化配置详情 - visual_setting_detail分页 Request VO")
public class VisualSettingDetailSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(value = "备注")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long visualDetailId;

    /** 状态（0 停用  1启用） */
    @Excel(name = "状态", readConverterExp = "0=,停=用,1=启用")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 主表ID */
    @Excel(name = "主表ID")
    @ApiModelProperty(value = "主表ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long visualMasterId;

    /** 接口模板类型ID */
    @Excel(name = "接口模板类型ID")
    @ApiModelProperty(value = "接口模板类型ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long visualTemplateId;

    /** 接口模板类型 */
    @Excel(name = "接口模板类型")
    @ApiModelProperty(value = "接口模板类型")
    private Long templateType;

    /** 请求类型（visual_req_type） */
    @Excel(name = "请求类型", readConverterExp = "v=isual_req_type")
    @ApiModelProperty(value = "请求类型")
    private String reqType;

    /** 接口地址 */
    @Excel(name = "接口地址")
    @ApiModelProperty(value = "接口地址")
    private String apiUrl;

    /** 数据类型（visual_content_type） */
    @Excel(name = "数据类型", readConverterExp = "v=isual_content_type")
    @ApiModelProperty(value = "数据类型")
    private String contentType;

    /** 响应参数名称例如：code */
    @Excel(name = "响应参数名称例如：code")
    @ApiModelProperty(value = "响应参数名称例如：code")
    private String respName;

    /** 响应参数值例如：200/success */
    @Excel(name = "响应参数值例如：200/success")
    @ApiModelProperty(value = "响应参数值例如：200/success")
    private String respCode;

    /**
     * 响应数据
     */
    @Excel(name = "响应数据")
    private String respData;

    /**
     * 响应消息
     */
    @Excel(name = "响应消息")
    private String respMsg;

    /** 调试状态（0 失败  1 成功  2其他） */
    @Excel(name = "调试状态", readConverterExp = "0=,失=败,1=,成=功,2=其他")
    @ApiModelProperty(value = "调试状态")
    private Integer debugResultStatus;

    /** 调试结果 */
    @Excel(name = "调试结果")
    @ApiModelProperty(value = "调试结果")
    private String debugResultMessage;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 可视化推送收款单类型 对应枚举/数据字典: visualReceiptType*/
    @Excel(name = "可视化推送收款单类型")
    private String visualReceiptType;

}
