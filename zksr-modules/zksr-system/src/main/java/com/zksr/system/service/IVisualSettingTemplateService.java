package com.zksr.system.service;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplatePageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingTemplateSaveReqVO;
import com.zksr.system.domain.VisualSettingTemplate;

import javax.validation.Valid;

/**
 * 可视化接口模板Service接口
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
public interface IVisualSettingTemplateService {

    /**
     * 新增可视化接口模板
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertVisualSettingTemplate(@Valid VisualSettingTemplateSaveReqVO createReqVO);

    /**
     * 修改可视化接口模板
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateVisualSettingTemplate(@Valid VisualSettingTemplateSaveReqVO updateReqVO);

    /**
     * 删除可视化接口模板
     *
     * @param visualTemplateId id
     */
    public void deleteVisualSettingTemplate(Long visualTemplateId);

    /**
     * 批量删除可视化接口模板
     *
     * @param visualTemplateIds 需要删除的可视化接口模板主键集合
     * @return 结果
     */
    public void deleteVisualSettingTemplateByVisualTemplateIds(Long[] visualTemplateIds);

    /**
     * 获得可视化接口模板
     *
     * @param visualTemplateId id
     * @return 可视化接口模板
     */
    public VisualSettingTemplate getVisualSettingTemplate(Long visualTemplateId);

    /**
     * 获得可视化接口模板分页
     *
     * @param pageReqVO 分页查询
     * @return 可视化接口模板分页
     */
    PageResult<VisualSettingTemplate> getVisualSettingTemplatePage(VisualSettingTemplatePageReqVO pageReqVO);

}
