package com.zksr.system.convert.supplier;

import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRateDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.controller.supplier.vo.SysSupplierAccountPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.domain.SysSupplier;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 入驻商转换器
 * @date 2024/4/9 10:37
 */
@Mapper
public interface SupplierConvert {

    SupplierConvert INSTANCE = Mappers.getMapper(SupplierConvert.class);

    @Mappings({
            @Mapping(source = "categoryIds", target = "catgoryIds"),
            @Mapping(source = "areaIds", target = "areaIds"),
            @Mapping(source = "supplierClassRateDTOS", target = "supplierClassRateDTOS")
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSetRespVO(
            @MappingTarget SysSupplierRespVO sysSupplierRespVO,
            List<Long> categoryIds,
            List<Long> areaIds,
            List<PrdtSupplierClassRateDTO> supplierClassRateDTOS
    );

    PageResult<SysSupplierAccountPageReqVO> convert(PageResult<SysSupplierRespVO> supplierPage);

    @Mappings({
            @Mapping(source = "account.platform", target = "payPlatform"),
            @Mapping(source = "account.accountId", target = "accountId"),
            @Mapping(source = "account.withdrawableAmt", target = "withdrawableAmt"),
            @Mapping(source = "account.frozenAmt", target = "frozenAmt"),
            @Mapping(source = "account.creditAmt", target = "creditAmt")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget SysSupplierAccountPageReqVO supplier, AccAccountDTO account);

    SupplierDTO convertDTO(SysSupplier sysSupplier);
}
