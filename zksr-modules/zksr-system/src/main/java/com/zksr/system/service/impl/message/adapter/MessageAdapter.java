package com.zksr.system.service.impl.message.adapter;

import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.common.third.message.dto.CommonMessageRespVO;
import com.zksr.system.api.commonMessage.dto.MessageCertificateTable;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/6 9:50
 */
public interface MessageAdapter {

    /**
     * 发送消息
     * @param messageList               消息数据
     * @param messageTemplate           消息模版
     * @param messageCertificateTable   接受者凭证信息
     */
    List<CommonMessageRespVO> sendTo(List<CommonMessageDTO> messageList, MessageTemplateDTO messageTemplate, MessageCertificateTable messageCertificateTable);


    /**
     * 失败消息
     * @param messageList   消息列表
     * @param msg           失败原因
     * @return  失败结果
     */
    default List<CommonMessageRespVO> fail(List<CommonMessageDTO> messageList, String msg) {
        return messageList
                .stream()
                .map(messageDTO -> CommonMessageRespVO.builder()
                        .commonMessage(messageDTO)
                        .success(false)
                        .msg(msg)
                        .build()
                ).collect(Collectors.toList());
    }
}
