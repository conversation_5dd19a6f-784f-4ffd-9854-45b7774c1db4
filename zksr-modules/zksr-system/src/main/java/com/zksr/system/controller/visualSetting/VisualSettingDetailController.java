package com.zksr.system.controller.visualSetting;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailPageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingDetailSaveReqVO;
import com.zksr.system.convert.visualSetting.visualSettingDetail.VisualSettingDetailConvert;
import com.zksr.system.domain.VisualSettingDetail;
import com.zksr.system.service.IVisualSettingDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 可视化配置详情Controller
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Api(tags = "管理后台 - 可视化配置详情接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/visualapi/detail")
public class VisualSettingDetailController {
    @Autowired
    private IVisualSettingDetailService visualSettingDetailService;

    /**
     * 新增可视化配置详情
     */
    @ApiOperation(value = "新增可视化配置详情", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "可视化配置详情", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody VisualSettingDetailSaveReqVO createReqVO) {
        return success(visualSettingDetailService.insertVisualSettingDetail(createReqVO));
    }

    /**
     * 修改可视化配置详情
     */
    @ApiOperation(value = "修改可视化配置详情", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "可视化配置详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody VisualSettingDetailSaveReqVO updateReqVO) {
        visualSettingDetailService.updateVisualSettingDetail(updateReqVO);
        return success(true);
    }

    /**
     * 删除可视化配置详情
     */
    @ApiOperation(value = "删除可视化配置详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "可视化配置详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{visualDetailIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] visualDetailIds) {
        visualSettingDetailService.deleteVisualSettingDetailByVisualDetailIds(visualDetailIds);
        return success(true);
    }

    /**
     * 获取可视化配置详情详细信息
     */
    @ApiOperation(value = "获得可视化配置详情详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{visualDetailId}")
    public CommonResult<VisualSettingDetailRespVO> getInfo(@PathVariable("visualDetailId") Long visualDetailId) {
        return success(HutoolBeanUtils.toBean(visualSettingDetailService.getVisualSettingDetailAndTemplate(visualDetailId), VisualSettingDetailRespVO.class));
    }

    /**
     * 分页查询可视化配置详情
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得可视化配置详情分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public R getPage(@Valid VisualSettingDetailPageReqVO pageReqVO) {
        PageUtils.startPage();
        List<VisualSettingDetailRespVO> list = visualSettingDetailService.list(pageReqVO);
        return R.tableData(list);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:visualSettingDetail:add";
        /** 编辑 */
        public static final String EDIT = "system:visualSettingDetail:edit";
        /** 删除 */
        public static final String DELETE = "system:visualSettingDetail:remove";
        /** 列表 */
        public static final String LIST = "system:visualSettingDetail:list";
        /** 查询 */
        public static final String GET = "system:visualSettingDetail:query";
        /** 停用 */
        public static final String DISABLE = "system:visualSettingDetail:disable";
        /** 启用 */
        public static final String ENABLE = "system:visualSettingDetail:enable";
    }
}
