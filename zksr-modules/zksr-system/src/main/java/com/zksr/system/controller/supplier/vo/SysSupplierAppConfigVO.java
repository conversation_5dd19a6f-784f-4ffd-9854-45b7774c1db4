package com.zksr.system.controller.supplier.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 入驻商APP配置
 * @date 2024/7/18 11:42
 */
@Data
@ApiModel(description = "入驻商APP配置")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SysSupplierAppConfigVO {

    @ApiModelProperty("小程序公众号appId")
    private String publishAppId;

    @ApiModelProperty("公众号名称")
    private String publishAccountName;
}
