package com.zksr.system.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 省市区对象 sys_area_city
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@TableName(value = "sys_area_city")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysAreaCity extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaCityId;

    /** 父城市区域 */
    @Excel(name = "父城市区域")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pid;

    /** 级别: 0 省份, 1 城市, 2 区域 */
    @Excel(name = "级别: 0 省份, 1 城市, 2 区域")
    private Integer deep;

    /** 编码 */
    @Excel(name = "编码")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long extId;

    /** 城市区域名称 */
    @Excel(name = "城市区域名称")
    private String name;

    @ApiModelProperty(value = "SaaS是否可用:0是启用/1是停用")
    private Integer saasEnable;
}
