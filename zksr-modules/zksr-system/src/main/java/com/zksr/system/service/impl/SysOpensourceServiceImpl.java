package com.zksr.system.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.enums.request.OpensourceMerchantType;
import com.zksr.common.core.enums.request.VisualTemplateType;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.Md5_Sign;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.cert.Base64;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.LoginOpensource;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.domain.VisualSettingMaster;
import com.zksr.system.enums.ErrorCodeConstants;
import com.zksr.system.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import com.zksr.system.domain.SysOpensource;
import com.zksr.system.controller.opensource.vo.SysOpensourcePageReqVO;
import com.zksr.system.controller.opensource.vo.SysOpensourceSaveReqVO;
import com.zksr.system.service.ISysOpensourceService;

import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 开放能力Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class SysOpensourceServiceImpl implements ISysOpensourceService {
    @Autowired
    private SysOpensourceMapper sysOpensourceMapper;

    @Autowired
    private SysOpenabilityMapper sysOpenabilityMapper;

    @Autowired
    private Cache<Long, OpensourceDto> opensourceDtoByMerchantIdCache;

    @Autowired
    private SysSupplierMapper sysSupplierMapper;

    @Autowired
    @Qualifier("visualSettingMasterBySupplierIdCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterBySupplierIdCache;

    @Autowired
    @Qualifier("visualSettingDetailBySupplierIdCache")
    private Cache<String, VisualSettingDetailDto> visualSettingDetailBySupplierIdCache;

    @Autowired
    private VisualSettingMasterMapper visualSettingMasterMapper;

    @Autowired
    private VisualSettingDetailMapper visualSettingDetailMapper;

    /**
     * 新增开放能力
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertSysOpensource(SysOpensourceSaveReqVO createReqVO) throws UnsupportedEncodingException {
        Long merchantId = createReqVO.getMerchantId();

        // 插入
        SysOpensource sysOpensource = HutoolBeanUtils.toBean(createReqVO, SysOpensource.class);
        String sourceSecret = generateSecret(merchantId, createReqVO.getMerchantType());
        sysOpensource.setSourceKey(IdUtils.simpleUUID());
        sysOpensource.setSourceSecret(sourceSecret);

        //如果是统配入驻商 设置对应权限
        if(OPEN_FLAG_1.equals(createReqVO.getSuperSupplier())
                && createReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            createReqVO.setMerchantType(OpensourceMerchantType.SUPER_SUPPLIER_AUTH.getType());
        }else if(OPEN_FLAG_0.equals(createReqVO.getSuperSupplier())
                && createReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            createReqVO.setMerchantType(OpensourceMerchantType.SUPPLIER.getType());
        }
        //如果开启了货到付款清账功能 设置对应的权限
        if(OPEN_FLAG_1.equals(createReqVO.getIsHdfkSettle()) && createReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            createReqVO.setMerchantType(OpensourceMerchantType.SUPPLIER_HDFK_PAY.getType());
        }else if(OPEN_FLAG_0.equals(createReqVO.getIsHdfkSettle()) && createReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            createReqVO.setMerchantType(OpensourceMerchantType.SUPPLIER.getType());
        }

        //插入前要做校验
        if(ToolUtil.isNotEmpty(this.getAccountByMerchantIdAndTypeByDefPlatform(merchantId, createReqVO.getMerchantType()))){
            throw exception(SYS_OPENSOURCE_NOT_MULTIPLE);
        }

        //如果是入驻商配置  需要设置平台商id
        if(createReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            SysSupplier sysSupplier = sysSupplierMapper.selectById(merchantId);
            if(ToolUtil.isNotEmpty(sysSupplier)) sysOpensource.setSysCode(sysSupplier.getSysCode());
        }

        sysOpensourceMapper.insert(sysOpensource);

        //清除资源ID缓存 （merchantId）   因为如果获取缓存查不到时 会设置一个空对象给缓存  所以新增后需要删除掉可能存在于缓存中的空对象
        opensourceDtoByMerchantIdCache.remove(merchantId);

        //清除资源ID对应的可视化主表缓存 因为如果获取缓存查不到时 会设置一个空对象给缓存  所以新增后需要删除掉可能存在于缓存中的空对象
        visualSettingMasterBySupplierIdCache.remove(merchantId);

        //清除资源ID对应的可视化详情缓存 因为如果获取缓存查不到时 会设置一个空对象给缓存  所以新增后需要删除掉可能存在于缓存中的空对象
        removeAllVisualDetailCache(merchantId);

        // 返回
        return sysOpensource.getOpensourceId();
    }

    private String generateSecret(Long merchantId, String merchantType) throws UnsupportedEncodingException {
        String randomStr = RandomUtil.randomString(16);
        String md5Key = "zksr_openapi_20240304";
        String str = merchantId + merchantType + randomStr;
        String secret = Base64.encode(Md5_Sign.SignByMD5(str, md5Key));
        return secret;
    }

    /**
     * 修改开放能力
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysOpensource(SysOpensourceSaveReqVO updateReqVO) throws UnsupportedEncodingException {
        SysOpensource sysOpensource = sysOpensourceMapper.selectById(updateReqVO.getOpensourceId());
        validateSysOpensourceExists(updateReqVO.getOpensourceId());
        String sourceSecret = generateSecret(sysOpensource.getMerchantId(), sysOpensource.getMerchantType());
        sysOpensource.setSourceSecret(sourceSecret);
        sysOpensourceMapper.updateById(HutoolBeanUtils.toBean(sysOpensource, SysOpensource.class));

        //清除资源ID缓存 （merchantId）
        opensourceDtoByMerchantIdCache.remove(sysOpensource.getMerchantId());

        //清除资源ID对应的可视化主表缓存
        visualSettingMasterBySupplierIdCache.remove(sysOpensource.getMerchantId());

        //清除资源ID对应的可视化详情缓存
        removeAllVisualDetailCache(sysOpensource.getMerchantId());
    }

    /**
     * 删除开放能力
     *
     * @param opensourceId 开放能力
     */
    @Override
    public void deleteSysOpensource(Long opensourceId) {
        // 删除
        sysOpensourceMapper.deleteById(opensourceId);

        SysOpensource sysOpensource = sysOpensourceMapper.selectById(opensourceId);
        //清除资源ID缓存 （merchantId）
        opensourceDtoByMerchantIdCache.remove(sysOpensource.getMerchantId());

        //清除资源ID对应的可视化主表缓存
        visualSettingMasterBySupplierIdCache.remove(sysOpensource.getMerchantId());

        //清除资源ID对应的可视化详情缓存
        removeAllVisualDetailCache(sysOpensource.getMerchantId());
    }

    /**
     * 批量删除开放能力
     *
     * @param opensourceIds 需要删除的开放能力主键
     * @return 结果
     */
    @Override
    public void deleteSysOpensourceByOpensourceIds(Long[] opensourceIds) {
        for(Long opensourceId : opensourceIds){
            this.deleteSysOpensource(opensourceId);
        }
    }

    /**
     * 获得开放能力
     *
     * @param opensourceId 开放能力
     * @return 开放能力
     */
    @Override
    public SysOpensource getSysOpensource(Long opensourceId) {
        return sysOpensourceMapper.selectById(opensourceId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysOpensource> getSysOpensourcePage(SysOpensourcePageReqVO pageReqVO) {
        return sysOpensourceMapper.selectPage(pageReqVO);
    }

    @Override
    public SysOpensource getInfoByOpensouceIdAndSourceSecret(String opensourceId, String sourceSecret) {
        return sysOpensourceMapper.getInfoByOpensouceIdAndSourceSecret(opensourceId, sourceSecret);
    }

    @Override
    public LoginOpensource getOpensourceInfo(String sourceKey, String sourceSecret) {
        LoginOpensource loginOpensource = new LoginOpensource();
        if (sysOpensourceMapper.selectCount(SysOpensource::getSourceKey, sourceKey) > 0) {
            SysOpensource sysOpensource = sysOpensourceMapper.getInfoBySouceKeyAndSourceSecret(sourceKey, sourceSecret);
            if (sysOpensource != null) {
                loginOpensource.setSysCode(sysOpensource.getSysCode());

                // 角色集合
                Set<String> abilityKeys = sysOpenabilityMapper.getMerchantAbilityKeysByList(sysOpensource.getMerchantType().split(","));
                loginOpensource.setAbilityKeys(abilityKeys);
                loginOpensource.setOpenlimitList(sysOpenabilityMapper.getOpenlimitListByList(sysOpensource.getMerchantType().split(",")));
                loginOpensource.setOpensourceDto(HutoolBeanUtils.toBean(sysOpensource, OpensourceDto.class));
                return loginOpensource;
            }
        }

        return null;
    }

    @Override
    public SysOpensource getAccountByMerchantIdAndTypeByDefPlatform(Long merchantId, String merchantType) {
        return sysOpensourceMapper.getAccountByMerchantIdAndTypeByDefPlatform(merchantId, merchantType);
    }

    @Override
    public void updateSysERPOpensource(@Valid SysOpensourceSaveReqVO updateReqVO) throws UnsupportedEncodingException {
        if(Objects.isNull(updateReqVO.getOpensourceId())){
            throw exception(SYS_OPENSOURCE_SETTING_EXISTS);
        }

        //如果是统配入驻商 设置对应权限
        if(OPEN_FLAG_1.equals(updateReqVO.getSuperSupplier())
                && updateReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            updateReqVO.setMerchantType(OpensourceMerchantType.SUPER_SUPPLIER_AUTH.getType());
        }else if(OPEN_FLAG_0.equals(updateReqVO.getSuperSupplier())
                && updateReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            updateReqVO.setMerchantType(OpensourceMerchantType.SUPPLIER.getType());
        }

        //如果开启了货到付款清账功能 设置对应的权限
        if(OPEN_FLAG_1.equals(updateReqVO.getIsHdfkSettle()) && updateReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            updateReqVO.setMerchantType(OpensourceMerchantType.SUPPLIER_HDFK_PAY.getType());
        }else if(OPEN_FLAG_0.equals(updateReqVO.getIsHdfkSettle()) && updateReqVO.getMerchantType().contains(OpensourceMerchantType.SUPPLIER.getType())){
            updateReqVO.setMerchantType(OpensourceMerchantType.SUPPLIER.getType());
        }

        sysOpensourceMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, SysOpensource.class));

        //清除资源ID缓存 （merchantId）
        opensourceDtoByMerchantIdCache.remove(updateReqVO.getMerchantId());

        //清除资源ID对应的可视化主表缓存
        visualSettingMasterBySupplierIdCache.remove(updateReqVO.getMerchantId());

        //清除资源ID对应的可视化详情缓存
        removeAllVisualDetailCache(updateReqVO.getMerchantId());
    }

    @Override
    public OpensourceDto getOpensourceByMerchantId(Long merchantId) {
        return HutoolBeanUtils.toBean(sysOpensourceMapper.getOpensourceByMerchantId(merchantId), OpensourceDto.class);
    }

    @Override
    public VisualSettingMasterDto getVisualSettingMasterByMerchantId(Long merchantId) {
        //根据入驻商ID查询对应的开发能力信息
        SysOpensource sysOpensource = sysOpensourceMapper.getOpensourceByMerchantId(merchantId);
        if(Objects.nonNull(sysOpensource)){
            return HutoolBeanUtils.toBean(visualSettingMasterMapper.selectById(sysOpensource.getVisualMasterId()),VisualSettingMasterDto.class);
        }
        return null;
    }

    @Override
    public VisualSettingDetailDto getVisualSettingDetailByMerchantId(String key) {
        String[] split = key.split(StringPool.COLON);
        Long supplierId = Long.parseLong(split[0]);
        Long templateType = Long.parseLong(split[1]);
        //根据入驻商ID查询对应的开发能力信息
        SysOpensource sysOpensource = sysOpensourceMapper.getOpensourceByMerchantId(supplierId);
        if(Objects.nonNull(sysOpensource)){
            return HutoolBeanUtils.toBean(visualSettingDetailMapper.getVisualSettingDetailByMasterIdAndTemplateType(sysOpensource.getVisualMasterId(),templateType),VisualSettingDetailDto.class);
        }
        return null;
    }

    @Override
    public OpensourceDto insertOpensourceSupplierByApi(OpensourceDto opensourceDto) {
        //获取需要新增的入驻商ID
        Long merchantId = opensourceDto.getMerchantId();

        Long superMerchantId = opensourceDto.getSuperSupplierId();
        if(ToolUtil.isEmpty(merchantId) || ToolUtil.isEmpty(superMerchantId)){
            throw exception(SYS_SUPPLIER_SUPPLIER_ID_NOT_EXISTS);
        }


        SysOpensource sysOpensource = HutoolBeanUtils.toBean(opensourceDto, SysOpensource.class);

        //插入前要做校验
        if(ToolUtil.isNotEmpty(this.getAccountByMerchantIdAndTypeByDefPlatform(merchantId, opensourceDto.getMerchantType()))){
            throw exception(SYS_OPENSOURCE_NOT_MULTIPLE);
        }

        //设置默认参数
        sysOpensource.setMerchantType(OpensourceMerchantType.SUPPLIER.getType()).setSuperSupplier(OPEN_FLAG_0);

        //根据接口调用的统配入驻商ID来获取入驻商开发能力公用配置
        SysOpensource superOpensoucre = sysOpensourceMapper.getOpensourceByMerchantId(superMerchantId);
        if(ToolUtil.isEmpty(superOpensoucre)){
            throw exception(SYS_OPENSOURCE_SUPER_SUPPLIER_EXISTS);
        }
        //设置统配入驻商的公共开放配置给新入驻商
        sysOpensource.setSendUrl(superOpensoucre.getSendUrl())
                .setVisualMasterId(superOpensoucre.getVisualMasterId())
                .setLogisticsInfo(superOpensoucre.getLogisticsInfo())
                .setOrderAutoPush(superOpensoucre.getOrderAutoPush())
                .setOrderDelayPushTime(superOpensoucre.getOrderDelayPushTime())
                .setSysCode(superOpensoucre.getSysCode())
                .setSyncMarkPrice(superOpensoucre.getSyncMarkPrice())
                .setSyncCostPrice(superOpensoucre.getSyncCostPrice())
                .setIpWhiteList(superOpensoucre.getIpWhiteList());

        //设置publicKey和sendUrl  如果没有传 则取统配入驻商  统配入驻商没配置则取可视化主表
        if(ToolUtil.isEmpty(sysOpensource.getPublicKey()) || ToolUtil.isEmpty(sysOpensource.getSendUrl())){
                //获取可视化信息
            VisualSettingMaster visualMaster = visualSettingMasterMapper.selectById(superOpensoucre.getVisualMasterId());
            sysOpensource.setPublicKey(ToolUtil.isEmpty(superOpensoucre.getPublicKey()) ? visualMaster.getPublicKey() : superOpensoucre.getPublicKey())
                        .setSendUrl(ToolUtil.isEmpty(superOpensoucre.getSendUrl()) ? visualMaster.getSendUrl() : superOpensoucre.getSendUrl());

        }


         // 插入
        sysOpensourceMapper.insert(sysOpensource);

        //清除资源ID缓存 （merchantId）   因为如果获取缓存查不到时 会设置一个空对象给缓存  所以新增后需要删除掉可能存在于缓存中的空对象
        opensourceDtoByMerchantIdCache.remove(merchantId);

        //清除资源ID对应的可视化主表缓存 因为如果获取缓存查不到时 会设置一个空对象给缓存  所以新增后需要删除掉可能存在于缓存中的空对象
        visualSettingMasterBySupplierIdCache.remove(merchantId);

        //清除资源ID对应的可视化详情缓存 因为如果获取缓存查不到时 会设置一个空对象给缓存  所以新增后需要删除掉可能存在于缓存中的空对象
        removeAllVisualDetailCache(merchantId);

        // 返回
        return HutoolBeanUtils.toBean(sysOpensource, OpensourceDto.class);
    }

    @Override
    public List<OpensourceDto> getOpenSourceBySysCode(Long sysCode) {
        return HutoolBeanUtils.toBean(sysOpensourceMapper.getOpenSourceBySysCode(sysCode), OpensourceDto.class);
    }

    @Override
    public void updateTokenUuid(Long opensourceId, String token) {
        SysOpensource sysOpensource = new SysOpensource();
        sysOpensource.setOpensourceId(opensourceId);
        sysOpensource.setToken(token);
        sysOpensourceMapper.updateById(sysOpensource);
    }

    private void validateSysOpensourceExists(Long opensourceId) {
        if (sysOpensourceMapper.selectById(opensourceId) == null) {
            throw exception(SYS_OPENSOURCE_NOT_EXISTS);
        }
    }


    /**
     * 清除key为入驻商 的可视化详情模板
     * @param merchantId
     */
    public void removeAllVisualDetailCache(Long merchantId){
        for (VisualTemplateType value : VisualTemplateType.values()) {
            visualSettingDetailBySupplierIdCache.remove(merchantId + StringPool.COLON + value.getType());
        }
    }



    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 开放能力 TODO 补充编号 ==========
     ErrorCode SYS_OPENSOURCE_NOT_EXISTS = ErrorCodeConstants.SYS_OPENSOURCE_NOT_EXISTS;
    ErrorCode SYS_OPENSOURCE_NOT_MULTIPLE = ErrorCodeConstants.SYS_OPENSOURCE_NOT_MULTIPLE;


}
