package com.zksr.system.controller.log;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.service.ISysInterfaceLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.log.vo.SysInterfaceLogPageReqVO;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.controller.log.vo.SysInterfaceLogRespVO;
import com.zksr.system.convert.log.SysInterfaceLogConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 同步接口日志Controller
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
@Api(tags = "管理后台 - 同步接口日志接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/log")
public class SysInterfaceLogController {
    @Autowired
    private ISysInterfaceLogService sysInterfaceLogService;

    /**
     * 新增同步接口日志
     */
    @ApiOperation(value = "新增同步接口日志", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "同步接口日志", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysInterfaceLogSaveReqVO createReqVO) {
        return success(sysInterfaceLogService.insertSysInterfaceLog(createReqVO));
    }

    /**
     * 修改同步接口日志
     */
    @ApiOperation(value = "修改同步接口日志", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "同步接口日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysInterfaceLogSaveReqVO updateReqVO) {
            sysInterfaceLogService.updateSysInterfaceLog(updateReqVO);
        return success(true);
    }

    /**
     * 删除同步接口日志
     */
    @ApiOperation(value = "删除同步接口日志", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "同步接口日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public CommonResult<Boolean> remove(@PathVariable Long[] ids) {
        sysInterfaceLogService.deleteSysInterfaceLogByIds(ids);
        return success(true);
    }

    /**
     * 获取同步接口日志详细信息
     */
    @ApiOperation(value = "获得同步接口日志详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{id}")
    public CommonResult<SysInterfaceLogRespVO> getInfo(@PathVariable("id") Long id) {
        SysInterfaceLog sysInterfaceLog = sysInterfaceLogService.getSysInterfaceLog(id);
        return success(SysInterfaceLogConvert.INSTANCE.convert(sysInterfaceLog));
    }

    /**
     * 分页查询同步接口日志
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得同步接口日志分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysInterfaceLogPageReqVO>> getPage(@Valid SysInterfaceLogPageReqVO pageReqVO) {
        return success(sysInterfaceLogService.getSysInterfaceLogPage(pageReqVO));
    }


    @ApiOperation(value = "消息重发", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.MAGRETRY)
    @RequiresPermissions(Permissions.MAGRETRY)
    @PostMapping(value = "/messageRetry")
    public CommonResult<Boolean> messageRetry(@RequestBody List<String> reqIds) {
        reqIds.forEach(reqId -> {
            sysInterfaceLogService.messageRetry(reqId);
        });
        return success(true);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:log:add";
        /** 编辑 */
        public static final String EDIT = "system:log:edit";
        /** 删除 */
        public static final String DELETE = "system:log:remove";
        /** 列表 */
        public static final String LIST = "system:log:list";
        /** 查询 */
        public static final String GET = "system:log:query";
        /** 停用 */
        public static final String DISABLE = "system:log:disable";
        /** 启用 */
        public static final String ENABLE = "system:log:enable";
        /** 消息重发 */
        public static final String MAGRETRY = "system:log:magRetry";
    }
}
