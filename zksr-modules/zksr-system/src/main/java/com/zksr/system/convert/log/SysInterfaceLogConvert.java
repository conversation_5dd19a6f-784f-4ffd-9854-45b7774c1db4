package com.zksr.system.convert.log;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.controller.log.vo.SysInterfaceLogRespVO;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 同步接口日志 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-05-31
*/
@Mapper
public interface SysInterfaceLogConvert {

    SysInterfaceLogConvert INSTANCE = Mappers.getMapper(SysInterfaceLogConvert.class);

    SysInterfaceLogRespVO convert(SysInterfaceLog sysInterfaceLog);

    SysInterfaceLog convert(SysInterfaceLogSaveReqVO sysInterfaceLogSaveReq);

    PageResult<SysInterfaceLogRespVO> convertPage(PageResult<SysInterfaceLog> sysInterfaceLogPage);
}