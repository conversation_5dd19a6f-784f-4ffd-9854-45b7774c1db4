package com.zksr.system.convert.pageConfig;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysPagesConfigTemplate;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplateRespVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplateSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 平台页面配置模版 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-11-06
*/
@Mapper
public interface SysPagesConfigTemplateConvert {

    SysPagesConfigTemplateConvert INSTANCE = Mappers.getMapper(SysPagesConfigTemplateConvert.class);

    SysPagesConfigTemplateRespVO convert(SysPagesConfigTemplate sysPagesConfigTemplate);

    SysPagesConfigTemplate convert(SysPagesConfigTemplateSaveReqVO sysPagesConfigTemplateSaveReq);

    PageResult<SysPagesConfigTemplateRespVO> convertPage(PageResult<SysPagesConfigTemplate> sysPagesConfigTemplatePage);
}