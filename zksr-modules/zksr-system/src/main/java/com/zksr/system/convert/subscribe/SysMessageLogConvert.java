package com.zksr.system.convert.subscribe;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysMessageLog;
import com.zksr.system.controller.message.vo.SysMessageLogRespVO;
import com.zksr.system.controller.message.vo.SysMessageLogSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 消息发送记录 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-12-06
*/
@Mapper
public interface SysMessageLogConvert {

    SysMessageLogConvert INSTANCE = Mappers.getMapper(SysMessageLogConvert.class);

    SysMessageLogRespVO convert(SysMessageLog sysMessageLog);

    SysMessageLog convert(SysMessageLogSaveReqVO sysMessageLogSaveReq);

    PageResult<SysMessageLogRespVO> convertPage(PageResult<SysMessageLog> sysMessageLogPage);
}