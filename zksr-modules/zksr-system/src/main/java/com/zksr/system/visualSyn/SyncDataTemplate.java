package com.zksr.system.visualSyn;

import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.enums.request.VisualCommonSettingType;
import com.zksr.common.core.enums.request.VisualTemplateType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysInterfaceLogService;
import com.zksr.system.visualSyn.dto.SyncDataResult;
import com.zksr.system.visualSyn.factory.SyncDataNewFactory;
import com.zksr.system.visualSyn.service.ISyncDataNewService;
import com.zksr.trade.api.supplierAfter.SupplierAfterApi;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

import static com.zksr.common.core.constant.OpenApiConstants.*;
import static com.zksr.common.core.constant.OpenApiConstants.LOG_TYPE_SYNC;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.api.opensource.dto.OpensourceDto.setOpensourcePublicSetting;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_RESPONSE_SUCCESS_DATA_ERR;

/**
*  对外系统数据同步  业务数据处理
* @date 2024/10/23 10:07
* <AUTHOR>
*/
@Component("syncDataTemplate")
@Slf4j
public class SyncDataTemplate {

    @Autowired
    private ISysInterfaceLogService sysInterfaceLogService;

    @Autowired
    private ISysCacheService sysCacheService;

    @Autowired
    private SyncDataNewFactory syncDataNewFactory;

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Resource
    private SupplierAfterApi supplierAfterApi;

    private Boolean checkSendData(String reqId){
        //校验ReqID
        SysInterfaceLog sysInterfaceLog = sysInterfaceLogService.getSysInterfaceLogByReqId(reqId);
        if(Objects.nonNull(sysInterfaceLog)){
            //处理消息重发
            if(LOG_IS_RETRY_1.equals(sysInterfaceLog.getIsRetry())){
                //消息重发一次  将重发状态修改 并增加重发次数
                sysInterfaceLog.setIsRetry(LOG_IS_RETRY_0);
                sysInterfaceLog.setRetryCount(Long.sum(sysInterfaceLog.getRetryCount(), NumberPool.LONG_ONE));
                sysInterfaceLogService.updateSysInterfaceLog(HutoolBeanUtils.toBean(sysInterfaceLog,SysInterfaceLogSaveReqVO.class));
                return true;
            }

            log.info("同步信息已推送，无需重复推送reqId:{}",reqId);
            return false;
        }
        return true;
    }

    private void saveLog(SyncDataDTO data, VisualSettingMasterDto visualMaster){
        //查询是否存在
        SysInterfaceLog sysInterfaceLog = sysInterfaceLogService.getSysInterfaceLogByReqId(data.getReqId());

        //如果不存在 则新增
        if(Objects.isNull(sysInterfaceLog)){
            SysInterfaceLogSaveReqVO logVO = new SysInterfaceLogSaveReqVO();
            logVO.setReqId(data.getReqId());
            logVO.setSource(SyncSourceType.B2B.getCode());
            logVO.setReceive(visualMaster.getSourceType());
            logVO.setReqTime(new Date());
            logVO.setStatus(LOG_STATUS_WAIT);
            logVO.setRequestType(data.getTemplateType());
            logVO.setSupplierId(data.getSupplierId());
            logVO.setSysCode(data.getSysCode());
            logVO.setOperationType(Objects.nonNull(data.getOperationTypeCode()) ? data.getOperationTypeCode() : OperationType.ADD.getCode());
            logVO.setReqStatus(OpenApiConstants.REQ_STATUS_0);
            logVO.setReqData(JSONObject.toJSONString(data));
            logVO.setLogType(LOG_TYPE_SYNC);
            sysInterfaceLogService.insertSysInterfaceLog(logVO);
        }
        //如果存在  则是重发或其他情况  不需要更新初始日志
    }

    private void updateSyncLogBefore(SysInterfaceLogSaveReqVO logVo){

    }

    private void updateSyncLogAfter(SyncDataResult res,SyncDataDTO data,Integer reqStatus,String errMessage){
        //如果存在错误信息 则组装数据过程中失败了
        if(ToolUtil.isNotEmpty(errMessage)){
            sysInterfaceLogService.updateLogByResult(new SysInterfaceLogSaveReqVO(data.getReqId(),errMessage,ERROR,reqStatus));
        }else{
            sysInterfaceLogService.updateLogByResult(new SysInterfaceLogSaveReqVO(data.getReqId(),res.getMessage(),res.getCode(),reqStatus));
        }
    }


    protected SyncDataResult syncData(SyncDataDTO data, VisualSettingMasterDto visualMaster,OpensourceDto opensourceDto,ISyncDataNewService syncService) {
        return null;
    }

    @DistributedLock(prefix = RedisLockConstants.LOCK_SYNC, condition = "#data.supplierId + #data.dataId")
    public boolean invoke(SyncDataDTO data) {

        //幂等校验
        if(!checkSendData(data.getReqId())){
            return false;
        }

        log.info("开始组装入驻商订单同步数据data：{}",data);
        //根据入驻商查询可视化接口配置
        VisualSettingMasterDto visualMaster = sysCacheService.getVisualMasterBySupplierId(data.getSupplierId());
        if(Objects.isNull(visualMaster) ||
                Objects.isNull(visualMaster.getVisualMasterId())){
            log.info("获取可视化接口配置失败：{}",data);
            return false;
        }

        //获取入驻商开发能力信息
        OpensourceDto opensourceDto = sysCacheService.getOpensourceByMerchantId(data.getSupplierId());
        if(Objects.isNull(opensourceDto) || Objects.isNull(opensourceDto.getOpensourceId())){
            log.info("获取入驻商开发能力信息失败：{}",data);
            return false;
        }

        //设置公共配置参数
        getVisualCommonSettingType(opensourceDto,visualMaster);

        //新增发送日志
        saveLog(data,visualMaster);

        //响应结果数据
        SyncDataResult resultLog = new SyncDataResult();

        try{
            resultLog = syncData(data,visualMaster,opensourceDto,getAsyncDataNewService(visualMaster.getSendType()));

            //处理响应推送消息成功后的操作处理
            responseSuccessProcessor(data,resultLog);
        }catch (Exception e){
            log.error("推送第三方数据异常，", e);
            //推送失败 增加错误日志
            updateSyncLogAfter(null,data,REQ_STATUS_1,e.getMessage());
        }

        //更新响应结果日志信息
        updateSyncLogAfter(resultLog,data,REQ_STATUS_2,null);

        return true;

    }

    /**
     * 获取设置 入驻商的可视化公共配置信息
     * @param opensourceDto
     * @param visualSettingMasterDto
     * @return
     */
    private void getVisualCommonSettingType(OpensourceDto opensourceDto, VisualSettingMasterDto visualSettingMasterDto){
        //设置公共配置信息
        if(VisualCommonSettingType.VISUAL.getType().equals(visualSettingMasterDto.getCommonSettingType())){
            setOpensourcePublicSetting(opensourceDto,
                    visualSettingMasterDto.getSendUrl(),
                    visualSettingMasterDto.getPublicKey(),
                    visualSettingMasterDto.getPrivateKey());
        }else if (VisualCommonSettingType.SUPPLIER.getType().equals(visualSettingMasterDto.getCommonSettingType())){
            //不需要改变值

        }else if(VisualCommonSettingType.PRIORITY_SUPPLIER.getType().equals(visualSettingMasterDto.getCommonSettingType())){
            setOpensourcePublicSetting(opensourceDto,
                    ToolUtil.isNotEmpty(opensourceDto.getSendUrl()) ? opensourceDto.getSendUrl() : visualSettingMasterDto.getSendUrl(),
                    ToolUtil.isNotEmpty(opensourceDto.getPublicKey()) ? opensourceDto.getPublicKey() : visualSettingMasterDto.getPublicKey(),
                    ToolUtil.isNotEmpty(opensourceDto.getPrivateKey()) ? opensourceDto.getPrivateKey() : visualSettingMasterDto.getPrivateKey());
        }else{
            //默认取可视化接口配置
            setOpensourcePublicSetting(opensourceDto,
                    visualSettingMasterDto.getSendUrl(),
                    visualSettingMasterDto.getPublicKey(),
                    visualSettingMasterDto.getPrivateKey());
        }
    }

    /**
     * 响应推送消息成功后的操作处理
     */
    public void responseSuccessProcessor(SyncDataDTO data,SyncDataResult syncDataResult){
        //如果接口响应--操作成功 对相关接口做推送成功后的操作信息
        if(OpenApiConstants.SUCCESS_0.equals(syncDataResult.getCode())){
            try{
                //销售订单推送成功
                if(VisualTemplateType.ORDER_TYPE.getType().equals(data.getTemplateType())){
                    supplierOrderApi.syncOrderResponseSuccess(data);
                }else if(VisualTemplateType.AFTER_TYPE.getType().equals(data.getTemplateType())){
                    //售后订单推送成功
                    supplierAfterApi.syncAfterResponseSuccess(data);
                }
            }catch (Exception e){
                log.error(" SyncDataTemplate.responseSuccessProcessor异常,", e);
                throw exception(SYNC_ASSEMBLE_RESPONSE_SUCCESS_DATA_ERR,e.getMessage());
            }
        }
    }

    /**
     * 根据对接类型获取对外系统数据组装的实现类
     * @param name
     * @return
     */
    protected ISyncDataNewService getAsyncDataNewService(String name) {
        return syncDataNewFactory.getSyncService(name);
    }
}

@Service("syncBranch")
class SyncBranch extends SyncDataTemplate{

    @Override
    protected SyncDataResult syncData(SyncDataDTO data, VisualSettingMasterDto visualMaster,OpensourceDto opensourceDto,ISyncDataNewService syncService) {
        return syncService.syncBranch(data,visualMaster,opensourceDto );
    }
}

@Service("syncOrder")
class SyncOrder extends SyncDataTemplate{

    @Override
    protected SyncDataResult syncData(SyncDataDTO data, VisualSettingMasterDto visualMaster,OpensourceDto opensourceDto,ISyncDataNewService syncService) {
        return syncService.syncOrder(data,visualMaster,opensourceDto );
    }
}

@Service("syncAfter")
class SyncAfter extends SyncDataTemplate{

    @Override
    protected SyncDataResult syncData(SyncDataDTO data, VisualSettingMasterDto visualMaster,OpensourceDto opensourceDto,ISyncDataNewService syncService) {
        return syncService.syncAfter(data,visualMaster,opensourceDto );
    }
}

@Service("syncReceipt")
class SyncReceipt extends SyncDataTemplate{

    @Override
    protected SyncDataResult syncData(SyncDataDTO data, VisualSettingMasterDto visualMaster,OpensourceDto opensourceDto,ISyncDataNewService syncService) {
        return syncService.syncReceipt(data,visualMaster,opensourceDto );
    }
}

@Service("syncBranchValueInfo")
class SyncBranchValueInfo extends SyncDataTemplate{

    @Override
    protected SyncDataResult syncData(SyncDataDTO data, VisualSettingMasterDto visualMaster,OpensourceDto opensourceDto,ISyncDataNewService syncService) {
        return syncService.syncBranchValueInfo(data,visualMaster,opensourceDto );
    }
}


