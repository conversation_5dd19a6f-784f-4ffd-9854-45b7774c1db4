package com.zksr.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 系统信息
 * @date 2024/5/9 8:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "系统信息")
public class SystemInfoVO {

    @ApiModelProperty("平台名称")
    private String partnerName;

    @ApiModelProperty("平台log")
    private String partnerLog;
}
