package com.zksr.system.openapi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.domain.vo.openapi.receive.SysSupplierDTO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.system.controller.log.vo.SysInterfaceLogSaveReqVO;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mq.OpenapiProducer;
import com.zksr.system.openapi.service.ISysTokenService;
import com.zksr.system.service.ISysInterfaceLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

import static com.zksr.common.core.constant.OpenApiConstants.LOG_STATUS_FAIN;

@RestController
@RequestMapping("/openapi/supplier")
@Api(tags = "OPENAPI - 入驻商模块接口")
@Slf4j
public class SupplierController {

    @Autowired
    private OpenapiProducer openapiProducer;

    @Resource
    private ISysTokenService sysTokenService;

    @Resource
    private ISysInterfaceLogService sysInterfaceLogService;

    @ApiOperation(value = "新增入驻商信息")
    @PostMapping(value = "/addSupplier")
    @ResponseBody
    @RequiresOpenapiLogin(abilityKey = "addSupplier")
    @Log(title = "OPENAPI - 新增入驻商信息", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> addSupplier(@Valid @RequestBody List<SysSupplierDTO> sysSupplierDTOS) throws CloneNotSupportedException {
        if(ToolUtil.isEmpty(sysSupplierDTOS)){
            log.info("OPENAPI - 新增入驻商信息:入参为空");
            return CommonResult.success(true);
        }
        List<SysInterfaceLog> interfaceLogList = new ArrayList(sysSupplierDTOS.size());
        SysInterfaceLog interfaceLog = sysTokenService.getOpenapiInitLog(B2BRequestType.SAVE_SUPPLIER.getB2bType(), OperationType.ADD_OR_UPDATE.getCode(),null );
        log.info("OPENAPI - 新增入驻商信息:{}", JSON.toJSONString(sysSupplierDTOS));
        for (SysSupplierDTO sysSupplierDTO : sysSupplierDTOS) {
            SysInterfaceLog newLog = new SysInterfaceLog();
            BeanUtil.copyProperties(interfaceLog, newLog);
            newLog.setBizData(JSON.toJSONString(sysSupplierDTO));
            newLog.setReqId(IdUtils.fastSimpleUUID());
            interfaceLogList.add(newLog);
        }
        boolean res = sysInterfaceLogService.insertBatch(interfaceLogList);
        if(res){
            for (SysInterfaceLog sysInterfaceLog : interfaceLogList) {
                try {
                    openapiProducer.sendSaveSupplierMsg(sysInterfaceLog);
                } catch (Exception e) {
                    log.error("OPENAPI - 发送入驻商消息失败: {}", e.getMessage(), e);
                    sysInterfaceLog.setStatus(LOG_STATUS_FAIN);
                    sysInterfaceLog.setMessage(e.getMessage());
                    sysInterfaceLogService.updateSysInterfaceLog(HutoolBeanUtils.toBean(sysInterfaceLog, SysInterfaceLogSaveReqVO.class));
                }
            }
        }else {
            log.error("OPENAPI - 新增入驻商信息: 日志批量插入失败");
            return CommonResult.error(500,"日志批量插入失败");
        }
        return CommonResult.success(true);
    }
}
