package com.zksr.system.controller.openability;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.system.controller.openability.vo.SysOpenabilityReqVO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysOpenability;
import com.zksr.system.service.ISysOpenabilityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.controller.openability.vo.SysOpenabilityPageReqVO;
import com.zksr.system.controller.openability.vo.SysOpenabilitySaveReqVO;
import com.zksr.system.controller.openability.vo.SysOpenabilityRespVO;
import com.zksr.system.convert.openability.SysOpenabilityConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 开放能力Controller
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Api(tags = "管理后台 - 开放能力接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/openability")
public class SysOpenabilityController {
    @Autowired
    private ISysOpenabilityService sysOpenabilityService;

    /**
     * 新增开放能力
     */
    @ApiOperation(value = "新增开放能力", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "开放能力", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysOpenabilitySaveReqVO createReqVO) {
        return success(sysOpenabilityService.insertSysOpenability(createReqVO));
    }

    /**
     * 修改开放能力
     */
    @ApiOperation(value = "修改开放能力", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "开放能力", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysOpenabilitySaveReqVO updateReqVO) {
            sysOpenabilityService.updateSysOpenability(updateReqVO);
        return success(true);
    }

    /**
     * 删除开放能力
     */
    @ApiOperation(value = "删除开放能力", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "开放能力", businessType = BusinessType.DELETE)
    @DeleteMapping("/{openabilityIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] openabilityIds) {
        sysOpenabilityService.deleteSysOpenabilityByOpenabilityIds(openabilityIds);
        return success(true);
    }

    /**
     * 获取开放能力详细信息
     */
    @ApiOperation(value = "获得开放能力详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{openabilityId}")
    public CommonResult<SysOpenabilityRespVO> getInfo(@PathVariable("openabilityId") Long openabilityId) {
        SysOpenability sysOpenability = sysOpenabilityService.getSysOpenability(openabilityId);
        return success(SysOpenabilityConvert.INSTANCE.convert(sysOpenability));
    }

    /**
     * 分页查询开放能力
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得开放能力分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysOpenabilityRespVO>> getPage(@Valid SysOpenabilityPageReqVO pageReqVO) {
        PageResult<SysOpenability> pageResult = sysOpenabilityService.getSysOpenabilityPage(pageReqVO);
        return success(SysOpenabilityConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 查询开放能力
     */
    @GetMapping("/menuList")
    @ApiOperation(value = "获得开放能力列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<List<SysOpenabilityRespVO>> getList(@Valid SysOpenabilityReqVO reqVO) {
        List<SysOpenability> listResult = sysOpenabilityService.getSysOpenabilityList(reqVO);
        return success(SysOpenabilityConvert.INSTANCE.convertList(listResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:openability:add";
        /** 编辑 */
        public static final String EDIT = "system:openability:edit";
        /** 删除 */
        public static final String DELETE = "system:openability:remove";
        /** 列表 */
        public static final String LIST = "system:openability:list";
        /** 查询 */
        public static final String GET = "system:openability:query";
        /** 停用 */
        public static final String DISABLE = "system:openability:disable";
        /** 启用 */
        public static final String ENABLE = "system:openability:enable";
    }
}
