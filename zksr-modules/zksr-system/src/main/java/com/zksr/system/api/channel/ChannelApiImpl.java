package com.zksr.system.api.channel;

import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.domain.SysChannel;
import com.zksr.system.service.ISysChannelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
public class ChannelApiImpl implements ChannelApi{

    @Autowired
    private ISysChannelService sysChannelService;

    @Override
    public CommonResult<ChannelDTO> getByChannelId(Long channelId) {
        SysChannel sysChannel = sysChannelService.getSysChannel(channelId);
        return CommonResult.success(HutoolBeanUtils.toBean(sysChannel, ChannelDTO.class));
    }

    @Override
    public CommonResult<Map<Long, ChannelDTO>> getChannelListByDcIdAndSysCode(@RequestParam(value = "sysCode",required = false) Long sysCode, @RequestParam(value =  "dcId",required = false) Long dcId) {
        Map<Long, ChannelDTO> channelMap = new HashMap<>();
        List<SysChannel> channelList = sysChannelService.getChannelList();
        if(ToolUtil.isNotEmpty(channelList)){
            List<ChannelDTO> channelDTOList = HutoolBeanUtils.toBean(channelList, ChannelDTO.class);
            channelMap = channelDTOList.stream().collect(Collectors.toMap(ChannelDTO::getChannelId, x->x));
        }

        return CommonResult.success(channelMap);
    }

    @Override
    public CommonResult<List<ChannelDTO>> getChannelList() {
        List<SysChannel> channelList = sysChannelService.getChannelList();
        return CommonResult.success(HutoolBeanUtils.toBean(channelList, ChannelDTO.class));
    }

    @Override
    public CommonResult<List<ChannelDTO>> getChannelListBySysCode(Long sysCode, String channelName) {
        List<SysChannel> channelList = sysChannelService.getChannelListBySysCode(sysCode, channelName);
        return CommonResult.success(HutoolBeanUtils.toBean(channelList, ChannelDTO.class));
    }
}
