package com.zksr.system.api.partner;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.domain.AjaxResultBase;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partner.vo.SysPartnerPageReqVO;
import com.zksr.system.api.partner.vo.SysPartnerRespVO;
import com.zksr.system.convert.partner.PartnerConvert;
import com.zksr.system.domain.SysPartner;
import com.zksr.system.service.ISysPartnerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.*;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
public class PartnerApiImpl implements PartnerApi {

    @Autowired
    private ISysPartnerService sysPartnerService;


    @Override
    public CommonResult<PartnerDto> getBySysCode(Long sysCode) {
        SysPartner sysPartner = sysPartnerService.getSysPartner(sysCode);
        PartnerDto partnerDto = HutoolBeanUtils.toBean(sysPartner, PartnerDto.class);
        return success(partnerDto);
    }

    @Override
    public CommonResult<PartnerDto> getPartnerBySource(String source) {
        PartnerDto partnerDto = sysPartnerService.getByCacheKey(source);
        return success(partnerDto);
    }

    @Override
    public CommonResult<List<PartnerDto>> getPartnerInfo(){
        List<SysPartner> partnerList =  sysPartnerService.getPartnerInfo();
        return success(HutoolBeanUtils.toBean(partnerList, PartnerDto.class));
    }

    @Override
    public CommonResult<PageResult<SysPartnerRespVO>> getPage(SysPartnerPageReqVO pageReqVO) {
        return success(PartnerConvert.INSTANCE.convertRespVO(sysPartnerService.getSysPartnerPage(pageReqVO)));
    }

    public CommonResult<PartnerDto> getBySaasTenantCode(@RequestParam("saasTenantCode") String saasTenantCode) {
        SysPartner sysPartner = sysPartnerService.getBySaasTenantCode(saasTenantCode);
        PartnerDto partnerDto = PartnerConvert.INSTANCE.convert2PartnerDto(sysPartner);
        return success(partnerDto);
    }

}
