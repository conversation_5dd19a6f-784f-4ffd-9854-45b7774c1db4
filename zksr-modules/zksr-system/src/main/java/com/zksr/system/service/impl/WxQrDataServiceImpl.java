package com.zksr.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;
import com.zksr.system.api.wx.dto.WxQrData;
import com.zksr.system.api.wx.vo.WxQrDataVo;
import com.zksr.system.convert.area.SysAreaCityConvert;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.mapper.SysAreaCityMapper;
import com.zksr.system.mapper.WxQrDataMapper;
import com.zksr.system.service.ISysAreaCityService;
import com.zksr.system.service.IWxQrDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class WxQrDataServiceImpl implements IWxQrDataService {

    @Autowired
    private WxQrDataMapper wxQrDataMapper;

    public WxQrData save(String qrValue){
        WxQrData wxQrData = new WxQrData();
        wxQrData.setQrValue(qrValue);
        wxQrDataMapper.insert(wxQrData);
        wxQrData.setQrKey("FF"+wxQrData.getQrId());
        wxQrDataMapper.updateById(wxQrData);
        return wxQrData;
    }

    public WxQrData getByQrKey(String qrKey){
        return wxQrDataMapper.selectOne(new LambdaQueryWrapper<WxQrData>().eq(WxQrData::getQrKey,qrKey));
    }

}
