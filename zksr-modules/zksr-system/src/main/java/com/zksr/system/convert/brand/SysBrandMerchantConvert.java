package com.zksr.system.convert.brand;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysBrandMerchant;
import com.zksr.system.api.brand.vo.SysBrandMerchantRespVO;
import com.zksr.system.controller.brand.vo.SysBrandMerchantSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 品牌商资料 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-08-05
*/
@Mapper
public interface SysBrandMerchantConvert {

    SysBrandMerchantConvert INSTANCE = Mappers.getMapper(SysBrandMerchantConvert.class);

    SysBrandMerchantRespVO convert(SysBrandMerchant sysBrandMerchant);

    SysBrandMerchant convert(SysBrandMerchantSaveReqVO sysBrandMerchantSaveReq);

    PageResult<SysBrandMerchantRespVO> convertPage(PageResult<SysBrandMerchant> sysBrandMerchantPage);
}