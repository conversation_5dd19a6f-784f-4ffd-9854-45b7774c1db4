package com.zksr.system.controller.visualSetting.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 可视化配置主对象 visual_setting_master
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Data
@ApiModel("可视化配置主 - visual_setting_master Response VO")
public class VisualSettingMasterRespVO {
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(value = "备注")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long visualMasterId;

    /** 平台名称 */
    @Excel(name = "平台名称")
    @ApiModelProperty(value = "平台名称")
    private String platformName;

    /** 加密类型（数据字典send_encrypt_type） */
    @Excel(name = "加密类型", readConverterExp = "数=据字典send_encrypt_type")
    @ApiModelProperty(value = "加密类型")
    private Long encryptType;

    /** 对接地址 */
    @Excel(name = "对接地址")
    @ApiModelProperty(value = "对接地址")
    private String sendUrl;

    /** 状态（0 停用  1启用） */
    @Excel(name = "状态", readConverterExp = "0=,停=用,1=启用")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 公钥 */
    @Excel(name = "公钥")
    @ApiModelProperty(value = "公钥")
    private String publicKey;

    /** 私钥 */
    @Excel(name = "私钥")
    @ApiModelProperty(value = "私钥")
    private String privateKey;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;


    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    /**
     * 对接类型
     */
    @ApiModelProperty(value = "对接类型")
    private String sendType;

    /** 系统类型(数据字典sync_source_type 枚举：syncSourceType) */
    @ApiModelProperty(value = "系统类型(安得ERP、安得WMS、新ERP)")
    @Excel(name = "系统类型(安得ERP、安得WMS、新ERP)")
    private Integer sourceType;

    /** 获取公共配置方式 */
    @ApiModelProperty(value = "获取公共配置方式(0可视化接口配置 1入驻商配置 2优先入驻商配置)")
    @Excel(name = "获取公共配置方式(0可视化接口配置 1入驻商配置 2优先入驻商配置)")
    private Integer commonSettingType;
}
