package com.zksr.system.openapi.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.domain.vo.openapi.SyncReceiptSendDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncAfterOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderCallDTO;
import com.zksr.common.core.domain.vo.openapi.syncCall.SyncOrderPageReqDTO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.Md5Utils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.security.annotation.RequiresOpenapiLogin;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.mq.OpenapiProducer;
import com.zksr.system.openapi.service.ISysTokenService;
import com.zksr.system.service.ISysInterfaceLogService;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.OrderReceiptRespDTO;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;
import static com.zksr.system.enums.ErrorCodeConstants.OPENAPI_CHECK_REQVO_EXISTS;

@Api(tags = "OPENAPI - 订单")
@RestController
@RequestMapping("/openapi/order")
@Slf4j
//!@开放API - 总Controller
public class TrdOrderController {

    @Autowired
    private OpenapiProducer openapiProducer;

    @Resource
    private ISysTokenService sysTokenService;

    @Resource
    private ISysInterfaceLogService sysInterfaceLogService;

    @Resource
    private OrderApi orderApi;

    @Resource
    private SupplierOrderApi supplierOrderApi;

    //TODO 订单发货前取消
    @ApiOperation(value = "订单发货前取消", httpMethod = HttpMethod.POST)
    @PostMapping("/orderCancel")
    @RequiresOpenapiLogin(abilityKey = "orderCancel")
    @Log(title = "OPENAPI - 订单发货前取消", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> orderCancel(@RequestBody @Valid OrderCancelVO vo) {

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.ORDER_CANCEL, vo.getSupplierOrderNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendOrderCancelMsg(interfaceLog);
        }
        return CommonResult.success(true);
    }

    //TODO 订单接收成功通知
    @ApiOperation(value = "订单接收成功通知", httpMethod = HttpMethod.POST)
    @PostMapping("/orderReceiveCallback")
    @RequiresOpenapiLogin(abilityKey = "orderReceiveCallback")
    @Log(title = "OPENAPI - 订单接收成功通知", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> orderReceiveCallback(@RequestBody @Valid OrderReceiveCallbackVO vo) {

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.ORDER_RECEIVE_CALLBACK, vo.getSupplierOrderNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendOrderReceiveCallbackMsg(interfaceLog);
        }
        return CommonResult.success(true);
    }

    @ApiOperation(value = "订单取消接收通知", httpMethod = HttpMethod.POST)
    @PostMapping("/orderCancelReceiveCallback")
    @RequiresOpenapiLogin(abilityKey = "orderCancelReceiveCallback")
    @Log(title = "OPENAPI - 订单取消接收通知")
    public CommonResult<Boolean> orderCancelReceiveCallback(@RequestBody @Valid OrderCancelReceiveCallbackVO vo) {

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.ORDER_CANCEL_RECEIVE_CALLBACK, vo.getSupplierOrderNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendOrderCancelReceiveCallbackMsg(interfaceLog);
        }
        return CommonResult.success(true);
    }

    //TODO 订单状态回传
    @ApiOperation(value = "订单发货", httpMethod = HttpMethod.POST)
    @PostMapping("/delivery")
    @RequiresOpenapiLogin(abilityKey = "delivery")
    @Log(title = "OPENAPI - 订单发货", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> delivery(@RequestBody @Valid OrderOutboundReturnVO vo) {

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.DELIVERY, vo.getSupplierOrderNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendDeliveryMsg(interfaceLog);
        }
        return CommonResult.success(true);
    }
    
    @ApiOperation(value = "订单包裹发货", httpMethod = HttpMethod.POST)
    @PostMapping("/deliveryPackage")
    @RequiresOpenapiLogin(abilityKey = "deliveryPackage")
    @Log(title = "OPENAPI - 包裹发货", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> deliveryPackage(@RequestBody @Valid OrderPackageReturnVO vo) {
        String md5Hash = Md5Utils.md5(JSON.toJSONString(vo));
        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.DELIVERY_PACKAGE, vo.getSupplierOrderNo()+"_"+md5Hash,null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendDeliveryPackageMsg(interfaceLog);
        }
        return CommonResult.success(true);
    }

    @ApiOperation(value = "订单状态", httpMethod = HttpMethod.POST)
    @PostMapping("/orderLog")
    @RequiresOpenapiLogin(abilityKey = "orderLog")
    @Log(title = "OPENAPI - 订单状态", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> orderLog(@RequestBody @Valid OrderStateReturnVO vo)  {

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.ORDER_LOG, vo.getSupplierOrderNo(),vo.getLogisticsStatus().toString());
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendOrderlogMsg(interfaceLog);
        }
        return CommonResult.success(true);

    }

    @ApiOperation("订单收货确认")
    @PostMapping(value = "/confirmReceipt")
    @RequiresOpenapiLogin(abilityKey = "confirmReceipt")
    @Log(title = "OPENAPI - 订单收货确认", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> confirmReceipt(@RequestBody @Valid ReceiveOrderTakeDeliveryVO vo){

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.CONFIRM_RECEIPT, vo.getSupplierOrderNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendConfirmReceiptMsg(interfaceLog);
        }
        return CommonResult.success(true);

    }

    @ApiOperation("获取订单信息")
    @PostMapping(value = "/getTheOrder")
    @RequiresOpenapiLogin(abilityKey = "getTheOrder")
    @Log(title = "OPENAPI - 获取订单信息", businessType = BusinessType.INSERT)
    @DistributedLock(prefix = RedisLockConstants.LOCK_GET_THEORDER, condition = "#reqDTO.startTime + #reqDTO.endTime")
    public CommonResult<PageResult<SyncOrderCallDTO>> getTheOrder(@RequestBody SyncOrderPageReqDTO reqDTO){
        PageResult<SyncOrderCallDTO> pageResult = orderApi.getOrdersWithDelay(reqDTO).getCheckedData();
        return success(HutoolBeanUtils.toBean(pageResult, SyncOrderCallDTO.class));
    }


    @ApiOperation("获取退货订单")
    @PostMapping(value = "/getAfterOrder")
    @RequiresOpenapiLogin(abilityKey = "getAfterOrder")
    @Log(title = "OPENAPI - 获取退货订单", businessType = BusinessType.INSERT)
    @DistributedLock(prefix = RedisLockConstants.LOCK_GET_AFTERORDER, condition = "#reqDTO.startTime + #reqDTO.endTime")
    public CommonResult<PageResult<SyncAfterOrderCallDTO>> getAfterOrder(@RequestBody SyncOrderPageReqDTO reqDTO){
        PageResult<SyncAfterOrderCallDTO> pageResult = orderApi.getAfterOrdersWithDelay(reqDTO).getCheckedData();
        return success(HutoolBeanUtils.toBean(pageResult, SyncAfterOrderCallDTO.class));
    }

    @ApiOperation(value = "退单接收成功通知", httpMethod = HttpMethod.POST)
    @PostMapping("/afterOrderReceiveCallback")
    @RequiresOpenapiLogin(abilityKey = "afterOrderReceiveCallback")
    @Log(title = "OPENAPI - 退单接收成功通知", businessType = BusinessType.UPDATE)
    public CommonResult<Boolean> afterOrderReceiveCallback(@RequestBody AfterOrderReceiveCallbackVO vo) {

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.AFTEER_ORDER_RECEIVE_CALLBACK, vo.getSupplierAfterNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendAfterOrderReceiveCallbackMsg(interfaceLog);
        }
        return CommonResult.success(true);
    }

    @ApiOperation("获取收款单")
    @PostMapping(value = "/getReceipt")
    @RequiresOpenapiLogin(abilityKey = "getReceipt")
    @Log(title = "OPENAPI - 获取收款单", businessType = BusinessType.INSERT)
    @DistributedLock(prefix = RedisLockConstants.LOCK_GET_RECEIPT, condition = "#vo.supplierSheetNo")
    public CommonResult<List<OrderReceiptRespDTO>> getReceipt(@RequestBody SyncReceiptSendDTO vo){
        return orderApi.getReceiptBySupplierOrderNo(vo);
    }

    @ApiOperation(value = "新增货到付款清账能力", httpMethod = HttpMethod.POST)
    @PostMapping("/addHdfkSettle")
    @RequiresOpenapiLogin(abilityKey = "addHdfkSettle")
    @Log(title = "OPENAPI - 新增货到付款清账能力", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> addHdfkSettle(@RequestBody OrderHdfkSettleVO vo)  {

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.ADD_HDFK_SETTLE, vo.getSupplierOrderNo(),null);
        if(ObjectUtil.isNotNull(interfaceLog) && ObjectUtil.isNotNull(interfaceLog.getId())){
            openapiProducer.sendAddHdfkSettle(interfaceLog);
        }
        return CommonResult.success(true);

    }

    @ApiOperation(value = "订单发票接收", httpMethod = HttpMethod.POST)
    @PostMapping("/receiveOrderInvoice")
    @RequiresOpenapiLogin(abilityKey = "receiveOrderInvoice")
    @Log(title = "OPENAPI - 订单发票接收", businessType = BusinessType.INSERT)
    public CommonResult<Boolean> receiveOrderInvoice(@RequestBody SupplierOrderInvoiceOpenDTO vo)  {

        //获取日志信息
        SysInterfaceLog interfaceLog = orderSaveLog(vo, B2BRequestType.RECEIVE_ORDER_INVOICE, vo.getSupplierOrderNo(),null);
        vo.setSysCode(interfaceLog.getSysCode());

        supplierOrderApi.receiveOrderInvoice(vo).getCheckedData();
        return CommonResult.success(true);

    }


    public SysInterfaceLog orderSaveLog(Object vo,B2BRequestType requestType,String dataId,String code){
        SysInterfaceLog interfaceLog = new SysInterfaceLog();

        if(ToolUtil.isEmpty(vo)){
            log.info("OPENAPI - {}:入参为空",requestType.getInfo());
            throw exception(OPENAPI_CHECK_REQVO_EXISTS,requestType.getInfo());
        }

        //设置reqId
        String reqId = dataId + StringPool.UNDERSCORE + requestType.getB2bType();
        if(B2BRequestType.ORDER_LOG.equals(requestType)){
            reqId = dataId + StringPool.UNDERSCORE + requestType.getB2bType() + StringPool.UNDERSCORE + code;
        }
        //TODO 这里应该也要根据业务有幂等校验
        //如果已存在该reqId的日志记录 则拦截不往下执行
        if(sysTokenService.checkReceiveLogBefore(reqId)){
            return interfaceLog;
        }

        //组装日志公共参数
        interfaceLog = sysTokenService.getOpenapiInitLog(requestType.getB2bType(), OperationType.UPDATE.getCode(), reqId);
        log.info("OPENAPI - {}:{}",requestType.getInfo(), JSON.toJSONString(vo));

        //组装业务参数
        //新增或更新请求参数
        interfaceLog.setBizData(JSON.toJSONString(vo));
        interfaceLog.setReqId(reqId);

        //新增日志信息
        sysInterfaceLogService.insertLog(interfaceLog);
        return interfaceLog;
    }
}
