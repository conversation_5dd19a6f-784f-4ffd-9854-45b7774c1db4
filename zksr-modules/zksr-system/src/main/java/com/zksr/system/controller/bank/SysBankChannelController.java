package com.zksr.system.controller.bank;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.domain.SysBankChannel;
import com.zksr.system.service.ISysBankChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.system.api.bank.vo.SysBankChannelPageReqVO;
import com.zksr.system.controller.bank.vo.SysBankChannelSaveReqVO;
import com.zksr.system.api.bank.vo.SysBankChannelRespVO;
import com.zksr.system.convert.bank.SysBankChannelConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 联行号信息Controller
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Api(tags = "管理后台 - 联行号信息接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/bankChannel")
public class SysBankChannelController {
    @Autowired
    private ISysBankChannelService sysBankChannelService;

    /**
     * 新增联行号信息
     */
    @ApiOperation(value = "新增联行号信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "联行号信息", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysBankChannelSaveReqVO createReqVO) {
        return success(sysBankChannelService.insertSysBankChannel(createReqVO));
    }

    /**
     * 修改联行号信息
     */
    @ApiOperation(value = "修改联行号信息", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "联行号信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysBankChannelSaveReqVO updateReqVO) {
            sysBankChannelService.updateSysBankChannel(updateReqVO);
        return success(true);
    }

    /**
     * 删除联行号信息
     */
    @ApiOperation(value = "删除联行号信息", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "联行号信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bankChannelIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] bankChannelIds) {
        sysBankChannelService.deleteSysBankChannelByBankChannelIds(bankChannelIds);
        return success(true);
    }

    /**
     * 获取联行号信息详细信息
     */
    @ApiOperation(value = "获得联行号信息详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    //@RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{bankChannelId}")
    public CommonResult<SysBankChannelRespVO> getInfo(@PathVariable("bankChannelId") Long bankChannelId) {
        SysBankChannel sysBankChannel = sysBankChannelService.getSysBankChannel(bankChannelId);
        return success(SysBankChannelConvert.INSTANCE.convert(sysBankChannel));
    }

    /**
     * 分页查询联行号信息
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得联行号信息分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    //@RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysBankChannelRespVO>> getPage(@Valid SysBankChannelPageReqVO pageReqVO) {
        PageResult<SysBankChannel> pageResult = sysBankChannelService.getSysBankChannelPage(pageReqVO);
        return success(SysBankChannelConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:bank-channel:add";
        /** 编辑 */
        public static final String EDIT = "system:bank-channel:edit";
        /** 删除 */
        public static final String DELETE = "system:bank-channel:remove";
        /** 列表 */
        public static final String LIST = "system:bank-channel:list";
        /** 查询 */
        public static final String GET = "system:bank-channel:query";
        /** 停用 */
        public static final String DISABLE = "system:bank-channel:disable";
        /** 启用 */
        public static final String ENABLE = "system:bank-channel:enable";
    }
}
