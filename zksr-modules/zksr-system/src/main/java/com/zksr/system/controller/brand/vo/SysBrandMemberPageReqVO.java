package com.zksr.system.controller.brand.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 品牌商子账户对象 sys_brand_member
 *
 * <AUTHOR>
 * @date 2024-08-07
 */
@ApiModel("品牌商子账户 - sys_brand_member分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysBrandMemberPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "帐号状态")
    private Long brandMemberId;

    /** 运营商编号 */
    @Excel(name = "运营商编号")
    @ApiModelProperty(value = "运营商编号")
    private Long sysCode;

    /** 品牌商ID */
    @Excel(name = "品牌商ID")
    @ApiModelProperty(value = "品牌商ID")
    private Long brandMerchantId;

    /** 联系人手机号 */
    @Excel(name = "联系人手机号")
    @ApiModelProperty(value = "联系人手机号")
    private String contactPhone;

    /** 联系人名称 */
    @Excel(name = "联系人名称")
    @ApiModelProperty(value = "联系人名称")
    private String contactName;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 关联管理员id */
    @Excel(name = "关联管理员id")
    @ApiModelProperty(value = "关联管理员id")
    private Long sysUserId;

    /** 关联品牌集合 */
    @Excel(name = "关联品牌集合")
    @ApiModelProperty(value = "关联品牌集合")
    private String brandIds;

    /** 关联城市集合 */
    @Excel(name = "关联城市集合")
    @ApiModelProperty(value = "关联城市集合")
    private String areaIds;

    /** 帐号状态（0正常 1停用） */
    @Excel(name = "帐号状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "帐号状态")
    private Integer status;
}
