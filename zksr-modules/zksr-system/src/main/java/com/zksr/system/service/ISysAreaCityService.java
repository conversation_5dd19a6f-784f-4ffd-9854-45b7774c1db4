package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;

import java.util.List;

/**
 * 省市区Service接口
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface ISysAreaCityService {

    /**
     * 新增省市区
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysAreaCity(@Valid SysAreaCitySaveReqVO createReqVO);

    /**
     * 修改省市区
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysAreaCity(@Valid SysAreaCitySaveReqVO updateReqVO);

    /**
     * 批量修改省市区
     *
     * @param updateReqVOList 修改信息
     * @return 结果
     */
    void updateBatch(List<SysAreaCitySaveReqVO> updateReqVOList);

    /**
     * 删除省市区
     *
     * @param areaCityId ${pkColumn.columnComment}
     */
    public void deleteSysAreaCity(Long areaCityId);

    /**
     * 批量删除省市区
     *
     * @param areaCityIds 需要删除的省市区主键集合
     * @return 结果
     */
    public void deleteSysAreaCityByAreaCityIds(Long[] areaCityIds);

    /**
     * 获得省市区
     *
     * @param areaCityId ${pkColumn.columnComment}
     * @return 省市区
     */
    public SysAreaCity getSysAreaCity(Long areaCityId);

    /**
     * 获得省市区分页
     *
     * @param pageReqVO 分页查询
     * @return 省市区分页
     */
    PageResult<SysAreaCity> getSysAreaCityPage(SysAreaCityPageReqVO pageReqVO);

    SysAreaCity getByNameAndParent(String name, Long pid, Integer deep);

    Long insert(SysAreaCitySaveReqVO insertVO);

    /**
     * 根据省市区名称查询三级地址
     * @param districtName  区域名称
     * @param cityName      市名称
     * @param provinceName  省名称
     * @return
     */
    SysAreaCity queryByName(String districtName, String cityName, String provinceName);
}
