package com.zksr.system.convert.area;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 省市区 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-07-18
*/
@Mapper
public interface SysAreaCityConvert {

    SysAreaCityConvert INSTANCE = Mappers.getMapper(SysAreaCityConvert.class);

    SysAreaCityRespVO convert(SysAreaCity sysAreaCity);

    SysAreaCity convert(SysAreaCitySaveReqVO sysAreaCitySaveReq);

    List<SysAreaCity> convert(List<SysAreaCitySaveReqVO> sysAreaCitySaveReq);

    PageResult<SysAreaCityRespVO> convertPage(PageResult<SysAreaCity> sysAreaCityPage);
}