package com.zksr.system.controller.area.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 区域城市对象 sys_area
 *
 * <AUTHOR>
 * @date 2024-03-01
 */
@ApiModel("区域城市 - sys_area分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysAreaPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 区域城市id */
    @Excel(name = "区域城市id")
    @ApiModelProperty(value = "区域城市id")
    private Long areaId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id")
    private Long pid;

    /** 区域城市名 */
    @Excel(name = "区域城市名")
    @ApiModelProperty(value = "区域城市名")
    private String areaName;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 运营商id */
    @Excel(name = "运营商id")
    @ApiModelProperty(value = "运营商id")
    private Long dcId;

    /** 是否开通本地配送业务 1-是 0-否 */
    @Excel(name = "是否开通本地配送业务 1-是 0-否")
    @ApiModelProperty(value = "是否开通本地配送业务 1-是 0-否")
    private String localFlag;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id")
    private Long groupId;

    /** 级别 */
    @Excel(name = "级别")
    @ApiModelProperty(value = "级别",  example = "0")
    private Integer level;
}
