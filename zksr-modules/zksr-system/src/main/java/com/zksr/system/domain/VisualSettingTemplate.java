package com.zksr.system.domain;

import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 可视化接口模板对象 visual_setting_template
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@TableName(value = "visual_setting_template")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisualSettingTemplate extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** id */
    @TableId
    private Long visualTemplateId;

    /** 状态（0 停用  1启用） */
    @Excel(name = "状态", readConverterExp = "0=,停=用,1=启用")
    private Integer status;

    /** 接口模板名称 */
    @Excel(name = "接口模板名称")
    private String templateName;

    /** 接口模板JSON */
    @Excel(name = "接口模板JSON")
    private String apiTemplate;

    /** 接口模板类型 */
    @Excel(name = "接口模板类型")
    private Long templateType;

    /** 系统类型(数据字典sync_source_type 枚举：syncSourceType) */
    @Excel(name = "系统类型(安得ERP、安得WMS、新ERP)")
    private Integer sourceType;

}
