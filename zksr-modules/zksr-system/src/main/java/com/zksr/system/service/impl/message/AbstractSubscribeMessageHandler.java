package com.zksr.system.service.impl.message;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.zksr.common.third.message.dto.CommonMessageDTO;
import com.zksr.system.api.commonMessage.dto.MessageTemplateDTO;
import com.zksr.system.api.commonMessage.vo.SubscribeEventBodyVO;
import com.zksr.system.service.ISubscribeMessageHandler;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.ISysMessageTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@SuppressWarnings("all")
@Slf4j
public class AbstractSubscribeMessageHandler {

    @Autowired
    List<ISubscribeMessageHandler> handlers;

    @Autowired
    private ISysCacheService sysCacheService;

    @Autowired
    protected ISysMessageTemplateService messageTemplateService;

    /**
     * 处理事件
     * @param subscribeEventBodyVO
     */
    public void processEvent(SubscribeEventBodyVO subscribeEventBodyVO) {
        // 解析获取模版
        // 同一个场景, 可能配置了多个模版
        // 需要通知不同类型
        List<MessageTemplateDTO> messageTemplateList = sysCacheService.getMessageTemplateDTO(subscribeEventBodyVO.getSysCode(), subscribeEventBodyVO.getScene());
        if (ObjectUtil.isEmpty(messageTemplateList)) {
            log.error("消息模版场景未配置 {}", JSON.toJSONString(subscribeEventBodyVO));
            return;
        }
        ISubscribeMessageHandler channel = getHandler(subscribeEventBodyVO.getScene());
        if (Objects.isNull(channel)) {
            log.error("不支持的通知场景 {}", JSON.toJSONString(subscribeEventBodyVO));
        }
        try {
            // 循环不同模版处理消息
            for (MessageTemplateDTO messageTemplateDTO : messageTemplateList) {
                // 获取消息内容, 以及接受者
                List<CommonMessageDTO> messsageList = channel.processConvert(subscribeEventBodyVO, messageTemplateDTO);
                // 调用消息模版处理发送消息
                messageTemplateService.sendMsg(messsageList, messageTemplateDTO);
            }
        } catch (Exception e) {
            log.error("消息中心消息处理失败", e);
        }
    }

    ISubscribeMessageHandler getHandler(Integer scene) {
        for (ISubscribeMessageHandler channelService : handlers) {
            if (channelService.scene().equals(scene)) {
                return channelService;
            }
        }
        return null;
    }
}
