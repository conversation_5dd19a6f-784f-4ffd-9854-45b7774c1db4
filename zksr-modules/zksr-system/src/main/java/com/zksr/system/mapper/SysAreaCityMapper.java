package com.zksr.system.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysAreaCity;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 省市区Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@Mapper
public interface SysAreaCityMapper extends BaseMapperX<SysAreaCity> {
    default PageResult<SysAreaCity> selectPage(SysAreaCityPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SysAreaCity>()
                    .eqIfPresent(SysAreaCity::getAreaCityId, reqVO.getAreaCityId())
                    .eqIfPresent(SysAreaCity::getPid, reqVO.getPid())
                    .eqIfPresent(SysAreaCity::getDeep, reqVO.getDeep())
                    .eqIfPresent(SysAreaCity::getExtId, reqVO.getExtId())
                    .likeIfPresent(SysAreaCity::getName, reqVO.getName())
                .orderByAsc(SysAreaCity::getAreaCityId));
    }

    default SysAreaCity getByNameAndParent(String name, Long pid, Integer deep){
        return selectOne(new LambdaQueryWrapperX<SysAreaCity>()
                .eq(SysAreaCity::getName, name)
                .eqIfPresent(SysAreaCity::getPid, pid)
                .eq(SysAreaCity::getDeep, deep)
        );
    }

    SysAreaCity queryByName(@Param("districtName")String districtName, @Param("cityName")String cityName, @Param("provinceName")String provinceName);
//    {

        // 创建 LambdaQueryWrapper
//        LambdaQueryWrapperX<SysAreaCity> queryWrapper = new LambdaQueryWrapperX<SysAreaCity>();
//
//        // 构建查询条件
//        queryWrapper.eq(SysAreaCity::getName, provinceName)
//                .eq(SysAreaCity::getPid,
//                        Wrappers.lambdaQuery(SysAreaCity.class)
//                                .select(SysAreaCity::getAreaCityId)
//                                .eq(SysAreaCity::getName, cityName)
//                                .eq(SysAreaCity::getPid,
//                                        Wrappers.lambdaQuery(SysAreaCity.class)
//                                                .select(SysAreaCity::getAreaCityId)
//                                                .eq(SysAreaCity::getName, districtName)
//                                )
//                );
//
//        // 执行查询
//        List<SysAreaCity> result = selectList(queryWrapper);
//        return CollectionUtils.isEmpty(result) ? null : result.get(0);
//    }
}
