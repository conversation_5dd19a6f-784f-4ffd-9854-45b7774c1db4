package com.zksr.system.controller.pageConfig.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 平台页面配置对象 sys_pages_config
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Data
@ApiModel("平台页面配置 - 拷贝配置 Request VO")
public class SysPagesConfigCopyReqVO {

    /** 自定义页面ID */
    @ApiModelProperty(value = "页面ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    @NotNull(message = "被复制的模版ID必填")
    private Long pageId;

    /** 页面名称 */
    @Excel(name = "页面名称")
    @ApiModelProperty(value = "页面名称", required = true)
    @Size(max = 64)
    private String pageName;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    @ApiModelProperty(value = "渠道ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private String channelId;

    /** 区域城市ID */
    @Excel(name = "区域城市ID")
    @ApiModelProperty(value = "区域城市ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("有效开始时间")
    private Date startTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("有效结束时间")
    private Date endTime;
}
