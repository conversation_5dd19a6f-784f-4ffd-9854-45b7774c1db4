package com.zksr.system.controller.pageConfig.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台页面配置对象 sys_pages_config
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@ApiModel("平台页面配置 - sys_pages_config分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysPagesConfigPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 自定义页面ID */
    @ApiModelProperty(value = "页面ID")
    private Long pageId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @ApiModelProperty(value = "平台商id;平台商id")
    private Long sysCode;

    /** 页面名称 */
    @Excel(name = "页面名称")
    @ApiModelProperty(value = "页面名称")
    private String pageName;

    /** 页面类型;页面类型,index-首页 */
    @Excel(name = "页面类型;页面类型,index-首页")
    @ApiModelProperty(value = "页面类型;页面类型,index-首页")
    private String pageType;

    /** 页面配置JSON */
    @Excel(name = "页面配置JSON")
    @ApiModelProperty(value = "页面配置JSON")
    private String pageConfig;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    @ApiModelProperty(value = "渠道ID")
    private Long channelId;

    /** 区域城市ID */
    @Excel(name = "区域城市ID")
    @ApiModelProperty(value = "区域城市ID")
    private Long areaId;

    /** 是否默认 (0非默认 1默认) */
    @Excel(name = "是否默认 (0非默认 1默认)")
    @ApiModelProperty(value = "是否默认 (0非默认 1默认)")
    private String defFlag;

    /** 状态 (0正常 1停用) */
    @Excel(name = "状态 (0正常 1停用)")
    @ApiModelProperty(value = "状态 (0正常 1停用)")
    private String status;

    /** 父级页面ID */
    @Excel(name = "父级页面ID")
    @ApiModelProperty(value = "父级页面ID")
    private Long pid;

    /** 1-一级页面, 2-二级页面 */
    @Excel(name = "1-一级页面, 2-二级页面")
    @ApiModelProperty(value = "1-一级页面, 2-二级页面")
    private Integer level;

    @ApiModelProperty("0-固定模版, 1-时效模版")
    private Integer type;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("有效开始时间")
    private Date startTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("有效结束时间")
    private Date endTime;
}
