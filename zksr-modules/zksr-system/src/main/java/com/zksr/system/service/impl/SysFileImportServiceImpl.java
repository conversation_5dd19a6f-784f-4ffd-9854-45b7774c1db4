package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.BeanUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.file.FileApi;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.form.SysFileImportForm;
import com.zksr.system.mapper.SysFileImportDtlMapper;
import com.zksr.system.mapper.SysFileImportMapper;
import com.zksr.system.mapper.SysPartnerDictDataMapper;
import com.zksr.system.mq.SystemMqProducer;
import com.zksr.system.service.ISysFileImportService;
import com.zksr.system.service.ISysPartnerDictDataService;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 导入记录 业务层处理
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class SysFileImportServiceImpl implements ISysFileImportService
{

    @Resource
    private SysFileImportMapper sysFileImportMapper;
    @Autowired
    private FileApi fileApi;
    @Autowired
    private SystemMqProducer systemMqProducer;
    @Resource
    private SysFileImportDtlMapper sysFileImportDtlMapper;


    /**
     * 创建导入任务
     * @param
     * @return
     */
    @Transactional
    public SysFileImport createFileImportTask(MultipartFile file,String importType) throws Exception {
        if (!isHaveGoingEvent(importType)){
            throw new RuntimeException("该导入类型已有导入任务在运行中，请稍后");
        }
        SysFileImport fileImport = new SysFileImport();
        fileImport.setSysCode(SecurityContextHolder.getSysCode());
        fileImport.setFileName(file.getOriginalFilename());
        fileImport.setUserId(SecurityUtils.getUserId());
        fileImport.setCreateBy(SecurityUtils.getUsername());
        fileImport.setCreateTime(new Date());
        fileImport.setImportType(importType);
        fileImport.setImportStatus(StatusConstants.STATUS_GOING);
        //上传文件
        CommonResult<String> stringCommonResult = fileApi.uploadFileByMail(file);
        if (stringCommonResult.isSuccess()&&StringUtils.isNotEmpty(stringCommonResult.getData())){
            fileImport.setFileUrl(stringCommonResult.getData());
        }else {
            throw new RuntimeException("文件上传失败");
        }
        fileImport.setMqSendNum(1);
        fileImport.setMqReceiveNum(0);
        int insert = sysFileImportMapper.insert(fileImport);
        //发送mq
        SysFileImportVO sysFileImportVO = new SysFileImportVO();
        BeanUtils.copyProperties(fileImport,sysFileImportVO);
        sysFileImportVO.setDcId(SecurityUtils.getDcId());
        sysFileImportVO.setFuncScop(SecurityUtils.getLoginUser().getFuncScop());
        sysFileImportVO.setSupplierId(SecurityUtils.getSupplierId());
        systemMqProducer.sendFileImportTaskEvent(sysFileImportVO);


        return fileImport;
    }

    /**
     * 查询指定类型进行中任务
     * @param importType
     * @return
     */
    public List<SysFileImport> findGoingEventByType(String importType){
        return sysFileImportMapper.selectList(new LambdaQueryWrapper<SysFileImport>().eq(SysFileImport::getImportType,importType).eq(SysFileImport::getImportStatus,2).eq(SysFileImport::getUserId,SecurityUtils.getUserId()));
    }

    public List<SysFileImport> findGoingEvent(){
        return sysFileImportMapper.selectList(new LambdaQueryWrapper<SysFileImport>().eq(SysFileImport::getImportStatus,StatusConstants.STATUS_GOING));
    }

    /**
     * 查询指定任务是否在运行
     * @param importType
     * @return
     */
    public Boolean isHaveGoingEvent(String importType){
        List<SysFileImport> goingEvent = findGoingEventByType(importType);
        Boolean f = true;
        for (SysFileImport sysFileImport : goingEvent) {
            long remainSecond = DateUtils.getRemainSecond(sysFileImport.getCreateTime(), new Date());
            if (remainSecond<1200l){
                f = false;
            }else {
                sysFileImport.setImportStatus(StatusConstants.STATUS_FAIL);
                updateSysFileImport(sysFileImport);
            }
        }

        return f;
    }

    /**
     * 增加接收mq
     * @param sysFileImport
     * @return
     */
    public SysFileImport addMqReceiveNum(SysFileImport sysFileImport){
        sysFileImport.setMqReceiveNum(sysFileImport.getMqReceiveNum()+1);
        sysFileImportMapper.update(sysFileImport,new LambdaUpdateWrapper<SysFileImport>().eq(SysFileImport::getFileImportId,sysFileImport.getFileImportId()));
        return sysFileImport;
    }


    public SysFileImport getById(Long fileImportId){
        return sysFileImportMapper.selectOne(new LambdaQueryWrapper<SysFileImport>().eq(SysFileImport::getFileImportId,fileImportId));
    }

    /**
     * 修改
     * @param sysFileImport
     * @return
     */
    public Integer updateSysFileImport(SysFileImport sysFileImport){
        return sysFileImportMapper.update(sysFileImport,new LambdaUpdateWrapper<SysFileImport>().eq(SysFileImport::getFileImportId,sysFileImport.getFileImportId()));
    }

    /**
     * 修改状态
     * @param id
     * @param status
     * @return
     */
    public Integer updateImportStatus(Long id,Integer status){
        return sysFileImportMapper.update(null,new LambdaUpdateWrapper<SysFileImport>().set(SysFileImport::getImportStatus,status).eq(SysFileImport::getFileImportId,id));
    }

    public List<SysFileImport> getList(SysFileImportForm form){
        return sysFileImportMapper.selectList(new LambdaQueryWrapper<SysFileImport>().eq(SysFileImport::getSysCode,SecurityContextHolder.getSysCode())
                .eq(StringUtils.isNotEmpty(form.getImportType()),SysFileImport::getImportType,form.getImportType())
                .eq(ObjectUtil.isNotNull(form.getImportStatus()),SysFileImport::getImportStatus,form.getImportStatus())
                .like(StringUtils.isNotEmpty(form.getFileName()),SysFileImport::getFileName,form.getFileName())
                .like(StringUtils.isNotEmpty(form.getCreateBy()),SysFileImport::getCreateBy,form.getCreateBy())
                .between((ObjectUtil.isNotNull(form.getStartTime())&&ObjectUtil.isNotNull(form.getEndTime())),SysFileImport::getCreateTime,form.getStartTime(),form.getEndTime())
                .orderByDesc(SysFileImport::getCreateTime));
    }

    /**
     * 消息重发
     * @param fileImportId
     * @return
     */
    public SysFileImport eventRetry(Long fileImportId){
        SysFileImport fileImport = getById(fileImportId);
        if (!isHaveGoingEvent(fileImport.getImportType())){
            throw new RuntimeException("该导入类型已有导入任务在运行中，请稍后");
        }
        fileImport.setMqSendNum(fileImport.getMqSendNum()+1);
        sysFileImportMapper.updateById(fileImport);
        sysFileImportDtlMapper.delete(new LambdaQueryWrapper<SysFileImportDtl>().eq(SysFileImportDtl::getFileImportId,fileImportId));

        SysFileImportVO sysFileImportVO = new SysFileImportVO();
        BeanUtils.copyProperties(fileImport,sysFileImportVO);
        sysFileImportVO.setDcId(SecurityUtils.getDcId());
        sysFileImportVO.setFuncScop(SecurityUtils.getLoginUser().getFuncScop());
        sysFileImportVO.setSupplierId(SecurityUtils.getSupplierId());
        systemMqProducer.sendFileImportTaskEvent(sysFileImportVO);

        return fileImport;
    }

}
