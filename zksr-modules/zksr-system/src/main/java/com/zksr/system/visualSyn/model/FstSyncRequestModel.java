package com.zksr.system.visualSyn.model;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.erpUtils.SecretUtil;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.api.visual.dto.VisualSettingTemplateDto;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS_SSS;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_HEADER_DATA_ERR;
import static com.zksr.system.enums.ErrorCodeConstants.SYNC_ASSEMBLE_VERIFY_DATA_ERR;

/**
*  对接福商通业务抽象子类
* @date 2024/10/25 15:43
* <AUTHOR>
*/
@Slf4j
public class FstSyncRequestModel<SYNCDTO> extends SyncRequestModel<SYNCDTO>{
    public FstSyncRequestModel(SyncDataDTO data, VisualSettingMasterDto visualMasterDto, VisualSettingDetailDto visualDetailDto, VisualSettingTemplateDto visualTemplateDto, OpensourceDto opensourceDto, SYNCDTO syncdto) {
        super(data, visualMasterDto, visualDetailDto,visualTemplateDto,opensourceDto, syncdto);
    }

    @Override
    public String assembleHeader(){
        try{
            Map<String, Object> header = new HashMap<>();
            header.put("strategyId", opensourceDto.getSendCode());
            return JSON.toJSONString(header);
        }catch (Exception e){
            log.error("对外推送数据--组装请求头数据失败，失败原因：", e);
            throw exception(SYNC_ASSEMBLE_HEADER_DATA_ERR,e.getMessage());
        }
    }

    @Override
    public String assembleVerify(String body){
        try{
            String verifyData = null;
            //RAS加密方式
            //body数据转Map
            Map bodyMap = JSONObject.parseObject(body, Map.class);
            Map<String, Object> reqMap = new HashMap<>();
            //组装公共数据
            reqMap.put("reqId", data.getReqId());
            reqMap.put("reqTime", DateUtils.dateTimeNow(YYYY_MM_DD_HH_MM_SS_SSS));
            reqMap.put("requestType", B2BRequestType.matchingTemplateType(data.getTemplateType()));
            reqMap.put("strategyId", opensourceDto.getSendCode());

            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("type", OperationType.findByCode(data.getOperationTypeCode()));
            dataModel.put("data", bodyMap);
            reqMap.put("dataModel", dataModel);

            //组装加密数据
            reqMap.put("bizData", (SecretUtil.encrypt(dataModel, opensourceDto.getPublicKey())));
            verifyData = JSON.toJSONString(reqMap);

            return verifyData;
        }catch (Exception e){
            log.error(" FstSyncRequestModel.assembleVerify对外推送数据--组装鉴权校验数据失败,", e);
            throw exception(SYNC_ASSEMBLE_VERIFY_DATA_ERR,e.getMessage());
        }
    }
}
