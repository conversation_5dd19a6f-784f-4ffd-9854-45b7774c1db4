package com.zksr.system.api.sms;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.sms.dto.*;
import com.zksr.system.domain.SysSms;
import com.zksr.system.service.ISysSmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 短信服务
 * @date 2024/4/30 15:09
 */
@InnerAuth
@ApiIgnore
@RestController
public class SmsApiImpl implements SmsApi{

    @Autowired
    private ISysSmsService smsService;

    @Override
    public CommonResult<SmsCodeRespDTO> sendSmsCode(SmsCodeReqDTO codeReqDTO) {
        return CommonResult.success(smsService.sendSmsCode(codeReqDTO));
    }

    @Override
    public CommonResult<SmsCodeValidRespDTO> validateSmsCode(SmsCodeValidDTO codeReqDTO) {
        return CommonResult.success(smsService.validateSmsCode(codeReqDTO));
    }
    @Override
    public CommonResult<String> sendNoticeSms(SmsNoticeDTO smsNoticeDTO) {
        SysSms sms = new SysSms();
        sms.setMobile(smsNoticeDTO.getPhone());
        sms.setSysCode(smsNoticeDTO.getSystemCode());
        smsService.sendNoticeSms(sms, smsNoticeDTO.getContext());
        return CommonResult.success("成功");
    }
}