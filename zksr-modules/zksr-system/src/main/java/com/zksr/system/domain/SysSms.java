package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 短信消息对象 sys_sms
 *
 * <AUTHOR>
 * @date 2024-04-30
 */
@TableName(value = "sys_sms")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysSms extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 短信ID */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long smsId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 手机号 */
    @Excel(name = "手机号")
    private String mobile;

    /** 使用场景 */
    @Excel(name = "使用场景")
    private Integer scene;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date useTime;

    /** 发送结果 */
    @Excel(name = "发送结果")
    private String msg;

    /** 消息模版ID */
    @Excel(name = "消息模版ID")
    private Long templateId;

    /** 消息模版参数 */
    @Excel(name = "消息模版参数")
    private String templateParams;

    @Excel(name = "请求IP")
    private String remoteIp;
}
