package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

import java.util.Date;

/**
 * 运营商区域城市拉链表 sys_dc_area_zip
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@TableName(value = "sys_dc_area_zip")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysDcAreaZip extends BaseEntity{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long dcAreaZipId;

    @Excel(name = "平台商ID")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode; // 平台商ID

    @Excel(name = "区域城市ID")
    private Long areaId; // 区域城市ID

    @Excel(name = "运营商id")
    private Long dcId; // 运营商id

    @Excel(name = "开始日期")
    private Date startDate;

    @Excel(name = "结束日期")
    private Date endDate;

}
