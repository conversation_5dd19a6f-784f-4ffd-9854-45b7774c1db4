package com.zksr.system.controller.pageConfig;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplatePageReqVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplateRespVO;
import com.zksr.system.controller.pageConfig.vo.SysPagesConfigTemplateSaveReqVO;
import com.zksr.system.convert.pageConfig.SysPagesConfigTemplateConvert;
import com.zksr.system.domain.SysPagesConfigTemplate;
import com.zksr.system.service.ISysPagesConfigTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台页面配置模版Controller
 *
 * <AUTHOR>
 * @date 2024-11-06
 */
@Api(tags = "管理后台 - 平台页面配置模版接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/pageConfigTemplate")
public class SysPagesConfigTemplateController {

    @Autowired
    private ISysPagesConfigTemplateService sysPagesConfigTemplateService;

    /**
     * 新增平台页面配置模版
     */
    @ApiOperation(value = "新增平台页面配置模版", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台页面配置模版", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysPagesConfigTemplateSaveReqVO createReqVO) {
        return success(sysPagesConfigTemplateService.insertSysPagesConfigTemplate(createReqVO));
    }

    /**
     * 修改平台页面配置模版
     */
    @ApiOperation(value = "修改平台页面配置模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "平台页面配置模版", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysPagesConfigTemplateSaveReqVO updateReqVO) {
        sysPagesConfigTemplateService.updateSysPagesConfigTemplate(updateReqVO);
        return success(true);
    }

    /**
     * 修改页面启用状态
     */
    @ApiOperation(value = "修改页面启用状态", httpMethod = "PUT", notes = "[pageId,status] 必须传入 权限字符:" + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "修改页面启用状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public CommonResult<Boolean> changeStatus(@Valid @RequestBody SysPagesConfigTemplateSaveReqVO updateReqVO) {
        sysPagesConfigTemplateService.updateStatus(updateReqVO);
        return success(true);
    }

    /**
     * 删除平台页面配置模版
     */
    @ApiOperation(value = "删除平台页面配置模版", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "平台页面配置模版", businessType = BusinessType.DELETE)
    @DeleteMapping("/{pageIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] pageIds) {
        sysPagesConfigTemplateService.deleteSysPagesConfigTemplateByPageIds(pageIds);
        return success(true);
    }

    /**
     * 获取平台页面配置模版详细信息
     */
    @ApiOperation(value = "获得平台页面配置模版详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{pageId}")
    public CommonResult<SysPagesConfigTemplateRespVO> getInfo(@PathVariable("pageId") Long pageId) {
        SysPagesConfigTemplate sysPagesConfigTemplate = sysPagesConfigTemplateService.getSysPagesConfigTemplate(pageId);
        return success(SysPagesConfigTemplateConvert.INSTANCE.convert(sysPagesConfigTemplate));
    }

    /**
     * 分页查询平台页面配置模版
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台页面配置模版分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysPagesConfigTemplateRespVO>> getPage(SysPagesConfigTemplatePageReqVO pageReqVO) {
        PageResult<SysPagesConfigTemplate> pageResult = sysPagesConfigTemplateService.getSysPagesConfigTemplatePage(pageReqVO);
        return success(SysPagesConfigTemplateConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:pageConfigTemplate:add";
        /** 编辑 */
        public static final String EDIT = "system:pageConfigTemplate:edit";
        /** 删除 */
        public static final String DELETE = "system:pageConfigTemplate:remove";
        /** 列表 */
        public static final String LIST = "system:pageConfigTemplate:list";
        /** 查询 */
        public static final String GET = "system:pageConfigTemplate:query";
        /** 停用 */
        public static final String DISABLE = "system:pageConfigTemplate:disable";
        /** 启用 */
        public static final String ENABLE = "system:pageConfigTemplate:enable";
    }
}
