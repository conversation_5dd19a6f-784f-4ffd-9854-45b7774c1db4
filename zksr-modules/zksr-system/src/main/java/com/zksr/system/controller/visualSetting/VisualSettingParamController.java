package com.zksr.system.controller.visualSetting;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.service.IVisualSettingParamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 可视化配置参数管理Controller
 *
 * <AUTHOR>
 * @date 2024/7/1:10:32
 */
@Validated
@RestController
@RequestMapping("/visualapi/param")
@Api(tags = "管理后台 - 可视化配置参数管理", produces = "application/json")
public class VisualSettingParamController {

    @Resource
    private IVisualSettingParamService visualSettingParamService;

    // 参数转换

    /**
     * 使用velocity模版替换参数
     *
     * @param paramData        参数数据
     * @param velocityTemplate velocity模版
     * @return {@link String}
     * <AUTHOR>
     * @date 2024/7/1 11:07
     */
    @GetMapping(value = "/replaceParam")
    @RequiresPermissions(VisualSettingParamController.Permissions.REPLACE)
    @ApiOperation(value = "使用velocity模版替换参数", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + VisualSettingParamController.Permissions.REPLACE)
    public CommonResult<String> replaceParam(@PathVariable("paramData") Object paramData, @PathVariable("velocityTemplate") String velocityTemplate) {
        String param = visualSettingParamService.replaceParam(paramData, velocityTemplate);
        return success(param);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 转换参数
         */
        public static final String REPLACE = "system:visualapi:param:replace";
    }

}
