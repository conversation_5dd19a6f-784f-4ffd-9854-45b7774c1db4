package com.zksr.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.core.exception.GlobalException;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterPageReqVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterSaveReqVO;
import com.zksr.system.domain.SysOpensource;
import com.zksr.system.domain.SysSupplier;
import com.zksr.system.domain.VisualSettingMaster;
import com.zksr.system.mapper.SysOpensourceMapper;
import com.zksr.system.mapper.SysSupplierMapper;
import com.zksr.system.mapper.VisualSettingMasterMapper;
import com.zksr.system.service.ISysCacheService;
import com.zksr.system.service.IVisualSettingMasterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 可视化配置主Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Service
public class VisualSettingMasterServiceImpl implements IVisualSettingMasterService {
    @Autowired
    private VisualSettingMasterMapper visualSettingMasterMapper;

    @Resource
    @Qualifier("visualSettingMasterCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterCache;

    @Autowired
    private SysSupplierMapper sysSupplierMapper;

    @Autowired
    @Qualifier("visualSettingMasterBySupplierIdCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterBySupplierIdCache;

    @Autowired
    private SysOpensourceMapper sysOpensourceMapper;


    /**
     * 新增可视化配置主
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertVisualSettingMaster(VisualSettingMasterSaveReqVO createReqVO) {
        // 插入
        VisualSettingMaster visualSettingMaster = HutoolBeanUtils.toBean(createReqVO, VisualSettingMaster.class);
        visualSettingMasterMapper.insert(visualSettingMaster);
        // 返回
        return visualSettingMaster.getVisualMasterId();
    }

    /**
     * 修改可视化配置主
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateVisualSettingMaster(VisualSettingMasterSaveReqVO updateReqVO) {
        visualSettingMasterMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, VisualSettingMaster.class));
        visualSettingMasterCache.remove(updateReqVO.getVisualMasterId());

        //删除入驻商ID 对应可视化主表缓存
        removeVisualMasterCacheBySupplier(updateReqVO.getVisualMasterId());

    }

    /**
     * 删除可视化配置主
     *
     * @param visualMasterId id
     */
    @Override
    public void deleteVisualSettingMaster(Long visualMasterId) {
        // 删除
        visualSettingMasterMapper.deleteById(visualMasterId);
        VisualSettingMaster visualSettingMaster = visualSettingMasterMapper.selectById(visualMasterId);
        if (ObjectUtil.isNotEmpty(visualSettingMaster)){
            visualSettingMasterCache.remove(visualSettingMaster.getVisualMasterId());

            //删除入驻商ID 对应可视化主表缓存
            removeVisualMasterCacheBySupplier(visualSettingMaster.getVisualMasterId());
        }
    }

    /**
     * 批量删除可视化配置主
     *
     * @param visualMasterIds 需要删除的可视化配置主主键
     * @return 结果
     */
    @Override
    public void deleteVisualSettingMasterByVisualMasterIds(Long[] visualMasterIds) {
        for(Long visualMasterId : visualMasterIds){
            this.deleteVisualSettingMaster(visualMasterId);
        }
    }

    /**
     * 获得可视化配置主
     *
     * @param visualMasterId id
     * @return 可视化配置主
     */
    @Override
    public VisualSettingMaster getVisualSettingMaster(Long visualMasterId) {
        return visualSettingMasterMapper.selectById(visualMasterId);
    }

    @Override
    public List<VisualSettingMasterRespVO> list(VisualSettingMasterPageReqVO pageReqVO) {
        return visualSettingMasterMapper.list(pageReqVO);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<VisualSettingMaster> getVisualSettingMasterPage(VisualSettingMasterPageReqVO pageReqVO) {
        PageResult<VisualSettingMaster> visualSettingMasterPageResult = visualSettingMasterMapper.selectPage(pageReqVO);
        List<VisualSettingMaster> list = visualSettingMasterPageResult.getList();

        return visualSettingMasterPageResult;
    }

    private void validateVisualSettingMasterExists(Long visualMasterId) {
        if (visualSettingMasterMapper.selectById(visualMasterId) == null) {
            throw new GlobalException("可视化配置主不存在");
        }
    }


    /**
     * 清除与该可视化主表相关的  key：入驻商ID value：可视化主表  缓存
     * @param visualMasterId
     */
    private void removeVisualMasterCacheBySupplier(Long visualMasterId){
        //获取配置了该可视化主表配置的 开发能力信息
        List<SysOpensource> opensourceList = sysOpensourceMapper.getOpensourceListByVisualMasterId(visualMasterId);

        //获取开发能力对应的入驻商ID
        Set<Long> supplierIdList = opensourceList.stream().map(SysOpensource::getMerchantId).collect(Collectors.toSet());

        if(!supplierIdList.isEmpty()){
            visualSettingMasterBySupplierIdCache.removeAll(supplierIdList);
        }
    }

    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 可视化配置主 TODO 补充编号 ==========
    // ErrorCode VISUAL_SETTING_MASTER_NOT_EXISTS = new ErrorCode(TODO 补充编号, "可视化配置主不存在");


}
