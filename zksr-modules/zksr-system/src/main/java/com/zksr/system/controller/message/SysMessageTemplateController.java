package com.zksr.system.controller.message;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.api.commonMessage.vo.SubscribeMsgConfigVO;
import com.zksr.system.controller.message.vo.SysMessageTemplatePageReqVO;
import com.zksr.system.controller.message.vo.SysMessageTemplateRespVO;
import com.zksr.system.controller.message.vo.SysMessageTemplateSaveReqVO;
import com.zksr.system.convert.subscribe.SysMessageTemplateConvert;
import com.zksr.system.domain.SysMessageTemplate;
import com.zksr.system.service.ISysMessageTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 公众号, 小程序订阅消息模版Controller
 *
 * <AUTHOR>
 * @date 2024-06-13
 */
@Api(tags = "管理后台 - 公众号, 小程序订阅消息模版接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/subscribeTemplate")
public class SysMessageTemplateController {

    @Autowired
    private ISysMessageTemplateService sysMessageTemplateService;

    /**
     * 新增公众号, 小程序订阅消息模版
     */
    @ApiOperation(value = "新增公众号, 小程序订阅消息模版", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "公众号, 小程序订阅消息模版", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody SysMessageTemplateSaveReqVO createReqVO) {
        return success(sysMessageTemplateService.insertSysMessageTemplate(createReqVO));
    }

    /**
     * 修改公众号, 小程序订阅消息模版
     */
    @ApiOperation(value = "修改公众号, 小程序订阅消息模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "公众号, 小程序订阅消息模版", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody SysMessageTemplateSaveReqVO updateReqVO) {
        sysMessageTemplateService.updateSysMessageTemplate(updateReqVO);
        return success(true);
    }

    /**
     * 删除公众号, 小程序订阅消息模版
     */
    @ApiOperation(value = "删除公众号, 小程序订阅消息模版", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "公众号, 小程序订阅消息模版", businessType = BusinessType.DELETE)
    @DeleteMapping("/{messageTemplateIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] messageTemplateIds) {
        sysMessageTemplateService.deleteSysMessageTemplateByMessageTemplateIds(messageTemplateIds);
        return success(true);
    }

    /**
     * 获取公众号, 小程序订阅消息模版详细信息
     */
    @ApiOperation(value = "获得公众号, 小程序订阅消息模版详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{messageTemplateId}")
    public CommonResult<SysMessageTemplateRespVO> getInfo(@PathVariable("messageTemplateId") Long messageTemplateId) {
        SysMessageTemplate sysMessageTemplate = sysMessageTemplateService.getSysMessageTemplate(messageTemplateId);
        SysMessageTemplateRespVO respVO = SysMessageTemplateConvert.INSTANCE.convert(sysMessageTemplate);
        respVO.setMsgConfigVO(JSON.parseObject(sysMessageTemplate.getConfig(), SubscribeMsgConfigVO.class));
        return success(respVO);
    }

    /**
     * 分页查询公众号, 小程序订阅消息模版
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得公众号, 小程序订阅消息模版分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<SysMessageTemplateRespVO>> getPage(@Valid SysMessageTemplatePageReqVO pageReqVO) {
        PageResult<SysMessageTemplate> pageResult = sysMessageTemplateService.getSysMessageTemplatePage(pageReqVO);
        return success(SysMessageTemplateConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 停用模版
     */
    @ApiOperation(value = "停用模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "公众号, 小程序订阅消息模版", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public CommonResult<Boolean> disable(@ApiParam(name = "messageTemplateId", value = "订阅消息模版ID", required = true) @RequestParam("messageTemplateId") Long messageTemplateId) {
        sysMessageTemplateService.disable(messageTemplateId);
        return success(true);
    }

    /**
     * 启用模版
     */
    @ApiOperation(value = "启用模版", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "公众号, 小程序订阅消息模版", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public CommonResult<Boolean> enable(@ApiParam(name = "messageTemplateId", value = "订阅消息模版ID", required = true) @RequestParam("messageTemplateId") Long messageTemplateId) {
        sysMessageTemplateService.enable(messageTemplateId);
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "system:subscribe-template:add";
        /** 编辑 */
        public static final String EDIT = "system:subscribe-template:edit";
        /** 删除 */
        public static final String DELETE = "system:subscribe-template:remove";
        /** 列表 */
        public static final String LIST = "system:subscribe-template:list";
        /** 查询 */
        public static final String GET = "system:subscribe-template:query";
        /** 停用 */
        public static final String DISABLE = "system:subscribe-template:disable";
        /** 启用 */
        public static final String ENABLE = "system:subscribe-template:enable";
    }
}
