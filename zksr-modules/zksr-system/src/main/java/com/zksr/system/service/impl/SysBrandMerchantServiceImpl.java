package com.zksr.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.vo.BindBrandMerchantReqVO;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.controller.brand.vo.SysBrandMerchantPageReqVO;
import com.zksr.system.controller.brand.vo.SysBrandMerchantSaveReqVO;
import com.zksr.system.convert.brand.SysBrandMerchantConvert;
import com.zksr.system.domain.SysBrandMember;
import com.zksr.system.domain.SysBrandMerchant;
import com.zksr.system.enums.SysRoleKeyEnum;
import com.zksr.system.mapper.SysBrandMerchantMapper;
import com.zksr.system.mapper.SysRoleMapper;
import com.zksr.system.service.ISysBrandMemberService;
import com.zksr.system.service.ISysBrandMerchantService;
import com.zksr.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.SYS_DC_ROLE_NULL;

/**
 * 品牌商资料Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-05
 */
@Service
public class SysBrandMerchantServiceImpl implements ISysBrandMerchantService {

    @Autowired
    private SysBrandMerchantMapper sysBrandMerchantMapper;

    @Autowired
    private ISysBrandMemberService sysBrandMemberService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Resource
    private BrandApi brandApi;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    /**
     * 新增品牌商资料
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertSysBrandMerchant(SysBrandMerchantSaveReqVO createReqVO) {
        // 插入
        SysBrandMerchant sysBrandMerchant = SysBrandMerchantConvert.INSTANCE.convert(createReqVO);
        sysBrandMerchantMapper.insert(sysBrandMerchant);
        // 创建管理员账户
        createAdminUser(createReqVO, sysBrandMerchant);
        // 绑定品牌
        String brandIds = createReqVO.getBrandIds();
        if (StringUtils.isNotEmpty(brandIds)) {
            BindBrandMerchantReqVO reqVO = new BindBrandMerchantReqVO();
            reqVO.setBrandMerchantId(sysBrandMerchant.getBrandMerchantId());
            reqVO.setBrandList(
                    Arrays.stream(brandIds.split(StringPool.COMMA)).map(Long::parseLong).collect(Collectors.toList())
            );
            brandApi.bindBrandMerchant(reqVO).checkError();
        }
        // 返回
        return sysBrandMerchant.getBrandMerchantId();
    }

    /**
     * 修改品牌商资料
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional
    public void updateSysBrandMerchant(SysBrandMerchantSaveReqVO updateReqVO) {
        SysBrandMerchant source = sysBrandMerchantMapper.selectById(updateReqVO.getBrandMerchantId());

        SysBrandMerchant merchant = SysBrandMerchantConvert.INSTANCE.convert(updateReqVO);
        // 修改密码
//        userService.changePass(source.getSysUserId(), updateReqVO.getPassword());
        // 更新信息
        sysBrandMerchantMapper.updateById(merchant);
        if (Objects.nonNull(source.getSysUserId())) {
            SysUser sysUser = userService.getById(source.getSysUserId());
            sysUser.setBrandId(merchant.getBrandIds());
            if (!saasAuthSwitch && StringUtils.isNotEmpty(updateReqVO.getPassword())) {
                sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPassword()));
            }
            sysUser.setStatus(updateReqVO.getStatus()+"");
            sysUser.setPhonenumber(updateReqVO.getContactPhone());
            sysUser.setUpdateBy(SecurityUtils.getUsername());
            userService.updateUserStatus(sysUser);
        }
        // 绑定品牌
        String brandIds = updateReqVO.getBrandIds();
        if (StringUtils.isNotEmpty(brandIds)) {
            BindBrandMerchantReqVO reqVO = new BindBrandMerchantReqVO();
            reqVO.setBrandMerchantId(updateReqVO.getBrandMerchantId());
            reqVO.setBrandList(
                    Arrays.stream(brandIds.split(StringPool.COMMA)).map(Long::parseLong).collect(Collectors.toList())
            );
            brandApi.bindBrandMerchant(reqVO).checkError();
        }
    }

    /**
     * 删除品牌商资料
     *
     * @param brandMerchantId ${pkColumn.columnComment}
     */
    @Override
    public void deleteSysBrandMerchant(Long brandMerchantId) {
        // 删除
        sysBrandMerchantMapper.deleteById(brandMerchantId);
    }

    /**
     * 批量删除品牌商资料
     *
     * @param brandMerchantIds 需要删除的品牌商资料主键
     * @return 结果
     */
    @Override
    public void deleteSysBrandMerchantByBrandMerchantIds(Long[] brandMerchantIds) {
        for (Long brandMerchantId : brandMerchantIds) {
            this.deleteSysBrandMerchant(brandMerchantId);
        }
    }

    /**
     * 获得品牌商资料
     *
     * @param brandMerchantId ${pkColumn.columnComment}
     * @return 品牌商资料
     */
    @Override
    public SysBrandMerchant getSysBrandMerchant(Long brandMerchantId) {
        return sysBrandMerchantMapper.selectById(brandMerchantId);
    }

    /**
     * 查询分页数据
     *
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<SysBrandMerchant> getSysBrandMerchantPage(SysBrandMerchantPageReqVO pageReqVO) {
        SysUser user = SecurityUtils.getLoginUser().getSysUser();
        if (StringUtils.isNotEmpty(user.getBrandId())) {
            SysBrandMerchant brandMerchant = this.getSysBrandMerchantByUserId(SecurityUtils.getUserId());
            SysBrandMember brandMember = sysBrandMemberService.getSysBrandMemberByUserId(SecurityUtils.getUserId());
            if (Objects.nonNull(brandMerchant)) {
                pageReqVO.setBrandMerchantId(brandMerchant.getBrandMerchantId());
            }
            if (Objects.nonNull(brandMember)) {
                pageReqVO.setBrandMerchantId(brandMember.getBrandMerchantId());
            }
        }
        return sysBrandMerchantMapper.selectPage(pageReqVO);
    }

    @Override
    public SysBrandMerchant getSysBrandMerchantByUserId(Long sysUserId) {
        return sysBrandMerchantMapper.selectByUserId(sysUserId);
    }

    @Override
    @Transactional
    public void disable(Long brandMerchantId) {
        // 修改品牌资料状态
        SysBrandMerchant brandMerchant = sysBrandMerchantMapper.selectById(brandMerchantId);
        brandMerchant.setStatus(NumberPool.INT_ONE);
        sysBrandMerchantMapper.updateById(brandMerchant);

        // 修改管理员账户
        SysUser sysUser = userService.selectUserById(brandMerchant.getSysUserId());
        sysUser.setStatus(StringPool.ONE);
        userService.updateUserStatus(sysUser);
    }

    @Override
    @Transactional
    public void enable(Long brandMerchantId) {
        // 修改品牌资料状态
        SysBrandMerchant brandMerchant = sysBrandMerchantMapper.selectById(brandMerchantId);
        brandMerchant.setStatus(NumberPool.INT_ZERO);
        sysBrandMerchantMapper.updateById(brandMerchant);

        // 修改管理员账户
        SysUser sysUser = userService.selectUserById(brandMerchant.getSysUserId());
        sysUser.setStatus(StringPool.ZERO);
        userService.updateUserStatus(sysUser);
    }

    private void createAdminUser(SysBrandMerchantSaveReqVO createReqVO, SysBrandMerchant sysBrandMerchant) {
        Long sysCode = SecurityUtils.getLoginUser().getSysCode();
        SysUser saveUser = new SysUser();
        // 完善账号信息
        saveUser.setNickName(saasAuthSwitch ?  createReqVO.getContactName() : String.format("【%s】%s", createReqVO.getSimpleName(), createReqVO.getContactName()));
        saveUser.setUserName(createReqVO.getUsername());
        saveUser.setStatus(StringPool.ZERO);
        saveUser.setPassword(saasAuthSwitch ? "" : SecurityUtils.encryptPassword(createReqVO.getPassword()));
        saveUser.setPhonenumber(createReqVO.getContactPhone());
        saveUser.setRemark(String.format("%s默认品牌商管理员用户", createReqVO.getSimpleName()));
        saveUser.setCreateBy(SecurityUtils.getUsername());
        saveUser.setCreateTime(DateUtil.date());
        if (!userService.checkUserNameUnique(saveUser)) {
            throw new RuntimeException("新增用户'" + saveUser.getUserName() + "'失败，登录账号已存在");
        }
        // 角色在创建平台商的时候已经创建好了
        // 见 com.zksr.system.service.ISysRoleService.createPartnerAdminRole
        // 现在只需要查询到角色具体ID, 赋权即可
        SysRole sysRole = sysRoleMapper.selectBySysCodeAndScope(sysCode, SysRoleKeyEnum.BRAND_ADMIN_ROLE.getRoleKey() + "-" + sysCode);
        if (Objects.isNull(sysRole)) {
            // 权限不存在
            throw exception(SYS_DC_ROLE_NULL);
        }
        Long roleId = sysRole.getRoleId();
        Long[] roleIds = {roleId};// 自动赋管理员权限
        saveUser.setRoleIds(roleIds);
        saveUser.setSysCode(sysCode);
        saveUser.setBrandId(createReqVO.getBrandIds());
        userService.insertUser(saveUser);
        //保存用户id字段
        sysBrandMerchant.setSysUserId(saveUser.getUserId());
        sysBrandMerchantMapper.updateById(sysBrandMerchant);
    }

}
