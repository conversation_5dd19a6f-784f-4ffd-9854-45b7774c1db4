package com.zksr.system.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.system.domain.SysArea;
import com.zksr.system.domain.SysDcAreaZip;
import org.apache.ibatis.annotations.Mapper;


/**
 * 运营商区域城市拉链表 Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Mapper
public interface SysDcAreaZipMapper extends BaseMapperX<SysDcAreaZip> {
    default SysDcAreaZip selectSysDcAreaZip(SysArea sysArea){
        LambdaQueryWrapper<SysDcAreaZip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysDcAreaZip::getSysCode, sysArea.getSysCode())
                    .eq(SysDcAreaZip::getAreaId, sysArea.getAreaId())
                    .orderByDesc(SysDcAreaZip::getStartDate)
                    .last("LIMIT 1");
        return selectOne(queryWrapper);
    }

    void updateSysDcAreaZip(SysDcAreaZip currentRelation);
}
