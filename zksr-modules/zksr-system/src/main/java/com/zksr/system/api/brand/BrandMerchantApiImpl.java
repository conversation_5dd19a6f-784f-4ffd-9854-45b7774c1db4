package com.zksr.system.api.brand;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.brand.vo.SysBrandMerchantRespVO;
import com.zksr.system.convert.brand.SysBrandMerchantConvert;
import com.zksr.system.domain.SysBrandMember;
import com.zksr.system.domain.SysBrandMerchant;
import com.zksr.system.service.ISysBrandMemberService;
import com.zksr.system.service.ISysBrandMerchantService;
import com.zksr.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 品牌商impl
 * @date 2024/8/5 16:26
 */
@RestController
@ApiIgnore
public class BrandMerchantApiImpl implements BrandMerchantApi{

    @Autowired
    private ISysBrandMerchantService sysBrandMerchantService;

    @Autowired
    private ISysBrandMemberService sysBrandMemberService;

    @Autowired
    private ISysUserService sysUserService;

    @Override
    public CommonResult<SysBrandMerchantRespVO> getByUser(Long sysUserId) {
        SysBrandMerchant sysBrandMerchant = sysBrandMerchantService.getSysBrandMerchantByUserId(sysUserId);
        SysBrandMember sysBrandMember = sysBrandMemberService.getSysBrandMemberByUserId(SecurityUtils.getUserId());
        if (Objects.isNull(sysBrandMerchant) && Objects.nonNull(sysBrandMember)) {
            sysBrandMerchant = sysBrandMerchantService.getSysBrandMerchant(sysBrandMember.getBrandMerchantId());
        }
        SysBrandMerchantRespVO respVO = SysBrandMerchantConvert.INSTANCE.convert(sysBrandMerchant);
        respVO.setUsername(sysUserService.getBySysUserId(sysUserId));
        return success(respVO);
    }
}
