package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.domain.SysUserColumn;
import com.zksr.system.controller.column.vo.SysUserColumnPageReqVO;
import com.zksr.system.controller.column.vo.SysUserColumnSaveReqVO;

import java.util.List;

/**
 * 用户头列配置Service接口
 *
 * <AUTHOR>
 * @date 2024-08-14
 */
public interface ISysUserColumnService {

    /**
     * 新增用户头列配置
     *
     * @param createReqVOList 创建信息
     * @return 结果
     */
    public List<Long> insertSysUserColumnBatch(@Valid List<SysUserColumnSaveReqVO> createReqVOList);


    /**
     * 批量删除用户头列配置
     *
     * @param userColumnIds 需要删除的用户头列配置主键集合
     * @return 结果
     */
    public void deleteSysUserColumnByUserColumnIds(Long[] userColumnIds);


    /**
     * 获得用户头列配置分页
     *
     * @param pageReqVO 分页查询
     * @return 用户头列配置分页
     */
    List<SysUserColumn> getSysUserColumnList(SysUserColumnPageReqVO pageReqVO);

    /**
     * 根据表Code删除用户头列配置
     * @param tableCode
     */
    public void deleteSysUserColumnByTableCode(String tableCode, String userName);

}
