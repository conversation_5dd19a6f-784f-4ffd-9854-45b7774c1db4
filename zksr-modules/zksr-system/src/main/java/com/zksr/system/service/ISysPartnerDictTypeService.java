package com.zksr.system.service;

import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysDictType;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.domain.SysPartnerDictType;

import java.util.List;

/**
 * 平台商字典 业务层
 * 
 * <AUTHOR>
 */
public interface ISysPartnerDictTypeService
{

    List<SysPartnerDictType> selectDictTypeList(SysPartnerDictType dictType);

    List<SysPartnerDictType> selectDictTypeAll();

    List<SysPartnerDictData> selectDictDataByType(String dictType,String sysSource);

    SysPartnerDictType selectDictTypeById(Long dictId);


    void deleteDictTypeByIds(Long[] dictIds);

    int insertDictType(SysPartnerDictType dict);

    int updateDictType(SysPartnerDictType dict);

    void resetDictCache();

}
