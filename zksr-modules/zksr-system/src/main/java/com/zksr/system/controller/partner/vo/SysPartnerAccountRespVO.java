package com.zksr.system.controller.partner.vo;

import com.zksr.system.api.partner.vo.SysPartnerRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


/**
 * 平台商信息对象 sys_partner
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
@ApiModel("平台商信息 - sys_partner account VO")
public class SysPartnerAccountRespVO extends SysPartnerRespVO {
    private static final long serialVersionUID = 1L;

    /** 可提现金额 */
    @ApiModelProperty(value = "可提现金额")
    private BigDecimal withdrawableAmt = BigDecimal.ZERO;

    /** 冻结金额 */
    @ApiModelProperty(value = "冻结金额")
    private BigDecimal frozenAmt = BigDecimal.ZERO;

    /** 授信额度 */
    @ApiModelProperty(value = "授信额度")
    private BigDecimal creditAmt = BigDecimal.ZERO;

    /** 账户ID */
    @ApiModelProperty(value = "账户ID")
    private Long accountId;

    /** 支付平台 */
    @ApiModelProperty(value = "支付平台")
    private String payPlatform;
}
