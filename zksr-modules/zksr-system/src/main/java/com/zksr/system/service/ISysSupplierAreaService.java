package com.zksr.system.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.system.api.supplierArea.dto.SupplierAreaDTO;
import com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO;
import com.zksr.system.domain.SysSupplierArea;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaPageReqVO;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaSaveReqVO;

import java.util.List;

/**
 * 入驻商-区域城市关联关系Service接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
public interface ISysSupplierAreaService {

    /**
     * 新增入驻商-区域城市关联关系
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysSupplierArea(@Valid SysSupplierAreaSaveReqVO createReqVO);

    /**
     * 修改入驻商-区域城市关联关系
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysSupplierArea(@Valid SysSupplierAreaSaveReqVO updateReqVO);

    /**
     * 删除入驻商-区域城市关联关系
     *
     * @param sysCode 平台商id
     */
    public void deleteSysSupplierArea(Long sysCode);

    /**
     * 批量删除入驻商-区域城市关联关系
     *
     * @param sysCodes 需要删除的入驻商-区域城市关联关系主键集合
     * @return 结果
     */
    public void deleteSysSupplierAreaBySysCodes(Long[] sysCodes);

    /**
     * 获得入驻商-区域城市关联关系
     *
     * @param sysCode 平台商id
     * @return 入驻商-区域城市关联关系
     */
    public SysSupplierArea getSysSupplierArea(Long sysCode);

    /**
     * 获得入驻商-区域城市关联关系分页
     *
     * @param pageReqVO 分页查询
     * @return 入驻商-区域城市关联关系分页
     */
    PageResult<SysSupplierArea> getSysSupplierAreaPage(SysSupplierAreaPageReqVO pageReqVO);

    /**
     * 获得入驻商-区域城市关系
     * @param areaId
     * @return
     */
    public List<SupplierAreaDTO> getSupplierAreaByAreaId(Long areaId);

    /**
     * 获取电子围栏绑定的入驻商
     * @param areaId 区域id
     * @return
     */
    public List<SupplierDzwlAreaDTO> getDzwlSupplierInfoByAreaId(Long areaId);

    /**
     * 查询入驻商运营区域
     * @param supplierId
     * @return
     */
    List<Long> selectListBySupplierId(Long supplierId);
}
