package com.zksr.system.controller.openability.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 开放能力对象 sys_openability
 *
 * <AUTHOR>
 * @date 2024-04-27
 */
@Data
@ApiModel("开放能力 - sys_openability分页 Request VO")
public class SysOpenabilitySaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 开放能力id */
    @ApiModelProperty(value = "菜单状态")
    private Long openabilityId;

    /** 父id */
    @Excel(name = "父id")
    @ApiModelProperty(value = "父id")
    private Long pid;

    /** 资源类型（数据字典 supplier-入驻商等） */
    @Excel(name = "资源类型", readConverterExp = "数=据字典,s=upplier-入驻商等")
    @ApiModelProperty(value = "资源类型", required = true)
    private String merchantType;

    /** 接口名 */
    @Excel(name = "接口名")
    @ApiModelProperty(value = "接口名", required = true)
    private String abilityName;

    /** 接口key(唯一) */
    @Excel(name = "接口key(唯一)")
    @ApiModelProperty(value = "接口key(唯一)", required = true)
    private String abilityKey;

    /** 菜单状态（0正常 1停用） */
    @Excel(name = "菜单状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "菜单状态")
    private String status;

    /** 单个用户qps限制 */
    @Excel(name = "单个用户qps限制")
    @ApiModelProperty(value = "单个用户qps限制")
    private Integer rateLimit;
}
