package com.zksr.system.controller.channel.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 渠道信息对象 sys_channel
 *
 * <AUTHOR>
 * @date 2024-02-04
 */
@Data
@ApiModel("渠道信息 - sys_channel slected Response VO")
public class SysChannelSelectedRespVO {
    private static final long serialVersionUID = 1L;

    /** 渠道id */
    @ApiModelProperty(value = "备注", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long channelId;

    /** 渠道名 */
    @Excel(name = "渠道名")
    @ApiModelProperty(value = "渠道名", example = "示例值")
    private String channelName;
}
