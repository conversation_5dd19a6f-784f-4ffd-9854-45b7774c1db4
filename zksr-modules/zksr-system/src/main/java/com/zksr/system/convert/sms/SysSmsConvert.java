package com.zksr.system.convert.sms;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.domain.SysSms;
import com.zksr.system.controller.sms.vo.SysSmsRespVO;
import com.zksr.system.controller.sms.vo.SysSmsSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 短信消息 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-04-30
*/
@Mapper
public interface SysSmsConvert {

    SysSmsConvert INSTANCE = Mappers.getMapper(SysSmsConvert.class);

    SysSmsRespVO convert(SysSms sysSms);

    SysSms convert(SysSmsSaveReqVO sysSmsSaveReq);

    PageResult<SysSmsRespVO> convertPage(PageResult<SysSms> sysSmsPage);
}