package com.zksr.system.convert.visualSetting.visualSettingMaster;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterRespVO;
import com.zksr.system.controller.visualSetting.vo.VisualSettingMasterSaveReqVO;
import com.zksr.system.domain.VisualSettingMaster;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 可视化配置主 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/110162124}
* <AUTHOR>
* @date 2024-06-29
*/
@Mapper
public interface VisualSettingMasterConvert {

    VisualSettingMasterConvert INSTANCE = Mappers.getMapper(VisualSettingMasterConvert.class);

    VisualSettingMasterRespVO convert(VisualSettingMaster visualSettingMaster);

    VisualSettingMaster convert(VisualSettingMasterSaveReqVO visualSettingMasterSaveReq);

    PageResult<VisualSettingMasterRespVO> convertPage(PageResult<VisualSettingMaster> visualSettingMasterPage);
}