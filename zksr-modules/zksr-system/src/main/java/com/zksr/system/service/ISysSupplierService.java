package com.zksr.system.service;

import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.common.core.domain.vo.openapi.receive.SysSupplierDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplier.vo.SupplierDropdownReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.controller.supplier.vo.*;
import com.zksr.system.controller.supplierArea.vo.SysSupplierAreaRespVO;
import com.zksr.system.domain.SysSupplier;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 入驻商信息Service接口
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
public interface ISysSupplierService {

    /**
     * 新增入驻商信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertSysSupplier(@Valid SysSupplierSaveReqVO createReqVO);

    /**
     * 修改入驻商信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateSysSupplier(@Valid SysSupplierSaveReqVO updateReqVO);

    /**
     * 入驻商(PC后台) 编辑自己的基础信息
     * @param updateReqVO   更新数据
     */
    void updateSysSupplierShelf(SysSupplierSaveShelfReqVO updateReqVO);

    /**
     * 删除入驻商信息
     *
     * @param supplierId 入驻商id
     */
    public void deleteSysSupplier(Long supplierId);

    /**
     * 批量删除入驻商信息
     *
     * @param supplierIds 需要删除的入驻商信息主键集合
     * @return 结果
     */
    public void deleteSysSupplierBySupplierIds(Long[] supplierIds);

    /**
     * 获得入驻商信息
     *
     * @param supplierId 入驻商id
     * @return 入驻商信息
     */
    public SysSupplierRespVO getSysSupplier(Long supplierId);

    /**
     * 获得入驻商信息分页
     *
     * @param pageReqVO 分页查询
     * @return 入驻商信息分页
     */
    PageResult<SysSupplierRespVO> getSysSupplierPage(SysSupplierPageReqVO pageReqVO);

    /**
     * 根据运营商、平台商查询入驻商信息Map集合
     *
     * @param dcId 运营商ID
     * @return
     */
    public Map<Long, SupplierDTO> getUserInfoByMap(Long dcId, Long supplierNo);

    /**
     * 根据运营商、平台商查询入驻商信息List集合
     *
     * @param dcId 运营商ID
     * @return
     */
    public List<SupplierDTO> getUserInfoByList(Long dcId, Long supplierNo);

    /**
     * 根据运营商、平台商查询入驻商信息
     *
     * @param dcId 运营商ID
     * @return 入驻商信息列表
     */
    Map<Long, SupplierDTO> getlistByUser(Long dcId);

    /**
     * @Description: 获取所有入驻商
     * @Author: liuxingyu
     * @Date: 2024/3/22 14:35
     */
    List<SysSupplier> getSupplierList(SysSupplier sysSupplier);

    /**
     * 获得入驻商
     *
     * @param supplierId 入驻商id
     * @return 区域城市
     */
    public SysSupplier getBySupplierId(Long supplierId);

    /**
     * 根据运营商、平台商查询入驻商信息List集合
     *
     * @param
     * @return
     */
    public List<SysSupplierRespVO> getSupplierListByDcId();

    /**
     * 批量获取入驻商信息 ( 暂时用于选中数据回显 )
     *
     * @param supplierIds 入驻商ID集合
     * @return 入驻商集合
     */
    List<SysSupplierRespVO> getSelectedSysSupplier(List<Long> supplierIds);

    /**
     * @Description: 根据运营商ID获取区域下所有的入驻商
     * @Author: liuxingyu
     * @Date: 2024/4/9 17:32
     */
    List<Long> getByOrder(Long dcId);

    /**
    * @Description: 获取入驻商下拉选
    * @Author: liuxingyu
    * @Date: 2024/4/12 9:34
    */
    PageResult<SysSupplierRespVO> getSupplierDropdown(SupplierDropdownReqVO reqVO);

    /**
    * @Description: 根据区域获取绑定的入驻商ID
    * @Author: liuxingyu
    * @Date: 2024/4/12 14:59
    */
    List<SysSupplierAreaRespVO> sysSupplierService(Long areaId);

    /**
     * 获取入驻商账户余额
     * @param pageReqVO
     * @return
     */
    PageResult<SysSupplierAccountPageReqVO> getSysSupplierAccountPage(SysSupplierPageReqVO pageReqVO);

    CustomApiMaster getCustomApiMaster(@RequestBody CustomApiMaster customApiMaster);

    List<CustomApiDetail> getCustomApiDetail(@RequestParam("apiNo") String apiNo);

    /**
     * 入驻商绑定公众号openid
     * @param code
     */
    void bindPublishCode(String code);

    /**
     * 解绑入驻商绑定公众号
     * @param supplierId 入驻商ID
     */
    void unbindPublishCode(Long supplierId);

    /**
     * 修改入驻商状态
     * @param updateReqVO
     */
    void changeStatus(SysSupplierStatusReqVO updateReqVO);

    /**
     * 第三方调用新增入驻商信息
     *
     * @param sysSupplierDTO 创建信息
     * @return 结果
     */
    public Long saveSysSupplier(@RequestParam("supplierId") Long supplierId,@Valid SysSupplierDTO sysSupplierDTO);

    /**
     * 根据入驻商ID 查询入驻商名称
     * @param supplierIds
     * @return
     */
    List<String> getSupplierNames(List<Long> supplierIds);

    /**
     * 校验该入驻商是否对接第三方系统
     *
     * @param supplierId 入驻商id
     * @return
     */
    public Boolean checkSyncConfig(Long supplierId);
    public List<SysSupplier> getSupplierListByIds(List<Long> supplierIds);

    void updateSysSupplierPassword(SysSupplierSaveReqVO updateReqVO);

    PageResult<SupplierDTO> getSupplierPage(SysSupplierPageReqVO pageReqVO);
}
