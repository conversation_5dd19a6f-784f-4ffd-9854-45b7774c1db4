package com.zksr.system.chin.partnerConfig;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.system.api.partnerConfig.dto.WxB2bPayConfigDTO;
import com.zksr.system.chin.general.PartnerConfigUtil;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigRespVO;
import com.zksr.system.controller.partnerConfig.vo.SysPartnerConfigSaveReqVO;
import com.zksr.system.domain.SysPartnerConfig;
import com.zksr.system.mapper.SysPartnerConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class ConfigWxB2bHandler extends ConfigPartnerChin {
    @Autowired
    private SysPartnerConfigMapper sysPartnerConfigMapper;

    @Autowired
    private Cache<Long, WxB2bPayConfigDTO> wxB2bPayConfigDTOCache;

    private static final String key = "wxB2bPayConfig.";

    @Override
    public void saveConfig(SysPartnerConfigSaveReqVO sysPartnerConfigSaveReqVO, Long sysCode) {
        if (ObjectUtil.isNotNull(sysPartnerConfigSaveReqVO.getWxB2bPayConfigDTO())) {
            //获取参数数据集合
            List<SysPartnerConfig> sysPartnerConfigs =
                    PartnerConfigUtil.jointConfigParam(sysCode, key, sysPartnerConfigSaveReqVO.getWxB2bPayConfigDTO());
            //根据平台ID 获取所有配置信息
            List<SysPartnerConfig> configList = sysPartnerConfigMapper.selectBySysCode(sysCode);
            //需要修改的数据
            List<SysPartnerConfig> updateList = sysPartnerConfigs
                    .stream()
                    .filter(a -> configList.stream().anyMatch(b -> ObjectUtil.equal(a.getConfigKey(), b.getConfigKey())))
                    .map(a -> {
                        for (SysPartnerConfig sysPartnerConfig : configList) {
                            if (ObjectUtil.equal(sysPartnerConfig.getConfigKey(), a.getConfigKey())) {
                                a.setPartnerConfigId(sysPartnerConfig.getPartnerConfigId());
                                return a;
                            }
                        }
                        return a;
                    })
                    .collect(Collectors.toList());
            //需要新增的数据
            List<SysPartnerConfig> insertList = sysPartnerConfigs
                    .stream()
                    .filter(a -> configList.stream().noneMatch(b -> ObjectUtil.equal(a.getConfigKey(), b.getConfigKey())))
                    .collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(updateList)) {
                sysPartnerConfigMapper.updateBatch(updateList);
            }
            if (ObjectUtil.isNotEmpty(insertList)) {
                sysPartnerConfigMapper.insertBatch(insertList);
            }
            //删除缓存
            wxB2bPayConfigDTOCache.remove(sysCode);
        }
        if (ObjectUtil.isNotNull(next)) {
            next.saveConfig(sysPartnerConfigSaveReqVO, sysCode);
        }
    }

    @Override
    public SysPartnerConfigRespVO getConfig(List<SysPartnerConfig> configList, SysPartnerConfigRespVO sysPartnerConfigRespVO, Long sysCode) {
        if (Objects.isNull(sysPartnerConfigRespVO.getWxB2bPayConfigDTO())){
            List<SysPartnerConfig> heLiBaoList = PartnerConfigUtil.matchingConfigKey(configList, key);
            if (ObjectUtil.isNotEmpty(heLiBaoList)) {
                WxB2bPayConfigDTO heLiBaoPayConfigDto = PartnerConfigUtil.encapsulationConfigObj(heLiBaoList, key, WxB2bPayConfigDTO.class);
                sysPartnerConfigRespVO.setWxB2bPayConfigDTO(heLiBaoPayConfigDto);
                //同步缓存
                wxB2bPayConfigDTOCache.put(sysCode, heLiBaoPayConfigDto);
            }
        }
        if (ObjectUtil.isNotNull(next)) {
            next.getConfig(configList, sysPartnerConfigRespVO, sysCode);
        }
        return sysPartnerConfigRespVO;
    }

}
