package com.zksr.system.controller.log.vo;

import lombok.*;

import java.io.Serializable;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 同步接口日志对象 sys_interface_log
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
@Data
@ApiModel("同步接口日志 - sys_interface_log Response VO")
public class SysInterfaceLogRespVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty(value = "消息重发失败日志记录")
    private Long id;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 请求编号id */
    @Excel(name = "请求编号id")
    @ApiModelProperty(value = "请求编号id")
    private String reqId;

    /** 来源方 */
    @Excel(name = "来源方")
    @ApiModelProperty(value = "来源方")
    private Integer source;

    /** 接收方 */
    @Excel(name = "接收方")
    @ApiModelProperty(value = "接收方")
    private Integer receive;

    /** 请求数据(推送数据) */
    @Excel(name = "请求数据(推送数据)", readConverterExp = "请求数据(推送数据)")
    @ApiModelProperty(value = "请求数据(推送数据)")
    private String reqData;

    /** 请求状态  0未组装数据  1组装数据推送中/接收中 2推送/接收完成 */
    @Excel(name = "请求状态  0未组装数据  1组装数据推送中/接收中 2推送/接收完成")
    @ApiModelProperty(value = "请求状态  0未组装数据  1组装数据推送中/接收中 2推送/接收完成")
    private Integer reqStatus;

    /** 业务数据（请求数据） */
    @Excel(name = "业务数据", readConverterExp = "请=求数据")
    @ApiModelProperty(value = "业务数据")
    private String bizData;

    /** 请求时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "请求时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "请求时间")
    private Date reqTime;

    /** 状态 -1失败 0待处理 1成功 */
    @Excel(name = "状态 -1失败 0待处理 1成功")
    @ApiModelProperty(value = "状态 -1失败 0待处理 1成功")
    private Integer status;

    /** 处理信息 */
    @Excel(name = "处理信息")
    @ApiModelProperty(value = "处理信息")
    private String message;

    /** 请求类型（接口） */
    @Excel(name = "请求类型", readConverterExp = "接=口")
    @ApiModelProperty(value = "请求类型")
    private Integer requestType;

    /** 入驻商ID */
    @Excel(name = "入驻商ID")
    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    /** 操作类型 */
    @Excel(name = "操作类型")
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /** 重试次数 */
    @Excel(name = "重试次数")
    @ApiModelProperty(value = "重试次数")
    private Long retryCount;

    /** 是否重试 0 否 1 是 */
    @Excel(name = "是否重试 0 否 1 是")
    @ApiModelProperty(value = "是否重试 0 否 1 是")
    private Long isRetry;

    /** 消息重发失败日志记录 */
    @Excel(name = "消息重发失败日志记录")
    @ApiModelProperty(value = "消息重发失败日志记录")
    private String resendMessage;

    /** 日志类型  1、同步数据 2、接收数据 */
    @Excel(name = "日志类型  1、同步数据 2、接收数据")
    private Integer logType;

    /** 开放能力ID */
    @Excel(name = "开放能力ID")
    @ApiModelProperty(value = "开放能力ID")
    private Long opensourceId;

    /** 平台商开放能力ID */
    @Excel(name = "平台商开放能力ID")
    @ApiModelProperty(value = "平台商开放能力ID")
    private Long partnerId;

}
