package com.zksr.system.api.software;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.software.dto.SoftwareDTO;
import com.zksr.system.convert.software.SysSoftwareConvert;
import com.zksr.system.service.ISysSoftwareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/26 15:34
 */
@RestController
@ApiIgnore
public class SoftwareApiImpl implements Software<PERSON>pi{

    @Autowired
    private ISysSoftwareService sysSoftwareService;

    @Override
    public CommonResult<SoftwareDTO> getById(Long softwareId) {
        return success(SysSoftwareConvert.INSTANCE.convertDTO(sysSoftwareService.getSysSoftware(softwareId)));
    }
}
