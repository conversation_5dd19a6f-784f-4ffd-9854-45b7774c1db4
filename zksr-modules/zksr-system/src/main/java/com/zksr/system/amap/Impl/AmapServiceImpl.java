package com.zksr.system.amap.Impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.zksr.system.amap.IAmapService;
import com.zksr.system.amap.constants.AmapConstant;
import com.zksr.system.amap.param.CheckLocationParam;
import com.zksr.system.amap.param.PolygonGeofenceParam;
import com.zksr.system.amap.param.ServiceParam;
import com.zksr.system.amap.result.CheckLocationResult;
import com.zksr.system.amap.result.PolygonGeofenceResult;
import com.zksr.system.amap.result.ServiceResult;
import com.zksr.system.api.amap.dto.LongitudeAndLatitudeResult;
import com.zksr.system.api.amap.vo.LongitudeAndLatitudeParam;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class AmapServiceImpl implements IAmapService {

    @Value("${amap.key}")
    private String key;

    /**
    * @Description: 新建服务
    * @Author: liuxingyu
    * @Date: 2024/3/27 16:18
    */
    @Override
    public ServiceResult addService(ServiceParam serviceParam) {
        serviceParam.setKey(key);
        return JSON.parseObject(HttpUtil.post(AmapConstant.ADD_SERVICE, BeanUtil.beanToMap(serviceParam)),ServiceResult.class);
    }

    /**
    * @Description: 创建多边形围栏
    * @Author: liuxingyu
    * @Date: 2024/3/27 16:18
    */
    @Override
    public PolygonGeofenceResult addPolygonGeofence(PolygonGeofenceParam polygonGeofenceParam) {
        polygonGeofenceParam.setKey(key);
        return JSON.parseObject(HttpUtil.post(AmapConstant.ADD_POLYGON, BeanUtil.beanToMap(polygonGeofenceParam)),PolygonGeofenceResult.class);
    }

    /**
    * @Description: 修改多边形围栏
    * @Author: liuxingyu
    * @Date: 2024/3/27 16:18
    */
    @Override
    public PolygonGeofenceResult updatePolygonGeofence(PolygonGeofenceParam polygonGeofenceParam) {
        polygonGeofenceParam.setKey(key);
        return JSON.parseObject(HttpUtil.post(AmapConstant.UPDATE_POLYGON, BeanUtil.beanToMap(polygonGeofenceParam)),PolygonGeofenceResult.class);
    }

    /**
    * @Description: 查询指定坐标与围栏关系
    * @Author: liuxingyu
    * @Date: 2024/3/27 16:47
    */
    @Override
    public CheckLocationResult checkLocation(CheckLocationParam checkLocationParam) {
        checkLocationParam.setKey(key);
        return JSON.parseObject(HttpUtil.get(AmapConstant.GEOFENCE_STATUS_LOCATION, BeanUtil.beanToMap(checkLocationParam)),CheckLocationResult.class);
    }

    @Override
    public LongitudeAndLatitudeResult selectLongitudeAndLatitude(LongitudeAndLatitudeParam longitudeAndLatitudeParam) {
        longitudeAndLatitudeParam.setKey(key);
        String aa = HttpUtil.get(AmapConstant.LONGITUDE_AND_LATITUDE, BeanUtil.beanToMap(longitudeAndLatitudeParam));
        return JSON.parseObject(aa,LongitudeAndLatitudeResult.class);
    }
}
