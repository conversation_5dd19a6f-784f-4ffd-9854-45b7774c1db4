package com.zksr.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysRole;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.controller.software.vo.SysSoftwarePageReqVO;
import com.zksr.system.controller.software.vo.SysSoftwareSaveReqVO;
import com.zksr.system.domain.SysSoftware;
import com.zksr.system.enums.SysRoleKeyEnum;
import com.zksr.system.mapper.SysRoleMapper;
import com.zksr.system.mapper.SysSoftwareMapper;
import com.zksr.system.mapper.SysUserMapper;
import com.zksr.system.service.ISysSoftwareService;
import com.zksr.system.service.ISysUserService;
import com.zksr.system.tenant.SaasHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.regex.Pattern;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.*;

/**
 * 软件商信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Service
public class SysSoftwareServiceImpl implements ISysSoftwareService {

    @Autowired
    private SysSoftwareMapper sysSoftwareMapper;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private SysUserMapper userMapper;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    @Autowired
    private SaasHelper saasHelper;

    /**
     * 新增软件商信息
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertSysSoftware(SysSoftwareSaveReqVO createReqVO) {
        // 插入
        SysSoftware sysSoftware = HutoolBeanUtils.toBean(createReqVO, SysSoftware.class);

        LoginUser user = SecurityUtils.getLoginUser();
        sysSoftware.setCreateBy(user.getUsername());
        sysSoftware.setUpdateBy(user.getUsername());
        sysSoftware.setCreateTime(DateUtil.date());
        sysSoftware.setUpdateTime(DateUtil.date());


        // 重复值校验
        validateSysSoftwareRept(sysSoftware);
        sysSoftwareMapper.insert(sysSoftware);

        // 完善账号信息
        SysUser saveUser = new SysUser();
        saveUser.setNickName(saasAuthSwitch ? createReqVO.getContactName() : String.format("【%s】%s", sysSoftware.getSoftwareName(), sysSoftware.getContactName()));
        saveUser.setUserName(createReqVO.getUserName());
        saveUser.setStatus(StringPool.ZERO);
        saveUser.setPassword(saasAuthSwitch ? "" : SecurityUtils.encryptPassword(createReqVO.getPassword()));
        saveUser.setPhonenumber(sysSoftware.getContactPhone());
        saveUser.setRemark(String.format("%s默认软件商管理员用户", sysSoftware.getSoftwareName()));
        saveUser.setCreateBy(SecurityUtils.getUsername());
        saveUser.setCreateTime(DateUtil.date());
        if (!userService.checkUserNameUnique(saveUser)) {
            throw new RuntimeException(String.format("新增用户%s失败,登录账号%s已存在", saveUser.getUserName(), saveUser.getUserName()));
        } else if (ToolUtil.isNotEmpty(saveUser.getPhonenumber())
                && !userService.checkPhoneUnique(saveUser)) {
            throw new RuntimeException(String.format("新增用户%s失败,手机号码%s已存在", saveUser.getUserName(),saveUser.getPhonenumber()));
        }
        else if (ToolUtil.isNotEmpty(saveUser.getEmail())
                && !userService.checkEmailUnique(saveUser)) {
            throw new RuntimeException(String.format("新增用户%s失败,邮箱账号%s已存在", saveUser.getUserName(),saveUser.getEmail()));
        }
        String roleKey = SysRoleKeyEnum.SOFTWARE_ADMIN_ROLE.getRoleKey();
        SysRole sysRole = sysRoleMapper.selectByRoleKey(roleKey);
        if (Objects.isNull(sysRole)) {
            // 权限不存在
            throw exception(SYS_SOFTWARE_ROLE_NOT_EXISTS);
        }
        Long roleId = sysRole.getRoleId();
        Long[] roleIds = {roleId};// 自动赋管理员权限
        saveUser.setRoleIds(roleIds);
        userService.saveUser(saveUser);
        userService.insertUserRole(saveUser);

        sysSoftware.setSoftwareUserId(saveUser.getUserId());
        sysSoftwareMapper.updateById(sysSoftware);

        // 返回
        return sysSoftware.getSoftwareId();
    }

    /**
     * 修改软件商信息
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateSysSoftware(SysSoftwareSaveReqVO updateReqVO) {

        SysSoftware sysSoftware = HutoolBeanUtils.toBean(updateReqVO, SysSoftware.class);

        //校验重复值
        validateSysSoftwareRept(sysSoftware);

        SysSoftware source = sysSoftwareMapper.selectById(sysSoftware.getSoftwareId());

        sysSoftwareMapper.updateById(sysSoftware);

        //修改用户账号
        SysUser sysUser = userMapper.selectUserById(source.getSoftwareUserId());

        if (!saasAuthSwitch && org.apache.commons.lang3.StringUtils.isNotBlank(updateReqVO.getPassword())){
            if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, updateReqVO.getPassword())) {
                throw exception(PASSWORD_FIAL);
            }
            sysUser.setPassword(SecurityUtils.encryptPassword(updateReqVO.getPassword().trim()));
        }
        sysUser.setUpdateBy(SecurityUtils.getUsername());
        userService.updateUserV2(sysUser);
    }

    /**
     * 删除软件商信息
     *
     * @param softwareId 软件商id
     */
    @Override
    public void deleteSysSoftware(Long softwareId) {
        // 删除
        sysSoftwareMapper.deleteById(softwareId);
    }

    /**
     * 批量删除软件商信息
     *
     * @param softwareIds 需要删除的软件商信息主键
     * @return 结果
     */
    @Override
    public void deleteSysSoftwareBySoftwareIds(Long[] softwareIds) {
        for(Long softwareId : softwareIds){
            this.deleteSysSoftware(softwareId);
        }
    }

    /**
     * 获得软件商信息
     *
     * @param softwareId 软件商id
     * @return 软件商信息
     */
    @Override
    public SysSoftware getSysSoftware(Long softwareId) {
        SysSoftware sysSoftware = sysSoftwareMapper.selectById(softwareId);
        if (Objects.nonNull(sysSoftware)) {
            sysSoftware.setUserName(userMapper.selectUserById(sysSoftware.getSoftwareUserId()).getUserName());
        }
        return sysSoftware;
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<SysSoftware> getSysSoftwarePage(SysSoftwarePageReqVO pageReqVO) {
        SysSoftware sysSoftware =sysSoftwareMapper.getBySysUserId(SecurityUtils.getLoginUser().getUserid());
        if(ToolUtil.isNotEmpty(sysSoftware)){
            pageReqVO.setSoftwareId(sysSoftware.getSoftwareId());
        }
        PageResult<SysSoftware> pageResult =sysSoftwareMapper.selectPage(pageReqVO);
        if (ToolUtil.isNotEmpty(pageResult.getList())) {
            //填充软件商账号名
            pageResult.getList().forEach(item -> {
                item.setUserName(userMapper.selectUserById(item.getSoftwareUserId()).getUserName());
            });
        }
        return pageResult;
    }

    @Override
    public SysSoftware getSysSoftwareByUserId(Long userId) {
        return sysSoftwareMapper.getBySysUserId(userId);
    }


    /**
    * 重复值教研
    */
    private void validateSysSoftwareRept(SysSoftware sysSoftware){

        //校验软件商名称
        {
            Long count = sysSoftwareMapper.selectCount(
                    Wrappers.lambdaQuery(SysSoftware.class)
                            .eq(SysSoftware::getSoftwareName, sysSoftware.getSoftwareName())
                            .ne(Objects.nonNull(sysSoftware.getSoftwareId()), SysSoftware::getSoftwareId, sysSoftware.getSoftwareId())
            );
            // 软件商名称重复
            if (count > 0) {
                throw exception(SYS_SOFTWARE_NAME_REPEAT);
            }
        }
        //校验软件商联系电话
        {
            Long count = sysSoftwareMapper.selectCount(
                    Wrappers.lambdaQuery(SysSoftware.class)
                            .eq(SysSoftware::getContactPhone, sysSoftware.getContactPhone())
                            .ne(Objects.nonNull(sysSoftware.getSoftwareId()), SysSoftware::getSoftwareId, sysSoftware.getSoftwareId())
            );
            // 软件商联系电话重复
            if (count > 0) {
                throw exception(SYS_SOFTWARE_PHONE_REPEAT);
            }
        }

    }

 /*   private void validateSysSoftwareExists(Long softwareId) {
        if (sysSoftwareMapper.selectById(softwareId) == null) {
            throw exception(SYS_SOFTWARE_NOT_EXISTS);
        }
    }*/

    // TODO 待办：请将下面的错误码复制到 com.zksr.system.enums.ErrorCodeConstants 类中。注意，请给“TODO 补充编号”设置一个错误码编号！！！
    // ========== 软件商信息 TODO 补充编号 ==========
    // ErrorCode SYS_SOFTWARE_NOT_EXISTS = new ErrorCode(TODO 补充编号, "软件商信息不存在");


}
