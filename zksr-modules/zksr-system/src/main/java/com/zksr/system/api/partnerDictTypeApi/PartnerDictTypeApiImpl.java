package com.zksr.system.api.partnerDictTypeApi;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.dcArea.DcAreaApi;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.partnerDict.PartnerDictTypeApi;
import com.zksr.system.service.ISysPartnerDictTypeService;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;

@RestController
@ApiIgnore
public class PartnerDictTypeApiImpl implements PartnerDictTypeApi {

    @Resource
    private ISysPartnerDictTypeService sysPartnerDictTypeService;

    @Override
    public CommonResult<List<SysPartnerDictData>> selectDictDataByType(String dictType,String sysSource) {
        return CommonResult.success(sysPartnerDictTypeService.selectDictDataByType(dictType,sysSource));
    }
}
