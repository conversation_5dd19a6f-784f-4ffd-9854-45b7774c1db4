package com.zksr.system.controller.software.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 软件商信息对象 sys_software
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@ApiModel("软件商信息 - sys_software分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysSoftwarePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 软件商id */
    @ApiModelProperty(value = "软件商分润比例")
    private Long softwareId;

    /** 软件商名 */
    @Excel(name = "软件商名")
    @ApiModelProperty(value = "软件商名")
    private String softwareName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 关联软件商管理员的账号id */
    @Excel(name = "关联软件商管理员的账号id")
    @ApiModelProperty(value = "关联软件商管理员的账号id")
    private Long softwareUserId;

    /** 软件商分润比例 */
    @Excel(name = "软件商分润比例")
    @ApiModelProperty(value = "软件商分润比例")
    private BigDecimal softwareRate;


}
