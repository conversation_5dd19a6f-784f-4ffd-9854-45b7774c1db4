package com.zksr.system.service;

import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysPartnerDictData;

import java.util.List;

/**
 * 平台商字典 业务层
 * 
 * <AUTHOR>
 */
public interface ISysPartnerDictDataService
{

    List<SysPartnerDictData> getList(SysPartnerDictData sysPartnerDictData);

    SysPartnerDictData selectDictDataById(Long dictCode);

    List<SysPartnerDictData> selectDictDataByType(String dictType);

    List<SysPartnerDictData> selectDictDataByType(String dictType,Long sysCode);

    void deleteDictDataByIds(Long[] dictCodes);

    int insertDictData(SysPartnerDictData data);

    int updateDictData(SysPartnerDictData data);

}
