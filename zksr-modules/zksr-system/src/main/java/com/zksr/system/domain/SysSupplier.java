package com.zksr.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 入驻商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@TableName(value = "sys_supplier")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysSupplier extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 入驻商id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long supplierId;

    /** 入驻商编号 */
    private String supplierCode;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    private String supplierName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    private String contactAddress;

    /** 状态-数据字典 */
    @Excel(name = "状态-数据字典")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 是否是电子围栏入驻商 */
    @Excel(name = "是否是电子围栏入驻商")
    private Long dzwlFlag;

    /** 电子围栏 */
    @Excel(name = "电子围栏")
    private String dzwlInfo;

    /** 运营商id;运营商id */
    @Excel(name = "运营商id;运营商id")
    private Long dcId;

    @Excel(name = "营业执照图片地址")
    private String licenceUrl;

    @Excel(name = "猎鹰电子围栏ID")
    private Long gfid;

    @Excel(name = "用户id")
    private Long userId;

    @Excel(name = "本地起送价")
    private BigDecimal minAmt;

    @Excel(name = "全国起送价")
    private BigDecimal globalMinAmt;

    /**
     * 支付账户最小保留金
     */
    @Excel(name = "支付账户最小保留金")
    @ApiModelProperty(value = "支付账户最小保留金", example = "1000")
    private BigDecimal minSettleAmt;

    @Excel(name = "公众号openid")
    private String publishOpenid;

    @Excel(name = "入驻商头像地址")
    private String avatar;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;
}
