<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysDcMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectPageExt" resultType="com.zksr.system.api.dc.vo.SysDcRespVO">
        SELECT
            sd.dc_id,
            sd.sys_code,
            sd.create_by,
            sd.create_time,
            sd.update_by,
            sd.memo,
            sd.status,
            sd.address,
            sd.dc_code,
            sd.dc_name,
            sd.contract_name,
            sd.contract_phone,
            sa.area_name,
            sd.min_amt,
            sd.global_min_amt
        FROM
            sys_dc sd
            LEFT JOIN sys_area sa ON sd.sys_area_id = sa.area_id
        <where>
            <if test="req.dcId != null">
                AND sd.dc_id = #{req.dcId}
            </if>
            <if test="req.sysCode != null">
                AND sd.dc_id = #{req.sysCode}
            </if>
            <if test='req.status != null and req.status != ""'>
                AND sd.status = #{req.status}
            </if>
            <if test='req.dcCode != null and req.dcCode != ""'>
                AND sd.dc_code LIKE CONCAT('%', #{req.dcCode}, '%')
            </if>
            <if test='req.dcName != null and req.dcName != ""'>
                AND sd.dc_name LIKE CONCAT('%', #{req.dcName}, '%')
            </if>
            <if test='req.contractPhone != null and req.contractPhone != ""'>
                AND sd.contract_phone LIKE CONCAT('%', #{req.contractPhone}, '%')
            </if>
            ${req.params.dataScope}
        </where>
    </select>

    <select id="getNotBindDcList" resultType="com.zksr.system.domain.SysDc">
        SELECT
            dc.dc_id,
            dc.dc_name
        FROM
            sys_dc dc
                LEFT JOIN sys_dc_area sda ON dc.dc_id = sda.dc_id
        WHERE
            sda.area_id IS NULL or sda.dc_id = #{dcId}
    </select>
    <select id="selectDcSupplierIdList" resultType="java.lang.Long">
        <!-- 运营商自己创建的 -->
        (
           SELECT
               supplier_id
           FROM
               sys_supplier su
           WHERE
               su.dc_id = #{dcId}
        )
        UNION
        <!-- 运营商管理区域的 -->
        (
            SELECT
                ssa.supplier_id
            FROM
                sys_dc_area sda
                INNER JOIN sys_supplier_area ssa ON ssa.area_id = sda.area_id
            WHERE
                sda.dc_id = #{dcId}
        )
    </select>
</mapper>
