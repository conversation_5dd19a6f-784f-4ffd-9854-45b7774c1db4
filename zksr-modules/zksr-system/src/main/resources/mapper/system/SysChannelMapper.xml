<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysChannelMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <update id="updateDeleted">
        UPDATE sys_channel SET deleted = #{param.deleted}
        WHERE channel_id = #{param.channelId}
              AND sys_code = #{param.sysCode}
    </update>
    <select id="selectPage" resultType="com.zksr.system.domain.SysChannel">
        select * from sys_channel
        <where>
            1=1
            <if test="param.channelId != null">
                and channel_id = #{param.channelId}
            </if>
            <if test="param.memo != null and param.memo != '' ">
                and memo = #{param.memo}
            </if>
            <if test="param.status != null  ">
                and status = #{param.status}
            </if>
            <if test="param.channelName != null and param.channelName != '' ">
                and (channel_name like concat('%',#{param.channelName},'%') or channel_id like
                concat('%',#{param.channelName},'%'))
            </if>
            order by channel_id desc
        </where>
    </select>
</mapper>