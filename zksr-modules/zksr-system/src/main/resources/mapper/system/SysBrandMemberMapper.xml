<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysBrandMemberMapper">


    <select id="selectPageExt" resultType="com.zksr.system.controller.brand.vo.SysBrandMemberRespVO">
        SELECT
            sbm.brand_member_id,
            sbm.sys_code,
            sbm.create_by,
            sbm.create_time,
            sbm.update_by,
            sbm.update_time,
            sbm.brand_merchant_id,
            sbm.contact_phone,
            sbm.contact_name,
            sbm.contact_address,
            sbm.memo,
            sbm.sys_user_id,
            sbm.brand_ids,
            sbm.area_ids,
            sbm.status,
            sbm2.name brandMerchantName,
            sur.user_name username
        FROM
            sys_brand_member sbm
            LEFT JOIN sys_brand_merchant sbm2 ON sbm.brand_merchant_id = sbm2.brand_merchant_id
            LEFT JOIN sys_user sur ON sbm.sys_user_id = sur.user_id
        <where>
            <if test='contactPhone != null and contactPhone != ""'>
                AND sbm.contact_phone LIKE CONCAT('%', #{contactPhone}, '%')
            </if>
            <if test='contactName != null and contactName != ""'>
                AND sbm.contact_name LIKE CONCAT('%', #{contactName}, '%')
            </if>
            <if test='brandMerchantId != null'>
                AND sbm.brand_merchant_id = #{brandMerchantId}
            </if>
            <if test='brandMemberId != null'>
                AND sbm.brand_member_id = #{brandMemberId}
            </if>
            <if test='status != null'>
                AND sbm.status = #{status}
            </if>
        </where>
        ORDER BY
            sbm.brand_member_id DESC
    </select>
</mapper>