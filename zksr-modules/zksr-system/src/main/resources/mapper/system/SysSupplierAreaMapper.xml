<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysSupplierAreaMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectSupplierIds" resultType="java.lang.Long">
        SELECT
            DISTINCT
            ssa.supplier_id
        FROM
            sys_supplier_area ssa
                INNER JOIN sys_dc_area sda ON ssa.area_id = sda.area_id
                AND sda.dc_id = #{dcId}
    </select>

    <!-- 获取区域下的电子围栏供应商信息 -->
    <select id="getDzwlSupplierInfoByAreaId"
            resultType="com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO">
        SELECT
               ssa.supplier_id,
               s.supplier_name,
               ssa.area_id,
               s.dzwl_info
        FROM
            sys_supplier_area ssa
            INNER JOIN sys_supplier s ON ssa.supplier_id = s.supplier_id
        WHERE
            s.dzwl_flag = 1
            AND ssa.area_id = #{areaId}
    </select>
    <select id="getSupplierListByIds" resultType="com.zksr.system.domain.SysSupplier">
        select * from sys_supplier
        <where>
            supplier_id in
            <foreach collection="supplierIds" close=")" open="(" separator="," item="supplierId">
                #{supplierId}
            </foreach>
        </where>
    </select>
</mapper>
