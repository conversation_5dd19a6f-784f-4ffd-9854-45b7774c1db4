<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysPagesConfigMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPageExt" resultType="com.zksr.system.controller.pageConfig.vo.SysPagesConfigRespVO">
        SELECT
           spc.page_id,
           spc.sys_code,
           spc.dc_id,
           spc.create_by,
           spc.create_time,
           spc.enable_time,
           spc.update_by,
           spc.update_time,
           spc.page_name,
           spc.page_type,
           spc.channel_id,
           spc.area_id,
           spc.def_flag,
           spc.status,
           spc.del_flag,
           sar.area_name,
           csh.channel_name,
           spc.level,
           spc.pid,
           spc.has_child,
           spc.type,
           spc.start_time,
           spc.end_time
        FROM
            sys_pages_config spc
            LEFT JOIN sys_area sar ON spc.area_id = sar.area_id
            LEFT JOIN sys_channel csh ON csh.channel_id = spc.channel_id
        WHERE
            spc.del_flag = '0'
            <if test='req.pageName != null and req.pageName != ""'>
                AND spc.page_name LIKE CONCAT('%', #{req.pageName}, '%')
            </if>
            <if test='req.status != null and req.status != "" and req.status == "1"'>
                AND (spc.status = 1 OR spc.end_time &lt; now())
            </if>
            <if test='req.status != null and req.status != "" and req.status == "0"'>
                AND spc.status = 0 AND spc.end_time > now()
            </if>
            <if test='req.areaId != null'>
                AND spc.area_id IN (SELECT area_id FROM sys_area WHERE pid = #{req.areaId} OR area_id = #{req.areaId})
            </if>
            <if test='req.pid != null'>
                AND spc.pid = #{req.pid}
            </if>
            <if test='req.level != null'>
                AND spc.level = #{req.level}
            </if>
            <!-- 手动注入数据隔离 -->
            ${req.params.dataScope}
        ORDER BY
            spc.page_id DESC
    </select>
</mapper>