<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysAreaCityMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="queryByName" resultType="com.zksr.system.domain.SysAreaCity">
        select
        t3.area_city_id,
        t3.name
        from
        sys_area_city t3
        left join sys_area_city t2 on
        t3.pid = t2.area_city_id
        left join sys_area_city t1 on
        t2.pid = t1.area_city_id
        where
        t3.name = #{districtName}
        and t2.name = #{cityName}
        and t1.name = #{provinceName}
        and t3.del_flag  = 0
        and t2.del_flag  = 0
        and t1.del_flag  = 0
        limit 1
    </select>


</mapper>
