<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysAreaMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getDcNotBindArea" resultType="com.zksr.system.domain.SysArea">
        SELECT
            sa.area_id,
            sa.area_name,
            sa.pid,
            sa.level,
            sa.sort_num
        FROM
            sys_area sa
            LEFT JOIN sys_dc_area sda ON sa.area_id = sda.area_id
        WHERE
            sa.status = 1
            AND sa.deleted = 0
            AND (sda.area_id IS NULL or sda.dc_id = #{dcId})
    </select>

    <select id="selectAreaPage" resultType="com.zksr.system.controller.area.vo.SysAreaRespVO">
        SELECT
            sa.area_id,
            sa.area_name,
            sa.pid,
            sa.level,
            psa.area_name parent_name,
            sa.status,
            sa.memo,
            sa.sort_num
        FROM
            sys_area sa
            LEFT JOIN sys_area psa on sa.pid = psa.area_id
        <where>
            sa.deleted = 0
            <if test="null != pageReqVO.areaId">
                and sa.area_id = #{pageReqVO.areaId}
            </if>
            <if test="null != pageReqVO.areaName and pageReqVO.areaName != '' ">
                and sa.area_name like concat('%',#{pageReqVO.areaName},'%')
            </if>
            <if test="null != pageReqVO.status">
                and sa.status = #{pageReqVO.status}
            </if>
            <if test="null != dcId">
                and sa.dc_id = #{dcId}
            </if>
            <if test="null != pageReqVO.level">
                and sa.level = #{pageReqVO.level}
            </if>
            <if test="null != pageReqVO.pid">
                and sa.area_id IN (SELECT area_id FROM sys_area WHERE area_id = #{pageReqVO.pid} OR pid = #{pageReqVO.pid})
            </if>
        </where>
        order by sa.sort_num asc, sa.area_id desc
    </select>

    <select id="getAreaAllList" resultType="com.zksr.system.domain.SysArea">
        <!-- 二级 -->
        SELECT
            area_id,
            sys_code,
            create_time,
            pid,
            area_name,
            status,
            memo,
            dc_id,
            local_flag,
            group_id,
            `level`,
            sort_num
        FROM
            sys_area sa
        WHERE
            status = 1
            and deleted = 0
            <if test="null != areaId">
                AND area_id != #{areaId}
            </if>
            <!-- 手动注入数据隔离 -->
            ${params.dataScope}
        UNION
        <!-- 一级的所有二级 -->
        SELECT
            sa2.area_id,
            sa2.sys_code,
            sa2.create_time,
            sa2.pid,
            sa2.area_name,
            sa2.status,
            sa2.memo,
            sa2.dc_id,
            sa2.local_flag,
            sa2.group_id,
            sa2.`level`,
            sa2.sort_num
        FROM
            sys_area sa
            INNER JOIN sys_area sa2 ON sa.pid = sa2.area_id
        WHERE
            sa2.status = 1
            and sa2.deleted = 0
            <if test="null != areaId">
                AND sa.area_id != #{areaId}
            </if>
            <!-- 手动注入数据隔离 -->
            ${params.dataScope}
    </select>

    <select id="getAreaListBySupplierId" resultType="com.zksr.system.domain.SysArea">
        SELECT
            sa.area_id,
            sa.sys_code,
            sa.create_time,
            sa.pid,
            sa.area_name,
            sa.STATUS,
            sa.memo,
            sa.dc_id,
            sa.local_flag,
            sa.group_id,
            sa.`level`,
            sa.sort_num
        FROM
            sys_area sa
            INNER JOIN sys_supplier_area ssa ON sa.area_id = ssa.area_id
        WHERE
            sa.status = 1
            AND ssa.supplier_id = #{supplierId}
            AND sa.deleted = 0
        UNION
        SELECT
            sa.area_id,
            sa.sys_code,
            sa.create_time,
            sa.pid,
            sa.area_name,
            sa.STATUS,
            sa.memo,
            sa.dc_id,
            sa.local_flag,
            sa.group_id,
            sa.`level`,
            sa.sort_num
        FROM
            sys_area sa
            JOIN (
                SELECT
                    pid
                FROM
                    sys_area sa
                    INNER JOIN sys_supplier_area ssa ON sa.area_id = ssa.area_id
               WHERE
                    ssa.supplier_id = #{supplierId} and sa.deleted = 0
           )sub on sub.pid = sa.area_id
        WHERE
            sa.status = 1
    </select>

    <!-- 获取区域城市下拉列表(只获取二级) -->
    <select id="getAreaDownList" resultType="com.zksr.system.domain.SysArea">
        SELECT
            area_id,
            sys_code,
            create_time,
            pid,
            area_name,
            status,
            memo,
            dc_id,
            local_flag,
            group_id,
            level,
            sort_num
        FROM
            sys_area
        WHERE
            status = 1
            AND level = 2
            <if test="null != areaId">
                AND area_id != #{areaId}
            </if>
            <if test="null != dcId">
                AND dc_id = #{dcId}
            </if>
    </select>

    <select id="getAreaBySyscodeAndDcId" resultType="com.zksr.system.api.area.dto.AreaDTO">
        SELECT
            sa.area_id,
            sa.area_name,
            sa.dc_id,
            sa.level,
            sa.sort_num
        FROM
            sys_area sa
            LEFT JOIN sys_dc_area sda ON sa.area_id = sda.area_id
        WHERE
            sa.status = 1
            AND sa.sys_code = #{sysCode}
            AND sda.dc_id = #{dcId}
    </select>

    <update id="updateDeleted">
        update sys_area set deleted = #{deleted}
        where area_id = #{areaId}
        and sys_code = #{sysCode}
    </update>

</mapper>
