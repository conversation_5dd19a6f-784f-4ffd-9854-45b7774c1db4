<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysInterfaceLogMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPageLog" resultType="com.zksr.system.controller.log.vo.SysInterfaceLogPageReqVO">
    SELECT
        sil.*,
        ss.supplier_name,
        sp.partner_name
    FROM
        sys_interface_log sil
    LEFT JOIN sys_supplier ss ON sil.supplier_id = ss.supplier_id
    LEFT JOIN sys_partner sp ON sil.sys_code = sp.sys_code
    <where>
        <!-- 模糊查询请求编号  -->
        <if test="reqVO.reqId != '' and reqVO.reqId != null">
            and sil.req_id like concat('%', #{reqVO.reqId}, '%')
        </if>
        <!-- 查询来源方  -->
        <if test="reqVO.source != null">
           and sil.source = #{reqVO.source}
        </if>
        <!-- 查询接收方  -->
        <if test="reqVO.receive != null">
            and sil.receive = #{reqVO.receive}
        </if>
        <!-- 模糊查询业务数据  -->
        <if test="reqVO.bizData != '' and reqVO.bizData != null">
            and sil.biz_data like concat('%', #{reqVO.bizData}, '%')
        </if>
        <!-- 模糊查询请求数据  -->
        <if test="reqVO.reqData != '' and reqVO.reqData != null">
            and sil.req_data like concat('%', #{reqVO.reqData}, '%')
        </if>
        <!-- 区间查询请求时间  -->
        <if test="reqVO.reqStartTime != null and reqVO.reqEndTime != null ">
            and sil.req_time BETWEEN #{reqVO.reqStartTime} AND #{reqVO.reqEndTime}
        </if>
        <!-- 查询状态  -->
        <if test="reqVO.status != null">
            and sil.status = #{reqVO.status}
        </if>
        <!-- 模糊查询处理信息  -->
        <if test="reqVO.message != null and reqVO.message != '' ">
            and sil.message like concat('%', #{reqVO.message}, '%')
        </if>
        <!-- 查询请求类型  -->
        <if test="reqVO.requestType != null">
            and sil.request_type = #{reqVO.requestType}
        </if>
        <!-- 查询操作类型  -->
        <if test="reqVO.operationType != null">
            and sil.operation_type = #{reqVO.operationType}
        </if>
        <!-- 查询请求状态  -->
        <if test="reqVO.reqStatus != null">
            and sil.req_status = #{reqVO.reqStatus}
        </if>
        <!-- 平台商  -->
        <if test="reqVO.sysCode != null">
            and sil.sys_code = #{reqVO.sysCode}
        </if>
        <!-- 入驻商  -->
        <if test="reqVO.supplierId != null">
            and sil.supplier_id = #{reqVO.supplierId}
        </if>
    </where>
        ORDER BY sil.req_time DESC
    </select>

    <select id="getLogByDaySyncReport" resultType="com.zksr.system.api.EmailMessage.dto.SyncEmailReportExcel">
        SELECT
        sil.receive AS sourceType,
        CASE
        WHEN sil.supplier_id IS NOT NULL THEN '入驻商级别'
        ELSE '平台商级别'
        END AS merchantType,
        COALESCE(sil.supplier_id, sil.partner_id) AS merchantId,
        COALESCE(ssr.supplier_name, spr.partner_name) AS merchantName,
        sil.sys_code,
        spr2.partner_name AS partnerName,
        sil.request_type,
        COUNT(1) AS sendCount,
        COUNT(CASE WHEN sil.STATUS = 1 THEN 1 END) AS successCount,
        COUNT(CASE WHEN sil.STATUS != 1 THEN 1 END) AS failCount
        FROM
        sys_interface_log sil
        LEFT JOIN sys_supplier ssr ON sil.supplier_id = ssr.supplier_id
        LEFT JOIN sys_partner spr ON sil.partner_id = spr.sys_code
        LEFT JOIN sys_partner spr2 ON sil.sys_code = spr2.sys_code
        WHERE
        sil.log_type = 1
        AND sil.sys_code = #{sysCode}
        AND DATE(sil.create_time) = #{date}
        GROUP BY
        sil.receive,
        sil.supplier_id,
        sil.partner_id,
        sil.sys_code,
        spr.partner_name,
        spr2.partner_name,
        sil.request_type
    </select>
</mapper>