<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysPartnerPolicyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    
    <select id="selectSearchConfigPage" resultType="com.zksr.system.controller.partnerPolicy.vo.SearchConfigPageRespVO">
        SELECT
            sp.partner_policy_id AS partnerPolicyId,
            sp.sys_code AS sysCode,
            sp.policy_name AS policyName,
            sp.policy_key AS policyKey,
            sp.policy_value AS policyValue,
            sp.policy_type AS policyType,
            sp.dc_id AS dcId,
            sp.supplier_id AS supplierId,
            sp.area_id AS areaId,
            sp.create_by AS createBy,
            sp.create_time AS createTime
        FROM
            sys_partner_policy sp
        WHERE
        sp.policy_type = 19
        <if test="reqVO.areaId != null">
            AND sp.area_id = #{reqVO.areaId}
        </if>
        <if test="reqVO.areaIds != null and reqVO.areaIds.size() > 0">
            AND sp.area_id IN
            <foreach collection="reqVO.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
    </select>

</mapper>