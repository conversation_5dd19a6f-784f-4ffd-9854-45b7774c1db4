<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysOpenabilityMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="getMerchantAbilityKeys" resultType="String">
        SELECT
            distinct m.ability_key
        FROM
            sys_openability m
        WHERE
            m.status = '0' and m.merchant_type = #{merchantType}
    </select>

    <select id="getMerchantAbilityKeysByList" resultType="String">
        SELECT
            distinct m.ability_key
        FROM
            sys_openability m
        WHERE
            m.status = '0' and m.merchant_type in
        <foreach collection="typeList" item="merchantType" open="(" separator="," close=")">
            #{merchantType}
        </foreach>
    </select>

<!--    <select id="getOpenlimitList" resultType="com.zksr.system.api.opensource.dto.OpenlimitDto">-->
<!--        SELECT-->
<!--            a.ability_key abilityKey,-->
<!--            l.rate_limit rateLimit-->
<!--        FROM-->
<!--            sys_openlimit l-->
<!--            LEFT JOIN sys_openability a on l.openability_id = a.openability_id-->
<!--        WHERE-->
<!--            l.opensource_id = #{opensourceId}-->
<!--    </select>-->
    <select id="getOpenlimitList" resultType="com.zksr.system.api.opensource.dto.OpenlimitDto">
        SELECT
            a.ability_key abilityKey,
            ifnull(a.rate_limit, 10) rateLimit
        FROM
            sys_openability a
        WHERE
            a.merchant_type = (
                SELECT merchant_type FROM sys_opensource WHERE opensource_id = #{opensourceId}
            )
    </select>

    <select id="getOpenlimitListByList" resultType="com.zksr.system.api.opensource.dto.OpenlimitDto">
        SELECT
            a.ability_key abilityKey,
            ifnull(a.rate_limit, 10) rateLimit
        FROM
            sys_openability a
        WHERE
                a.merchant_type in
        <foreach collection="typeList" item="merchantType" open="(" separator="," close=")">
            #{merchantType}
        </foreach>
    </select>

    <select id="hasChildByOpenabilityId" resultType="Integer">
        select count(1) from sys_openability where pid = #{openabilityId}
    </select>
</mapper>
