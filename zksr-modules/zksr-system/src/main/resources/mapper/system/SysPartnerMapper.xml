<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysPartnerMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectSysPartnerList" resultType="com.zksr.system.domain.SysPartner">
        SELECT sp.*, su.user_name AS partnerAccount
        FROM sys_partner sp
        LEFT JOIN sys_user su ON sp.partner_user_id = su.user_id
        <where>
            <if test="sysCode != null">AND sp.sys_code = #{sysCode} </if>
            <if test="partnerName != null">AND sp.partner_name LIKE CONCAT('%', #{partnerName}, '%') </if>
            <if test="partnerUserId != null">AND sp.partner_user_id = #{partnerUserId} </if>
            <if test="contactName != null">AND sp.contact_name LIKE CONCAT('%', #{contactName}, '%') </if>
            <if test="contactPhone != null">AND sp.contact_phone = #{contactPhone} </if>
            <if test="contactAddress != null">AND sp.contact_address = #{contactAddress} </if>
            <if test="status != null">AND sp.status = #{status} </if>
            <if test="memo != null">AND sp.memo = #{memo} </if>
            <if test="source != null">AND sp.source = #{source} </if>
            <if test="partnerCode != null">AND sp.partner_code = #{partnerCode} </if>
        </where>
        ORDER BY sp.sys_code DESC
        LIMIT #{pageNo},#{pageSize}
    </select>

    <select id="getByCacheKey" resultType="com.zksr.system.domain.SysPartner">
        SELECT sys_code,
               partner_name,
               partner_user_id,
               contact_name,
               contact_phone,
               contact_address,
               status,
               memo,
               source,
               area_num,
               dc_num,
               supplier_num,
               partner_code,
               sid,
               software_id,
               software_rate,
               create_by,
               create_time,
               update_by,
               update_time,
                enable_retail,
                enable_o2o
        FROM sys_partner
        WHERE sys_code = #{cacheKey} OR source = #{cacheKey}
    </select>
    <select id="countSelectSysPartnerList" resultType="java.lang.Long">
        SELECT
            COUNT(*)
        FROM sys_partner sp
        LEFT JOIN sys_user su ON sp.partner_user_id = su.user_id
        <where>
            <if test="sysCode != null">AND sp.sys_code = #{sysCode} </if>
            <if test="partnerName != null">AND sp.partner_name LIKE CONCAT('%', #{partnerName}, '%') </if>
            <if test="partnerUserId != null">AND sp.partner_user_id = #{partnerUserId} </if>
            <if test="contactName != null">AND sp.contact_name LIKE CONCAT('%', #{contactName}, '%') </if>
            <if test="contactPhone != null">AND sp.contact_phone = #{contactPhone} </if>
            <if test="contactAddress != null">AND sp.contact_address = #{contactAddress} </if>
            <if test="status != null">AND sp.status = #{status} </if>
            <if test="memo != null">AND sp.memo = #{memo} </if>
            <if test="source != null">AND sp.source = #{source} </if>
            <if test="partnerCode != null">AND sp.partner_code = #{partnerCode} </if>
        </where>
        ORDER BY sp.sys_code DESC
    </select>
</mapper>