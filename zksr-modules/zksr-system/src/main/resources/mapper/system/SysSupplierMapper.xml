<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysSupplierMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="selectPage" resultType="com.zksr.system.api.supplier.vo.SysSupplierRespVO">
        SELECT
            ss.supplier_id,
            ss.sys_code,
            ss.supplier_name,
            ss.contact_name,
            ss.contact_phone,
            ss.contact_address,
            ss.status,
            ss.memo,
            ss.dzwl_flag,
            ss.dzwl_info,
            ss.dc_id,
            sd.dc_name,
            ss.create_by,
            ss.create_time,
            ss.update_by,
            ss.update_time,
            ss.avatar
        FROM
            sys_supplier ss
                LEFT JOIN sys_dc sd ON ss.dc_id = sd.dc_id
        <where>
            <if test="null != reqVO.supplierId">
                and ss.supplier_id = #{reqVO.supplierId}
            </if>
            <if test="null != reqVO.supplierName and '' != reqVO.supplierName">
                and ss.supplier_name like concat('%', #{reqVO.supplierName}, '%')
            </if>
            <if test="null != supplierIds and supplierIds.size > 0">
                and ss.supplier_id in
                <foreach collection="supplierIds" item="supplierId" open="(" separator="," close=")">
                    #{supplierId}
                </foreach>
            </if>
            <if test="null != reqVO.status">
                and ss.status = #{reqVO.status}
            </if>
        </where>
        ORDER BY ss.create_time DESC
    </select>

    <select id="selectSupplierListByUser" resultType="com.zksr.system.api.supplier.vo.SysSupplierRespVO">
        SELECT distinct
        ss.supplier_id,
        ss.sys_code,
        ss.supplier_name,
        ss.contact_name,
        ss.contact_phone,
        ss.contact_address,
        ss.status,
        ss.memo,
        ss.dzwl_flag,
        ss.dzwl_info,
        ss.dc_id,
        ss.create_by,
        ss.create_time,
        ss.update_by,
        ss.update_time
        FROM
        sys_dc sd
        LEFT JOIN sys_dc_area sda on sda.dc_id = sd.dc_id
        LEFT JOIN sys_supplier_area ssa on ssa.area_id = sda.area_id
        LEFT JOIN sys_supplier ss ON ss.supplier_id = ssa.supplier_id
        where
            ss.supplier_id is not null
            <if test="dcId != null">
                and  sd.dc_id = #{dcId}
            </if>
            <if test="supplierId != null">
               and ss.supplier_id = #{supplierId}
            </if>
        ORDER BY ss.create_time DESC
    </select>
    <select id="selectSysSupplierById" resultType="com.zksr.system.api.supplier.vo.SysSupplierRespVO">
        SELECT
            ss.supplier_id,
            ss.supplier_code,
            ss.sys_code,
            ss.supplier_name,
            ss.contact_name,
            ss.contact_phone,
            ss.contact_address,
            ss.status,
            ss.memo,
            ss.dzwl_flag,
            ss.dzwl_info,
            ss.dc_id,
            ss.licence_url,
            ss.min_amt,
            ss.user_id,
            sd.dc_name,
            ss.min_settle_amt,
            ss.avatar,
            ss.global_min_amt,
            ss.is_negative_stock
        FROM
            sys_supplier ss
                LEFT JOIN sys_dc sd ON sd.dc_id = ss.dc_id
        WHERE
            ss.supplier_id = #{supplierId}
    </select>
    <select id="getByDcId" resultType="java.lang.Long">
        select distinct ssa.supplier_id
        from sys_dc_area sda
        inner join sys_supplier_area ssa on sda.area_id = ssa.area_id
        where sda.dc_id = #{dcId}
    </select>
    <select id="getCustomApiMaster" resultType="com.zksr.common.core.domain.vo.openapi.CustomApiMaster">
        select
        cam.*
        from
        sys_custom_api_master cam
        <where>
            1=1
            <if test="customApiMaster.modelType != null and customApiMaster.modelType != '' ">
                and cam.model_type =#{customApiMaster.modelType}
            </if>
            <if test="customApiMaster.apiStatus != null and customApiMaster.apiStatus != '' ">
                and cam.api_status =#{customApiMaster.apiStatus}
            </if>
            <if test="customApiMaster.systemType != null and customApiMaster.systemType != '' ">
                and cam.system_type =#{customApiMaster.systemType}
            </if>
        </where>
    </select>
    <select id="getCustomApiDetail" resultType="com.zksr.common.core.domain.vo.openapi.CustomApiDetail">
        select
            cad.*
        from
            sys_custom_api_detail cad
        where
            cad.api_no =#{apiNo}
    </select>
</mapper>
