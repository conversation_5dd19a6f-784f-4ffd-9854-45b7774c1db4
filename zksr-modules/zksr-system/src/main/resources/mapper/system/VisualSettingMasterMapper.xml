<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.VisualSettingMasterMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="list" resultType="com.zksr.system.controller.visualSetting.vo.VisualSettingMasterRespVO">
        select
            v.visual_master_id,
            v.create_by,
            v.create_time,
            v.update_by,
            v.update_time,
            v.platform_name,
            v.encrypt_type,
            v.send_url,
            v.status,
            v.public_key,
            v.private_key,
            v.memo,
            v.create_time
        from visual_setting_master v
        <where>
            <if test="vo.platformName != null and vo.platformName != '' ">
                and v.platform_name like concat ('%',#{vo.platformName},'%')
            </if>
            <if test="vo.status != null ">
                and v.status = #{vo.status}
            </if>
        </where>
    </select>
</mapper>