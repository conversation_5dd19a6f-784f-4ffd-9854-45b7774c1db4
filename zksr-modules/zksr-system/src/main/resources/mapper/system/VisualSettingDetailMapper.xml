<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.VisualSettingDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="list" resultType="com.zksr.system.controller.visualSetting.vo.VisualSettingDetailRespVO">
        select
            vsd.visual_detail_id,
            vsd.create_time,
            vsd.update_time,
            vsd.status,
            vsd.visual_master_id,
            vsd.visual_template_id,
            vsd.template_type,
            vsd.req_type,
            vsd.api_url,
            vsd.content_type,
            vsd.resp_name,
            vsd.resp_code,
            vsd.debug_result_status,
            vsd.debug_result_message,
            vsd.memo,
            template.template_name visualTemplateName
        from visual_setting_detail vsd
        left join visual_setting_template template on template.visual_template_id = vsd.visual_template_id
        <where>
            <if test="vo.status != null ">
                and vsd.status =#{vo.status}
            </if>
            <if test="vo.reqType != null  and vo.reqType != '' ">
                and vsd.req_type = #{vo.reqType}
            </if>
            <if test="vo.visualMasterId != null  and vo.visualMasterId != '' ">
                and vsd.visual_master_id = #{vo.visualMasterId}
            </if>

        </where>
    </select>
</mapper>
