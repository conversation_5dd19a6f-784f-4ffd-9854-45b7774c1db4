<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysExportJobMapper">
	<insert id="insertJob">
		INSERT INTO sys_export_job(
			   `job_key`,
			   `name`,
			   `export_type`,
			   `create_by`,
			   `create_time`,
			   `remote_ip`,
			   `user_key`,
			   `user_info`,
			   `query_data`)
		VALUES (
		        #{jobKey},
		        #{name},
		        #{exportType},
		        #{createBy},
		        #{createTime},
		        #{remoteIp},
		        #{userKey},
		        #{userInfo},
		        #{queryData}
		)
	</insert>
	<select id="selectWaitJobList" resultType="com.zksr.system.api.export.vo.SysExportJob">
		SELECT
		    id,
		    name,	-- 任务名称
		    status,
			job_key AS jobKey, -- 任务key
			user_info AS userInfo,	--	执行人session快照
			state,	--	执行状态0-等待中,1-执行中,2-执行成功,3-执行失败
			export_type AS exportType,	--	导出类型
			query_data,	--	导出参数
			user_key,	--	用户key
			col_config	--	列配置
		FROM
		    sys_export_job
		WHERE
		    state = '0'
		LIMIT 10
	</select>
	<select id="selectPageExt" resultType="com.zksr.system.api.export.vo.SysExportJobRespVO">
		SELECT
			sej.id,
			sej.name,
			sej.status,
			sej.remark,
			sej.create_by,
			sej.create_time,
			sej.update_by,
			sej.update_time,
			sej.del_flag,
			sej.job_key,
			sej.state,
			sej.file,
			sej.operate,
			sej.export_type,
			sej.query_data,
			sej.sys_code,
			sej.execute_time,
			sej.finish_time,
			sej.remote_ip,
			sej.user_key
		FROM
			sys_export_job sej
		<where>
			<if test="state != null">
				AND sej.state = #{state}
			</if>
			<if test="remoteIp != null">
				AND sej.remote_ip = #{remoteIp}
			</if>
			<if test="exportType != null">
				AND sej.export_type = #{exportType}
			</if>
			<if test="username != null">
				AND sej.create_by = #{username}
			</if>
			<if test="remark != null">
				AND sej.remark LIKE CONCAT('%', #{remark}, '%')
			</if>
			<if test="name != null">
				AND sej.name LIKE CONCAT('%', #{name}, '%')
			</if>
		</where>
		ORDER BY
		    id DESC
	</select>
</mapper>