<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.system.mapper.SysRoleMenuMapper">

	<resultMap type="com.zksr.system.domain.SysRoleMenu" id="SysRoleMenuResult">
		<result property="roleId"     column="role_id"      />
		<result property="menuId"     column="menu_id"      />
		<result property="sysCode"    column="sys_code"    />
	</resultMap>

	<select id="checkMenuExistRole" resultType="Integer">
	    select count(1) from sys_role_menu where menu_id = #{menuId}
	</select>

	<select id="selectRoleMenuByRoleId" resultType="com.zksr.system.domain.SysRoleMenu">
		select role_id roleId,menu_id menuId,sys_code sysCode from sys_role_menu where role_id=#{roleId}
	</select>

	<delete id="deleteRoleMenuByRoleId" parameterType="Long">
		delete from sys_role_menu where role_id=#{roleId}
	</delete>

	<delete id="deleteChildRoleMenu" >
		DELETE
		FROM
			sys_role_menu
		WHERE
			sys_code = #{sysCode}
		  AND NOT EXISTS (
				SELECT
					*
				FROM
					(
						SELECT
							*
						FROM
							sys_role_menu
						WHERE
							EXISTS (
									SELECT
										1
									FROM
										sys_user_role
									WHERE
										user_id = #{userId}
									  AND sys_user_role.role_id = sys_role_menu.role_id
								)
					) b
				WHERE
					 sys_role_menu.menu_id = b.menu_id
		            <!-- sys_role_menu.menu_code = b.menu_code -->
			)
	</delete>

	<delete id="deleteRoleMenu" parameterType="Long">
 		delete from sys_role_menu where role_id in
 		<foreach collection="array" item="roleId" open="(" separator="," close=")">
 			#{roleId}
        </foreach>
 	</delete>

	<insert id="batchRoleMenu">
		insert into sys_role_menu(role_id, menu_id, sys_code) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.roleId},#{item.menuId},#{item.sysCode})
		</foreach>
	</insert>

<!--		<insert id="batchRoleMenu">
		insert into sys_role_menu(role_id, menu_code, sys_code) values
		<foreach item="item" index="index" collection="list" separator=",">
			(#{item.roleId},#{item.menuCode},#{item.sysCode})
		</foreach>
	</insert>-->

</mapper>
