
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.enums.request.VisualTemplateType;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.system.ZksrMallSystemApplication;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.mq.SyncDataProducer;
import com.zksr.system.visualSyn.SyncDataTemplate;
import com.zksr.system.api.EmailMessage.dto.SyncEmailData;
import com.zksr.system.visualSyn.service.IEmailMessageService;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallSystemApplication.class)
@Slf4j
public class ZksrMallSystemApplicationTests {

    /*@Autowired
    private ISysSmsService smsService;

    @Test
    public void testRedisService() throws InterruptedException {
        SmsCodeValidDTO dto = new SmsCodeValidDTO();
        dto.setPhone("15580493965");
        dto.setCode("123456");
        smsService.validateSmsCode(dto);
    }*/

    /*@Test
    public void testRedisService() throws InterruptedException {

        SubscribeMsgConfigVO configVO = new SubscribeMsgConfigVO();
        List<SubscribeMsgConfigVO.SubscribeField> itemList = new ArrayList<>();
        {
            SubscribeMsgConfigVO.SubscribeField field = new SubscribeMsgConfigVO.SubscribeField();
            field.setName("thing4");
            field.setVariable("memberName");
            itemList.add(field);
        }
        {
            SubscribeMsgConfigVO.SubscribeField field = new SubscribeMsgConfigVO.SubscribeField();
            field.setName("thing2");
            field.setVariable("spuName");
            itemList.add(field);
        }
        {
            SubscribeMsgConfigVO.SubscribeField field = new SubscribeMsgConfigVO.SubscribeField();
            field.setName("amount3");
            field.setVariable("payAmt");
            itemList.add(field);
        }
        {
            SubscribeMsgConfigVO.SubscribeField field = new SubscribeMsgConfigVO.SubscribeField();
            field.setName("time9");
            field.setVariable("orderCreateTime");
            itemList.add(field);
        }
        {
            SubscribeMsgConfigVO.SubscribeField field = new SubscribeMsgConfigVO.SubscribeField();
            field.setName("character_string1");
            field.setVariable("orderNo");
            itemList.add(field);
        }
        configVO.setFields(itemList);

        System.out.println(JSON.toJSONString(configVO));
    }*/

    @Autowired
    private SyncDataTemplate syncBranch;

    @Test
    public void testyncAfter() {
        //{"dataId":"519779824130424832","operationTypeCode":"add","propertyDelayTimeLevel":1,"reqId":"4d329f83cb9846a9ab0b7aad4eddb420","supplierId":15580493988,"sysCode":4,"templateType":501}
        SyncDataDTO syncDataDTO = new SyncDataDTO();
        syncDataDTO.setDataId("516400728531206144");
        syncDataDTO.setOperationTypeCode("add");
        syncDataDTO.setPropertyDelayTimeLevel(1);
        syncDataDTO.setReqId("4d329f83cb9846a9ab0b7aad4eddb420");
        syncDataDTO.setSupplierId(15580493988L);
        syncDataDTO.setSysCode(4L);
        syncDataDTO.setTemplateType(VisualTemplateType.BRANCH_TYPE.getType());
        syncBranch.invoke(syncDataDTO);
    }

    @Resource
    private SpuApi spuApi;

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Autowired
    private SyncDataProducer syncDataProducer;


    @Test
    public void test() {

/*        LoginOpensource loginOpensource = new LoginOpensource();
        loginOpensource.setSysCode(9990l);
        loginOpensource.setOpensourceId(9999l);
//
//        SecurityContextHolder.setSysCode(loginOpensource.getSysCode() + "");
//        SecurityContextHolder.setOpensourceId(loginOpensource.getOpensourceId()+"");
//        SecurityContextHolder.set(OpenapiSecurityConstants.LOGIN_OPENSOURCE, JSON.toJSON(loginOpensource));

        String json = JSON.toJSONString(loginOpensource);
        System.out.println(json);

        SpuReceiveVO spuReceiveVO = new SpuReceiveVO();
        Boolean checkedData = spuApi.addOrUpdateSpu(999l, 9999l, JSON.toJSONString(loginOpensource), spuReceiveVO).getCheckedData();
        System.out.println(checkedData);
        String s = "{\"opensourceDto\":{\"logisticsInfo\":\"0\",\"merchantId\":15580493988,\"merchantType\":\"supplier\",\"opensourceId\":486788286221484032,\"orderAutoPush\":1,\"orderDelayPushTime\":1,\"privateKey\":\"MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAl2z4FV3y8JwWgVJR20U3dXYbxKeMXO9T7gvbrC/NKuPkiBcnLkGrmI/X7KqYNsSrmcoAkYs8QTmZg6e//o4+4QIDAQABAkBQGXFFhDPdOQPilpP8RCno9XgM14A1LNgdxKIH/CUwORolXFzJ53Cx/G0QuUSvkwN3aaTyRXT0QSU4DspJS23tAiEAyw/d8s3mcwIhyxco0QWBMGpSXCG+NXDHDpxno4KeUK8CIQC+5vNxE0wDCCD6LfbEtl9KhKPOf+z+Hg0GFVohwY2tbwIhAKhphnij0Cc+c2yOzqlc2WKgK6KB0dFcsZsqmMIByO+9AiAJ7VibeLVrMvEEEX/Tmug5p8wc4OoiDM4Akvus5bPIYQIgSEza5U1fu2u45C5B/LjmwER+6jLJJZaaKlCpsS4FL9s=\",\"publicKey\":\"MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJds+BVd8vCcFoFSUdtFN3V2G8SnjFzvU+4L26wvzSrj5IgXJy5Bq5iP1+yqmDbEq5nKAJGLPEE5mYOnv/6OPuECAwEAAQ==\",\"sendCode\":\"002\",\"sendUrl\":\"https://579t55a763.goho.co/erp11.0/gateway/receive_gateway!\",\"sourceKey\":\"acffd23f2bb8453db95ddb1762f1c648\",\"sourceSecret\":\"QTJENjJERERGOTdENkExOTQ4MzBDNjA3MzE0ODczRDcyQTA4OTBGMlJVSkZOVGxEUWpRMk9UZzVSa05ET1RORlFUQkNRVFV3UVRBMU56UkJNRFU9\",\"sysCode\":4,\"token\":\"89f45a22-bb7d-4143-8048-87ed8b8ead8d\",\"visualMasterId\":511521817490620416},\"opensourceId\":486788286221484032,\"sysCode\":4}";

        Boolean checkedData1 = supplierOrderApi.receiveOrderOutboundReturn(999l, 9999l, null, new OrderOutboundReturnVO()).getCheckedData();
        System.out.println(checkedData1);*/

        //组装需要发送的数据信息
        SyncDataDTO syncDataDTO = new SyncDataDTO();
/*        //订单
        syncDataDTO.setDataId("XSS240930160001169953").setReqId(IdUtils.fastSimpleUUID()).setSysCode(4L).setSupplierId(15580493948L).setTemplateType(VisualTemplateType.ORDER_TYPE.getType());*/

/*        //售后
        syncDataDTO.setDataId("SHS241011750001166532").setReqId(IdUtils.fastSimpleUUID()).setSysCode(4L).setSupplierId(15580493948L).setTemplateType(VisualTemplateType.AFTER_TYPE.getType());*/

                //收款
        syncDataDTO.setDataId("XSS241012880002331648").setReqId(IdUtils.fastSimpleUUID()).setSysCode(4L)
                .setSupplierId(537121231358296064L).setTemplateType(VisualTemplateType.RECEIPT_TYPE.getType()).setSheetType(SheetTypeConstants.XSS).setIsProceeds(1).setReceilptType("12");
/*        syncDataDTO.setDataId("SHS241021050000104237").setReqId(IdUtils.fastSimpleUUID()).setSysCode(4L)
                .setSupplierId(537121231358296064L).setTemplateType(VisualTemplateType.RECEIPT_TYPE.getType()).setSheetType(SheetTypeConstants.SHS).setIsProceeds(1).setReceilptType("22");*/

/*        //门店
        syncDataDTO.setDataId("528312511113166848").setReqId(IdUtils.fastSimpleUUID()).setSysCode(4L).setSupplierId(537121231358296064L).setTemplateType(VisualTemplateType.BRANCH_TYPE.getType());*/

        //XSS240930220000147546
        //XSS240930660000189905
        //异步处理需要同步的入驻商订单数据
        //director.invoke(syncDataDTO);


    }


    /*@Autowired
    private AbstractSubscribeMessageHandler abstractSubscribeMessageHandler;

    @Test
    public void test02() {
        SubscribeEventLocalDeliveryVO localDeliveryVO = SubscribeEventLocalDeliveryVO.builder()
                .orderId(556408482087600128L)
                .supplierId(15580493951L)
                .build();

        SubscribeOrderCancelVO cancelVO = SubscribeOrderCancelVO.builder()
                .sysCode(4L)
                .orderId(557584293810274304L)
                .supplierAfterDtlIdList(ListUtil.toList(557584362529751042L, 557584362529751043L))
                .build();

        SubscribeOrderReceiveVO receiveVO = SubscribeOrderReceiveVO.builder()
                .supplierOrderDtlIdList(ListUtil.toList(556408482087600131L))
                .build();


        SubscribeOrderAfterCreateVO afterCreateVO = SubscribeOrderAfterCreateVO
                .builder()
                .orderId(557584293810274304L)
                .supplierAfterDtlIdList(ListUtil.toList(557584362529751042L, 557584362529751043L))
                .sysCode(4L)
                .build();

        SubscribeOrderAfterFinishVO afterFinishVO = SubscribeOrderAfterFinishVO.builder()
                .sysCode(4L)
                .supplierAfterDtlIdList(ListUtil.toList(557584362529751042L, 557584362529751043L))
                .sysCode(4L)
                .build();

        abstractSubscribeMessageHandler.processEvent(new SubscribeEventBodyVO<Long>(
                466979106766028801L,
                4L,
                CommonMessageSceneEnum.BRANCH_REGISTER.getScene()
        ));
    }*/


    @Autowired
    private IEmailMessageService emailMessageService;

    @Test
    public void testEmail() {
        OpensourceDto opensourceDto = new OpensourceDto();
        opensourceDto.setSubscribeSendEmail("501,502,503,504");
        opensourceDto.setAlarmEmail("<EMAIL>");
        emailMessageService.sendSyncEmail(new SyncDataDTO("567175458309210113",15580493948L,501,4L),opensourceDto,new SyncEmailData("这个是头","这个是内容","这个是校验信息","这个是响应结果"));
    }
}
