package com.zksr.system.service.impl;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.system.ZksrMallSystemApplication;
import com.zksr.system.visualSyn.SyncDataTemplate;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * @Description: 推送数据测试
 * @Date: 2025/07/18
 *
 */
@Slf4j
@ActiveProfiles({"sit"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallSystemApplication.class)
public class SyncDataTemplateTest {
    @Autowired
    private SyncDataTemplate syncOrder;
    @Autowired
    private SyncDataTemplate syncAfter;

    /**
     * 测试入驻商订单推送
     */
    @Test
    public void testSupplierSyncDataOrderEvent() {
        // 构造测试数据
        SyncDataDTO req = new SyncDataDTO();
        req.setSupplierId(640000124946219008L);
        req.setDataId("XSS250718650243547860");
        req.setReqId(IdUtils.fastSimpleUUID());
        req.setSysCode(4L);
        req.setTemplateType(502);
        req.setPropertyDelayTimeLevel(1);
        req.setIsProceeds(1);

        syncOrder.invoke(req);

    }

    /**
     * 测试入驻商售后单单推送
     */
    @Test
    public void testSupplierSyncDataAfterEvent() {
        // 构造测试数据
        SyncDataDTO req = new SyncDataDTO();
        req.setSupplierId(640000124946219008L);
        req.setDataId("SHS250721390137384712");
        req.setReqId(IdUtils.fastSimpleUUID());
        req.setSysCode(4L);
        req.setTemplateType(503);
        req.setPropertyDelayTimeLevel(1);
        req.setIsProceeds(1);

        syncAfter.invoke(req);

    }
}
