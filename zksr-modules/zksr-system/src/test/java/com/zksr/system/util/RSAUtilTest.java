package com.zksr.system.util;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.FileUrlResource;
import org.springframework.core.io.Resource;

import java.io.IOException;
import java.io.InputStream;

/**
 * 根据美的付证书生成私钥、公钥
 */
@Slf4j
@SpringBootTest
public class RSAUtilTest {

    private static String fileName = "E:\\美的付\\midea_sit.pfx";
    private static String password = "qahsrjze";

    @Test
    public void testGenerateKey() {
        try{
            String privateKey = getPriKey(fileName, password);
            log.info("获取私钥:{}",privateKey);

            String publicKey = getPubKey(fileName, password);
            log.info("获取公钥:{}",publicKey);
        }catch (Exception e){
            log.error("证书解析异常：",e);
        }
    }

    private static String getPriKey(String fileName, String password) {
        try {
            Resource resource = new FileUrlResource(fileName);
            InputStream input = resource.getInputStream();
            return PfxUtil.getPriKeyEncodedWithBase64(input, password);
        } catch (IOException var5) {
            return null;
        }
    }
    private static String getPubKey(String fileName, String password) {
        try {
            Resource resource = new FileUrlResource(fileName);
            InputStream input = resource.getInputStream();
            return PfxUtil.getPubKeyEncodedWithBase64(input, password);
        } catch (IOException var5) {
            return null;
        }
    }
}
