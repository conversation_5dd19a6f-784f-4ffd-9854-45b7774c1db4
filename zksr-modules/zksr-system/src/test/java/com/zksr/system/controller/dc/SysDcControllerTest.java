package com.zksr.system.controller.dc;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.ZksrMallSystemApplication;
import com.zksr.system.controller.dc.vo.SysDcSaveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @Author: chenyj8
 * @Desciption: 测试类
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallSystemApplication.class)
public class SysDcControllerTest {
//    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private SysDcController sysDcController;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(sysDcController)
                .setValidator(new LocalValidatorFactoryBean())
                .build();

    }

    @Test
//    @WithMockUser(username = "zksr", roles = {"member:branch:add"}) // 模拟一个有权限的用户
    public void testEdit() throws Exception {
        SysDcSaveReqVO updateReqVO = new SysDcSaveReqVO();
        updateReqVO.setDcId(3L);
        updateReqVO.setMemo("本地自测");
        updateReqVO.setMinAmt(null);
        updateReqVO.setGlobalMinAmt(null);

        List<Long> sysAreaId = new ArrayList<>();
        sysAreaId.add(4301L);
        updateReqVO.setSysAreaId(sysAreaId);

        mockMvc.perform(put("/dc")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }


}