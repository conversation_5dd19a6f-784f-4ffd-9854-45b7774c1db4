//package com.zksr.system.mq;
//
//import com.zksr.common.core.domain.vo.openapi.receive.MemBranchRegisterReqDTO;
//import com.zksr.common.core.utils.JsonUtils;
//import com.zksr.system.ZksrMallSystemApplication;
//import com.zksr.system.domain.SysInterfaceLog;
//import com.zksr.system.openapi.service.OpenapiProcessTemplate;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.runner.RunWith;
//import org.mockito.ArgumentCaptor;
//import org.mockito.Mockito;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import javax.annotation.Resource;
//import java.util.UUID;
//import java.util.function.Consumer;
//
//import static org.mockito.Mockito.*;
//
//@Slf4j
//@ActiveProfiles({"dev"})
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ZksrMallSystemApplication.class)
//class OpenapiConsumerTest {
//
//    private OpenapiConsumer openapiConsumer;
//    @Resource
//    private OpenapiProcessTemplate registerBranch;
//
//    @BeforeEach
//    void setUp() {
//        openapiConsumer = new OpenapiConsumer();
////        registerBranch = mock(OpenapiProcessTemplate.class);
////        // 注入mock的registerBranch
////        ReflectionTestUtils.setField(openapiConsumer, "registerBranch", registerBranch);
//    }
//
//    @Test
//    void testOpenapiRegisterBranch() {
//        // 构造测试数据
//        SysInterfaceLog req = new SysInterfaceLog();
//        req.setRequestType(17);
//
//        // 获取Consumer
//        Consumer<SysInterfaceLog> consumer = openapiConsumer.openapi_register_branch();
//
//        // 执行消费
//        consumer.accept(req);
//
//        // 验证registerBranch.invoke被正确调用
//        verify(registerBranch, times(1)).invoke(req);
//    }
//
//    @Test
//    void testRegisterBranch() {
//        // 构造测试数据
//        SysInterfaceLog req = new SysInterfaceLog();
//        req.setRequestType(17);
//        MemBranchRegisterReqDTO reqDTO = new MemBranchRegisterReqDTO();
//        reqDTO.setReqId(UUID.randomUUID().toString());
//        reqDTO.setSysCode(4L);
//        reqDTO.setDistrictName("天心区");
//        reqDTO.setCityName("长沙市");
//        reqDTO.setProvinceName("湖南省");
//
//
//        reqDTO.setBranchName("cyj-test1");
//        reqDTO.setContactName("cyj-test用户名");
//        reqDTO.setContactPhone("13650762000");
//        reqDTO.setBranchAddr("测试地址");
//
//        req.setReqData(JsonUtils.toJsonString(reqDTO));
//        registerBranch.invoke(req);
//    }
//}
package com.zksr.system.mq;

import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.receive.MemBranchRegisterReqDTO;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.system.ZksrMallSystemApplication;
import com.zksr.system.domain.SysInterfaceLog;
import com.zksr.system.openapi.service.OpenapiProcessTemplate;
import com.zksr.system.visualSyn.SyncDataTemplate;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.util.UUID;
import java.util.function.Consumer;

import static org.mockito.Mockito.*;

@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallSystemApplication.class)
class OpenapiConsumerTest {

    private OpenapiConsumer openapiConsumer;

    private SyncDataConsumer syncDataConsumer;
    @Resource
    private OpenapiProcessTemplate registerBranch;

    @Resource
    private SyncDataTemplate syncDataTemplate;

    @Autowired
    private SyncDataTemplate syncOrder;

    @BeforeEach
    void setUp() {
        openapiConsumer = new OpenapiConsumer();
        syncDataConsumer = new SyncDataConsumer();
//        registerBranch = mock(OpenapiProcessTemplate.class);
//        // 注入mock的registerBranch
//        ReflectionTestUtils.setField(openapiConsumer, "registerBranch", registerBranch);
    }

    @Test
    void testOpenapiRegisterBranch() {
        // 构造测试数据
        SysInterfaceLog req = new SysInterfaceLog();
        req.setRequestType(17);

        // 获取Consumer
        Consumer<SysInterfaceLog> consumer = openapiConsumer.openapi_registerBranch();

        // 执行消费
        consumer.accept(req);

        // 验证registerBranch.invoke被正确调用
        verify(registerBranch, times(1)).invoke(req);
    }

    /**
     * 测试入驻商订单推送
     */
    @Test
    void testSupplierSyncDataOrderEvent() {
        // 构造测试数据
        SyncDataDTO req = new SyncDataDTO();
        req.setSupplierId(15580493955L);
        req.setDataId("XSS250612510000613481");
        req.setReqId("reqid2025061200008");
        req.setSysCode(4L);
        req.setOperationTypeCode("add");
        req.setTemplateType(502);
        req.setPropertyDelayTimeLevel(1);
        req.setIsProceeds(1);

        syncOrder.invoke(req);

        // 获取Consumer
//        Consumer<SyncDataDTO> consumer = syncDataConsumer.supplierSyncDataOrderEvent();
//
//        // 执行消费
//        consumer.accept(req);
//
//        // 验证registerBranch.invoke被正确调用
//        verify(syncDataTemplate, times(1)).invoke(req);
    }

    @Test
    void testRegisterBranch() {
        // 构造测试数据
        SysInterfaceLog req = new SysInterfaceLog();
        req.setRequestType(17);
        MemBranchRegisterReqDTO reqDTO = new MemBranchRegisterReqDTO();
        reqDTO.setReqId(UUID.randomUUID().toString());
        reqDTO.setSysCode(4L);
        reqDTO.setDistrictName("天心区");
        reqDTO.setCityName("长沙市");
        reqDTO.setProvinceName("湖南省");


        reqDTO.setBranchName("cyj-test1");
        reqDTO.setContactName("cyj-test用户名");
        reqDTO.setContactPhone("13650762000");
        reqDTO.setBranchAddr("测试地址");

        req.setReqData(JsonUtils.toJsonString(reqDTO));
        registerBranch.invoke(req);
    }
}