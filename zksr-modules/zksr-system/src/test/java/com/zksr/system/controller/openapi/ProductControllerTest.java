package com.zksr.system.controller.openapi;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.zksr.common.core.domain.vo.openapi.receive.AreaItemOpenDTO;
import com.zksr.common.core.enums.AreaItemOpenDTOItemUnitEnum;
import com.zksr.system.ZksrMallSystemApplication;
import com.zksr.system.controller.dc.SysDcController;
import com.zksr.system.controller.dc.vo.SysDcSaveReqVO;
import com.zksr.system.openapi.controller.ProductController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @Author: chenyj8
 * @Desciption: 测试类
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallSystemApplication.class)
public class ProductControllerTest {
//    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ProductController productController;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(productController)
                .setValidator(new LocalValidatorFactoryBean())
                .build();

    }

    @Test
//    @WithMockUser(username = "zksr", roles = {"member:branch:add"}) // 模拟一个有权限的用户
    public void testAddAreaItem() throws Exception {
        List<AreaItemOpenDTO> areaItemOpenDTOList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            AreaItemOpenDTO dto = new AreaItemOpenDTO();
            dto.setSpuNo("spu-"+i);
            dto.setType(1);
            dto.setItemUnit(AreaItemOpenDTOItemUnitEnum.CS.getCode());
            areaItemOpenDTOList.add(dto);
        }
        for (int i = 0; i < 5; i++) {
            AreaItemOpenDTO dto = new AreaItemOpenDTO();
            dto.setSpuNo("spu-"+i);
            dto.setType(1);
            dto.setItemUnit(AreaItemOpenDTOItemUnitEnum.IP.getCode());
            areaItemOpenDTOList.add(dto);
        }
        mockMvc.perform(post("/openapi/product/addAreaItem")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(areaItemOpenDTOList)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }


}