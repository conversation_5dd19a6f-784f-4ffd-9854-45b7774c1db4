package com.zksr.system.signature;

import com.zksr.common.core.utils.signature.MideaPayApiSignatureUtils;
import com.zksr.system.ZksrMallSystemApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;

/**
 * @Description: 美的API签名工具类测试
 * @Date: 2025/07/16
 */
@Slf4j
@ActiveProfiles({"sit"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallSystemApplication.class)
public class MideaPayApiSignatureUtilsTest {

    // 测试用的RSA密钥对（实际使用时应该使用美的提供的密钥）
    private static final String TEST_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKB" +
            "xEiOfHiGJjYwqXvtxEdHkTwgbhFIBcWSjp+8xdBzQGWw274wHHmQvsZGrpxCwLvi" +
            "uiV9NyGrDLxd5EgLiJdKWzAoSMDg+qzkQb+h5fcRBjAyqSy+hUAeRlN49Aa2ms4H" +
            "1nxVBBp6XqICLABFRVzXBw4q4ivjuixFBiJVXvBYQn0sDp1AV9RrliZXdcs4EEhI" +
            "YIxYVNXK3f1t6hYEcw9I6FX6LWoXz69TzllzMBOXufhxCKCkMp4GnDgGrAqVGUuG" +
            "coGHEkzAtFuMEAa7rxZMmd12xwYBV8DDjy01gBriQ4L3xJ3Yb6pZVN+5aANSTu10" +
            "wh8AgMBAAECggEBALKOtQBwhQFfkjviHzFxKbzK8oe7ygxdB0dxQ4oKvtbm4cD1" +
            "VoqzgZ0WpXdjKgHZ/kYmqrSz7qjFRxW9iutyAlBQnXoAWBe0SBjYnHPzgHQmwqFy" +
            "T3k+CXQ8cGlOmFEpjdhzpSFQuoYgxkMMr/7itsEhMlNpwmsZ4HqyS0gQBFLOWMU9" +
            "6jvHOTgpRMKlbENl4PjWS5LlIRD3lM2AnBjGcHlLtXXx3A+CY9k2Py4PwdVLYhFi" +
            "GVJ3jHNKroAHNgGQoBdxaS0hAc8+lJECS6SClCIb1s+GxEXH4LMpdg6TRVxzSHr7" +
            "BdqEreNMdPfOLKMHl93g24DlKLiFaggHGb9gm2ECgYEA4ZU4qwI4IdQ+IJyEAHEG" +
            "FQCqXMJQxOvLWiZr+mT1V+l/dNDNOOCdyrmbo2gGGtqsAiGnOd/Q2rXheLhwbyD+" +
            "LFkFbXIuEHdiIz+vzfTrmBfVRocHuKOhjd1cSf7Y+A+nrI7j0M7SczitVELQ+aTL" +
            "DSFmsStnyg4QjgdNJQmcl2sCgYEA1KMfoQBe7Q4ohBXuQiTnMcs2tamN8jSNrpOv" +
            "SpQoReMPiGBXelM3iBem4SJtRNbvsqjLcBvt8A7A6zyCpQrCJ4SXZS9Mq9H5Bn8K" +
            "WfVfZahAlXw53OOO/1NVby/wqSFLA2R9IQBdmZnGgxGXmiq2xa2wsVISX+gKyg8S" +
            "X6VJj+sCgYAd4LdXBOxuJugneLzA6TuBKfRPuAiK6+bJ4hQvKpimLEVyAL8qmyXx" +
            "ct1pu8n5yAzPjLGOfNgoH1aBsKI7l7AtjKHFxcU1HnFceM+7UOGbVxWlyT7zM8Q4" +
            "NUmKrkg2A2j8Oy2vDCN9B0oQsvkBiCDjdI3Gu08Zu6YDNB3yOQKBgQC5jxv5l5Xr" +
            "eoXMdRDrUBvOqhzlPCXheLr1zaoWWcMxqNjr6xMHdTxtZJaPQs6Ir9y2AlkzjWSf" +
            "wHkjoQiez1C1LRMnOy2HrQ8HzgL/Hs1yOx7cq+DyHUwCY0cMQoGBANmjNQAmJmlJ" +
            "RP8BuQHBgPjQU6phiHXK8xXWiwWV0eoKuuXBbADnJfyu1hPMtVnP4bwjXGxGas/z" +
            "TmMa2jzVhplN0FBHjy5Ir5o8wjdfnyHESHzz7Dtgr7C0QD2cNs4kKS53CjRAfwSV" +
            "tCKWQoQ==";

    private static final String TEST_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu1SU1LfVLPHCgcRIjnx4" +
            "hiY2MKl77cRHR5E8IG4RSAXFko6fvMXQc0BlsNu+MBx5kL7GRq6cQsC74rolfTch" +
            "qwy8XeRIC4iXSlswKEjA4Pqs5EG/oeX3EQYwMqksvgVAHkZTeOQGtprOB9Z8VQQa" +
            "el6iAiwARUVc1wcOKuIr47osRQYiVV7wWEJ9LA6dQFfUa5YmV3XLOBBISGCMWFTVyt39beoWBHMPSOhV+i1qF8+vU85ZczATl7n4cQigpDKeBpw4BqwKlRlLhnKBhxJMwLRbjBAGu68WTJnddscGAVfAw48tNYAa4kOC98Sd2G+qWVTfuWgDUk7tdMIfAIDARB";

    @Test
    public void testBuildSignContentExample() {
        // 测试构建签名内容（基于文档示例）
        Map<String, String> params = new HashMap<>();
        params.put("app_id", "MCSP-EIS");
        params.put("merchant_id", "0000111008");
        params.put("request-id", "6b4cfa8de67c49a08b66a0c77137bba7");
        params.put("version", "1.0");

        String requestBody = "{\"bizId\":\"16400069659452154\",\"transType\":1}";

        String signContent = buildSignatureExample();
        String expected = "app_id=MCSP-EIS&body={\"bizId\":\"16400069659452154\",\"transType\":1}&merchant_id=0000111008&request-id=6b4cfa8de67c49a08b66a0c77137bba7&version=1.0";

        assertEquals(expected, signContent);
        System.out.println("构建的签名内容: " + signContent);
    }

    @Test
    public void testGenerateRequestSignature() {
        // 测试生成请求签名
        Map<String, String> params = new HashMap<>();
        params.put("app_id", "MCSP-EIS");
        params.put("merchant_id", "0000111008");
        params.put("request-id", "6b4cfa8de67c49a08b66a0c77137bba7");
        params.put("version", "1.0");

        String requestBody = "{\"bizId\":\"16400069659452154\",\"transType\":1}";

        String signature = MideaPayApiSignatureUtils.generateRequestSignature(params, requestBody, TEST_PRIVATE_KEY);

        assertNotNull(signature);
        assertFalse(signature.isEmpty());
        System.out.println("生成的请求签名: " + signature);
    }

    @Test
    public void testVerifyResponseSignature() {
        // 测试验证响应签名
        String responseBody = "{\"code\":\"00000\",\"message\":\"success\",\"data\":{\"result\":\"test\"}}";

        // 先生成一个签名作为"接收到的签名"
        String signature = MideaPayApiSignatureUtils.sign(responseBody, TEST_PRIVATE_KEY);

        boolean isValid = MideaPayApiSignatureUtils.verifyResponseSignature(responseBody, signature, TEST_PUBLIC_KEY);

        assertTrue(isValid);
        System.out.println("响应验签通过");
    }

    @Test
    public void testExtractRequestParams() {
        // 测试提取请求参数
        Map<String, String> urlParams = new HashMap<>();
        urlParams.put("app_id", "MCSP-EIS");
        urlParams.put("merchant_id", "0000111008");

        Map<String, String> headerParams = new HashMap<>();
        headerParams.put("version", "1.0");
        headerParams.put("request-id", "test_request_id");
        headerParams.put("Content-Type", "application/json"); // 应该被排除
        headerParams.put("User-Agent", "test-agent"); // 应该被排除

        Map<String, String> allParams = MideaPayApiSignatureUtils.extractRequestParams(urlParams, headerParams);

        assertEquals(4, allParams.size());
        assertTrue(allParams.containsKey("app_id"));
        assertTrue(allParams.containsKey("merchant_id"));
        assertTrue(allParams.containsKey("version"));
        assertTrue(allParams.containsKey("request-id"));
        assertFalse(allParams.containsKey("Content-Type"));
        assertFalse(allParams.containsKey("User-Agent"));

        System.out.println("提取的请求参数: " + allParams);
    }

    @Test
    public void testBuildFullRequestParams() {
        // 测试构建完整请求参数
        Map<String, String> urlParams = new HashMap<>();
        urlParams.put("param1", "value1");
        urlParams.put("param2", "value2");

        Map<String, String> fullParams = MideaPayApiSignatureUtils.buildFullRequestParams(
                "0000111008", "MCSP-EIS", "1.0", "test_request_id", urlParams);

        assertEquals(6, fullParams.size());
        assertEquals("0000111008", fullParams.get("merchant_id"));
        assertEquals("MCSP-EIS", fullParams.get("app_id"));
        assertEquals("1.0", fullParams.get("version"));
        assertEquals("test_request_id", fullParams.get("request-id"));
        assertEquals("value1", fullParams.get("param1"));
        assertEquals("value2", fullParams.get("param2"));

        System.out.println("完整请求参数: " + fullParams);
    }

    @Test
    public void testParseRequestBodyForSignature() {
        // 测试解析请求体
        Object jsonObject = new HashMap<String, Object>() {{
            put("bizId", "test_biz_id");
            put("transType", 1);
        }};

        String parsedBody = MideaPayApiSignatureUtils.parseRequestBodyForSignature(jsonObject);

        assertNotNull(parsedBody);
        assertTrue(parsedBody.contains("bizId"));
        assertTrue(parsedBody.contains("test_biz_id"));

        System.out.println("解析的请求体: " + parsedBody);
    }

    @Test
    public void testGenerateRequestId() {
        // 测试生成请求ID
        String requestId1 = MideaPayApiSignatureUtils.generateRequestId();
        String requestId2 = MideaPayApiSignatureUtils.generateRequestId();

        assertNotNull(requestId1);
        assertNotNull(requestId2);
        assertNotEquals(requestId1, requestId2);
        assertEquals(32, requestId1.length()); // UUID去掉横线后的长度

        System.out.println("生成的请求ID1: " + requestId1);
        System.out.println("生成的请求ID2: " + requestId2);
    }

    @Test
    public void testValidateSignatureParams() {
        // 测试签名参数验证
        assertDoesNotThrow(() -> {
            MideaPayApiSignatureUtils.validateSignatureParams("merchant_id", "app_id", "private_key");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            MideaPayApiSignatureUtils.validateSignatureParams("", "app_id", "private_key");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            MideaPayApiSignatureUtils.validateSignatureParams("merchant_id", "", "private_key");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            MideaPayApiSignatureUtils.validateSignatureParams("merchant_id", "app_id", "");
        });
    }

    @Test
    public void testValidateVerifyParams() {
        // 测试验签参数验证
        assertDoesNotThrow(() -> {
            MideaPayApiSignatureUtils.validateVerifyParams("response_body", "signature", "public_key");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            MideaPayApiSignatureUtils.validateVerifyParams("", "signature", "public_key");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            MideaPayApiSignatureUtils.validateVerifyParams("response_body", "", "public_key");
        });

        assertThrows(IllegalArgumentException.class, () -> {
            MideaPayApiSignatureUtils.validateVerifyParams("response_body", "signature", "");
        });
    }

    @Test
    public void testSignatureWithEmptyBody() {
        // 测试空请求体的签名
        Map<String, String> params = new HashMap<>();
        params.put("app_id", "MCSP-EIS");
        params.put("merchant_id", "0000111008");
        params.put("version", "1.0");

        String signature = MideaPayApiSignatureUtils.generateRequestSignature(params, null, TEST_PRIVATE_KEY);

        assertNotNull(signature);
        assertFalse(signature.isEmpty());
        System.out.println("空请求体的签名: " + signature);
    }

    @Test
    public void testSignatureWithSpecialCharacters() {
        // 测试包含特殊字符的签名
        Map<String, String> params = new HashMap<>();
        params.put("app_id", "MCSP-EIS");
        params.put("merchant_id", "0000111008");
        params.put("special_param", "value with spaces & symbols!");
        params.put("chinese_param", "中文参数");

        String requestBody = "{\"message\":\"测试消息\",\"special\":\"value&test=123\"}";

        String signature = MideaPayApiSignatureUtils.generateRequestSignature(params, requestBody, TEST_PRIVATE_KEY);

        assertNotNull(signature);
        assertFalse(signature.isEmpty());
        System.out.println("特殊字符签名: " + signature);
    }

    @Test
    public void testFilterRequestSignatureParam() {
        // 测试过滤request-signature参数
        Map<String, String> params = new HashMap<>();
        params.put("app_id", "MCSP-EIS");
        params.put("merchant_id", "0000111008");
        params.put("request-signature", "should_be_filtered"); // 应该被过滤掉

        String signature1 = MideaPayApiSignatureUtils.generateRequestSignature(params, null, TEST_PRIVATE_KEY);

        // 移除request-signature参数
        params.remove("request-signature");
        String signature2 = MideaPayApiSignatureUtils.generateRequestSignature(params, null, TEST_PRIVATE_KEY);

        // 两个签名应该相同，因为request-signature参数被过滤了
        assertEquals(signature1, signature2);
        System.out.println("过滤request-signature参数测试通过");
    }

    /**
     * 构建签名示例（用于调试）
     */
    public static String buildSignatureExample() {
        Map<String, String> params = new HashMap<>();
        params.put("app_id", "MCSP-EIS");
        params.put("merchant_id", "0000111008");
        params.put("request-id", "6b4cfa8de67c49a08b66a0c77137bba7");
        params.put("version", "1.0");

        String requestBody = "{\"bizId\":\"16400069659452154\",\"transType\":1}";

        return MideaPayApiSignatureUtils.buildSignContent(params, requestBody);
    }
}
