package com.zksr.system.openapi.service;

import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.domain.vo.openapi.receive.SpuOpenDTO;
import com.zksr.system.ZksrMallSystemApplication;
import com.zksr.system.domain.SysInterfaceLog;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallSystemApplication.class)
class OpenapiProcessTemplateTest {

    @Resource
    private OpenapiProcessTemplate saveprdt;
    // 创建OpenapiProcessTemplate的匿名子类用于测试抽象方法
    private final OpenapiProcessTemplate template = new OpenapiProcessTemplate() {
        @Override
        protected Boolean process(SysInterfaceLog sysInterfaceLog) {
            // 简单返回true用于测试
            return true;
        }
    };

    @Test
    void testProcess() {
        SysInterfaceLog log = new SysInterfaceLog();
        SpuOpenDTO spuOpenDTO = new SpuOpenDTO();
        spuOpenDTO.setImages("https://img.shop.hisense.com/2025/07/29/de5c1d7c-488a-45d2-93f3-bae227152ec3.jpg");
//        spuOpenDTO.setDetails("https://test-1317294607.cos.ap-guangzhou.myqcloud.com/%E5%95%86%E6%B5%81/es.txt,dddd,https://test-1317294607.cos.ap-guangzhou.myqcloud.com/%E5%95%86%E6%B5%81/es.txt,aaa");
        log.setBizData(JSON.toJSONString(spuOpenDTO));
        Boolean result = saveprdt.process(log);
        assertTrue(result);
    }


} 