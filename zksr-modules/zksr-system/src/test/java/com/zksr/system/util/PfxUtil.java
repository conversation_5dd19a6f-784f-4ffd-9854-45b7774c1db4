package com.zksr.system.util;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Enumeration;

/**
 * @description pfx证书获得公钥、私钥和证书对象工具
 */
public class PfxUtil {

    private static final Logger logger = LoggerFactory.getLogger(PfxUtil.class);

    // 证书存储密钥的方式
    public static final String KEY_STORE_TYPE = "PKCS12";
    private static KeyStore keyStore;

    static {
        try {
            if (keyStore == null) {
                keyStore = KeyStore.getInstance(KEY_STORE_TYPE);
            }
        } catch (KeyStoreException e) {
            logger.error("获取KeyStore参数错误, type={}", KEY_STORE_TYPE, e);
        }
    }

    private PfxUtil() {
    }

    /**
     * 获得私钥Base64字符串
     *
     * @param pfxPath  pfx证书流
     * @param password 证书密码
     * @return 私钥字符串
     */
    public static String getPriKeyEncodedWithBase64(InputStream pfxPath, String password) {
        PrivateKey privateKey = getPrivateKey(pfxPath, password);
        if (privateKey == null) {
            return null;
        }
        return Base64.encodeBase64String(privateKey.getEncoded());
    }

    /**
     * 获得私钥Base64字符串
     *
     * @param pfxPath  证书绝对路径
     * @param password 证书密码
     * @return 私钥字符串
     */
    public static String getPriKeyEncodedWithBase64(String pfxPath, String password) {
        PrivateKey privateKey = getPrivateKey(pfxPath, password);
        if (privateKey == null) {
            return null;
        }
        return Base64.encodeBase64String(privateKey.getEncoded());
    }

    /**
     * 获得私钥Base64字符串
     *
     * @param pfxData  pfx数据
     * @param password 证书密码
     * @return 私钥字符串
     */
    public static String getPriKeyEncodedWithBase64(byte[] pfxData, String password) {
        PrivateKey privateKey = getPrivateKey(new ByteArrayInputStream(pfxData), password);
        if (privateKey == null) {
            return null;
        }
        return Base64.encodeBase64String(privateKey.getEncoded());
    }

    /**
     * 获得公钥Base64字符串
     *
     * @param pfxPath  pfx证书绝对路径
     * @param password 证书密码
     * @return 私钥字符串
     */
    public static String getPubKeyEncodedWithBase64(String pfxPath, String password) {
        X509Certificate certificate = getX509Certificate(pfxPath, password);
        if (certificate == null) {
            return null;
        }
        PublicKey publicKey = certificate.getPublicKey();
        if (publicKey == null) {
            return null;
        }
        return Base64.encodeBase64String(publicKey.getEncoded());
    }

    /**
     * 获得公钥Base64字符串
     *
     * @param pfxData  pfx数据
     * @param password 证书密码
     * @return 私钥字符串
     */
    public static String getPubKeyEncodedWithBase64(byte[] pfxData, String password) {
        return getPubKeyEncodedWithBase64(new ByteArrayInputStream(pfxData), password);
    }

    /**
     * 获得公钥Base64字符串
     *
     * @param pfxData  pfx数据
     * @param password 证书密码
     * @return 私钥字符串
     */
    public static String getPubKeyEncodedWithBase64(InputStream pfxData, String password) {
        X509Certificate certificate = getX509Certificate(pfxData, password);
        if (certificate == null) {
            return null;
        }
        PublicKey publicKey = certificate.getPublicKey();
        if (publicKey == null) {
            return null;
        }
        return Base64.encodeBase64String(publicKey.getEncoded());
    }

    /**
     * 根据pfx证书得到私钥
     *
     * @param pfxPath  pfx证书路径
     * @param password 证书密码
     */
    private static PrivateKey getPrivateKey(String pfxPath, String password) {
        File file = new File(pfxPath);
        try (InputStream input = new FileInputStream(file)) {
            return getPrivateKey(input, password);
        } catch (IOException e) {
            logger.error("证书未找到，路径pfxPath={}", pfxPath, e);
        }
        return null;
    }

    /**
     * 根据pfx证书得到私钥
     *
     * @param pfxData  pfx证书流
     * @param password 证书密码
     */
    private static PrivateKey getPrivateKey(InputStream pfxData, String password) {
        PrivateKey privateKey = null;
        KeyStore keystore = getKeyStore(pfxData, password);
        String keyAlias = null;
        try {
            Enumeration<String> enums = keystore.aliases();
            while (enums.hasMoreElements()) {
                keyAlias = enums.nextElement();
                if (keystore.isKeyEntry(keyAlias)) {
                    privateKey = (PrivateKey) keystore.getKey(keyAlias, password.toCharArray());
                }
            }
        } catch (KeyStoreException e) {
            logger.error("证书加载错误", e);
        } catch (NoSuchAlgorithmException e) {
            logger.error("加载证书的算法异常", e);
        } catch (UnrecoverableKeyException e) {
            logger.error("加载证书密码错误, password={}", password, e);
        }
        return privateKey;
    }

    /**
     * 根据pfx证书获取证书对象
     *
     * @param pfxPath  pfx证书路径
     * @param password pfx证书密码
     * @return
     */
    private static X509Certificate getX509Certificate(String pfxPath, String password) {
        File pfxFile = new File(pfxPath);
        try (InputStream input = new FileInputStream(pfxFile)) {
            return getX509Certificate(input, password);
        } catch (IOException e) {
            logger.error("证书未找到，路径pfxPath={}", pfxPath, e);
        }
        return null;
    }

    /**
     * 根据pfx证书获取keyStore
     *
     * @param pfxData  pfx数据
     * @param password 证书密码
     * @return KeyStore对象
     */
    private static KeyStore getKeyStore(InputStream pfxData, String password) {
        try {
            keyStore.load(pfxData, password.toCharArray());
        } catch (IOException e) {
            logger.error("加载证书参数错误, type={}, password={}", KEY_STORE_TYPE, password, e);
        } catch (NoSuchAlgorithmException e) {
            logger.error("加载证书的算法异常", e);
        } catch (CertificateException e) {
            logger.error("加载证书文件异常", e);
        }
        return keyStore;
    }

    /**
     * 根据pfx证书获取证书对象
     *
     * @param pfxData  pfx文件流
     * @param password pfx证书密码
     * @return 证书对象
     */
    private static X509Certificate getX509Certificate(InputStream pfxData, String password) {
        X509Certificate x509Certificate = null;
        KeyStore keystore = getKeyStore(pfxData, password);
        String keyAlias = "";
        try {
            Enumeration<String> enums = keystore.aliases();
            while (enums.hasMoreElements()) {
                keyAlias = enums.nextElement();
                if (keystore.isKeyEntry(keyAlias)) {
                    x509Certificate = (X509Certificate) keystore.getCertificate(keyAlias);
                }
            }
        } catch (KeyStoreException e) {
            logger.error("证书加载错误", e);
        }
        return x509Certificate;
    }
}

