package com.zksr.system.service.impl;

import com.zksr.system.ZksrMallSystemApplication;
import com.zksr.system.api.invoice.dto.*;
import com.zksr.system.service.ISysMideaInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 美的发票服务集成测试
 * @Date: 2025/07/17
 * 
 * 注意：此测试需要真实的网络环境和美的API访问权限
 */
@TestPropertySource(properties = {
    "midea-pay.api.merchant-id=0000033005",
    "midea-pay.api.app-id=SaaS-B2b",
    "midea-pay.api.base-url=https://fenqitest.midea.com/invoice_uat",
    "midea-pay.api.version=1.0",
    "midea-pay.api.connect-timeout=30000",
    "midea-pay.api.read-timeout=60000",
    "midea-pay.api.privateKey=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCkPrr1NwQgSwgGOqgOpkIfD46KvxdU3dRgkD7RhLOofEiBtrT55JsdxSKiDim5MUjTrt8A2GueIieJzeKH3vLeyN9kxXU2845XZCePvkvcNDv3mBeTSXGbAK+NUY3cjCBB4AaCy0M5stWhb8AVNb7e8tzSYOdYMsqGC/8Sq4pxQAU+7lAvtu+gxp7PgtYiKh0mOZC1w/+yHLeglcKYHCOtrE415HxI0K0v7p5dfwpM19B48DnjqJFsbJSbp7+U+xWDzvjI5Mv6GL6q7EsKMfgqS9RKuKKop0IhUSfN8CxP2cGxjrUgM+9HRFMrAjrFiI31qN+FyBMalgj9BKZ2bSpVAgMBAAECggEAI79jgauPwleST4jEuyuEiK4iICCnO1lst/etNHzlfW5PeawCh8Ex4bDecNcUTbJkX25YKx7vjWNxp5SgEWJ9CfA+bvhrIqKwAGVUFv0cFblos+Lufqglic8EZoWxkqcM/kl8IUuMPiqmRJTGAdfIy02gsklVEOU9Hx/NTIr5ftxOevMz26u6IPHlvMRLeDF5HtWC3eBwuerFZgXWzTi6+Hb0cZJ6jat1NRMoK8F8g73WGfhefJjzzgzOGMkd/VPttGsdm9M2hX23/DeWJXhHO3eMfsiMOeyAU5lOZzbJ9qMipqIxpuo3r2cTNrHHoCJYp157PXLEhmSbHTCOhpExAQKBgQDk5rYLaVCv8jEVnR/Wm+9A2lWLNgjbCYMn54NpFbiS616WjYxqfYiAYiO1jRZpK4GLxomtfTECiMmt+EAYG62efh+wOsHk8YhV/TP572VlTidXIoUfLhcgpM655k4OK7TC0v6bZtO7Yj3E9MSpyWYmjNU/X7vBUtP0v8Kt+Q6t6QKBgQC3sH1+qEFjfiBBk0P7Iu28ReKOH+etckO2wxBlg9VGprO2pKFdK4PaF99Y0xthJOJeHRtOJEmbHHt8bwNQowyoHqcQJeJAChE+bo2htHTDUnkOobizPvcJOEfQPx9wvq/QB/hVSgiAZIubB8ZtMxbnTpE+4ClJbpNjmxeMZBq5jQKBgAEBgotKSzB6v/x4VEt9I/AFBZs3AZL01761Jb6web8riZ7fwF43bwIhbEa/9/k+V7Nli/VXN3/rWfeJv75bYMfxhfIgT/nRlefK1Q+kHa30Sff4Dt20NiXOk5n9iRpG1uSESNol+Yg5Rkw3RyX33JsOw2Ej6o9O7d+FeDWLgqUBAoGBALJyt4e7B0MTfVyXJzyrMw9rSOvPXO+oCNZc6+nix+fJ/N6bUtPDeWT/Og/9jiSyg2Lwnd0s7YDNi71Xz32f2KTemmfaldCmtxfSqK8GM0SpM0eenmMr8Su4zDMnj3ClHwFXLM4WjOnGQ8WJKSBeuIpfTkUp2ZIn5PGn8pmFilzlAoGBAM4tj2tDUsPjLBXLKjrUdQyE6Shw6KBWe+K2NYuHm7fpwU1ZYwstCW8en+pFIPHy9UmH3MlCPsB4EYGSzWV3ueZ2eBmf7vgVxSTs43MR5mq/oxSN02benVJOxCOwefQ595AR8frU+2cmCR476Jftp8ToTSFLnwovzJisTFsxo0H8",
    "midea-pay.api.publicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApD669TcEIEsIBjqoDqZCHw+Oir8XVN3UYJA+0YSzqHxIgba0+eSbHcUiog4puTFI067fANhrniInic3ih97y3sjfZMV1NvOOV2Qnj75L3DQ795gXk0lxmwCvjVGN3IwgQeAGgstDObLVoW/AFTW+3vLc0mDnWDLKhgv/EquKcUAFPu5QL7bvoMaez4LWIiodJjmQtcP/shy3oJXCmBwjraxONeR8SNCtL+6eXX8KTNfQePA546iRbGyUm6e/lPsVg874yOTL+hi+quxLCjH4KkvUSriiqKdCIVEnzfAsT9nBsY61IDPvR0RTKwI6xYiN9ajfhcgTGpYI/QSmdm0qVQIDAQAB",
    "logging.level.com.zksr.common.core.utils.signature=DEBUG",
    "logging.level.com.zksr.common.core.utils.http=DEBUG"
})
@Slf4j
@ActiveProfiles({"sit"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallSystemApplication.class)
public class SysMideaInvoiceServiceTest {

    @Resource
    private ISysMideaInvoiceService sysMideaInvoiceService;

    private TransBlueInvoiceRequest blueInvoiceRequest;
    private TransRedInvoiceRequest redInvoiceRequest;

    @Before
    public void setUp() {
        blueInvoiceRequest = createTestBlueInvoiceRequest();
        redInvoiceRequest = createTestRedInvoiceRequest();
    }

    @Test
    public void testTransBlueInvoiceSuccess() {
        // 测试蓝票开具成功场景
        System.out.println("=== 测试蓝票开具 ===");
        System.out.println("请求URL: http://msigntest.midea.com/invoice_ver/saas/invoice/trans/blue");
        System.out.println("商户ID: 0000033005");
        System.out.println("应用ID: SaaS-B2b");
        System.out.println("业务ID: " + blueInvoiceRequest.getBizId());
        System.out.println("发票类型: " + blueInvoiceRequest.getInvoiceType());
        System.out.println("是否直接开票: " + blueInvoiceRequest.getImmediateInvoice());
        System.out.println("发票金额: " + blueInvoiceRequest.getInvoiceAmt());

        try {
            Boolean result = sysMideaInvoiceService.transBlueInvoice(blueInvoiceRequest);

            System.out.println("蓝票开具结果: " + result);

            // 验证结果
            assertNotNull(result, "蓝票开具结果不能为空");

            if (result) {
                System.out.println("✅ 蓝票开具成功");
            } else {
                System.out.println("❌ 蓝票开具失败");
            }

        } catch (Exception e) {
            System.out.println("蓝票开具异常: " + e.getMessage());
            e.printStackTrace();

            // 在测试环境中，可能会因为网络、配置等问题导致异常
            // 这里我们验证异常是否被正确处理
            assertTrue(e.getMessage().contains("蓝票开具") || e.getMessage().contains("请求失败"),
                    "异常信息应该包含相关错误描述");
        }
    }

    @Test
    public void testTransRedInvoiceSuccess() {
        // 测试红票开具成功场景
        System.out.println("=== 测试红票开具/红冲 ===");
        System.out.println("请求URL: http://msigntest.midea.com/invoice_ver/saas/invoice/trans/red");
        System.out.println("商户ID: 0000033005");
        System.out.println("应用ID: SaaS-B2b");
        System.out.println("红冲业务ID: " + redInvoiceRequest.getRedBizId());
        System.out.println("发票类型: " + redInvoiceRequest.getInvoiceType());
        System.out.println("红冲金额: " + redInvoiceRequest.getRedAmount());
        System.out.println("红冲明细数量: " + redInvoiceRequest.getRedCount());

        try {
            Boolean result = sysMideaInvoiceService.transRedInvoice(redInvoiceRequest);

            System.out.println("红票开具结果: " + result);

            // 验证结果
            assertNotNull(result, "红票开具结果不能为空");

            if (result) {
                System.out.println("✅ 红票开具成功");
            } else {
                System.out.println("❌ 红票开具失败");
            }

        } catch (Exception e) {
            System.out.println("红票开具异常: " + e.getMessage());
            e.printStackTrace();

            // 在测试环境中，可能会因为网络、配置等问题导致异常
            // 这里我们验证异常是否被正确处理
            assertTrue(e.getMessage().contains("红票开具") || e.getMessage().contains("请求失败"),
                    "异常信息应该包含相关错误描述");
        }
    }

    @Test
    public void testFullInvoiceWorkflow() {
        // 完整的开票流程测试：蓝票开具 -> 红票冲红
        System.out.println("=== 完整开票流程测试 ===");
        
        StopWatch stopWatch = new StopWatch();
        
        try {
            // 步骤1：开具蓝票
            stopWatch.start("蓝票开具");
            System.out.println("步骤1：开具蓝票");
            System.out.println("业务ID: " + blueInvoiceRequest.getBizId());
            
            Boolean blueResult = sysMideaInvoiceService.transBlueInvoice(blueInvoiceRequest);
            stopWatch.stop();
            
            System.out.println("蓝票开具结果: " + blueResult);
            System.out.println("蓝票开具耗时: " + stopWatch.getLastTaskTimeMillis() + "ms");
            
            if (blueResult) {
                // 步骤2：等待一段时间（模拟业务处理时间）
                Thread.sleep(2000);
                
                // 步骤3：开具红票冲红
                stopWatch.start("红票开具");
                System.out.println("步骤2：开具红票冲红");
                System.out.println("红冲业务ID: " + redInvoiceRequest.getRedBizId());
                System.out.println("关联蓝票ID: " + redInvoiceRequest.getBizId());
                
                Boolean redResult = sysMideaInvoiceService.transRedInvoice(redInvoiceRequest);
                stopWatch.stop();
                
                System.out.println("红票开具结果: " + redResult);
                System.out.println("红票开具耗时: " + stopWatch.getLastTaskTimeMillis() + "ms");
                
                // 验证结果
                assertTrue(redResult, "红票开具应该成功");
            }
            
            System.out.println("=== 完整流程测试完成 ===");
            System.out.println("总耗时: " + stopWatch.getTotalTimeMillis() + "ms");
            
        } catch (Exception e) {
            System.out.println("完整流程测试异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testErrorHandling() {
        // 错误处理测试
        System.out.println("=== 错误处理测试 ===");
        
        // 测试1：无效的发票类型
        TransBlueInvoiceRequest invalidRequest1 = createTestBlueInvoiceRequest();
        invalidRequest1.setInvoiceType(999); // 无效的发票类型
        
        try {
            sysMideaInvoiceService.transBlueInvoice(invalidRequest1);
            fail("应该抛出异常");
        } catch (Exception e) {
            System.out.println("无效发票类型测试通过: " + e.getMessage());
        }
        
        // 测试2：缺少必填字段
        TransBlueInvoiceRequest invalidRequest2 = createTestBlueInvoiceRequest();
        invalidRequest2.setTaxpayerCode(null); // 缺少必填字段
        
        try {
            sysMideaInvoiceService.transBlueInvoice(invalidRequest2);
            fail("应该抛出异常");
        } catch (Exception e) {
            System.out.println("缺少必填字段测试通过: " + e.getMessage());
        }
        
        // 测试3：金额为负数
        TransBlueInvoiceRequest invalidRequest3 = createTestBlueInvoiceRequest();
        invalidRequest3.setInvoiceAmt(new BigDecimal("-100.00")); // 负金额
        
        try {
            sysMideaInvoiceService.transBlueInvoice(invalidRequest3);
            fail("应该抛出异常");
        } catch (Exception e) {
            System.out.println("负金额测试通过: " + e.getMessage());
        }
        
        System.out.println("✅ 错误处理测试完成");
    }

    @Test
    public void testDifferentInvoiceScenarios() {
        // 不同开票场景测试
        System.out.println("=== 不同开票场景测试 ===");
        
        // 场景1：电子普票
        testInvoiceScenario("电子普票", 1, 1);
        
        // 场景2：电子专票
        testInvoiceScenario("电子专票", 2, 2);
        
        // 场景3：纸质普票
        testInvoiceScenario("纸质普票", 3, 1);
        
        // 场景4：纸质专票
        testInvoiceScenario("纸质专票", 4, 2);
        
        System.out.println("✅ 不同开票场景测试完成");
    }

    private void testInvoiceScenario(String scenarioName, int invoiceType, int invoiceHead) {
        System.out.println("测试场景: " + scenarioName);
        
        try {
            TransBlueInvoiceRequest request = createTestBlueInvoiceRequest();
            request.setInvoiceType(invoiceType);
            request.setInvoiceHead(invoiceHead);
            request.setBizId("SCENARIO_" + scenarioName + "_" + System.currentTimeMillis());
            
            // 如果是专票，需要完整的购买方信息
            if (invoiceHead == 2) {
                request.setPayTaxpayerCode("91440606MA4UH7E96K");
                request.setPayUnitAddress("广东省佛山市顺德区测试路123号");
                request.setPayFixedPhoneNumber("0757-********");
                request.setPayBankName("中国工商银行佛山分行");
                request.setPayBankAccount("********90********9");
            }
            
            Boolean result = sysMideaInvoiceService.transBlueInvoice(request);
            System.out.println(scenarioName + " 开具结果: " + result);
            
        } catch (Exception e) {
            System.out.println(scenarioName + " 开具异常: " + e.getMessage());
        }
    }

    @Test
    public void testApiConnectivity() {
        // API连通性测试
        System.out.println("=== API连通性测试 ===");
        
        try {
            // 创建最简单的请求来测试连通性
            TransBlueInvoiceRequest simpleRequest = new TransBlueInvoiceRequest();
            simpleRequest.setBizId("CONNECTIVITY_TEST_" + System.currentTimeMillis());
            simpleRequest.setImmediateInvoice(1);
            simpleRequest.setInvoiceType(1);
            simpleRequest.setTaxpayerCode("91440606MA4UH7E96K");
            simpleRequest.setInvoiceAmt(new BigDecimal("100.00"));
            simpleRequest.setInvoiceHead(2);
            simpleRequest.setPayTaxpayerName("连通性测试公司");
            
            // 创建简单明细
            List<TransBlueInvoiceRequest.InvoiceBlueDetailDTO> details = new ArrayList<>();
            TransBlueInvoiceRequest.InvoiceBlueDetailDTO detail = new TransBlueInvoiceRequest.InvoiceBlueDetailDTO();
            detail.setBizDetailId("CONN_DETAIL_" + System.currentTimeMillis());
            detail.setBusinessNo("CONN_BUS_001");
            detail.setGoodsName("连通性测试商品");
            detail.setAmtContainTax(new BigDecimal("100.00"));
            details.add(detail);
            simpleRequest.setDetailList(details);
            
            long startTime = System.currentTimeMillis();
            Boolean result = sysMideaInvoiceService.transBlueInvoice(simpleRequest);
            long endTime = System.currentTimeMillis();
            
            System.out.println("API连通性测试结果: " + result);
            System.out.println("响应时间: " + (endTime - startTime) + "ms");
            
            if (endTime - startTime > 10000) {
                System.out.println("⚠️ 警告：API响应时间过长，可能存在网络问题");
            } else {
                System.out.println("✅ API响应时间正常");
            }
            
        } catch (Exception e) {
            System.out.println("❌ API连通性测试失败: " + e.getMessage());
            
            // 分析异常类型
            if (e.getMessage().contains("timeout") || e.getMessage().contains("超时")) {
                System.out.println("原因：网络超时");
            } else if (e.getMessage().contains("connection") || e.getMessage().contains("连接")) {
                System.out.println("原因：网络连接问题");
            } else if (e.getMessage().contains("authentication") || e.getMessage().contains("认证")) {
                System.out.println("原因：认证失败");
            } else {
                System.out.println("原因：其他错误");
            }
        }
    }

    @Test
    public void testBlueInvoiceRequestValidation() {
        // 测试蓝票开具请求参数验证
        System.out.println("=== 测试蓝票开具参数验证 ===");

        // 测试空请求
        assertThrows(Exception.class, () -> {
            sysMideaInvoiceService.transBlueInvoice(null);
        }, "空请求应该抛出异常");

        // 测试缺少必填字段的请求
        TransBlueInvoiceRequest invalidRequest = new TransBlueInvoiceRequest();
        assertThrows(Exception.class, () -> {
            sysMideaInvoiceService.transBlueInvoice(invalidRequest);
        }, "缺少必填字段应该抛出异常");

        System.out.println("✅ 蓝票开具参数验证测试通过");
    }

    @Test
    public void testRedInvoiceRequestValidation() {
        // 测试红票开具请求参数验证
        System.out.println("=== 测试红票开具参数验证 ===");

        // 测试空请求
        assertThrows(Exception.class, () -> {
            sysMideaInvoiceService.transRedInvoice(null);
        }, "空请求应该抛出异常");

        // 测试缺少必填字段的请求
        TransRedInvoiceRequest invalidRequest = new TransRedInvoiceRequest();
        assertThrows(Exception.class, () -> {
            sysMideaInvoiceService.transRedInvoice(invalidRequest);
        }, "缺少必填字段应该抛出异常");

        System.out.println("✅ 红票开具参数验证测试通过");
    }

    @Test
    public void testDifferentInvoiceTypes() {
        // 测试不同发票类型
        System.out.println("=== 测试不同发票类型 ===");

        // 测试电子普票
        TransBlueInvoiceRequest electronicRequest = createTestBlueInvoiceRequest();
        electronicRequest.setInvoiceType(1); // 电子普票
        System.out.println("测试电子普票开具...");

        try {
            Boolean result = sysMideaInvoiceService.transBlueInvoice(electronicRequest);
            System.out.println("电子普票开具结果: " + result);
        } catch (Exception e) {
            System.out.println("电子普票开具异常: " + e.getMessage());
        }

        // 测试电子专票
        TransBlueInvoiceRequest specialRequest = createTestBlueInvoiceRequest();
        specialRequest.setInvoiceType(2); // 电子专票
        specialRequest.setInvoiceHead(2); // 企业
        specialRequest.setPayTaxpayerCode("91440606MA4UH7E96K"); // 购方税号
        specialRequest.setPayUnitAddress("广东省佛山市顺德区测试路123号");
        specialRequest.setPayFixedPhoneNumber("0757-********");
        specialRequest.setPayBankName("中国工商银行佛山分行");
        specialRequest.setPayBankAccount("********90********9");
        System.out.println("测试电子专票开具...");

        try {
            Boolean result = sysMideaInvoiceService.transBlueInvoice(specialRequest);
            System.out.println("电子专票开具结果: " + result);
        } catch (Exception e) {
            System.out.println("电子专票开具异常: " + e.getMessage());
        }

        System.out.println("✅ 不同发票类型测试完成");
    }

    @Test
    public void testConcurrentInvoiceRequests() {
        // 测试并发开票请求
        System.out.println("=== 测试并发开票请求 ===");

        List<Thread> threads = new ArrayList<>();
        List<Boolean> results = new ArrayList<>();

        // 创建5个并发请求
        for (int i = 0; i < 5; i++) {
            final int index = i;
            Thread thread = new Thread(() -> {
                try {
                    TransBlueInvoiceRequest request = createTestBlueInvoiceRequest();
                    request.setBizId("CONCURRENT_TEST_" + index + "_" + System.currentTimeMillis());

                    Boolean result = sysMideaInvoiceService.transBlueInvoice(request);
                    synchronized (results) {
                        results.add(result);
                    }
                    System.out.println("并发请求 " + index + " 完成，结果: " + result);
                } catch (Exception e) {
                    System.out.println("并发请求 " + index + " 异常: " + e.getMessage());
                    synchronized (results) {
                        results.add(false);
                    }
                }
            });
            threads.add(thread);
        }

        // 启动所有线程
        threads.forEach(Thread::start);

        // 等待所有线程完成
        threads.forEach(thread -> {
            try {
                thread.join(10000); // 最多等待10秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });

        System.out.println("并发测试完成，总请求数: " + threads.size() + "，完成数: " + results.size());
        System.out.println("✅ 并发开票请求测试完成");
    }

    /**
     * 创建测试用的蓝票开具请求
     */
    private TransBlueInvoiceRequest createTestBlueInvoiceRequest() {
        TransBlueInvoiceRequest request = new TransBlueInvoiceRequest();
        
        request.setBizId("INTEGRATION_BLUE_" + UUID.randomUUID().toString().replace("-", ""));
        request.setImmediateInvoice(1);
        request.setEditable(0);
        request.setInvoiceType(1);
        request.setTaxpayerCode("91440606MA4UH7E96K");
        request.setInvoiceAmt(new BigDecimal("2260.00"));
        request.setInvoiceHead(2);
        request.setPayTaxpayerName("测试购买方公司");
        request.setRemarks("单元测试开具蓝票");
        request.setDetailList(createTestBlueInvoiceDetails());
        
        return request;
    }

    /**
     * 创建测试用的红票开具请求
     */
    private TransRedInvoiceRequest createTestRedInvoiceRequest() {
        TransRedInvoiceRequest request = new TransRedInvoiceRequest();
        
        request.setRedBizId("TEST_RED_" + UUID.randomUUID().toString().replace("-", ""));
        request.setBizId(blueInvoiceRequest.getBizId()); // 关联蓝票
        request.setInvoiceType(1);
        request.setRedAmount(new BigDecimal("2260.00"));
        request.setRedCount(1);
        request.setTaxpayerCode("91440606MA4UH7E96K");
        request.setInvoiceHead(2);
        request.setBusinessNo("BUS_NO_" + System.currentTimeMillis());
        request.setEditable(0);
        request.setRemarks("单元测试开具红票");
        request.setRedReason("01");
        request.setBlueInvoiceNo("********");
        request.setBlueInvoiceCode("*********");
        request.setBlueInvoiceDate("2021-09-14");
        request.setDetailList(createTestRedInvoiceDetails());
        
        return request;
    }

    /**
     * 创建测试蓝票明细
     */
    private List<TransBlueInvoiceRequest.InvoiceBlueDetailDTO> createTestBlueInvoiceDetails() {
        List<TransBlueInvoiceRequest.InvoiceBlueDetailDTO> details = new ArrayList<>();
        
        TransBlueInvoiceRequest.InvoiceBlueDetailDTO detail = new TransBlueInvoiceRequest.InvoiceBlueDetailDTO();
        detail.setBizDetailId("BLUE_DETAIL_" + UUID.randomUUID().toString().replace("-", ""));
        detail.setBusinessNo("BUS_001");
        detail.setGoodsName("测试商品A");
        detail.setGoodsUnit("台");
        detail.setTaxCode("1010101010000000000");
        detail.setTaxRate(new BigDecimal("0.13"));
        detail.setGoodCount(new BigDecimal("2"));
        detail.setTaxUnitPrice(new BigDecimal("1130.00"));
        detail.setAmtContainTax(new BigDecimal("2260.00"));
        detail.setAmtTax(new BigDecimal("260.00"));
        detail.setDiscountStatus(0);
        details.add(detail);
        
        return details;
    }

    /**
     * 创建测试红票明细
     */
    private List<TransRedInvoiceRequest.SpecialRedInfoDetailDTO> createTestRedInvoiceDetails() {
        List<TransRedInvoiceRequest.SpecialRedInfoDetailDTO> details = new ArrayList<>();
        
        TransRedInvoiceRequest.SpecialRedInfoDetailDTO detail = new TransRedInvoiceRequest.SpecialRedInfoDetailDTO();
        detail.setBizDetailId("RED_DETAIL_" + UUID.randomUUID().toString().replace("-", ""));
        detail.setBusinessNo("BUS_001");
        detail.setGoodsName("测试商品A（红冲）");
        detail.setGoodsUnit("台");
        detail.setTaxCode("1010101010000000000");
        detail.setTaxRate(new BigDecimal("0.13"));
        detail.setGoodCount(new BigDecimal("2"));
        detail.setTaxUnitPrice(new BigDecimal("1130.00"));
        detail.setAmtContainTax(new BigDecimal("2260.00"));
        detail.setAmtTax(new BigDecimal("260.00"));
        detail.setDiscountStatus(0);
        details.add(detail);
        
        return details;
    }


}
