apiVersion: apps/v1
kind: Deployment
metadata:
  name: zksr-system
  namespace: haixin
  labels:
    app: zksr-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: zksr-system
  template:
    metadata:
      labels:
        app: zksr-system
    spec:
      containers:
        - name: zksr-system
          image: haixin.tencentcloudcr.com/hisense/u-biz/prod/b2b/system:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 6100
          env:
            - name: TZ
              value: "Asia/Shanghai"
            - name: SPRING_PROFILES_ACTIVE
              value: "dev"
          resources:
            requests:
              memory: "6Gi"
              cpu: "1000m"
            limits:
              memory: "8Gi"
              cpu: "2000m"
      imagePullSecrets:
        - name: hisense