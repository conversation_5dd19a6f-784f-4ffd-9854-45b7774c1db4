<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zksr</groupId>
        <artifactId>zksr-modules</artifactId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zksr-modules-system</artifactId>

    <description>
        zksr-modules-system系统模块
    </description>

    <dependencies>

    	<!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

    	<!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

    	<!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- Zksr-Mall Common DataSource -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-datasource</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Database -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-mp</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Log -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-log</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Swagger -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-swagger</artifactId>
        </dependency>

        <!-- 文件|导出| 接口 -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-file</artifactId>
        </dependency>

        <!-- 消息中间件 -->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-third</artifactId>
        </dependency>

        <!-- 单元测试 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 阿里云短信 (同步) -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
            <version>2.0.24</version>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
        </dependency>

        <!-- 腾讯云短信 -->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java</artifactId>
            <version>3.1.1000</version>
        </dependency>

        <!-- Spring 邮件依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-portal</artifactId>
        </dependency>


<!--        <dependency>
            <groupId>com.midea.mbf</groupId>
            <artifactId>mbf-mq-producer-spring-boot-starter</artifactId>
        </dependency>-->

        <!--kafka依赖-->
        <!--<dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka</artifactId>
        </dependency>-->
        <!--RocketMq依赖-->
<!--        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
        </dependency>-->

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
