<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccRechargeImportMapper">

    <select id="selectPageExt" resultType="com.zksr.account.controller.recharge.vo.AccRechargeImportRespVO">
        SELECT
            ri.recharge_import_id,
            ri.sys_code,
            ri.create_by,
            ri.create_time,
            ri.update_by,
            ri.update_time,
            ri.deleted,
            ri.audit_by,
            ri.recharge_import_state,
            ri.remark,
            ri.voucher,
            ri.counter,
            ri.recharge_amt
        FROM
            acc_recharge_import ri
        WHERE
            ri.deleted = 0
            <if test="rechargeImportId != null">
                AND ri.recharge_import_id = #{rechargeImportId}
            </if>
            <if test="sysCode != null">
                AND ri.sys_code = #{sysCode}
            </if>
            <if test="auditBy != null">
                AND ri.audit_by = #{auditBy}
            </if>
            <if test="rechargeImportState != null">
                AND ri.recharge_import_state = #{rechargeImportState}
            </if>
            <if test="startTime != null">
                AND ri.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="auditTime != null">
                AND ri.update_time BETWEEN #{auditTime} AND #{auditEndTime}
                AND ri.recharge_import_state IN (1, 2)
            </if>
            <if test="branchId != null">
                AND ri.recharge_import_id IN (
                    SELECT recharge_import_id FROM acc_recharge_import_dtl WHERE branch_id = #{branchId}
                )
            </if>
        ORDER BY
            ri.recharge_import_id DESC
    </select>
</mapper>