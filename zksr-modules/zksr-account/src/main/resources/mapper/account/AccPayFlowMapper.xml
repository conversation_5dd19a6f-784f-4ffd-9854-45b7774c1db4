<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccPayFlowMapper">

    <select id="selectOrderPayInfoListBySyncOrder" resultType="com.zksr.common.core.domain.vo.openapi.ReceiptPayOpenDTO">
        SELECT
        case when #{reqDTO.sheetType} = 'XSS'
        then out_trade_no
        when #{reqDTO.sheetType} = 'SHS'
        then out_trade_no
        else out_trade_no
        end sheetTradeNo
        ,platform
        ,case when #{reqDTO.sheetType} = 'XSS'
        then pay_amt
        when #{reqDTO.sheetType} = 'SHS'
        then -refund_amt
        else pay_amt
        end payAmt
        FROM acc_pay_flow
        <where>
            <if test="null != reqDTO.sheetType and '' != reqDTO.sheetType">
                <!-- 销售订单： 线上支付订单 根据销售订单查询  / 货到付款订单 根据货到付款单号集合查询 -->
                <if test="reqDTO.sheetType == 'XSS' and reqDTO.payWay == '0'.toString() ">
                    and trade_no = #{reqDTO.sheetNo} and pay_type = 0
                </if>
                <if test="reqDTO.sheetType == 'XSS' and reqDTO.payWay == '1'.toString() ">
                    and trade_no in
                    <foreach collection="reqDTO.hdfkNoList" item="hdfkNo" open="(" separator="," close=")">
                        #{hdfkNo}
                    </foreach>
                    and pay_type = 0
                </if>
                <if test="reqDTO.sheetType == 'SHS'">
                    and refund_no in
                    <foreach collection="reqDTO.hdfkNoList" item="hdfkNo" open="(" separator="," close=")">
                        #{hdfkNo}
                    </foreach>
                </if>
            </if>
        </where>

    </select>
    <select id="selectWxB2bRechargeDivideFlow" resultType="com.zksr.account.domain.AccPayFlow">
        SELECT
            *
        FROM
            acc_pay_flow
        WHERE
            platform = 'wxb2b'
            AND order_type = 2
            AND callback_flag = 1
            AND is_divide = 0
            AND create_time &gt; DATE_SUB(now(),INTERVAL 3 DAY)
            AND update_time &lt; DATE_SUB(now(),INTERVAL 8 MINUTE)
            <if test="minPayFlowId != null">
                AND pay_flow_id &gt; #{minPayFlowId}
            </if>
            ORDER BY pay_flow_id ASC
            LIMIT 100
    </select>

</mapper>