<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccRechargeSchemeMapper">

    <select id="selectOverlappingEnabledEntries" resultType="com.zksr.account.domain.AccRechargeScheme">
        SELECT
            *
        FROM
            acc_recharge_scheme
        WHERE
            status = 0
            AND FIND_IN_SET(#{areaId}, area_ids)
            AND (
                (start_time &lt;= #{endTime} AND end_time &gt;= #{startTime})
                OR (start_time  &lt;= #{startTime} AND end_time &gt;= #{endTime})
                OR (start_time &gt;= #{startTime} AND end_time &lt;= #{endTime})
            )
    </select>
    <select id="selectPage" resultType="com.zksr.account.domain.AccRechargeScheme">
        SELECT
            *
        FROM
            acc_recharge_scheme ars
        WHERE 1 = 1
            <if test="rechargeSchemeId != null">
                AND ars.recharge_scheme_id = #{rechargeSchemeId}
            </if>
            <if test="sysCode != null">
                AND ars.sys_code = #{sysCode}
            </if>
            <if test="status != null">
                AND ars.status = #{status}
            </if>
            <if test="schemeName != null">
                AND ars.scheme_name LIKE CONCAT('%', #{schemeName}, '%')
            </if>
            <if test="areaId != null">
                AND ars.recharge_scheme_id IN (
                    SELECT acc_recharge_scheme_area.recharge_scheme_id FROM acc_recharge_scheme_area WHERE area_id = #{areaId}
                )
            </if>
            <!-- 运营商城市集合隔离 -->
            <if test="areaIdList != null">
                AND ars.recharge_scheme_id IN (
                    SELECT acc_recharge_scheme_area.recharge_scheme_id FROM acc_recharge_scheme_area WHERE area_id IN
                    <foreach collection="areaIdList" item="areaId" open="(" separator="," close=")">
                        #{areaId}
                    </foreach>
                )
            </if>
            ${params.dataScope}
        ORDER BY
            ars.recharge_scheme_id DESC
    </select>
    <select id="selectValidAreaRechargeSchemeList" resultType="com.zksr.account.domain.AccRechargeScheme">
        SELECT
            *
        FROM
            acc_recharge_scheme
        WHERE
            status = 0
            AND FIND_IN_SET(#{areaId}, area_ids)
            AND end_time &gt; NOW()
    </select>
</mapper>