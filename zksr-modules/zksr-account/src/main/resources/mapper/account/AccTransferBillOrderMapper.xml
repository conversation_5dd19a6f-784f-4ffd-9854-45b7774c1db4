<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccTransferBillOrderMapper">
    <delete id="deleteTransferBillsByDateAndAltNo">
        DELETE FROM acc_transfer_bill_order
        WHERE  DATE(transfer_time) = #{dataFormatted}
                AND alt_no = #{altNo}
    </delete>
    <select id="countTransferBillsByDateAndAltNo" resultType="java.lang.String">
        SELECT
            DISTINCT transfer_bill_id
        FROM acc_transfer_bill_order
        WHERE
            DATE(transfer_time) = #{dataFormatted}
          AND alt_no = #{altNo}
    </select>
</mapper>