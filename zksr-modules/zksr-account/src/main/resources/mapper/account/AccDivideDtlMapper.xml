<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccDivideDtlMapper">

    <select id="selectPageExt" resultType="com.zksr.account.domain.AccDivideDtl">
        SELECT
            adl.divide_dtl_id,
            adl.sys_code,
            adl.create_by,
            adl.create_time,
            adl.update_by,
            adl.update_time,
            adl.merchant_type,
            adl.merchant_id,
            adl.alt_mch_no,
            adl.alt_mch_name,
            adl.order_amt,
            adl.alt_amt,
            adl.fee_rate,
            adl.fee,
            adl.platform,
            adl.pay_flow_id,
            adl.online_or_offline,
            adl.online_divide_state,
            adl.offline_pro_state,
            adl.offline_pro_no,
            adl.divide_time,
            adl.trade_no,
            adl.sub_trade_no,
            adl.out_trade_no,
            adl.refund_no,
            adl.sub_refund_no,
            adl.out_refund_no,
            adl.pay_type,
            adl.platform_divide_flow_no,
            adl.order_type
        FROM
            acc_divide_dtl adl
        WHERE
            <!-- 0-线上分账  1-线下分账 -->
            adl.online_or_offline = 1
            <if test='offlineProNo != null and offlineProNo != ""'>
                AND adl.offline_pro_no = #{offlineProNo}
            </if>
            <if test='tradeNo != null and tradeNo != ""'>
                AND adl.trade_no = #{tradeNo}
            </if>
            <if test='subTradeNo != null and subTradeNo != ""'>
                AND adl.sub_trade_no = #{subTradeNo}
            </if>
            <if test='refundNo != null and refundNo != ""'>
                AND adl.refund_no = #{refundNo}
            </if>
            <if test='startTime != null'>
                AND adl.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test='divideTime != null'>
                AND adl.divide_time BETWEEN #{divideTime} AND #{divideEndTime}
            </if>
            <if test='merchantId != null'>
                AND adl.merchant_id = #{merchantId}
            </if>
            <if test='onlineDivideState != null'>
                AND adl.online_divide_state = #{onlineDivideState}
            </if>
            <if test='offlineProState != null'>
                AND adl.offline_pro_state = #{offlineProState}
            </if>
        ORDER BY
            adl.divide_dtl_id DESC
    </select>
</mapper>