<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccPlatformMerchantMapper">

    <select id="getPlatformMerchantNameAndKey"
            resultType="com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO">
        SELECT
            alt_mch_no AS altMchNo,
            MIN(alt_mch_key) AS altMchKey
        FROM
            acc_platform_merchant
        WHERE
            sys_code = #{sysCode}
            AND merchant_type = #{merchantType}
            AND platform = #{payPlatform}
            AND mch_status = 1
            AND (alt_mch_no is not null and alt_mch_no != '' and alt_mch_key is not null and alt_mch_key != '')
        GROUP BY
            alt_mch_no;
    </select>
</mapper>