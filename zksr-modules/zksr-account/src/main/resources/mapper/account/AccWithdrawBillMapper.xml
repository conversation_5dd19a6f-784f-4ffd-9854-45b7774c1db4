<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccWithdrawBillMapper">
    <delete id="deleteWithdrawBillsByDateAndAltNo">
        DELETE FROM acc_withdraw_bill
         WHERE alt_no = #{altNo}
          AND DATE(finish_time) = #{dataFormatted}
    </delete>

    <select id="countWithdrawBillsByDateAndAltNo" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM acc_withdraw_bill
        WHERE alt_no = #{altNo}
        AND DATE(finish_time) = #{dataFormatted}
    </select>
</mapper>