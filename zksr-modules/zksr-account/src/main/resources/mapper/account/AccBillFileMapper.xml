<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccBillFileMapper">
    <delete id="deleteBillFileRecord">
        DELETE FROM acc_bill_file WHERE bill_file_id = #{billFileId}
    </delete>

    <select id="checkBillFileRecordExists" resultType="java.lang.String">
        SELECT
            bill_file_id
        FROM acc_bill_file
        WHERE
            alt_no = #{altNo}
          AND DATE(date) = #{data}
        LIMIT 1
    </select>
</mapper>