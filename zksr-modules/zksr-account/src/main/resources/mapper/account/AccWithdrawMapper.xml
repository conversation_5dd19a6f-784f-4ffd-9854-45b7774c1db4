<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccWithdrawMapper">

    <select id="selectTotalWithdrawAmt" resultType="java.math.BigDecimal">
        -- settle_state = 14 结算成功
        SELECT SUM(apply_amt) FROM acc_withdraw WHERE account_id = #{accountId} AND settle_state = '14'
    </select>
    <select id="selectPageExt" resultType="com.zksr.account.controller.withdraw.vo.AccWithdrawRespVO">
        SELECT
            aw.withdraw_id,
            aw.sys_code,
            aw.create_by,
            aw.create_time,
            aw.update_by,
            aw.update_time,
            aw.merchant_type,
            aw.merchant_id,
            aw.account_id,
            aw.platform,
            aw.transfer_type,
            aw.alt_mch_no,
            aw.alt_mch_name,
            aw.bsi_mch_no,
            aw.bank_account_no,
            aw.bank_name,
            aw.apply_amt,
            aw.reject_reason,
            aw.reject_time,
            aw.approve_time,
            aw.state,
            aw.transfer_state,
            aw.transfer_no,
            aw.transfer_init_time,
            aw.transfer_finish_time,
            aw.transfer_msg,
            aw.settle_state,
            aw.settle_no,
            aw.settle_msg,
            aw.settle_init_time,
            aw.settle_finish_time,
            aw.fee,
            aw.transfer_state1,
            aw.transfer_no1,
            aw.transfer_init_time1,
            aw.transfer_finish_time1,
            aw.transfer_msg1,
            aw.transfer_state2,
            aw.transfer_no2,
            aw.transfer_init_time2,
            aw.transfer_finish_time2,
            aw.transfer_msg2,
            aw.bank_account_no1,
            aw.bank_account_name1,
            aw.oper_fee,
            aw.withdraw_amt,
            aw.partner_profit,
            aw.transfer_amt,
            aw.voucher,
            act.account_type,
            act.platform accountPlatform
        FROM
            acc_withdraw aw
            INNER JOIN acc_account act ON act.account_id = aw.account_id
        WHERE 1 = 1
            <if test="accountPlatform != null">
                AND act.platform = #{accountPlatform}
            </if>
            <if test="dcId != null">
                AND act.hold_merchant_id = #{dcId}
            </if>
            <if test="withdrawId != null">
                AND aw.withdraw_id = #{withdrawId}
            </if>
            <if test="sysCode != null">
                AND aw.sys_code = #{sysCode}
            </if>
            <if test="merchantType != null">
                AND aw.merchant_type = #{merchantType}
            </if>
            <if test="merchantId != null">
                AND aw.merchant_id = #{merchantId}
            </if>
            <if test="createTime != null">
                AND aw.create_time &gt; #{createTime}
            </if>
            <if test="createTimeEnd != null">
                AND aw.create_time &lt; #{createTimeEnd}
            </if>
            <if test="state != null and state.size() > 0">
                AND aw.state IN
                <foreach item="item" index="index" collection="state" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="transferState != null">
                AND aw.transfer_state = #{transferState}
            </if>
            <if test="settleState != null">
                AND aw.settle_state = #{settleState}
            </if>
            <if test="approveTime != null">
                AND aw.approve_time &gt; #{approveTime}
            </if>
            <if test="approveTimeEnd != null">
                AND aw.approve_time &lt; #{approveTimeEnd}
            </if>
            <if test="merchantTypeList != null and merchantTypeList.size() > 0">
                AND aw.merchant_type IN
                <foreach item="item" index="index" collection="merchantTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        ORDER BY aw.withdraw_id DESC
    </select>
</mapper>