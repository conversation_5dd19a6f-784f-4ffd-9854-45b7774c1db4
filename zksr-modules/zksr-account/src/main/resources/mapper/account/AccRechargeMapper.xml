<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccRechargeMapper">

    <select id="selectExtPage" resultType="com.zksr.account.controller.recharge.vo.AccRechargeRespVO">
        SELECT
            re.recharge_id,
            re.sys_code,
            re.create_by,
            re.create_time,
            re.update_by,
            re.update_time,
            re.charge_type,
            re.pay_way,
            re.recharge_amt,
            re.recharge_merchant_type,
            re.recharge_merchant_id,
            re.receive_merchant_type,
            re.receive_merhcant_id,
            re.state,
            re.platform,
            re.recharge_no,
            re.fee,
            re.recharge_scheme_id,
            re.scheme_rule_json,
            re.give_amt,
            af.settles
        FROM
            acc_recharge re
            LEFT JOIN acc_pay_flow af ON af.trade_no = re.recharge_no
        WHERE
            re.state = 1
            <if test="rechargeId != null">AND re.recharge_id = #{rechargeId}</if>
            <if test="sysCode != null">AND re.sys_code = #{sysCode}</if>
            <if test="chargeType != null">AND re.charge_type = #{chargeType}</if>
            <if test="rechargeMerchantType != null">AND re.recharge_merchant_type = #{rechargeMerchantType}</if>
            <if test="rechargeMerchantId != null">AND re.recharge_merchant_id = #{rechargeMerchantId}</if>
            <if test="receiveMerhcantId != null">AND re.receive_merhcant_id = #{receiveMerhcantId}</if>
            <if test="state != null">AND re.state = #{state}</if>
            <if test="rechargeNo != null">AND re.recharge_no = #{rechargeNo}</if>
            <if test="startTime != null">AND re.create_time &gt; #{startTime}</if>
            <if test="endTime != null">AND re.create_time &lt; #{endTime}</if>
        ORDER BY
            re.recharge_id DESC
    </select>
</mapper>