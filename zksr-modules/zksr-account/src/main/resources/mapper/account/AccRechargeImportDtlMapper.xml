<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccRechargeImportDtlMapper">

    <select id="selectByRechargeImportIdRespVO"
            resultType="com.zksr.account.controller.recharge.vo.AccRechargeImportDtlRespVO">
        SELECT
            rid.branch_id,
            rid.recharge_amt,
            -- 本金 + 赠送
            IFNULL(at.withdrawable_amt, 0) + IFNULL(at2.withdrawable_amt, 0)  balance
        FROM
            acc_recharge_import_dtl rid
            LEFT JOIN acc_account at ON at.merchant_id = rid.branch_id AND at.platform = 'wallet' AND at.account_type = 0
            LEFT JOIN acc_account at2 ON at2.merchant_id = rid.branch_id AND at2.platform = 'wallet' AND at2.account_type = 1
        WHERE
            rid.recharge_import_id = #{rechargeImportId}
    </select>
</mapper>