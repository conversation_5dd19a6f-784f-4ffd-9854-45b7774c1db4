<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zksr.account.mapper.AccAccountFlowMapper">


    <select id="selectBranchAccountFlowPage" resultType="com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO">
        SELECT
            af.account_flow_id,
            af.sys_code,
            af.create_by,
            af.create_time,
            af.update_by,
            af.update_time,
            af.account_id,
            af.merchant_type,
            af.merchant_id branchId,
            af.busi_withdrawable_amt + IFNULL(af2.busi_withdrawable_amt, 0) AS busi_withdrawable_amt,
            af.now_withdrawable_amt + IFNULL(af2.now_withdrawable_amt, 0) AS now_withdrawable_amt,
            IFNULL(af2.busi_withdrawable_amt, 0) AS giveAmt,
            af.now_withdrawable_amt baseAccountBalance,
            IFNULL(af2.now_withdrawable_amt, 0) giveAccountBalance,
            af.busi_type,
            af.busi_id,
            af.busi_no,
            af.memo,
            af.busi_fields,
            af.process_flag,
            af.process_time,
            af.platform,
            af.account_type
        FROM
            acc_account_flow af
            LEFT JOIN acc_account_flow af2 ON af2.busi_id = af.busi_id AND af2.account_type = 1
            <if test="busiFields != null and busiFields != ''">
                AND FIND_IN_SET(#{busiFields}, af2.busi_fields)
            </if>
        WHERE
            af.account_type = 0
            <if test="accountFlowId != null">
                AND af.account_flow_id = #{accountFlowId}
            </if>
            <if test="sysCode != null">
                AND af.sys_code = #{sysCode}
            </if>
            <if test="accountId != null">
                AND af.account_id = #{accountId}
            </if>
            <if test="merchantId != null">
                AND af.merchant_id = #{merchantId}
            </if>
            <if test="merchantType != null">
                AND af.merchant_type = #{merchantType}
            </if>
            <if test="busiType != null">
                AND af.busi_type = #{busiType}
            </if>
            <if test="busiId != null">
                AND af.busi_id = #{busiId}
            </if>
            <if test="busiFields != null and busiFields != ''">
                AND FIND_IN_SET(#{busiFields}, af.busi_fields)
            </if>
            <if test="processFlag != null">
                AND af.process_flag = #{processFlag}
            </if>
            <if test="platform != null">
                AND af.platform = #{platform}
            </if>
            <if test="processStartTime != null and processEndTime != null">
                AND af.process_time BETWEEN #{processStartTime} AND #{processEndTime}
            </if>
            <if test="ioType != null">
                <choose>
                    <when test="ioType == 0">
                        AND af.busi_withdrawable_amt &gt; 0
                    </when>
                    <otherwise>
                        AND af.busi_withdrawable_amt &lt; 0
                    </otherwise>
                </choose>
            </if>
        ORDER BY
            af.process_time DESC
    </select>

    <select id="getBranchValueInfoByIds" resultType="com.zksr.common.core.domain.vo.openapi.BranchValueInfoOpenDTO">
        select merchant_id branchId,busi_type,ABS(sum(amt)) totaltAmt,ABS(sum(giveAmt)) giveAmt
        from
        (
        select
        merchant_id,
        case when account_type = 0 then busi_withdrawable_amt
        else 0 end AS amt,
        case when account_type = 1  then busi_withdrawable_amt
        else 0 end AS giveAmt,
        case when busi_type != 'withdraw_success'   then 'branch_recharge'
        else busi_type end as busi_type
        from acc_account_flow
        where
        account_flow_id in
        <foreach collection="accountFlowIds" item="accountFlowId" separator="," open="(" close=")">
            #{accountFlowId}
        </foreach>
        and merchant_type = 'branch'
        ) a
        group by merchant_id,busi_type
    </select>




</mapper>