package com.zksr.account.client.hlb.vo.body;

import com.zksr.account.enums.MerchantCredentialType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝商户变更
 * @date 2024/7/12 14:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "合利宝商户变更")
public class HlbMerchantUpdateBodyVO {

    @ApiModelProperty(value = "商户号", notes = "进件注册审核以后才有")
    private String merchantNo;

    @ApiModelProperty(value = "进件商户订单号", notes = "商户内部编号")
    private String orderNo;

    /**
     * 展示名
     */
    @ApiModelProperty("展示名")
    private String showName;
    /**
     * 法人名字
     */
    @ApiModelProperty("法人名字")
    private String legalPerson;

    /**
     * 法人身份证号
     */
    @ApiModelProperty("法人身份证号")
    private String legalPersonID;

    /**
     * 营业执照号
     */
    @ApiModelProperty("营业执照号")
    private String businessLicense;


    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String linkman;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String linkPhone;

    /**
     * 组织机构代码
     */
    @ApiModelProperty("组织机构代码")
    private String orgNum;

    /**
     * 文件签名列表
     */
    private Map<String,String> fileSigns;

    @ApiModelProperty("结算信息")
    private UpdateSettleInfo updateSettleInfo;

    @ApiModel(description = "结算信息")
    @Data
    public static class UpdateSettleInfo {
        /**
         * 开户名
         */
        private String accountName;
        /**
         * 变更后开户名
         */
        private String updateAccountName;
        /**
         * 开户账号
         */
        private String accountNo;
        /**
         * 变更后结算卡号
         */
        private String updateAccountNo;
        /**
         * 原结算卡类型
         */
        private String settleBankType;
        /**
         * 变更结算卡类型
         */
        private String updateSettleBankType;
        /**
         * 联行号
         */
        private String bankCode;
        /**
         * 变更后联行号
         */
        private String updateBankCode;
        /**
         * 结算人身份证开始日期
         */
        private String settlementIdCardStartDate;
        /**
         * 结算人身份证结束日期
         */
        private String settlementIdCardEndDate;
        /**
         * 结算人身份证号
         */
        private String settlementIdCardNo;
        /**
         * 电子账户号
         */
        private String electronicAccountNo;
        /**
         * 电子账户户名
         */
        private String electronicAccountName;
        /**
         * 结算人手机号
         */
        private String settlementPhoneNo;
        /**
         * D0 自动结算日切时间
         */
        private String settlementCutTime;

        /**
         * 日切时间生效日期
         */
        private String settleCutTimeEffectiveDate;

        /**
         * 变更类型, 结算卡
         */
        private String merchantEntryAlterationType = "SETTLE_BANKCARD";
    }
}
