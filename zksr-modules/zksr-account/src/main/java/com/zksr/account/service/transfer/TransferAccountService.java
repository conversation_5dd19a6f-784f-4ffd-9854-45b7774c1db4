package com.zksr.account.service.transfer;

import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;

/**
 * <AUTHOR>
 * @time 2024/3/11
 * @desc
 */
public interface TransferAccountService {
    /**
     * 提交付款单
     * @param reqVO
     * @return
     */
    TransferSubmitRespDTO transferSubmit(TransferSubmitReqVO reqVO);

    /**
     * 交易结算 (提现)
     * @param reqVO
     * @return
     */
    TransferSettleRespDTO transferSettle(TransferSettleReqVO reqVO);

    /**
     * 转账回调
     * @param notify
     */
    void notify(TransferSubmitRespDTO notify);

    /**
     * 交易结算 (提现) 回调
     * @param notify
     */
    void notify(TransferSettleRespDTO notify);
}
