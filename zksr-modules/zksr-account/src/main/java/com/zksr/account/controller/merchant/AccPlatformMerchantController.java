package com.zksr.account.controller.merchant;

import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.partner.PartnerApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 支付平台商户Controller
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Api(tags = "管理后台 - 支付平台商户接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/merchant")
@SuppressWarnings("all")
public class AccPlatformMerchantController {

    @Autowired
    private IAccPlatformMerchantService accPlatformMerchantService;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private DcApi dcApi;

    /**
     * 注册支付平台商户
     */
    @ApiOperation(value = "注册支付平台商户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.REGISTER)
    @RequiresPermissions(Permissions.REGISTER)
    @Log(title = "支付平台商户", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public CommonResult<Long> register(@Valid @RequestBody AccPlatformMerchantRegisterReqVO reqVO) {
        return success(accPlatformMerchantService.register(reqVO));
    }

    /**
     * 上传商户资质
     */
    @ApiOperation(value = "上传商户资质", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.UPLOAD_PIC + ", 目前仅需要上传 FRONT_OF_ID_CARD(身份证正面), BACK_OF_ID_CARD(身份证反面), UNIFIED_CODE_CERTIFICATE(三证合一营业执照), PERMIT_FOR_BANK_ACCOUNT(开户许可证)")
    @RequiresPermissions(Permissions.UPLOAD_PIC)
    @Log(title = "上传商户资质", businessType = BusinessType.UPDATE)
    @PostMapping("/uploadPic")
    public CommonResult<Long> uploadPic(@Valid @RequestBody AccPlatformMerchantUploadPicReqVO reqVO) {
        accPlatformMerchantService.uploadPic(reqVO);
        return success(NumberPool.LONG_ONE);
    }

    /**
     * 更新支付平台商户
     */
    @ApiOperation(value = "更新支付平台商户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "更新支付平台商户", businessType = BusinessType.UPDATE)
    @PostMapping("/updateRegister")
    public CommonResult<Long> updateRegister(@Valid @RequestBody AccPlatformMerchantRegisterReqVO reqVO) {
        return success(accPlatformMerchantService.updateRegister(reqVO));
    }

    /**
     * 同步商户信息, 用于从支持平台查询信息保存到数据库
     */
    @ApiOperation(value = "同步商户信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SYNC)
    @RequiresPermissions(Permissions.SYNC)
    @Log(title = "同步商户信息", businessType = BusinessType.UPDATE)
    @PostMapping("/syncData")
    public CommonResult<Boolean> syncData(@RequestBody AccPlatformMerchantSyncReqVO reqVO) {
        accPlatformMerchantService.syncData(reqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 获取支付平台商户详细信息
     */
    @ApiOperation(value = "获得支付平台商户详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{platformMerchantId}")
    public CommonResult<AccPlatformMerchantRespVO> getInfo(@PathVariable("platformMerchantId") Long platformMerchantId) {
        AccPlatformMerchant accPlatformMerchant = accPlatformMerchantService.getAccPlatformMerchant(platformMerchantId);
        return success(HutoolBeanUtils.toBean(accPlatformMerchant, AccPlatformMerchantRespVO.class));
    }

    /**
     * 分页查询支付平台商户
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得支付平台商户分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccPlatformMerchantRespVO>> getPage(@Valid AccPlatformMerchantPageReqVO pageReqVO) {
        PageResult<AccPlatformMerchant> pageResult = accPlatformMerchantService.getAccPlatformMerchantPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, AccPlatformMerchantRespVO.class));
    }

    /**
     * 解除商户绑定
     */
    @ApiOperation(value = "解除商户绑定", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "支付平台商商户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{platformMerchantIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] platformMerchantIds) {
        accPlatformMerchantService.deleteAccPlatformMerchantByPlatformMerchantIds(platformMerchantIds);
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:platform-merchant:add";
        /** 编辑 */
        public static final String EDIT = "account:platform-merchant:edit";
        /** 删除 */
        public static final String DELETE = "account:platform-merchant:remove";
        /** 列表 */
        public static final String LIST = "account:platform-merchant:list";
        /** 查询 */
        public static final String GET = "account:platform-merchant:query";
        /** 停用 */
        public static final String DISABLE = "account:platform-merchant:disable";
        /** 启用 */
        public static final String ENABLE = "account:platform-merchant:enable";
        /** 注册 */
        public static final String REGISTER = "account:platform-merchant:register";
        /** 上传资质 */
        public static final String UPLOAD_PIC = "account:platform-merchant:uploadPic";
        /** 同步信息 */
        public static final String SYNC = "account:platform-merchant:sync";
        /** 列表 */
        public static final String LIST_PARTNER = "account:platform-merchant:listPartner";
        /** 列表 */
        public static final String LIST_DC = "account:platform-merchant:listDc";
    }
}
