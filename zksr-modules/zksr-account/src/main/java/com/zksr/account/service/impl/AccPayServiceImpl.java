package com.zksr.account.service.impl;

import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.account.mapper.AccPayMapper;
import com.zksr.account.domain.AccPay;
import com.zksr.account.controller.pay.vo.AccPayPageReqVO;
import com.zksr.account.controller.pay.vo.AccPaySaveReqVO;
import com.zksr.account.service.IAccPayService;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 账户付款单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Service
public class AccPayServiceImpl implements IAccPayService {
    @Autowired
    private AccPayMapper accPayMapper;

    /**
     * 新增账户付款单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertAccPay(AccPaySaveReqVO createReqVO) {
        // 插入
        AccPay accPay = HutoolBeanUtils.toBean(createReqVO, AccPay.class);
        accPayMapper.insert(accPay);
        // 返回
        return accPay.getPayId();
    }

    /**
     * 修改账户付款单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAccPay(AccPaySaveReqVO updateReqVO) {
        accPayMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, AccPay.class));
    }

    /**
     * 删除账户付款单
     *
     * @param payId 账户付款单id
     */
    @Override
    public void deleteAccPay(Long payId) {
        // 删除
        accPayMapper.deleteById(payId);
    }

    /**
     * 批量删除账户付款单
     *
     * @param payIds 需要删除的账户付款单主键
     * @return 结果
     */
    @Override
    public void deleteAccPayByPayIds(Long[] payIds) {
        for(Long payId : payIds){
            this.deleteAccPay(payId);
        }
    }

    /**
     * 获得账户付款单
     *
     * @param payId 账户付款单id
     * @return 账户付款单
     */
    @Override
    public AccPay getAccPay(Long payId) {
        return accPayMapper.selectById(payId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccPay> getAccPayPage(AccPayPageReqVO pageReqVO) {
        return accPayMapper.selectPage(pageReqVO);
    }

    /**
     * 验证订单是否支付
     * @param orderNo
     * @return
     */
    @Override
    public boolean validRepeatPay(String orderNo) {
        return accPayMapper.selectCountByOrderNoSuccess(orderNo) > 0;
    }

    /**
     * 插入订单支付流水
     * @param accPay
     */
    @Override
    public void insertAccPay(AccPay accPay) {
        accPayMapper.insert(accPay);
    }

    @Override
    public AccPay getByOrderNo(String orderNo) {
        return accPayMapper.selectByOrderNo(orderNo);
    }

    /*private void validateAccPayExists(Long payId) {
        if (accPayMapper.selectById(payId) == null) {
            throw exception(ACC_PAY_NOT_EXISTS);
        }
    }*/


}
