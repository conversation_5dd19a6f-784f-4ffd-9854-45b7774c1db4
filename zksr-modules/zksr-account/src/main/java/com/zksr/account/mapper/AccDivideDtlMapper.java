package com.zksr.account.mapper;

import com.zksr.common.core.enums.DivideStateEnum;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.controller.divide.vo.AccDivideDtlPageReqVO;

import java.util.List;


/**
 * 支付分账详情Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Mapper
public interface AccDivideDtlMapper extends BaseMapperX<AccDivideDtl> {
    default PageResult<AccDivideDtl> selectPage(AccDivideDtlPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccDivideDtl>()
                    .eqIfPresent(AccDivideDtl::getDivideDtlId, reqVO.getDivideDtlId())
                    .eqIfPresent(AccDivideDtl::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccDivideDtl::getMerchantType, reqVO.getMerchantType())
                    .eqIfPresent(AccDivideDtl::getMerchantId, reqVO.getMerchantId())
                    .eqIfPresent(AccDivideDtl::getAltMchNo, reqVO.getAltMchNo())
                    .likeIfPresent(AccDivideDtl::getAltMchName, reqVO.getAltMchName())
                    .eqIfPresent(AccDivideDtl::getPlatform, reqVO.getPlatform())
                    .eqIfPresent(AccDivideDtl::getPayFlowId, reqVO.getPayFlowId())
                    .eqIfPresent(AccDivideDtl::getOnlineOrOffline, reqVO.getOnlineOrOffline())
                    .eqIfPresent(AccDivideDtl::getOnlineDivideState, reqVO.getOnlineDivideState())
                    .eqIfPresent(AccDivideDtl::getOfflineProState, reqVO.getOfflineProState())
                    .eqIfPresent(AccDivideDtl::getOfflineProNo, reqVO.getOfflineProNo())
                    .eqIfPresent(AccDivideDtl::getDivideTime, reqVO.getDivideTime())
                    .eqIfPresent(AccDivideDtl::getTradeNo, reqVO.getTradeNo())
                    .eqIfPresent(AccDivideDtl::getOutTradeNo, reqVO.getOutTradeNo())
                    .eqIfPresent(AccDivideDtl::getRefundNo, reqVO.getRefundNo())
                    .eqIfPresent(AccDivideDtl::getOutRefundNo, reqVO.getOutRefundNo())
                    .eqIfPresent(AccDivideDtl::getPayType, reqVO.getPayType())
                    .eqIfPresent(AccDivideDtl::getPlatformDivideFlowNo, reqVO.getPlatformDivideFlowNo())
                    .eqIfPresent(AccDivideDtl::getOrderType, reqVO.getOrderType())
                .orderByDesc(AccDivideDtl::getDivideDtlId));
    }

    List<AccDivideDtl> selectPageExt(AccDivideDtlPageReqVO pageReqVO);

    default List<AccDivideDtl> selectDivideList(AccDivideDtl reqVO) {
        return selectList(new LambdaQueryWrapperX<AccDivideDtl>()
                .eqIfPresent(AccDivideDtl::getDivideDtlId, reqVO.getDivideDtlId())
                .eqIfPresent(AccDivideDtl::getSysCode, reqVO.getSysCode())
                .eqIfPresent(AccDivideDtl::getMerchantType, reqVO.getMerchantType())
                .eqIfPresent(AccDivideDtl::getMerchantId, reqVO.getMerchantId())
                .eqIfPresent(AccDivideDtl::getAltMchNo, reqVO.getAltMchNo())
                .likeIfPresent(AccDivideDtl::getAltMchName, reqVO.getAltMchName())
                .eqIfPresent(AccDivideDtl::getPlatform, reqVO.getPlatform())
                .eqIfPresent(AccDivideDtl::getPayFlowId, reqVO.getPayFlowId())
                .eqIfPresent(AccDivideDtl::getOnlineOrOffline, reqVO.getOnlineOrOffline())
                .eqIfPresent(AccDivideDtl::getOnlineDivideState, reqVO.getOnlineDivideState())
                .eqIfPresent(AccDivideDtl::getOfflineProState, reqVO.getOfflineProState())
                .eqIfPresent(AccDivideDtl::getOfflineProNo, reqVO.getOfflineProNo())
                .eqIfPresent(AccDivideDtl::getDivideTime, reqVO.getDivideTime())
                .eqIfPresent(AccDivideDtl::getTradeNo, reqVO.getTradeNo())
                .eqIfPresent(AccDivideDtl::getOutTradeNo, reqVO.getOutTradeNo())
                .eqIfPresent(AccDivideDtl::getRefundNo, reqVO.getRefundNo())
                .eqIfPresent(AccDivideDtl::getOutRefundNo, reqVO.getOutRefundNo())
                .eqIfPresent(AccDivideDtl::getPayType, reqVO.getPayType())
                .eqIfPresent(AccDivideDtl::getPlatformDivideFlowNo, reqVO.getPlatformDivideFlowNo())
                .eqIfPresent(AccDivideDtl::getOrderType, reqVO.getOrderType())
                .eqIfPresent(AccDivideDtl::getDivideFlowId, reqVO.getDivideFlowId())
                .orderByDesc(AccDivideDtl::getDivideDtlId));
    }
}
