package com.zksr.account.convert.account;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.zksr.account.api.invoiceSetting.vo.AccInvoiceSettingRespVO;
import com.zksr.account.api.platformMerchant.vo.AccInvoiceSettingSaveReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRespVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantSaveReqVO;
import com.zksr.account.domain.AccInvoiceSetting;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;

@Mapper
public interface AccInvoiceSetttingConvert {

    AccInvoiceSetttingConvert INSTANCE = Mappers.getMapper(AccInvoiceSetttingConvert.class);
    
    AccInvoiceSetting convert(AccInvoiceSettingSaveReqVO createReqVO);
    
    List<AccInvoiceSettingRespVO> convertToAccInvoiceSettingRespVO(List<AccInvoiceSetting> list);

}
