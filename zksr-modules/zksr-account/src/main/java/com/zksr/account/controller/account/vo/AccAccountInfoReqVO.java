package com.zksr.account.controller.account.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账户对象 acc_account
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("账户 - acc_account 详情查询")
public class AccAccountInfoReqVO extends BaseEntity {

    @ApiModelProperty(value = "商户ID, suplierId || dcId || ...", required = true)
    private Long merchantId;

    /**
     * 商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    @ApiModelProperty("支付平台")
    private String platform;
}
