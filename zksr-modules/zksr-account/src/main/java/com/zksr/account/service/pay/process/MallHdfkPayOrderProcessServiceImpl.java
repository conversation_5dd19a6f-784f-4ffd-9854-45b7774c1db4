package com.zksr.account.service.pay.process;

import cn.hutool.core.collection.ListUtil;
import com.zksr.account.convert.pay.PayConvert;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderNotifyRespDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.pay.process.PayOrderProcessService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.vo.PayRefundVO;
import com.zksr.trade.api.hdfk.HdfkApi;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import com.zksr.trade.api.order.OrderApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.HDFK_PAYMENT_ORDER_NOT_EXIST;
import static com.zksr.common.core.constant.PayConstants.SUPPLIER_ORDER_DTL_ID_LIST;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商城订单货到付款流程
 * @date 2024/3/7 16:13
 */
@Service
public class MallHdfkPayOrderProcessServiceImpl extends PayOrderProcessService {

    @Resource
    private OrderApi orderApi;

    @Resource
    private AfterApi afterApi;

    @Resource
    private HdfkApi hdfkApi;

    @Autowired
    private IAccAccountService accountService;

    @Override
    public Integer getOrderType() {
        return OrderTypeConstants.PAY_DELIVERY;
    }

    @Override
    public PayOrderDTO validateOrder(PayOrderSubmitReqVO reqVO) {
        // payOrderSubmitReqVO.setExtras(MapUtil.of("supplierOrderIdList", StringUtils.join(payOrderSubmitReqVO.getSupplierOrderIdList(), StringPool.COMMA)));
        if (Objects.isNull(reqVO.getExtras()) || !reqVO.getExtras().containsKey(SUPPLIER_ORDER_DTL_ID_LIST)) {
            throw exception(HDFK_PAYMENT_ORDER_NOT_EXIST);
        }
        List<Long> supplierOrderDtlIdList = ListUtil.toList(reqVO.getExtras().get(SUPPLIER_ORDER_DTL_ID_LIST).split(StringPool.COMMA)).stream().map(Long::parseLong).collect(Collectors.toList());
        // 创建支付参数
        HdfkPaySaveReqVO paySaveReqVO = HdfkPaySaveReqVO.builder()
                .supplierOrderDtlIdList(supplierOrderDtlIdList)
                .payWay(reqVO.getPayWay())
                .sysCode(reqVO.getSysCode())
                .build();

        HdfkPaySaveRespVO hdfkPaySaveRespVO = hdfkApi.createPay(paySaveReqVO).getCheckedData();
        // 必须参数 ============================== start
        PayOrderDTO dto = new PayOrderDTO();
        dto.setBusiId(hdfkPaySaveRespVO.getBusiId());
        dto.setOrderType(reqVO.getOrderType());
        dto.setAppid(reqVO.getAppid());
        dto.setOrderNo(hdfkPaySaveRespVO.getOrderNo());
        dto.setSysCode(reqVO.getSysCode());
        dto.setPayAmt(hdfkPaySaveRespVO.getPayAmt());
        dto.setOpenid(reqVO.getOpenid());
        // 必须参数 ============================== end
        // 返回分账信息
        dto.setBody("货到付款");
        dto.setSettlements(PayConvert.INSTANCE.convert(hdfkPaySaveRespVO.getSettlementDTOS()));
        return dto;
    }

    @Override
    public PayOrderNotifyRespDTO notifyOrderSuccess(PayOrderRespDTO notify) {
        // 支付成功
        hdfkApi.orderPaySuccessCallback(notify.getOrderNo(), notify.getPayPlatform(), notify.getPayWay()).getCheckedData();
        return PayOrderNotifyRespDTO.success("处理成功");
    }

    @Override
    public void notifyRefundSuccess(PayRefundRespDTO notify) {
        // 退款成功
        afterApi.afterRefundSuccessCallback(HutoolBeanUtils.toBean(notify, PayRefundVO.class)).getCheckedData();
    }

    @Override
    public void notifyRefundFailure(PayRefundRespDTO notify) {
        // 退款失败
        afterApi.afterRefundFailCallback(HutoolBeanUtils.toBean(notify, PayRefundVO.class)).getCheckedData();
    }
}
