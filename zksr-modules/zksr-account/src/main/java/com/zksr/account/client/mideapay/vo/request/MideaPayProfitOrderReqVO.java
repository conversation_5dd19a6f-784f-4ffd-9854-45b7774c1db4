package com.zksr.account.client.mideapay.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("美的付多级分账下单请求VO")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayProfitOrderReqVO extends MideaPayBaseReqVO {
    
    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;
    
    @ApiModelProperty("商户分账订单号")
    @JsonProperty("out_profit_no")
    private String outProfitNo;

    @ApiModelProperty("原商户交易订单号")
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    @ApiModelProperty("分账详情，json字符串（支持多级嵌套对象）")
    @JsonProperty("profit_sharing_details")
    private String profitSharingDetails;


    @ApiModelProperty("分账类型，固定为FIX")
    @JsonProperty("profit_sharing_type")
    private String profitSharingType;

    @ApiModelProperty("释放标志 TRUE/FALSE")
    @JsonProperty("release")
    private String release;

    @ApiModelProperty("商户附言")
    @JsonProperty("attach")
    private String attach;

    @ApiModelProperty("提交订单时间 yyyyMMddHHmmss")
    @JsonProperty("out_profit_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private LocalDateTime outProfitTime;

    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @ApiModel("分账接收方")
    public static class ProfitReceiver {
        @ApiModelProperty("收钱的人或商户的编号")
        @JsonProperty("user_login_name")
        private String userLoginName;
        @ApiModelProperty("用户类型，B=商户，C=个人")
        @JsonProperty("user_type")
        private String userType;
        @ApiModelProperty("分给这个人的钱，单位分")
        @JsonProperty("pay_amount")
        private String payAmount;
        @ApiModelProperty("分账描述")
        @JsonProperty("desc")
        private String desc;
        @ApiModelProperty("下级分账列表")
        @JsonProperty("receivers")
        private List<ProfitReceiver> receivers;
    }

    // 在设置 profitSharingReceivers 时自动序列化为 profitSharingDetails
    public void setProfitSharingReceivers(List<ProfitReceiver> receivers) {
        if (receivers != null) {
            this.profitSharingDetails = com.zksr.common.core.utils.JsonUtils.toJsonString(receivers);
        }
    }
} 