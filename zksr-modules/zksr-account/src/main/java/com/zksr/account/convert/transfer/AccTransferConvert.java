package com.zksr.account.convert.transfer;

import com.zksr.account.api.transfer.dto.TransferRetryDTO;
import com.zksr.account.api.transfer.dto.TransferSaveDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccTransfer;
import com.zksr.account.controller.transfer.vo.AccTransferRespVO;
import com.zksr.account.controller.transfer.vo.AccTransferSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 账户转账单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-04-11
*/
@Mapper
public interface AccTransferConvert {

    AccTransferConvert INSTANCE = Mappers.getMapper(AccTransferConvert.class);

    AccTransferRespVO convert(AccTransfer accTransfer);

    AccTransfer convert(AccTransferSaveReqVO accTransferSaveReq);

    PageResult<AccTransferRespVO> convertPage(PageResult<AccTransfer> accTransferPage);

    AccTransferSaveReqVO convert(TransferSaveDTO transferSaveDTO);

    List<TransferRetryDTO> convert(List<AccTransfer> retryAccTransfer);

    List<AccTransferSaveReqVO> convertList(List<TransferSaveDTO> transferSaveDTO);

}