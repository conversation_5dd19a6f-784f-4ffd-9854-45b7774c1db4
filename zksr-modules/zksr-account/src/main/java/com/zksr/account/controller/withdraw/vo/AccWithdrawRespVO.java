package com.zksr.account.controller.withdraw.vo;

import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 账户提现单对象 acc_withdraw
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Data
@ApiModel("账户提现单 - acc_withdraw Response VO")
public class AccWithdrawRespVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 账户提现单 */
    @ApiModelProperty(value = "转账失败信息;转账失败时写入")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long withdrawId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 支付平台 */
    @Excel(name = "支付平台")
    @ApiModelProperty(value = "支付平台")
    private String platform;

    /** 转账方式;0-平台商转提现方 1-平台商转软件商再转提现方 2-无需转账 */
    @Excel(name = "转账方式;0-平台商转提现方 1-平台商转软件商再转提现方 2-无需转账")
    @ApiModelProperty(value = "转账方式;0-平台商转提现方 1-平台商转软件商再转提现方 2-无需转账")
    private String transferType;

    /** 分账方编号 */
    @Excel(name = "分账方编号")
    @ApiModelProperty(value = "分账方编号")
    private String altMchNo;

    /** 分账方全称 */
    @Excel(name = "分账方全称")
    @ApiModelProperty(value = "分账方全称")
    private String altMchName;

    /** 预计导致银行卡号;从平台方分账方查询接口获取 */
    @Excel(name = "预计导致银行卡号;从平台方分账方查询接口获取")
    @ApiModelProperty(value = "预计导致银行卡号;从平台方分账方查询接口获取")
    private String bankAccountNo;

    @Excel(name = "预计到账银行")
    @ApiModelProperty(value = "预计到账银行")
    private String bankName;

    /** 申请提现金额 */
    @Excel(name = "申请提现金额")
    @ApiModelProperty(value = "申请提现金额")
    private BigDecimal applyAmt;

    /** 拒绝原因 */
    @Excel(name = "拒绝原因")
    @ApiModelProperty(value = "拒绝原因")
    private String rejectReason;

    /** 审核拒绝时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "审核拒绝时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "审核拒绝时间")
    private Date rejectTime;

    /** 审核通过时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "审核通过时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "审核通过时间")
    private Date approveTime;

    /**
     * 参见 {@link com.zksr.common.core.enums.WithdrawStateEnum}
     * */
    @Excel(name = "提现单状态(字典:account_withdraw_state);0-待审核 1-打款中", readConverterExp = "已=审核")
    @ApiModelProperty(value = "提现单状态(字典:account_withdraw_state);0-待审核 1-提现中, 2-审核拒绝 , 3-提现成功, 4-提现失败")
    private Integer state;

    /** 提现单转账状态;transfer_type=0时走这一套字段  10-转帐中 11-转账成功 12-转帐失败 */
    @Excel(name = "提现单转账状态(字典:account_withdraw_transfer_state);transfer_type=0时走这一套字段  10-转帐中 11-转账成功 12-转帐失败")
    @ApiModelProperty(value = "提现单转账状态(字典:account_withdraw_transfer_state);transfer_type=0时走这一套字段  10-转帐中 11-转账成功 12-转帐失败")
    private Integer transferState;

    /** 转账单号;平台商分账方A会先调用转账接口 */
    @Excel(name = "转账单号;平台商分账方A会先调用转账接口")
    @ApiModelProperty(value = "转账单号;平台商分账方A会先调用转账接口")
    private String transferNo;

    /** 转账开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "转账开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "转账开始时间")
    private Date transferInitTime;

    /** 转账完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "转账完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "转账完成时间")
    private Date transferFinishTime;

    /** 转账失败信息;转账失败时写入 */
    @Excel(name = "转账失败信息;转账失败时写入")
    @ApiModelProperty(value = "转账失败信息;转账失败时写入")
    private String transferMsg;

    /** 提现单结算状态;130未结算 131-结算中 132结算成功 133-结算失败 */
    @Excel(name = "提现单结算状态(字典:account_withdraw_settle_state);13-结算中 14结算成功 15结算失败")
    @ApiModelProperty(value = "提现单结算状态(字典:account_withdraw_settle_state);13-结算中 14结算成功 15结算失败")
    private Integer settleState;

    /** 结算单号 */
    @Excel(name = "结算单号")
    @ApiModelProperty(value = "结算单号")
    private String settleNo;

    /** 结算失败信息;结算失败时写入 */
    @Excel(name = "结算失败信息;结算失败时写入")
    @ApiModelProperty(value = "结算失败信息;结算失败时写入")
    private String settleMsg;

    /** 结算开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "结算开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "结算开始时间")
    private Date settleInitTime;

    /** 结算完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "结算完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "结算完成时间")
    private Date settleFinishTime;

    /** 提现手续费(支付平台收取,一般是固定金额) */
    @Excel(name = "提现手续费(支付平台收取,一般是固定金额)")
    @ApiModelProperty(value = "提现手续费(支付平台收取,一般是固定金额)")
    private BigDecimal fee;

    /** 提现单转账状态;transfer_type=1时走这一套字段，记录第一步转账  10-转帐中 11-转账成功 12-转帐失败 */
    @Excel(name = "提现单转账状态;transfer_type=1时走这一套字段，记录第一步转账  10-转帐中 11-转账成功 12-转帐失败")
    @ApiModelProperty(value = "提现单转账状态;transfer_type=1时走这一套字段，记录第一步转账  10-转帐中 11-转账成功 12-转帐失败")
    private Integer transferState1;

    /** 转账单号;平台商分账方A会先调用转账接口 */
    @Excel(name = "转账单号;平台商分账方A会先调用转账接口")
    @ApiModelProperty(value = "转账单号;平台商分账方A会先调用转账接口")
    private String transferNo1;

    /** 转账开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "转账开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "转账开始时间")
    private Date transferInitTime1;

    /** 转账完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "转账完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "转账完成时间")
    private Date transferFinishTime1;

    /** 转账失败信息;转账失败时写入 */
    @Excel(name = "转账失败信息;转账失败时写入")
    @ApiModelProperty(value = "转账失败信息;转账失败时写入")
    private String transferMsg1;

    /** 提现单转账状态;transfer_type=1时走这一套字段，记录第一步转账   10-转帐中 11-转账成功 12-转帐失败 */
    @Excel(name = "提现单转账状态;transfer_type=1时走这一套字段，记录第一步转账   10-转帐中 11-转账成功 12-转帐失败")
    @ApiModelProperty(value = "提现单转账状态;transfer_type=1时走这一套字段，记录第一步转账   10-转帐中 11-转账成功 12-转帐失败")
    private Integer transferState2;

    /** 转账单号;平台商分账方A会先调用转账接口 */
    @Excel(name = "转账单号;平台商分账方A会先调用转账接口")
    @ApiModelProperty(value = "转账单号;平台商分账方A会先调用转账接口")
    private String transferNo2;

    /** 转账开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "转账开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "转账开始时间")
    private Date transferInitTime2;

    /** 转账完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "转账完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "转账完成时间")
    private Date transferFinishTime2;

    /** 转账失败信息;转账失败时写入 */
    @Excel(name = "转账失败信息;转账失败时写入")
    @ApiModelProperty(value = "转账失败信息;转账失败时写入")
    private String transferMsg2;


    /** 实际到账银行账户 */
    @Excel(name = "实际到账银行账户")
    @ApiModelProperty(value = "实际到账银行账户")
    private String bankAccountNo1;

    /** 实际到账银行账户名称 */
    @Excel(name = "实际到账银行账户名称")
    @ApiModelProperty(value = "实际到账银行账户名称")
    private String bankAccountName1;

    /** 凭证, 最多上传3张照片 */
    @Excel(name = "凭证, 最多上传3张照片")
    @ApiModelProperty(value = "凭证, 最多上传3张照片")
    private String voucher;

    /** 预计到账金额 */
    @Excel(name = "预计到账金额")
    @ApiModelProperty(value = "预计到账金额")
    private BigDecimal withdrawAmt;

    /** 平台商手续费利润 */
    @Excel(name = "平台商手续费利润")
    @ApiModelProperty(value = "平台商手续费利润")
    private BigDecimal partnerProfit;

    /** 转账金额 */
    @Excel(name = "转账金额")
    @ApiModelProperty(value = "转账金额")
    private BigDecimal transferAmt;

    @ApiModelProperty(value = "账户类型;0-储值账户 1-赠送余额")
    private Integer accountType;

    @ApiModelProperty(value = "对于客户来说只需要知道平台收取多少手续费就行了")
    private BigDecimal operFee;

    /**
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "账户支付平台")
    private String accountPlatform;
}
