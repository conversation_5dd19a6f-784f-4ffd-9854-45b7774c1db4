package com.zksr.account.controller.recharge.vo;

import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 后台导入充值对象 acc_recharge_import
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@ApiModel("后台导入充值 - acc_recharge_import Response VO")
public class AccRechargeImportRespVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总金额")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeImportId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 0-未删除,1-已删除 */
    @Excel(name = "0-未删除,1-已删除")
    @ApiModelProperty(value = "0-未删除,1-已删除")
    private Integer deleted;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private String auditBy;

    /** 0-初始化, 1-审核成功, 2-作废 */
    @Excel(name = "0-初始化, 1-审核成功, 2-作废")
    @ApiModelProperty(value = "0-初始化, 1-审核成功, 2-作废")
    private Integer rechargeImportState;

    /** 凭证 */
    @Excel(name = "凭证")
    @ApiModelProperty(value = "凭证")
    private String voucher;

    /** 计数 */
    @Excel(name = "计数")
    @ApiModelProperty(value = "计数")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long counter;

    /** 总金额 */
    @Excel(name = "总金额")
    @ApiModelProperty(value = "总金额")
    private BigDecimal rechargeAmt;

    @ApiModelProperty(value = "审核手机号")
    private String auditPhone;

    @ApiModelProperty(value = "充值详情数据")
    private List<AccRechargeImportDtlRespVO> dtlList;
}
