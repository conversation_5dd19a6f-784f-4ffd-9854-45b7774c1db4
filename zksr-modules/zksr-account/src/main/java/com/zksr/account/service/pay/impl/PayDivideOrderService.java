package com.zksr.account.service.pay.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.zksr.account.controller.divide.vo.AccDivideFlowSaveReqVO;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.mapper.AccDivideFlowMapper;
import com.zksr.account.model.pay.vo.CreateDivideReqVO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.PayWxB2bDivideRespVO;
import com.zksr.account.service.IAccDivideDtlService;
import com.zksr.account.service.IAccDivideFlowService;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.pay.IPayDivideOrderService;
import com.zksr.common.core.enums.DivideStateEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/5 14:23
 */
public abstract class PayDivideOrderService implements IPayDivideOrderService {

    @Autowired
    protected IAccountCacheService accountCacheService;

    @Autowired
    protected IAccPlatformMerchantService platformMerchantService;

    @Autowired
    protected IAccDivideFlowService divideFlowService;

    @Autowired
    protected IAccDivideDtlService divideDtlService;

    @Autowired
    protected AccDivideFlowMapper accDivideFlowMapper;

    /**
     * 获取分账商户, 主要判断业务员是否走统一分润账户, 还有获取最新商户号信息
     * @param divideReqVO   分账请求
     * @param totalAltAmt   分账金额
     * @param payFlow       支付流水
     * @return  分账商户号
     */
    public String getPayeeMerchantNo(CreateDivideReqVO divideReqVO, BigDecimal totalAltAmt, AccPayFlow payFlow) {
        String altMchNo = null;
        // 大于零的有效分账才查询验证商户信息
        if (NumberUtil.isGreater(totalAltAmt, BigDecimal.ZERO)) {
            PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(payFlow.getSysCode());
            // 查看是否统一业务员分润账户
            if (MerchantTypeEnum.isColonel(divideReqVO.getMerchantType()) && StringPool.ONE.equals(payConfigDTO.getSwitchPartnerColonel())) {
                AccPlatformMerchant payee = platformMerchantService.getPlatformMerchant(payFlow.getSysCode(), MerchantTypeEnum.PARTNER_COLONEL.getType(), payFlow.getPlatform(), payFlow.getSysCode());
                if (Objects.nonNull(payee) && StringUtils.isNotEmpty(payee.getAltMchNo())) {
                    altMchNo = payee.getAltMchNo();
                };
            } else {
                AccPlatformMerchant payee = platformMerchantService.getPlatformMerchant(divideReqVO.getMerchantId(), divideReqVO.getMerchantType(), payFlow.getPlatform(), payFlow.getSysCode());
                if (Objects.nonNull(payee) && StringUtils.isNotEmpty(payee.getAltMchNo())) {
                    altMchNo = payee.getAltMchNo();
                };
            }
        }
        return altMchNo;
    }


    /**
     * 发起分账成功
     * @param divideList    分账发起绑定分账明细数据
     * @param divideFlow    分账流水
     */
    public void processing(List<AccDivideDtl> divideList, AccDivideFlowSaveReqVO divideFlow) {
        ArrayList<AccDivideDtl> updateList = new ArrayList<>();
        for (AccDivideDtl divideDtl : divideList) {
            divideDtl.setDivideDtlId(divideDtl.getDivideDtlId());
            divideDtl.setProcessTime(new Date());
            divideDtl.setOnlineDivideState(DivideStateEnum.PROCESSING.getState());
            divideDtl.setDivideFlowId(divideFlow.getDivideFlowId());
            divideDtl.setAltMchNo(divideFlow.getPayeeNo());
            updateList.add(divideDtl);
        }
        divideDtlService.updateBatch(updateList);
    }

    /**
     * 发起分账失败
     * @param divideFlow    分账流水
     * @param divideRespVO  分账结果
     */
    public void fail(AccDivideFlowSaveReqVO divideFlow, CreateDivideRespVO divideRespVO) {
        divideFlow.setDivideStatus(NumberPool.INT_THREE);
        divideFlow.setErrMsg(divideRespVO.getMsg());
        divideFlow.setDivideReqStatus(NumberPool.INT_ONE);
        divideFlow.setDivideFlowId(divideFlow.getDivideFlowId());
        divideFlowService.updateAccDivideFlow(divideFlow);
    }

    /**
     * 更新分账状态
     * @param divideFlow    分账流水
     * @param divideRespVO  分账结果
     */
    public void updateDivide(AccDivideFlow divideFlow, PayWxB2bDivideRespVO divideRespVO) {
        // 分账中无需处理
        if (divideRespVO.getStatus() == 1) {
            return;
        }
        // 失败和成功, 都更新数据
        divideFlow.setDivideStatus(divideRespVO.getStatus());
        divideFlow.setErrMsg(divideRespVO.getMsg());
        accDivideFlowMapper.updateById(divideFlow);
        // 分账数据
        List<AccDivideDtl> divideDtlList = divideDtlService.getDivideList(
                AccDivideDtl.builder()
                        .divideFlowId(divideFlow.getDivideFlowId())
                        .build()
        );
        if (divideDtlList.isEmpty()) {
            return;
        }
        ArrayList<AccDivideDtl> updateDivideDtlList = new ArrayList<>();
        for (AccDivideDtl divideDtl : divideDtlList) {
            AccDivideDtl updateDivideDtl = new AccDivideDtl();
            updateDivideDtl.setDivideDtlId(divideDtl.getDivideDtlId());
            updateDivideDtl.setOnlineDivideState(DivideStateEnum.FINISH.getState());
            updateDivideDtl.setDivideTime(DateUtil.date());
            updateDivideDtlList.add(updateDivideDtl);
        }
        divideDtlService.updateBatch(updateDivideDtlList);
    };

}
