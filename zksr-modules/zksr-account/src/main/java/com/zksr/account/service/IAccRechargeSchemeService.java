package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.api.recharge.dto.RechargeSchemeDTO;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemeRespVO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccRechargeScheme;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemePageReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemeSaveReqVO;

import java.util.List;

/**
 * 储值充值套餐配置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
public interface IAccRechargeSchemeService {

    /**
     * 新增储值充值套餐配置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAccRechargeScheme(@Valid AccRechargeSchemeSaveReqVO createReqVO);

    /**
     * 修改储值充值套餐配置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccRechargeScheme(@Valid AccRechargeSchemeSaveReqVO updateReqVO);

    /**
     * 批量删除储值充值套餐配置
     *
     * @param rechargeSchemeIds 需要删除的储值充值套餐配置主键集合
     * @return 结果
     */
    public void deleteAccRechargeSchemeByRechargeSchemeIds(Long[] rechargeSchemeIds);

    /**
     * 获得储值充值套餐配置
     *
     * @param rechargeSchemeId 充值方案id
     * @return 储值充值套餐配置
     */
    public AccRechargeScheme getAccRechargeScheme(Long rechargeSchemeId);

    /**
     * 获得储值充值套餐配置分页
     *
     * @param pageReqVO 分页查询
     * @return 储值充值套餐配置分页
     */
    PageResult<AccRechargeSchemeRespVO> getAccRechargeSchemePage(AccRechargeSchemePageReqVO pageReqVO);

    /**
     * 停用充值配置
     * @param rechargeSchemeId  配置ID
     */
    void disable(Long rechargeSchemeId);

    /**
     * 启用充值配置
     * @param rechargeSchemeId  配置ID
     */
    void enable(Long rechargeSchemeId);

    /**
     * 获取有效启用的城市储值配置
     * @param areaId    城市ID
     */
    List<RechargeSchemeDTO> getAreaRechargeSchemeList(Long areaId);

    /**
     * 获取城市有效储值套餐
     * @param areaId    城市ID
     * @return  储值套餐
     */
    RechargeSchemeDTO getValidAreaRechargeScheme(Long areaId);
}
