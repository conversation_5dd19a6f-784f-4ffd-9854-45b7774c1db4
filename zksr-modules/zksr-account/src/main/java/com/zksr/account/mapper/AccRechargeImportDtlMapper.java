package com.zksr.account.mapper;

import com.zksr.account.controller.recharge.vo.AccRechargeImportDtlRespVO;
import com.zksr.account.domain.AccRechargeImport;
import com.zksr.account.domain.AccRechargeImportDtl;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * PC后台导入充值详情
Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Mapper
public interface AccRechargeImportDtlMapper extends BaseMapperX<AccRechargeImportDtl> {
    default void deleteByRechargeImportId(Long rechargeImportId) {
        delete(
                new LambdaQueryWrapperX<AccRechargeImportDtl>()
                        .eq(AccRechargeImportDtl::getRechargeImportId, rechargeImportId)
        );
    }

    default List<AccRechargeImportDtl> selectByRechargeImportId(Long rechargeImportId) {
        return selectList(
                new LambdaQueryWrapperX<AccRechargeImportDtl>()
                        .eq(AccRechargeImportDtl::getRechargeImportId, rechargeImportId)
        );
    }

    List<AccRechargeImportDtlRespVO> selectByRechargeImportIdRespVO(@Param("rechargeImportId") Long rechargeImportId);
}
