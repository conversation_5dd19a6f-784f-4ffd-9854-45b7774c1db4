package com.zksr.account.mq;

import com.alibaba.fastjson.JSON;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccTransfer;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundQueryVO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.rocketmq.constant.MessageConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.support.MessageBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/1 10:26
 */
@Configuration
@Slf4j
public class AccountMqProducer {

    @Autowired
    private StreamBridge streamBridge;

    @Autowired
    private RedisService redisService;

    /**
     * 发送结算流水
     * {@link AccountMqConsumer#accountFlow()}
     * @param accountFlow   账户流水
     */
    public void sendSettleFlow(AccAccountFlow accountFlow){
        log.info("acceptSettleFlow发送消息：" + JSON.toJSONString(accountFlow));
        boolean flag = streamBridge.send(
                MessageConstant.ACCOUNT_FLOW_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(accountFlow)
                        .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, NumberPool.INT_TWO)
                        .build());
    }

    /**
     * 发送支付回调通知
     * {@link AccountMqConsumer#payNotify()}
     * @param orderResp
     */
    public void sendPayNotify(PayOrderRespDTO orderResp){
        log.info("sendPayNotify发送消息：" + JSON.toJSONString(orderResp));
        boolean flag = streamBridge.send(
                MessageConstant.ORDER_PAY_NOTIFY_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(orderResp)
                        .build());
    }

    /**
     * 发送退款回调通知
     * {@link AccountMqConsumer#refundNotify()()}
     * @param refundResp
     */
    public void sendRefundNotify(PayRefundRespDTO refundResp){
        log.info("sendRefundNotify发送消息：" + JSON.toJSONString(refundResp));
        boolean flag = streamBridge.send(
                MessageConstant.ORDER_REFUND_NOTIFY_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(refundResp)
                        .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, NumberPool.INT_THREE)
                        .build());
    }

    /**
     * 处理内部转账单
     * {@link com.zksr.account.mq.AccountMqConsumer#accountTransfer()}
     * @param accTransfer
     */
    public void sendAccountTransfer(AccTransfer accTransfer){
        log.info("sendAccountTransfer发送消息：" + JSON.toJSONString(accTransfer));
        boolean flag = streamBridge.send(
                MessageConstant.ACCOUNT_TRANSFER_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(accTransfer)
                        .build());
    }

    /**
     * 发送延迟退款查询
     * {@link AccountMqConsumer#refundDelayQuery()}
     * @param refundQueryVO 退款查询对象
     */
    public void sendRefundDelayQuery(PayRefundQueryVO refundQueryVO){
        log.info("sendRefundDelayQuery发送消息：" + JSON.toJSONString(refundQueryVO));
        boolean flag = streamBridge.send(
                MessageConstant.ORDER_REFUND_DELAY_QUERY_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(refundQueryVO)
                        // 延迟3分钟
                        .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, NumberPool.INT_SEVEN)
                        .build());
    }

    /**
     * 发送延迟查询分账结果
     * {@link AccountMqConsumer#divideDelayQuery()}
     * @param divideFlowId 分账请求流水
     */
    public void sendDivideDelayQuery(Long divideFlowId){
        log.info("sendDivideDelayQuery发送消息：" + JSON.toJSONString(divideFlowId));
        boolean flag = streamBridge.send(
                MessageConstant.DIVIDE_DELAY_QUERY_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(divideFlowId)
                        // 延迟8分钟
                        .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 12)
                        .build());
    }

    /**
     * 发送微信整单分账延迟查询分账结果
     * {@link AccountMqConsumer#wxOrderDivideDelayQuery()}
     * @param submitReqVO 分账请求流水
     */
    public void sendWxOrderDivideDelayQuery(PayRefundOrderSubmitReqVO submitReqVO){
        log.info("sendDivideDelayQuery发送消息：" + JSON.toJSONString(submitReqVO));
        boolean flag = streamBridge.send(
                MessageConstant.WX_ORDER_DIVIDE_DELAY_QUERY_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(submitReqVO)
                        // 延迟2分钟
                        .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 6)
                        .build());
    }

    /**
     * 发送延迟推送第三方储值充值、提现信息
     * {@link com.zksr.system.mq.SyncDataConsumer#syncDataBranchValueInfoEvent()}
     * @param accountFlowIds 账户流水ID
     */
    public void sendSyncBranchValueInfo(List<Long> accountFlowIds){
        log.info("sendSyncBranchValueInfo -- 发送延迟推送第三方储值充值、提现信息:发送消息：" + JSON.toJSONString(accountFlowIds));
        boolean flag = streamBridge.send(
                MessageConstant.SYNC_DATA_BRANCH_VALUE_INFO_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(accountFlowIds)
                        // 延迟10秒钟
                        .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, NumberPool.INT_TWO)
                        .build());
    }
}
