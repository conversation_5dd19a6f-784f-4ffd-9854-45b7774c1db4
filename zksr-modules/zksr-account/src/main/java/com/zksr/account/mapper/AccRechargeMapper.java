package com.zksr.account.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.account.controller.recharge.vo.AccRechargeRespVO;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.controller.recharge.vo.AccRechargePageReqVO;

import java.util.List;


/**
 * 账户充值单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Mapper
public interface AccRechargeMapper extends BaseMapperX<AccRecharge> {
    default PageResult<AccRecharge> selectPage(AccRechargePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccRecharge>()
                    .eqIfPresent(AccRecharge::getRechargeId, reqVO.getRechargeId())
                    .eqIfPresent(AccRecharge::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccRecharge::getChargeType, reqVO.getChargeType())
                    .eqIfPresent(AccRecharge::getRechargeMerchantType, reqVO.getRechargeMerchantType())
                    .eqIfPresent(AccRecharge::getRechargeMerchantId, reqVO.getRechargeMerchantId())
                    .eqIfPresent(AccRecharge::getReceiveMerhcantId, reqVO.getReceiveMerhcantId())
                    .eqIfPresent(AccRecharge::getState, reqVO.getState())
                    .eqIfPresent(AccRecharge::getRechargeNo, reqVO.getRechargeNo())
                    .gtIfPresent(AccRecharge::getCreateTime, reqVO.getStartTime())
                    .ltIfPresent(AccRecharge::getCreateTime, reqVO.getEndTime())
                .orderByDesc(AccRecharge::getRechargeId));
    }

    default AccRecharge selectRechargeByNo(String rechargeNo) {
        return selectOne(Wrappers.lambdaQuery(AccRecharge.class).eq(AccRecharge::getRechargeNo, rechargeNo));
    }

    List<AccRechargeRespVO> selectExtPage(AccRechargePageReqVO pageReqVO);
}
