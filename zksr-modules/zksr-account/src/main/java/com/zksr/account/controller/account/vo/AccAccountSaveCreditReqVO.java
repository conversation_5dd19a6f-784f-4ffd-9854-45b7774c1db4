package com.zksr.account.controller.account.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("账户 - 操作授信金额实体")
public class AccAccountSaveCreditReqVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商户ID, 如supplierId", required = true)
    private Long merchantId;

    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    @ApiModelProperty(value = "支付平台", hidden = true)
    private String platform;

    /** 授信额度 */
    @ApiModelProperty(value = "授信额度, 叠加操作, 允许传入负值", required = true)
    @NotNull(message = "creditAmt can't be null")
    private BigDecimal creditAmt;
}
