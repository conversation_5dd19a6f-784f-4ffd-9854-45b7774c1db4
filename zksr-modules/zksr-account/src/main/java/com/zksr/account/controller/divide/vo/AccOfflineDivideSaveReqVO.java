package com.zksr.account.controller.divide.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 线下分账处理对象 acc_offline_divide
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Data
@ApiModel("线下分账处理 - acc_offline_divide分页 Request VO")
public class AccOfflineDivideSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型", required = true)
    @NotNull(message = "merchantType 商户类型不能为空")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id", required = true)
    @NotNull(message = "merchantId 商户ID不能为空")
    private Long merchantId;

    /** 线下转账单号 */
    @Excel(name = "线下转账单号")
    @ApiModelProperty(value = "线下转账单号")
    private String offlineNo;

    /** 凭证图片 */
    @Excel(name = "凭证图片")
    @ApiModelProperty(value = "凭证图片")
    private String voucherPic;

    /** 分账数据列表 */
    @ApiModelProperty(value = "分账数据列表, 使用divideDtlId填充")
    @NotNull(message = "至少处理一条分账数据")
    @Size(min = 1, message = "至少处理一条分账数据")
    private List<Long> divideDtlList;
}
