package com.zksr.account.client.hlb;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.client.MerchantClient;
import com.zksr.account.client.hlb.vo.AccountBalanceQueryVO;
import com.zksr.account.client.hlb.vo.HlbMerchantRequestVO;
import com.zksr.account.client.hlb.vo.body.*;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.enums.MerchantCredentialType;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.MerchantUploadPicStateEnum;
import com.zksr.common.core.enums.PayTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.cert.HlbCertUtil;
import com.zksr.account.client.http.HttpClientService;
import com.zksr.common.security.utils.SecurityUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝商户操作
 * @date 2024/4/23 14:11
 */
@Data
@Slf4j
public class HlbMerchantClient implements MerchantClient {

    /**
     * 平台商编
     */
    private String platformMerchantNo;

    /**
     * 公共平台md5签名秘钥
     */
    private String publicMerchantMd5SignKey;

    /**
     * des3 加密key
     */
    private String publicEncryptKey;

    /**
     * 回调地址
     */
    private String notifyUrl;

    private final String MERCHANT_URL = "http://pay.trx.helipay.com/trx/merchant/interface.action";

    private final String MERCHANT_URL02 = "http://entry.trx.helipay.com/trx/merchantEntry/interface.action";

    private final String MODIFY_MERCHANT_INFO_V2 = "http://entry.trx.helipay.com/trx/merchantEntry/upload.action";

    private static Map<String, String> merchantType = new HashMap<>();

    static {
        // 商户类型
        merchantType.put("0", "PERSON");
        merchantType.put("1", "INDIVIDUALBISS");
        merchantType.put("2", "ENTERPRISE");
    }

    public HlbMerchantClient(String platformMerchantNo, String publicMerchantMd5SignKey, String publicEncryptKey, String notifyUrl) {
        this.platformMerchantNo = platformMerchantNo;
        this.publicMerchantMd5SignKey = publicMerchantMd5SignKey;
        this.publicEncryptKey = publicEncryptKey;
        this.notifyUrl = notifyUrl;
    }

    @Override
    public PayPlatformAccountVO getAccountBalance(String merchantNo) {
        AccountBalanceQueryVO reqVo = new AccountBalanceQueryVO();
        reqVo.setP2_customerNumber(merchantNo);
        PayPlatformAccountVO accountVO = new PayPlatformAccountVO();
        if (StringUtils.isEmpty(merchantNo)) {
            return accountVO;
        }
        try {
            String resultMsg = reuqest(MERCHANT_URL, reqVo, AccountBalanceQueryVO.NEED_SIGN_PARAMS, AccountBalanceQueryVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝查询商户余额结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("rt2_retCode"))) {
                accountVO.setBalance(new BigDecimal(resultObj.getString("rt6_balance")));
                accountVO.setFrozenBalance(new BigDecimal(resultObj.getString("rt7_frozenBalance")));
                accountVO.setSettleAmt(new BigDecimal(resultObj.getString("rt15_amountToBeSettled")));
                accountVO.setD0BalanceAmt(new BigDecimal(resultObj.getString("rt8_d0Balance")));
            }
        } catch (Exception e) {
            log.error("查询合利宝余额异常", e);
        }
        return accountVO;
    }

    @Override
    public PayPlatformMerchantVO getRegisterMerchant(String orderNo, String merchantNo) {
        HlbMerchantRequestVO requestVO = new HlbMerchantRequestVO();
        requestVO.setMerchantNo(platformMerchantNo);
        requestVO.setInterfaceName("registerQuery");
        requestVO.setEncryptionKey(publicEncryptKey);
        requestVO.setBody(JSON.toJSONString(HlbMerchantInfoBodyVO.buildRegisterQuery(orderNo, merchantNo)));
        PayPlatformMerchantVO payPlatformMerchantVO = null;
        try {
            String resultMsg = reuqest(MERCHANT_URL02, requestVO, HlbMerchantRequestVO.NEED_SIGN_PARAMS, HlbMerchantRequestVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝查询进件商户结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("code"))) {
                JSONObject userInfo = resultObj.getJSONObject("data");
                // 构建Java Po 返回数据
                payPlatformMerchantVO = new PayPlatformMerchantVO();
                payPlatformMerchantVO.setAccountNo(userInfo.getString("accountNo"));
                payPlatformMerchantVO.setSignName(userInfo.getString("signName"));
                payPlatformMerchantVO.setBankName(userInfo.getString("bankName"));
                payPlatformMerchantVO.setAccountName(userInfo.getString("accountName"));
                payPlatformMerchantVO.setBankBranch(userInfo.getString("bankBranch"));
                payPlatformMerchantVO.setSettleBankType(userInfo.getString("settleBankType"));
                payPlatformMerchantVO.setMerchantNo(userInfo.getString("merchantNo"));
                payPlatformMerchantVO.setBankCode(userInfo.getString("bankCode"));
                payPlatformMerchantVO.setLegalPerson(userInfo.getString("legalPerson"));
                payPlatformMerchantVO.setLegalPersonID(userInfo.getString("legalPersonID"));
                payPlatformMerchantVO.setBusinessLicense(userInfo.getString("businessLicense"));
                payPlatformMerchantVO.setLinkman(userInfo.getString("linkman"));
                payPlatformMerchantVO.setLinkPhone(userInfo.getString("linkPhone"));
                payPlatformMerchantVO.setState(MerchantRegisterStateEnum.getState(userInfo.getString("status")));
                payPlatformMerchantVO.setMsg(userInfo.getString("msg"));
                String type = userInfo.getString("merchantType");
                for (String key : merchantType.keySet()) {
                    if (merchantType.get(key).equals(type)) {
                        payPlatformMerchantVO.setMerchantType(key);
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.error(" getRegisterMerchant异常,", e);
            throw new RuntimeException(e);
        }
        return payPlatformMerchantVO;
    }

    @Override
    public PayPlatformMerchantVO getMerchant(String merchantNo) {
        HlbMerchantRequestVO requestVO = new HlbMerchantRequestVO();
        requestVO.setMerchantNo(platformMerchantNo);
        requestVO.setInterfaceName("merchantInfoQuery");
        requestVO.setEncryptionKey(publicEncryptKey);
        requestVO.setBody(JSON.toJSONString(HlbMerchantInfoBodyVO.buildInfoQuery(merchantNo, platformMerchantNo)));
        PayPlatformMerchantVO payPlatformMerchantVO = null;
        try {
            String resultMsg = reuqest(MERCHANT_URL02, requestVO, HlbMerchantRequestVO.NEED_SIGN_PARAMS, HlbMerchantRequestVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝查询商户结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("code"))) {
                JSONObject userInfo = resultObj.getJSONObject("data");
                // 构建Java Po 返回数据
                payPlatformMerchantVO = new PayPlatformMerchantVO();
                payPlatformMerchantVO.setAccountNo(userInfo.getString("accountNo"));
                payPlatformMerchantVO.setSignName(userInfo.getString("signName"));
                payPlatformMerchantVO.setBankName(userInfo.getString("bankName"));
                payPlatformMerchantVO.setAccountName(userInfo.getString("accountName"));
                payPlatformMerchantVO.setBankBranch(userInfo.getString("bankBranch"));
                payPlatformMerchantVO.setSettleBankType(userInfo.getString("settleBankType"));
                payPlatformMerchantVO.setMerchantNo(userInfo.getString("merchantNo"));
                payPlatformMerchantVO.setBankCode(userInfo.getString("bankCode"));
                payPlatformMerchantVO.setLegalPerson(userInfo.getString("legalPerson"));
                payPlatformMerchantVO.setLegalPersonID(userInfo.getString("legalPersonID"));
                payPlatformMerchantVO.setBusinessLicense(userInfo.getString("businessLicense"));
                payPlatformMerchantVO.setLinkman(userInfo.getString("linkman"));
                payPlatformMerchantVO.setLinkPhone(userInfo.getString("linkPhone"));
                payPlatformMerchantVO.setState(MerchantRegisterStateEnum.getState(userInfo.getString("status")));
                payPlatformMerchantVO.setMsg(userInfo.getString("msg"));
                String type = userInfo.getString("merchantType");
                for (String key : merchantType.keySet()) {
                    if (merchantType.get(key).equals(type)) {
                        payPlatformMerchantVO.setMerchantType(key);
                    }
                }
            }
        } catch (IllegalAccessException e) {
            log.error(" getMerchant异常,", e);
            throw new RuntimeException(e);
        }
        return payPlatformMerchantVO;
    }

    @Override
    public PlatformMerchantRegisterSaveRespVO registerMerchant(AccPlatformMerchantRegisterReqVO reqVO) {
        PlatformMerchantRegisterSaveReqVO saveReqVO = reqVO.getPlatformMerchantRegisterSaveReqVO();

        HlbMerchantRequestVO requestVO = new HlbMerchantRequestVO();
        requestVO.setMerchantNo(platformMerchantNo);
        requestVO.setInterfaceName("register");
        requestVO.setEncryptionKey(publicEncryptKey);
        // 请求体
        HlbRegisterMerchantBodyVO body = new HlbRegisterMerchantBodyVO();
        // HLB + yyyyMMddHHmmss + 6 位随机数
        body.setOrderNo("HLB" +
                DateUtil.formatDateTime(new Date())
                        .replace(StringPool.DASH, StringPool.EMPTY)
                        .replace(StringPool.COLON, StringPool.EMPTY)
                        .replace(StringPool.SPACE, StringPool.EMPTY)
                + RandomUtil.randomNumbers(6));

        body.setSignName(saveReqVO.getAltMchName());
        body.setShowName(saveReqVO.getAltMchName());
        body.setMerchantType(merchantType.get(saveReqVO.getMerchantType()));
        body.setLegalPerson(saveReqVO.getLegalPerson());
        body.setLegalPersonID(saveReqVO.getIdCardNo());
        body.setBusinessLicense(saveReqVO.getLicenseNo());
        body.setAddress("湖南省长沙市雨花区树木岭43号兴旺双铁城5层");//详细地址
        body.setLinkman(saveReqVO.getBusiContactName());//联系人
        body.setLinkPhone(saveReqVO.getBusiContactMobileNo());//联系电话

        // 是否指定邮箱
        if (StringUtils.isNotEmpty(saveReqVO.getEmail())) {
            body.setEmail(saveReqVO.getEmail());
        } else {
            body.setEmail(StringUtils.format("{}@qq.com", saveReqVO.getBusiContactMobileNo()));
        }
        body.setBankCode(saveReqVO.getBankChannelNo());//联行号注：需商户电话联系银行核实具体联行号
        body.setAccountName(saveReqVO.getBankAccountName());//持卡人名称
        body.setAccountNo(saveReqVO.getBankAccountNo());//银行卡号

        body.setOrgNum("0".equals(saveReqVO.getMerchantType()) ? body.getLegalPersonID() : body.getBusinessLicense());
        body.setRegionCode("180105");//区编码
        body.setSettleBankType("0".equals(saveReqVO.getSettleBankType()) ? "TOPRIVATE" : "TOPUBLIC");//结算卡类型 TOPRIVATE 对私, TOPUBLIC 对公

        /**
         * 不是运营商的全部T+1自动到账, 目前的流程2025年2月22日11:37:33, B2B支付再用, 其他的支付都没有在使用
         * 只有运营商的账户需要自己提现, 其他的人全部使用自动到账
         * 业务员提现也是, 转账完成以后表示提现完成了
         */
        MerchantTypeEnum merchantType = MerchantTypeEnum.fromValue(reqVO.getMerchantType());
        //  结算类型 T1：工作日隔天结算 D1：自然日隔天结算 D0：当日结算（目前不支持）
        //  NOTOPEN 不开通结算, AUTO 自动结算, SELF 自主结算
        switch (merchantType) {
            case SUPPLIER:
            case DC:
                body.setSettlementPeriod("D1");
                body.setSettlementMode("SELF");
                break;
            default:
                body.setSettlementPeriod("T1");
                body.setSettlementMode("AUTO");
                break;
        }
        body.setAuthorizationFlag(true);
        body.setNeedPosFunction(false);
        body.setAgreeProtocol(true);
        body.setSettleMode("MERCHANT"); //结算模式 MERCHANT 自主结算
        body.setIdType("IDCARD"); // 证件类型, IDCARD 身份证
        body.setLinkmanType(body.getLinkman().equals(body.getLegalPerson())?"LEGAL":"SUPER");//联系人类型
        body.setCallbackUrl(notifyUrl + StringUtils.format("/platformMerchant/notify/register/{}/{}/{}",
                SecurityUtils.getLoginUser().getSysCode(),
                PayTypeEnum.PAY.getType(),
                saveReqVO.getPlatform()
        ));
        if ("1".equals(saveReqVO.getMerchantType())){
            body.setMerchantCategory("OTHER");//经营类别 OTHER其他
            body.setIndustryTypeCode("292");
        }else if("2".equals(saveReqVO.getMerchantType())){
            body.setMerchantCategory("OTHER");//经营类别 38其他
            body.setIndustryTypeCode("291");
        }else if("0".equals(saveReqVO.getMerchantType())){
            body.setMerchantCategory("OTHER");//经营类别
        }
        requestVO.setBody(JSON.toJSONString(body));
        try {
            String resultMsg = reuqest(MERCHANT_URL02, requestVO, HlbMerchantRequestVO.NEED_SIGN_PARAMS, HlbMerchantRequestVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝进件商户结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("code"))) {
                JSONObject data = resultObj.getJSONObject("data");
                return PlatformMerchantRegisterSaveRespVO
                        .builder()
                        .auditStatus(data.getString("entryStatus"))
                        .merchantNo(data.getString("merchantNo"))
                        .orderNo(body.getOrderNo())
                        .build();
            } else {
                return PlatformMerchantRegisterSaveRespVO
                        .builder()
                        .msg(resultObj.getString("message"))
                        .orderNo(body.getOrderNo())
                        .auditStatus(MerchantRegisterStateEnum.OVERRULE.getState())
                        .build();
            }
        } catch (IllegalAccessException e) {
            log.error(" registerMerchant异常,", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public PlatformMerchantRegisterSaveRespVO parseRegisterNotify(Map<String, String> params, String body) {
        JSONObject resultObj = JSONObject.parseObject(body);
        String decryptDataStr = HlbCertUtil.verificationAndDecrypt(
                resultObj.getString("data"),
                resultObj.getString("sign"),
                publicEncryptKey,
                publicMerchantMd5SignKey
        );
        JSONObject decryptObj = JSONObject.parseObject(decryptDataStr);
        if ("0000".equals(decryptObj.getString("code"))) {
            JSONObject data = decryptObj.getJSONObject("data");
            return PlatformMerchantRegisterSaveRespVO
                    .builder()
                    .auditStatus(data.getString("entryStatus"))
                    .merchantNo(data.getString("merchantNo"))
                    .orderNo(data.getString("orderNo"))
                    .build();
        } else {
            return PlatformMerchantRegisterSaveRespVO
                    .builder()
                    .msg(resultObj.getString("message"))
                    .auditStatus(MerchantRegisterStateEnum.OVERRULE.getState())
                    .build();
        }
    }

    @Override
    public PlatformMerchantUploadSaveRespVO uploadPic(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) {
        HlbMerchantRequestVO requestVO = new HlbMerchantRequestVO();
        requestVO.setMerchantNo(platformMerchantNo);
        requestVO.setInterfaceName("uploadImageUrl");
        requestVO.setEncryptionKey(publicEncryptKey);
        // 请求体
        HlbMerchantUploadPicBodyVO body = new HlbMerchantUploadPicBodyVO();
        body.setMerchantNo(uploadSaveReqVO.getAltMchNo());
        body.setOrderNo(uploadSaveReqVO.getOrderNo());
        body.setCredentialType(uploadSaveReqVO.getCredentialType().name());
        body.setCredentialUrl(uploadSaveReqVO.getCredentialUrl());
        requestVO.setBody(JSON.toJSONString(body));
        try {
            String resultMsg = reuqest(MERCHANT_URL02, requestVO, HlbMerchantRequestVO.NEED_SIGN_PARAMS, HlbMerchantRequestVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝商户资质上传结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("code"))) {
                JSONObject data = resultObj.getJSONObject("data");
                return PlatformMerchantUploadSaveRespVO
                        .builder()
                        .auditStatus(data.getString("status"))
                        .merchantNo(uploadSaveReqVO.getAltMchNo())
                        .orderNo(body.getOrderNo())
                        .build();
            } else {
                return PlatformMerchantUploadSaveRespVO
                        .builder()
                        .msg(resultObj.getString("message"))
                        .auditStatus(MerchantUploadPicStateEnum.FAIL.getState())
                        .build();
            }
        } catch (IllegalAccessException e) {
            log.error(" uploadPic异常,", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public PlatformMerchantUploadSaveRespVO changePic(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) {
        HlbMerchantRequestVO requestVO = new HlbMerchantRequestVO();
        requestVO.setMerchantNo(platformMerchantNo);
        requestVO.setInterfaceName("imageUrlAlteration");
        requestVO.setEncryptionKey(publicEncryptKey);
        // 请求体
        HlbMerchantUploadPicBodyVO body = new HlbMerchantUploadPicBodyVO();
        body.setMerchantNo(uploadSaveReqVO.getAltMchNo());
        body.setOrderNo(uploadSaveReqVO.getOrderNo());
        body.setCredentialType(uploadSaveReqVO.getCredentialType().name());
        body.setCredentialUrl(uploadSaveReqVO.getCredentialUrl());
        requestVO.setBody(JSON.toJSONString(body));
        try {
            String resultMsg = reuqest(MERCHANT_URL02, requestVO, HlbMerchantRequestVO.NEED_SIGN_PARAMS, HlbMerchantRequestVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝商户资质变更结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("code"))) {
                JSONObject data = resultObj.getJSONObject("data");
                return PlatformMerchantUploadSaveRespVO
                        .builder()
                        .auditStatus(data.getString("status"))
                        .merchantNo(uploadSaveReqVO.getAltMchNo())
                        .orderNo(body.getOrderNo())
                        .build();
            } else {
                return PlatformMerchantUploadSaveRespVO
                        .builder()
                        .msg(resultObj.getString("message"))
                        .auditStatus(MerchantUploadPicStateEnum.FAIL.getState())
                        .build();
            }
        } catch (IllegalAccessException e) {
            log.error(" changePic异常,", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public PlatformMerchantRegisterSaveRespVO updateMerchant(PlatformMerchantRegisterSaveReqVO saveReqVO, AccPlatformMerchant platformMerchant) {
        HlbMerchantRequestVO requestVO = new HlbMerchantRequestVO();
        requestVO.setMerchantNo(platformMerchantNo);
        requestVO.setInterfaceName("modifyMerchantInfoV2");
        requestVO.setEncryptionKey(publicEncryptKey);
        // 请求体
        HlbMerchantUpdateBodyVO body = new HlbMerchantUpdateBodyVO();
        // HLB + yyyyMMddHHmmss + 6 位随机数
        body.setOrderNo("HLBU" +
                DateUtil.formatDateTime(new Date())
                        .replace(StringPool.DASH, StringPool.EMPTY)
                        .replace(StringPool.COLON, StringPool.EMPTY)
                        .replace(StringPool.SPACE, StringPool.EMPTY)
                + RandomUtil.randomNumbers(6));
        body.setMerchantNo(platformMerchant.getAltMchNo());
        body.setShowName(saveReqVO.getAltMchName());//商户名称
        body.setLegalPerson(saveReqVO.getLegalPerson());//法人
        body.setLegalPersonID(saveReqVO.getIdCardNo());//法人身份证
        body.setBusinessLicense(saveReqVO.getLicenseNo());//营业执照
        body.setLinkman(saveReqVO.getBusiContactName());//联系人
        body.setLinkPhone(saveReqVO.getBusiContactMobileNo());//联系电话
        //body.setFileSigns(new HashMap<String, String>());

        if((ToolUtil.isNotEmpty(saveReqVO.getBankAccountName())
                || ToolUtil.isNotEmpty(saveReqVO.getBankAccountNo())
                || ToolUtil.isNotEmpty(saveReqVO.getSettleBankType())
                || ToolUtil.isNotEmpty(saveReqVO.getBankChannelNo()))
                && !saveReqVO.getBankAccountNo().equals(platformMerchant.getAccountNo())
        ){
            HlbMerchantUpdateBodyVO.UpdateSettleInfo updateSettleInfoParam = new HlbMerchantUpdateBodyVO.UpdateSettleInfo();
            updateSettleInfoParam.setAccountName(platformMerchant.getAccountName());
            updateSettleInfoParam.setAccountNo(platformMerchant.getAccountNo());
            updateSettleInfoParam.setSettleBankType(platformMerchant.getBankType());
            updateSettleInfoParam.setBankCode(platformMerchant.getBankChannelNo());
            if(ToolUtil.isNotEmpty(saveReqVO.getBankAccountName()) && !saveReqVO.getBankAccountName().equals(platformMerchant.getAccountName())){
                updateSettleInfoParam.setUpdateAccountName(saveReqVO.getBankAccountName());
            }
            if(ToolUtil.isNotEmpty(saveReqVO.getBankAccountNo()) && !saveReqVO.getBankAccountNo().equals(platformMerchant.getAccountNo())){
                updateSettleInfoParam.setUpdateAccountNo(saveReqVO.getBankAccountNo());
            }
            if(ToolUtil.isNotEmpty(saveReqVO.getSettleBankType())){
                String settleType = "0".equals(saveReqVO.getSettleBankType()) ? "TOPRIVATE" : "TOPUBLIC";
                if (!settleType.equals(platformMerchant.getBankType())) {
                    updateSettleInfoParam.setUpdateSettleBankType(settleType);
                }
            }
            if(ToolUtil.isNotEmpty(saveReqVO.getBankChannelNo()) && !saveReqVO.getBankChannelNo().equals(platformMerchant.getBankChannelNo())){
                updateSettleInfoParam.setUpdateBankCode(saveReqVO.getBankChannelNo());
            }
            body.setUpdateSettleInfo(updateSettleInfoParam);
        }
        requestVO.setBody(JSON.toJSONString(body));
        try {
            /** 文件类型要对应枚举*/
            Map<String,File> fileMap = new HashMap<>();
            fileMap.put(MerchantCredentialType.AUTHORIZATION_FOR_SETTLEMENT.name(), File.createTempFile("tmp",null));
            fileMap.put(MerchantCredentialType.ACCOUNT_OPENING_CERTIFICATE.name(), File.createTempFile("tmp",null));
            fileMap.put(MerchantCredentialType.SETTLE_FRONT_OF_ID_CARD.name(), File.createTempFile("tmp",null));
            fileMap.put(MerchantCredentialType.SETTLE_BACK_OF_ID_CARD.name(), File.createTempFile("tmp",null));
            fileMap.put(MerchantCredentialType.HANDHELD_OF_ID_CARD.name(), File.createTempFile("tmp",null));
            fileMap.put(MerchantCredentialType.HANDHELD_OF_BANK_CARD.name(), File.createTempFile("tmp",null));
            fileMap.put(MerchantCredentialType.SUBLEASE_CERTIFICATE.name(), File.createTempFile("tmp",null));
            fileMap.put(MerchantCredentialType.BANK_CARD.name(), File.createTempFile("tmp",null));
            fileMap.put(MerchantCredentialType.CHECKOUT_COUNTER.name(), File.createTempFile("tmp",null));

            String resultMsg = reuqest(MODIFY_MERCHANT_INFO_V2, requestVO, HlbMerchantRequestVO.NEED_SIGN_PARAMS, HlbMerchantRequestVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS, fileMap);
            log.info("合利宝商户信息变更结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("code"))) {
                JSONObject data = resultObj.getJSONObject("data");
                String alterationStatus = data.getString("alterationStatus");
                if ("WAIT".equals(alterationStatus)) {
                    alterationStatus = MerchantRegisterStateEnum.INIT.getState();
                }
                return PlatformMerchantRegisterSaveRespVO
                        .builder()
                        .auditStatus(alterationStatus)
                        .merchantNo(data.getString("merchantNo"))
                        .orderNo(body.getOrderNo())
                        .build();
            } else {
                return PlatformMerchantRegisterSaveRespVO
                        .builder()
                        .msg(resultObj.getString("message"))
                        .orderNo(body.getOrderNo())
                        .auditStatus(MerchantRegisterStateEnum.OVERRULE.getState())
                        .build();
            }
        } catch (Exception e) {
            log.error(" updateMerchant异常,", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public PlatformMerchantUploadSaveRespVO queryUploadPicStatus(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) {
        HlbMerchantRequestVO requestVO = new HlbMerchantRequestVO();
        requestVO.setMerchantNo(platformMerchantNo);
        requestVO.setInterfaceName("imageUrlQuery");
        requestVO.setEncryptionKey(publicEncryptKey);
        // 请求体
        HlbMerchantUploadPicBodyVO body = new HlbMerchantUploadPicBodyVO();
        body.setMerchantNo(uploadSaveReqVO.getAltMchNo());
        body.setOrderNo(uploadSaveReqVO.getOrderNo());
        body.setCredentialType(uploadSaveReqVO.getCredentialType().name());
        requestVO.setBody(JSON.toJSONString(body));
        try {
            String resultMsg = reuqest(MERCHANT_URL02, requestVO, HlbMerchantRequestVO.NEED_SIGN_PARAMS, HlbMerchantRequestVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝商户资质凭证上传进度查询 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("code"))) {
                JSONObject data = resultObj.getJSONObject("data");
                return PlatformMerchantUploadSaveRespVO
                        .builder()
                        .auditStatus(data.getString("status"))
                        .merchantNo(uploadSaveReqVO.getAltMchNo())
                        .orderNo(body.getOrderNo())
                        .build();
            } else {
                return PlatformMerchantUploadSaveRespVO
                        .builder()
                        .msg(resultObj.getString("message"))
                        .auditStatus(MerchantRegisterStateEnum.OVERRULE.getState())
                        .build();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public PlatformMerchantUpdateStatusRespVO queryUploadMerchantStatus(String merchantNo, String editOrderNo) {
        HlbMerchantRequestVO requestVO = new HlbMerchantRequestVO();
        requestVO.setMerchantNo(platformMerchantNo);
        requestVO.setInterfaceName("queryAlteration");
        requestVO.setEncryptionKey(publicEncryptKey);
        // 请求体
        requestVO.setBody(JSON.toJSONString(new HlbMerchantUpdateQueryBodyVO(editOrderNo, merchantNo)));
        try {
            String resultMsg = reuqest(MERCHANT_URL02, requestVO, HlbMerchantRequestVO.NEED_SIGN_PARAMS, HlbMerchantRequestVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝商户变更状态查询 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("code"))) {
                JSONObject data = resultObj.getJSONObject("data");
                String alterationStatus = data.getString("alterationStatus");
                if ("WAIT".equals(alterationStatus)) {
                    alterationStatus = MerchantRegisterStateEnum.INIT.getState();
                }
                if ("REFUSE".equals(alterationStatus)) {
                    alterationStatus = MerchantRegisterStateEnum.OVERRULE.getState();
                }
                return PlatformMerchantUpdateStatusRespVO
                        .builder()
                        .auditStatus(alterationStatus)
                        .merchantNo(merchantNo)
                        .orderNo(editOrderNo)
                        .msg(data.getString("remark"))
                        .build();
            } else {
                return PlatformMerchantUpdateStatusRespVO
                        .builder()
                        .msg(resultObj.getString("message"))
                        .auditStatus(MerchantRegisterStateEnum.OVERRULE.getState())
                        .build();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private String reuqest(
            String requestUrl,
            Object req,
            Set<String> needSignParams,
            Set<String> needEncryptParams
    ) throws IllegalAccessException {
        String resultMsg;
        Map<String, String> map = HlbCertUtil.convertBean(req, new LinkedHashMap<>());
        //建议是加签/验签的固定参数,具体按照文档要求加签.因为新增参数需要历史兼容是排除签名的
        HlbCertUtil.getSignAndEncryptedByReqMd5(map, needSignParams, needEncryptParams, publicEncryptKey, publicMerchantMd5SignKey);
        Map<String, Object> resultMap = HttpClientService.getHttpResp(map, requestUrl);
        resultMsg = (String) resultMap.get("response");
        if (map.containsKey("needDecrypt")) {
            JSONObject obj = JSON.parseObject(resultMsg);
            if (obj.containsKey("data")) {
                String data = HlbCertUtil.verificationAndDecrypt(
                        obj.getString("data"),
                        obj.getString("sign"),
                        publicEncryptKey,
                        publicMerchantMd5SignKey
                );
                obj.put("data", JSON.parseObject(data));
                return obj.toJSONString();
            }
        }
        return resultMsg;
    }

    private String reuqest(
            String requestUrl,
            Object req,
            Set<String> needSignParams,
            Set<String> needEncryptParams,
            Map<String, File> fileMap
    ) throws IllegalAccessException {
        String resultMsg;
        Map<String, String> map = HlbCertUtil.convertBean(req, new LinkedHashMap<>());
        //建议是加签/验签的固定参数,具体按照文档要求加签.因为新增参数需要历史兼容是排除签名的
        HlbCertUtil.getSignAndEncryptedByReqMd5(map, needSignParams, needEncryptParams, publicEncryptKey, publicMerchantMd5SignKey);
        Map<String, Object> resultMap = HttpClientService.getHttpResp(map, requestUrl, fileMap);
        resultMsg = (String) resultMap.get("response");
        if (map.containsKey("needDecrypt")) {
            JSONObject obj = JSON.parseObject(resultMsg);
            if (obj.containsKey("data")) {
                String data = HlbCertUtil.verificationAndDecrypt(
                        obj.getString("data"),
                        obj.getString("sign"),
                        publicEncryptKey,
                        publicMerchantMd5SignKey
                );
                obj.put("data", JSON.parseObject(data));
                return obj.toJSONString();
            }
        }
        return resultMsg;
    }
}
