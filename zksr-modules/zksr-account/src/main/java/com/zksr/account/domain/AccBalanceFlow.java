package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 门店账户余额流水表
 */
@TableName(value = "acc_balance_flow")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccBalanceFlow extends BaseEntity {

    private static final long serialVersionUID = 7766206524810540315L;

    /**
     * 余额流水id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long balanceFlowId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /**
     * 门店id
     */
    @Excel(name = "门店id")
    private Long branchId;

    /**
     * 门店账户余额id
     */
    @Excel(name = "门店id")
    private Long balanceId;

    /**
     * 操作类型：0-充值，1-订单支付，2-取消订单，3-后台退款
     */
    @Excel(name = "操作类型", readConverterExp = "0=充值,1=订单支付,2=取消订单,3=后台退款")
    private Integer actionType;

    /**
     * 关联单号
     */
    @Excel(name = "关联单号")
    private String referenceNo;

    /**
     * 关联单号
     */
    @Excel(name = "关联子单号")
    private String referenceSubNo;

    /**
     * 操作金额
     */
    @Excel(name = "操作金额")
    private BigDecimal opAmt;

    /**
     * 操作前金额
     */
    @Excel(name = "操作前金额")
    private BigDecimal beforeAmt;

    /**
     * 操作后金额
     */
    @Excel(name = "操作后金额")
    private BigDecimal afterAmt;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 删除标识，0否，1是
     */
    @Excel(name = "删除标识", readConverterExp = "0=否,1=是")
    private Integer deleteFlag;

}
