package com.zksr.account.client.hlb.vo;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableSet;
import com.zksr.common.core.enums.SignatureType;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    查询合利宝支付订单
 * @date 2024/3/7 18:19
 */
@Data
public class AppPayOrderQueryReqVO {

    private String P1_bizType = "AppPayQuery";
    private String P2_orderId;
    private String P3_customerNumber;

    /**
     * 签名类型(不参与签名)
     */
    private SignatureType signatureType = SignatureType.MD5;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_orderId", "P3_customerNumber");
}
