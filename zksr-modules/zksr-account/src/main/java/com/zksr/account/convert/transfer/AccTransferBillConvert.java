package com.zksr.account.convert.transfer;

import java.math.BigDecimal;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccTransferBill;
import com.zksr.account.controller.transfer.vo.AccTransferBillRespVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 交易对账单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-11-05
*/
@Mapper
public interface AccTransferBillConvert {

    AccTransferBillConvert INSTANCE = Mappers.getMapper(AccTransferBillConvert.class);

    AccTransferBillRespVO convert(AccTransferBill accTransferBill);

    AccTransferBill convert(AccTransferBillSaveReqVO accTransferBillSaveReq);

    PageResult<AccTransferBillRespVO> convertPage(PageResult<AccTransferBill> accTransferBillPage);
}