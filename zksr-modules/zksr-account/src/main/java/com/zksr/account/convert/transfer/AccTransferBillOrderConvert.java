package com.zksr.account.convert.transfer;

import com.zksr.account.api.transfer.dto.AccTransferBillOrderRespDTO;
import com.zksr.account.api.transfer.vo.AccTransferBillOrderExportPageVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderPageReqVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccTransferBillOrder;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderRespVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 交易对账单明细单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-11-05
*/
@Mapper
public interface AccTransferBillOrderConvert {

    AccTransferBillOrderConvert INSTANCE = Mappers.getMapper(AccTransferBillOrderConvert.class);

    AccTransferBillOrderRespVO convert(AccTransferBillOrder accTransferBillOrder);

    AccTransferBillOrder convert(AccTransferBillOrderSaveReqVO accTransferBillOrderSaveReq);

    PageResult<AccTransferBillOrderRespVO> convertPage(PageResult<AccTransferBillOrder> accTransferBillOrderPage);

    AccTransferBillOrderPageReqVO convertExportReq(AccTransferBillOrderExportPageVO reqVO);

    List<AccTransferBillOrderRespDTO> convertExportResp(List<AccTransferBillOrder> dto);
}