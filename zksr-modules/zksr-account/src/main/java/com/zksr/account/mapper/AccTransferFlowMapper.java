package com.zksr.account.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.database.query.QueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccTransferFlow;
import com.zksr.account.controller.flow.vo.AccTransferFlowPageReqVO;


/**
 * 支付平台转账流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
@Mapper
public interface AccTransferFlowMapper extends BaseMapperX<AccTransferFlow> {
    default PageResult<AccTransferFlow> selectPage(AccTransferFlowPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccTransferFlow>()
                    .eqIfPresent(AccTransferFlow::getTransferFlowId, reqVO.getTransferFlowId())
                    .eqIfPresent(AccTransferFlow::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccTransferFlow::getBusiType, reqVO.getBusiType())
                    .eqIfPresent(AccTransferFlow::getBusiId, reqVO.getBusiId())
                    .eqIfPresent(AccTransferFlow::getPlatform, reqVO.getPlatform())
                    .eqIfPresent(AccTransferFlow::getSourceMerchantType, reqVO.getSourceMerchantType())
                    .eqIfPresent(AccTransferFlow::getSourceMerchantId, reqVO.getSourceMerchantId())
                    .eqIfPresent(AccTransferFlow::getSourceAltMchNo, reqVO.getSourceAltMchNo())
                    .likeIfPresent(AccTransferFlow::getSourceAltMchName, reqVO.getSourceAltMchName())
                    .eqIfPresent(AccTransferFlow::getTargetMerchantType, reqVO.getTargetMerchantType())
                    .eqIfPresent(AccTransferFlow::getTargetMerchantId, reqVO.getTargetMerchantId())
                    .eqIfPresent(AccTransferFlow::getTargetAltMchNo, reqVO.getTargetAltMchNo())
                    .likeIfPresent(AccTransferFlow::getTargetAltMchName, reqVO.getTargetAltMchName())
                    .eqIfPresent(AccTransferFlow::getTransferNo, reqVO.getTransferNo())
                    .eqIfPresent(AccTransferFlow::getTransferAmt, reqVO.getTransferAmt())
                    .eqIfPresent(AccTransferFlow::getState, reqVO.getState())
                    .eqIfPresent(AccTransferFlow::getTransferType, reqVO.getTransferType())
                    .eqIfPresent(AccTransferFlow::getTransferRemark, reqVO.getTransferRemark())
                    .eqIfPresent(AccTransferFlow::getInitTime, reqVO.getInitTime())
                    .eqIfPresent(AccTransferFlow::getProcessingTime, reqVO.getProcessingTime())
                    .eqIfPresent(AccTransferFlow::getFinishTime, reqVO.getFinishTime())
                    .eqIfPresent(AccTransferFlow::getErrorReason, reqVO.getErrorReason())
                    .eqIfPresent(AccTransferFlow::getTransferDebitAmount, reqVO.getTransferDebitAmount())
                    .eqIfPresent(AccTransferFlow::getOutFlowNo, reqVO.getOutFlowNo())
                    .eqIfPresent(AccTransferFlow::getTransferFee, reqVO.getTransferFee())
                .orderByDesc(AccTransferFlow::getTransferFlowId));
    }

    default AccTransferFlow selectTransferFlowByTransferNo(String transferNo) {
        return selectOne(
                new LambdaQueryWrapperX<AccTransferFlow>().eq(AccTransferFlow::getTransferNo, transferNo)
        );
    }
}
