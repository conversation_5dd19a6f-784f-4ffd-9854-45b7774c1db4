package com.zksr.account.util;

import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zksr.common.core.domain.PayCallBack;
import com.zksr.common.core.utils.ToolUtil;

import static com.zksr.account.enums.ErrorCodeConstants.MIDEA_PAY_ATTACH_NULL;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/7 11:28
 */
public class MideaPayUtil {
    
    public static PayCallBack getMideaCallBack(Map<String, String> params,String body) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, String> parsedBody = objectMapper.readValue(body, Map.class);
            params.putAll(parsedBody);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if(!params.containsKey("attach")){
            throw exception(MIDEA_PAY_ATTACH_NULL);
        }
        String attach =  params.get("attach").toString();
        if (ToolUtil.isEmpty(attach)) {
            throw exception(MIDEA_PAY_ATTACH_NULL);
        }
        PayCallBack callBack = JSON.parseObject(attach, PayCallBack.class);
        return callBack;
    }
}
