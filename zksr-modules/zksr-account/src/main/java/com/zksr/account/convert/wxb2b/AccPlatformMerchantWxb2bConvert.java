package com.zksr.account.convert.wxb2b;

import com.zksr.account.api.platformMerchant.dto.PlatformMerchantWxb2bDTO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoRespVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bSaveReqVO;
import com.zksr.account.domain.AccPlatformMerchantWxb2b;
import com.zksr.common.core.web.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 支付平台商户, 微信B2B平台商户扩展信息 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-09-24
*/
@Mapper
public interface AccPlatformMerchantWxb2bConvert {

    AccPlatformMerchantWxb2bConvert INSTANCE = Mappers.getMapper(AccPlatformMerchantWxb2bConvert.class);

    AccPlatformMerchantWxb2bInfoRespVO convertInfoRespVO(AccPlatformMerchantWxb2b accPlatformMerchantWxb2b);

    AccPlatformMerchantWxb2b convertPO(AccPlatformMerchantWxb2bSaveReqVO saveReqVO);

    PlatformMerchantWxb2bDTO convertDTO(AccPlatformMerchantWxb2b platformMerchant);
}