package com.zksr.account.client.mideapay;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import com.zksr.account.client.mideapay.vo.request.*;
import com.zksr.account.client.mideapay.vo.response.*;
import com.zksr.account.constants.BizTrackingIdConstants;
import com.zksr.account.controller.notify.PayNotifyController;
import com.zksr.account.controller.notify.TransferNotifyController;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import com.zksr.common.core.constant.SysBasicConstants;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.system.api.model.dto.MideaPayConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.collections4.CollectionUtils;
import com.zksr.common.core.utils.StringUtils;
import org.apache.http.Consts;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.ContentType;
import org.apache.http.message.BasicNameValuePair;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @description: 美的付
 */
@Data
@Slf4j
public class MideaPaySdkClient {
    /**
     * 小程序
     */
    private String appid;

    /**
     * 美的付配置
     */
    private MideaPayConfig payConfig;

    private static final String MIDEA_PAY_SUCCESS_CODE = "1001";

    /**
     * 微信支付
     * 回调：{@link PayNotifyController#notifyOrder }
     * @param payOrderDTO
     * @return
     */
    public PayOrderRespDTO mideaPay(PayOrderDTO payOrderDTO) {
        PayOrderRespDTO orderRespDTO = new PayOrderRespDTO();
        //构造请求参数
        MideaPayWechatReqVO wechatReqVO = buildMideaPayWechatReq(payOrderDTO);
        //构造子单
        List<MideaPayWechatSubOrderReqVO> subOrderList = getMideaPayWechatSubOrder(payOrderDTO.getSettlements(),wechatReqVO);
        if (CollectionUtils.isNotEmpty(subOrderList)) {
            for (OrderSettlementDTO settlement : payOrderDTO.getSettlements()) {
                if (this.payConfig.getMerchantNo().equals(settlement.getAccountNo())) {
                    orderRespDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus())
                            .setMessage("入驻商商编和收单商编不能相同");
                    return orderRespDTO;
                }
            }
        }
        wechatReqVO.setAppId(this.appid);
        wechatReqVO.setNotifyUrl(String.format("%s/pay/notify/order/%s/%s/%s", this.payConfig.getNotifyUrl(), payOrderDTO.getSysCode(), payOrderDTO.getOrderType(), payOrderDTO.getAppid()));
        wechatReqVO.setPartner(this.payConfig.getMerchantNo());
        wechatReqVO.setSubOrders(JsonUtils.toJsonString(subOrderList));
        wechatReqVO.setSign(sign(wechatReqVO, this.payConfig.getSignUrl()));

        Date start = new Date();
        MideaPayWechatRespVO response = null;
        try {
            response = httpPost(this.payConfig.getPayUrl(), wechatReqVO, MideaPayWechatRespVO.class, this.payConfig.getCheckSignUrl(), false);
        } catch (Throwable e) {
            log.error(" {}调用美的付微信支付失败,", payOrderDTO.getOrderNo(), e);
            throw exception(PAY_MIDEA_PAY_FAIL,e.getMessage());
        } finally {
            String errorCode = response != null ? response.getResultCode() : BizLogUtil.UNDEFINED_ERR_CODE;
            String errorMsgTemp = PAY_MIDEA_PAY_FAIL.getMsg();
            String errorMessage = response != null ? response.getResultInfo() : SysBasicConstants.QUOTE;
            BizLogUtil.printBizLogWithParams(
                    BizTrackingIdConstants.MIDEA_PAY_WECHAT_PAY_BIZ_ID,
                    wechatReqVO,
                    errorCode,
                    errorMsgTemp,
                    errorMessage,
                    response,
                    start
            );
        }

        if (!MIDEA_PAY_SUCCESS_CODE.equals(response.getResultCode())) {
            orderRespDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus()).setMessage(response.getResultInfo());
        } else {
            JSONObject rawData = JsonUtils.toJavaClass(response.getWeixinUrl(),JSONObject.class);
            orderRespDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus())
                    .setSuccessTime(new Date())
                    .setOrderNo(payOrderDTO.getOrderNo())
                    .setRawData(rawData);
        }

        return orderRespDTO;
    }

    /**
     * 美的付退款
     * 回调：{@link PayNotifyController#notifyRefund }
     * @param reqDTO
     * @return
     */
    public PayRefundRespDTO mideaRefund(PayRefundOrderSubmitReqVO reqDTO) {
        PayRefundRespDTO refundRespDTO = new PayRefundRespDTO();
        MideaPayRefundReqVO refundReqVO = buildMideaPayRefundReq(reqDTO);
        refundReqVO.setNotifyUrl(
                StringUtils.format(this.payConfig.getNotifyUrl() + "/account/pay/notify/refund/{}/{}/{}/{}",
                        reqDTO.getSysCode(),
                        reqDTO.getOrderType(),
                        reqDTO.getAppid(),
                        PayChannelEnum.MIDEA_PAY.getCode()
                )
        );
        MideaPayRefundRespVO response = null;
        Date start = new Date();
        try {
            refundReqVO.setSign(sign(refundReqVO, this.payConfig.getSignUrl()));
            response = httpPost(this.payConfig.getPayUrl(), refundReqVO, MideaPayRefundRespVO.class, this.payConfig.getCheckSignUrl(), false);
        } catch (Throwable e) {
            log.error(" {}调用美的付退款失败,", reqDTO.getRefundNo(), e);
            throw exception(PAY_MIDEA_PAY_REFUND_FAIL,e.getMessage());
        } finally {
            String errorCode = response != null ? response.getResultCode() : BizLogUtil.UNDEFINED_ERR_CODE;
            String errorMsgTemp = PAY_MIDEA_PAY_REFUND_FAIL.getMsg();
            String errorMessage = response != null ? response.getResultInfo() : SysBasicConstants.QUOTE;
            BizLogUtil.printBizLogWithParams(
                    BizTrackingIdConstants.MIDEA_PAY_REFUND_BIZ_ID,
                    refundReqVO,
                    errorCode,
                    errorMsgTemp,
                    errorMessage,
                    response,
                    start
            );
        }

        if (!MIDEA_PAY_SUCCESS_CODE.equals(response.getResultCode())) {
            refundRespDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setRefundNo(reqDTO.getRefundNo())
                    .setOrderNo(reqDTO.getOrderNo())
                    .setErrorMsg(response.getResultInfo());
        } else {
            refundRespDTO.setStatus(PayRefundStatusEnum.PROCESSING.getStatus())
                    .setRefundNo(reqDTO.getRefundNo())
                    .setOrderNo(reqDTO.getOrderNo())
                    .setOutRefundNo(response.getRefundNo());
        }
        return refundRespDTO;
    }

    /**
     * 美的付提现
     * 回调：{@link TransferNotifyController#notifySettleTransfer }
     * @param reqVO
     * @return
     */
    public TransferSettleRespDTO mideaWithdraw(TransferSettleReqVO reqVO) {
        //构造提现参数
        MideaPayWithdrawReqVO withdrawReqVO = buildMideaPayWithdrawReq(reqVO);
        TransferSettleRespDTO transferSubmitRespDTO = new TransferSettleRespDTO();
        withdrawReqVO.setNotifyUrl(String.format("%s/transfer/notify/settle/%s/%s", this.payConfig.getNotifyUrl(), reqVO.getSysCode(), reqVO.getPlatform()));
//        withdrawReqVO.setPartner(this.payConfig.getMerchantNo());
        withdrawReqVO.setPayeeBankAcctNo(aesEncode(withdrawReqVO,reqVO.getBankAccountNo(),this.payConfig.getPartnerKey()));

        MideaPayWithdrawRespVO response = null;
        Date start = new Date();
        try {
            withdrawReqVO.setSign(sign(withdrawReqVO, this.payConfig.getSignUrl()));
            response = httpPost(this.payConfig.getPayUrl(), withdrawReqVO, MideaPayWithdrawRespVO.class, this.payConfig.getCheckSignUrl(), false);
        } catch (Throwable e) {
            log.error(" {}调用美的付提现失败,", reqVO.getWithdrawNo(), e);
            transferSubmitRespDTO.setStatus(TransferStatusEnum.FAIL.getStatus())
                    .setWithdrawNo(reqVO.getWithdrawNo())
                    .setMsg(e.getMessage());
            return transferSubmitRespDTO;
//            throw exception(PAY_MIDEA_PAY_WITHDRAW_FAIL,e.getMessage());
        } finally {
            String errorCode = response != null ? response.getResultCode() : BizLogUtil.UNDEFINED_ERR_CODE;
            String errorMsgTemp = PAY_MIDEA_PAY_WITHDRAW_FAIL.getMsg();
            String errorMessage = response != null ? response.getResultInfo() : SysBasicConstants.QUOTE;
            BizLogUtil.printBizLogWithParams(
                    BizTrackingIdConstants.MIDEA_PAY_WITHDRAW_BIZ_ID,
                    withdrawReqVO,
                    errorCode,
                    errorMsgTemp,
                    errorMessage,
                    response,
                    start
            );
        }

        if (!MIDEA_PAY_SUCCESS_CODE.equals(response.getResultCode())) {
            transferSubmitRespDTO.setStatus(TransferStatusEnum.FAIL.getStatus())
                    .setWithdrawNo(reqVO.getWithdrawNo())
                    .setMsg(response.getResultInfo());
        } else {
            transferSubmitRespDTO.setStatus(TransferStatusEnum.CREATED.getStatus())
                    .setWithdrawNo(reqVO.getWithdrawNo());
        }

        return transferSubmitRespDTO;
    }

    /**
     * 美的付转账
     * 回调：{@link TransferNotifyController#notifySubmitTransfer }
     * @param reqVO
     * @return
     */
    public TransferSubmitRespDTO mideaTransfer(TransferSubmitReqVO reqVO) {
        TransferSubmitRespDTO respDTO = new TransferSubmitRespDTO();
        MideaPayTransferReqVO transferReqVO = buildMideaPayTransferReq(reqVO);
        transferReqVO.setNotifyUrl(String.format("%s/transfer/notify/submit/%s/%s", this.payConfig.getNotifyUrl(), reqVO.getSysCode(), reqVO.getPlatform()));
        transferReqVO.setPartner(this.payConfig.getSubMerchantNo());//入驻商充值的商户

        MideaPayTransferRespVO response = null;
        Date start = new Date();
        try {
            transferReqVO.setSign(sign(transferReqVO, this.payConfig.getSignUrl()));
            response = httpPost(this.payConfig.getPayUrl(), transferReqVO, MideaPayTransferRespVO.class, this.payConfig.getCheckSignUrl(), false);
        } catch (Throwable e) {
            log.error(" {}调用美的付转账失败,", reqVO.getTransferNo(), e);
            respDTO.setStatus(TransferStatusEnum.FAIL.getStatus())
                    .setTransferNo(reqVO.getTransferNo())
                    .setMsg(e.getMessage());
            return respDTO;
//            throw exception(PAY_MIDEA_PAY_TRANSFER_FAIL,e.getMessage());
        } finally {
            String errorCode = response != null ? response.getResultCode() : BizLogUtil.UNDEFINED_ERR_CODE;
            String errorMsgTemp = PAY_MIDEA_PAY_TRANSFER_FAIL.getMsg();
            String errorMessage = response != null ? response.getResultInfo() : SysBasicConstants.QUOTE;
            BizLogUtil.printBizLogWithParams(
                    BizTrackingIdConstants.MIDEA_PAY_TRANSFER_BIZ_ID,
                    transferReqVO,
                    errorCode,
                    errorMsgTemp,
                    errorMessage,
                    response,
                    start
            );
        }

        if (!MIDEA_PAY_SUCCESS_CODE.equals(response.getResultCode())) {
            respDTO.setStatus(TransferStatusEnum.FAIL.getStatus())
                    .setTransferNo(reqVO.getTransferNo())
                    .setOutTradeNo(response.getTradeNo())
                    .setMsg(response.getResultInfo());
        } else {
            respDTO.setStatus(TransferStatusEnum.CREATED.getStatus())
                    .setTransferNo(reqVO.getTransferNo());
        }

        return respDTO;
    }

    private static MideaPayTransferReqVO buildMideaPayTransferReq(TransferSubmitReqVO reqVO) {
        MideaPayTransferReqVO transferReqVO = new MideaPayTransferReqVO();
        String uuidStr = UUID.randomUUID().toString().replace("-", "");
        transferReqVO.setReqSeqNo(uuidStr);
        transferReqVO.setInputCharset(Charsets.UTF_8.name());
        transferReqVO.setTerminalType(TerminalTypeEnum.MOBILE.getValue());
        transferReqVO.setSignType("MD5_RSA_TW");
        transferReqVO.setService("distribute_pay");
        transferReqVO.setVersion("3.0.0");
        transferReqVO.setOutTradeNo(reqVO.getTransferNo());
        transferReqVO.setOutTradeTime(LocalDateTime.now());
        transferReqVO.setTradeType(MideaPayTradeTypeEnum.BALANCE.getCode());
        String amount = convertYuanToFen(reqVO.getTransferAmt()).stripTrailingZeros().toPlainString();
        transferReqVO.setPayAmount(amount);
        transferReqVO.setCurrencyType("CNY");
        transferReqVO.setPayeeLoginName(reqVO.getTargetAltMchNo());
        transferReqVO.setPayeeName(reqVO.getTargetAltMchName());
//        transferReqVO.setPayeeType("B"); //美的付确认可以不传
        return transferReqVO;
    }

    private static MideaPayWithdrawReqVO buildMideaPayWithdrawReq(TransferSettleReqVO reqVO) {
        MideaPayWithdrawReqVO withdrawReqVO = new MideaPayWithdrawReqVO();
        String uuidStr = UUID.randomUUID().toString().replace("-", "");
        withdrawReqVO.setReqSeqNo(uuidStr);
        withdrawReqVO.setInputCharset(Charsets.UTF_8.name());
        withdrawReqVO.setTerminalType(TerminalTypeEnum.MOBILE.getValue());
        withdrawReqVO.setSignType("MD5_RSA_TW");
        withdrawReqVO.setService("distribute_pay");
        withdrawReqVO.setVersion("3.0.0");
        withdrawReqVO.setPartner(reqVO.getCustomerNo());
        withdrawReqVO.setOutTradeNo(reqVO.getWithdrawNo());
        withdrawReqVO.setOutTradeTime(LocalDateTime.now());
        withdrawReqVO.setTradeType(MideaPayTradeTypeEnum.CARD.getCode());
        String amount = convertYuanToFen(reqVO.getAmount()).stripTrailingZeros().toPlainString();
        withdrawReqVO.setPayAmount(amount);
        withdrawReqVO.setCurrencyType("CNY");
        withdrawReqVO.setPayeeBankAcctNo(reqVO.getBankAccountNo());//AES加密
        withdrawReqVO.setPayeeName(reqVO.getBankAccountName());
        withdrawReqVO.setPayeeAcctType(BankTypeEnum.TOPUBLIC.getCode().equals(reqVO.getBankType()) ? MideaPayTradeTypeEnum.PUBLIC_ACCT.getCode() : MideaPayTradeTypeEnum.BANK_CARD.getCode());
        return withdrawReqVO;
    }

    private static String aesEncode(MideaPayBaseReqVO params, String decodeValue, String partnerKey) {
        Map<String, Object> paramsMap = covert2MapIgnoreNull(params);
        StringBuilder sb = new StringBuilder();
        sb.append("out_trade_no=").append(params.getOutTradeNo()).append("&out_trade_time=")
                .append(paramsMap.get("out_trade_time")).append("&partner=").append(params.getPartner())
                .append("&req_seq_no=").append(params.getReqSeqNo()).append("&key=").append(partnerKey);
        String md5To16 = DigestUtils.getMD5String(sb.toString()).substring(8, 24).toLowerCase();
        byte[] bytes = DigestUtils.aesEncrypt(decodeValue.getBytes(), md5To16.getBytes());
        char[] chars = DigestUtils.encodeHex1(bytes);
        return new String(chars);
    }

    private static MideaPayRefundReqVO buildMideaPayRefundReq(PayRefundOrderSubmitReqVO reqDTO) {
        MideaPayRefundReqVO refundReqVO = new MideaPayRefundReqVO();
        String uuidStr = UUID.randomUUID().toString().replace("-", "");
        refundReqVO.setReqSeqNo(uuidStr);
        refundReqVO.setInputCharset(Charsets.UTF_8.name());
        refundReqVO.setTerminalType(TerminalTypeEnum.MOBILE.getValue());
        refundReqVO.setSignType("MD5_RSA_TW");
        refundReqVO.setService("trade_refund");
        refundReqVO.setVersion("3.1.0");
        refundReqVO.setOutRefundNo(reqDTO.getRefundNo());
        refundReqVO.setOutRefundTime(LocalDateTime.now());
        //refundReqVO.setOutTradeNo(StringUtils.isEmpty(reqDTO.getExtras().get(PayOrderSubmitExtras.SUPPLIER_ORDER_NO)) ? reqDTO.getOrderNo() : reqDTO.getExtras().get(PayOrderSubmitExtras.SUPPLIER_ORDER_NO));
        refundReqVO.setOutTradeNo(StringUtils.isEmpty(reqDTO.getExtras().get(PayOrderSubmitExtras.SUPPLIER_ORDER_NO)) ? String.valueOf(reqDTO.getFlowId()) : reqDTO.getExtras().get(PayOrderSubmitExtras.SUPPLIER_ORDER_NO));
        refundReqVO.setCurrencyType("CNY");
        String amount = convertYuanToFen(reqDTO.getRefundAmt()).stripTrailingZeros().toPlainString();
        refundReqVO.setRefundTotalAmount(amount);
        refundReqVO.setRefundAmount(amount);
        refundReqVO.setRefundMarketAmount(null);
        String ip = IpUtils.getIpAddr();
        MideaPayRiskReqVO risk = MideaPayRiskReqVO.builder().ip(ip).build();
        refundReqVO.setRiskParams(JsonUtils.toJsonString(risk));
        refundReqVO.setAttach(reqDTO.getOrderNo());//商户自定义的信息，回调通知的时候美的支付平台会原值返回给商户
        if(CollectionUtils.isNotEmpty(reqDTO.getSettlements())){
            refundReqVO.setPartner(reqDTO.getSettlements().get(0).getAccountNo());
        }

        return refundReqVO;
    }

    private static MideaPayWechatReqVO buildMideaPayWechatReq(PayOrderDTO payOrderDTO){
        MideaPayWechatReqVO parameters = new MideaPayWechatReqVO();
        String uuidStr = UUID.randomUUID().toString().replace("-", "");
        parameters.setReqSeqNo(uuidStr);
        parameters.setInputCharset(Charsets.UTF_8.name());
        parameters.setTerminalType(TerminalTypeEnum.MOBILE.getValue());
        parameters.setSignType("MD5_RSA_TW");//天威证书签名
        parameters.setService("batch_trade_pay_wechatpay");
        parameters.setVersion("3.6.0");
        parameters.setIsGuarantee("FALSE");//担保标记，确认该属性作用
        //parameters.setOutTradeNo(payOrderDTO.getOrderNo());//订单号
        parameters.setOutTradeNo(String.valueOf(payOrderDTO.getFlowId()));//订单号
        parameters.setOutTradeTime(LocalDateTime.now());
        parameters.setBarCode(MideaPayBarCodeEnum.MINI_PROGRAM.getValue());//支付类型
        parameters.setOpenId(payOrderDTO.getOpenid());
        parameters.setCurrencyType("CNY");//货币类型
        String amount = convertYuanToFen(payOrderDTO.getPayAmt()).stripTrailingZeros().toPlainString();
        parameters.setTotalOrderAmount(amount);
        parameters.setTotalAmount(amount);
        parameters.setTotalCount(Optional.ofNullable(payOrderDTO.getSettlements().size()).orElse(0));
        parameters.setWapUrl(null);//H5支付需要
        parameters.setWapName(null);
        parameters.setLimitAcctType(null);//支付卡限定,DC：借记卡；DOC：借记卡和信用卡（双卡）
        parameters.setPayExpireTime(null);//交易超时时间,不传默认30分钟
        parameters.setIsVirtualProduct("FALSE");
        parameters.setProductName("B2B快消品");
        parameters.setProductInfo("B2B快消品");
        String ip = IpUtils.getIpAddr();
        MideaPayRiskReqVO risk = MideaPayRiskReqVO.builder().ip(ip).build();
        parameters.setRiskParams(JsonUtils.toJsonString(risk));
        parameters.setAttach(payOrderDTO.getOrderNo());//商户自定义的信息，回调通知的时候美的支付平台会原值返回给商户

        return parameters;
    }

    private List<MideaPayWechatSubOrderReqVO> getMideaPayWechatSubOrder(List<OrderSettlementDTO> subList, MideaPayWechatReqVO wechatReqVO) {
        if(CollectionUtils.isEmpty(subList)){
            return null;
        }
        List<MideaPayWechatSubOrderReqVO> subOrderReqVOList = new ArrayList<>();
        int i = 0;
        for(OrderSettlementDTO sub : subList){
            MideaPayWechatSubOrderReqVO subOrder = MideaPayWechatSubOrderReqVO.builder()
                    .partner(sub.getAccountNo())
                    .subOutTradeNo(StringUtils.isEmpty(sub.getSubOrderNo()) ? String.format("%s-%s",wechatReqVO.getOutTradeNo(),++i) : sub.getSubOrderNo())
                    .payAmount(convertYuanToFen(sub.getAmt()).stripTrailingZeros().toPlainString())
                    .subProductName("B2B快消品")
                    .subProductInfo("B2B快消品")
                    .profitSharing("FALSE").build();
            subOrderReqVOList.add(subOrder);
        }

        return subOrderReqVOList;
    }

    private static BigDecimal convertYuanToFen(BigDecimal yuan) {
        return yuan == null ? BigDecimal.ZERO : yuan.multiply(BigDecimal.valueOf(100));
    }

    private static <T> Map<String, Object> covert2MapIgnoreNull(T obj) {
        Map<String, Object> map;
        try {
            if (obj instanceof Map) {
                map = (Map<String, Object>) obj;
            } else if (obj instanceof String) {
                map = JsonUtils.toJavaClass((String) obj, Map.class);
            } else {
                map = JsonUtils.convertObject(obj, Map.class);
            }
        } catch (Exception e) {
            log.error(" 格式化返回内容失败，{}",obj.toString(), e);
            throw new ServiceException(parseHtmlMsg(obj.toString()));
        }
        map.entrySet().removeIf(entry ->
                entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString()));
        return map;
    }

    private static String parseHtmlMsg(String input){
        if(StringUtils.isEmpty(input)){
            return input;
        }
        // 删除换行符、制表符
        String text = input.replaceAll("[\n\t]", "");
        Pattern pattern = Pattern.compile("<div class=\"err-msg\">(.*?)</div>");
        Matcher matcher = pattern.matcher(text);
        return matcher.find() ? matcher.group(1).trim() : "返回消息不符合格式";
    }

    private static <T> String sign(T t, String url) {
        Map<String, Object> map = covert2MapIgnoreNull(t);
        return sign(map, url);
    }

    private static <T> String sign(Map<String, T> map, String url) {
        List<NameValuePair> nameValuePairs = changeNameValuePairsByRemoveSign(map);
        String ciphertext = md5Digest(nameValuePairsFormatter(nameValuePairs));
        nameValuePairs.add(new BasicNameValuePair("source", ciphertext));
        SignRespVO response = null;
        Date start = new Date();
        try {
            response = httpPost(url, nameValuePairs);
        } catch (Exception e) {
            log.error(" {}调用美的付签名失败,", map.get("out_trade_no"), e);
            throw exception(PAY_MIDEA_PAY_SIGN_FAIL,e.getMessage());
        } finally {
            String errorCode = response != null ? response.getCode() : BizLogUtil.UNDEFINED_ERR_CODE;
            String errorMsgTemp = PAY_MIDEA_PAY_SIGN_FAIL.getMsg();
            String errorMessage = response != null ? response.getResult() : SysBasicConstants.QUOTE;
            BizLogUtil.printBizLogWithParams(
                    BizTrackingIdConstants.MIDEA_PAY_SIGN_BIZ_ID,
                    nameValuePairs,
                    errorCode,
                    errorMsgTemp,
                    errorMessage,
                    response,
                    start
            );
        }

        if ("1".equals(response.getCode())) {
            return response.getSign();
        } else {
            throw new ServiceException(String.format(" 支付签名失败,%s", response.getResult()));
        }

    }

    private static String nameValuePairsFormatter(List<NameValuePair> nameValuePairs) {
        return (String)nameValuePairs.stream().map(Objects::toString).collect(Collectors.joining("&"));
    }

    private static <T> List<NameValuePair> changeNameValuePairsByRemoveSign(Map<String, T> map) {
        TreeMap<String, T> treeMap = new TreeMap(map);
        treeMap.entrySet().removeIf((entry) -> {
            return entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString()) || "sign".equals(entry.getKey());
        });
        return (List)treeMap.entrySet().stream().map((entry) -> {
            return new BasicNameValuePair((String)entry.getKey(), entry.getValue().toString());
        }).collect(Collectors.toList());
    }

    private static <T> List<NameValuePair> changeNameValuePairs(Map<String, T> map) {
        Map<String, T> hashMap = new HashMap<>(map);
        hashMap.entrySet().removeIf(entry ->
                entry.getValue() == null || StringUtils.isBlank(entry.getValue().toString()));
        return hashMap.entrySet().stream().map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue().toString()))
                .collect(Collectors.toList());
    }

    private static String md5Digest(String plaintext) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(plaintext.getBytes(StandardCharsets.UTF_8));
            return Hex.encodeHexString(messageDigest.digest());
        } catch (Exception var2) {
            throw new RuntimeException(var2);
        }
    }

    private static SignRespVO httpPost(String url, List<NameValuePair> nameValuePairs) {
        String formatStr = URLEncodedUtils.format(nameValuePairs, Charsets.UTF_8);
        ContentType contentType = ContentType.create(ContentType.APPLICATION_FORM_URLENCODED.getMimeType(), Consts.UTF_8);
        ResultHandler resultHandler = HttpClientPool.post(url, formatStr, (Map)null, contentType);
        String result = resultHandler.getString();
        SignRespVO signResult = (SignRespVO)resultHandler.toJavaObject(SignRespVO.class);
        signResult.setResult(result);
        return signResult;
    }
    private static <T, R> T httpPost(String url, R params, Class<T> classType, String checkSignUrl, boolean isProxy) {
        Map<String, Object> paramsMap = covert2MapIgnoreNull(params);
        List<NameValuePair> nameValuePairs = changeNameValuePairs(paramsMap);
        ResultHandler resultHandler = httpPost(url, nameValuePairs, checkSignUrl, isProxy);
        return resultHandler.toJavaObject(classType);
    }

    private static ResultHandler httpPost(String url, List<NameValuePair> nameValuePairs, String checkSignUrl, boolean isProxy) {
        String formatStr = URLEncodedUtils.format(nameValuePairs, Charsets.UTF_8);
        ContentType contentType = ContentType.create(ContentType.APPLICATION_FORM_URLENCODED.getMimeType(), Consts.UTF_8);
        ResultHandler result;
        if (isProxy) {
            result = HttpClientPool.post(url, formatStr, null, contentType, null);
        } else {
            result = HttpClientPool.post(url, formatStr, null, contentType);
        }
        checkSign(result.getString(), checkSignUrl);
        return result;
    }

    public static <T> void checkSign(T t, String checkSignUrl) {
        Map<String, Object> map = covert2MapIgnoreNull(t);
        boolean checkSignFlag = checkSign(map, checkSignUrl);
        if (!checkSignFlag) {
            throw new ServiceException("验签失败");
        }
    }

    private static <T> boolean checkSign(Map<String, T> map, String checkSignUrl) {
        if (map.get("sign") == null) {
            throw new ServiceException("签名信息为空");
        }
        String sign = map.remove("sign").toString();
        List<NameValuePair> nameValuePairs = changeNameValuePairsByRemoveSign(map);
        String ciphertext = md5Digest(nameValuePairsFormatter(nameValuePairs));
        List<NameValuePair> params = Arrays.asList(new BasicNameValuePair("source", ciphertext),
                new BasicNameValuePair("sign", sign));
        try {
            SignRespVO signResult = httpPost(checkSignUrl, params);
            return "1".equals(signResult.getCode());
        } catch (Exception e) {
            log.error(" 请求验签失败,", e);
            throw new ServiceException(" 请求验签失败,");
        }
    }

    public PayPlatformAccountVO queryBalance(String merchantNo) {
        PayPlatformAccountVO respVO = new PayPlatformAccountVO();
        if (StringUtils.isEmpty(merchantNo)) {
            return respVO;
        }
        MideaPayBalanceQryReqVO reqVO = new MideaPayBalanceQryReqVO();
        String uuidStr = UUID.randomUUID().toString().replace("-", "");
        reqVO.setReqSeqNo(uuidStr);
        reqVO.setInputCharset(Charsets.UTF_8.name());
        reqVO.setTerminalType(TerminalTypeEnum.MOBILE.getValue());
        reqVO.setSignType("MD5_RSA_TW");//天威证书签名
        reqVO.setService("data_partner_actbal_query");
        reqVO.setVersion("3.0.1");
        reqVO.setQueryPartner(merchantNo);
        reqVO.setPartner(this.payConfig.getMerchantNo());
        reqVO.setSign(sign(reqVO, this.payConfig.getSignUrl()));

        MideaPayBalanceQryRespVO response = null;
        Date start = new Date();
        try {
            response = httpPost(this.payConfig.getPayUrl(), reqVO, MideaPayBalanceQryRespVO.class, this.payConfig.getCheckSignUrl(), false);
        } catch (Throwable e) {
            log.error(" {}调用美的付余额查询失败,", merchantNo, e);
            throw exception(PAY_MIDEA_PAY_QUERY_FAIL,e.getMessage());
        } finally {
            String errorCode = response != null ? response.getResultCode() : BizLogUtil.UNDEFINED_ERR_CODE;
            String errorMsgTemp = PAY_MIDEA_PAY_QUERY_FAIL.getMsg();
            String errorMessage = response != null ? response.getResultInfo() : SysBasicConstants.QUOTE;
            BizLogUtil.printBizLogWithParams(
                    BizTrackingIdConstants.MIDEA_PAY_QUERY_BIZ_ID,
                    reqVO,
                    errorCode,
                    errorMsgTemp,
                    errorMessage,
                    response,
                    start
            );
        }

        if (MIDEA_PAY_SUCCESS_CODE.equals(response.getResultCode())) {
            if(CollectionUtils.isNotEmpty(response.getMideaPayBalanceActInfo())){
                Optional<MideaPayBalanceQryRespVO.MideaPayBalanceActInfo> cashActInfo = response.getMideaPayBalanceActInfo().stream()
                        .filter(actInfo -> "CASH".equals(actInfo.getActType()))
                        .findFirst();
                if (cashActInfo.isPresent()) {
                    MideaPayBalanceQryRespVO.MideaPayBalanceActInfo cashAct = cashActInfo.get();
                    respVO.setBalance(new BigDecimal(cashAct.getActBal()).divide(new BigDecimal("100")));
                    respVO.setFrozenBalance(new BigDecimal(cashAct.getFrzBal()).divide(new BigDecimal("100")));
                    respVO.setSettleAmt(respVO.getBalance().subtract(respVO.getFrozenBalance()));
                }
                Optional<MideaPayBalanceQryRespVO.MideaPayBalanceActInfo> tradeActInfo = response.getMideaPayBalanceActInfo().stream()
                        .filter(actInfo -> "TRADE".equals(actInfo.getActType()))
                        .findFirst();
                if (tradeActInfo.isPresent()) {
                    MideaPayBalanceQryRespVO.MideaPayBalanceActInfo tradeAct = tradeActInfo.get();
                    BigDecimal tradeAmount = new BigDecimal(tradeAct.getActBal()).divide(new BigDecimal("100"));
                    respVO.setBalance(respVO.getBalance().add(tradeAmount));
                }
            }
        }

        return respVO;
    }


    /*
    public static void main(String[] args) {
        String s = "{\"appId\":\"wx41e339828fa06fd5\",\"timeStamp\":\"1719304572\",\"nonceStr\":\"41595682f9a04f49b8cf9fae9d3be990\",\"package\":\"prepay_id=wx2516361227052887e1e7fd75eb1da30000\",\"signType\":\"RSA\",\"paySign\":\"cvwiHLBAViU+47Uc6Z8Fa4aO/0gcr5jyHEId+/+ebQPPcYvRi54DWKXDUNe5ydT3N49G2UqJuSVr+l4FfCi4Cwzm57kZ5eDA1T4yBGgQVBQH7aqBKSRt1/SRav3awE/90atJPnvd10x+QbPaldiXzU68ir4wRtDh/OTYXec+hkrBio4wSfX51o0IjT2c+bFQfuHGnGP+/JTaz0wEIHwXmFtd6psXzdrv5/GP2Lhg38knfl2RjZYNoOh5aLqPsgneIVQhTRoAnsjQiAPdjqk4u+46ae90IL9+r0PBF7s9hAJEIfsjFtzzL/Y2ZJw5lto90gY+NhfJi9Z+NNdtlSIbvA==\"}";
        JSONObject rawData1 = JsonUtils.toJavaClass(s,JSONObject.class);
        System.out.println(rawData1);

    }

     */

}
