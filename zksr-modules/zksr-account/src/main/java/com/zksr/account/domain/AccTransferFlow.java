package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.business.TransferBusiType;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 支付平台转账流水对象 acc_transfer_flow
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
@TableName(value = "acc_transfer_flow")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccTransferFlow extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 转账流水id */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long transferFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /**
     * 业务类型
     * {@link TransferBusiType}
     */
    @Excel(name = "业务类型(数据字典)")
    private String busiType;

    /** 业务单据id */
    @Excel(name = "业务单据id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long busiId;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

    /** 转出方商户类型 */
    @Excel(name = "转出方商户类型")
    private String sourceMerchantType;

    /** 转出方商户id */
    @Excel(name = "转出方商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sourceMerchantId;

    /** 转出方分账方商户编号 */
    @Excel(name = "转出方分账方商户编号")
    private String sourceAltMchNo;

    /** 转出方分账方商户名 */
    @Excel(name = "转出方分账方商户名")
    private String sourceAltMchName;

    /** 转入方商户类型 */
    @Excel(name = "转入方商户类型")
    private String targetMerchantType;

    /** 转入方商户id */
    @Excel(name = "转入方商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long targetMerchantId;

    /** 转入方分账方商户编号 */
    @Excel(name = "转入方分账方商户编号")
    private String targetAltMchNo;

    /** 转入方分账方商户名 */
    @Excel(name = "转入方分账方商户名")
    private String targetAltMchName;

    /** 转账单号 */
    @Excel(name = "转账单号")
    private String transferNo;

    /** 转账金额 */
    @Excel(name = "转账金额")
    private BigDecimal transferAmt;

    /** 状态（数据字典）;提现状态 0-已提交 1-处理中 2-已完成 3-转账失败 */
    @Excel(name = "状态", readConverterExp = "数=据字典")
    private Integer state;

    /** 转账类型;1-平台转分帐方 2-分帐方转分帐方 3-分帐方转平台 */
    @Excel(name = "转账类型;1-平台转分帐方 2-分帐方转分帐方 3-分帐方转平台")
    private Integer transferType;

    /** 转账说明 */
    @Excel(name = "转账说明")
    private String transferRemark;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initTime;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date processingTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishTime;

    /** 失败原因 */
    @Excel(name = "失败原因")
    private String errorReason;

    /** 实际扣款金额 扣款金额=转账金额+手续费 */
    @Excel(name = "实际扣款金额 扣款金额=转账金额+手续费")
    private BigDecimal transferDebitAmount;

    /** 支付平台流水号;一般回调会返回 */
    @Excel(name = "支付平台流水号;一般回调会返回")
    private String outFlowNo;

    /** 转账手续费 */
    @Excel(name = "转账手续费")
    private BigDecimal transferFee;

}
