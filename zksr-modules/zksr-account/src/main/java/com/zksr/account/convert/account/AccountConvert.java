package com.zksr.account.convert.account;

import com.zksr.account.api.account.vo.AccAccountRespVO;
import com.zksr.account.api.account.vo.ColonelAccountInfoVO;
import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.controller.account.vo.AccAccountPageReqVO;
import com.zksr.account.controller.account.vo.SupplierAccountRespVO;
import com.zksr.account.controller.withdraw.vo.AccDcWithdrawRespVO;
import com.zksr.account.controller.withdraw.vo.AccOtherWithdrawRespVO;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.partnerPolicy.dto.WithdrawalSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/10
 * @desc
 */
@Mapper
public interface AccountConvert {

    AccountConvert INSTANCE = Mappers.getMapper(AccountConvert.class);

    List<AccAccountDTO> convert(List<AccAccount> accAccounts);

    AccAccountDTO convert(AccAccount accAccount);

    AccAccountRespVO convertRespVO(AccAccount accAccount);

    @Mappings({
            @Mapping(source = "dc.dcName", target = "dcName")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget AccDcWithdrawRespVO item, DcDTO dc);

    @Mappings({
            @Mapping(source = "supplier.supplierName", target = "merchantName")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget AccOtherWithdrawRespVO item, SupplierDTO supplier);

    @Mappings({
            @Mapping(source = "colonel.colonelName", target = "merchantName")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget AccOtherWithdrawRespVO item, ColonelDTO colonel);

    @Mappings({
            @Mapping(source = "branch.branchName", target = "merchantName")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget AccOtherWithdrawRespVO item, BranchDTO branch);

    List<AccAccountRespVO> convertAccountRespListVO(ArrayList<AccAccount> list);

    SupplierAccountRespVO convertSupplierBalance(PayPlatformAccountVO supplierBalance);


    @Mappings({
            @Mapping(source = "platformMerchant.bankBranch", target = "bankBranch"),
            @Mapping(source = "platformMerchant.accountNo", target = "accountNo"),
            @Mapping(source = "platformMerchant.altMchName", target = "accountName"),
            @Mapping(source = "supplier.minSettleAmt", target = "minSettleAmt"),
    })
    @BeanMapping(ignoreByDefault = true)
    void setterSupplierBalance(@MappingTarget SupplierAccountRespVO respVO, AccPlatformMerchant platformMerchant, SupplierDTO supplier);

    @Mappings({
            @Mapping(source = "withdrawalSetting.withdrawalFeeRate", target = "withdrawalFeeRate"),
            @Mapping(source = "withdrawalSetting.minimumWithdrawalAmount", target = "minimumWithdrawalAmount"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buildWithdrawSetting(@MappingTarget AccAccountRespVO accountRespVO, WithdrawalSettingPolicyDTO withdrawalSetting);

    @Mappings({
            @Mapping(source = "withdrawalSetting.withdrawalFeeRate", target = "withdrawalFeeRate"),
            @Mapping(source = "withdrawalSetting.minimumWithdrawalAmount", target = "minimumWithdrawalAmount"),
    })
    AccAccountRespVO build(AccAccount accAccount, WithdrawalSettingPolicyDTO withdrawalSetting);


    @Mappings({
            @Mapping(source = "accAccount.accountId", target = "accountId"),
            @Mapping(source = "accAccount.sysCode", target = "sysCode"),
            @Mapping(source = "accAccount.accountType", target = "accountType"),
            @Mapping(source = "accAccount.merchantType", target = "merchantType"),
            @Mapping(source = "accAccount.merchantId", target = "merchantId"),
            @Mapping(source = "accAccount.withdrawableAmt", target = "withdrawableAmt"),
            @Mapping(source = "accAccount.frozenAmt", target = "frozenAmt"),
            @Mapping(source = "accAccount.creditAmt", target = "creditAmt"),
            @Mapping(source = "accAccount.holdMerchantType", target = "holdMerchantType"),
            @Mapping(source = "accAccount.holdMerchantId", target = "holdMerchantId"),
            @Mapping(source = "accAccount.platform", target = "platform"),
            @Mapping(source = "withdrawalSetting.withdrawalFeeRate", target = "withdrawalFeeRate"),
            @Mapping(source = "withdrawalSetting.minimumWithdrawalAmount", target = "minimumWithdrawalAmount"),
            @Mapping(source = "platformMerchant.bankBranch", target = "bankBranch"),
            @Mapping(source = "platformMerchant.accountNo", target = "accountNo"),
            @Mapping(source = "platformMerchant.altMchName", target = "accountName"),
    })
    ColonelAccountInfoVO buildColonelAccount(AccAccount accAccount, BigDecimal totalWithdrawAmt, AccPlatformMerchant platformMerchant, WithdrawalSettingPolicyDTO withdrawalSetting);

    AccAccountPageReqVO convertPageReqVO(AccAccountDTO accountDTO);
}
