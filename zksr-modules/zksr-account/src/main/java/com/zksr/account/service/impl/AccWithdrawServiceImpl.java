package com.zksr.account.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.controller.withdraw.vo.AccDirectSettleWithdrawSaveReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawAuditVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawRespVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawSaveReqVO;
import com.zksr.account.convert.withdraw.AccWithdrawConvert;
import com.zksr.account.domain.*;
import com.zksr.account.mapper.AccWithdrawMapper;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.account.service.*;
import com.zksr.account.service.metchant.MerchantService;
import com.zksr.account.service.transfer.TransferAccountService;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.business.TransferBusiType;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.PayConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ErrorCode;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.WithdrawUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.partnerConfig.dto.PayAccountConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.WithdrawalSettingPolicyDTO;
import com.zksr.system.api.sysconfig.GlobalPayConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 账户提现单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Service
@Slf4j
@SuppressWarnings(StringPool.ALL)
public class AccWithdrawServiceImpl implements IAccWithdrawService {

    @Autowired
    private AccWithdrawMapper accWithdrawMapper;

    @Autowired
    private IAccAccountService accountService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccAccountFlowService accountFlowService;

    @Autowired
    private TransferAccountService transferAccountService;

    @Autowired
    private MerchantService merchantService;
    /**
     * 新增账户提现单, 不适用与入驻商提现
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public AccWithdraw insertAccWithdraw(AccWithdrawSaveReqVO createReqVO) {
        // 插入
        AccWithdraw accWithdraw = new AccWithdraw();
        // 验证提现参数
        AccAccount account = validateAccount(createReqVO, accWithdraw);
        if (Objects.nonNull(createReqVO.getMerchantId()) && !createReqVO.getMerchantId().equals(account.getMerchantId())) {
            throw exception(NOT_EXIST_ACCOUNT);
        }
        // 平台商ID
        Long sysCode = Objects.nonNull(SecurityContextHolder.hasSysCode()) ? null : account.getSysCode();
        // 平台商
        AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(account.getMerchantId(), account.getMerchantType(), account.getPlatform(), account.getSysCode());
        if (PayChannelEnum.MOCK.getCode().equals(account.getPlatform())) {
            // 兼容模拟测试
            platformMerchant = AccPlatformMerchant.getMockAccount();
        }
        if (Objects.isNull(platformMerchant)) {
            throw exception(NOT_EXIST_MERCHANT_ACCOUNT_CONFIG);
        }
        //accWithdraw.
        String uniqueNumber = WithdrawUtil.getWithdrawNo(account.getMerchantType(), redisService.getUniqueNumber(CacheConstants.WITHDRAW_SEQUENCE));;
        // 填充提现参数
        accWithdraw.setMerchantType(account.getMerchantType())
                    .setSysCode(sysCode)
                    .setAccountId(account.getAccountId())
                    .setMerchantId(account.getMerchantId())
                    .setPlatform(account.getPlatform())
                    .setTransferType(StringPool.TWO)
                    .setApplyAmt(createReqVO.getWithdrawAmt())
                    .setState(AccountTransferStateEnum.INIT.getState())
                    .setTransferNo(uniqueNumber)
                    .setFee(BigDecimal.ZERO)
                    .setBankAccountNo(platformMerchant.getAccountNo())
                    .setBankName(platformMerchant.getBankName() + platformMerchant.getBankBranch())
                    ;
        // 手续费计算
        WithdrawalSettingPolicyDTO withdrawalSetting = accountCacheService.getWithdrawalSetting(account.getSysCode());
        PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(account.getSysCode());
        if (Objects.isNull(payConfigDTO)) {
            throw exception(NOT_EXIST_PAY_CONFIG);
        }
        GlobalPayConfigDTO globalPayConfig = redisSysConfigService.getGlobalPayConfig(payConfigDTO.getInteriorStoredPayPlatform());
        if (Objects.isNull(globalPayConfig)) {
            throw exception(GLOBAL_PAY_CONFIG_NOT_EXIST);
        }
        // 平台收取手续费比例
        BigDecimal freeRate = BigDecimal.ZERO;
        if (Objects.nonNull(withdrawalSetting) && StringUtils.isNotEmpty(withdrawalSetting.getWithdrawalFeeRate())) {
            freeRate = new BigDecimal(withdrawalSetting.getWithdrawalFeeRate()).divide(new BigDecimal("100"));
        }
        // 总手续费
        BigDecimal totalFree = freeRate.multiply(accWithdraw.getApplyAmt()).setScale(2, RoundingMode.HALF_UP);;
        // 预计到账金额
        BigDecimal arrivalAmt = accWithdraw.getApplyAmt().subtract(totalFree);
        // 计算实际手续费
        BigDecimal free = BigDecimal.ZERO;
        if (PayConstants.TO_PUBLIC.equals(platformMerchant.getBankType())) {
            // 对公
            free = globalPayConfig.getWithdraw().getPublicMuch();
        } else {
            // 对私
            free = globalPayConfig.getWithdraw().getPrivateMuch();
        }
        // 需要转账金额
        BigDecimal transferAmt = arrivalAmt.add(free);
        // 保存计算值
        accWithdraw.setFee(free)
                .setOperFee(totalFree)
                .setPartnerProfit(totalFree.subtract(free))
                .setWithdrawAmt(arrivalAmt)
                .setTransferAmt(transferAmt)
        ;
        accWithdrawMapper.insert(accWithdraw);
        // 冻结提现金额
        AccAccountFlow accountFlow = new AccAccountFlow();
        accountFlow.setAccountId(account.getAccountId())
                    .setSysCode(accWithdraw.getSysCode())
                    .setBusiFrozenAmt(accWithdraw.getApplyAmt())
                    .setBusiType(AccountBusiType.WITHDRAW_FROZEN.getType())
                    .setBusiId(accWithdraw.getWithdrawId())
                    .setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField())
                    .setMerchantId(account.getMerchantId())
                    .setMerchantType(account.getMerchantType())
                    .setPlatform(account.getPlatform())
                    ;
        accountFlowService.insertAccAccountFlow(accountFlow);
        // 执行冻结
        accountFlowService.processFlow(accountFlow);
        // 返回
        return accWithdraw;
    }

    /**
     * 新增钱包余额分润提现, 适用于通过钱包分账余额方提现
     * @param createReqVO 创建信息
     * @return
     */
    @Override
    public AccWithdraw insertWalletWithdraw(AccWithdrawSaveReqVO createReqVO) {
        // 插入
        AccWithdraw accWithdraw = new AccWithdraw();
        // 验证提现参数
        AccAccount account = accountService.getAccAccount(createReqVO.getAccountId());
        // 验证账户是否存在
        if (Objects.isNull(account) || !account.getMerchantType().equals(createReqVO.getMerchantType())) {
            throw exception(NOT_EXIST_ACCOUNT);
        }
        // 余额验证
        if (NumberUtil.isGreater(createReqVO.getWithdrawAmt(), account.getValidAccountAmt())) {
            throw exception(BALANCE_ERR);
        }
        // 非模拟支付账户
        PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(account.getSysCode());
        // 平台商ID
        Long sysCode = account.getSysCode();
        // 默认获取门店充值支付平台的商户新, 后期可能会采用审核时决定从那个支付平台付款, 但是现在默认就是一个
        // 验证业务员有没有进件
        AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(account.getMerchantId(), account.getMerchantType(), payConfigDTO.getWalletPayPlatform(), account.getSysCode());
        if (Objects.isNull(platformMerchant)) {
            throw exception(NOT_EXIST_MERCHANT_ACCOUNT_CONFIG);
        }
        // 业务员所在区域只允许一个运营商
        ColonelDTO colonelDTO = accountCacheService.getColonelDTO(account.getMerchantId());
        AreaDTO areaDTO = accountCacheService.getAreaDTO(colonelDTO.getAreaId());
        // 验证默认运营商出账方
        AccPlatformMerchant dcPlatformMerchant = platformMerchantService.getPlatformMerchant(areaDTO.getDcId(), MerchantTypeEnum.DC.getType(), payConfigDTO.getWalletPayPlatform());
        if (Objects.isNull(dcPlatformMerchant)) {
            throw exception(WITHDRAW_DC_NOT_REGISTER);
        }
        //accWithdraw.
        String uniqueNumber = WithdrawUtil.getWithdrawNo(account.getMerchantType(), redisService.getUniqueNumber(CacheConstants.WITHDRAW_SEQUENCE));;
        // 填充提现参数
        accWithdraw.setMerchantType(account.getMerchantType())
                .setSysCode(sysCode)
                .setBsiMchNo(dcPlatformMerchant.getAltMchNo())
                .setAccountId(account.getAccountId())
                .setMerchantId(account.getMerchantId())
                .setPlatform(account.getPlatform())
                .setTransferType(StringPool.THREE)
                .setApplyAmt(createReqVO.getWithdrawAmt())
                .setState(AccountTransferStateEnum.INIT.getState())
                .setTransferNo(uniqueNumber)
                .setFee(BigDecimal.ZERO)
                .setBankAccountNo(platformMerchant.getAccountNo())
                .setPlatform(dcPlatformMerchant.getPlatform())
                .setAltMchNo(platformMerchant.getAltMchNo())
                .setAltMchName(platformMerchant.getAltMchName())
                .setBankName(platformMerchant.getBankName() + platformMerchant.getBankBranch())
        ;
        // 手续费计算
        WithdrawalSettingPolicyDTO withdrawalSetting = accountCacheService.getWithdrawalSetting(account.getSysCode());
        if (Objects.isNull(payConfigDTO)) {
            throw exception(NOT_EXIST_PAY_CONFIG);
        }
        GlobalPayConfigDTO globalPayConfig = redisSysConfigService.getGlobalPayConfig(payConfigDTO.getInteriorStoredPayPlatform());
        if (Objects.isNull(globalPayConfig)) {
            throw exception(GLOBAL_PAY_CONFIG_NOT_EXIST);
        }
        // 平台收取手续费比例
        BigDecimal freeRate = BigDecimal.ZERO;
        if (Objects.nonNull(withdrawalSetting) && StringUtils.isNotEmpty(withdrawalSetting.getWithdrawalFeeRate())) {
            freeRate = new BigDecimal(withdrawalSetting.getWithdrawalFeeRate()).divide(new BigDecimal("100"));
        }
        // 总手续费
        BigDecimal totalFree = freeRate.multiply(accWithdraw.getApplyAmt()).setScale(2, RoundingMode.HALF_UP);
        // 预计到账金额
        BigDecimal arrivalAmt = accWithdraw.getApplyAmt().subtract(totalFree);
        // 计算实际手续费
        BigDecimal free = BigDecimal.ZERO;
        if (PayConstants.TO_PUBLIC.equals(platformMerchant.getBankType())) {
            // 对公
            free = globalPayConfig.getWithdraw().getPublicMuch();
        } else {
            // 对私
            free = globalPayConfig.getWithdraw().getPrivateMuch();
        }
        // 需要转账金额
        BigDecimal transferAmt = arrivalAmt.add(free);
        // 保存计算值
        accWithdraw.setFee(free)
                .setOperFee(totalFree)
                .setPartnerProfit(totalFree.subtract(free))
                .setWithdrawAmt(arrivalAmt)
                .setTransferAmt(transferAmt)
        ;
        accWithdrawMapper.insert(accWithdraw);
        // 冻结提现金额
        AccAccountFlow accountFlow = new AccAccountFlow();
        accountFlow.setAccountId(account.getAccountId())
                .setSysCode(accWithdraw.getSysCode())
                .setBusiFrozenAmt(accWithdraw.getApplyAmt())
                .setBusiType(AccountBusiType.WITHDRAW_FROZEN.getType())
                .setBusiId(accWithdraw.getWithdrawId())
                .setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField())
                .setMerchantId(account.getMerchantId())
                .setMerchantType(account.getMerchantType())
                .setPlatform(account.getPlatform())
        ;
        accountFlowService.insertAccAccountFlow(accountFlow);
        // 执行冻结
        accountFlowService.processFlow(accountFlow);
        // 返回
        return accWithdraw;
    }

    /**
     * 新增入驻商提现单, 入驻商平台提现是直接结算的, 不用转账
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public AccWithdraw insertDirectWithdraw(AccDirectSettleWithdrawSaveReqVO createReqVO) {
        PayConfigDTO payConfig = accountCacheService.getPayConfigDTO(createReqVO.getSysCode());
        if (Objects.isNull(payConfig)){
            throw exception(NOT_EXIST_PAY_CONFIG);
        }
        AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(createReqVO.getMerchantId(), createReqVO.getMerchantType().getType(), createReqVO.getPlatform().getCode());
        // 入驻商
        if (PayChannelEnum.MOCK.getCode().equals(createReqVO.getPlatform())) {
            // 兼容模拟测试
            platformMerchant = AccPlatformMerchant.getMockAccount();
        }
        if (Objects.isNull(platformMerchant)) {
            throw exception(NOT_EXIST_MERCHANT_ACCOUNT_CONFIG);
        }
        PayPlatformAccountVO balance = merchantService.getBalance(platformMerchant.getAltMchNo(), platformMerchant.getSysCode(), createReqVO.getPlatform());
        if (Objects.isNull(balance)) {
            throw exception(NOT_EXIST_ACCOUNT);
        }
        // 有效可提现余额
        BigDecimal validAmt = balance.getSettleAmt().subtract(createReqVO.getMinSettleAmt());
        if (NumberUtil.isGreater(createReqVO.getWithdrawAmt(), validAmt)) {
            throw exception(WITHDRAW_BALANCE_ERR);
        }
        // 插入
        AccWithdraw accWithdraw = new AccWithdraw();
        //accWithdraw.
        String uniqueNumber = WithdrawUtil.getWithdrawNo(createReqVO.getMerchantType(), redisService.getUniqueNumber(CacheConstants.WITHDRAW_SEQUENCE));
        // 填充提现参数
        accWithdraw.setMerchantType(createReqVO.getMerchantType().getType())
                    .setMerchantId(createReqVO.getMerchantId())
                    .setBsiMchNo(platformMerchant.getAltMchNo())
                    .setAltMchNo(platformMerchant.getAltMchNo())
                    .setAltMchName(platformMerchant.getAltMchName())
                    .setBankName(platformMerchant.getBankName())
                    .setBankAccountNo(platformMerchant.getAccountNo())
                    // accountId < 0, 就不会去操作账户流水
                    .setAccountId(NumberPool.LOWER_GROUND_LONG)
                    .setPlatform(platformMerchant.getPlatform())
                    .setTransferType(StringPool.TWO)
                    .setApplyAmt(createReqVO.getWithdrawAmt())
                    .setTransferAmt(createReqVO.getWithdrawAmt())
                    .setWithdrawAmt(createReqVO.getWithdrawAmt())
                    .setState(AccountTransferStateEnum.INIT.getState())
                    .setTransferNo(uniqueNumber)
                    .setFee(BigDecimal.ZERO)
                    .setBankAccountNo(platformMerchant.getAccountNo())
                    .setPlatform(payConfig.getInteriorStoredPayPlatform())
                    .setBankName(platformMerchant.getBankName() + platformMerchant.getBankBranch())
                    ;
        // 不用计算手续费, 回调以后会计算
        accWithdrawMapper.insert(accWithdraw);
        // 返回
        return accWithdraw;
    }

    /**
     * 获得账户提现单
     *
     * @param withdrawId 账户提现单
     * @return 账户提现单
     */
    @Override
    public AccWithdraw getAccWithdraw(Long withdrawId) {
        return accWithdrawMapper.selectById(withdrawId);
    }

    @Override
    public AccWithdraw getAccWithdraw(String transferNo) {
        return accWithdrawMapper.selectByTransferNo(transferNo);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccWithdrawRespVO> getAccWithdrawPage(AccWithdrawPageReqVO pageReqVO) {
        // 运营商数据隔离
        pageReqVO.setDcId(SecurityUtils.getDcId());
        Page<AccWithdrawRespVO> page = PageUtils.startPage(pageReqVO);
        List<AccWithdrawRespVO> accWithdrawList = accWithdrawMapper.selectPageExt(pageReqVO);
        return PageResult.result(page, accWithdrawList);
    }

    /**
     * 处理转账单
     * @param withdraw
     */
    @Override
    public TransferSubmitRespDTO processWithdraw(AccWithdraw withdraw) {
        // 提现
        TransferSubmitReqVO reqVO = new TransferSubmitReqVO();
        reqVO.setTransferNo(withdraw.getTransferNo())
             .setPlatform(withdraw.getPlatform())
             .setSysCode(withdraw.getSysCode())
             .setTransferAmt(withdraw.getApplyAmt())
             .setBusiType(TransferBusiType.WITHDRAW.name())
             .setBusiId(withdraw.getWithdrawId())
             .setSourceMerchantType(MerchantTypeEnum.PARTNER.getType())
             .setSourceMerchantId(withdraw.getSysCode())
             .setSourceAltMchNo(withdraw.getBsiMchNo())
             .setTargetMerchantType(withdraw.getMerchantType())
             .setTargetAltMchNo(withdraw.getAltMchNo())
             .setTargetAltMchName(withdraw.getAltMchName())
             .setTransferRemark("活动资金")
             ;
        return transferAccountService.transferSubmit(reqVO);
    }

    @Override
    public TransferSubmitRespDTO processWalletWithdraw(AccWithdraw withdraw) {
        // 获取账户余额
        AccAccount account = accountService.getAccAccount(withdraw.getAccountId());
        if (account.payChannel() != PayChannelEnum.WALLET) {
            // 如果不是钱包余额的, 还是走正常的平台商转账, 入驻商分润模式
            return processWithdraw(withdraw);
        }
        // 业务员所在区域只允许一个运营商
        ColonelDTO colonelDTO = accountCacheService.getColonelDTO(account.getMerchantId());
        AreaDTO areaDTO = accountCacheService.getAreaDTO(colonelDTO.getAreaId());
        // 提现
        TransferSubmitReqVO reqVO = new TransferSubmitReqVO();
        reqVO.setTransferNo(withdraw.getTransferNo())
                .setPlatform(withdraw.getPlatform())
                .setSysCode(withdraw.getSysCode())
                .setTransferAmt(withdraw.getApplyAmt())
                .setBusiType(TransferBusiType.WITHDRAW.name())
                .setBusiId(withdraw.getWithdrawId())
                .setSourceMerchantType(MerchantTypeEnum.DC.getType())
                .setSourceMerchantId(areaDTO.getDcId())
                .setSourceAltMchNo(withdraw.getBsiMchNo())
                .setTargetMerchantType(withdraw.getMerchantType())
                .setTargetAltMchNo(withdraw.getAltMchNo())
                .setTargetAltMchName(withdraw.getAltMchName())
                .setTransferRemark("活动资金")
                .setUsage(PayTypeEnum.PAY.getUsage())
        ;
        return transferAccountService.transferSubmit(reqVO);
    }

    /**
     * 提现   -   转账发起成功
     * @param transferNo    提现单号
     */
    @Override
    @Transactional
    public void processWithdrawTransferProcessing(String transferNo) {
        AccWithdraw withdraw = accWithdrawMapper.selectByTransferNo(transferNo);
        AccWithdraw update = new AccWithdraw();
        update.setState(WithdrawStateEnum.PROCESSING.getState())
                .setWithdrawId(withdraw.getWithdrawId())
                .setTransferState(WithdrawStateEnum.TRANSFER_PROCESSING.getState())
                .setTransferInitTime(DateUtil.date())
                ;
        accWithdrawMapper.updateById(update);
    }

    /**
     * 提现   -   转账失败
     * @param transferNo    提现单号
     */
    @Override
    @Transactional
    public void processWithdrawTransferFail(String transferNo, String failMessage) {
        // 修改提现单状态
        AccWithdraw withdraw = accWithdrawMapper.selectByTransferNo(transferNo);
        AccWithdraw update = new AccWithdraw();
        update.setState(WithdrawStateEnum.FAIL.getState())
                .setWithdrawId(withdraw.getWithdrawId())
                .setTransferState(WithdrawStateEnum.TRANSFER_FAIL.getState())
                .setTransferMsg(failMessage)
        ;
        LambdaQueryWrapperX<AccWithdraw> qw = new LambdaQueryWrapperX<>();
        qw.in(AccWithdraw::getState, WithdrawStateEnum.TRANSFER_PROCESSING.getState(), WithdrawStateEnum.INIT.getState());
        qw.eq(AccWithdraw::getWithdrawId, withdraw.getWithdrawId());
        int updated = accWithdrawMapper.update(update, qw);
        if (updated == 0) {
            throw exception(WITHDRAW_TSF_ERR);
        }
        // 解除账户冻结金额
        AccAccountFlow accountFlow = unLockAccountWithdraw(withdraw);
        // 执行冻结
        accountFlowService.processFlow(accountFlow);
    }

    /**
     * 提现   -   转账成功
     * @param transferNo    提现单号
     */
    @Override
    public void processWithdrawTransferSuccess(String transferNo) {
        // 修改提现单状态
        AccWithdraw withdraw = accWithdrawMapper.selectByTransferNo(transferNo);
        AccWithdraw update = new AccWithdraw();
        update.setWithdrawId(withdraw.getWithdrawId())
                .setTransferState(WithdrawStateEnum.TRANSFER_SUCCESS.getState())
                .setTransferFinishTime(DateUtil.date())
        ;
        accWithdrawMapper.updateById(update);
        // 发起结算
        shelf().settle(accWithdrawMapper.selectByTransferNo(transferNo));
    }

    @Override
    public void processWithdrawProcessing(AccWithdrawFlow withdrawFlow) {
        AccWithdraw withdraw = accWithdrawMapper.selectById(withdrawFlow.getBusiId());
        // 更新提现信息
        AccWithdraw update = new AccWithdraw();
        update.setWithdrawId(withdraw.getWithdrawId())
                .setSettleState(WithdrawStateEnum.SETTLE_PROCESSING.getState())
                .setSettleInitTime(DateUtil.date())
                .setState(WithdrawStateEnum.PROCESSING.getState())
                .setSettleNo(withdrawFlow.getWithdrawNo())
        ;
        accWithdrawMapper.updateById(update);
    }

    @Override
    public void processWithdrawFail(AccWithdrawFlow withdrawFlow) {
        AccWithdraw withdraw = accWithdrawMapper.selectById(withdrawFlow.getBusiId());
        // 更新提现信息
        AccWithdraw update = new AccWithdraw();
        update.setWithdrawId(withdraw.getWithdrawId())
                .setSettleNo(withdrawFlow.getWithdrawNo())
                .setSettleInitTime(DateUtil.date())
                .setSettleState(WithdrawStateEnum.SETTLE_FAIL.getState())
                .setSettleMsg(withdrawFlow.getErrorReason())
        ;
        // 入驻商提现没有转账流程, 可以直接定义为提现失败
        if (MerchantTypeEnum.isSupplier(withdraw.getMerchantType())) {
            update.setState(WithdrawStateEnum.FAIL.getState());
        }
        accWithdrawMapper.updateById(update);
    }

    @Override
    @Transactional
    public void processWithdrawSuccess(AccWithdrawFlow withdrawFlow) {
        AccWithdraw withdraw = accWithdrawMapper.selectById(withdrawFlow.getBusiId());
        // 更新提现信息
        AccWithdraw update = new AccWithdraw();
        update.setWithdrawId(withdraw.getWithdrawId())
                .setState(WithdrawStateEnum.FINISH.getState())
                .setSettleFinishTime(DateUtil.date())
                .setSettleState(WithdrawStateEnum.SETTLE_SUCCESS.getState())
                .setSettleMsg("结算成功")
                .setBankAccountName1(withdrawFlow.getBankAccountName())
                .setBankAccountNo1(withdrawFlow.getBankAccountNo())
                .setFee(withdrawFlow.getFree())
        ;
        accWithdrawMapper.updateById(update);
        // 处理账户信息, 入驻商结算提现不需要操作流水
        if (withdraw.getAccountId() > NumberPool.LONG_ZERO) {
            // 写结算流水
            withdrawSuccessAccountFlow(withdraw);
        }
    }

    /**
     * 提现成功, 操作账户
     * @param withdraw
     */
    private void withdrawSuccessAccountFlow(AccWithdraw withdraw) {
        String busiFields = StringUtils.join(ListUtil.toList(AccountBusiTypeField.FROZEN_AMT.getField(), AccountBusiTypeField.WITHDRAWABLE_AMT.getField()), StringPool.COMMA);
        List<Long> flowIdList = new ArrayList<Long>();
        // 解除账户冻结 扣减余额
        {
            AccAccountFlow accountFlow = new AccAccountFlow();
            accountFlow.setAccountId(withdraw.getAccountId())
                    .setSysCode(withdraw.getSysCode())
                    .setBusiWithdrawableAmt(BigDecimal.ZERO.subtract(withdraw.getApplyAmt()))
                    .setBusiFrozenAmt(BigDecimal.ZERO.subtract(withdraw.getApplyAmt()))
                    .setBusiType(AccountBusiType.WITHDRAW_SUCCESS.getType())
                    .setBusiId(withdraw.getWithdrawId())
                    .setBusiFields(busiFields)
                    .setMerchantId(withdraw.getMerchantId())
                    .setMerchantType(withdraw.getMerchantType())
                    .setPlatform(withdraw.getPlatform())
            ;
            accountFlowService.insertAccAccountFlow(accountFlow);
            flowIdList.add(accountFlow.getAccountFlowId());
        }
        // 增加补贴大区手续费补贴账户
        {
            AccAccountFlow accountFlow = new AccAccountFlow();
            accountFlow.setSysCode(withdraw.getSysCode())
                    .setBusiWithdrawableAmt(withdraw.getPartnerProfit())
                    .setBusiType(AccountBusiType.WITHDRAW_SUCCESS.getType())
                    .setBusiId(withdraw.getWithdrawId())
                    .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField())
                    .setMerchantId(withdraw.getSysCode())
                    .setMerchantType(MerchantTypeEnum.PARTNER_FREE.getType())
                    .setPlatform(withdraw.getPlatform())
            ;
            accountFlowService.insertAccAccountFlow(accountFlow);
            flowIdList.add(accountFlow.getAccountFlowId());
        }
        // 异步处理账户余额
        accountFlowService.processFlowByIds(flowIdList);
    }

    @Override
    @Transactional
    public void rejectWithdraw(AccWithdrawAuditVO withdrawAudit) {
        // 修改提现单状态
        AccWithdraw withdraw = accWithdrawMapper.selectById(withdrawAudit.getWithdrawId());
        AccWithdraw update = new AccWithdraw();
        update.setState(WithdrawStateEnum.REJECT.getState())
                .setWithdrawId(withdraw.getWithdrawId())
                .setRejectTime(DateUtil.date())
                .setRejectReason(withdrawAudit.getRemark())
        ;
        LambdaQueryWrapperX<AccWithdraw> qw = new LambdaQueryWrapperX<>();
        qw.eq(AccWithdraw::getState, WithdrawStateEnum.INIT.getState());
        qw.eq(AccWithdraw::getWithdrawId, withdraw.getWithdrawId());
        int updated = accWithdrawMapper.update(update, qw);
        if (updated == 0) {
            throw exception(WITHDRAW_TSF_ERR);
        }
        // 非入驻商提现, 才需要解除冻结流水, 入驻商提现只做记录
        if (!MerchantTypeEnum.SUPPLIER.getType().equals(withdraw.getMerchantType())) {
            // 解除账户冻结金额
            AccAccountFlow accountFlow = unLockAccountWithdraw(withdraw);
            // 执行解冻
            accountFlowService.processFlow(accountFlow);
        }
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_PROCESS_FLOW, condition = "#accWithdraw.merchantType + #accWithdraw.merchantId", tryLock = true)
    public TransferSettleRespDTO settle(AccWithdraw accWithdraw) {
        if (!accWithdraw.getTransferState().equals(WithdrawStateEnum.TRANSFER_SUCCESS.getState())) {
            throw exception(WITHDRAW_TRANSFER_NOT_FINISH);
        }
        if (Objects.nonNull(accWithdraw.getSettleState()) && accWithdraw.getSettleState().equals(WithdrawStateEnum.SETTLE_PROCESSING.getState())) {
            throw exception(WITHDRAW_SETTLE_PROCESS);
        }
        if (Objects.nonNull(accWithdraw.getSettleState()) && accWithdraw.getSettleState().equals(WithdrawStateEnum.SETTLE_SUCCESS.getState())) {
            throw exception(WITHDRAW_SETTLE_FINISH);
        }
        // 发起结算
        TransferSettleReqVO reqVo = new TransferSettleReqVO();
        AccWithdrawConvert.INSTANCE.convert(reqVo, accWithdraw);
        //初始银行卡信息
        initBankInfo(reqVo, accWithdraw);
        // 提现金额
        reqVo.setTips("活动资金");
        return transferAccountService.transferSettle(reqVo);
    }

    private void initBankInfo(TransferSettleReqVO reqVo,AccWithdraw accWithdraw){
        if (!PayChannelEnum.MIDEA_PAY.getCode().equals(accWithdraw.getPlatform())) {
            return;
        }
        AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(accWithdraw.getMerchantId(), accWithdraw.getMerchantType(), accWithdraw.getPlatform());
        if(null == platformMerchant){
            return;
        }
        if(StringUtils.isEmpty(reqVo.getAltMchName())){
            reqVo.setAltMchName(platformMerchant.getAltMchName());
        }
        if(StringUtils.isEmpty(reqVo.getBankType())){
            reqVo.setBankType(platformMerchant.getBankType());
        }
        if(StringUtils.isEmpty(reqVo.getBankAccountNo())){
            reqVo.setBankAccountNo(platformMerchant.getAccountNo());
        }
        if(StringUtils.isEmpty(reqVo.getBankAccountName())){
            reqVo.setBankAccountName(platformMerchant.getAccountName());
        }
    }

    /**
     * 直接结算, 不直接转账操作
     * @param accWithdraw   提现单
     */
    @Override
    public void directSettle(AccWithdraw accWithdraw) {
        accWithdraw.setTransferState(WithdrawStateEnum.TRANSFER_SUCCESS.getState())
                .setTransferFinishTime(DateUtil.date())
                ;
        accWithdrawMapper.updateById(accWithdraw);
        shelf().settle(accWithdraw);
    }

    @Override
    public BigDecimal getTotalWithdrawAmt(Long accountId) {
        return accWithdrawMapper.selectTotalWithdrawAmt(accountId);
    }

    /**
     * 验证提现参数
     * @param createReqVO
     * @return
     */
    private AccAccount validateAccount(AccWithdrawSaveReqVO createReqVO, AccWithdraw accWithdraw) {
        AccAccount account = accountService.getAccAccount(createReqVO.getAccountId());
        // 验证账户是否存在
        if (Objects.isNull(account) || !account.getMerchantType().equals(createReqVO.getMerchantType())) {
            throw exception(NOT_EXIST_ACCOUNT);
        }
        // 余额验证
        if (NumberUtil.isGreater(createReqVO.getWithdrawAmt(), account.getValidAccountAmt())) {
            throw exception(BALANCE_ERR);
        }
        // 账户提现, 分账方都是, 平台商配置的收款商户号, 收款商户号接受入驻商的充值
        if (!PayChannelEnum.MOCK.getCode().equals(account.getPlatform())) {
            // 非模拟支付账户
            PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(account.getSysCode());
            if (Objects.isNull(payConfigDto)) {
                // 大区支付配置不存在
                throw exception(NOT_EXIST_MERCHANT_CONFIG);
            }
            if (!account.getPlatform().equals(payConfigDto.getInteriorStoredPayPlatform())) {
                // 提现账户与平台储值支付不一致
                throw exception(WITHDRAW_MERCHANT_ERR);
            }
            // 获取平台合利宝支付配置, 目前只有合利宝, 无需判断平台目前是什么支付平台
            PayAccountConfigDTO payAccountConfig = accountCacheService.getPayAccountConfigDTO(account.getSysCode());
            if (Objects.isNull(payAccountConfig)) {
                // 大区支付配置不存在
                throw exception(NOT_EXIST_MERCHANT_CONFIG);
            }
            if (StringUtils.isEmpty(payAccountConfig.getGatheringTenantCode())) {
                // 收款商编不存在
                throw exception(WITHDRAW_BSI_MERCHANT_ERR);
            }
            accWithdraw.setBsiMchNo(payAccountConfig.getGatheringTenantCode());
            /*if (MerchantTypeEnum.PARTNER.getType().equals(account.getMerchantType())) {
                // 平台商提现
                accWithdraw.setAltMchNo(payAccountConfig.getWithdrawTenantCode());
            } else {
                // 非平台商提现
                AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(account.getMerchantId(), account.getMerchantType(), account.getPlatform());
                if (Objects.isNull(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchNo())) {
                    throw exception(WITHDRAW_MERCHANT_NONE_EXIST);
                }
                accWithdraw.setAltMchNo(platformMerchant.getAltMchNo());
                accWithdraw.setAltMchName(platformMerchant.getAltMchName());
            }*/
            AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(account.getMerchantId(), account.getMerchantType(), account.getPlatform(), account.getSysCode());
            if (Objects.isNull(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchNo())) {
                throw exception(WITHDRAW_MERCHANT_NONE_EXIST);
            }
            accWithdraw.setAltMchNo(platformMerchant.getAltMchNo());
            accWithdraw.setAltMchName(platformMerchant.getAltMchName());
        } else {
            // 模拟支付填充假参数
            accWithdraw.setBsiMchNo(PayConstants.DEFAULT_MOCK_NO);
            accWithdraw.setAltMchNo(PayConstants.DEFAULT_MOCK_NO);
            accWithdraw.setAltMchName(PayConstants.DEFAULT_MOCK_NAME);
        }
        WithdrawalSettingPolicyDTO withdrawalSetting = accountCacheService.getWithdrawalSetting(account.getSysCode());
        if (Objects.nonNull(withdrawalSetting)) {
            if (StringUtils.isNotEmpty(withdrawalSetting.getMinimumWithdrawalAmount())) {
                if (NumberUtil.isGreater(new BigDecimal(withdrawalSetting.getMinimumWithdrawalAmount()), createReqVO.getWithdrawAmt())) {
                    // 提现金额小于最小提现金额
                    throw exception(new ErrorCode(WITHDRAW_MIN_AMT.getCode(), StringUtils.format("最小提现金额:{}", withdrawalSetting.getMinimumWithdrawalAmount())));
                }
            }
        }
        return account;
    }


    /**
     * 创建解除账户冻结流水
     * @param withdraw
     * @return
     */
    @NotNull
    private AccAccountFlow unLockAccountWithdraw(AccWithdraw withdraw) {
        AccAccountFlow accountFlow = new AccAccountFlow();
        accountFlow.setAccountId(withdraw.getAccountId())
                .setSysCode(withdraw.getSysCode())
                .setBusiFrozenAmt(BigDecimal.ZERO.subtract(withdraw.getApplyAmt()))
                .setBusiType(AccountBusiType.WITHDRAW_FAIL_FROZEN.getType())
                .setBusiId(withdraw.getWithdrawId())
                .setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField())
                .setMerchantId(withdraw.getMerchantId())
                .setMerchantType(withdraw.getMerchantType())
                .setPlatform(withdraw.getPlatform())
        ;
        accountFlowService.insertAccAccountFlow(accountFlow);
        return accountFlow;
    }

    IAccWithdrawService shelf() {
        return SpringUtils.getAopProxy(this);
    }
}
