package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.controller.recharge.vo.AccRechargeImportAuditReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeImportRespVO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccRechargeImport;
import com.zksr.account.controller.recharge.vo.AccRechargeImportPageReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeImportSaveReqVO;

/**
 * 后台导入充值Service接口
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface IAccRechargeImportService {

    /**
     * 新增后台导入充值
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAccRechargeImport(@Valid AccRechargeImportSaveReqVO createReqVO);

    /**
     * 修改后台导入充值
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccRechargeImport(@Valid AccRechargeImportSaveReqVO updateReqVO);

    /**
     * 删除后台导入充值
     *
     * @param rechargeImportId ${pkColumn.columnComment}
     */
    public void deleteAccRechargeImport(Long rechargeImportId);

    /**
     * 批量删除后台导入充值
     *
     * @param rechargeImportIds 需要删除的后台导入充值主键集合
     * @return 结果
     */
    public void deleteAccRechargeImportByRechargeImportIds(Long[] rechargeImportIds);

    /**
     * 获得后台导入充值
     *
     * @param rechargeImportId ${pkColumn.columnComment}
     * @return 后台导入充值
     */
    public AccRechargeImport getAccRechargeImport(Long rechargeImportId);

    /**
     * 获得后台导入充值分页
     *
     * @param pageReqVO 分页查询
     * @return 后台导入充值分页
     */
    PageResult<AccRechargeImportRespVO> getAccRechargeImportPage(AccRechargeImportPageReqVO pageReqVO);

    /**
     * 停用充值批次
     */
    void disable(AccRechargeImportAuditReqVO reqVO);

    /**
     * 审核充值批次
     */
    void enable(AccRechargeImportAuditReqVO reqVO);

    /**
     * 获取审核验证码
     */
    void smsCode(Long rechargeImportId);
}
