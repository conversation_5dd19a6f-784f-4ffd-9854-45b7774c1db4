package com.zksr.account.controller.pay.vo;

import java.math.BigDecimal;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 账户付款单对象 acc_pay
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@ApiModel("账户付款单 - acc_pay分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccPayPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 账户付款单id */
    @ApiModelProperty(value = "付款状态;0-已提交 1-已完成  订单支付:订单生成完之后，调用支付接口，支付方式为储值支付，校验储值金额是否充足:1.如充足，则生成账户付款单，状态为0-已提交，同步插入一条账户流水并同时更改门店账户，减少门店账户金额，然后付款单状态变更为1-已完成；2.如不充足，则直接返回'门店储值金额不足'，支付失败售后(取消订单)退款:1.调用退款接口，退款方式为储值支付，则生成账户付款单")
    private Long payId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 支付类型 0-支付 1-退款 */
    @Excel(name = "支付类型 0-支付 1-退款")
    @ApiModelProperty(value = "支付类型 0-支付 1-退款")
    private Integer payType;

    /** 商城订单号 */
    @Excel(name = "商城订单号")
    @ApiModelProperty(value = "商城订单号")
    private String orderNo;

    /** 支付金额 */
    @Excel(name = "支付金额")
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmt;

    /** 售后单号 */
    @Excel(name = "售后单号")
    @ApiModelProperty(value = "售后单号")
    private String afterNo;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmt;

    /** 账户id */
    @Excel(name = "账户id")
    @ApiModelProperty(value = "账户id")
    private Long payAccountId;

    /** 付款状态;0-已提交 1-已完成  订单支付:订单生成完之后，调用支付接口，支付方式为储值支付，校验储值金额是否充足:1.如充足，则生成账户付款单，状态为0-已提交，同步插入一条账户流水并同时更改门店账户，减少门店账户金额，然后付款单状态变更为1-已完成；2.如不充足，则直接返回"门店储值金额不足"，支付失败售后(取消订单)退款:1.调用退款接口，退款方式为储值支付，则生成账户付款单（退款），状态为0-已提交，同步插入一条账户流水并同时增加门店账户金额，然后付款单状态变更为1-已完成； */
    @Excel(name = "付款状态;0-已提交 1-已完成  订单支付:订单生成完之后，调用支付接口，支付方式为储值支付，校验储值金额是否充足:1.如充足，则生成账户付款单，状态为0-已提交，同步插入一条账户流水并同时更改门店账户，减少门店账户金额，然后付款单状态变更为1-已完成；2.如不充足，则直接返回'门店储值金额不足'，支付失败售后(取消订单)退款:1.调用退款接口，退款方式为储值支付，则生成账户付款单", readConverterExp = "退=款")
    @ApiModelProperty(value = "付款状态;0-已提交 1-已完成  订单支付:订单生成完之后，调用支付接口，支付方式为储值支付，校验储值金额是否充足:1.如充足，则生成账户付款单，状态为0-已提交，同步插入一条账户流水并同时更改门店账户，减少门店账户金额，然后付款单状态变更为1-已完成；2.如不充足，则直接返回'门店储值金额不足'，支付失败售后(取消订单)退款:1.调用退款接口，退款方式为储值支付，则生成账户付款单")
    private Integer state;


}
