package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zksr.common.core.utils.JsonUtils;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayWechatNotifyRespVO {
    /**
     * 接口名称
     */
    @JsonProperty("service")
    private String service;

    /**
     * 接口版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 平台商户号
     */
    @JsonProperty("partner")
    private String partner;

    /**
     * 商户网站使用的编码格式，目前只能取值UTF-8
     */
    @JsonProperty("input_charset")
    private String inputCharset;

    /**
     * 签名方式
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * 签名
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 商户单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 美的支付生成的交易单号
     */
    @JsonProperty("trade_no")
    private String tradeNo;

    /**
     * 担保交易标志
     */
    @JsonProperty("is_guarantee")
    private String isGuarantee;

    /**
     * 美的支付交易订单创建时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("trade_accept_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String tradeAcceptTime;

    /**
     * 美的支付通知商户时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("notify_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String notifyTime;

    /**
     * 支付金额的货币类型：
     * CNY：人民币
     */
    @JsonProperty("currency_type")
    private String currencyType;

    /**
     * 总金额等于总支付金额加总营销金额
     */
    @JsonProperty("total_order_amount")
    private String totalOrderAmount;

    /**
     * 支付总金额（单位分）
     */
    @JsonProperty("total_amount")
    private String totalAmount;

    /**
     * 订单总数
     */
    @JsonProperty("total_count")
    private String totalCount;

    /**
     * 交易成功的金额，单位：分
     */
    @JsonProperty("success_amount")
    private String successAmount;

    /**
     * 交易成功的订单数（<=total_count）
     */
    @JsonProperty("success_count")
    private String successCount;

    /**
     * 完成状态,
     * NOT_EXIST：订单不存在
     * WAIT_PAY：订单已接收
     * PAYING：支付中
     * COMPLETE：支付完成
     */
    @JsonProperty("complete_status")
    private String completeStatus;

    /**
     * 完成状态描述
     */
    @JsonProperty("complete_status_info")
    private String completeStatusInfo;

    /**
     * 该笔交易的支付完成时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("complete_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String completeTime;

    /**
     * 商户自定义信息，原值返回给商户
     */
    @JsonProperty("attach")
    private String attach;

    /**
     * 支付方式：三级业务类型
     *
     * 注：B2C下单版本>=3.1.0才返回此参数
     */
    @JsonProperty("business_types")
    private String businessTypes;

    /**
     * 子单列表，格式为json字符串
     */
    @JsonProperty("sub_orders")
    private String subOrders;

    public List<SubOrder> getSubOrders(){
        return StringUtils.isEmpty(subOrders)?null: JsonUtils.toJavaList(subOrders, SubOrder.class);
    }

    /**
     * 优惠券参数
     */
    @JsonProperty("coupon_params")
    private String couponParams;

    /**
     * 美的支付下游渠道响应信息
     */
    @JsonProperty("channel_rsp_info")
    private String channelRspInfo;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class SubOrder {

        /**
         * 交易收款商户号
         */
        @JsonProperty("partner")
        private String partner;

        /**
         * 子B2C订单商户订单号
         */
        @JsonProperty("sub_out_trade_no")
        private String subOutTradeNo;

        /**
         * 美的支付生成的交易单号
         */
        @JsonProperty("sub_trade_no")
        private String subTradeNo;

        /**
         * 营销商户号
         */
        @JsonProperty("market_acc_partner")
        private String marketAccPartner;

        /**
         * 营销金额，单位:分
         */
        @JsonProperty("market_amount")
        private String marketAmount;

        /**
         * 子B2C订单金额，单位: 分
         */
        @JsonProperty("pay_amount")
        private String payAmount;

        /**
         * 该笔交易的支付时间，格式为yyyyMMddHHmmss
         */
        @JsonProperty("pay_time")
        @JsonFormat(pattern = "yyyyMMddHHmmss")
        private LocalDateTime payTime;

        /**
         * 支付结果
         * NOT_EXIST：订单不存在
         * WAIT_PAY：未支付
         * PAYING：支付中
         * SUCCESS：成功
         * FAIL：失败
         */
        @JsonProperty("trade_status")
        private String tradeStatus;

        /**
         * 支付结果描述
         */
        @JsonProperty("trade_status_info")
        private String tradeStatusInfo;

        /**
         * 商户自定义的信息，原值返回给商户
         */
        @JsonProperty("sub_attach")
        private String subAttach;
    }


    @Getter
    public enum MideaPayTradeStatus {
        WAITING_TRADE("WAITING_TRADE", "待交易"),
        SUCCESS("SUCCESS", "成功"),
        FAIL("FAIL", "失败"),
        CLOSE("CLOSE", "交易关闭"),
        ;

        private final String value;
        private final String desc;
        private static final Map<String, MideaPayTradeStatus> valueMap = Arrays.stream(values()).collect(Collectors.toMap(MideaPayTradeStatus::getValue, Function.identity()));

        MideaPayTradeStatus(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static MideaPayTradeStatus formValue(String value) {
            return Optional.ofNullable(valueMap.get(value)).orElseThrow(() -> new RuntimeException("can not find the enum for this value: " + value));
        }
    }
}
