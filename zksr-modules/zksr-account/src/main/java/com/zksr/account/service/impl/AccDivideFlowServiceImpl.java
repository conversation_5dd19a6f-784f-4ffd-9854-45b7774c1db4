package com.zksr.account.service.impl;

import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.account.mapper.AccDivideFlowMapper;
import com.zksr.account.convert.divideFlow.AccDivideFlowConvert;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.controller.divide.vo.AccDivideFlowPageReqVO;
import com.zksr.account.controller.divide.vo.AccDivideFlowSaveReqVO;
import com.zksr.account.service.IAccDivideFlowService;

import java.util.List;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.account.enums.ErrorCodeConstants.*;

/**
 * 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
@Service
public class AccDivideFlowServiceImpl implements IAccDivideFlowService {
    @Autowired
    private AccDivideFlowMapper accDivideFlowMapper;

    /**
     * 新增分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertAccDivideFlow(AccDivideFlowSaveReqVO createReqVO) {
        // 插入
        AccDivideFlow accDivideFlow = AccDivideFlowConvert.INSTANCE.convert(createReqVO);
        accDivideFlowMapper.insert(accDivideFlow);
        // 反写flowId
        createReqVO.setDivideFlowId(accDivideFlow.getDivideFlowId());
        // 返回
        return accDivideFlow.getDivideFlowId();
    }

    /**
     * 修改分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAccDivideFlow(AccDivideFlowSaveReqVO updateReqVO) {
        accDivideFlowMapper.updateById(AccDivideFlowConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param divideFlowId 分账流水id
     */
    @Override
    public void deleteAccDivideFlow(Long divideFlowId) {
        // 删除
        accDivideFlowMapper.deleteById(divideFlowId);
    }

    /**
     * 批量删除分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param divideFlowIds 需要删除的分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)主键
     * @return 结果
     */
    @Override
    public void deleteAccDivideFlowByDivideFlowIds(Long[] divideFlowIds) {
        for(Long divideFlowId : divideFlowIds){
            this.deleteAccDivideFlow(divideFlowId);
        }
    }

    /**
     * 获得分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param divideFlowId 分账流水id
     * @return 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     */
    @Override
    public AccDivideFlow getAccDivideFlow(Long divideFlowId) {
        return accDivideFlowMapper.selectById(divideFlowId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccDivideFlow> getAccDivideFlowPage(AccDivideFlowPageReqVO pageReqVO) {
        return accDivideFlowMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AccDivideFlow> getTryDivideFlow(Long minId) {
        return accDivideFlowMapper.selectTryDivideFlow(minId);
    }


}
