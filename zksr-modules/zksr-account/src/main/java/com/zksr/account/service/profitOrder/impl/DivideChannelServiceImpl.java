package com.zksr.account.service.profitOrder.impl;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.zksr.account.client.DivideClient;
import com.zksr.account.client.mideapay.MideaPayDivideClient;
import com.zksr.account.convert.pay.PayConvert;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.profitOrder.DivideChannelService;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.model.dto.MideaPayConfig;
import com.zksr.system.api.partnerConfig.dto.MideaPayConfigDTO;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import static com.zksr.common.core.utils.CacheUtils.buildAsyncReloadingCache;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 获取交易操作对象
 * @date 2024/3/11 14:41
 */
@Slf4j
@Service
public class DivideChannelServiceImpl implements DivideChannelService {

    /**
     * {@link DivideClient} 缓存，30秒自动异步刷新
     */
    @Getter
    private final LoadingCache<DivideClient.ClientParam, DivideClient> transferClientMap = buildAsyncReloadingCache(Duration.ofSeconds(30L),
            new CacheLoader<DivideClient.ClientParam, DivideClient>() {
                @Override
                public DivideClient load(DivideClient.ClientParam param) {
                    return initClient(param.getSysCode(), param.getPlatform());
                }
            });


    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private IAccountCacheService accountCacheService;


    @Override
    public DivideClient getDivideClient(Long sysCode, String platform) {
        //if (PayChannelEnum.MOCK.getCode().equals(platform)) {
        //    // 直接返回模拟支付支持
        //    return mockTransferClient;
        //}
        return transferClientMap.getUnchecked(new DivideClient.ClientParam(sysCode, platform));
    }

    private DivideClient initClient(Long sysCode, String platform) {
        try {
            PayChannelEnum payChannel = PayChannelEnum.fromValue(platform);
            if(payChannel == PayChannelEnum.MIDEA_PAY){//美的付
                
                //!@分账 - 2、根据平台获取配置
                MideaPayConfigDTO mideaPayConfig = accountCacheService.getMideaPayConfig(sysCode);
                MideaPayConfig payConfig = PayConvert.INSTANCE.convert2MideaPayConfig(mideaPayConfig);
                if(null == payConfig || payConfig.checkEmpty()){
                    throw new ServiceException("商户信息没有配置或缺失关键配置项");
                }
                MideaPayDivideClient mideaPayProfitOrderClient = new MideaPayDivideClient();
                mideaPayProfitOrderClient.setConfig(JSON.toJSONString(mideaPayConfig));
                return mideaPayProfitOrderClient;
            }
            
            // 其他..  系统暂不支持
            if (payChannel == PayChannelEnum.HLB) {
                //合利宝目前系统暂不支持
                return null;
            }
            
        } catch (ServiceException e) {
            log.error("商户交易客户端异常", e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("商户交易客户端异常", e);
            throw new RuntimeException(e);
        }
        return null;
    }

}
