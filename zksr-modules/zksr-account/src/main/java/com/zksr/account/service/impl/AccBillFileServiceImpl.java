package com.zksr.account.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.account.api.transfer.dto.AccBillFileDTO;
import com.zksr.account.domain.AccBillFile;
import com.zksr.account.mapper.AccBillFileMapper;
import com.zksr.account.service.IAccBillFileService;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 商户账单文件备份 Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class AccBillFileServiceImpl implements IAccBillFileService {
    @Autowired
    private AccBillFileMapper accBillFileMapper;
    @Override
    public int insertBillFileRecord(AccBillFileDTO accBillFileDTO) {
        AccBillFile accBillFile = HutoolBeanUtils.toBean(accBillFileDTO, AccBillFile.class);
        accBillFile.setCreateTime(DateUtil.date());
        return accBillFileMapper.insert(accBillFile);
    }

    @Override
    public List<String> checkBillFileRecordExists(String altNo, String data) {
        return accBillFileMapper.checkBillFileRecordExists(altNo,data);
    }
}
