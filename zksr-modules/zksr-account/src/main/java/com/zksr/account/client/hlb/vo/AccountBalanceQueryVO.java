package com.zksr.account.client.hlb.vo;

import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableSet;
import lombok.Data;

import java.util.Date;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝账户余额查询请求
 * @date 2024/4/23 14:59
 */
@Data
public class AccountBalanceQueryVO {
    private String P1_bizType = "MerchantAccountQuery";
    private String P2_customerNumber;
    private String P3_timestamp = DateUtil.format(new Date(), "yyyyMMddHHmmss");
    private String sign;
    private boolean firstAnd = true;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of(
            "P1_bizType",
            "P2_customerNumber",
            "P3_timestamp"
    );

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = null;
}
