package com.zksr.account.service.transfer;

import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;

/**
 * <AUTHOR>
 * @time 2024/3/11
 * @desc  转账操作流程控制
 */
public interface TransferSubmitProcessService {

    /**
     * 转账处理中
     * @param transferSubmitRespDTO
     */
    void notifyProcessing(TransferSubmitRespDTO transferSubmitRespDTO);

    /**
     * 处理转账成功回调
     * @param transferSubmitRespDTO
     */
    void notifySuccess(TransferSubmitRespDTO transferSubmitRespDTO);

    /**
     * 处理转账失败
     * @param transferSubmitRespDTO
     */
    void notifyFail(TransferSubmitRespDTO transferSubmitRespDTO);

}
