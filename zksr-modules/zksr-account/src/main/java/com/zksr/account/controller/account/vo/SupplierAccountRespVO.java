package com.zksr.account.controller.account.vo;

import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 入驻商账户余额
 * @date 2024/4/25 18:32
 */
@Data
public class SupplierAccountRespVO extends PayPlatformAccountVO {

    @ApiModelProperty("账户保证金, settleAmt-minSettleAmt=可提现金额")
    private BigDecimal minSettleAmt = BigDecimal.ZERO;

    @ApiModelProperty("账户名称")
    private String accountName;

    @ApiModelProperty("银行卡号")
    private String accountNo;

    @ApiModelProperty("银行")
    private String bankBranch;
}
