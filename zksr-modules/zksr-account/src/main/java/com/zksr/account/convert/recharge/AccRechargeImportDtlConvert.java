package com.zksr.account.convert.recharge;

import com.zksr.account.controller.recharge.vo.AccRechargeImportDtlRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeImportDtlSaveReqVO;
import com.zksr.account.domain.AccRechargeImportDtl;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* PC后台导入充值详情
 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2025-03-24
*/
@Mapper
public interface AccRechargeImportDtlConvert {

    AccRechargeImportDtlConvert INSTANCE = Mappers.getMapper(AccRechargeImportDtlConvert.class);

    AccRechargeImportDtl convert(AccRechargeImportDtlSaveReqVO accRechargeImportDtlSaveReq);

    List<AccRechargeImportDtl> convert(List<AccRechargeImportDtlSaveReqVO> rechargeImportDtls);

    List<AccRechargeImportDtlRespVO> convertRespVO(List<AccRechargeImportDtl> dtls);
}