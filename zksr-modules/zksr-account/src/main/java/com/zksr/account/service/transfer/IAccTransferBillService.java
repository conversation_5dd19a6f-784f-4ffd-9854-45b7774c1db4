package com.zksr.account.service.transfer;

import com.zksr.account.api.transfer.dto.AccTransferBillDTO;
import com.zksr.account.api.transfer.dto.AccTransferBillOrderDTO;
import com.zksr.account.controller.transfer.vo.AccTransferBillPageReqVO;
import com.zksr.account.domain.AccTransferBill;
import com.zksr.common.core.web.pojo.PageResult;

import java.util.List;

/**
 * 交易对账单Service接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
public interface IAccTransferBillService {
    public void insertTransferBill(AccTransferBillDTO accTransferBillDTO);

    /**
     * 获得交易对账单分页
     *
     * @param pageReqVO 分页查询
     * @return 交易对账单分页
     */
    PageResult<AccTransferBill> getAccTransferBillPage(AccTransferBillPageReqVO pageReqVO);
}
