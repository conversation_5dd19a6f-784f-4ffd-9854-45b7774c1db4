package com.zksr.account.convert.withdraw;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.account.api.withdraw.dto.SaveWithdrawDTO;
import com.zksr.account.api.withdraw.dto.WithdrawDTO;
import com.zksr.account.controller.withdraw.vo.*;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccWithdraw;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 账户提现单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-04-12
*/
@Mapper
public interface AccWithdrawConvert {

    AccWithdrawConvert INSTANCE = Mappers.getMapper(AccWithdrawConvert.class);

    AccWithdrawRespVO convert(AccWithdraw accWithdraw);

    AccWithdraw convert(AccWithdrawSaveReqVO accWithdrawSaveReq);

    PageResult<AccWithdrawRespVO> convertPage(PageResult<AccWithdraw> accWithdrawPage);

    @Mappings({
            @Mapping(source = "withdraw.transferNo", target = "transferNo"),
            @Mapping(source = "withdraw.withdrawId", target = "busiId"),
            @Mapping(source = "withdraw.sysCode", target = "sysCode"),
            @Mapping(source = "withdraw.altMchNo", target = "customerNo"),
            @Mapping(source = "withdraw.altMchName", target = "altMchName"),
            @Mapping(source = "withdraw.bankAccountNo", target = "bankAccountNo"),
            @Mapping(source = "withdraw.bankName", target = "bankName"),
            @Mapping(source = "withdraw.merchantId", target = "merchantId"),
            @Mapping(source = "withdraw.merchantType", target = "merchantType"),
            @Mapping(source = "withdraw.platform", target = "platform"),
            @Mapping(source = "withdraw.transferType", target = "transferType"),
            @Mapping(source = "withdraw.withdrawAmt", target = "amount"),
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget TransferSettleReqVO reqVo, AccWithdraw withdraw);

    PageResult<AccDcWithdrawRespVO> convertDcPage(PageResult<AccWithdrawRespVO> pageResult);

    PageResult<AccOtherWithdrawRespVO> convertOtherPage(PageResult<AccWithdrawRespVO> pageResult);

    PageResult<AccPartnerWithdrawRespVO> convertPartnerPage(PageResult<AccWithdrawRespVO> accWithdrawPage);

    WithdrawDTO convertDTO(AccWithdraw accWithdraw);

    AccWithdrawSaveReqVO convertReqVO(SaveWithdrawDTO transferSaveDTO);

    PageResult<WithdrawDTO> convertDTOPage(PageResult<AccWithdrawRespVO> accWithdrawPage);

    PageResult<AccSoftwareWithdrawRespVO> convertSoftwarePage(PageResult<AccWithdrawRespVO> accWithdrawPage);
}