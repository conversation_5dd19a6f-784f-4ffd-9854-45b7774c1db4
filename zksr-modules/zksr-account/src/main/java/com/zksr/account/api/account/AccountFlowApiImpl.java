package com.zksr.account.api.account;

import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.account.vo.ApiAccAccountFlowPageVO;
import com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO;
import com.zksr.account.convert.account.AccountFlowConvert;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.mapper.AccAccountFlowMapper;
import com.zksr.account.service.IAccAccountFlowService;
import com.zksr.common.core.domain.vo.openapi.BranchValueInfoOpenDTO;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.tools.Tool;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 账户流水对外API
 * @date 2024/4/26 11:07
 */
@InnerAuth
@RestController
@ApiIgnore
public class AccountFlowApiImpl implements AccountFlowApi{

    @Autowired
    private IAccAccountFlowService accountFlowService;

    @Override
    public CommonResult<List<AccAccountFlowDTO>> getTrySettleFlow(Long minId) {
        return accountFlowService.getTrySettleFlow(minId);
    }

    @Override
    public CommonResult<PageResult<AccAccountFlowDTO>> getAccountFlowPge(ApiAccAccountFlowPageVO pageVO) {
        return CommonResult.success(
                AccountFlowConvert.INSTANCE.convertDTOPage(
                        accountFlowService.getAccAccountFlowPage(AccountFlowConvert.INSTANCE.convertPageReqVO(pageVO))
                )
        );
    }

    @Override
    public CommonResult<PageResult<RechargeConsumeRespVO>> getBranchAccountFlowPge(ApiAccAccountFlowPageVO pageVO) {
        return CommonResult.success(accountFlowService.getBranchAccountFlowPage(AccountFlowConvert.INSTANCE.convertPageReqVO(pageVO)));
    }

    @Override
    public CommonResult<List<AccAccountFlowDTO>> getAccAccountFlowListByIds(List<Long> accountFlowIds) {
        List<AccAccountFlow> accAccountFlowList = accountFlowService.getAccAccountFlowListByIds(accountFlowIds);
        if(ToolUtil.isNotEmpty(accAccountFlowList)){
            return CommonResult.success(AccountFlowConvert.INSTANCE.convert(accAccountFlowList));
        }
        return CommonResult.success(null);
    }

    @Override
    public CommonResult<BranchValueInfoOpenDTO> getBranchValueInfoByIds(List<Long> accountFlowIds) {
        return CommonResult.success(accountFlowService.getBranchValueInfoByIds(accountFlowIds));
    }
}
