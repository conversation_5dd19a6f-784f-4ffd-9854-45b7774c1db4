package com.zksr.account.service.pay.process;

import com.zksr.account.domain.AccRecharge;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.service.IAccRechargeService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.ChargeTypeEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayWayEnum;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.PayAccountConfigDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 入驻商充值单数据提供
 * @date 2024年3月22日09:40:46
 */
@Service
@Slf4j
public class SupplierRechargeOrderProcessServiceImpl extends RechargeOrderProcessServiceImpl {

    @Resource
    private SupplierApi supplierApi;

    @Resource
    private PartnerConfigApi partnerConfigApi;

    @Autowired
    private IAccRechargeService rechargeService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Override
    public Integer getOrderType() {
        return OrderTypeConstants.SUPPLIER_CHARGE;
    }

    @Override
    public PayOrderDTO validateOrder(PayOrderSubmitReqVO reqVO) {
        // 必须参数 ============================== start
        PayOrderDTO dto = new PayOrderDTO();
        dto.setOrderType(reqVO.getOrderType());
        dto.setAppid(reqVO.getAppid());
        dto.setOrderNo(reqVO.getOrderNo());
        dto.setOpenid(reqVO.getOpenid());
        // 必须参数 ============================== end
        AccRecharge recharge = rechargeService.getRechargeByNo(reqVO.getOrderNo());
        if (Objects.isNull(recharge)) {
            throw exception(NOT_EXIST_RECHARGE);
        }
        if (!ChargeTypeEnum.SUPPLIER.getType().equals(recharge.getChargeType())) {
            log.warn("处理充值单异常, 充值单类型非supplier, rechargeNo : {}", reqVO.getOrderNo());
            throw exception(NON_RECHARGE_TYPE);
        }
        CommonResult<SupplierDTO> supplierInfo = supplierApi.getBySupplierId(recharge.getRechargeMerchantId());
        if (!supplierInfo.isSuccess()) {
            log.warn("处理充值单异常, 找不到入驻商, rechargeNo : {}", reqVO.getOrderNo());
            throw exception(NON_RECHARGE_SUPPLIER);
        }
        dto.setSysCode(recharge.getSysCode());
        dto.setPayAmt(recharge.getRechargeAmt());
        dto.setBody(String.format("%s预充值活动资金", supplierInfo.getData().getSupplierName()));
        dto.setBusiId(recharge.getRechargeId());
        // 入驻商充值是结算方是平台, 下单的金额全部都给平台即可
        // 组装分账
        if (PayWayEnum.ONLINE.getPayWay().equals(reqVO.getPayWay())) {
            // 获取平台合利宝支付配置, 目前只有合利宝, 无需判断平台目前是什么支付平台
            PayAccountConfigDTO heLiBaoPayConfig = accountCacheService.getPayAccountConfigDTO(recharge.getSysCode());
            if (Objects.isNull(heLiBaoPayConfig)) {
                log.warn("平台找不到支付配置, sysCode : {}", recharge.getSysCode());
                throw exception(NOT_EXIST_MERCHANT_CONFIG);
            }
            List<OrderSettlementDTO> settles = new ArrayList<>();
            // 入驻商充值 结算方是 平台
            if (MerchantTypeEnum.PARTNER.getType().equals(recharge.getReceiveMerchantType())) {
                OrderSettlementDTO settlementDTO = new OrderSettlementDTO(
                        heLiBaoPayConfig.getGatheringTenantCode(),
                        recharge.getRechargeAmt().subtract(recharge.getFee())
                );
                settles.add(settlementDTO);
            }
            dto.setSettlements(settles);
        }
        reqVO.setSysCode(recharge.getSysCode());
        // 返回分账信息
        return dto;
    }
}
