package com.zksr.account.service.impl;

import com.github.pagehelper.Page;
import com.zksr.account.controller.recharge.vo.*;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.domain.AccRechargeImportDtl;
import com.zksr.account.enums.recharge.RechargeSource;
import com.zksr.account.mapper.AccRechargeMapper;
import com.zksr.account.service.*;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.sms.SmsApi;
import com.zksr.system.api.sms.dto.SmsCodeReqDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.account.mapper.AccRechargeImportMapper;
import com.zksr.account.convert.recharge.AccRechargeImportConvert;
import com.zksr.account.domain.AccRechargeImport;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.account.enums.ErrorCodeConstants.*;

/**
 * 后台导入充值Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Service
public class AccRechargeImportServiceImpl implements IAccRechargeImportService {

    @Autowired
    private AccRechargeImportMapper accRechargeImportMapper;

    @Autowired
    private IAccRechargeImportDtlService rechargeImportDtlService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IAccRechargeService accRechargeService;

    @Autowired
    private AccRechargeMapper accRechargeMapper;

    @Resource
    private SmsApi smsApi;

    @Resource
    private PartnerApi partnerApi;

    @Autowired
    private IAccAccountFlowService accountFlowService;

    /**
     * 新增后台导入充值
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertAccRechargeImport(AccRechargeImportSaveReqVO createReqVO) {
        // 插入
        AccRechargeImport accRechargeImport = AccRechargeImportConvert.INSTANCE.convert(createReqVO);
        accRechargeImport.setCounter(createReqVO.getRechargeImportDtls().size());
        accRechargeImport.setRechargeAmt(
                createReqVO.getRechargeImportDtls()
                        .stream()
                        .map(AccRechargeImportDtlSaveReqVO::getRechargeAmt)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        accRechargeImport.setRechargeImportState(NumberPool.INT_ZERO);
        accRechargeImportMapper.insert(accRechargeImport);
        rechargeImportDtlService.saveDtl(accRechargeImport, createReqVO.getRechargeImportDtls());
        // 返回
        return accRechargeImport.getRechargeImportId();
    }

    /**
     * 修改后台导入充值
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    @Transactional
    public void updateAccRechargeImport(AccRechargeImportSaveReqVO updateReqVO) {
        AccRechargeImport rechargeImport = accRechargeImportMapper.selectById(updateReqVO.getRechargeImportId());
        if (rechargeImport.getRechargeImportState() != NumberPool.INT_ZERO) {
            throw exception(RECHARGE_IMPORT_STATE_ERR);
        }
        // 插入
        AccRechargeImport accRechargeImport = AccRechargeImportConvert.INSTANCE.convert(updateReqVO);
        accRechargeImport.setCounter(updateReqVO.getRechargeImportDtls().size());
        accRechargeImport.setRechargeAmt(
                updateReqVO.getRechargeImportDtls()
                        .stream()
                        .map(AccRechargeImportDtlSaveReqVO::getRechargeAmt)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
        );
        accRechargeImportMapper.updateById(accRechargeImport);
        rechargeImportDtlService.saveDtl(accRechargeImport, updateReqVO.getRechargeImportDtls());
    }

    /**
     * 删除后台导入充值
     *
     * @param rechargeImportId ${pkColumn.columnComment}
     */
    @Override
    public void deleteAccRechargeImport(Long rechargeImportId) {
        // 删除
        accRechargeImportMapper.deleteById(rechargeImportId);
    }

    /**
     * 批量删除后台导入充值
     *
     * @param rechargeImportIds 需要删除的后台导入充值主键
     * @return 结果
     */
    @Override
    public void deleteAccRechargeImportByRechargeImportIds(Long[] rechargeImportIds) {
        for(Long rechargeImportId : rechargeImportIds){
            this.deleteAccRechargeImport(rechargeImportId);
        }
    }

    /**
     * 获得后台导入充值
     *
     * @param rechargeImportId ${pkColumn.columnComment}
     * @return 后台导入充值
     */
    @Override
    public AccRechargeImport getAccRechargeImport(Long rechargeImportId) {
        return accRechargeImportMapper.selectById(rechargeImportId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccRechargeImportRespVO> getAccRechargeImportPage(AccRechargeImportPageReqVO pageReqVO) {
        Page<AccRechargeImportRespVO> page = PageUtils.startPage(pageReqVO);
        List<AccRechargeImportRespVO> list = accRechargeImportMapper.selectPageExt(pageReqVO);
        return PageResult.result(page, list);
    }

    @Override
    public void disable(AccRechargeImportAuditReqVO reqVO) {
        AccRechargeImport rechargeImport = accRechargeImportMapper.selectById(reqVO.getRechargeImportId());
        if (rechargeImport.getRechargeImportState() != NumberPool.INT_ZERO) {
            throw exception(RECHARGE_IMPORT_STATE_ERR);
        }
        rechargeImport.setRechargeImportState(NumberPool.INT_TWO);
        accRechargeImportMapper.updateById(rechargeImport);
    }

    @Override
    @Transactional
    public void enable(AccRechargeImportAuditReqVO reqVO) {
        // 获取充值单据
        AccRechargeImport rechargeImport = accRechargeImportMapper.selectById(reqVO.getRechargeImportId());
        // 获取平台商联系手机号
        PartnerDto partnerDto = partnerApi.getBySysCode(rechargeImport.getSysCode()).getCheckedData();
        // 验证验证码
        SmsCodeValidRespDTO validRespDTO = smsApi.validateSmsCode(
                SmsCodeValidDTO.builder()
                        .code(reqVO.getCode())
                        .phone(partnerDto.getContactPhone())
                        .createIp(ServletUtils.getClientIP())
                        .build()
        ).getCheckedData();
        if (!validRespDTO.getSuccess()) {
            throw new RuntimeException(validRespDTO.getErrMsg());
        }

        // 需要CAS比较
        AccRechargeImport update = new AccRechargeImport();
        update.setRechargeImportId(reqVO.getRechargeImportId());
        update.setRechargeImportState(NumberPool.INT_ONE);
        update.setAuditBy(SecurityUtils.getUsername());
        int updated = accRechargeImportMapper.update(
                update,
                new LambdaQueryWrapperX<AccRechargeImport>()
                        .eq(AccRechargeImport::getRechargeImportId, reqVO.getRechargeImportId())
                        .eq(AccRechargeImport::getRechargeImportState, NumberPool.INT_ZERO)
        );
        if (updated == 0) {
            throw exception(RECHARGE_IMPORT_UPDATE_ERR);
        }
        // 执行绑定充值单
        List<AccRechargeImportDtl> rechargeImportDtls = rechargeImportDtlService.getDtls(reqVO.getRechargeImportId());
        for (AccRechargeImportDtl importDtl : rechargeImportDtls) {
            // 验证门店是否有效存在
            BranchDTO branchDTO = accountCacheService.getBranchDTO(importDtl.getBranchId());
            if (Objects.isNull(branchDTO)) {
                throw exception(RECHARGE_IMPORT_BRANCH_ERR, importDtl.getBranchId());
            }
            if (Objects.isNull(branchDTO.getAreaId())) {
                throw exception(RECHARGE_IMPORT_BRANCH_AREA_ERR, branchDTO.getBranchName());
            }
            AreaDTO areaDTO = accountCacheService.getAreaDTO(branchDTO.getAreaId());
            if (Objects.isNull(areaDTO) || Objects.isNull(areaDTO.getDcId())) {
                throw exception(RECHARGE_IMPORT_AREA_DC_ERR, branchDTO.getBranchName());
            }
            // 新增充值单
            AccRecharge recharge = new AccRecharge();
            recharge.setChargeType(NumberPool.INT_ONE);
            recharge.setPayWay("pc");
            recharge.setRechargeAmt(importDtl.getRechargeAmt());
            recharge.setFee(BigDecimal.ZERO);
            recharge.setRechargeMerchantType(MerchantTypeEnum.BRANCH.getType());
            recharge.setRechargeMerchantId(importDtl.getBranchId());
            recharge.setReceiveMerhcantId(areaDTO.getDcId());
            recharge.setReceiveMerchantType(MerchantTypeEnum.DC.getType());
            recharge.setState(NumberPool.INT_ZERO);
            recharge.setPlatform(PayChannelEnum.NONE.getCode());
            recharge.setRechargeNo("PCZ" + redisService.getUniqueNumber());
            recharge.setGiveAmt(BigDecimal.ZERO);
            recharge.setSource(RechargeSource.PC.getSource());
            accRechargeMapper.insert(recharge);
            // 调整成充值成功
            List<AccAccountFlow> accountFlows = accRechargeService.rechargeSuccess(recharge);
            // 变更用户流水
            accountFlows.forEach(accountFlowService::processFlow);
            // 绑定记录信息
            importDtl.setRechargeId(recharge.getRechargeId());
            rechargeImportDtlService.updateById(importDtl);
        }
    }

    @Override
    public void smsCode(Long rechargeImportId) {
        // 获取充值单据
        AccRechargeImport rechargeImport = accRechargeImportMapper.selectById(rechargeImportId);
        // 获取平台商联系手机号
        PartnerDto partnerDto = partnerApi.getBySysCode(rechargeImport.getSysCode()).getCheckedData();
        // 获取验证码信息
        SmsCodeReqDTO smsCodeReqDTO = new SmsCodeReqDTO(rechargeImport.getSysCode(), partnerDto.getContactPhone(), ServletUtils.getClientIP());
        smsApi.sendSmsCode(smsCodeReqDTO).checkError();
    }
}
