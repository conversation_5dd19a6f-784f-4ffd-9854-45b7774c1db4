package com.zksr.account.controller.balance;

import cn.hutool.core.collection.ListUtil;
import com.zksr.account.api.account.vo.AccAccountRespVO;
import com.zksr.account.controller.account.vo.AccAccountInfoReqVO;
import com.zksr.account.convert.account.AccountConvert;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.service.IAccBalanceService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 门店账户余额
 */
@Api(tags = "管理后台 - 账户余额", produces = "application/json")
@Validated
@RestController
@RequestMapping("/balance")
public class AccBalanceController {

    @Autowired
    private IAccBalanceService accBalanceService;




    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "account:account:add";
        /**
         * 编辑
         */
        public static final String EDIT = "account:account:edit";
        /**
         * 删除
         */
        public static final String DELETE = "account:account:remove";
        /**
         * 列表
         */
        public static final String LIST = "account:account:list";
        /**
         * 查询
         */
        public static final String GET = "account:account:query";
        /**
         * 查询平台商账户
         */
        public static final String GET_PARTNER = "account:account:query-partner";
        /**
         * 查询软件商账户
         */
        public static final String GET_SOFTWARE = "account:account:query-software";
        /**
         * 查询入驻商余额
         */
        public static final String GET_SUPPLIER = "account:balance:queryBySupplier";
        /**
         * 查询入驻商结算余额
         */
        public static final String GET_SUPPLIER_SETTLE = "account:account:query-supplier-settle";
        /**
         * 查询运营商账户
         */
        public static final String GET_DC = "account:account:query-dc";
        /**
         * 查询入驻商余额
         */
        public static final String GET_SUPPLIER_02 = "account:account:query-supplier";
        /**
         * 查询业务员账户
         */
        public static final String GET_COLONEL = "account:account:query-colonel";
        /**
         * 停用
         */
        public static final String DISABLE = "account:account:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "account:account:enable";
        /**
         * 修改入驻商授信金额
         */
        public static final String SUPPLIER_CREDIT = "account:account:update-supplier-credit";
        /**
         * 查询运营商储值可结算金额
         */
        public static final String GET_DC_SETTLE_LIST = "account:account:query-dc-settle-list";
    }
}
