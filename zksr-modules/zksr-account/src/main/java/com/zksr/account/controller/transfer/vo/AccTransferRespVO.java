package com.zksr.account.controller.transfer.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 账户转账单对象 acc_transfer
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@Data
@ApiModel("账户转账单 - acc_transfer Response VO")
public class AccTransferRespVO {
    private static final long serialVersionUID = 1L;

    /** 账户转账单id */
    @ApiModelProperty(value = "支付平台(数据字典);从订单")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long transferId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 订单结算id */
    @Excel(name = "订单结算id")
    @ApiModelProperty(value = "订单结算id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long settleId;

    /** 转出方账户id */
    @Excel(name = "转出方账户id")
    @ApiModelProperty(value = "转出方账户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sourceAccountId;

    /** 转入方账户id */
    @Excel(name = "转入方账户id")
    @ApiModelProperty(value = "转入方账户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long targetAccountId;

    /** 转账单号 */
    @Excel(name = "转账单号")
    @ApiModelProperty(value = "转账单号")
    private String transferNo;

    /** 转账金额 */
    @Excel(name = "转账金额")
    @ApiModelProperty(value = "转账金额")
    private BigDecimal transferAmt;

    /** 转账发起方解除金额 */
    @Excel(name = "转账发起方解除金额")
    @ApiModelProperty(value = "转账发起方解除金额")
    private BigDecimal settleAmt;

    /** 转账状态;0-已提交 1-处理中 2-已完成 3-转账失败 */
    @Excel(name = "转账状态;0-已提交 1-处理中 2-已完成 3-转账失败")
    @ApiModelProperty(value = "转账状态;0-已提交 1-处理中 2-已完成 3-转账失败")
    private Integer state;

    /** 处理时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "处理时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "处理时间")
    private Date processingTime;

    /** 完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    /** 支付平台(数据字典);从订单 */
    @Excel(name = "支付平台(数据字典);从订单")
    @ApiModelProperty(value = "支付平台(数据字典);从订单")
    private String platform;

}
