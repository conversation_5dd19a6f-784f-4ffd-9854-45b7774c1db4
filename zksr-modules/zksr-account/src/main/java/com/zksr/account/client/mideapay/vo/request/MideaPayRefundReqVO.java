package com.zksr.account.client.mideapay.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zksr.account.client.mideapay.vo.request.MideaPayBaseReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayRefundReqVO extends MideaPayBaseReqVO {
    /**
     * 退款订单号
     */
    @JsonProperty("out_refund_no")
    private String outRefundNo;

    /**
     * 提交订单时间
     */
    @JsonProperty("out_refund_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private LocalDateTime outRefundTime;


    /**
     * 货币类型
     */
    @JsonProperty("currency_type")
    private String currencyType;

    /**
     * 退款总金额
     */
    @JsonProperty("refund_total_amount")
    private String refundTotalAmount;

    /**
     * 退款金额
     */
    @JsonProperty("refund_amount")
    private String refundAmount;

    /**
     * 营销金额退款
     */
    @JsonProperty("refund_market_amount")
    private String refundMarketAmount;

    /**
     * 商户自定义信息
     */
    @JsonProperty("attach")
    private String attach;

    /**
     * 风控参数
     */
    @JsonProperty("risk_params")
    private String riskParams;

}
