package com.zksr.account.service.impl;

import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.api.balance.vo.AccBalanceFlowRespVO;
import com.zksr.account.domain.AccBalance;
import com.zksr.account.domain.AccBalanceFlow;
import com.zksr.account.enums.balance.ActionTypeEnum;
import com.zksr.account.mapper.AccBalanceFlowMapper;
import com.zksr.account.service.IAccBalanceFlowService;
import com.zksr.account.service.IAccBalanceService;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class AccBalanceFlowServiceImpl implements IAccBalanceFlowService {

    @Autowired
    private AccBalanceFlowMapper accBalanceFlowMapper;

    @Override
    public Long saveFlow(AccBalance accBalance, MemBranchBalanceDTO balanceDTO, ActionTypeEnum typeEnum) {
        AccBalanceFlow flow = new AccBalanceFlow();
        Date now = new Date();
        flow.setSysCode(accBalance.getSysCode()).setBalanceId(accBalance.getBalanceId()).setBranchId(accBalance.getBranchId()).setActionType(typeEnum.getCode())
                .setReferenceNo(balanceDTO.getTradeNo()).setReferenceSubNo(balanceDTO.getSourceTradeNo()).setOpAmt(balanceDTO.getOpAmt())
                .setBeforeAmt(balanceDTO.getBeforeAmt()).setAfterAmt(balanceDTO.getAfterAmt()).setRemark(balanceDTO.getRemark());
        flow.setCreateBy(balanceDTO.getOperUserName());
        flow.setCreateTime(now);
        flow.setUpdateBy(balanceDTO.getOperUserName());
        flow.setUpdateTime(now);
        accBalanceFlowMapper.insert(flow);
        return flow.getBalanceFlowId();
    }

    @Override
    public List<AccBalanceFlowRespVO> getBalanceFlowList(Long branchId, Integer actionType) {
        List<AccBalanceFlow> flowList = accBalanceFlowMapper.selectBalanceFlowList(branchId, actionType);
        if (ToolUtil.isEmpty(flowList)) {
            return Collections.emptyList();
        }
        List<AccBalanceFlowRespVO> flowRespVOList = HutoolBeanUtils.toBean(flowList, AccBalanceFlowRespVO.class);
        flowRespVOList.forEach(t -> {
            t.setActionTypeDesc(ActionTypeEnum.getValueByCode(t.getActionType()));
        });
        return flowRespVOList;
    }

    @Override
    public PageResult<AccBalanceFlowRespVO> getFlowPageList(MemBranchBalanceFlowDTO pageReqDTO) {
        PageResult<AccBalanceFlow> queryResult = accBalanceFlowMapper.selectPage(pageReqDTO);
        List<AccBalanceFlow> flowList = queryResult.getList();
        if (ToolUtil.isEmpty(flowList)) {
            return new PageResult<>(Collections.emptyList(), queryResult.getTotal());
        }
        List<AccBalanceFlowRespVO> flowRespVOList = HutoolBeanUtils.toBean(flowList, AccBalanceFlowRespVO.class);
        flowRespVOList.forEach(t -> {
            t.setActionTypeDesc(ActionTypeEnum.getValueByCode(t.getActionType()));
        });
        return new PageResult<>(flowRespVOList, queryResult.getTotal());
    }

    @Override
    public Boolean rollBackByFlowId(String balanceFlowId) {
        AccBalanceFlow flow = accBalanceFlowMapper.selectById(Long.parseLong(balanceFlowId));
        // 为空或者删除标识为1
        if (Objects.isNull(flow) || flow.getDeleteFlag().equals(NumberPool.INT_ONE)) {
            return true;
        }
        MemBranchBalanceDTO balanceDTO = new MemBranchBalanceDTO();
        balanceDTO.setBranchId(flow.getBranchId());
        balanceDTO.setSysCode(flow.getSysCode());
        BigDecimal opAmt = flow.getOpAmt();
        // 充值或取消订单回滚时，操作金额置为负数
        // 退款和订单支付回滚时，就用操作金额
        boolean isRechargeOrOrderCancel = ActionTypeEnum.isRechargeOrOrderCancel(flow.getActionType());
        if (isRechargeOrOrderCancel) {
            opAmt = opAmt.negate();
        }
        balanceDTO.setOpAmt(opAmt);
        // 避免互相依赖
        IAccBalanceService accBalanceService = SpringUtils.getBean(AccBalanceServiceImpl.class);
        boolean rollBackFlag = accBalanceService.rollBackBalance(balanceDTO);
        log.info("===============回滚门店：{}账户余额更新：{}", flow.getBranchId(), rollBackFlag);
        // 逻辑删除该流水记录
        flow.setDeleteFlag(NumberPool.INT_ONE);
        int rows = accBalanceFlowMapper.updateById(flow);
        log.info("===============回滚门店：{}删除流水记录：{}，操作：{}", flow.getBranchId(), balanceFlowId, rows);
        return true;
    }

}
