package com.zksr.account.service.pay.process;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderNotifyRespDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.pay.process.PayOrderProcessService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayWayEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.vo.PayRefundVO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.OrderPayInfoRespDTO;
import com.zksr.trade.api.order.dto.TrdSupplierResDto;
import com.zksr.trade.api.order.vo.TrdPayOrderPageVO;
import com.zksr.trade.api.order.vo.TrdSupplierPageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商场订单数据提供
 * @date 2024/3/7 16:13
 */
@Service
@Slf4j
public class MallPayOrderProcessServiceImpl extends PayOrderProcessService {

    @Autowired
    private OrderApi orderApi;
    @Autowired
    private AfterApi afterApi;

    @Autowired
    private IAccAccountService accountService;
    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccPlatformMerchantService merchantService;
    @Override
    public Integer getOrderType() {
        return OrderTypeConstants.MALL;
    }

    @Override
    public PayOrderDTO validateOrder(PayOrderSubmitReqVO reqVO) {
        // 必须参数 ============================== start
        reqVO.setPayAmt(BigDecimal.ZERO);
        PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(reqVO.getSysCode());
        if (ToolUtil.isEmpty(payConfigDTO) || ToolUtil.isEmpty(payConfigDTO.getStoreOrderPayPlatform())) {
            throw new ServiceException("平台【"+MallSecurityUtils.getLoginMember().getSysCode()+"】未设置支付平台！");
        }
        /**
         * 根据订单号获取入驻商订单信息
         */
        TrdSupplierPageVO supplierOrderVo = new TrdSupplierPageVO();
        supplierOrderVo.setOrderNo(reqVO.getOrderNo());
        supplierOrderVo.setSysCode(reqVO.getSysCode());
        if (Objects.equals(PayWayEnum.ONLINE.getPayWay(), reqVO.getPayWay())) {
            supplierOrderVo.setStoreOrderPayPlatform(payConfigDTO.getStoreOrderPayPlatform());
        } else if (Objects.equals(PayWayEnum.WALLET.getPayWay(), reqVO.getPayWay())) {
            supplierOrderVo.setStoreOrderPayPlatform(PayChannelEnum.WALLET.getCode());
        } else {
            throw new ServiceException("支付平台参数错误！");
        }

        OrderPayInfoRespDTO orderPayInfoRespDTO = orderApi.getSupplierPayInfo(supplierOrderVo).getCheckedData();
        reqVO.setPayAmt(orderPayInfoRespDTO.getPayAmt());
        
        //!@支付 - 提交支付订单 - 2、校验订单 - 3、获取分账信息(构建OrderSettl 设置到 accPayFlow.settles )
        List<OrderSettlementDTO> settlements = orderPayInfoRespDTO.getSupplierList().stream().map(orderResDto -> {
            AccPlatformMerchant accMerchant = merchantService.getPlatformMerchant(orderResDto.getMerchantId(), orderResDto.getMerchantType(), payConfigDTO.getStoreOrderPayPlatform());

            OrderSettlementDTO orderSettlement = OrderSettlementDTO.builder()
                    //
                    .subAmt(orderResDto.getSubOrderAmt())
                    .amt(orderResDto.getSubOrderAmt().subtract(orderResDto.getSubPayFee()))
                    .subOrderNo(orderResDto.getSubOrderNo())
                    .merchantId(orderResDto.getMerchantId())
                    .merchantType(orderResDto.getMerchantType())
                    .build();
            // 分账信息  获取当前支付方式 属性【是否支持在线分账】值
            if (PayChannelEnum.getPayOnlineSupportDivide(payConfigDTO.getStoreOrderPayPlatform())) {
                orderSettlement.setAccountNo(ToolUtil.isEmpty(accMerchant) ? null : accMerchant.getAltMchNo());
            } else {
                orderSettlement.setAccountNo(orderResDto.getSupplierId()+"");
            }
            return orderSettlement;
        }).collect(Collectors.toList());

        PayOrderDTO dto = new PayOrderDTO();
        dto.setBusiId(orderPayInfoRespDTO.getOrderId());
        dto.setOrderType(reqVO.getOrderType());
        dto.setAppid(reqVO.getAppid());
        dto.setOrderNo(reqVO.getOrderNo());
        dto.setSysCode(reqVO.getSysCode());
        dto.setPayAmt(reqVO.getPayAmt());
        dto.setWalletGiveAmt(orderPayInfoRespDTO.getWalletGiveAmt());
        dto.setOpenid(reqVO.getOpenid());
        dto.setDistributionMode(orderPayInfoRespDTO.getDistributionMode());
        // 必须参数 ============================== end
        String itemInfo = StringUtils.join(orderPayInfoRespDTO.getSupplierList().stream().map(TrdSupplierResDto::getItemInfo).filter(Objects::nonNull).collect(Collectors.toList()), StringPool.COMMA);

        // 返回分账信息
        dto.setBody(StringUtils.isEmpty(itemInfo) ? "商城下单" : itemInfo);
        if (Objects.nonNull(settlements)) {
            dto.setSettlements(settlements);
        }
        log.info("订单【{}】，分润信息数据:{}", reqVO.getOrderNo(), JSON.toJSONString(dto));
        return dto;
    }

    @Override
    public PayOrderNotifyRespDTO notifyOrderSuccess(PayOrderRespDTO notify) {
        // 支付成功
        orderApi.orderPaySuccessCallback(HutoolBeanUtils.toBean(notify, TrdPayOrderPageVO.class)).getCheckedData();
        return PayOrderNotifyRespDTO.success("处理成功");
    }

    @Override
    public void notifyRefundSuccess(PayRefundRespDTO notify) {
        // 退款成功
        afterApi.afterRefundSuccessCallback(HutoolBeanUtils.toBean(notify, PayRefundVO.class)).getCheckedData();
    }

    @Override
    public void notifyRefundFailure(PayRefundRespDTO notify) {
        // 退款失败
        afterApi.afterRefundFailCallback(HutoolBeanUtils.toBean(notify, PayRefundVO.class)).getCheckedData();
    }
}
