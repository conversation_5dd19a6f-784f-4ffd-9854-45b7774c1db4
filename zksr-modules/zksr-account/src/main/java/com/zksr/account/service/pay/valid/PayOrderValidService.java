package com.zksr.account.service.pay.valid;

import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.UnifiedOrderValidateDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.enums.PayChannelEnum;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付订单渠道验证
 * @date 2024/10/17 9:38
 */
public interface PayOrderValidService {

    PayChannelEnum getPayChannel();

    /**
     * 发起支付前验证
     * @param reqVO
     * @return
     */
    UnifiedOrderValidateDTO processPayBeforeCommonValidate(PayOrderDTO reqVO);

    /**
     * 发起退款通用验证
     * @param reqDTO
     * @return
     */
    UnifiedOrderValidateDTO processRefundBeforeCommonValidate(PayRefundOrderSubmitReqVO reqDTO);
}
