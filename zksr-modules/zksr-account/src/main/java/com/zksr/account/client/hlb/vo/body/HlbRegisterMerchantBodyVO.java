package com.zksr.account.client.hlb.vo.body;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户进件信息
 * @date 2024/7/12 14:02
 */
@Data
public class HlbRegisterMerchantBodyVO {

    /**
     * 平台商商编
     */
    @ApiModelProperty("平台商商编")
    private String firstClassMerchantNo;

    /**
     * 商户订单号
     */
    @ApiModelProperty("商户订单号")
    private String orderNo;

    /**
     * 子商户签约名
     */
    @ApiModelProperty("子商户签约名")
    private String signName;

    /**
     * 展示名
     */
    @ApiModelProperty("展示名")
    private String showName;

    /**
     * 网站网址
     */
    @ApiModelProperty("网站网址")
    private String webSite;

    /**
     * 接入地址
     */
    @ApiModelProperty("接入地址")
    private String accessUrl;

    /**
     * 子商户类型
     */
    @ApiModelProperty("子商户类型")
    private String merchantType;

    /**
     * 法人名字
     */
    @ApiModelProperty("法人名字")
    private String legalPerson;

    /**
     * 法人身份证号
     */
    @ApiModelProperty("法人身份证号")
    private String legalPersonID;

    /**
     * 组织机构代码
     */
    @ApiModelProperty("组织机构代码")
    private String orgNum;

    /**
     * 营业执照号
     */
    @ApiModelProperty("营业执照号")
    private String businessLicense;

    /**
     * 子商户所在省份
     */
    @ApiModelProperty("子商户所在省份")
    private String province;

    /**
     * 子商户所在城市
     */
    @ApiModelProperty("子商户所在城市")
    private String city;

    /**
     * 区县编码
     */
    @ApiModelProperty("区县编码")
    private String regionCode;

    /**
     * 通讯地址
     */
    @ApiModelProperty("通讯地址")
    private String address;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String linkman;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String linkPhone;

    /**
     * 联系邮箱
     */
    @ApiModelProperty("联系邮箱")
    private String email;

    /**
     * 绑定手机
     */
    @ApiModelProperty("绑定手机")
    private String bindMobile;

    /**
     * 客服联系电话
     */
    @ApiModelProperty("客服联系电话")
    private String servicePhone;

    /**
     * 结算卡联行号
     */
    @ApiModelProperty("结算卡联行号")
    private String bankCode;

    /**
     * 结算账户
     */
    @ApiModelProperty("结算账户")
    private String accountName;

    /**
     * 结算账号
     */
    @ApiModelProperty("结算账号")
    private String accountNo;

    /**
     * 结算卡类型
     */
    @ApiModelProperty("结算卡类型")
    private String settleBankType;

    /**
     * 结算类型
     */
    @ApiModelProperty("结算类型")
    private String settlementPeriod;

    /**
     * 结算方式
     */
    @ApiModelProperty("结算方式")
    private String settlementMode;

    /**
     * 结算备注
     */
    @ApiModelProperty("结算备注")
    private String settlementRemark;

    /**
     * 经营类别
     */
    @ApiModelProperty("经营类别")
    private String merchantCategory;

    /**
     * 行业类型编码
     */
    @ApiModelProperty("行业类型编码")
    private String industryTypeCode;

    /**
     * 授权使用平台商秘钥
     */
    @ApiModelProperty("授权使用平台商秘钥")
    private Boolean authorizationFlag;

    /**
     * 银联二维码
     */
    @ApiModelProperty("银联二维码")
    private String unionPayQrCode;

    /**
     * 是否需要开通 POS 功能
     */
    @ApiModelProperty("是否需要开通 POS 功能")
    private Boolean needPosFunction;

    /**
     * 法人身份证开始日期
     */
    @ApiModelProperty("法人身份证开始日期")
    private String idCardStartDate;

    /**
     * 法人身份证结束日期
     */
    @ApiModelProperty("法人身份证结束日期")
    private String idCardEndDate;

    /**
     * 经营起始日期
     */
    @ApiModelProperty("经营起始日期")
    private String businessDateStart;

    /**
     * 经营期限
     */
    @ApiModelProperty("经营期限")
    private String businessDateLimit;

    /**
     * 开户人身份证
     */
    @ApiModelProperty("开户人身份证")
    private String accountIdCard;

    /**
     * 银联 mcc 码
     */
    @ApiModelProperty("银联 mcc 码")
    private String mcc;

    /**
     * 是否同意协议
     */
    @ApiModelProperty("是否同意协议")
    private Boolean agreeProtocol;

    /**
     * 回调地址
     */
    @ApiModelProperty("回调地址")
    private String callbackUrl;

    /**
     * 结算模式
     */
    @ApiModelProperty("结算模式")
    private String settleMode;

    /**
     * 结算信息鉴权
     */
    @ApiModelProperty("结算信息鉴权")
    private String settlementAuth;

    /**
     * 注册地址
     */
    @ApiModelProperty("注册地址")
    private String postalAddress;

    /**
     * 小微经营类型
     */
    @ApiModelProperty("小微经营类型")
    private String microBizType;

    /**
     * 证书类型
     */
    @ApiModelProperty("证书类型")
    private String certType;

    /**
     * 联系人身份证号
     */
    @ApiModelProperty("联系人身份证号")
    private String linkManId;

    /**
     * 是否需要认证
     */
    @ApiModelProperty("是否需要认证")
    private Boolean needAuthorize;

    /**
     * 是否需要特殊处理商户名称
     */
    @ApiModelProperty("是否需要特殊处理商户名称")
    private Boolean specialSignName;

    /**
     * 法人证件类型
     */
    @ApiModelProperty("法人证件类型")
    private String idType;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private String latitude;

    /**
     * 电子账户
     */
    @ApiModelProperty("电子账户")
    private String electronicAccountNo;

    /**
     * 电子账户户名
     */
    @ApiModelProperty("电子账户户名")
    private String electronicAccountName;

    /**
     * 电子账户类型
     */
    @ApiModelProperty("电子账户类型")
    private String electronicSettleBankType;

    /**
     * 结算到账方式
     */
    @ApiModelProperty("结算到账方式")
    private String settleChangeType;

    /**
     * 结算人身份证号
     */
    @ApiModelProperty("结算人身份证号")
    private String settlementIdCardNo;

    /**
     * 结算人手机号
     */
    @ApiModelProperty("结算人手机号")
    private String settlementPhoneNo;

    /**
     * 支付宝商户服务类型
     */
    @ApiModelProperty("支付宝商户服务类型")
    private String serviceCodes;

    /**
     * 联系人类型
     */
    @ApiModelProperty("联系人类型")
    private String linkmanType;

    /**
     * 联系人证件类型
     */
    @ApiModelProperty("联系人证件类型")
    private String linkmanIdType;

    /**
     * 联系人证件有效期开始时间
     */
    @ApiModelProperty("联系人证件有效期开始时间")
    private String linkmanIdCardStartDate;

    /**
     * 联系人证件有效期结束时间
     */
    @ApiModelProperty("联系人证件有效期结束时间")
    private String linkmanIdCardEndDate;

    /**
     * 证件持有人类型
     */
    @ApiModelProperty("证件持有人类型")
    private String idHolderType;

    /**
     * 法人证件居住地址
     */
    @ApiModelProperty("法人证件居住地址")
    private String legalPersonIdAddress;

    /**
     * 经营者/法人是否为受益人
     */
    @ApiModelProperty("经营者/法人是否为受益人")
    private Boolean enterpriseOwner;

    /**
     * 受益人证件类型
     */
    @ApiModelProperty("受益人证件类型")
    private String benefLegalPersonIdType;

    /**
     * 受益人证件姓名
     */
    @ApiModelProperty("受益人证件姓名")
    private String benefLegalPerson;

    /**
     * 受益人证件号码
     */
    @ApiModelProperty("受益人证件号码")
    private String benefLegalPersonID;

    /**
     * 受益人证件居住地址
     */
    @ApiModelProperty("受益人证件居住地址")
    private String benefAddress;

    /**
     * 受益人证件有效期开始时间
     */
    @ApiModelProperty("受益人证件有效期开始时间")
    private String benefIdCardStartDate;

    /**
     * 受益人证件有效期结束时间
     */
    @ApiModelProperty("受益人证件有效期结束时间")
    private String benefIdCardEndDate;
}
