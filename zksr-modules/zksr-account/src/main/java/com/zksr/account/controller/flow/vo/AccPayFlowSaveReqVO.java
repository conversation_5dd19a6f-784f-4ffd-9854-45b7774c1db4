package com.zksr.account.controller.flow.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 支付流水对象 acc_pay_flow
 *
 * <AUTHOR>
 * @date 2024-03-10
 */
@Data
@ApiModel("支付流水 - acc_pay_flow分页 Request VO")
public class AccPayFlowSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 支付流水id */
    @ApiModelProperty(value = "是否分账  0-否 1-是")
    private Long payFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 支付类型 0-支付 1-退款 */
    @Excel(name = "支付类型 0-支付 1-退款")
    @ApiModelProperty(value = "支付类型 0-支付 1-退款")
    private Integer payType;

    /** 支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号 */
    @Excel(name = "支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号")
    @ApiModelProperty(value = "支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号")
    private String tradeNo;

    /** 支付平台商户订单号 */
    @Excel(name = "支付平台商户订单号")
    @ApiModelProperty(value = "支付平台商户订单号")
    private String outTradeNo;

    /** 退款单号;商城退款即是售后单号 */
    @Excel(name = "退款单号;商城退款即是售后单号")
    @ApiModelProperty(value = "退款单号;商城退款即是售后单号")
    private String refundNo;

    /** 支付平台商户退款单号 */
    @Excel(name = "支付平台商户退款单号")
    @ApiModelProperty(value = "支付平台商户退款单号")
    private String outRefundNo;

    /** 支付平台流水号;一般回调会返回 */
    @Excel(name = "支付平台流水号;一般回调会返回")
    @ApiModelProperty(value = "支付平台流水号;一般回调会返回")
    private String outFlowNo;

    /** 支付方式;0-在线支付 1-储值支付 */
    @Excel(name = "支付方式;0-在线支付 1-储值支付")
    @ApiModelProperty(value = "支付方式;0-在线支付 1-储值支付")
    private String payWay;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;

    /** 回调标识 0-未回调 1-已回调 */
    @Excel(name = "回调标识 0-未回调 1-已回调")
    @ApiModelProperty(value = "回调标识 0-未回调 1-已回调")
    private Integer callbackFlag;

    /** 回调时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "回调时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "回调时间")
    private Date callbackTime;

    /** 支付金额 */
    @Excel(name = "支付金额")
    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmt;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @ApiModelProperty(value = "退款金额")
    private BigDecimal refundAmt;

    /** 手续费 */
    @Excel(name = "手续费")
    @ApiModelProperty(value = "手续费")
    private BigDecimal fee;

    /** 订单类型 0-商城订单  1-入驻商充值  2-门店充值 */
    @Excel(name = "订单类型 0-商城订单  1-入驻商充值  2-门店充值")
    @ApiModelProperty(value = "订单类型 0-商城订单  1-入驻商充值  2-门店充值")
    private Integer orderType;

    /** 是否分账  0-否 1-是 */
    @Excel(name = "是否分账  0-否 1-是")
    @ApiModelProperty(value = "是否分账  0-否 1-是")
    private Integer isDivide;

}
