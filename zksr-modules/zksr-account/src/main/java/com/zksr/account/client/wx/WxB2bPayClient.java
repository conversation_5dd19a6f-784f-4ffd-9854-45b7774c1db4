package com.zksr.account.client.wx;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.account.client.PayClient;
import com.zksr.account.model.pay.dto.order.PayInfoDTO;
import com.zksr.account.model.pay.vo.*;
import com.zksr.account.util.WxB2bPayUtil;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.security.utils.WxHttpUtil;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayOrderStatusRespEnum;
import com.zksr.common.core.enums.PayRefundStatusEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.utils.RedisWxTokenUtil;
import com.zksr.system.api.model.dto.PayConfigDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信b2b小程序支付
 * @date 2024/8/9 9:37
 */
@Slf4j
@Data
public class WxB2bPayClient implements PayClient {



    /**
     * appid
     */
    private String appId;

    @Override
    public String getChannel() {return PayChannelEnum.WX_B2B_LETE.getCode();}

    @Override
    public String getPlatform() {return PayChannelEnum.WX_B2B_PAY.getCode();}

    @Override
    public void setConfig(PayConfigDTO config, String appid) throws Exception {}

    /**
     * b2b 小程序支付
     * <a href="https://developers.weixin.qq.com/miniprogram/dev/api/payment/wx.requestCommonPayment.html">参考开发文档</a>
     * <a href="https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/industry/B2b_store_assistant.html">参考开发文档</a>
     * <a href="https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/ministore/wxafunds/callback/create_order.html">支付回调</a>
     * @param payOrderDTO 下单信息
     * @return
     */
    @Override
    public PayOrderRespDTO unifiedOrder(PayOrderDTO payOrderDTO) {
        log.info("发起微信B2B支付入参={}, syscode={}", JSON.toJSONString(payOrderDTO), payOrderDTO.getSysCode());

        if(payOrderDTO.isCombinePay()){
            return unifiedOrderNew(payOrderDTO);
        }

        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        // 支付发起者信息
        AccPlatformMerchant platformMerchant = WxB2bPayUtil.getPaySponsorMerchant(payOrderDTO.getSettlements(), payOrderDTO.getOrderType(), getPlatform());
        if (ObjectUtil.isEmpty(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchKey()) ) {
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setMessage("商户或者支付秘钥未配置");
            return respDTO;
        }

        // 	可通过小程序MP查看：门店助手 -> 支付管理 -> 商户号管理查看详情->基本配置中的沙箱AppKey和现网AppKey。注意：记得根据env值选择不同AppKey，env = 0对应现网AppKey，env = 1对应沙箱AppKey
        // https://developers.weixin.qq.com/minigame/dev/api-backend/open-api/login/auth.code2Session.html
        String sessionKey = payOrderDTO.getSessionKey();

        HashMap<Object, Object> signData = new HashMap<>();
        // b2b 支付小程序后台商户号
        signData.put("mchid", platformMerchant.getAltMchNo());    //  由微信支付生成并下发的商户号。示例值：**********
        signData.put("out_trade_no", String.valueOf(payOrderDTO.getFlowId())); // 商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一，长度限制为[6,32]。示例值：1217752501201407033233368018
        signData.put("description", StringUtils.replaceJsonErr(payOrderDTO.getBody()));   //  商品描述。示例值：Image形象店-深圳腾大-QQ公仔

        // 自定义参数
        HashMap<Object, Object> attach = new HashMap<>();
        attach.put("sysCode", payOrderDTO.getSysCode());
        attach.put("orderType", payOrderDTO.getOrderType());
        attach.put("appid", payOrderDTO.getAppid());
        signData.put("attach", StringUtils.format("sysCode={}&orderType={}&appid={}&orderNo={}", payOrderDTO.getSysCode(), payOrderDTO.getOrderType(), payOrderDTO.getAppid(), payOrderDTO.getOrderNo()));

        HashMap<Object, Object> amount = new HashMap<>();
        amount.put("order_amount", payOrderDTO.getPayAmt().multiply(new BigDecimal("100")).setScale(0));    //  订单总需支付金额，也即是真正下单总金额，单位为分。示例值：1300

        signData.put("amount", amount);   //订单金额信息。
        signData.put("env", 0);   //  0	生产环境/现网环境, 1	沙箱环境/测试环境
        signData.put("need_profit_sharing", 1);   //  0-不需要分账, 1-需要分账

        /*
        时间: 2024年12月5日08:49:43
        需求人: 谭湘江:
        需求: 不分账也需要冻结资金, 不允许提现, 所以默认需要分账*/
        if (Objects.isNull(payOrderDTO.getSettlements())
                || payOrderDTO.getSettlements().isEmpty()
                || payOrderDTO.getSettlements().size() == 1
        ) {
            // 如果没有分账信息, 或者只有一条分账信息, 就不需要分账
            signData.put("need_profit_sharing", 0);   //  0-不需要分账, 1-需要分账
        }

        String signStr = JSON.toJSONString(signData);

        // 有效返回参数
        HashMap<Object, Object> rawData = new HashMap<>();
        rawData.put("mode", "retail_pay_goods");
        rawData.put("signData", signStr);
        rawData.put("paySig", calcPaySig("requestCommonPayment", signStr, platformMerchant.getAltMchKey()));
        rawData.put("signature", calcSignature(signStr, sessionKey));

        log.info("微信小程序b2b加密数据={}", JSON.toJSONString(rawData));

        // 构建标准返回数据体 http://release-b2b-api.zhongkeshangruan.cn/account
        respDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus())
                .setOrderNo(payOrderDTO.getOrderNo())
                .setRawData(rawData)
                // 发起支付信息
                .setPayInfoDTO(
                        PayInfoDTO.builder()
                                .merchantNo(platformMerchant.getAltMchNo())
                                .merchantId(platformMerchant.getMerchantId())
                                .openid(payOrderDTO.getOpenid())
                                .build()
                )
        ;
        return respDTO;
    }

    /**
     * 新微信B2B合单支付
     * @param payOrderDTO
     * @return
     */
    public PayOrderRespDTO unifiedOrderNew(PayOrderDTO payOrderDTO) {
        log.info("发起微信B2B支付入参={}", JSON.toJSONString(payOrderDTO));
        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        if(CollectionUtils.isEmpty(payOrderDTO.getSettlements())){
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setMessage("没有设置分账商户信息.");
            return respDTO;
        }
        /**
         * 合单支付文档：
         * https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/industry/B2b_store_assistant.html#_3-7-%E5%90%88%E5%8D%95%E6%94%AF%E4%BB%98
         */
        WxCombineSignData wxCombineSignData = WxCombineSignData.builder().build();

//        wxCombineSignData.setMode("retail_pay_combined_goods");

        List<CombinedOrder> combinedOrderList = new ArrayList<>();
        List<PaySig> paySigList = new ArrayList<>();
        for(OrderSettlementDTO orderSettlementDTO : payOrderDTO.getSettlements()){
            //构造参数适配原有方法请求
            List<OrderSettlementDTO> tempList = new ArrayList<>();
            tempList.add(orderSettlementDTO);

            // 支付发起者信息
            AccPlatformMerchant platformMerchant = WxB2bPayUtil.getPaySponsorMerchant(tempList, payOrderDTO.getOrderType(), getPlatform());
            if (ObjectUtil.isEmpty(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchKey()) ) {
                respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                        .setMessage("商户或者支付秘钥未配置,入驻商单号["+orderSettlementDTO.getSubOrderNo()+"]");
                return respDTO;
            }

            CombinedOrder combinedOrder = CombinedOrder.builder()
                    .attach(StringUtils.format("sysCode={}&orderType={}&appid={}&orderNo={}", payOrderDTO.getSysCode(), payOrderDTO.getOrderType(), payOrderDTO.getAppid(), orderSettlementDTO.getSubOrderNo()))
                    .description(StringUtils.replaceJsonErr(payOrderDTO.getBody()))
                    .needProfitSharing(0)
                    .outTradeNo(String.valueOf(orderSettlementDTO.getFlowId()))
                    .mchid(platformMerchant.getAltMchNo())
                    .amount(Amount.builder()
                            .orderAmount(orderSettlementDTO.getSubAmt().multiply(new BigDecimal("100")).setScale(0))
                            .build())
                    .build();

            String signStr = JSON.toJSONString(combinedOrder);
            PaySig paySig = PaySig.builder()
                    .mchid(platformMerchant.getAltMchNo())
                    .altMchKey(platformMerchant.getAltMchKey())
//                    .paysig(calcPaySig("requestCommonPayment", signStr, platformMerchant.getAltMchKey()))
                    .build();
            paySigList.add(paySig);
            combinedOrderList.add(combinedOrder);
        }

        wxCombineSignData.setCombinedOrderList(combinedOrderList);
        wxCombineSignData.setEnv(0);

        // 	可通过小程序MP查看：门店助手 -> 支付管理 -> 商户号管理查看详情->基本配置中的沙箱AppKey和现网AppKey。注意：记得根据env值选择不同AppKey，env = 0对应现网AppKey，env = 1对应沙箱AppKey
        // https://developers.weixin.qq.com/minigame/dev/api-backend/open-api/login/auth.code2Session.html
        String sessionKey = payOrderDTO.getSessionKey();

        HashMap<Object, Object> signData = new HashMap<>();

        String signStr = JSON.toJSONString(wxCombineSignData);

        HashMap<Object, Object> paySigMap = new HashMap<>();

        for (PaySig paySig : paySigList) {
            String sig = calcPaySig("requestCommonPayment", signStr, paySig.getAltMchKey());
            paySig.setPaysig(sig);
        }

        paySigList.forEach(paySig -> {paySig.setAltMchKey(null);});

        PropertyFilter filter = (Object object, String name, Object value) -> value != null;

        // 有效返回参数
        HashMap<Object, Object> rawData = new HashMap<>();
        rawData.put("mode", "retail_pay_combined_goods");
        rawData.put("signData", signStr);
        rawData.put("paySig", com.alibaba.fastjson.JSON.toJSONString(paySigList, filter));
        rawData.put("signature", calcSignature(signStr, sessionKey));

        log.info("微信小程序b2b加密数据new={}", JSON.toJSONString(rawData));

        // 构建标准返回数据体 http://release-b2b-api.zhongkeshangruan.cn/account
        respDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus())
                .setOrderNo(payOrderDTO.getOrderNo())
                .setRawData(rawData)
                // 发起支付信息
                .setPayInfoDTO(
                        PayInfoDTO.builder()
//                                .merchantNo(platformMerchant.getAltMchNo())
//                                .merchantId(platformMerchant.getMerchantId())
                                .openid(payOrderDTO.getOpenid())
                                .build()
                )
        ;
        return respDTO;
    }

    public PayOrderRespDTO unifiedOrderNew2(PayOrderDTO payOrderDTO) {
        log.info("发起微信B2B支付入参={}, syscode={}", JSON.toJSONString(payOrderDTO), payOrderDTO.getSysCode());

        if(payOrderDTO.isCombinePay()){
            return unifiedOrderNew(payOrderDTO);
        }

        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        // 支付发起者信息
        AccPlatformMerchant platformMerchant = WxB2bPayUtil.getPaySponsorMerchant(payOrderDTO.getSettlements(), payOrderDTO.getOrderType(), getPlatform());
        if (ObjectUtil.isEmpty(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchKey()) ) {
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setMessage("商户或者支付秘钥未配置");
            return respDTO;
        }

        // 	可通过小程序MP查看：门店助手 -> 支付管理 -> 商户号管理查看详情->基本配置中的沙箱AppKey和现网AppKey。注意：记得根据env值选择不同AppKey，env = 0对应现网AppKey，env = 1对应沙箱AppKey
        // https://developers.weixin.qq.com/minigame/dev/api-backend/open-api/login/auth.code2Session.html
        String sessionKey = payOrderDTO.getSessionKey();

        HashMap<Object, Object> signData = new HashMap<>();
        // b2b 支付小程序后台商户号
        signData.put("mchid", platformMerchant.getAltMchNo());    //  由微信支付生成并下发的商户号。示例值：**********
        signData.put("out_trade_no", String.valueOf(payOrderDTO.getFlowId())); // 商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一，长度限制为[6,32]。示例值：1217752501201407033233368018
        signData.put("description", StringUtils.replaceJsonErr(payOrderDTO.getBody()));   //  商品描述。示例值：Image形象店-深圳腾大-QQ公仔

        // 自定义参数
        HashMap<Object, Object> attach = new HashMap<>();
        attach.put("sysCode", payOrderDTO.getSysCode());
        attach.put("orderType", payOrderDTO.getOrderType());
        attach.put("appid", payOrderDTO.getAppid());
        signData.put("attach", StringUtils.format("sysCode={}&orderType={}&appid={}&orderNo={}", payOrderDTO.getSysCode(), payOrderDTO.getOrderType(), payOrderDTO.getAppid(), payOrderDTO.getOrderNo()));

        HashMap<Object, Object> amount = new HashMap<>();
        amount.put("order_amount", payOrderDTO.getPayAmt().multiply(new BigDecimal("100")).setScale(0));    //  订单总需支付金额，也即是真正下单总金额，单位为分。示例值：1300

        signData.put("amount", amount);   //订单金额信息。
        signData.put("env", 0);   //  0	生产环境/现网环境, 1	沙箱环境/测试环境
        signData.put("need_profit_sharing", 1);   //  0-不需要分账, 1-需要分账

        /*
        时间: 2024年12月5日08:49:43
        需求人: 谭湘江:
        需求: 不分账也需要冻结资金, 不允许提现, 所以默认需要分账*/
        if (Objects.isNull(payOrderDTO.getSettlements())
                || payOrderDTO.getSettlements().isEmpty()
                || payOrderDTO.getSettlements().size() == 1
        ) {
            // 如果没有分账信息, 或者只有一条分账信息, 就不需要分账
            signData.put("need_profit_sharing", 0);   //  0-不需要分账, 1-需要分账
        }

        String signStr = JSON.toJSONString(signData);

        // 有效返回参数
        HashMap<Object, Object> rawData = new HashMap<>();
        rawData.put("mode", "retail_pay_goods");
        rawData.put("signData", signStr);
        rawData.put("paySig", calcPaySig("requestCommonPayment", signStr, platformMerchant.getAltMchKey()));
        rawData.put("signature", calcSignature(signStr, sessionKey));

        log.info("微信小程序b2b加密数据={}", JSON.toJSONString(rawData));

        // 构建标准返回数据体 http://release-b2b-api.zhongkeshangruan.cn/account
        respDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus())
                .setOrderNo(payOrderDTO.getOrderNo())
                .setRawData(rawData)
                // 发起支付信息
                .setPayInfoDTO(
                        PayInfoDTO.builder()
                                .merchantNo(platformMerchant.getAltMchNo())
                                .merchantId(platformMerchant.getMerchantId())
                                .openid(payOrderDTO.getOpenid())
                                .build()
                )
        ;
        return respDTO;
    }

    @Override
    public PayOrderRespDTO parseOrderNotify(Map<String, String> params, String body) {


        return null;
    }

    @Override
    public PayOrderRespDTO getOrder(PayOrderQueryVO payOrderQueryVO) {
        OrderSettlementDTO paySettle;
        if (payOrderQueryVO.getPayFlow().getOrderType().equals(OrderTypeConstants.BRANCH_CHARGE)) {
            paySettle = payOrderQueryVO.getPayFlow().buildSettle().stream().filter(item -> MerchantTypeEnum.DC.getType().equals(item.getMerchantType())).findFirst().get();
        } else {
            paySettle = payOrderQueryVO.getPayFlow().buildSettle().stream().filter(item -> MerchantTypeEnum.isSupplier(item.getMerchantType())).findFirst().get();
        }
        HashMap<String, String> signData = new HashMap<>();
        signData.put("out_trade_no", payOrderQueryVO.getPayFlow().getPayFlowId().toString());
        signData.put("mchid", paySettle.getAccountNo());
        // 小程序accessToken
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(appId);
        // 请求数据
        String signStr = JSON.toJSONString(signData);
        // 获取支付商户信息
        AccPlatformMerchant platformMerchant = SpringUtils.getBean(IAccPlatformMerchantService.class).getPlatformMerchant(paySettle.getMerchantId(), paySettle.getMerchantType(), this.getPlatform());
        // 接口签名
        String paySig = calcPaySig("/retail/B2b/getorder", signStr, platformMerchant.getAltMchKey());
        String url = StringUtils.format("https://api.weixin.qq.com/retail/B2b/getorder?access_token={}&pay_sig={}", accessToken, paySig);
        log.info("查询B2B支付订单状态, url={}", url);
        String response = WxHttpUtil.post(url, signStr);
        log.info("查询B2B支付订单状态结果={}", response);
        JSONObject result = JSON.parseObject(response);
        if (result.getInteger("errcode") != 0) {
            return null;
        }
        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        if ("ORDER_PAY_SUCC".equals(result.getString("pay_status"))) {
            respDTO.setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                    .setOrderNo(payOrderQueryVO.getPayFlow().getTradeNo())
                    .setPayPlatform(this.getPlatform())
                    .setFlowId(payOrderQueryVO.getPayFlow().getPayFlowId())
                    .setOutTradeNo(result.getString("wxpay_transaction_id"));
        } else {
            respDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus())
                    .setOrderNo(payOrderQueryVO.getPayFlow().getTradeNo());
        }
        return respDTO;
    }

    @Override
    public PayRefundRespDTO unifiedRefund(PayRefundOrderSubmitReqVO reqDTO) {
        PayRefundRespDTO respDTO = new PayRefundRespDTO();
        PayRefundRespDTO refundRespDTO = new PayRefundRespDTO();

        String accessToken = RedisWxTokenUtil.getAppletAccessToken(appId);
        if (StringUtils.isEmpty(accessToken)) {
            log.error("发起微信b2b支付退款, accessToken不存在, appid={}", appId);
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setErrorMsg("accessToken 不存在");
            return respDTO;
        }

        List<OrderSettlementDTO> supplierSettleDTOList = reqDTO.getPayFlowDTO().buildSettle().stream().filter(item -> MerchantTypeEnum.isSupplier(item.getMerchantType())).collect(Collectors.toList());
        if (supplierSettleDTOList.isEmpty()) {
            refundRespDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setRefundNo(reqDTO.getRefundNo())
                    .setOrderNo(reqDTO.getOrderNo())
                    .setErrorMsg("无效的入驻商商户信息");
            return refundRespDTO;
        }

        OrderSettlementDTO supplierSettleDTO = supplierSettleDTOList.get(0);
        AccPlatformMerchant platformMerchant = SpringUtils.getBean(IAccPlatformMerchantService.class).getPlatformMerchant(supplierSettleDTO.getMerchantId(), supplierSettleDTO.getMerchantType(), getPlatform());
        if ( StringUtils.isEmpty(platformMerchant.getAltMchKey()) ) {
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setErrorMsg("appKey未配置");
            return respDTO;
        }

        // 获取退款签名信息
        HashMap<Object, Object> signData = getRefundSigData(reqDTO, supplierSettleDTOList);
        signData.put("mchid", supplierSettleDTO.getAccountNo());    //  由微信支付生成并下发的商户号。示例值：**********

        String signStr = JSON.toJSONString(signData);
        String paySig = calcPaySig("/retail/B2b/refund", signStr, platformMerchant.getAltMchKey());
        String url = StringUtils.format("https://api.weixin.qq.com/retail/B2b/refund?access_token={}&pay_sig={}", accessToken, paySig);

        log.info("微信b2b支付退款, url={}", url);
        log.info("微信b2b支付退款参数={}", signStr);
        // 接口
        String response = WxHttpUtil.post(url, signStr);
        // {"refund_id":"r2024081419300731684","out_refund_no":"********************","order_id":"o20240814160231127759951","out_trade_no":"********************","errcode":0,"errmsg":"OK"}
        log.info("微信b2b支付退款结果={}", response);

        JSONObject resObj = JSON.parseObject(response);
        if ("0".equals(resObj.getString("errcode"))) {
            refundRespDTO.setStatus(PayRefundStatusEnum.PROCESSING.getStatus())
                    .setRefundNo(reqDTO.getRefundNo())
                    .setOrderNo(reqDTO.getOrderNo())
                    .setRefundTradeNo(reqDTO.getRefundFlowDTO().getPayFlowId().toString())
                    .setOutRefundNo(resObj.getString("out_refund_no"));

            // b2b支付退款没有提供退款回调, 需要自行处理
            try {
                AccountMqProducer mqProducer = SpringUtils.getBean(AccountMqProducer.class);
                mqProducer.sendRefundDelayQuery(
                        PayRefundQueryVO.builder()
                                .payFlowId(reqDTO.getFlowId())
                                .refundFlowId(reqDTO.getRefundFlowDTO().getPayFlowId())
                                .refundNo(reqDTO.getRefundNo())
                                .tradeNo(reqDTO.getOrderNo())
                                .build()
                );
            } catch (Exception e) {
                log.error("发起延迟退款处理异常, refundNo={}", reqDTO.getRefundNo());
                log.error("发起延迟退款处理异常", e);
            }
        } else {
            refundRespDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setRefundNo(reqDTO.getRefundNo())
                    .setOrderNo(reqDTO.getOrderNo())
                    .setRefundTradeNo(reqDTO.getRefundFlowDTO().getPayFlowId().toString())
                    .setErrorMsg(resObj.getString("errmsg"));
        }
        return refundRespDTO;
    }


    @Override
    public PayRefundRespDTO getRefund(PayRefundQueryVO refundQueryVO) {
        PayRefundRespDTO respDTO = new PayRefundRespDTO();
        respDTO.setRefundNo(refundQueryVO.getRefundFlowId().toString());
        respDTO.setOrderNo(refundQueryVO.getTradeNo());
        // 查询退款结果
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(appId);
        if (StringUtils.isEmpty(accessToken)) {
            log.error("查询微信b2b支付退款, accessToken不存在, appid={}", appId);
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setErrorMsg("accessToken 不存在");
            return respDTO;
        }

        // 入驻商结算信息, 里面有入驻商商户号
        List<OrderSettlementDTO> supplierSettleDTOList = refundQueryVO.getPayFlowDTO().buildSettle().stream().filter(item -> MerchantTypeEnum.isSupplier(item.getMerchantType())).collect(Collectors.toList());
        if (supplierSettleDTOList.isEmpty()) {
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setErrorMsg("无效的入驻商商户信息");
            return respDTO;
        }
        OrderSettlementDTO supplierSettleDTO = supplierSettleDTOList.get(0);
        AccPlatformMerchant platformMerchant = SpringUtils.getBean(IAccPlatformMerchantService.class).getPlatformMerchant(supplierSettleDTO.getMerchantId(), supplierSettleDTO.getMerchantType(), getPlatform());
        if ( StringUtils.isEmpty(platformMerchant.getAltMchKey()) ) {
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setErrorMsg("appKey未配置");
            return respDTO;
        }


        HashMap<Object, Object> signData = new HashMap<>();
        signData.put("mchid", supplierSettleDTO.getAccountNo());    //  由微信支付生成并下发的商户号。示例值：**********
        signData.put("out_refund_no", refundQueryVO.getRefundFlowId());
        String signStr = JSON.toJSONString(signData);
        String paySig = calcPaySig("/retail/B2b/getrefund", signStr, platformMerchant.getAltMchKey());
        String url = StringUtils.format("https://api.weixin.qq.com/retail/B2b/getrefund?access_token={}&pay_sig={}", accessToken, paySig);

        log.info("查询微信b2b支付退款, url={}", url);
        log.info("查询微信b2b支付退款参数={}", signStr);
        String response = WxHttpUtil.post(url, signStr);
        log.info("查询微信b2b支付退款结果={}", response);

        JSONObject resObj = JSON.parseObject(response);
        if ("0".equals(resObj.getString("errcode")) && "REFUND_SUCC".equals(resObj.getString("refund_status"))) {
            // 退款成功
            respDTO.setStatus(PayRefundStatusEnum.SUCCESS.getStatus())
                    .setOutRefundNo(resObj.getString("wxpay_refund_id"));

        } else if ("0".equals(resObj.getString("errcode")) && "REFUND_PROCESSING".equals(resObj.getString("refund_status"))) {
            // 退款中
            respDTO.setStatus(PayRefundStatusEnum.PROCESSING.getStatus())
                    .setOutRefundNo(resObj.getString("wxpay_refund_id"));

        } else if ("0".equals(resObj.getString("errcode")) && "REFUND_FAIL".equals(resObj.getString("refund_status"))) {
            // 失败
            respDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setOutRefundNo(resObj.getString("wxpay_refund_id"))
                    .setErrorMsg(resObj.getString("refund_desc"));
        } else {
            // 退款中
            respDTO.setStatus(PayRefundStatusEnum.PROCESSING.getStatus())
                    .setErrorMsg(resObj.getString("errmsg"));
        }
        return respDTO;
    }

    @Override
    public PayRefundRespDTO parseRefundNotify(Map<String, String> params, String body) {
        return null;
    }

    /**
     * 请求微信B2B支付分账
     * @param mchId             发起支付商户ID
     * @param outTradeNo        系统订单号
     * @param amt               分账金额
     * @param receiverMchId     接受方分账号
     */
    public CreateDivideRespVO divide(String mchId, String outTradeNo, BigDecimal amt, String receiverMchId, String appKey) {
        // 查询退款结果
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(appId);
        if (StringUtils.isEmpty(accessToken)) {
            throw new ServiceException("accessToken 不存在");
        }
        HashMap<Object, Object> signData = new HashMap<>();
        signData.put("mchid", mchId);    //  由微信支付生成并下发的商户号。示例值：**********
        signData.put("out_trade_no", outTradeNo);
        signData.put("profit_fee", amt.multiply(new BigDecimal("100")).longValue());
        if (NumberUtil.isNumber(receiverMchId)) {
            // 纯数字是商户号
            signData.put("receiver_type", "PAYEE_TYPE_EXTERNAL_MERCHANT");
        } else {
            // 否则是openid对私
            signData.put("receiver_type", "PAYEE_TYPE_EXTERNAL_USER");
        }
        signData.put("receiver_account", receiverMchId);

        // 如果分账商户号和入驻商商户号一致, 则直接返回分账成功, 因为根本就不需要分账
        // 或者分账金额是0
        if (mchId.equals(receiverMchId) || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, amt) || StringUtils.isEmpty(receiverMchId)) {
            return PayWxB2bCreateDivideRespVO.success();
        }

        String signStr = JSON.toJSONString(signData);
        String paySig = calcPaySig("/retail/B2b/createprofitsharingorder", signStr, appKey);
        String url = String.format("https://api.weixin.qq.com/retail/B2b/createprofitsharingorder?access_token=%s&pay_sig=%s", accessToken, paySig);

        log.info("请求微信b2b支付分账参数={}", signStr);
        String response = WxHttpUtil.post(url, signStr);
        log.info("请求微信b2b支付分账结果={}", response);

        JSONObject resultObj = JSON.parseObject(response);
        if (resultObj.getInteger("errcode") != 0) {
            return PayWxB2bCreateDivideRespVO.fail(resultObj.getString("errmsg"));
        }
        return PayWxB2bCreateDivideRespVO.success();
    }

    /**
     * 请求分账完成
     * @param mchId         商户ID
     * @param outTradeNo    系统订单号
     */
    public CreateDivideRespVO divideOver(String mchId, String outTradeNo, String appKey) {
        // 查询退款结果
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(appId);
        if (StringUtils.isEmpty(accessToken)) {
            throw new ServiceException("accessToken 不存在");
        }
        HashMap<Object, Object> signData = new HashMap<>();
        signData.put("mchid", mchId);    //  由微信支付生成并下发的商户号。示例值：**********
        signData.put("out_trade_no", outTradeNo);

        String signStr = JSON.toJSONString(signData);
        String paySig = calcPaySig("/retail/B2b/finishprofitsharingorder", signStr, appKey);
        String url = String.format("https://api.weixin.qq.com/retail/B2b/finishprofitsharingorder?access_token=%s&pay_sig=%s", accessToken, paySig);

        log.info("请求微信b2b支付分账完成参数={}", signStr);
        String response = WxHttpUtil.post(url, signStr);
        log.info("请求微信b2b支付分账完成结果={}", response);

        JSONObject resultObj = JSON.parseObject(response);
        if (resultObj.getInteger("errcode") != 0) {
            return PayWxB2bCreateDivideRespVO.fail(resultObj.getString("errmsg"));
        }
        return PayWxB2bCreateDivideRespVO.success();
    }

    public PayWxB2bDivideRespVO queryDivide(String mchId, String outTradeNo, String altNo, String appKey) {
        // 查询退款结果
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(appId);
        if (StringUtils.isEmpty(accessToken)) {
            throw new ServiceException("accessToken 不存在");
        }
        HashMap<Object, Object> signData = new HashMap<>();
        signData.put("mchid", mchId);    //  由微信支付生成并下发的商户号。示例值：**********
        signData.put("out_trade_no", outTradeNo);
        if (NumberUtil.isNumber(altNo)) {
            // 否则是对公商户号
            signData.put("receiver_type", "PAYEE_TYPE_EXTERNAL_MERCHANT");
        } else {
            // 否则是openid对私
            signData.put("receiver_type", "PAYEE_TYPE_EXTERNAL_USER");
        }
        signData.put("receiver_account", altNo);

        String signStr = JSON.toJSONString(signData);
        String paySig = calcPaySig("/retail/B2b/queryprofitsharingorder", signStr, appKey);
        String url = String.format("https://api.weixin.qq.com/retail/B2b/queryprofitsharingorder?access_token=%s&pay_sig=%s", accessToken, paySig);

        log.info("请求微信b2b分账查询参数={}", signStr);
        String response = WxHttpUtil.post(url, signStr);
        log.info("请求微信b2b分账查询结果={}", response);

        JSONObject resultObj = JSON.parseObject(response);
        if (resultObj.getInteger("errcode") != 0) {
            log.warn("查询B2B支付分账异常");
            return new PayWxB2bDivideRespVO();
        }
        Integer orderStatus = resultObj.getInteger("order_status");
        if (orderStatus == 1) {
            return new PayWxB2bDivideRespVO();
        }
        if (orderStatus == 2) {
            return PayWxB2bDivideRespVO.success();
        }
        return PayWxB2bDivideRespVO.fail(resultObj.getString("errmsg"));
    }

    @NotNull
    private static HashMap<Object, Object> getRefundSigData(PayRefundOrderSubmitReqVO reqDTO, List<OrderSettlementDTO> supplierSettleDTOList) {
        HashMap<Object, Object> signData = new HashMap<>();
        if (!reqDTO.isUseOrderNoRefund()) {
            // 优先使用flowId
            signData.put("out_trade_no", reqDTO.getFlowId());         //  	原支付交易对应的商户订单号。商户订单号和B2b支付订单号必填其一
        } else {
            signData.put("out_trade_no", reqDTO.getOrderNo());         //  	原支付交易对应的商户订单号。商户订单号和B2b支付订单号必填其一
        }
        signData.put("out_refund_no", reqDTO.getRefundFlowDTO().getPayFlowId());    //  商户系统内部退款单号，商户系统内部唯一，只能是数字、大小写字母_-*，同一退款单号多次请求只退一笔。
        signData.put("refund_amount", reqDTO.getRefundAmt().multiply(new BigDecimal("100")));    //  退款金额，单位为分，只能为整数，不能超过原订单支付金额。
        signData.put("refund_from", 3);    //  退款来源，枚举值 1：人工客服退款 2：用户自己退款 3：其他
        signData.put("refund_reasom", 0);    //  	退款原因，枚举值 0：暂无描述 1：产品问题 2：售后问题 3：意愿问题 4：价格问题 5：其他原因
        return signData;
    }

    /**
     * 计算 pay_sig 签名
     *
     * @param uri       当前请求的 API 的 URI 部分，不带 query_string。
     * @param postBody  HTTP POST 数据包体。
     * @param appKey    对应环境的 AppKey。
     * @return          计算出的支付请求签名 pay_sig。
     */
    public static String calcPaySig(String uri, String postBody, String appKey) {
        try {
            String needSignMsg = uri + '&' + postBody;
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(appKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] hash = sha256Hmac.doFinal(needSignMsg.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("计算 HMAC 时发生错误", e);
        }
    }

    /**
     * 计算用户登录态签名signature。
     *
     * @param postBody     HTTP POST的数据包体。
     * @param sessionKey   当前用户有效的session_key，参考auth.code2Session接口。
     * @return             计算出的用户登录态签名signature。
     */
    public static String calcSignature(String postBody, String sessionKey) {
        try {
            String needSignMsg = postBody;
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(sessionKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] hash = sha256Hmac.doFinal(needSignMsg.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("计算 HMAC 时发生错误", e);
        }
    }

    public static void main(String[] args) {
        //calcPaySig(String uri, String postBody, String appKey)
        String uri = "requestCommonPayment";
        String postBody = "{\"amount\":{\"order_amount\":400},\"mchid\":\"1719598770\",\"out_trade_no\":\"627301469990682624\",\"need_profit_sharing\":0,\"description\":\"SP00114\",\"attach\":\"sysCode=4&orderType=0&appid=wx7e30a513ad4d15fa&orderNo=XS250614960000018026\",\"env\":0}";
        String appKey = "ByfwJJkAS8PDcbI2ZusWjEpBn5lKp3eb";
        String paySig = calcPaySig(uri, postBody, appKey);
        System.out.println("paySig:"+paySig);


        //calcSignature(String postBody, String sessionKey)
        String postBody1 = "{\"amount\":{\"order_amount\":400},\"mchid\":\"1719598770\",\"out_trade_no\":\"627301469990682624\",\"need_profit_sharing\":0,\"description\":\"SP00114\",\"attach\":\"sysCode=4&orderType=0&appid=wx7e30a513ad4d15fa&orderNo=XS250614960000018026\",\"env\":0}";
        String sessionKey = "5MdyAdZz10hJfiLSbpG8Zg==";
        String signature = calcSignature(postBody1, sessionKey);
        System.out.println("signature:"+signature);
    }

    //合单数据
    public static void main2() {
        //calcPaySig(String uri, String postBody, String appKey)
        String uri = "requestCommonPayment";
        String postBody = "{\"amount\":{\"order_amount\":400},\"mchid\":\"1719598770\",\"out_trade_no\":\"627301469990682624\",\"need_profit_sharing\":0,\"description\":\"SP00114\",\"attach\":\"sysCode=4&orderType=0&appid=wx7e30a513ad4d15fa&orderNo=XS250614960000018026\"}";
        String appKey = "ByfwJJkAS8PDcbI2ZusWjEpBn5lKp3eb";
        String paySig = calcPaySig(uri, postBody, appKey);
        System.out.println("paySig2:"+paySig);


        //calcSignature(String postBody, String sessionKey)
        String postBody1 = "{\"env\":0,\"combined_order_list\":[{\"amount\":{\"order_amount\":400},\"mchid\":\"1719598770\",\"out_trade_no\":\"627301469990682624\",\"need_profit_sharing\":0,\"description\":\"SP00114\",\"attach\":\"sysCode=4&orderType=0&appid=wx7e30a513ad4d15fa&orderNo=XS250614960000018026\"}]}";
        String sessionKey = "5MdyAdZz10hJfiLSbpG8Zg==";
        String signature = calcSignature(postBody1, sessionKey);
        System.out.println("signature2:"+signature);
    }
}
