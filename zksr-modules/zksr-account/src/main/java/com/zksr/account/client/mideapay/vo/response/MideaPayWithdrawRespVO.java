package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayWithdrawRespVO extends MideaPayBaseRespVO {
    /**
     * 商户单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 平台代付交易单号
     */
    @JsonProperty("trade_no")
    private String tradeNo;

    /**
     * 交易类型
     */
    @JsonProperty("trade_type")
    private String tradeType;

    /**
     * 订单状态,订单的状态码 WAIT_PAY：订单已接收
     */
    @JsonProperty("trade_status")
    private String tradeStatus;

    /**
     * 订单状态描述
     */
    @JsonProperty("trade_status_info")
    private String tradeStatusInfo;

    /**
     * 交易金额
     */
    @JsonProperty("pay_amount")
    private String payAmount;

    /**
     * 货币类型
     */
    @JsonProperty("currency_type")
    private String currencyType;

    /**
     * 账户类型,PAYMENT 支付账户（默认） ESCROW 托管账户
     */
    @JsonProperty("payer_act_type")
    private String payerActType;

    /**
     * 商户自定义信息
     */
    @JsonProperty("attach")
    private String attach;

    /**
     * 订单接收时间
     */
    @JsonProperty("trade_accept_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String tradeAcceptTime;

}
