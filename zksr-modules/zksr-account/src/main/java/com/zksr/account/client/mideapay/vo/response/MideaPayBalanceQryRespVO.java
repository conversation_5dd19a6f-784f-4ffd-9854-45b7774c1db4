package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zksr.common.core.utils.JsonUtils;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayBalanceQryRespVO extends MideaPayBaseRespVO {
    /**
     * 待查询商户号
     */
    @JsonProperty("query_partner")
    private String queryPartner;

    /**
     * 账户信息,账户List的json串，每个账户的字段见“账户余额”
     */
    @JsonProperty("act_info_list")
    private String actInfoList;

    public List<MideaPayBalanceActInfo> getMideaPayBalanceActInfo(){
        return StringUtils.isEmpty(actInfoList)?null: JsonUtils.toJavaList(actInfoList,MideaPayBalanceActInfo.class);
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class MideaPayBalanceActInfo {
        /**
         * 账户类型,CASH：支付现金户
         *
         * TRADE：支付交易户
         *
         * DIPOSIT：保证金账户
         *
         * GUARANTEE：担保账户
         *
         * ESCROW_CASH：托管现金户
         *
         * ESCROW_TRADE：托管交易户
         *
         * PROJECT:项目户
         */
        @JsonProperty("act_type")
        private String actType;

        /**
         * 商户账户余额，单位分
         */
        @JsonProperty("act_bal")
        private String actBal;

        /**
         * 商户账户冻结金额，单位分
         */
        @JsonProperty("frz_bal")
        private String frzBal;

        /**
         * 项目编号,act_type为PROJECT时有该值
         */
        @JsonProperty("project_no")
        private String projectNo;

    }

}
