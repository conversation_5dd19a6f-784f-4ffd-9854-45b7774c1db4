package com.zksr.account.controller.root;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zksr.account.controller.recharge.vo.AccRechargePageReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSaveReqVO;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.service.IAccRechargeService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.O2OSettleTaskParamVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import static com.zksr.common.core.web.pojo.CommonResult.success;

@Api(tags = "JOB管理后台 - 后门处理", produces = "application/json")
@Validated
@RestController
@RequestMapping("/jobRoot")
public class JobRootController {
    
    @Resource
    private OrderApi orderApi;
  
    @ApiOperation(value = "O2O分账定时器执行方法", httpMethod = "POST")
    @Log(title = "O2O分账定时器执行方法", businessType = BusinessType.INSERT)
    @PostMapping("/orderO2OSignAfterSettle")
    public CommonResult<Boolean> orderO2OSignAfterSettle(@Valid @RequestBody O2OSettleTaskParamVO settleTaskParam) {
        return success(orderApi.orderO2OSignAfterSettle(settleTaskParam).getCheckedData());
    }
   
}
