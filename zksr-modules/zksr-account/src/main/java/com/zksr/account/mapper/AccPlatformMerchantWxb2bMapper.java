package com.zksr.account.mapper;

import com.zksr.account.domain.AccPlatformMerchantWxb2b;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.database.query.QueryWrapperX;
import org.apache.ibatis.annotations.Mapper;


/**
 * 支付平台商户, 微信B2B平台商户扩展信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@Mapper
public interface AccPlatformMerchantWxb2bMapper extends BaseMapperX<AccPlatformMerchantWxb2b> {
    default AccPlatformMerchantWxb2b selectByMerchantTypeAndMerchantId(String merchantType, Long merchantId) {
        return selectOne(
                new LambdaQueryWrapperX<AccPlatformMerchantWxb2b>()
                        .eq(AccPlatformMerchantWxb2b::getMerchantType, merchantType)
                        .eq(AccPlatformMerchantWxb2b::getMerchantId, merchantId)
        );
    }
}
