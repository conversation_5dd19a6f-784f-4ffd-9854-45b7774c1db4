package com.zksr.account.service.pay.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.account.client.PayClient;
import com.zksr.account.controller.flow.vo.AccPayFlowPageReqVO;
import com.zksr.account.convert.pay.PayConvert;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.mapper.AccPayFlowMapper;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.order.UnifiedOrderValidateDTO;
import com.zksr.account.model.pay.vo.PayOrderQueryVO;
import com.zksr.account.model.pay.vo.PayRefundQueryVO;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderNotifyRespDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.service.IAccDivideDtlService;
import com.zksr.account.service.IAccPayFlowService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.pay.PayChannelService;
import com.zksr.account.service.pay.process.PayOrderProcessService;
import com.zksr.account.service.pay.PayOrderService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dictData.DictDataApi;
import com.zksr.system.api.domain.SysDictData;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/7 10:52
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class PayOrderServiceImpl implements PayOrderService {

    @Value("${wxpayway:wxpayway}")
    private String wechatCombineConfig;
    
    @Value("${combineNoSettleSysCode:combineNoSettleSysCode}")
    private String combineNoSettleSysCode;

    
    
    //已废弃
    @Deprecated
    @Value("${mideaPayScanCode:mideaPayScanCode}")
    private String mideaPayScanCode;

    @Autowired
    private IAccPayFlowService payFlowService;

    @Autowired
    private PayChannelService payChannelService;

    @Autowired
    private AccPayFlowMapper accPayFlowMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccDivideDtlService divideDtlService;

    @Autowired
    private AccountMqProducer accountMqProducer;
    
    @Autowired
    private DictDataApi dictDataApi;

    //!@支付 - 提交支付订单 - 1、入口
    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_ORDER_PAY, condition = "#reqVO.orderNo")
    public PayOrderRespDTO submitOrder(PayOrderSubmitReqVO reqVO) {
        if (!OrderTypeConstants.VALID_ORDER.contains(reqVO.getOrderType())) {
            // 订单类型不存在
            throw exception(ORDER_TYPE_NOT_EXIST);
        }
        // 获取订单类型 订单处理器
        PayOrderProcessService processService = getOrderProcess(reqVO.getOrderType());
        // 查看订单是否支付
        if (payFlowService.validRepeatPay(reqVO.getOrderNo())) {
            throw exception(ORDER_REPAT_PAY);
        }

        // 前端不传appid, 根据平台 + 订单类型获取
        // loadAppConfig(reqVO);
        // 业务模块前置校验订单
        PayOrderDTO payOrder = processService.processValidateOrderBefore(reqVO);
        log.info("获取支付client={}", JSON.toJSONString(reqVO));
        log.info("付款订单payOrder={}", JSON.toJSONString(payOrder));

        // 根据平台 + appid 获取支付配置
        // 获取支付client
        //!@支付 - 提交支付订单 - 3、获取支付client
        PayClient payClient = payChannelService.getPayClient(reqVO.getAppid(), reqVO.getSysCode(), reqVO.getPayWay(), reqVO.getOrderType());

        if(wechatCombineConfig.contains(payOrder.getSysCode()+"") || wechatCombineConfig.equals("ALL")) {
            payOrder.setCombinePay(true);
        }

        PayOrderRespDTO orderRespDTO = null;
        
        //美的付特殊配置
        mideaPayConfig(payClient, payOrder);
        
        //!@支付 - 提交支付订单 - 4、合单支付
        if(payOrder.isCombinePay()){
            
            Long combineFlowId  = ToolUtil.getDateTimeWithRandom18();
            payOrder.setFlowId(combineFlowId);
            
            //不分账
            //boolean notSettle = combineNoSettleSysCode.contains(payOrder.getSysCode()+"") || combineNoSettleSysCode.equals("ALL");
            boolean notSettle = DistributionModeEnum.O2O.getCode().equals(payOrder.getDistributionMode());
            
            //新增逻辑：只对入驻商支付，不对【平台/运营商/业务员/业务负责人/门店】支付，后续再使用分账接口处理平台等的分账
            if (notSettle) {
                reBuildNoSettlement(payOrder,reqVO,payClient,combineFlowId);
            }else{
                //TODO  2025-7-13 01:32:21  这里有问题，不应该按照分账信息创建多个流水
                //TODO  应该以入驻商订单作为支付主体，创建一个流水，流水的 settles 应该是：此子单下的【入驻商+平台+运营商+业务员+业务负责人+门店】的集合
                for(OrderSettlementDTO orderSettlementDTO : payOrder.getSettlements()) {
                    // 插入订单支付流水
                    AccPayFlow payFlow = this.createPayFlowByCombine(payOrder, reqVO, payClient, orderSettlementDTO,combineFlowId);
                }
            }
           
            
            // 发起支付
            orderRespDTO = this.unifiedOrder(payClient, reqVO, payOrder);
            
            for(OrderSettlementDTO orderSettlementDTO : payOrder.getSettlements()) {
                AccPayFlow payFlow = new AccPayFlow();
                payFlow.setPayFlowId(orderSettlementDTO.getFlowId());
                // 更新flow
                this.updatePayFlow(orderRespDTO, payFlow);
            }
            
        //!@支付 - 提交支付订单 - 5、单笔支付
        }else {
            // 插入订单支付流水
            AccPayFlow payFlow = this.createPayFlow(payOrder, reqVO, payClient);
            // 发起支付
            orderRespDTO = this.unifiedOrder(payClient, reqVO, payOrder);

            // 更新flow
            this.updatePayFlow(orderRespDTO, payFlow);
        }



        // 处理支付结果
        if (PayOrderStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            // 储值支付 和 模拟支付 会直接支付成功, 处理回调
            // 如果是支付失败, 则直到支付成功
            accountMqProducer.sendPayNotify(orderRespDTO);
        }
        return orderRespDTO;
    }
    
    /**
     * 美的付配置
     * @param payClient
     * @param payOrder
     */
    private void mideaPayConfig(PayClient payClient, PayOrderDTO payOrder) {
        if(PayChannelEnum.MIDEA_PAY.getCode().equals(payClient.getPlatform())){
            
            //1、增加美的付支付扫码的平台，没有配置的平台是直接弹出支付页面，配置了就弹出二维码扫码
            List<SysDictData> payScanCodeKList = dictDataApi.selectDictDataList(new SysDictData("midea_pay_scan_code"));
            if (ToolUtil.isNotEmpty(payScanCodeKList)) {
                List<SysDictData> scanCodeList = payScanCodeKList.stream()
                 .filter(a -> ToolUtil.isNotEmpty(a.getDictValue()) && a.getDictValue().equals(payOrder.getSysCode().toString()))
                 .collect(Collectors.toList());
                if (ToolUtil.isNotEmpty(scanCodeList)) {
                    payOrder.setBarCode(MideaPayBarCodeEnum.SCAN_CODE.getValue());
                }
            }
            
            //2、增加美的付支付支付平台APPID
            List<SysDictData> mideaPayAppIdList = dictDataApi.selectDictDataList(new SysDictData("midea_pay_app_id"));
            if (ToolUtil.isNotEmpty(mideaPayAppIdList)) {
                List<SysDictData> dictList = mideaPayAppIdList.stream()
                 .filter(a -> ToolUtil.isNotEmpty(a.getDictLabel()) && a.getDictLabel().equals(payOrder.getSysCode().toString()))
                 .collect(Collectors.toList());
                if (ToolUtil.isNotEmpty(dictList)) {
                    String appId = dictList.get(0).getDictValue();
                    //为 - 就清空
                    if ("-".equals(appId)){
                        payOrder.setAppid(null);
                    }else{
                        payOrder.setAppid(dictList.get(0).getDictValue());
                    }
                }
            }
        }
    }
    
    /**
     * 只对入驻商分账，不对【平台/运营商/业务员/业务负责人/门店】分账
     * @param reqVO
     * @param payOrder
     */
    private void reBuildNoSettlement(PayOrderDTO payOrder,PayOrderSubmitReqVO reqVO,PayClient payClient,Long combineFlowId) {
        PayOrderRespDTO orderRespDTO;
        // 将子单的对应的分账信息合并起来，所以一个子单只对应一个流水
        List<OrderSettlementDTO> mergedSettlements = payOrder.getSettlements().stream()
            .collect(Collectors.groupingBy(OrderSettlementDTO::getSubOrderNo))
            .values()
            .stream()
            .map(list -> {
                
                OrderSettlementDTO first = list.get(0);
                OrderSettlementDTO supplierSumSettle = new OrderSettlementDTO();
                supplierSumSettle.setSubOrderNo(first.getSubOrderNo());
                // 合计所有的SubAmt和Amt
                supplierSumSettle.setSubAmt(list.stream()
                    .map(OrderSettlementDTO::getSubAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
                supplierSumSettle.setAmt(list.stream()
                    .map(OrderSettlementDTO::getAmt)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
                // 设置供应商商户信息
                list.stream()
                    .filter(item -> MerchantTypeEnum.SUPPLIER.getType().equals(item.getMerchantType()))
                    .findFirst()
                    .ifPresent(supplier -> {
                        supplierSumSettle.setMerchantId(supplier.getMerchantId());
                        supplierSumSettle.setMerchantType(supplier.getMerchantType());
                        supplierSumSettle.setAccountNo(supplier.getAccountNo());
                    });
                
                //此子单下所有的分账信息（入驻商+ 平台/运营商/业务员/业务负责人/门店 ）
                List<OrderSettlementDTO> allSubSettle = list.stream().collect(Collectors.toList());
                //根据每一个入驻商订单创建一条流水
                createPayFlowByNoSettle(payOrder, reqVO, payClient, supplierSumSettle,allSubSettle, combineFlowId);
                log.info("子单{}分账信息：入驻商分账(合计){}，所有分账信息：{}", first.getSubOrderNo(), JSON.toJSONString(supplierSumSettle), JSON.toJSONString(allSubSettle));
                return supplierSumSettle;
            })
            .collect(Collectors.toList());
        
        //使用支付金额处理
        payOrder.setUseSubAmt(true);
        payOrder.setSettlements(mergedSettlements);
    }
    
    /**
     * 这里不推荐加事务, 如果退款发起的时候, 这个时候还有flow记录防止, 退款重复发起
     * @param reqDTO 退款申请信息
     * @return
     */
    //!@退款 - 提交退款 - 1、入口
    //!@同意退款 - 3、提交退款（调用微信、美的付）
    //!@订单 - 取消 - 3、模拟回调 - 5、提交退款
    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_ORDER_PAY, condition = "#reqDTO.orderNo")
    public PayRefundRespDTO createPayRefund(PayRefundOrderSubmitReqVO reqDTO) {
        log.info("发起退款请求,req={}", JSON.toJSONString(reqDTO));
        // 验证是否支付
        AccPayFlow payFlow = payFlowService.getByOrderPayFlowSuccessFlag(reqDTO.getOrderNo());
        if (Objects.isNull(payFlow) || payFlow.getCallbackFlag() != NumberPool.INT_ONE) {
            // 主单查不出来，换子单再查一次
            payFlow = payFlowService.getByOrderPayFlowSuccessFlag(reqDTO.getSupplierOrderNo());
            if (Objects.isNull(payFlow) || payFlow.getCallbackFlag() != NumberPool.INT_ONE) {
                log.info("退款发起, 支付记录不存, order:{}", reqDTO.getOrderNo());
                throw exception(PAY_FLOW_NOT_EXIST);
            }
        }

        // 根据平台 + appid 获取支付配置
        // 获取支付client
        PayClient payClient = payChannelService.getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
        
        
        PayRefundRespDTO refundRespDTO = unifiedRefund(payClient, payFlow, reqDTO);

        // 消息通知
        if (PayRefundStatusEnum.isFailure(refundRespDTO.getStatus()) || PayRefundStatusEnum.isSuccess(refundRespDTO.getStatus())) {
            // 失败也直接通知
            // 处理退款结果, 如果是成功就直接回调, 兼容模拟支付, 钱包付款
            accountMqProducer.sendRefundNotify(refundRespDTO);
        }
        return refundRespDTO;
    }

    /**
     * 这里不加事务, 具体订单流程控制实现类里面可以自己加事务
     * !@回调支付 - 4、通知订单支付成功 （更新支付流水AccPayFlow的回调结果和时间）
     * @param notify
     * @param orderType 订单类型 {@link PayOrderSubmitReqVO orderType}
     * @return
     */
    @Override
    @Transactional
    public PayOrderNotifyRespDTO notifyOrder(PayOrderRespDTO notify, Integer orderType) {
        log.info("支付成功, notify : {}", JSON.toJSONString(notify));
        // 获取订单类型 订单处理器
        PayOrderProcessService processService = getOrderProcess(orderType);
        if (Objects.isNull(processService) || Objects.isNull(processService)) {
            return PayOrderNotifyRespDTO.fail();
        }
        // 这里需要判断是支付成功还是支付失败
        // 大部分支付在回调都是成功, 暂时没有支付失败场景
        if (PayOrderStatusRespEnum.isSuccess(notify.getStatus())) {
            AccPayFlow payFlow;
            if (Objects.nonNull(notify.getFlowId())) {
                // TODO: 此处为了兼容美的支付, 美的支付还是使用的orderNo业务单号合一的支付单号
                // 如果美的支付需要使用 flowId, 则需要再回调里面兼容返回flowId
                // 优先使用flowId
                payFlow = payFlowService.getAccPayFlow(notify.getFlowId());
            } else {
                payFlow = payFlowService.getByOrderPayFlow(notify.getOrderNo());
            }
            if (Objects.isNull(payFlow)) {
                throw exception(PAY_FLOW_NOT_EXIST);
            }
            if (payFlow.getCallbackFlag() == NumberPool.INT_ONE) {
                log.error("支付流水已经处理");
                return PayOrderNotifyRespDTO.fail();
            }
            payFlow.setCallbackTime(DateUtil.date())
                    .setCallbackFlag(NumberPool.INT_ONE)
                    .setOutTradeNo(notify.getOutTradeNo());
            int updateLine = accPayFlowMapper.update(
                    payFlow,
                    Wrappers.lambdaUpdate(AccPayFlow.class)
                            .eq(AccPayFlow::getPayFlowId, payFlow.getPayFlowId())
                            .eq(AccPayFlow::getCallbackFlag, NumberPool.INT_ZERO)
            );
            if (updateLine == 0) {
                log.error("CAS 更新失败, 支付流水已经处理");
                throw exception(NET_WORK_ERR01);
            }

            // 记录分账信息
            divideDtlService.createDivide(payFlow, notify);

            // 设置支付方式支付平台
            notify.setPayPlatform(payFlow.getPlatform())
                    .setOrderNo(payFlow.getTradeNo())   // 美的支付没兼容返回orderNo, 这里直接给一个orderNo
                    .setPayWay(payFlow.getPayWay());
            // 储值支付 和 模拟支付 会直接支付成功, 处理回调
            return processService.notifyOrderSuccess(notify);
        }
        return PayOrderNotifyRespDTO.fail();
    }

    /**
     * 通知回调处理成功, 方便处理回调事务完成以后进行结算等操作
     * @param payOrderNotifyRespDTO
     * @param orderType 订单类型 {@link PayOrderSubmitReqVO orderType}
     */
    @Override
    public void notifyOrderSuccess(PayOrderNotifyRespDTO payOrderNotifyRespDTO, Integer orderType) {
        PayOrderProcessService processService = getOrderProcess(orderType);
        if (Objects.isNull(processService)) {
            return;
        }
        processService.notifyOrderSuccessCall(payOrderNotifyRespDTO);
    }


    @Override
    @Transactional
    public void notifyRefund(PayRefundRespDTO notify, Integer orderType) {
        log.info("退款回调, notify : {}", JSON.toJSONString(notify));
        // 获取订单类型 订单处理器
        PayOrderProcessService processService = getOrderProcess(orderType);
        if (Objects.isNull(processService)) {
            log.error("退款回调处理找不到订单处理器");
            return;
        }

        // 退款流水, 理论上最新的都是走独立单号, 使用payFlowId进行退款
        AccPayFlow refundFlow;
        if (NumberUtil.isNumber(notify.getRefundNo())) {
            // 查询退款流水, 返回给trade模块, 退款发起单号
            refundFlow = payFlowService.getById(Long.parseLong(notify.getRefundNo()));
            // 设置退款单号
            notify.setRefundNo(refundFlow.getRefundNo());
            log.info("退款回调, 转换 notify : {}", JSON.toJSONString(notify));
        } else {
            refundFlow = payFlowService.getByOrderRefundFlow(notify.getRefundNo());
        }

        // 验证, 修改退款流水
        if (!callBackRefundFlow(notify, refundFlow)) {
            log.error("退款流水已经处理");
            return;
        }
        // 这里需要判断是支付成功还是支付失败
        // 大部分支付在回调都是成功, 暂时没有支付失败场景
        if (PayRefundStatusEnum.isSuccess(notify.getStatus())) {
            processService.notifyRefundSuccess(notify);
        } else if (PayRefundStatusEnum.isFailure(notify.getStatus())){
            processService.notifyRefundFailure(notify);
        }
    }

    /**
     * 处理延迟订单退款查询, 一般是无退款回调使用, 需要手动进行查询行为
     * @param refundQueryVO 查询参数
     */
    @Override
    public void processDelayRefundQuery(PayRefundQueryVO refundQueryVO) {
        // 查询订单支付的流水用来获取支付client
        AccPayFlow payFlow = payFlowService.getById(refundQueryVO.getPayFlowId());
        if (Objects.isNull(payFlow)) {
            log.warn("延迟查询订单退款状态, 处理退款时flowId没有查询到支付流水, payFlowId={}", refundQueryVO.getPayFlowId());
            return;
        }
        refundQueryVO.setPayFlowDTO(PayConvert.INSTANCE.convertPayFlowDTO(payFlow));

        // 获取退款流水
        AccPayFlow refundFlow = payFlowService.getAccPayFlow(refundQueryVO.getRefundFlowId());
        if (Objects.nonNull(refundFlow) && refundFlow.getCallbackFlag() == NumberPool.INT_ONE) {
            log.warn("退款已经处理, refundNo:{}", refundQueryVO.getRefundNo());
            return;
        }

        // 获取支付client
        PayClient payClient = payChannelService.getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
        PayRefundRespDTO refundRespDTO = payClient.getRefund(refundQueryVO);

        // 成功或者失败就直接处理, 退款中就不调整了
        if (PayRefundStatusEnum.isSuccess(refundRespDTO.getStatus()) || PayRefundStatusEnum.isFailure(refundRespDTO.getStatus())) {
            // 参数补偿
            refundRespDTO.setOrderType(payFlow.getOrderType());
            accountMqProducer.sendRefundNotify(refundRespDTO);
        }
    }

    @Override
    public PayOrderRespDTO queryOrder(PayOrderQueryVO payOrderQueryVO) {
        AccPayFlow successPayFlow = payFlowService.getByOrderPayFlowSuccessFlag(payOrderQueryVO.getTradeNo());
        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        if (Objects.nonNull(successPayFlow)) {
            // 已经有支付成功流水了
            return respDTO.setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                    .setFlowId(successPayFlow.getPayFlowId())
                    .setOutTradeNo(successPayFlow.getOutTradeNo())
                    .setPayPlatform(successPayFlow.getPlatform())
                    .setOrderNo(successPayFlow.getTradeNo());
        }
        // 需要查询出支付流水, 然后一个一个去处理查询, 直到查询到成功的
        AccPayFlowPageReqVO reqVO = new AccPayFlowPageReqVO();
        reqVO.setTradeNo(payOrderQueryVO.getTradeNo());
        reqVO.setPayType(NumberPool.INT_ZERO);
        reqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AccPayFlow> accPayFlows = payFlowService.getAccPayFlowPage(reqVO).getList();
        for (AccPayFlow payFlow : accPayFlows) {
            // 根据平台 + appid 获取支付配置
            // 获取支付client
            PayClient payClient = payChannelService.getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
            payOrderQueryVO.setPayFlow(PayConvert.INSTANCE.convertPayFlowDTO(payFlow));
            // 查询支付订单状态
            PayOrderRespDTO orderRespDTO = payClient.getOrder(payOrderQueryVO);
            if (Objects.nonNull(orderRespDTO) && orderRespDTO.getStatus() == PayOrderStatusRespEnum.FINISH.getStatus()) {
                // 如果查询出来支付成功的
                if (payOrderQueryVO.isAdvanceNoticeOrder()) {
                    // 是否需要直接通知订单支付成功了
                    // 这里必须得有支付流水号
                    orderRespDTO.setPayPlatform(payClient.getPlatform())
                                .setPayWay(payFlow.getPayWay())
                                .setOrderType(payFlow.getOrderType())
                                .setFlowId(payFlow.getPayFlowId());
                    accountMqProducer.sendPayNotify(orderRespDTO);
                }
                return orderRespDTO;
            }
        }
        // 返回发起支付成功状态
        return respDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus())
                .setOrderNo(payOrderQueryVO.getTradeNo());
    }

    /**
     * !@支付 - 提交支付订单 - 7、发起支付
     * @param payClient
     * @param payOrder
     * @return
     */
    private PayOrderRespDTO unifiedOrder(PayClient payClient, PayOrderSubmitReqVO submitReqVO, PayOrderDTO payOrder) {
        // 发起支付前验证
        UnifiedOrderValidateDTO unifiedOrderValidateDTO = payChannelService.processPayBeforeValidate(payClient, payOrder);
        if (!unifiedOrderValidateDTO.getSuccess()) {
            return new PayOrderRespDTO().setStatus(PayRefundStatusEnum.FAILURE.getStatus()).setMessage(unifiedOrderValidateDTO.getMsg());
        }

        if(wechatCombineConfig.contains(payOrder.getSysCode()+"") || wechatCombineConfig.equals("ALL")) {
            payOrder.setCombinePay(true);
        }
            // 发起支付
        PayOrderRespDTO orderRespDTO = payClient.unifiedOrder(payOrder);

        // 设置支付渠道
        orderRespDTO.setPayPlatform(payClient.getPlatform())
                .setPayWay(submitReqVO.getPayWay())
                .setOrderType(submitReqVO.getOrderType());
        return orderRespDTO;
    }

    /**
     * 退款
     * @param payClient
     * @param reqDTO
     * @return
     */
    private PayRefundRespDTO unifiedRefund(PayClient payClient, AccPayFlow payFlow, PayRefundOrderSubmitReqVO reqDTO) {
        PayRefundRespDTO refundRespDTO = new PayRefundRespDTO();

        // 设置必要参数
        PayConvert.INSTANCE.buildSetRefundOrderSubmitReqVO(reqDTO, payFlow);

        // 发起退款前验证
        UnifiedOrderValidateDTO unifiedOrderValidateDTO = payChannelService.processRefundBeforeValidate(payClient, reqDTO);
        if (!unifiedOrderValidateDTO.getSuccess()) {
           refundRespDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                    .setRefundNo(reqDTO.getRefundNo())
                    .setOrderNo(reqDTO.getOrderNo())
                    .setErrorMsg(unifiedOrderValidateDTO.getMsg());
            return refundRespDTO;
        }

        // 创建退款流水
        reqDTO.setRefundFlowDTO(PayConvert.INSTANCE.convertPayFlowDTO(createRefundFlow(payFlow, reqDTO)));
        if (unifiedOrderValidateDTO.getCode() == 1) {
            // 发送查询分账结果处理
            accountMqProducer.sendWxOrderDivideDelayQuery(reqDTO);
            // 微信特殊流程处理
            refundRespDTO.setStatus(PayRefundStatusEnum.PROCESSING.getStatus())
                    .setRefundNo(reqDTO.getRefundNo())
                    .setOrderNo(reqDTO.getOrderNo());
            return refundRespDTO;
        }

        //!@退款 - 提交退款 - 4、发起统一退款（微信、美的付、合利宝）
        refundRespDTO = payClient.unifiedRefund(reqDTO);
        refundRespDTO.setOrderType(reqDTO.getOrderType());

        log.info("发起退款结果,req={}", JSON.toJSONString(refundRespDTO));
        return refundRespDTO;
    }

    //!@支付 - 提交支付订单 - 6、创建支付流水
    private AccPayFlow createPayFlow(PayOrderDTO payOrder, PayOrderSubmitReqVO reqVO, PayClient client) {
        // 新增唯一性校验
        AccPayFlow existFlow = accPayFlowMapper.selectByPayTypeAndTradeNo(NumberPool.INT_ZERO, payOrder.getOrderNo());
        if (existFlow != null) {
            throw new ServiceException("已存在支付流水");
        }
        AccPayFlow payFlow = new AccPayFlow();
        payFlow.setSysCode(payOrder.getSysCode())
                .setPayType(NumberPool.INT_ZERO)
                .setTradeNo(payOrder.getOrderNo())
                .setPayWay(reqVO.getPayWay())
                .setPlatform(client.getPlatform())
                .setCallbackFlag(NumberPool.INT_ZERO)
                .setPayAmt(payOrder.getPayAmt())
                .setOrderType(reqVO.getOrderType())
                .setIsDivide(NumberPool.INT_ZERO)
                .setSplitType(NumberPool.INT_ONE)
                .setFee(calculatingCommission(payOrder.getSettlements(), payOrder.getPayAmt()))
                .setSettles(payOrder.getSettlements())
                .setAppid(reqVO.getAppid());
        // 减法计算手续费
        accPayFlowMapper.insert(payFlow);

        // 设置支付ID
        payOrder.setFlowId(payFlow.getPayFlowId());
        return payFlow;
    }

    // 创建支付流水
    private AccPayFlow createPayFlowByCombine(PayOrderDTO payOrder, PayOrderSubmitReqVO reqVO, PayClient client, OrderSettlementDTO subOrder,Long combineFlowId) {
        // 新增唯一性校验
        AccPayFlow existFlow = accPayFlowMapper.selectByPayTypeAndTradeNo(NumberPool.INT_ZERO, subOrder.getSubOrderNo());
        if (existFlow != null) {
            throw new ServiceException("已存在支付流水");
        }
        List<OrderSettlementDTO> reqFeeList = new ArrayList<>();
        reqFeeList.add(subOrder);
        AccPayFlow payFlow = new AccPayFlow();
        payFlow.setSysCode(payOrder.getSysCode())
                .setPayType(NumberPool.INT_ZERO)
                .setTradeNo(subOrder.getSubOrderNo())
                .setPayWay(reqVO.getPayWay())
                .setPlatform(client.getPlatform())
                .setCallbackFlag(NumberPool.INT_ZERO)
                .setPayAmt(subOrder.getSubAmt())
                .setOrderType(reqVO.getOrderType())
                .setIsDivide(NumberPool.INT_ZERO)
                .setSplitType(NumberPool.INT_ONE)
                .setFee(calculatingCommission(reqFeeList, subOrder.getSubAmt()))
                .setSettles(reqFeeList)
                .setAppid(reqVO.getAppid())
                .setCombineFlowId(String.valueOf(combineFlowId));
        // 减法计算手续费
        accPayFlowMapper.insert(payFlow);

        // 设置支付ID
        subOrder.setFlowId(payFlow.getPayFlowId());
        return payFlow;
    }
    
    // 创建支付流水（不分账） - 一个入驻商对应一条流水
    private AccPayFlow createPayFlowByNoSettle(PayOrderDTO payOrder, PayOrderSubmitReqVO reqVO, PayClient client, OrderSettlementDTO subOrder,List<OrderSettlementDTO> subOrderSettleList,Long combineFlowId) {
        List<OrderSettlementDTO> reqFeeList = new ArrayList<>();
        reqFeeList.addAll(subOrderSettleList);
        AccPayFlow payFlow = new AccPayFlow();
        payFlow.setSysCode(payOrder.getSysCode())
                .setPayType(NumberPool.INT_ZERO)
                .setTradeNo(subOrder.getSubOrderNo())
                .setPayWay(reqVO.getPayWay())
                .setPlatform(client.getPlatform())
                .setCallbackFlag(NumberPool.INT_ZERO)
                .setPayAmt(subOrder.getSubAmt())
                .setOrderType(reqVO.getOrderType())
                .setIsDivide(NumberPool.INT_ZERO)
                .setSplitType(NumberPool.INT_ONE)
                .setFee(calculatingCommission(reqFeeList, payOrder.getPayAmt()))
                .setSettles(reqFeeList)
                .setAppid(reqVO.getAppid())
                .setCombineFlowId(String.valueOf(combineFlowId));
        // 减法计算手续费
        accPayFlowMapper.insert(payFlow);

        // 设置支付ID
        subOrder.setFlowId(payFlow.getPayFlowId());
        return payFlow;
    }

    // 更新支付流水信息
    private void updatePayFlow(PayOrderRespDTO orderRespDTO, AccPayFlow payFlow) {
        // 查询是否有支付信息需要保存
        if (ObjectUtil.isNotEmpty(orderRespDTO.getPayInfoDTO())) {
            AccPayFlow updateFlow = new AccPayFlow();
            updateFlow.setPayFlowId(payFlow.getPayFlowId());
            updateFlow.setReqInfo(JSON.toJSONString(orderRespDTO.getPayInfoDTO()));
            accPayFlowMapper.updateById(updateFlow);
        }
    }

    /**
     * 创建退款流水
     * @param payFlow
     * @param reqDTO
     */
    //!@退款 - 提交退款 - 3、创建退款流水
    private AccPayFlow createRefundFlow(AccPayFlow payFlow, PayRefundOrderSubmitReqVO reqDTO) {
        AccPayFlow refundFlow = new AccPayFlow();
        refundFlow.setSysCode(payFlow.getSysCode())
                .setPayType(NumberPool.INT_ONE)
                .setTradeNo(payFlow.getTradeNo())
                .setRefundNo(reqDTO.getRefundNo())
                .setPayWay(payFlow.getPayWay())
                .setPlatform(payFlow.getPlatform())
                .setCallbackFlag(NumberPool.INT_ZERO)
                .setPayAmt(payFlow.getPayAmt())
                .setRefundAmt(reqDTO.getRefundAmt())
                .setOrderType(payFlow.getOrderType())
                .setIsDivide(NumberPool.INT_ZERO)
                .setFee(calculatingCommission(reqDTO.getSettlements(), reqDTO.getRefundAmt()))
                .setSettles(reqDTO.getSettlements())
                .setAppid(payFlow.getAppid())
                .setRefundStatus(PayRefundStatusEnum.PROCESSING.getStatus()); // 默认发起成功
        // 减法计算手续费
        accPayFlowMapper.insert(refundFlow);
        return refundFlow;
    }

    private static BigDecimal calculatingCommission(List<OrderSettlementDTO> reqDTO,BigDecimal payAmt) {
        if (Objects.nonNull(reqDTO)) {
            // 所有的分账金额
            BigDecimal totalProfit = reqDTO.stream().map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 支付金额 - 分账金额 就是手续费
            return payAmt.subtract(totalProfit);
        }
        return BigDecimal.ZERO;
    }

    // 处理退款回调流水
    private boolean callBackRefundFlow(PayRefundRespDTO notify, AccPayFlow payFlow) {
        if (Objects.isNull(payFlow)) {
            log.error("退款回调处理, 退款发起流水不存在. refundNo={}", notify.getRefundNo());
            return false;
        }
        if (payFlow.getCallbackFlag() == NumberPool.INT_ONE) {
            return false;
        }
        payFlow.setCallbackTime(DateUtil.date())
                .setCallbackFlag(NumberPool.INT_ONE)
                .setRefundStatus(notify.getStatus())
                .setOutTradeNo(notify.getOutRefundNo());
        int updateLine = accPayFlowMapper.update(
                payFlow,
                Wrappers.lambdaUpdate(AccPayFlow.class)
                        .eq(AccPayFlow::getPayFlowId, payFlow.getPayFlowId())
                        .eq(AccPayFlow::getCallbackFlag, NumberPool.INT_ZERO)
        );
        if (updateLine == 0) {
            log.error("CAS 更新失败, 支付流水已经处理");
            throw exception(NET_WORK_ERR01);
        }
        // 退款成功
        // 记录分账信息
        if (PayRefundStatusEnum.isSuccess(notify.getStatus())) {
            divideDtlService.createDivide(payFlow, notify);
        }
        return true;
    }

    @Autowired
    private List<PayOrderProcessService> payOrderProcessServices;

    private PayOrderProcessService getOrderProcess(Integer orderType) {
        for (PayOrderProcessService processService : payOrderProcessServices) {
            if (processService.getOrderType() == orderType) {
                return processService;
            }
        }
        return null;
    }

    /**
     * 获取当前自己的代理对象
     * @return
     */
    public PayOrderServiceImpl shelf() {
        return SpringUtils.getAopProxy(this);
    }
}
