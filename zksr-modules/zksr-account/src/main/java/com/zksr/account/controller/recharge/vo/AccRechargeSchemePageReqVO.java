package com.zksr.account.controller.recharge.vo;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 储值充值套餐配置对象 acc_recharge_scheme
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@ApiModel("储值充值套餐配置 - acc_recharge_scheme分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccRechargeSchemePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 充值方案id */
    @ApiModelProperty(value = "充值金额，赠送金额")
    private Long rechargeSchemeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 适用区域城市 */
    @Excel(name = "适用区域城市")
    @ApiModelProperty(value = "适用区域城市")
    private Long areaId;

    /** 适用区域城市 */
    @Excel(name = "适用区域城市")
    @ApiModelProperty(value = "适用区域城市", hidden = true)
    private List<Long> areaIdList;

    /** 方案名称 */
    @Excel(name = "方案名称")
    @ApiModelProperty(value = "方案名称")
    private String schemeName;

    /** 生效开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效开始时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效开始时间")
    private Date startTime;

    /** 生效结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效结束时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "生效结束时间")
    private Date endTime;

    /** 充值金额，赠送金额 */
    @Excel(name = "充值金额，赠送金额")
    @ApiModelProperty(value = "充值金额，赠送金额")
    private String ruleJson;

    @ApiModelProperty(value = "0-正常, 1-停用")
    private Integer status;
}
