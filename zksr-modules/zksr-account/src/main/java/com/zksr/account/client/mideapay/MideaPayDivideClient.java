package com.zksr.account.client.mideapay;

import java.util.Map;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.client.DivideClient;
import com.zksr.account.client.mideapay.vo.response.MideaPayProfitOrderNotifyRespVO;
import com.zksr.account.model.profitOrder.dto.DivideOrderRepDto;
import com.zksr.common.core.enums.MideaPayCompleteStatusEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.TransferStatusEnum;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.system.api.model.dto.MideaPayConfig;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 美的付多级分账下单Client
 */
@Data
@Slf4j
public class MideaPayDivideClient implements DivideClient {

    private MideaPaySdkClient client;

    @Override
    public String getPlatform() {return PayChannelEnum.MIDEA_PAY.getCode();}

    @Override
    public void setConfig(String config) throws Exception {
        this.client = new MideaPaySdkClient();
        /**
         * 初始化支付工具
         */
        MideaPayConfig payConfig = JSON.parseObject(config, MideaPayConfig.class);
        this.client.setPayConfig(payConfig);
    }
	
	/**
     * 解析多级分账回调
     */
    @Override
    public DivideOrderRepDto parseDivideNotify(Map<String, String> params, String body) {
        //验签
        MideaPaySdkClient.checkSign(params,client.getPayConfig().getCheckSignUrl());
        //转账回调
        DivideOrderRepDto resp = new DivideOrderRepDto();
        MideaPayProfitOrderNotifyRespVO respVO = JsonUtils.toJavaClass(params, MideaPayProfitOrderNotifyRespVO.class);
        resp.setOutProfitNo(respVO.getOutProfitNo());
        resp.setProfitNo(respVO.getProfitNo());
        resp.setOutTradeNo(respVO.getOutTradeNo());
        resp.setStatus(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(respVO.getTradeStatus()) ? TransferStatusEnum.SUCCESS.getStatus() : TransferStatusEnum.FAIL.getStatus());
        resp.setMsg(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(respVO.getTradeStatus()) ? respVO.getTradeStatusInfo() : String.format("%s,%s",respVO.getErrorCode(),respVO.getErrorInfo()));
        return resp;

    }
}