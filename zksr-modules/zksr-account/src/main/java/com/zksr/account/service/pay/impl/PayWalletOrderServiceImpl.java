package com.zksr.account.service.pay.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.client.wallet.WalletAppletPayClient;
import com.zksr.account.controller.divide.vo.AccDivideFlowSaveReqVO;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.mapper.AccDivideFlowMapper;
import com.zksr.account.mapper.AccPayFlowMapper;
import com.zksr.account.model.pay.vo.CreateDivideReqVO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.DivideReqVO;
import com.zksr.account.service.IAccDivideDtlService;
import com.zksr.account.service.IAccDivideFlowService;
import com.zksr.account.service.IAccPayFlowService;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.pay.PayWalletDivideOrderService;
import com.zksr.common.core.enums.DivideStateEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.PAY_FLOW_NOT_EXIST;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    钱包支付分账逻辑处理, 目前钱包支付只会给业务员进行系统记账,
 * @date 2025/2/17 11:34
 */
@Service
@Slf4j
@SuppressWarnings(StringPool.ALL)
public class PayWalletOrderServiceImpl implements PayWalletDivideOrderService {

    @Autowired
    private IAccPayFlowService payFlowService;

    @Autowired
    private IAccDivideDtlService divideDtlService;

    @Autowired
    private IAccDivideFlowService divideFlowService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Autowired
    private AccDivideFlowMapper accDivideFlowMapper;

    @Autowired
    private AccPayFlowMapper accPayFlowMapper;

    @Resource
    private OrderApi orderApi;

    @Autowired
    private WalletAppletPayClient payClient;

    @Override
    public PayChannelEnum getPlatform() {
        return PayChannelEnum.WALLET;
    }

    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_WXB2B_DIVIDE, condition = "#divideReqVO.tradeNo", tryLock = true)
    public CreateDivideRespVO divide(CreateDivideReqVO divideReqVO) {

        if (MerchantTypeEnum.isSupplier(divideReqVO.getMerchantType())) {
            log.warn("发起钱包请求分账, 入驻商不需要发起");
            return CreateDivideRespVO.success();
        }
        log.info("发起钱包请求分账,req={}", JSON.toJSONString(divideReqVO));
        // 验证是否支付
        AccPayFlow payFlow = payFlowService.getByOrderPayFlowSuccessFlag(divideReqVO.getTradeNo());
        if (Objects.isNull(payFlow) || payFlow.getCallbackFlag() != NumberPool.INT_ONE) {
            log.info("发起钱包请求分账, 支付记录不存, tradeNo:{}", divideReqVO.getTradeNo());
            throw exception(PAY_FLOW_NOT_EXIST);
        }
        // 获取在线分账需要处理的数据
        List<AccDivideDtl> divideList = divideDtlService.getDivideList(
                AccDivideDtl.builder()
                        .tradeNo(divideReqVO.getTradeNo())
                        .merchantType(divideReqVO.getMerchantType())
                        .merchantId(divideReqVO.getMerchantId())
                        .onlineDivideState(DivideStateEnum.UNDEFINED.getState())
                        .build()
        );
        // 验证空
        if (divideList.isEmpty()) {
            log.info("无可结算数据时, 直接返回成功, 没有找到分账方信息 {}", divideReqVO.getTradeNo());
            return CreateDivideRespVO.success();
        }

        // 总分账金额
        BigDecimal totalAltAmt = divideList.stream().map(AccDivideDtl::getAltAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 分账接受账号
        Long merchantId = divideList.get(0).getMerchantId();

        // 创建分账流水
        AccDivideFlowSaveReqVO divideFlow = new AccDivideFlowSaveReqVO();
        divideFlow.setPlatform(payFlow.getPlatform());
        divideFlow.setType(NumberPool.INT_ONE);
        divideFlow.setTradeNo(payFlow.getTradeNo());
        divideFlow.setOutTradeNo(payFlow.getOutTradeNo());
        divideFlow.setPayeeType(divideReqVO.getMerchantType());
        // 钱包支付, 不会真实分账到支付公司商户上去
        divideFlow.setPayeeNo(merchantId.toString());
        divideFlow.setDivideAmt(totalAltAmt);
        divideFlow.setDivideReqStatus(NumberPool.INT_ZERO);
        divideFlow.setAccPayFlowId(payFlow.getPayFlowId());
        divideFlow.setTargetPlatformMerchantId(divideReqVO.getMerchantId());
        divideFlow.setDivideStatus(NumberPool.INT_TWO);
        Long divideFlowId = divideFlowService.insertAccDivideFlow(divideFlow);

        // 根据平台 + appid 获取支付配置
        // 获取支付client
        // 请求分账
        // 1: 分账中 2: 分账完成 3：分账失败
        CreateDivideRespVO divideRespVO;
        if (payFlow.getIsDivide() == NumberPool.INT_ONE) {
            divideRespVO = CreateDivideRespVO.success();
        } else {
            divideRespVO = payClient.divide(
                    DivideReqVO.builder()
                            .divideFlowId(divideFlowId)
                            .tradeNo(payFlow.getTradeNo())
                            .merchantId(merchantId)
                            .merchantType(divideReqVO.getMerchantType())
                            .sysCode(payFlow.getSysCode())
                            .amount(totalAltAmt)
                            .platform(PayChannelEnum.fromValue(payFlow.getPlatform()))
                            .build()
            );
        }
        if (!divideRespVO.isSuccess()) {
            // 失败
            divideFlow.setDivideStatus(NumberPool.INT_THREE);
            divideFlow.setErrMsg(divideRespVO.getMsg());
            divideFlow.setDivideReqStatus(NumberPool.INT_ONE);
            divideFlow.setDivideFlowId(divideFlowId);
            divideFlowService.updateAccDivideFlow(divideFlow);
        } else {
            // 修改分账状态
            ArrayList<AccDivideDtl> updateList = new ArrayList<>();
            for (AccDivideDtl divideDtl : divideList) {
                divideDtl.setDivideDtlId(divideDtl.getDivideDtlId());
                divideDtl.setProcessTime(new Date());
                divideDtl.setDivideTime(new Date());
                divideDtl.setOnlineDivideState(DivideStateEnum.FINISH.getState());
                divideDtl.setDivideFlowId(divideFlowId);
                divideDtl.setAltMchNo(divideFlow.getPayeeNo());
                updateList.add(divideDtl);
            }
            divideDtlService.updateBatch(updateList);
        }

        // 返回分账流水ID
        // divideRespVO.setDivideFlowId(divideFlowId);
        return divideRespVO;
    }

    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_WXB2B_DIVIDE, condition = "divideReqVO.tradeNo", tryLock = true)
    public void divideCheckOver(CreateDivideReqVO divideReqVO) {
        log.info("验证订单是否可以钱包支付分账完成, req={}", JSON.toJSONString(divideReqVO));
        // 验证是否支付
        AccPayFlow payFlow = payFlowService.getByOrderPayFlowSuccessFlag(divideReqVO.getTradeNo());
        if (Objects.isNull(payFlow) || payFlow.getCallbackFlag() != NumberPool.INT_ONE) {
            log.info("发起钱包支付, 支付记录不存, order:{}", divideReqVO.getTradeNo());
            return;
        }

        // 查看是不是只有入驻商的没有完成了
        // 如果是的, 那就可以请求完成了
        // 获取在线分账需要处理的数据
        List<AccDivideDtl> divideList = divideDtlService.getDivideList(
                AccDivideDtl.builder()
                        .tradeNo(divideReqVO.getTradeNo())
                        .build()
        );
        // 查询分账完成的
        long finishCount = divideList.stream().filter(item ->item.getOnlineDivideState().equals(DivideStateEnum.FINISH.getState())).count();
        if (finishCount < divideList.size()) {
            return;
        }
        // 更新支付流水是已完成分账
        AccPayFlow flow = new AccPayFlow();
        flow.setPayFlowId(payFlow.getPayFlowId());
        flow.setIsDivide(NumberPool.INT_ONE);
        accPayFlowMapper.updateById(flow);

        // 这里需要回调结算, 告诉trade模块, 结算已经实际处理完成了
        orderApi.updateOrderDivideSettleState(payFlow.getTradeNo()).checkError();
    }

    @Override
    public void updateDivideStatus(DivideFlowDTO divideFlowDTO) {
        log.info("钱包支付在分账发起时已完成全部流程, 不需要关注分账处理结果, divideFlowId: {}", divideFlowDTO.getDivideFlowId());
    }

    @Override
    public CreateDivideRespVO refundOver(PayFlowDTO payFlow) {
        log.info("钱包支付全额退款时, 不需要关注提前分账完成, 只需要关注最终结算, tradeNo: {}", payFlow.getTradeNo());
        return null;
    }
}
