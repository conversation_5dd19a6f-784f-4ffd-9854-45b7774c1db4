package com.zksr.account.client;

import java.util.Map;
import java.util.Objects;

import com.zksr.account.model.profitOrder.dto.DivideOrderRepDto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分账
 */
public interface DivideClient {

    /**
     * 获取支付平台标识
     * @return
     */
    String getPlatform();

    /**
     * 设置配置
     * @param config
     */
    void setConfig(String config) throws Exception;

    
    /**
     * 解析回调参数处理
     * @param params
     * @param body
     * @return
     */
    DivideOrderRepDto parseDivideNotify(Map<String, String> params, String body);
    
    
    @ApiModel(description = "客户端描述")
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ClientParam {
        
        @ApiModelProperty("平台商编号")
        private Long sysCode;
        
        @ApiModelProperty("支付方式")
        private String platform;
        
        
        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            DivideClient.ClientParam that = (DivideClient.ClientParam) o;
            return Objects.equals(sysCode, that.sysCode) && Objects.equals(platform, that.platform) ;
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(sysCode, platform);
        }
    }
}
