package com.zksr.account.controller.account;

import cn.hutool.core.collection.ListUtil;
import com.zksr.account.api.account.vo.AccAccountRespVO;
import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.controller.account.vo.*;
import com.zksr.account.convert.account.AccountConvert;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.metchant.MerchantService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 账户Controller
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Api(tags = "管理后台 - 账户接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/account")
public class AccAccountController {

    @Autowired
    private IAccAccountService accAccountService;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccPlatformMerchantService accPlatformMerchantService;

    /**
     * 获取入驻商账户信息
     */
    @ApiOperation(value = "获取入驻商账户余额(小程序)列表", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_SUPPLIER)
    @RequiresPermissions(Permissions.GET_SUPPLIER)
    @PostMapping(value = "/queryBySupplier")
    @DataScope(supplierAlias = "acc_account", supplierFieldAlias = SystemConstants.MERCHANT_ID)
    public CommonResult<List<AccAccountRespVO>> queryBySupplier(@RequestBody AccAccountInfoReqVO accAccountInfoReqVO) {
        AccAccount accAccount = accAccountService.getAccountByMerchantIdAndTypeByDefPlatform(accAccountInfoReqVO.getMerchantId(), MerchantTypeEnum.SUPPLIER.getType(), SecurityContextHolder.getSysCode());
        return success(AccountConvert.INSTANCE.convertAccountRespListVO(Objects.nonNull(accAccount) ? ListUtil.toList(accAccount) : null));
    }

    /**
     * 获取入驻商支付平台账户余额
     */
    @ApiOperation(value = "获取入驻商支付平台账户余额(小程序)", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_SUPPLIER_SETTLE)
    @RequiresPermissions(Permissions.GET_SUPPLIER_SETTLE)
    @PostMapping(value = "/query-supplier-settle")
    public CommonResult<SupplierAccountRespVO> querySupplierSettle(@ApiParam(name = "supplierId", value = "入驻商ID", required = true) @RequestParam("supplierId") Long supplierId) {
        SupplierAccountRespVO respVO = AccountConvert.INSTANCE.convertSupplierBalance(merchantService.getSupplierBalance(supplierId));
        AccPlatformMerchant platformMerchant = accPlatformMerchantService.getPlatformMerchant(supplierId, MerchantTypeEnum.SUPPLIER.getType(), respVO.getPlatform());
        AccountConvert.INSTANCE.setterSupplierBalance(respVO, platformMerchant, accountCacheService.getSupplierDTO(supplierId));
        return success(respVO);
    }

    /**
     * 通用获取账户信息
     */
    @ApiOperation(value = "通用获取账户信息(所有账户)", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @PostMapping(value = "/query")
    public CommonResult<List<AccAccountRespVO>> query(
            @ApiParam(name = "merchantId", value = "商户ID", required = true) @RequestParam("merchantId")
            Long merchantId,
            @ApiParam(name = "merchantType", value = "商户类型", required = true) @RequestParam("merchantType")
            String merchantType
    ) {
        List<AccAccount> accAccount = accAccountService.getAccountByMerchantIdAndType(merchantId, merchantType);
        return success(HutoolBeanUtils.toBean(accAccount, AccAccountRespVO.class));
    }

    /**
     * 分页查询账户
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得账户分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccAccountRespVO>> getPage(@Valid AccAccountPageReqVO pageReqVO) {
        PageResult<AccAccount> pageResult = accAccountService.getAccAccountPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, AccAccountRespVO.class));
    }


    /**
     * 修改入驻商授信金额
     */
    @PostMapping("/update-supplier-credit")
    @ApiOperation(value = "修改入驻商授信金额", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SUPPLIER_CREDIT)
    @RequiresPermissions(Permissions.SUPPLIER_CREDIT)
    public CommonResult<Boolean> updateSupplierCredit(@RequestBody @Valid AccAccountSaveCreditReqVO creditReqVO) {
        creditReqVO.setMerchantType(MerchantTypeEnum.SUPPLIER.getType());
        accAccountService.updateAccountCreditAmt(creditReqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 获取平台商账户
     */
    @ApiOperation(value = "获取平台商账户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_PARTNER)
    @RequiresPermissions(Permissions.GET_PARTNER)
    @PostMapping(value = "/query-partner")
    public CommonResult<AccAccountRespVO> queryPartnerAccount(@ApiParam(name = "accountId", value = "账户ID", required = true) @RequestParam("accountId") Long accountId) {
        AccAccount accAccount = accAccountService.getAccAccount(accountId, MerchantTypeEnum.PARTNER.getType());
        // 转换成返回类型
        AccAccountRespVO accountRespVO = AccountConvert.INSTANCE.build(
                accAccount,
                accountCacheService.getWithdrawalSetting(accAccount.getSysCode())
        );
        return success(accountRespVO);
    }

    /**
     * 获取运营商账户
     */
    @ApiOperation(value = "获取运营商账户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_DC)
    @RequiresPermissions(Permissions.GET_DC)
    @PostMapping(value = "/query-dc")
    public CommonResult<AccAccountRespVO> queryDcAccount(@ApiParam(name = "accountId", value = "账户ID", required = true) @RequestParam("accountId") Long accountId) {
        AccAccount accAccount = accAccountService.getAccAccount(accountId, MerchantTypeEnum.DC.getType());
        // 转换成返回类型
        AccAccountRespVO accountRespVO = AccountConvert.INSTANCE.build(
                accAccount,
                accountCacheService.getWithdrawalSetting(accAccount.getSysCode())
        );
        return success(accountRespVO);
    }

    /**
     * 获取软件商账户
     */
    @ApiOperation(value = "获取软件商账户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_SOFTWARE)
    @RequiresPermissions(Permissions.GET_PARTNER)
    @PostMapping(value = "/query-software")
    public CommonResult<AccAccountRespVO> querySoftwareAccount(@ApiParam(name = "accountId", value = "账户ID", required = true) @RequestParam("accountId") Long accountId) {
        AccAccount accAccount = accAccountService.getAccAccount(accountId, MerchantTypeEnum.SOFTWARE.getType());
        // 转换成返回类型
        AccAccountRespVO accountRespVO = AccountConvert.INSTANCE.build(
                accAccount,
                accountCacheService.getWithdrawalSetting(accAccount.getSysCode())
        );
        return success(accountRespVO);
    }

    /**
     * 获取业务员账户
     */
    @ApiOperation(value = "获取业务员账户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_COLONEL)
    @RequiresPermissions(Permissions.GET_COLONEL)
    @PostMapping(value = "/query-colonel")
    public CommonResult<ColonelAccountRespVO> queryColonelAccount() {
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        return success(accAccountService.getColonelAccount(colonelId));
    }

    /**
     * 获取运营商账户
     */
    @ApiOperation(value = "获取入驻商账户(后台)", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_SUPPLIER_02)
    @RequiresPermissions(Permissions.GET_SUPPLIER_02)
    @PostMapping(value = "/query-supplier")
    public CommonResult<AccAccountRespVO> querySupplierAccount(@ApiParam(name = "accountId", value = "账户ID", required = true) @RequestParam("accountId") Long accountId) {
        AccAccount accAccount = accAccountService.getAccAccount(accountId, MerchantTypeEnum.SUPPLIER.getType());
        // 转换成返回类型
        AccAccountRespVO accountRespVO = AccountConvert.INSTANCE.build(
                accAccount,
                accountCacheService.getWithdrawalSetting(accAccount.getSysCode())
        );
        return success(accountRespVO);
    }

    /**
     * 获取运营商储值结算余额
     */
    @ApiOperation(value = "获取运营商储值结算余额", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.GET_DC_SETTLE_LIST)
    @RequiresPermissions(Permissions.GET_DC_SETTLE_LIST)
    @PostMapping(value = "/queryDcRechargeSettleList")
    @DataScope(dcAlias = "sd", dcFieldAlias = SystemConstants.DC_ID)
    public CommonResult<PageResult<AccDcRechargeSettleRespVO>> getDcRechargeSettleList(@RequestBody @Valid AccDcRechargeSettlePageReqVO rechargeSettlePageReqVO) {
        return success(accAccountService.getDcRechargeSettleList(rechargeSettlePageReqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:account:add";
        /** 编辑 */
        public static final String EDIT = "account:account:edit";
        /** 删除 */
        public static final String DELETE = "account:account:remove";
        /** 列表 */
        public static final String LIST = "account:account:list";
        /** 查询 */
        public static final String GET = "account:account:query";
        /** 查询平台商账户 */
        public static final String GET_PARTNER = "account:account:query-partner";
        /** 查询软件商账户 */
        public static final String GET_SOFTWARE = "account:account:query-software";
        /** 查询入驻商余额 */
        public static final String GET_SUPPLIER = "account:account:queryBySupplier";
        /** 查询入驻商结算余额 */
        public static final String GET_SUPPLIER_SETTLE = "account:account:query-supplier-settle";
        /** 查询运营商账户 */
        public static final String GET_DC = "account:account:query-dc";
        /** 查询入驻商余额 */
        public static final String GET_SUPPLIER_02 = "account:account:query-supplier";
        /** 查询业务员账户 */
        public static final String GET_COLONEL = "account:account:query-colonel";
        /** 停用 */
        public static final String DISABLE = "account:account:disable";
        /** 启用 */
        public static final String ENABLE = "account:account:enable";
        /** 修改入驻商授信金额 */
        public static final String SUPPLIER_CREDIT = "account:account:update-supplier-credit";
        /** 查询运营商储值可结算金额 */
        public static final String GET_DC_SETTLE_LIST = "account:account:query-dc-settle-list";
    }
}
