package com.zksr.account.controller.recharge.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/24 16:45
 */
@Data
public class AccRechargeImportDtlRespVO {

    @ApiModelProperty("门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty("充值金额")
    private BigDecimal rechargeAmt;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("门店联系电话")
    private String contactPhone;

    @ApiModelProperty("余额")
    private BigDecimal balance;
}
