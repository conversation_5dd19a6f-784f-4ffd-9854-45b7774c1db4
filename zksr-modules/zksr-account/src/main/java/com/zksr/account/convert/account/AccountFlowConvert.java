package com.zksr.account.convert.account;

import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.account.vo.ApiAccAccountFlowPageVO;
import com.zksr.account.controller.flow.vo.AccAccountFlowPageReqVO;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/26
 * @desc
 */
@Mapper
public interface AccountFlowConvert {

    AccountFlowConvert INSTANCE = Mappers.getMapper(AccountFlowConvert.class);

    List<AccAccountFlowDTO> convert(List<AccAccountFlow> accountFlows);

    AccAccountFlowPageReqVO convertPageReqVO(ApiAccAccountFlowPageVO pageVO);

    PageResult<AccAccountFlowDTO> convertDTOPage(PageResult<AccAccountFlow> accAccountFlowPage);

}
