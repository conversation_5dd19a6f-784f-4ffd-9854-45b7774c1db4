package com.zksr.account.controller.account.vo;

import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 运营商储值结算余额
 * @date 2025/2/15 9:25
 */
@Data
@ApiModel(description = "运营商储值结算余额")
public class AccDcRechargeSettlePageReqVO extends SysDcPageReqVO {

    @ApiModelProperty("支付平台")
    private PayChannelEnum platform;
}
