package com.zksr.account.controller.pay;

import com.zksr.account.controller.withdraw.AccDcWithdrawController;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.service.pay.PayOrderService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单支付
 * @date 2024/3/7 10:41
 */
@Api(tags = "交易服务 - 统一订单支付", produces = "application/json")
@RestController
@RequestMapping("/pay/order")
@Validated
@Slf4j
public class PayOrderController {

    @Autowired
    private PayOrderService payOrderService;

    @ApiOperation(value = "提交支付订单", httpMethod = HttpMethod.POST)
    @PostMapping("/submit")
    public CommonResult<PayOrderRespDTO> submitPayOrder(@RequestBody PayOrderSubmitReqVO reqVO) {
        // 1. 提交支付
        reqVO.setIpAddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        PayOrderRespDTO respVO = payOrderService.submitOrder(reqVO);
        return success(respVO);
    }

    @ApiOperation(value = "创建退款单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.REFUND)
    @PostMapping("/refund")
    @RequiresPermissions(Permissions.REFUND)
    public CommonResult<PayRefundRespDTO> submitPayOrder(@RequestBody PayRefundOrderSubmitReqVO reqVO) {
        log.info("{},入参,{}",reqVO.getOrderNo(), JsonUtils.toJsonString(reqVO));
        // 1. 创建退货单
        PayRefundRespDTO respVO = payOrderService.createPayRefund(reqVO);
        return success(respVO);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 编辑 */
        public static final String REFUND = "account:pay-order:refund";
    }
}
