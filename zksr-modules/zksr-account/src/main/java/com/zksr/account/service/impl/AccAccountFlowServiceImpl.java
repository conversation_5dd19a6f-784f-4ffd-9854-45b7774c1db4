package com.zksr.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alicp.jetcache.Cache;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO;
import com.zksr.account.controller.flow.vo.AccAccountFlowPageReqVO;
import com.zksr.account.convert.account.AccountConvert;
import com.zksr.account.convert.account.AccountFlowConvert;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.mapper.AccAccountFlowMapper;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.service.IAccAccountFlowService;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccRechargeService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.domain.vo.openapi.BranchValueInfoOpenDTO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 账户流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Service
@Slf4j
public class AccAccountFlowServiceImpl implements IAccAccountFlowService {

    @Autowired
    private AccAccountFlowMapper accAccountFlowMapper;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccAccountService accountService;

    @Autowired
    private AccountMqProducer accountFlowMqChannel;

    @Autowired
    private IAccRechargeService rechargeService;

    @Autowired
    private Cache<String, AccAccountDTO> accountCache;

    @Override
    @Transactional
    public List<Long> insertAccAccountFlow(List<AccAccountFlowDTO> flowList) {
        // 转换数据
        List<AccAccountFlow> accountFlows = BeanUtil.copyToList(flowList, AccAccountFlow.class);
        ArrayList<Long> flowIds = new ArrayList<>();
        for (AccAccountFlow accountFlow : accountFlows) {
            if (StringUtils.isEmpty(accountFlow.getPlatform())) {
                throw new ServiceException(StringUtils.format("保存流水 platform 不存在, bsiId={}", accountFlow.getBusiId()));
            }
            accAccountFlowMapper.insert(accountFlow);
            flowIds.add(accountFlow.getAccountFlowId());
        }
        return flowIds;
    }

    @Override
    public void insertAccAccountFlow(AccAccountFlow accAccountFlow) {
        if (StringUtils.isEmpty(accAccountFlow.getPlatform())) {
            throw new ServiceException(StringUtils.format("保存流水 platform 不存在, bsiId={}", accAccountFlow.getBusiId()));
        }
        accAccountFlowMapper.insert(accAccountFlow);
    }

    @Override
    public List<Long> saveAccountFlowAndProcess(List<AccAccountFlowDTO> flowList) {
        List<Long> accountFlow = shelf().insertAccAccountFlow(flowList);
        try {
            processFlowByIds(accountFlow);
        } catch (Exception e) {
            log.error("发送异步处理流水异常", e);
        }
        return accountFlow;
    }

    @Override
    @Transactional
    public List<Long> saveAccountFlowAndProcessSync(List<AccAccountFlowDTO> flowList) {
        List<Long> accountFlow = insertAccAccountFlow(flowList);
        try {
            shelf().processFlowByIdsSync(accountFlow);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return accountFlow;
    }

    /**
     * 获得账户流水
     *
     * @param accountFlowId 账户流水id
     * @return 账户流水
     */
    @Override
    public AccAccountFlow getAccAccountFlow(Long accountFlowId) {
        return accAccountFlowMapper.selectById(accountFlowId);
    }

    @Override
    public List<AccAccountFlow> getAccAccountFlowByBusiNo(String busiNo, String busiype) {
        return accAccountFlowMapper.selectAccAccountFlowByBusiNo(busiNo, busiype);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccAccountFlow> getAccAccountFlowPage(AccAccountFlowPageReqVO pageReqVO) {
        return accAccountFlowMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<RechargeConsumeRespVO> getBranchAccountFlowPage(AccAccountFlowPageReqVO pageReqVO) {
        Page<RechargeConsumeRespVO> page = PageUtils.startPage(pageReqVO);
        List<RechargeConsumeRespVO> accountFlows = accAccountFlowMapper.selectBranchAccountFlowPage(pageReqVO);
        return PageResult.result(page, accountFlows);
    }

    /**
     * 异步处理账户流水
     * @param flowIdList 账户流水ID集合
     */
    @Override
    public void processFlowByIds(List<Long> flowIdList) {
        for (Long accountFlowId : flowIdList) {
            AccAccountFlow accountFlow = accAccountFlowMapper.selectById(accountFlowId);
            if (Objects.nonNull(accountFlow)) {
                accountFlowMqChannel.sendSettleFlow(accountFlow);
            }
        }
    }

    @Override
    public void processFlowByIdsSync(List<Long> flowIdList){
        for (Long accountFlowId : flowIdList) {
            AccAccountFlow accountFlow = accAccountFlowMapper.selectById(accountFlowId);
            if (Objects.nonNull(accountFlow)) {
                // 重新获取aop, 使用锁
                shelf().processFlow(accountFlow);
            }
        }
    }

    /**
     * 结算账户流水
     * @param accountFlow
     */
    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_PROCESS_FLOW, condition = "#accountFlow.merchantType + #accountFlow.merchantId", tryLock = true)
    public void processFlow(AccAccountFlow accountFlow) {
        // 要在事务方法外使用锁
        log.info("开始处理账户流水 id = {}", accountFlow.getAccountFlowId());
        AccAccountFlow accAccountFlow = accAccountFlowMapper.selectById(accountFlow.getAccountFlowId());
        if (Objects.isNull(accAccountFlow)) {
            log.error("未查询到账户流水");
            return;
        }
        if (NumberPool.INT_ONE == accAccountFlow.getProcessFlag()) {
            log.warn("账户流水已处理");
            return;
        }
        if (Objects.isNull(accAccountFlow.getMerchantId()) || StringUtils.isEmpty(accAccountFlow.getMerchantType()) || StringUtils.isEmpty(accAccountFlow.getPlatform())) {
            log.warn("账户流水处理缺少必要参数, merchantId = {}, merchantType = {}, platform = {}",  accAccountFlow.getMerchantId(), accAccountFlow.getMerchantType(), accAccountFlow.getPlatform());
            return;
        }
        AccAccount account;
        // 账户判断
        {
            if (Objects.nonNull(accAccountFlow.getAccountId())) {
                account = accountService.getAccAccount(accAccountFlow.getAccountId());
            } else {
                account = accountService.getAccountByMerchantIdAndType(
                        accAccountFlow.getMerchantId(),
                        accAccountFlow.getMerchantType(),
                        accAccountFlow.getPlatform(),
                        accAccountFlow.getAccountType()
                );
            }
            if (Objects.isNull(account)) {
                account = createAccount(accAccountFlow);
            }
            // 账户余额不能小于0
            // 余额 + 授信
            // 大区手续费 和 门店欠款账户不用判断
            // 业务金额不为空, 且不是加钱
            if (Objects.nonNull(accAccountFlow.getBusiWithdrawableAmt()) &&!NumberUtil.isGreater(accAccountFlow.getBusiWithdrawableAmt(), BigDecimal.ZERO)) {
                // 需要判断账户有没有欠款
                // 账户余额 + 授信金额是否充足
                // 不用判断授信金额, 只要保证实际账户金额在授权和真实余额可控就行
                if (NumberUtil.isGreater(BigDecimal.ZERO, account.getWithdrawableAmt().add(account.getCreditAmt()).add(accAccountFlow.getBusiWithdrawableAmt()))
                        && !MerchantTypeEnum.isPartnerFeeAccount(account.getMerchantType())
                        && !MerchantTypeEnum.isBranchDebt(account.getMerchantType())
                ) {
                    throw exception(BALANCE_ERR);
                }
            }
        }
        String[] busiFields = accAccountFlow.getBusiFields().split(StringPool.COMMA);
        BigDecimal withdrawableAmt = account.getWithdrawableAmt();
        BigDecimal frozenAmt = account.getFrozenAmt();
        BigDecimal creditAmt = account.getCreditAmt();
        for (String busiField : busiFields) {
            if (AccountBusiTypeField.WITHDRAWABLE_AMT.getField().equals(busiField) && Objects.nonNull(accAccountFlow.getBusiWithdrawableAmt())) {

                accAccountFlow.setPreWithdrawableAmt(withdrawableAmt);
                withdrawableAmt = withdrawableAmt.add(accAccountFlow.getBusiWithdrawableAmt());
                accAccountFlow.setNowWithdrawableAmt(withdrawableAmt);

            } else if (AccountBusiTypeField.FROZEN_AMT.getField().equals(busiField) && Objects.nonNull(accAccountFlow.getBusiFrozenAmt())) {

                accAccountFlow.setPreFrozenAmt(frozenAmt);
                frozenAmt = frozenAmt.add(accAccountFlow.getBusiFrozenAmt());
                accAccountFlow.setNowFrozenAmt(frozenAmt);

            } else if (AccountBusiTypeField.CREDIT_AMT.getField().equals(busiField) && Objects.nonNull(accAccountFlow.getBusiCreditAmt())) {

                accAccountFlow.setPreCreditAmt(creditAmt);
                creditAmt = creditAmt.add(accAccountFlow.getBusiCreditAmt());
                accAccountFlow.setNowCreditAmt(creditAmt);

            }
        }
        // 设置账户金额
        account.setWithdrawableAmt(withdrawableAmt)
                .setFrozenAmt(frozenAmt)
                .setCreditAmt(creditAmt);

        // 设置处理状态
        accAccountFlow.setProcessFlag(NumberPool.INT_ONE)
                .setAccountId(account.getAccountId())
                .setProcessTime(DateUtil.date());

        // CAS 更新流水
        int flowLine = accAccountFlowMapper.update(
                accAccountFlow,
                Wrappers.lambdaUpdate(AccAccountFlow.class)
                        .eq(AccAccountFlow::getAccountFlowId, accAccountFlow.getAccountFlowId())
                        .eq(AccAccountFlow::getProcessFlag, NumberPool.INT_ZERO)
        );
        if (flowLine == 0) {
            log.error("更新账户流水CAS更新错误, id = {}", accAccountFlow.getAccountFlowId());
            throw exception(ACCOUNT_FLOW_CAS_ERR);
        }
        // CAS 更新账户
        boolean accountLine = accountService.updateAccAccount(account);
        if (!accountLine) {
            log.error("更新账户CAS更新错误, id = {}", account.getAccountId());
            throw exception(ACCOUNT_CAS_ERR);
        }
        // 因为入驻商要判断是否欠款, 所以目前只需要更新入驻商缓存
        if (MerchantTypeEnum.isCacheAccount(account.getMerchantType())) {
            try {
                accountCache.put(
                        RedisConstants.getAccountKey(account.getMerchantId(), account.getMerchantType(), account.getPlatform()),
                        AccountConvert.INSTANCE.convert(account)
                );
            } catch (Exception e) {
                log.error("更新账户缓存失败", e);
            }
        }
    }

    @Override
    public CommonResult<List<AccAccountFlowDTO>> getTrySettleFlow(Long minId) {
        List<AccAccountFlow> accountFlows = accAccountFlowMapper.selectTrySettleFlow(minId);
        return CommonResult.success(AccountFlowConvert.INSTANCE.convert(accountFlows));
    }

    @Override
    public List<AccAccountFlow> getAccAccountFlowListByIds(List<Long> accountFlowIds) {
        return accAccountFlowMapper.selectBatchIds(accountFlowIds);
    }

    @Override
    public BranchValueInfoOpenDTO getBranchValueInfoByIds(List<Long> accountFlowIds) {
        return accAccountFlowMapper.getBranchValueInfoByIds(accountFlowIds);
    }

    private AccAccount createAccount(AccAccountFlow accAccountFlow) {
        AccAccount account = new AccAccount();
        account.setSysCode(accAccountFlow.getSysCode())
                .setAccountType(NumberPool.INT_ZERO)
                .setMerchantType(accAccountFlow.getMerchantType())
                .setMerchantId(accAccountFlow.getMerchantId())
                .setWithdrawableAmt(BigDecimal.ZERO)
                .setFrozenAmt(BigDecimal.ZERO)
                .setCreditAmt(BigDecimal.ZERO)
                .setPlatform(accAccountFlow.getPlatform())
                .setAccountType(accAccountFlow.getAccountType()); // 执行流水指定了账户类型, 就采用执行流水的账户类型
        // 2025年2月11日, 门店储值需求, 门店的账户托管方调整为运营商, 对运营商充值
        if (AccountBusiType.BRANCH_RECHARGE.getType().equals(accAccountFlow.getBusiType())) {
            AccRecharge recharge = rechargeService.getAccRecharge(accAccountFlow.getBusiId());
            account.setHoldMerchantId(String.valueOf(recharge.getReceiveMerhcantId()));
            account.setHoldMerchantType(recharge.getReceiveMerchantType());
        }
        // 如果平台是钱包支付, 并且是业务员, 账户托管方
        if (MerchantTypeEnum.COLONEL.getType().equals(accAccountFlow.getMerchantType()) && PayChannelEnum.WALLET.getCode().equals(accAccountFlow.getPlatform())) {
            ColonelDTO colonelDTO = accountCacheService.getColonelDTO(accAccountFlow.getMerchantId());
            AreaDTO areaDTO = accountCacheService.getAreaDTO(colonelDTO.getAreaId());
            account.setHoldMerchantType(MerchantTypeEnum.DC.getType());
            account.setHoldMerchantId(String.valueOf(areaDTO.getDcId()));
        }
        accountService.insertAccAccount(account);
        return account;
    }

    public AccAccountFlowServiceImpl shelf() {
        return SpringUtils.getAopProxy(this);
    }
}
