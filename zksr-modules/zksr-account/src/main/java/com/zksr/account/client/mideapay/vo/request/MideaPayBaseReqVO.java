package com.zksr.account.client.mideapay.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayBaseReqVO implements MideaPayReq, Serializable {

    /**
     * 商户单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 下单时间
     */
    @JsonProperty("out_trade_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private LocalDateTime outTradeTime;

    /**
     * 接口名称
     */
    @JsonProperty("service")
    private String service;

    /**
     * 接口版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 请求序列号
     */
    @JsonProperty("req_seq_no")
    private String reqSeqNo;

    /**
     * 合作者身份ID
     * 签约的唯一商户号
     * 10位纯数字组成
     */
    @JsonProperty("partner")
    private String partner;

    /**
     * 参数编码字符集
     * 商户网站使用的编码格式，目前只支持UTF-8
     */
    @JsonProperty("input_charset")
    private String inputCharset;

    /**
     * 语言类型：ZH-CN
     */
    @JsonProperty("language")
    private String language;

    /**
     * 终端类型:
     * MOBILE：移动端
     * PC：PC端
     * ITG：后台
     */
    @JsonProperty("terminal_type")
    private String terminalType;

    /**
     * 签名方式
     * MD5_RSA_TW：天威证书加密
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * 签名
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 支付成功后，美的支付通过此地址通知业务方订单状态，支持多个，英文分号分隔’;’
     */
    @JsonProperty("notify_url")
    private String notifyUrl;

    /**
     * 交易完成后跳转到商户的地址，PC端必传
     */
    @JsonProperty("return_url")
    private String returnUrl;

    @JsonProperty("token")
    private String token;

}
