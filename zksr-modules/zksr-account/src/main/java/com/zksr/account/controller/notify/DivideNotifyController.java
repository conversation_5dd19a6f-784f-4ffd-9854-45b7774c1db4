package com.zksr.account.controller.notify;

import java.util.Map;
import java.util.Objects;

import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.client.DivideClient;
import com.zksr.account.convert.divideFlow.AccDivideFlowConvert;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideReqVO;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.service.IAccDivideFlowService;
import com.zksr.account.service.pay.DivideOrderService;
import com.zksr.account.service.pay.IPayDivideOrderService;
import com.zksr.account.service.pay.PayWxB2bDivideOrderService;
import com.zksr.common.core.domain.PayCallBack;
import com.zksr.account.model.profitOrder.dto.DivideOrderRepDto;
import com.zksr.account.service.DivideService;
import com.zksr.account.service.profitOrder.DivideChannelService;
import com.zksr.account.util.MideaPayUtil;
import com.zksr.common.core.enums.PayChannelEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "分账回调", produces = "application/json")
@RestController
@RequestMapping("/pay/notify")
@Slf4j
public class DivideNotifyController {
    
    @Autowired
    private DivideService profitOrderService;
    @Autowired
    private DivideChannelService profitOrderChannelService;
    
    @Autowired
    private AccountMqProducer accountMqProducer;
    
    @Autowired
    private IAccDivideFlowService divideFlowService;
    
    @Autowired
    private DivideOrderService divideOrderService;
    
    @ApiOperation(value = "美的付支付渠道的统一【支付】回调")
    @RequestMapping(value = "/divide/mideaPay")
    //!@回调分账 - 1、总入口（美的付）
    public String mideaPayNotifyOrder(@RequestParam(required = false) Map<String, String> params, @RequestBody(required = false) String body)
    {
        PayCallBack callBack = MideaPayUtil.getMideaCallBack(params, body);
        return notifyDivide(callBack.getSysCode(),callBack.getPlatform(),params,body);
    }
    
    @ApiOperation(value = "支付分账回调")
    @RequestMapping(value = "/divide/{sysCode}/{platform}")
    public String notifyDivide(
            @ApiParam(name = "sysCode", value = "平台商ID") @PathVariable("sysCode") Long sysCode,
            @ApiParam(name = "platform", value = "支付平台") @PathVariable("platform") String platform,
            //@ApiParam(name = "merchantType", value = "商户类型") @PathVariable("merchantType") String merchantType,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body
    ) {
        log.info("[notifySubmitProfitOrder][sysCode({}),platform({}) 回调数据({}/{})]", sysCode, platform, params, body);
        // 1. 校验支付渠道是否存在
        // PayWayEnum.OFFLINE , 在线支付才有回调, 模拟支付直接回调
        DivideClient profitOrderClient = profitOrderChannelService.getDivideClient(sysCode, platform);
        if (Objects.isNull(profitOrderClient)) {
            log.info("[notifySubmitProfitOrder][sysCode({}),platform({}) 找不到对应的支付客户端]", sysCode, platform);
            return "success";
        }
        DivideOrderRepDto notify = profitOrderClient.parseDivideNotify(params, body);
        accountMqProducer.sendDivideNotity(notify);
        return "success";
    }
}