package com.zksr.account.controller.withdraw.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 提现对账单对象 acc_withdraw_bill
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@ApiModel("提现对账单 - acc_withdraw_bill分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccWithdrawBillPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /**
     * 支付渠道, hlb-合利宝,wxb2b-微信b2b
     */
    @ApiModelProperty(value = "支付渠道, hlb-合利宝,wxb2b-微信b2b")
    private String platform;

    /**
     * 提现商户号
     */
    @ApiModelProperty(value = "提现商户号")
    private String altNo;

    /**
     * 账单开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = YYYY_MM_DD)
    @ApiModelProperty(value = "账单开始时间")
    private Date finishStartTime;

    /**
     * 账单结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = YYYY_MM_DD)
    @ApiModelProperty(value = "账单结束时间")
    private Date finishEndTime;
}
