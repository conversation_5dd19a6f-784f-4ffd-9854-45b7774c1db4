package com.zksr.account.controller.transfer;

import com.zksr.account.controller.pay.PayOrderController;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.account.service.transfer.TransferAccountService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 提现操作
 * @date 2024/3/11 9:09
 */
@Api(tags = "交易服务 - 账户转账", produces = "application/json")
@RestController
@RequestMapping("/transfer")
@Validated
public class TransferAccountController {

    @Autowired
    private TransferAccountService transferAccountService;

    @ApiOperation(value = "提交转账单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.TRANSFER)
    @PostMapping("/submit")
    @RequiresPermissions(Permissions.TRANSFER)
    public CommonResult<TransferSubmitRespDTO> submitTransfer(@RequestBody TransferSubmitReqVO reqVO) {
        TransferSubmitRespDTO respVO = transferAccountService.transferSubmit(reqVO);
        return success(respVO);
    }

    @ApiOperation(value = "账户结算|提现", httpMethod = HttpMethod.POST, notes = "账户结算|提现" + StringPool.PERMISSIONS_FIX + Permissions.SETTLE_OVER)
    @PostMapping("/withdraw")
    @RequiresPermissions(Permissions.SETTLE_OVER)
    public CommonResult<TransferSettleRespDTO> withdraw(@RequestBody TransferSettleReqVO reqVO) {
        TransferSettleRespDTO respVO = transferAccountService.transferSettle(reqVO);
        return success(respVO);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 转账 */
        public static final String TRANSFER = "account:settle:transfer";
        /** 结算 */
        public static final String SETTLE_OVER = "account:settle:settle-over";
    }

    /**
     * 余额查询
     */

    /**
     * 转账查询 (非必要)
     */

    /**
     * 提现查询 (非必要)
     */

    /**
     * 转账回调
     */

    /**
     * 结算回调
     */
}
