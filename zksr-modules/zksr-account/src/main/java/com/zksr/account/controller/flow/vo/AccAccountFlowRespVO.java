package com.zksr.account.controller.flow.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 账户流水对象 acc_account_flow
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("账户流水 - acc_account_flow Response VO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AccAccountFlowRespVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 账户流水id */
    @ApiModelProperty(value = "支付平台(数据字典)")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private Long accountFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private Long sysCode;

    /** 账户id */
    @Excel(name = "账户id")
    @ApiModelProperty(value = "账户id")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private Long accountId;

    /** 以前的可提现金额 */
    @Excel(name = "以前的可提现金额")
    @ApiModelProperty(value = "以前的可提现金额")
    private BigDecimal preWithdrawableAmt;

    /** 增量可提现金额 */
    @Excel(name = "增量可提现金额")
    @ApiModelProperty(value = "增量可提现金额")
    private BigDecimal busiWithdrawableAmt;

    /** 现在的可提现金额 */
    @Excel(name = "现在的可提现金额")
    @ApiModelProperty(value = "现在的可提现金额")
    private BigDecimal nowWithdrawableAmt;

    /** 以前的冻结金额 */
    @Excel(name = "以前的冻结金额")
    @ApiModelProperty(value = "以前的冻结金额")
    private BigDecimal preFrozenAmt;

    /** 增量冻结金额 */
    @Excel(name = "增量冻结金额")
    @ApiModelProperty(value = "增量冻结金额")
    private BigDecimal busiFrozenAmt;

    /** 现在的冻结金额 */
    @Excel(name = "现在的冻结金额")
    @ApiModelProperty(value = "现在的冻结金额")
    private BigDecimal nowFrozenAmt;

    /** 以前的授信额度 */
    @Excel(name = "以前的授信额度")
    @ApiModelProperty(value = "以前的授信额度")
    private BigDecimal preCreditAmt;

    /** 增量授信额度 */
    @Excel(name = "增量授信额度")
    @ApiModelProperty(value = "增量授信额度")
    private BigDecimal busiCreditAmt;

    /** 现在的授信额度 */
    @Excel(name = "现在的授信额度")
    @ApiModelProperty(value = "现在的授信额度")
    private BigDecimal nowCreditAmt;

    /** 业务类型(待枚举，根据busi_type找busi出处) */
    @Excel(name = "参考字典account_flow_busi_type")
    @ApiModelProperty(value = "参考字典account_flow_busi_type")
    private String busiType;

    /** 业务id */
    @Excel(name = "业务id")
    @ApiModelProperty(value = "业务id")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private Long busiId;

    /** 影响字段 */
    @Excel(name = "影响字段")
    @ApiModelProperty(value = "影响字段")
    private String busiFields;

    /** 处理状态 0-未处理  1-已处理 */
    @Excel(name = "处理状态 0-未处理  1-已处理")
    @ApiModelProperty(value = "处理状态 0-未处理  1-已处理")
    private Integer processFlag;

    /** 处理时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "处理时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "处理时间")
    private Date processTime;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;

    /**
     * 账户类型;0-储值账户 1-赠送余额
     */
    @Excel(name = "账户类型;0-储值账户 1-赠送余额")
    @ApiModelProperty(value = "账户类型;0-储值账户 1-赠送余额")
    private Integer accountType;

    /**
     * 流水备注
     */
    @Excel(name = "流水备注")
    @ApiModelProperty(value = "流水备注")
    private String memo;

    /**
     * 业务单号
     */
    @Excel(name = "业务单号")
    @ApiModelProperty(value = "业务单号")
    private String busiNo;

    /**
     * 商户名称
     */
    @Excel(name = "商户名称")
    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    /**
     * 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店
     */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /**
     * 商户id
     */
    @Excel(name = "商户id")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private Long merchantId;
}
