package com.zksr.account.mapper;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.core.domain.vo.openapi.AccPayReceiptReqDTO;
import com.zksr.common.core.domain.vo.openapi.ReceiptPayOpenDTO;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayRefundStatusEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.controller.flow.vo.AccPayFlowPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 支付流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-10
 */
@Mapper
public interface AccPayFlowMapper extends BaseMapperX<AccPayFlow> {
    default PageResult<AccPayFlow> selectPage(AccPayFlowPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccPayFlow>()
                    .eqIfPresent(AccPayFlow::getPayFlowId, reqVO.getPayFlowId())
                    .eqIfPresent(AccPayFlow::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccPayFlow::getPayType, reqVO.getPayType())
                    .eqIfPresent(AccPayFlow::getTradeNo, reqVO.getTradeNo())
                    .eqIfPresent(AccPayFlow::getOutTradeNo, reqVO.getOutTradeNo())
                    .eqIfPresent(AccPayFlow::getRefundNo, reqVO.getRefundNo())
                    .eqIfPresent(AccPayFlow::getOutRefundNo, reqVO.getOutRefundNo())
                    .eqIfPresent(AccPayFlow::getOutFlowNo, reqVO.getOutFlowNo())
                    .eqIfPresent(AccPayFlow::getPayWay, reqVO.getPayWay())
                    .eqIfPresent(AccPayFlow::getPlatform, reqVO.getPlatform())
                    .eqIfPresent(AccPayFlow::getCallbackFlag, reqVO.getCallbackFlag())
                    .eqIfPresent(AccPayFlow::getCallbackTime, reqVO.getCallbackTime())
                    .eqIfPresent(AccPayFlow::getPayAmt, reqVO.getPayAmt())
                    .eqIfPresent(AccPayFlow::getRefundAmt, reqVO.getRefundAmt())
                    .eqIfPresent(AccPayFlow::getFee, reqVO.getFee())
                    .eqIfPresent(AccPayFlow::getOrderType, reqVO.getOrderType())
                    .eqIfPresent(AccPayFlow::getIsDivide, reqVO.getIsDivide())
                .orderByDesc(AccPayFlow::getPayFlowId));
    }

    default Long selectByOrderSuccess(String orderNo) {
        return selectCount(
                Wrappers.lambdaQuery(AccPayFlow.class)
                        .eq(AccPayFlow::getTradeNo, orderNo)
                        .eq(AccPayFlow::getCallbackFlag, NumberPool.INT_ONE)
        );
    }

    default AccPayFlow selectByOrderPayFlow(String orderNo) {
        List<AccPayFlow> rs = selectList(
                Wrappers.lambdaQuery(AccPayFlow.class)
                        .eq(AccPayFlow::getTradeNo, orderNo)
                        .eq(AccPayFlow::getPayType, NumberPool.INT_ZERO)
        );
//        return selectOne(
//                Wrappers.lambdaQuery(AccPayFlow.class)
//                        .eq(AccPayFlow::getTradeNo, orderNo)
//                        .eq(AccPayFlow::getPayType, NumberPool.INT_ZERO)
//        );

        return CollectionUtils.isEmpty(rs) ? null : rs.get(0);
    }

    default List<AccPayFlow> selectByOrdersPayFlow(List<String> orderNos) {
        return selectList(
                Wrappers.lambdaQuery(AccPayFlow.class)
                        .in(AccPayFlow::getTradeNo, orderNos)
                        .eq(AccPayFlow::getPayType, NumberPool.INT_ZERO)
        );
    }

    default AccPayFlow selectByOrderRefundFlow(String refundNo) {
        List<AccPayFlow> rs = selectList(
                Wrappers.lambdaQuery(AccPayFlow.class)
                        .eq(AccPayFlow::getRefundNo, refundNo)
                        .eq(AccPayFlow::getPayType, NumberPool.INT_ONE)
                        .orderByDesc(AccPayFlow::getPayFlowId)
                        .last(StringPool.LIMIT_ONE)
        );

//        return selectOne(
//                Wrappers.lambdaQuery(AccPayFlow.class)
//                        .eq(AccPayFlow::getRefundNo, refundNo)
//                        .eq(AccPayFlow::getPayType, NumberPool.INT_ONE)
//                        .orderByDesc(AccPayFlow::getPayFlowId)
//                        .last(StringPool.LIMIT_ONE)
//        );

        return CollectionUtils.isEmpty(rs) ? null : rs.get(0);
    }

    default AccPayFlow selectByOrderPayFlowSuccessFlag(String orderNo) {
        List<AccPayFlow> rs = selectList(Wrappers.lambdaQuery(AccPayFlow.class)
                .eq(AccPayFlow::getTradeNo, orderNo)
                .eq(AccPayFlow::getCallbackFlag, NumberPool.INT_ONE)
                .eq(AccPayFlow::getPayType, NumberPool.INT_ZERO));

        return CollectionUtils.isEmpty(rs) ? null : rs.get(0);
//        return selectOne(
//                Wrappers.lambdaQuery(AccPayFlow.class)
//                        .eq(AccPayFlow::getTradeNo, orderNo)
//                        .eq(AccPayFlow::getCallbackFlag, NumberPool.INT_ONE)
//                        .eq(AccPayFlow::getPayType, NumberPool.INT_ZERO)
//        );
    }

    default List<AccPayFlow> selectByWxB2bRefundProcessAgainList(Long minId) {
        return selectList(
                Wrappers.lambdaQuery(AccPayFlow.class)
                        .eq(AccPayFlow::getPayType, NumberPool.INT_ONE)
                        .eq(AccPayFlow::getCallbackFlag, NumberPool.INT_ZERO)
                        .eq(AccPayFlow::getPlatform, PayChannelEnum.WX_B2B_PAY.getCode())
                        .gt(AccPayFlow::getPayFlowId, minId)
                        // 48小时内, 5分钟前
                        .between(AccPayFlow::getCreateTime, DateUtil.offsetHour(new Date(), -48), DateUtil.offsetMinute(new Date(), -5))
                        .orderByAsc(AccPayFlow::getPayFlowId)
                        .last("LIMIT 100")
        );
    }

    default List<AccPayFlow> selectByOrderRefundFlowList(String tradeNo) {
        return selectList(
                Wrappers.lambdaQuery(AccPayFlow.class)
                        .eq(AccPayFlow::getPayType, NumberPool.INT_ONE)
                        .eq(AccPayFlow::getTradeNo, tradeNo)
                        .in(
                                AccPayFlow::getRefundStatus,
                                PayRefundStatusEnum.WAITING.getStatus(),
                                PayRefundStatusEnum.PROCESSING.getStatus(),
                                PayRefundStatusEnum.SUCCESS.getStatus()
                        )
        );
    }

    List<ReceiptPayOpenDTO> selectOrderPayInfoListBySyncOrder(@Param("reqDTO") AccPayReceiptReqDTO reqDTO);

    //!@定时器 - 1、处理微信B2B支付充值流水自动分账 - 1、获取门店充值流水
    List<AccPayFlow> selectWxB2bRechargeDivideFlow(@Param("minPayFlowId") Long minPayFlowId);

    /**
     * 根据PayType和TradeNo查询唯一支付流水
     */
    default AccPayFlow selectByPayTypeAndTradeNo(Integer payType, String tradeNo) {
        if(StringUtils.isEmpty(tradeNo)){
            return null;
        }
        List<AccPayFlow> rs = selectList(
                Wrappers.lambdaQuery(AccPayFlow.class)
                        .eq(AccPayFlow::getPayType, payType)
                        .eq(AccPayFlow::getTradeNo, tradeNo)
                        .eq(AccPayFlow::getCallbackFlag, NumberPool.INT_ONE)
        );
        return CollectionUtils.isEmpty(rs) ? null : rs.get(0);
    }
}
