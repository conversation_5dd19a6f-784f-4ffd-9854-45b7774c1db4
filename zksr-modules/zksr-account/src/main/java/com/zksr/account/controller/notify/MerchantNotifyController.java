package com.zksr.account.controller.notify;

import com.zksr.account.api.platformMerchant.vo.PlatformMerchantRegisterSaveRespVO;
import com.zksr.account.client.MerchantClient;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.metchant.MerchantChannelService;
import com.zksr.common.core.enums.PayTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付商户变更回调
 * @date 2024年7月13日
 */
@Api(tags = "交易服务 - 支付商户变更回调", produces = "application/json")
@RestController
@RequestMapping("/platformMerchant/notify")
@Validated
@Slf4j
public class MerchantNotifyController {

    @Autowired
    private MerchantChannelService merchantChannelService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @ApiOperation(value = "支付渠道的统一【进件】回调")
    @RequestMapping(value = "/register/{sysCode}/{payType}/{platform}")
    public String notifyMerchantRegister(
            @ApiParam(name = "sysCode", value = "平台商ID") @PathVariable("sysCode") Long sysCode,
            @ApiParam(name = "payType", value = "支付体系pay-商城收款系统, store-分润体系") @PathVariable("payType") String payType,
            @ApiParam(name = "platform", value = "支付平台") @PathVariable("platform") String platform,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body
    ) {
        // 日志记录
        log.info("[notifyMerchantRegister][sysCode({}),payType({}),platform({}) 回调数据({}/{})]", sysCode, payType, platform, params, body);
        // 获取商户操作client
        MerchantClient client = merchantChannelService.getMerchantClient(sysCode, PayTypeEnum.parsePayType(payType), platform);
        if (Objects.isNull(client)) {
            log.error("商户client 未查询到");
            return "fail";
        }
        // 解析进件回调结果
        PlatformMerchantRegisterSaveRespVO respVO = client.parseRegisterNotify(params, body);
        // 更新进件状态
        platformMerchantService.updateRegisterStatus(respVO);
        return "success";
    }
}
