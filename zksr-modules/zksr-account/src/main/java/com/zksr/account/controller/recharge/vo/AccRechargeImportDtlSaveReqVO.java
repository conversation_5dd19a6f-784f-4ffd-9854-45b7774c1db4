package com.zksr.account.controller.recharge.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * PC后台导入充值详情
对象 acc_recharge_import_dtl
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@ApiModel("PC后台导入充值详情")
public class AccRechargeImportDtlSaveReqVO {

    @ApiModelProperty("导入批次ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeImportId;

    @ApiModelProperty("门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    @NotNull(message = "门店ID必填")
    private Long branchId;

    @ApiModelProperty("充值金额")
    @NotNull(message = "充值金额必填")
    @DecimalMin(value = "0.01", message = "至少充值一分钱")
    @DecimalMax(value = "1000000", message = "最大充值1000000元")
    private BigDecimal rechargeAmt;
}
