package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.controller.transfer.vo.AccTransferPageReqVO;
import com.zksr.account.controller.transfer.vo.AccTransferSaveReqVO;
import com.zksr.account.domain.AccTransfer;
import com.zksr.common.core.web.pojo.PageResult ;

import java.util.List;

/**
 * 账户转账单Service接口
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
public interface IAccTransferService {

    /**
     * 新增账户转账单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public AccTransfer insertAccTransfer(@Valid AccTransferSaveReqVO createReqVO);

    /**
     * 新增账户转账单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public List<AccTransfer> insertBatchAccTransfer(@Valid List<AccTransferSaveReqVO> createReqVO);

    /**
     * 修改账户转账单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccTransfer(@Valid AccTransferSaveReqVO updateReqVO);

    /**
     * 获得账户转账单
     *
     * @param transferId 账户转账单id
     * @return 账户转账单
     */
    public AccTransfer getAccTransfer(Long transferId);

    /**
     * 获得账户转账单分页
     *
     * @param pageReqVO 分页查询
     * @return 账户转账单分页
     */
    PageResult<AccTransfer> getAccTransferPage(AccTransferPageReqVO pageReqVO);

    /**
     * 获取转账单重试列表数据
     * @param minId 最小ID
     * @return  每批次返回1000条重试数据
     */
    List<AccTransfer> getRetryAccTransfer(Long minId);

    /**
     * 处理账户转账
     * @param transfer
     */
    void processTransfer(AccTransfer transfer);
}
