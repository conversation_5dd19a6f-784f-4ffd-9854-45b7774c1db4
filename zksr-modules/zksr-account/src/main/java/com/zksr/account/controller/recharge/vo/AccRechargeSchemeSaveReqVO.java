package com.zksr.account.controller.recharge.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.account.api.recharge.vo.RechargeSchemeContentVO;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 储值充值套餐配置对象 acc_recharge_scheme
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Data
@ApiModel("储值充值套餐配置 - acc_recharge_scheme分页 Request VO")
public class AccRechargeSchemeSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 充值方案id */
    @ApiModelProperty(value = "充值金额，赠送金额")
    private Long rechargeSchemeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 方案名称 */
    @Excel(name = "方案名称")
    @ApiModelProperty(value = "方案名称")
    private String schemeName;

    /** 适用区域城市 */
    @Excel(name = "适用区域城市")
    @ApiModelProperty(value = "适用区域城市")
    private String areaIds;

    /** 生效开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效开始时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "生效开始时间")
    @NotNull(message = "生效时间必填")
    private Date startTime;

    /** 生效结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "生效结束时间", width = 30, dateFormat = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "生效结束时间")
    @NotNull(message = "生效时间必填")
    private Date endTime;

    /** 充值金额，赠送金额 */
    @Excel(name = "充值金额，赠送金额")
    @ApiModelProperty(value = "充值金额，赠送金额")
    @Size(min = 1, max = 20, message = "至少一个规则, 最大20个规则")
    private List<RechargeSchemeContentVO> ruleJson;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    @ApiModelProperty(value = "状态0=正常,1=停用")
    private Integer status;

}
