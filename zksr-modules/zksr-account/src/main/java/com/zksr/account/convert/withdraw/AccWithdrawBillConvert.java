package com.zksr.account.convert.withdraw;


import com.zksr.account.controller.withdraw.vo.AccWithdrawBillRespVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawBillSaveReqVO;
import com.zksr.account.domain.AccWithdrawBill;
import com.zksr.common.core.web.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
/**
* 提现对账单 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-11-05
*/
@Mapper
public interface AccWithdrawBillConvert {

    AccWithdrawBillConvert INSTANCE = Mappers.getMapper(AccWithdrawBillConvert.class);

    AccWithdrawBillRespVO convert(AccWithdrawBill accWithdrawBill);

    AccWithdrawBill convert(AccWithdrawBillSaveReqVO accWithdrawBillSaveReq);

    PageResult<AccWithdrawBillRespVO> convertPage(PageResult<AccWithdrawBill> accWithdrawBillPage);
}