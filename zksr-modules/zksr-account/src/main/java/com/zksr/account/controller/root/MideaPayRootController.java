package com.zksr.account.controller.root;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.service.rootMock.RootMockPayService;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import static com.zksr.common.core.web.pojo.CommonResult.success;

@Api(tags = "管理后台 - 美的支付接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/root/mideaPay")
@Slf4j
public class MideaPayRootController {

    @Autowired
    private RootMockPayService rootMockPayService;

    /**
     * 模拟退款
     */
    @ApiOperation(value = "模拟退款", httpMethod = "POST")
    @PostMapping("/mockRefund")
    @Log(title = "模拟退款", businessType = BusinessType.UPDATE)
    public CommonResult<PayRefundRespDTO> mockRefund(@RequestBody PayRefundOrderSubmitReqVO reqVO) {
        return rootMockPayService.mockRefund(reqVO);
    }
}
