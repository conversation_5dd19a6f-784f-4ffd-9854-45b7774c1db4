package com.zksr.account.service.pay;


import cn.hutool.core.util.NumberUtil;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.pay.vo.CreateDivideReqVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2024/3/7
 * @desc 微信B2B 支付特殊逻辑处理
 */
public interface PayWxB2bDivideOrderService extends IPayDivideOrderService {

}
