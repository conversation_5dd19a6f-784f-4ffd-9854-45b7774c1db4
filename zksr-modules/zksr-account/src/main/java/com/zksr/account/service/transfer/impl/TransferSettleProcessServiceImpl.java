package com.zksr.account.service.transfer.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.account.domain.AccWithdrawFlow;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.service.IAccWithdrawFlowService;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.account.service.transfer.TransferSettleProcessService;
import com.zksr.common.core.enums.WithdrawStateEnum;
import com.zksr.common.core.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 提现流程控制
 * @date 2024/3/11 16:37
 */
@Slf4j
@Service
public class TransferSettleProcessServiceImpl implements TransferSettleProcessService {

    @Autowired
    private IAccWithdrawFlowService  withdrawFlowService;

    @Override
    public void notifyProcessing(TransferSettleRespDTO transferSettleRespDTO) {
        // 获取流水单
        AccWithdrawFlow withdrawFlow = withdrawFlowService.getAccWithdrawFlow(transferSettleRespDTO.getWithdrawNo());
        // 更新
        AccWithdrawFlow updateFlow = new AccWithdrawFlow();
        updateFlow.setProcessingTime(DateUtil.date())
                  .setState(WithdrawStateEnum.PROCESSING.getState())
                  .setWithdrawFlowId(withdrawFlow.getWithdrawFlowId())
                  ;
        withdrawFlowService.updateWithdrawFlow(updateFlow);
        // 处理提现单
        getWithdrawService().processWithdrawProcessing(withdrawFlowService.getAccWithdrawFlow(withdrawFlow.getWithdrawFlowId()));
    }

    @Override
    @Transactional
    public void notifySuccess(TransferSettleRespDTO transferSettleRespDTO) {
        // 获取流水单
        AccWithdrawFlow withdrawFlow = withdrawFlowService.getAccWithdrawFlow(transferSettleRespDTO.getWithdrawNo());
        if (Objects.isNull(withdrawFlow) || withdrawFlow.getState().equals(WithdrawStateEnum.FINISH.getState())) {
            log.error("提现流水已经处理, withdrawNo={}", transferSettleRespDTO.getWithdrawNo());
            return;
        }
        // 更新
        AccWithdrawFlow updateFlow = new AccWithdrawFlow();
        updateFlow.setFinishTime(DateUtil.date())
                .setState(WithdrawStateEnum.FINISH.getState())
                .setWithdrawFlowId(withdrawFlow.getWithdrawFlowId())
                .setOutFlowNo(withdrawFlow.getOutFlowNo())
                .setBankAccountName(transferSettleRespDTO.getAccountName())
                .setBankAccountNo(transferSettleRespDTO.getAccountNo())
                .setFree(transferSettleRespDTO.getSettleFee())
        ;
        withdrawFlowService.updateWithdrawFlow(updateFlow);
        // 处理提现单
        getWithdrawService().processWithdrawSuccess(withdrawFlowService.getAccWithdrawFlow(withdrawFlow.getWithdrawFlowId()));
    }

    @Override
    public void notifyFail(TransferSettleRespDTO transferSettleRespDTO) {
        // 提现失败
        // 获取流水单
        AccWithdrawFlow withdrawFlow = withdrawFlowService.getAccWithdrawFlow(transferSettleRespDTO.getWithdrawNo());
        // 更新
        AccWithdrawFlow updateFlow = new AccWithdrawFlow();
        updateFlow.setFinishTime(DateUtil.date())
                .setState(WithdrawStateEnum.FAIL.getState())
                .setWithdrawFlowId(withdrawFlow.getWithdrawFlowId())
                .setErrorReason(transferSettleRespDTO.getMsg())
        ;
        withdrawFlowService.updateWithdrawFlow(updateFlow);
        // 处理提现单
        getWithdrawService().processWithdrawFail(withdrawFlowService.getAccWithdrawFlow(withdrawFlow.getWithdrawFlowId()));
    }

    public IAccWithdrawService getWithdrawService() {
        return SpringUtils.getBean(IAccWithdrawService.class);
    }
}
