package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 支付平台提现流水对象 acc_withdraw_flow
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@TableName(value = "acc_withdraw_flow")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccWithdrawFlow extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 提现流水id */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long withdrawFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 业务类型(数据字典) */
    @Excel(name = "业务类型(数据字典)")
    private String busiType;

    /** 业务单据id */
    @Excel(name = "业务单据id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long busiId;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

    /** 提现方商户类型 */
    @Excel(name = "提现方商户类型")
    private String merchantType;

    /** 提现方商户id */
    @Excel(name = "提现方商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 提现方分账方商户编号 */
    @Excel(name = "提现方分账方商户编号")
    private String altMchNo;

    /** 提现方分账方商户名 */
    @Excel(name = "提现方分账方商户名")
    private String altMchName;

    /** 提现单号;传给支付平台的 */
    @Excel(name = "提现单号;传给支付平台的")
    private String withdrawNo;

    /** 提现金额 */
    @Excel(name = "提现金额")
    private BigDecimal withdrawAmt;

    /** 提现状态;0-已申请 1-处理中 2-审核拒绝 3-已完成 4-提现失败 */
    @Excel(name = "提现状态;0-已申请 1-处理中 2-审核拒绝 3-已完成 4-提现失败")
    private Integer state;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initTime;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date processingTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishTime;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 失败原因 */
    @Excel(name = "失败原因")
    private String errorReason;

    /** 到账银行卡号 */
    @Excel(name = "到账银行卡号")
    private String bankAccountNo;

    /** 到账银行卡账户名 */
    @Excel(name = "到账银行卡账户名")
    private String bankAccountName;

    /** 支付平台流水号;一般回调会返回 */
    @Excel(name = "支付平台流水号;一般回调会返回")
    private String outFlowNo;

    /** 实际到账金额 */
    @Excel(name = "实际到账金额")
    private BigDecimal remitAmt;

    /** 到账手续费 */
    @Excel(name = "到账手续费")
    private BigDecimal free;
}
