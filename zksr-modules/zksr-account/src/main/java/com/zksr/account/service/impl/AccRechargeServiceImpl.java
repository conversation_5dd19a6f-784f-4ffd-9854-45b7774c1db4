package com.zksr.account.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.zksr.account.api.recharge.dto.RechargeSchemeDTO;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveReqVO;
import com.zksr.account.api.recharge.vo.RechargeSchemeContentVO;
import com.zksr.account.controller.recharge.vo.AccRechargePageReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSaveReqVO;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.enums.recharge.RechargeSource;
import com.zksr.account.mapper.AccAccountFlowMapper;
import com.zksr.account.mapper.AccRechargeMapper;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccRechargeSchemeService;
import com.zksr.account.service.IAccRechargeService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.business.AccountType;
import com.zksr.common.core.constant.PayConstants;
import com.zksr.common.core.enums.ChargeTypeEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayWayEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.DcOtherSettingPolicyDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.sysconfig.GlobalPayConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 账户充值单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class AccRechargeServiceImpl implements IAccRechargeService {

    @Autowired
    private AccRechargeMapper accRechargeMapper;

    @Autowired
    private RedisService  redisService;

    @Autowired
    private AccAccountFlowMapper accAccountFlowMapper;

    @Autowired
    private SupplierApi supplierApi;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Autowired
    private IAccRechargeSchemeService accRechargeSchemeService;

    @Resource
    private PartnerApi partnerApi;

    @Autowired
    private AccountMqProducer accountMqProducer;

    /**
     * 根据充值单号获取充值单
     * @param rechargeNo    充值单号
     * @return  充值单
     */
    @Override
    public AccRecharge getRechargeByNo(String rechargeNo) {
        return accRechargeMapper.selectRechargeByNo(rechargeNo);
    }

    /**
     * 获得账户充值单
     *
     * @param rechargeId 账户充值单id
     * @return 账户充值单
     */
    @Override
    public AccRecharge getAccRecharge(Long rechargeId) {
        return accRechargeMapper.selectById(rechargeId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccRecharge> getAccRechargePage(AccRechargePageReqVO pageReqVO) {
        // 入驻商数据隔离
        if (Objects.nonNull(SecurityUtils.getSupplierId())) {
            pageReqVO.setRechargeMerchantId(SecurityUtils.getSupplierId());
            pageReqVO.setState(NumberPool.INT_ONE);
        }
        // 运营商数据隔离, 只查询充值托管是自己的
        if (Objects.nonNull(SecurityUtils.getDcId())) {
            pageReqVO.setReceiveMerhcantId(SecurityUtils.getDcId());
            pageReqVO.setState(NumberPool.INT_ONE);
        }
        return accRechargeMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<AccRechargeRespVO> getAccRechargeExtPage(AccRechargePageReqVO pageReqVO) {
        // 入驻商数据隔离
        if (Objects.nonNull(SecurityUtils.getSupplierId())) {
            pageReqVO.setRechargeMerchantId(SecurityUtils.getSupplierId());
            pageReqVO.setState(NumberPool.INT_ONE);
        }
        // 运营商数据隔离, 只查询充值托管是自己的
        if (Objects.nonNull(SecurityUtils.getDcId())) {
            pageReqVO.setReceiveMerhcantId(SecurityUtils.getDcId());
            pageReqVO.setState(NumberPool.INT_ONE);
        }
        Page<AccRechargeRespVO> page = PageUtils.startPage(pageReqVO);
        List<AccRechargeRespVO> list = accRechargeMapper.selectExtPage(pageReqVO);
        return PageResult.result(page, list);
    }

    /**
     * 保存入驻商充值单
     * @param createReqVO
     * @return
     */
    @Override
    public String insertSupplierAccRecharge(AccRechargeSaveReqVO createReqVO) {
        // 插入
        AccRecharge accRecharge = HutoolBeanUtils.toBean(createReqVO, AccRecharge.class);
        // 支付单号保持唯一
        accRecharge.setRechargeNo("CZ" + redisService.getUniqueNumber());
        Long supplierId = SecurityUtils.getLoginUser().getSysUser().getSupplierId();
        if (Objects.isNull(supplierId)) {
            // 只能入驻商权限操作
            throw exception(NON_TENANT_SUPPLIER);
        }
        SupplierDTO supplierInfoDTO = supplierApi.getBySupplierId(supplierId).getCheckedData();
        accRecharge.setRechargeMerchantId(supplierId)                           // 入驻商ID
                .setRechargeMerchantType(MerchantTypeEnum.SUPPLIER.getType())   // supplier 充值方为入驻商
                .setChargeType(ChargeTypeEnum.SUPPLIER.getType())               // 充值类型入驻商
                .setReceiveMerchantType(MerchantTypeEnum.PARTNER.getType())     // 收款方为平台
                .setPayWay(PayWayEnum.ONLINE.getPayWay())                       // 在线支付方式, 支付的时候会根据平台设置, 转发到mock支付
                .setState(NumberPool.INT_ZERO); // 未支付

        // 计算手续费
        extractedRechargeFree(accRecharge);
        // 设置sysCode
        if (Objects.isNull(SecurityUtils.getLoginUser().getSysUser().getSysCode())) {
            accRecharge.setSysCode(supplierInfoDTO.getSysCode());
        }
        accRechargeMapper.insert(accRecharge);
        // 返回
        return accRecharge.getRechargeNo();
    }

    @Override
    public String insertBranchAccRecharge(BranchRechargeSaveReqVO createReqVO) {
        // 插入
        AccRecharge accRecharge = new AccRecharge();
        accRecharge.setRechargeAmt(createReqVO.getRechargeAmt());
        // 支付单号保持唯一
        accRecharge.setRechargeNo("CZ" + redisService.getUniqueNumber());

        // 获取门店运营商
        BranchDTO branchDTO = accountCacheService.getBranchDTO(createReqVO.getBranchId());
        AreaDTO areaDTO = accountCacheService.getAreaDTO(branchDTO.getAreaId());
        if (Objects.isNull(areaDTO.getDcId()) || Objects.isNull(areaDTO)) {
            throw exception(STORE_AREA_NOT_DC);
        }
        Long dcId = areaDTO.getDcId();
        accRecharge.setRechargeMerchantId(createReqVO.getBranchId())            // 门店ID
                .setRechargeMerchantType(MerchantTypeEnum.BRANCH.getType())     // branch 充值方为门店
                .setChargeType(ChargeTypeEnum.BRANCH.getType())                 // 充值类型入驻商
                .setReceiveMerchantType(MerchantTypeEnum.DC.getType())          // 收款方为平台
                .setReceiveMerhcantId(dcId)                                     // 收款方ID
                .setPayWay(PayWayEnum.ONLINE.getPayWay())                       // 在线支付方式, 支付的时候会根据平台设置, 转发到mock支付
                .setState(NumberPool.INT_ZERO)                                  // 未支付
                .setSysCode(branchDTO.getSysCode());

        // 计算手续费
        extractedRechargeFree(accRecharge);
        // 验证门店充值分润商户
        validateBranchRechargeMerchant(accRecharge);

        // 计算手续费
        accRecharge.setFee(PayConstants.PAY_FREE.multiply(accRecharge.getRechargeAmt()).setScale(2, RoundingMode.HALF_UP));
        if (NumberUtil.equals(accRecharge.getFee(), BigDecimal.ZERO)) {
            // 最低一分钱手续费
            accRecharge.setFee(PayConstants.MIN_FEE);
        }
        // 设置sysCode
        accRechargeMapper.insert(accRecharge);
        // 返回
        return accRecharge.getRechargeNo();
    }


    @Override
    public List<OrderSettlementDTO> getBranchSettles(AccRecharge recharge, PayConfigDTO configDTO) {
        List<OrderSettlementDTO> settles = new ArrayList<>();
        // 门店充值 结算方是 运营商
        AccPlatformMerchant merchant = platformMerchantService.getPlatformMerchant(recharge.getReceiveMerhcantId(), MerchantTypeEnum.DC.getType(), configDTO.getWalletPayPlatform());
        if (Objects.isNull(merchant)) {
            throw exception(NOT_EXIST_SUPPLIER_MERCHANT);
        }
        BigDecimal totalAmt = recharge.getRechargeAmt().subtract(recharge.getFee());

        // 运营商其他配置
        DcOtherSettingPolicyDTO dcOtherSettingPolicy = accountCacheService.getDcOtherSettingPolicy(recharge.getReceiveMerhcantId());
        // 软件商充值分润比例
        BigDecimal softwareRate = StringUtils.isNotEmpty(configDTO.getWalletSoftwareRate()) ? new BigDecimal(configDTO.getWalletSoftwareRate()) : BigDecimal.ZERO;
        // 平台商充值分润比例
        BigDecimal partnerRate = Objects.nonNull(dcOtherSettingPolicy) && StringUtils.isNotEmpty(dcOtherSettingPolicy.getWalletPartnerRate()) ? new BigDecimal(dcOtherSettingPolicy.getWalletPartnerRate()) : BigDecimal.ZERO;
        // 商户是否存在
        if (NumberUtil.isGreater(softwareRate, BigDecimal.ZERO)) {
            PartnerDto partnerDto = partnerApi.getBySysCode(merchant.getSysCode()).getCheckedData();
            // 软件商户
            AccPlatformMerchant software = platformMerchantService.getPlatformMerchant(partnerDto.getSoftwareId(), MerchantTypeEnum.SOFTWARE.getType(), configDTO.getWalletPayPlatform());
            if (Objects.isNull(software)) {
                throw exception(RECHARGE_MERCHANT_ERR_1);
            }
            BigDecimal profit = recharge.getRechargeAmt().multiply(softwareRate).setScale(2, RoundingMode.DOWN);
            totalAmt = totalAmt.subtract(profit);
            settles.add(new OrderSettlementDTO(software.getAltMchNo(), software.getMerchantId(), software.getMerchantType(), profit));
        }
        if (NumberUtil.isGreater(partnerRate, BigDecimal.ZERO)) {
            // 平台商商户
            AccPlatformMerchant partner = platformMerchantService.getPlatformMerchant(recharge.getSysCode(), MerchantTypeEnum.PARTNER.getType(), configDTO.getWalletPayPlatform());
            if (Objects.isNull(partner)) {
                throw exception(RECHARGE_MERCHANT_ERR_2);
            }
            BigDecimal profit = recharge.getRechargeAmt().multiply(softwareRate).setScale(2, RoundingMode.DOWN);
            totalAmt = totalAmt.subtract(profit);
            settles.add(new OrderSettlementDTO(partner.getAltMchNo(), partner.getMerchantId(), partner.getMerchantType(), profit));
        }
        // 运营商拿剩余的
        settles.add(new OrderSettlementDTO(merchant.getAltMchNo(), merchant.getMerchantId(), merchant.getMerchantType(), totalAmt));
        // 分账大于0的分账方
        return settles.stream().filter(item -> NumberUtil.isGreater(item.getAmt(), BigDecimal.ZERO)).collect(Collectors.toList());
    }

    /**
     * 充值支付成功
     * @param recharge
     */
    @Override
    @Transactional
    public List<AccAccountFlow> rechargeSuccess(AccRecharge recharge) {
        // 简单判断是否支付
        if (recharge.getState() == NumberPool.INT_ONE) {
            throw exception(RECHARGE_REPEAT);
        }
        AccRecharge updateRecharge = new AccRecharge();
        updateRecharge.setState(NumberPool.INT_ONE)
                .setPlatform(recharge.getPlatform())
                .setPayWay(recharge.getPayWay());

        // CAS 比较更新, 如果没回调
        int line = accRechargeMapper.update(updateRecharge,
                Wrappers.lambdaUpdate(AccRecharge.class)
                        .eq(AccRecharge::getRechargeId, recharge.getRechargeId())
                        .eq(AccRecharge::getState, NumberPool.INT_ZERO)
        );
        if (line == 0) {
            throw exception(RECHARGE_REPEAT);
        }
        if (recharge.isSupplier()) {
            return supplierRecharge(recharge);
        } else {
            return branchRecharge(recharge);
        }
    }

    /**
     * 入驻商充值
     * @param recharge  充值单
     * @return  入驻商账户执行流水
     */
    private List<AccAccountFlow> supplierRecharge(AccRecharge recharge) {
        List<AccAccountFlow> result = new ArrayList<>();
        // 有效充值金额
        BigDecimal busiAmt = recharge.getRechargeAmt();
        // 增加入驻商余额流水
        {
            AccAccountFlow accAccountFlow = new AccAccountFlow();
            accAccountFlow.setMerchantType(recharge.getRechargeMerchantType())
                    .setSysCode(recharge.getSysCode())
                    .setMerchantId(recharge.getRechargeMerchantId())
                    .setBusiWithdrawableAmt(busiAmt)
                    .setBusiType(AccountBusiType.SUPPLIER_RECHARGE.getType())
                    .setBusiId(recharge.getRechargeId())
                    .setPlatform(recharge.getPlatform())
                    .setBusiNo(recharge.getRechargeNo())
                    .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField());
            accAccountFlowMapper.insert(accAccountFlow);
            result.add(accAccountFlow);
        }
        // 扣减补贴账户余额
        // 补贴账户余额由平台商手续费账户
        {
            AccAccountFlow accAccountFlow = new AccAccountFlow();
            accAccountFlow.setMerchantType(MerchantTypeEnum.PARTNER_FREE.getType())
                    .setSysCode(recharge.getSysCode())
                    .setMerchantId(recharge.getSysCode())
                    .setBusiWithdrawableAmt(BigDecimal.ZERO.subtract(recharge.getFee()))
                    .setBusiType(
                        AccountBusiType.RECHARGE_FEE.getType()
                    )
                    .setBusiId(recharge.getRechargeId())
                    .setPlatform(recharge.getPlatform())
                    .setBusiNo(recharge.getRechargeNo())
                    .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField());
            accAccountFlowMapper.insert(accAccountFlow);
            result.add(accAccountFlow);
        }
        return result;
    }

    /**
     * 门店充值
     * @param recharge  充值单
     * @return  门店账户执行流水
     */
    private List<AccAccountFlow> branchRecharge(AccRecharge recharge) {
        List<AccAccountFlow> result = new ArrayList<>();
        // 有效充值金额
        BigDecimal busiAmt = recharge.getRechargeAmt();
        // 判断活动, 是否还需要赠送
        // 先不管赠送
        {
            AccAccountFlow accAccountFlow = new AccAccountFlow();
            accAccountFlow.setMerchantType(recharge.getRechargeMerchantType())
                    .setSysCode(recharge.getSysCode())
                    .setMerchantId(recharge.getRechargeMerchantId())
                    .setBusiWithdrawableAmt(busiAmt)
                    .setBusiType(AccountBusiType.BRANCH_RECHARGE.getType())
                    .setBusiId(recharge.getRechargeId())
                    .setBusiNo(recharge.getRechargeNo())
                    .setPlatform(PayChannelEnum.WALLET.getCode()) // 使用记账平台记账, 不需要知道门店的金额来自具体哪个支付平台
                    .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField())
                    .setAccountType(AccountType.BASE_BALANCE.getAccountType())
            ;
            accAccountFlowMapper.insert(accAccountFlow);
            result.add(accAccountFlow);
        }
        BranchDTO branchDTO = accountCacheService.getBranchDTO(recharge.getRechargeMerchantId());
        // 匹配套餐数据
        // PC 充值不赠送任何金额
        RechargeSchemeDTO rechargeScheme = accRechargeSchemeService.getValidAreaRechargeScheme(branchDTO.getAreaId());
        if (Objects.nonNull(rechargeScheme) && !RechargeSource.PC.getSource().equals(recharge.getSource())) {
            // 赠送金额
            RechargeSchemeContentVO rule = rechargeScheme.buildGiveAmt(recharge.getRechargeAmt());
            if (Objects.nonNull(rule)) {
                BigDecimal giveAmt = Objects.isNull(rule.getGiveAmt()) || NumberUtil.isGreater(BigDecimal.ZERO, rule.getGiveAmt()) ? BigDecimal.ZERO : rule.getGiveAmt();
                // 更新数据, 充值方案
                AccRecharge update = new AccRecharge();
                update.setRechargeId(recharge.getRechargeId());
                update.setRechargeSchemeId(rechargeScheme.getRechargeSchemeId());
                update.setSchemeRuleJson(JSON.toJSONString(rule));
                update.setGiveAmt(giveAmt);
                accRechargeMapper.updateById(update);

                // 赠送余额
                AccAccountFlow accAccountFlow = new AccAccountFlow();
                accAccountFlow.setMerchantType(recharge.getRechargeMerchantType())
                        .setSysCode(recharge.getSysCode())
                        .setMerchantId(recharge.getRechargeMerchantId())
                        .setBusiWithdrawableAmt(giveAmt)
                        .setBusiType(AccountBusiType.BRANCH_RECHARGE_GIVE.getType())
                        .setBusiId(recharge.getRechargeId())
                        .setBusiNo(recharge.getRechargeNo())
                        .setPlatform(PayChannelEnum.WALLET.getCode()) // 使用记账平台记账, 不需要知道门店的金额来自具体哪个支付平台
                        .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField())
                        .setAccountType(AccountType.GIFT_BALANCE.getAccountType())
                ;
                accAccountFlowMapper.insert(accAccountFlow);
                result.add(accAccountFlow);
            }
        }

        //发送门店储值充值通知--至第三方
        try{
            if(ToolUtil.isNotEmpty(result) && !PayChannelEnum.NONE.getCode().equals(recharge.getPlatform())){
                accountMqProducer.sendSyncBranchValueInfo(result.stream().map(AccAccountFlow::getAccountFlowId).collect(Collectors.toList()));
            }
        }catch (Exception e){
            log.error("发送门店储值充值通知--至第三方异常，推送异常资金流失ID集合：{}，",result,e);
        }

        return result;
    }

    /**
     * 计算充值手续费
     * @param accRecharge   充值单
     */
    private void extractedRechargeFree(AccRecharge accRecharge) {
        PayConfigDTO configDTO = accountCacheService.getPayConfigDTO(accRecharge.getSysCode());
        if (Objects.isNull(configDTO) || StringUtils.isEmpty(configDTO.getInteriorStoredPayPlatform())) {
            throw exception(NOT_EXIST_PAY_CONFIG);
        }
        GlobalPayConfigDTO globalPayConfig;
        if (accRecharge.isSupplier()) {
            // 入驻商属于储值平台
            globalPayConfig = redisSysConfigService.getGlobalPayConfig(configDTO.getInteriorStoredPayPlatform());
        } else {
            // 门店其他属于商城支付平台
            globalPayConfig = redisSysConfigService.getGlobalPayConfig(configDTO.getWalletPayPlatform());
        }
        if (Objects.nonNull(globalPayConfig)) {
            // 计算手续费
            accRecharge.setFee(globalPayConfig.getPay().getFreeRate().multiply(accRecharge.getRechargeAmt()).setScale(2, RoundingMode.HALF_UP));
        } else {
            // 模拟支付不需要手续费
            accRecharge.setFee(BigDecimal.ZERO);
        }
        if (NumberUtil.equals(accRecharge.getFee(), BigDecimal.ZERO) && Objects.nonNull(globalPayConfig) && !NumberUtil.equals(globalPayConfig.getPay().getFreeRate(), BigDecimal.ZERO)) {
            accRecharge.setFee(PayConstants.MIN_FEE);
        }
    }

    /**
     * 验证门店充值分润商户信息
     * @param accRecharge   充值单
     */
    private void validateBranchRechargeMerchant(AccRecharge accRecharge) {
        // 支付配置
        PayConfigDTO configDTO = accountCacheService.getPayConfigDTO(accRecharge.getSysCode());
        if (Objects.isNull(configDTO) || StringUtils.isEmpty(configDTO.getWalletPayPlatform())) {
            throw exception(NOT_EXIST_PAY_CONFIG);
        }
        // 没开启钱包支付
        if (!StringPool.ONE.equals(configDTO.getSwitchWalletPay())) {
            throw exception(NOT_OPEN_WALLET);
        }
        DcOtherSettingPolicyDTO dcOtherSettingPolicy = accountCacheService.getDcOtherSettingPolicy(accRecharge.getReceiveMerhcantId());
        // 软件商充值分润比例
        BigDecimal softwareRate = StringUtils.isNotEmpty(configDTO.getWalletSoftwareRate()) ? new BigDecimal(configDTO.getWalletSoftwareRate()) : BigDecimal.ZERO;
        // 平台商充值分润比例
        BigDecimal partnerRate = Objects.nonNull(dcOtherSettingPolicy) && StringUtils.isNotEmpty(dcOtherSettingPolicy.getWalletPartnerRate()) ? new BigDecimal(dcOtherSettingPolicy.getWalletPartnerRate()) : BigDecimal.ZERO;
        if (NumberUtil.isGreater(partnerRate.add(partnerRate), NumberPool.B2B_PAY_NAX_RATE)) {
            throw exception(RECHARGE_MAX_RATE);
        }
        // 商户是否存在
        if (NumberUtil.isGreater(softwareRate, BigDecimal.ZERO)) {
            PartnerDto partnerDto = partnerApi.getBySysCode(accRecharge.getSysCode()).getCheckedData();
            // 软件商户
            AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(partnerDto.getSoftwareId(), MerchantTypeEnum.SOFTWARE.getType(), configDTO.getWalletPayPlatform());
            if (Objects.isNull(platformMerchant)) {
                throw exception(RECHARGE_MERCHANT_ERR_1);
            }
        }
        if (NumberUtil.isGreater(partnerRate, BigDecimal.ZERO)) {
            // 平台商商户
            AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(accRecharge.getSysCode(), MerchantTypeEnum.PARTNER.getType(), configDTO.getWalletPayPlatform());
            if (Objects.isNull(platformMerchant)) {
                throw exception(RECHARGE_MERCHANT_ERR_2);
            }
        }
        // 运营商商户
        AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(accRecharge.getReceiveMerhcantId(), MerchantTypeEnum.DC.getType(), configDTO.getWalletPayPlatform());
        if (Objects.isNull(platformMerchant)) {
            throw exception(RECHARGE_MERCHANT_ERR_3);
        }
    }
}
