package com.zksr.account.mapper;

import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccPay;
import com.zksr.account.controller.pay.vo.AccPayPageReqVO;


/**
 * 账户付款单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Mapper
public interface AccPayMapper extends BaseMapperX<AccPay> {
    default PageResult<AccPay> selectPage(AccPayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccPay>()
                    .eqIfPresent(AccPay::getPayId, reqVO.getPayId())
                    .eqIfPresent(AccPay::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccPay::getPayType, reqVO.getPayType())
                    .eqIfPresent(AccPay::getOrderNo, reqVO.getOrderNo())
                    .eqIfPresent(AccPay::getPayAmt, reqVO.getPayAmt())
                    .eqIfPresent(AccPay::getAfterNo, reqVO.getAfterNo())
                    .eqIfPresent(AccPay::getRefundAmt, reqVO.getRefundAmt())
                    .eqIfPresent(AccPay::getPayAccountId, reqVO.getPayAccountId())
                    .eqIfPresent(AccPay::getState, reqVO.getState())
                .orderByDesc(AccPay::getPayId));
    }

    default long selectCountByOrderNoSuccess(String orderNo) {
        return selectCount(
                Wrappers.lambdaQuery(AccPay.class)
                        .eq(AccPay::getOrderNo, orderNo)
                        .eq(AccPay::getState, StringPool.ONE)
        );
    }

    default AccPay selectByOrderNo(String orderNo) {
        return selectOne(
                Wrappers.lambdaQuery(AccPay.class)
                        .eq(AccPay::getOrderNo, orderNo)
                        .last(com.zksr.common.core.pool.StringPool.LIMIT_ONE)
        );
    }
}
