package com.zksr.account.convert.divideFlow;

import java.math.BigDecimal;

import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.controller.divide.vo.AccDivideFlowRespVO;
import com.zksr.account.controller.divide.vo.AccDivideFlowSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付) 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-09-21
*/
@Mapper
public interface AccDivideFlowConvert {

    AccDivideFlowConvert INSTANCE = Mappers.getMapper(AccDivideFlowConvert.class);

    AccDivideFlowRespVO convert(AccDivideFlow accDivideFlow);

    AccDivideFlow convert(AccDivideFlowSaveReqVO accDivideFlowSaveReq);

    PageResult<AccDivideFlowRespVO> convertPage(PageResult<AccDivideFlow> accDivideFlowPage);

    List<DivideFlowDTO> convertDTOList(List<AccDivideFlow> tryDivideFlow);

    DivideFlowDTO convertDTO(AccDivideFlow divideFlow);
}