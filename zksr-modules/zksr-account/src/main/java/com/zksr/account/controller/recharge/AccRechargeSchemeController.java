package com.zksr.account.controller.recharge;

import com.zksr.account.controller.recharge.vo.AccRechargeSchemePageReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemeRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemeSaveReqVO;
import com.zksr.account.convert.recharge.AccRechargeSchemeConvert;
import com.zksr.account.domain.AccRechargeScheme;
import com.zksr.account.service.IAccRechargeSchemeService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 储值充值套餐配置Controller
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Api(tags = "管理后台 - 储值充值套餐配置接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/rechargeScheme")
public class AccRechargeSchemeController {

    @Autowired
    private IAccRechargeSchemeService accRechargeSchemeService;

    /**
     * 新增储值充值套餐配置
     */
    @ApiOperation(value = "新增储值充值套餐配置", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "储值充值套餐配置", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccRechargeSchemeSaveReqVO createReqVO) {
        return success(accRechargeSchemeService.insertAccRechargeScheme(createReqVO));
    }

    /**
     * 修改储值充值套餐配置
     */
    @ApiOperation(value = "修改储值充值套餐配置", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "储值充值套餐配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AccRechargeSchemeSaveReqVO updateReqVO) {
        accRechargeSchemeService.updateAccRechargeScheme(updateReqVO);
        return success(true);
    }

    /**
     * 删除储值充值套餐配置
     */
    @ApiOperation(value = "删除储值充值套餐配置", httpMethod = HttpMethod.DEL, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "储值充值套餐配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{rechargeSchemeIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] rechargeSchemeIds) {
        accRechargeSchemeService.deleteAccRechargeSchemeByRechargeSchemeIds(rechargeSchemeIds);
        return success(true);
    }

    /**
     * 获取储值充值套餐配置详细信息
     */
    @ApiOperation(value = "获得储值充值套餐配置详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{rechargeSchemeId}")
    public CommonResult<AccRechargeSchemeRespVO> getInfo(@PathVariable("rechargeSchemeId") Long rechargeSchemeId) {
        AccRechargeScheme accRechargeScheme = accRechargeSchemeService.getAccRechargeScheme(rechargeSchemeId);
        return success(AccRechargeSchemeConvert.INSTANCE.convert(accRechargeScheme));
    }

    /**
     * 分页查询储值充值套餐配置
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得储值充值套餐配置分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccRechargeSchemeRespVO>> getPage(@Valid AccRechargeSchemePageReqVO pageReqVO) {
        PageResult<AccRechargeSchemeRespVO> pageResult = accRechargeSchemeService.getAccRechargeSchemePage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 停用充值配置
     */
    @ApiOperation(value = "停用充值配置", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "充值配置", businessType = BusinessType.UPDATE)
    @PutMapping("/disable")
    public CommonResult<Boolean> disable(@ApiParam(name = "rechargeSchemeId", value = "充值配置ID", required = true) @RequestParam("rechargeSchemeId") Long rechargeSchemeId) {
        accRechargeSchemeService.disable(rechargeSchemeId);
        return success(true);
    }

    /**
     * 启用充值配置
     */
    @ApiOperation(value = "启用充值配置", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "充值配置", businessType = BusinessType.UPDATE)
    @PutMapping("/enable")
    public CommonResult<Boolean> enable(@ApiParam(name = "rechargeSchemeId", value = "充值配置ID", required = true) @RequestParam("rechargeSchemeId") Long rechargeSchemeId) {
        accRechargeSchemeService.enable(rechargeSchemeId);
        return success(true);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "account:rechargeScheme:add";
        /**
         * 编辑
         */
        public static final String EDIT = "account:rechargeScheme:edit";
        /**
         * 删除
         */
        public static final String DELETE = "account:rechargeScheme:remove";
        /**
         * 列表
         */
        public static final String LIST = "account:rechargeScheme:list";
        /**
         * 查询
         */
        public static final String GET = "account:rechargeScheme:query";
        /**
         * 停用
         */
        public static final String DISABLE = "account:rechargeScheme:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "account:rechargeScheme:enable";
    }
}
