package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.constant.PayConstants;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 支付平台商户对象 acc_platform_merchant
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@TableName(value = "acc_platform_merchant")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccPlatformMerchant extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 支付平台商户id */
    @TableId(type = IdType.AUTO)
    private Long platformMerchantId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    private Long merchantId;

    /** 分账方商户编号 */
    @Excel(name = "分账方商户编号")
    private String altMchNo;

    /** 分账方名称 */
    @Excel(name = "分账方名称")
    private String altMchName;

    /** 商户KEY */
    @Excel(name = "商户KEY")
    @ApiModelProperty("商户KEY")
    private String altMchKey;

    /** 0-停用  1-启用 */
    @Excel(name = "0-停用  1-启用")
    private String mchStatus;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

    /** 银行名称 */
    @Excel(name = "银行名称")
    private String bankName;

    /** 银行支行 */
    @Excel(name = "银行支行")
    private String bankBranch;

    /** 银行卡号 */
    @Excel(name = "银行卡号")
    private String accountNo;

    /** 银行户名 */
    @Excel(name = "银行户名")
    private String accountName;

    /** 支付平台进件单号 */
    @ApiModelProperty(value = "支付平台进件单号")
    private String thirdOrderNo;

    /**
     * 子商户类型
     */
    @ApiModelProperty("子商户类型,0-个人, 1-个人工商, 2-企业")
    private String busiMerchantType;

    /** 结算卡类型 */
    @ApiModelProperty(value = "结算卡类型: TOPRIVATE-对私,TOPUBLIC-对公", notes = "TOPRIVATE-对私,TOPUBLIC-对公")
    private String bankType;

    /** 身份证 */
    @ApiModelProperty("身份证")
    private String idCard;

    /** 联行号 */
    @ApiModelProperty("联行号")
    private String bankChannelNo;

    /** 联系人名称 */
    @ApiModelProperty("联系人名称")
    private String contractName;

    /** 联系人手机号 */
    @ApiModelProperty("联系人手机号")
    private String contractPhone;

    @ApiModelProperty(value = "法人姓名")
    private String legalPerson;//法人姓名

    @ApiModelProperty(value = "营业执照编号  分账方类型为个体工商户和企业类型必填，商户类型为个人则不填")
    private String licenseNo;//营业执照编号  分账方类型为个体工商户和企业类型必填，商户类型为个人则不填

    @ApiModelProperty("认证信息")
    private String authMsg;

    /** 审核状态(字典:platform_merchant_audit_status): INIT-待审核, OVERRULE-驳回, AUDITED-审核通过 {@link  MerchantRegisterStateEnum}*/
    @ApiModelProperty("审核状态(字典:platform_merchant_audit_status): INIT-待审核, OVERRULE-驳回, AUDITED-审核通过")
    private String auditStatus;

    /** 图片审核状态: SUCCESS-成功, DOING-处理中, FAIL-失败 */
    @ApiModelProperty("图片审核状态: SUCCESS-成功, DOING-处理中, FAIL-失败")
    private String picStatus;

    /** 变更状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过 */
    @ApiModelProperty("变更状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过")
    private String editStatus;

    /** 凭证上传结果 */
    @ApiModelProperty("凭证上传结果")
    private String picMsg;

    /** 身份证正面（国徽页）图片 */
    @ApiModelProperty(value = "身份证正面")
    private String cardPositiveUrl;

    /** 身份证反面（头像页）图片 */
    @ApiModelProperty(value = "身份证反面")
    private String cardNegativeUrl;

    /** 营业执照 图片 */
    @ApiModelProperty(value = "营业执照 图片")
    private String tradeLicenceUrl;

    /** 开户许可证图片 */
    @ApiModelProperty(value = "开户许可证图片")
    private String openAccountLicenceUrl;

    /** 变更单号 */
    @ApiModelProperty(value = "变更单号")
    private String editOrderNo;

    /** 变更结果 */
    @ApiModelProperty(value = "变更结果")
    private String editMsg;

    /** 外部认证链接 */
    @ApiModelProperty(value = "外部认证链接")
    private String outLink;

    /** 身份证地址 */
    @ApiModelProperty(value = "身份证地址")
    private String idCardAddress;

    /** 企业地址 */
    @ApiModelProperty(value = "企业地址")
    private String licenceAddress;

    /**
     * 模拟进件平台商户
     * @return
     */
    public static AccPlatformMerchant getMockAccount() {
        AccPlatformMerchant platformMerchant = new AccPlatformMerchant();
        platformMerchant.setAltMchNo(PayConstants.DEFAULT_MOCK_NO);
        platformMerchant.setAltMchName(PayConstants.DEFAULT_MOCK_NAME);
        platformMerchant.setBankName("模拟银行");
        platformMerchant.setBankBranch("模拟银行长沙麓谷支行");
        platformMerchant.setAccountNo("**********");
        platformMerchant.setThirdOrderNo("**********");
        platformMerchant.setBankType("TOPRIVATE");
        return platformMerchant;
    }
}
