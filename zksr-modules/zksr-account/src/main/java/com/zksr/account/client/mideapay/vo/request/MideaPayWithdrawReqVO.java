package com.zksr.account.client.mideapay.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayWithdrawReqVO extends MideaPayBaseReqVO {


    /**
     * 交易类型,BALANCE 代付到余额 CARD 代付到银行账户，含银行卡、存折，以及对公账户
     */
    @JsonProperty("trade_type")
    private String tradeType;


    /**
     * 交易金额
     */
    @JsonProperty("pay_amount")
    private String payAmount;

    /**
     * 货币类型
     */
    @JsonProperty("currency_type")
    private String currencyType;

    /**
     * 商户自定义信息
     */
    @JsonProperty("attach")
    private String attach;

    /**
     * 通知手机号码,本笔代付交易成功时，短信通知此手机号码；AES加密
     */
    @JsonProperty("notify_mobile")
    private String notifyMobile;

    /**
     * 账户类型,PAYMENT 支付账户（默认） ESCROW 托管账户
     */
    @JsonProperty("payer_act_type")
    private String payerActType;


    //当交易类型（trade_type）为代付到余额（BALANCE），添加以下业务参数：<<//
    /**
     * 美的支付登录名
     */
    @JsonProperty("payee_login_name")
    private String payeeLoginName;

    /**
     * 美的支付用户类型,C：个人用户 B：商户,默认为B
     */
    @JsonProperty("payee_type")
    private String payeeType;

    /**
     * 收款方名称
     */
    @JsonProperty("payee_name")
    private String payeeName;

    /**
     * 银行附言
     */
    @JsonProperty("bank_attach")
    private String bankAttach;
    //>>当交易类型（trade_type）为代付到余额（BALANCE），添加以上业务参数//




    //当交易类型（trade_type）为代付到代付到银行账户（CARD），添加以下业务参数：<<//
    /**
     * 收款方银行账户,AES加密
     */
    @JsonProperty("payee_bank_acct_no")
    private String payeeBankAcctNo;

    /**
     * 收款方名称
     */
//    @JsonProperty("payee_name")
//    private String payeeName;

    /**
     * 收款方银行账户类型,账户类型分类
     *
     * BANK_CARD：银行卡
     *
     * PASS_BOOK：存折
     *
     * PUBLIC_ACCT：对公账户
     */
    @JsonProperty("payee_acct_type")
    private String payeeAcctType;

    /**
     * 收款方开户行归属省份
     */
    @JsonProperty("payee_acct_province")
    private String payeeAcctProvince;

    /**
     * 收款方开户行归属市
     */
    @JsonProperty("payee_acct_city")
    private String payeeAcctCity;

    /**
     * 收款方开户支行名称
     */
    @JsonProperty("payee_sub_bank_name")
    private String payeeSubBankName;

    /**
     * 收款方开户支行联行行号
     */
    @JsonProperty("payee_sub_bank_code")
    private String payeeSubBankCode;


    /**
     * 付款方替换户名
     */
    @JsonProperty("replace_name")
    private String replaceName;

    /**
     * 银行附言
     */
//    @JsonProperty("bank_attach")
//    private String bankAttach;
    //>>当交易类型（trade_type）为代付到代付到银行账户（CARD），添加以上业务参数//


}
