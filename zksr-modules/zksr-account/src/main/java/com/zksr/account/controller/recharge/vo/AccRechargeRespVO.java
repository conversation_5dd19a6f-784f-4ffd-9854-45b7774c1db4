package com.zksr.account.controller.recharge.vo;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import lombok.*;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 账户充值单对象 acc_recharge
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("账户充值单 - acc_recharge Response VO")
public class AccRechargeRespVO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 账户充值单id */
    @ApiModelProperty(value = "充值单号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 充值类型;0-入驻商充值 1-门店充值 2-模拟支付 */
    @Excel(name = "充值类型;0-入驻商充值 1-门店充值 2-模拟支付")
    @ApiModelProperty(value = "充值类型;0-入驻商充值 1-门店充值 2-模拟支付")
    private Integer chargeType;

    /** 支付方式 */
    @Excel(name = "支付方式")
    @ApiModelProperty(value = "支付方式")
    private String payWay;

    /** 充值金额 */
    @Excel(name = "充值金额")
    @ApiModelProperty(value = "充值金额")
    private BigDecimal rechargeAmt;

    /** 收单手续费 */
    @Excel(name = "收单手续费")
    @ApiModelProperty(value = "收单手续费")
    private BigDecimal fee;

    /** 充值方类型;supplier-入驻商  branch-门店 */
    @Excel(name = "充值方类型;supplier-入驻商  branch-门店")
    @ApiModelProperty(value = "充值方类型;supplier-入驻商  branch-门店")
    private String rechargeMerchantType;

    /** 充值方id */
    @Excel(name = "充值方id")
    @ApiModelProperty(value = "充值方id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeMerchantId;

    /** 收款方类型;supplier-入驻商 partner-平台商 */
    @Excel(name = "收款方类型;supplier-入驻商 partner-平台商")
    @ApiModelProperty(value = "收款方类型;supplier-入驻商 partner-平台商")
    private String receiveMerchantType;

    /** 收款方id;当收款方为软件商时，不需要记录此值 */
    @Excel(name = "收款方id;当收款方为软件商时，不需要记录此值")
    @ApiModelProperty(value = "收款方id;当收款方为软件商时，不需要记录此值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long receiveMerhcantId;

    /** 充值状态(数据字典);0-已提交 1-已完成入驻商充值：1.调用充值接口，生成账户充值单，初始状态为0-已提交。2.充值接口再调用在线支付接口，唤起支付。3.支付完成后，支付公司回调，充值单状态变更为已完成，同时插入一条增加储值金额的账户流水，异步增加账户金额 */
    @Excel(name = "充值状态(数据字典);0-已提交 1-已完成入驻商充值：1.调用充值接口，生成账户充值单，初始状态为0-已提交。2.充值接口再调用在线支付接口，唤起支付。3.支付完成后，支付公司回调，充值单状态变更为已完成，同时插入一条增加储值金额的账户流水，异步增加账户金额")
    @ApiModelProperty(value = "充值状态(数据字典);0-已提交 1-已完成入驻商充值：1.调用充值接口，生成账户充值单，初始状态为0-已提交。2.充值接口再调用在线支付接口，唤起支付。3.支付完成后，支付公司回调，充值单状态变更为已完成，同时插入一条增加储值金额的账户流水，异步增加账户金额")
    private Integer state;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;

    /** 充值单号 */
    @Excel(name = "充值单号")
    @ApiModelProperty(value = "充值单号")
    private String rechargeNo;

    /** 充值方案内容 */
    @Excel(name = "充值方案内容")
    @ApiModelProperty(value = "充值方案内容")
    private String schemeRuleJson;

    /** 充值赠送金额 */
    @Excel(name = "充值赠送金额")
    @ApiModelProperty(value = "充值赠送金额")
    private BigDecimal giveAmt;

    /** 充值商户名称 */
    @Excel(name = "充值商户名称")
    @ApiModelProperty(value = "充值商户名称")
    private String merchantName;

    /** 商户联系电话 */
    @Excel(name = "商户联系电话")
    @ApiModelProperty(value = "商户联系电话")
    private String merchantContactPhone;

    private List<OrderSettlementDTO> settles = ListUtil.empty();

    public List<OrderSettlementDTO> getSettles() {
        return settles;
    }

    public void setSettles(String settles) {
        if (StringUtils.isNotEmpty(settles)) {
            this.settles = JSON.parseArray(settles, OrderSettlementDTO.class);
            // 补偿平台商
            if (!this.settles.stream().anyMatch(item -> item.getMerchantType().equals(MerchantTypeEnum.PARTNER.getType()))) {
                OrderSettlementDTO settlementDTO = new OrderSettlementDTO();
                settlementDTO.setMerchantType(MerchantTypeEnum.PARTNER.getType());
                settlementDTO.setAmt(BigDecimal.ZERO);
                this.settles.add(settlementDTO);
            }
            // 补偿软件商
            if (!this.settles.stream().anyMatch(item -> item.getMerchantType().equals(MerchantTypeEnum.SOFTWARE.getType()))) {
                OrderSettlementDTO settlementDTO = new OrderSettlementDTO();
                settlementDTO.setMerchantType(MerchantTypeEnum.SOFTWARE.getType());
                settlementDTO.setAmt(BigDecimal.ZERO);
                this.settles.add(settlementDTO);
            }
            // 实现排序PARTNER -> SOFTWARE -> DC
            // 实现排序 PARTNER -> SOFTWARE -> DC
            Comparator<OrderSettlementDTO> comparator = Comparator.comparingInt(item -> {
                switch (MerchantTypeEnum.fromValue(item.getMerchantType())) {
                    case PARTNER:
                        return 1;
                    case SOFTWARE:
                        return 2;
                    case DC:
                        return 3;
                    default:
                        return Integer.MAX_VALUE; // 其他类型的MerchantType排在最后
                }
            });
            Collections.sort(this.settles, comparator);
        }
    }

    public void setMerchant(BranchDTO branchDTO) {
        if (Objects.isNull(branchDTO)) {
            return;
        }
        this.merchantName = branchDTO.getBranchName();
        this.merchantContactPhone = branchDTO.getContactPhone();
    }

    public void setMerchant(SupplierDTO supplierDTO) {
        if (Objects.isNull(supplierDTO)) {
            return;
        }
        this.merchantName = supplierDTO.getSupplierName();
        this.merchantContactPhone = supplierDTO.getContactPhone();
    }
}
