package com.zksr.account.service.impl;

import com.zksr.account.controller.recharge.vo.AccRechargeImportDtlRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeImportDtlSaveReqVO;
import com.zksr.account.convert.recharge.AccRechargeImportDtlConvert;
import com.zksr.account.domain.AccRechargeImport;
import com.zksr.account.domain.AccRechargeImportDtl;
import com.zksr.account.mapper.AccRechargeImportDtlMapper;
import com.zksr.account.service.IAccRechargeImportDtlService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * PC后台导入充值详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Service
public class AccRechargeImportDtlServiceImpl implements IAccRechargeImportDtlService {

    @Autowired
    private AccRechargeImportDtlMapper accRechargeImportDtlMapper;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Override
    public void saveDtl(AccRechargeImport accRechargeImport, List<AccRechargeImportDtlSaveReqVO> rechargeImportDtls) {
        // 这个记录在没审核之前可以直接物理删除
        accRechargeImportDtlMapper.deleteByRechargeImportId(accRechargeImport.getRechargeImportId());
        List<AccRechargeImportDtl> list = AccRechargeImportDtlConvert.INSTANCE.convert(rechargeImportDtls);
        list.forEach(item -> {
            item.setRechargeImportId(accRechargeImport.getRechargeImportId());
            // 验证门店是否有效存在
            BranchDTO branchDTO = accountCacheService.getBranchDTO(item.getBranchId());
            if (Objects.isNull(branchDTO)) {
                throw exception(RECHARGE_IMPORT_BRANCH_ERR, item.getBranchId());
            }
            if (Objects.isNull(branchDTO.getAreaId())) {
                throw exception(RECHARGE_IMPORT_BRANCH_AREA_ERR, branchDTO.getBranchName());
            }
            AreaDTO areaDTO = accountCacheService.getAreaDTO(branchDTO.getAreaId());
            if (Objects.isNull(areaDTO) || Objects.isNull(areaDTO.getDcId())) {
                throw exception(RECHARGE_IMPORT_AREA_DC_ERR, branchDTO.getBranchName());
            }
        });
        accRechargeImportDtlMapper.insertBatch(list);
    }

    @Override
    public List<AccRechargeImportDtl> getDtls(Long rechargeImportId) {
        return accRechargeImportDtlMapper.selectByRechargeImportId(rechargeImportId);
    }

    @Override
    public List<AccRechargeImportDtlRespVO> getDtlsRespVO(Long rechargeImportId) {
        return accRechargeImportDtlMapper.selectByRechargeImportIdRespVO(rechargeImportId);
    }

    @Override
    public void updateById(AccRechargeImportDtl importDtl) {
        accRechargeImportDtlMapper.updateById(importDtl);
    }
}
