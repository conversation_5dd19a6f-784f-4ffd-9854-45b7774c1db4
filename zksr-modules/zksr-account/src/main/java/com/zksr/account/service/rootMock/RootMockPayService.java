package com.zksr.account.service.rootMock;


import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.web.pojo.CommonResult;

/**
 * <AUTHOR>
 * @time 2024/3/7
 * @desc 公共支付, 退款, 处理
 */
public interface RootMockPayService {
	
	CommonResult<PayRefundRespDTO> mockRefund(PayRefundOrderSubmitReqVO reqVO);
	
}
