package com.zksr.account.client.hlb.vo;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableSet;
import com.zksr.common.core.enums.SignatureType;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 账户转账实体
 * @date 2024/3/11 15:41
 */
@Data
public class AccountPaySubReqVO {
    private String P1_bizType;
    private String P2_signType;
    private String P3_timestamp;
    private String P4_orderId;
    private String P5_customerNumber;
    private String P6_ext;
    private String sign;

    /**
     * 签名类型(不参与签名)
     */
    private SignatureType signatureType = SignatureType.SM3WITHSM2;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of(
            "P1_bizType",
            "P2_signType",
            "P3_timestamp",
            "P4_orderId",
            "P5_customerNumber",
            "P6_ext"
    );

    @Data
    public static class AccountPaySubReqVoExt {
        private String inMerchantNo;
        private String orderType;
        private Double amount;
        private String serverCallbackUrl;
        private String goodsName;
        private String orderDesc;
        private String productType;
        private String associatedOrderNo;
        private String inEscrow;
        private String splitBillRules;
        private String belongsType;
    }
}
