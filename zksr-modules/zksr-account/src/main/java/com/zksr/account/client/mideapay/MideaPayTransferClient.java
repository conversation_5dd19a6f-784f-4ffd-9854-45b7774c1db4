package com.zksr.account.client.mideapay;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.account.client.TransferClient;
import com.zksr.account.client.mideapay.vo.response.MideaPayTransferNotifyRespVO;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.common.core.enums.MideaPayCompleteStatusEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.TransferStatusEnum;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.system.api.model.dto.MideaPayConfig;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @Desciption: 美的付账户交易
 *
 */
public class MideaPayTransferClient implements TransferClient {

    private MideaPaySdkClient client;

    @Override
    public String getPlatform() {return PayChannelEnum.MIDEA_PAY.getCode();}

    @Override
    public void setConfig(String config) throws Exception {
        this.client = new MideaPaySdkClient();
        /**
         * 初始化支付工具
         */
        MideaPayConfig payConfig = JSON.parseObject(config, MideaPayConfig.class);
        this.client.setPayConfig(payConfig);
    }

    @Override
    public TransferSubmitRespDTO transferSubmit(TransferSubmitReqVO reqVO) {
        return client.mideaTransfer(reqVO);
    }

    @Override
    public TransferSubmitRespDTO parseSubmitNotify(Map<String, String> params, String body) {
        //验签
        MideaPaySdkClient.checkSign(params,client.getPayConfig().getCheckSignUrl());
        //转账回调
        TransferSubmitRespDTO resp = new TransferSubmitRespDTO();
        MideaPayTransferNotifyRespVO respVO = JsonUtils.toJavaClass(params, MideaPayTransferNotifyRespVO.class);

        resp.setTransferNo(respVO.getOutTradeNo());
        resp.setOutTradeNo(respVO.getTradeNo());
        resp.setStatus(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(respVO.getTradeStatus()) ? TransferStatusEnum.SUCCESS.getStatus() : TransferStatusEnum.FAIL.getStatus());
        resp.setMsg(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(respVO.getTradeStatus()) ? respVO.getTradeStatusInfo() : String.format("%s,%s",respVO.getErrorCode(),respVO.getErrorInfo()));

        return resp;
    }

    @Override
    public TransferSettleRespDTO transferSettle(TransferSettleReqVO reqVO) {
        return client.mideaWithdraw(reqVO);
    }

    @Override
    public TransferSettleRespDTO parseSettleNotify(Map<String, String> params, String body) {
        //验签
        MideaPaySdkClient.checkSign(params,client.getPayConfig().getCheckSignUrl());

        TransferSettleRespDTO resp = new TransferSettleRespDTO();
        MideaPayTransferNotifyRespVO respVO = JsonUtils.toJavaClass(params, MideaPayTransferNotifyRespVO.class);
        resp.setWithdrawNo(respVO.getOutTradeNo());
        resp.setOutTradeNo(respVO.getTradeNo());
        resp.setStatus(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(respVO.getTradeStatus()) ? TransferStatusEnum.SUCCESS.getStatus() : TransferStatusEnum.FAIL.getStatus());
        resp.setMsg(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(respVO.getTradeStatus()) ? respVO.getTradeStatusInfo() : String.format("%s,%s",respVO.getErrorCode(),respVO.getErrorInfo()));

        return resp;
    }

}
