package com.zksr.account.controller.recharge;

import com.zksr.account.controller.recharge.vo.AccRechargePageReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSaveReqVO;
import com.zksr.account.convert.recharge.AccRechargeConvert;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveReqVO;
import com.zksr.account.service.IAccRechargeService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 账户充值单Controller
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Api(tags = "管理后台 - 账户充值单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/recharge")
public class AccRechargeController {

    @Autowired
    private IAccRechargeService accRechargeService;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 新增入驻商充值单
     */
    @ApiOperation(value = "新增入驻商账户充值单", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD_SUPPLIER)
    @RequiresPermissions(Permissions.ADD_SUPPLIER)
    @Log(title = "入驻商充值单", businessType = BusinessType.INSERT)
    @PostMapping("/supplierRecharge")
    public CommonResult<String> supplierRecharge(@Valid @RequestBody AccRechargeSaveReqVO createReqVO) {
        return success(accRechargeService.insertSupplierAccRecharge(createReqVO));
    }

    /**
     * 获取账户充值单详细信息
     */
    @ApiOperation(value = "获得账户充值单详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{rechargeId}")
    public CommonResult<AccRechargeRespVO> getInfo(@PathVariable("rechargeId") Long rechargeId) {
        AccRecharge accRecharge = accRechargeService.getAccRecharge(rechargeId);
        return success(HutoolBeanUtils.toBean(accRecharge, AccRechargeRespVO.class));
    }

    /**
     * 分页查询账户充值单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得账户充值单分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccRechargeRespVO>> getPage(@Valid AccRechargePageReqVO pageReqVO) {
        PageResult<AccRechargeRespVO> pageResult = accRechargeService.getAccRechargeExtPage(pageReqVO);
        for (AccRechargeRespVO recharge : pageResult.getList()) {
            MerchantTypeEnum merchantType = MerchantTypeEnum.fromValue(recharge.getRechargeMerchantType());
            // 入驻商
            if (merchantType == MerchantTypeEnum.SUPPLIER) {
                recharge.setMerchant(accountCacheService.getSupplierDTO(recharge.getRechargeMerchantId()));
            }
            // 入驻商
            if (merchantType == MerchantTypeEnum.BRANCH) {
                recharge.setMerchant(accountCacheService.getBranchDTO(recharge.getRechargeMerchantId()));
            }
        }
        return success(pageResult);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 新增入驻商充值单 */
        public static final String ADD_SUPPLIER = "account:recharge:addBySupplier";
        /** 编辑 */
        public static final String EDIT = "account:recharge:edit";
        /** 删除 */
        public static final String DELETE = "account:recharge:remove";
        /** 列表 */
        public static final String LIST = "account:recharge:list";
        /** 查询 */
        public static final String GET = "account:recharge:query";
        /** 停用 */
        public static final String DISABLE = "account:recharge:disable";
        /** 启用 */
        public static final String ENABLE = "account:recharge:enable";
    }
}
