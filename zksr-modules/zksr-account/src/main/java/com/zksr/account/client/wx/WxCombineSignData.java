package com.zksr.account.client.wx;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: chenyj8
 * @Desciption: 微信合单支付json格式构造
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxCombineSignData {
    @JSONField(ordinal = 1)
    private int env;

//    @JsonProperty("need_profit_sharing")
//    private int needProfitSharing;
//    private String mode;
//    private List<PaySig> paySig;
//    private String signature;

    @JSONField(ordinal = 2)
    @JsonProperty("combined_order_list")
    private List<CombinedOrder> combinedOrderList;

//    public WxCombineSignData(int env, List<CombinedOrder> combinedOrderList) {
//        this.env = env;
//        this.combinedOrderList = combinedOrderList;
//    }
//
//    public int getEnv() {
//        return env;
//    }
//
//    public void setEnv(int env) {
//        this.env = env;
//    }
//
//    @JsonProperty("combined_order_list")
//    public List<CombinedOrder> getCombinedOrderList() {
//        return combinedOrderList;
//    }
//
//    public void setCombinedOrderList(List<CombinedOrder> combinedOrderList) {
//        this.combinedOrderList = combinedOrderList;
//    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class PaySig {
    private String mchid;

    private String paysig;

    private String altMchKey;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class CombinedOrder {
    @JSONField(ordinal = 1)
    private Amount amount;
    @JSONField(ordinal = 2)
    private String mchid;
    @JSONField(ordinal = 3)
    @JsonProperty("out_trade_no")
    private String outTradeNo;
    @JSONField(ordinal = 4)
    @JsonProperty("need_profit_sharing")
    private int needProfitSharing;
    @JSONField(ordinal = 5)
    private String description;

    @JSONField(ordinal = 6)
    private String attach;

//    public CombinedOrder(String mchid, String outTradeNo, String description, Amount amount, String attach) {
//        this.mchid = mchid;
//        this.outTradeNo = outTradeNo;
//        this.description = description;
//        this.amount = amount;
//        this.attach = attach;
//    }
//
//    public String getMchid() {
//        return mchid;
//    }
//
//    public void setMchid(String mchid) {
//        this.mchid = mchid;
//    }
//
//    @JsonProperty("out_trade_no")
//    public String getOutTradeNo() {
//        return outTradeNo;
//    }
//
//    public void setOutTradeNo(String outTradeNo) {
//        this.outTradeNo = outTradeNo;
//    }
//
//    public String getDescription() {
//        return description;
//    }
//
//    public void setDescription(String description) {
//        this.description = description;
//    }
//
//    public Amount getAmount() {
//        return amount;
//    }
//
//    public void setAmount(Amount amount) {
//        this.amount = amount;
//    }
//
//    public String getAttach() {
//        return attach;
//    }
//
//    public void setAttach(String attach) {
//        this.attach = attach;
//    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class Amount {
    @JsonProperty("order_amount")
    private BigDecimal orderAmount;
    private String currency;

//    public Amount(int orderAmount, String currency) {
//        this.orderAmount = orderAmount;
//        this.currency = currency;
//    }
//
//    @JsonProperty("order_amount")
//    public int getOrderAmount() {
//        return orderAmount;
//    }
//
//    public void setOrderAmount(int orderAmount) {
//        this.orderAmount = orderAmount;
//    }
//
//    public String getCurrency() {
//        return currency;
//    }
//
//    public void setCurrency(String currency) {
//        this.currency = currency;
//    }
}
