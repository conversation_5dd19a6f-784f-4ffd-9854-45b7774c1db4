package com.zksr.account.client.hlb.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分账数据结果
 * @date 2024/8/16 16:59
 */
@Data
public class RefundRuleJsonRespVO {

    @ApiModelProperty("分账金额")
    private BigDecimal refundAmount;

    @ApiModelProperty("邮箱")
    private String splitBillMerchantEmail;

    @ApiModelProperty("分账商户号")
    private String merchantNo;

    @ApiModelProperty("分账编号")
    private String splitBillOrderNum;

    @ApiModelProperty("分账状态, SUCCESS-成功")
    private String refundStatus;
}
