package com.zksr.account.controller.transfer.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 交易对账单明细单对象 acc_transfer_bill_order
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@ApiModel("交易对账单明细单 - acc_transfer_bill_order分页 Request VO")
public class AccTransferBillOrderSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 账单ID */
    @Excel(name = "账单ID")
    @ApiModelProperty(value = "账单ID")
    private Long billOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 交易账单id */
    @Excel(name = "交易账单id")
    @ApiModelProperty(value = "交易账单id")
    private Long transferBillId;

    /** 交易时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "交易时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "交易时间")
    private Date transferTime;

    /** 平台支付金额 */
    @Excel(name = "平台支付金额")
    @ApiModelProperty(value = "平台支付金额")
    private BigDecimal platformPayAmt;

    /** 商户支付金额 */
    @Excel(name = "商户支付金额")
    @ApiModelProperty(value = "商户支付金额")
    private BigDecimal merchantPayAmt;

    /** 平台退款金额 */
    @Excel(name = "平台退款金额")
    @ApiModelProperty(value = "平台退款金额")
    private BigDecimal platformRefundAmt;

    /** 商户退款金额 */
    @Excel(name = "商户退款金额")
    @ApiModelProperty(value = "商户退款金额")
    private BigDecimal merchantRefundAmt;

    /** 平台支付手续费 */
    @Excel(name = "平台支付手续费")
    @ApiModelProperty(value = "平台支付手续费")
    private BigDecimal platformPayFree;

    /** 商户支付手续费 */
    @Excel(name = "商户支付手续费")
    @ApiModelProperty(value = "商户支付手续费")
    private BigDecimal merchantPayFree;

    /** 平台退款手续费 */
    @Excel(name = "平台退款手续费")
    @ApiModelProperty(value = "平台退款手续费")
    private BigDecimal platformRefundFree;

    /** 商户退款手续费 */
    @Excel(name = "商户退款手续费")
    @ApiModelProperty(value = "商户退款手续费")
    private BigDecimal merchantRefundFree;

    /** 平台交易单号(外部) */
    @Excel(name = "平台交易单号(外部)")
    @ApiModelProperty(value = "平台交易单号(外部)")
    private String platformTradeNo;

    /** 商户交易单号(内部) */
    @Excel(name = "商户交易单号(内部)")
    @ApiModelProperty(value = "商户交易单号(内部)")
    private String merchantTradeNo;

    /** 商户号 */
    @Excel(name = "商户号")
    @ApiModelProperty(value = "商户号")
    private String altNo;

    /** 业务单号(订单号, 退款单号...) */
    @Excel(name = "业务单号(订单号, 退款单号...)")
    @ApiModelProperty(value = "业务单号(订单号, 退款单号...)")
    private String busiTradeNo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer state;

    /** 支付平台, hlb-合利宝,wxb2b-微信b2b */
    @ApiModelProperty(value = "状态")
    private String platform;

    /** 订单类型 */
    @Excel(name = "订单类型")
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

}
