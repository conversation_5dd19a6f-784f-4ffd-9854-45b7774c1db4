package com.zksr.account.service.impl;

import com.alicp.jetcache.Cache;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.Colonel<PERSON>pi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.*;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.DcOtherSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.PartnerMiniSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.WithdrawalSettingPolicyDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 账户模块缓存
 * @date 2024/3/30 8:52
 */
@Service
public class IAccountCacheServiceImpl implements IAccountCacheService {

    @Autowired
    private Cache<Long, AppletBaseConfigDTO> appletBaseConfigDTOCache;

    @Autowired
    private Cache<Long, PayConfigDTO> payConfigDTOCache;

    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;

    @Autowired
    private Cache<Long, PayAccountConfigDTO> payAccountConfigDTOCache;

    @Autowired
    private Cache<Long, DcDTO> dcDTOCache;

    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;

    @Autowired
    private Cache<Long, WithdrawalSettingPolicyDTO> withdrawalSettingPolicyCache;

    @Autowired
    private Cache<Long, HeLiBaoPayConfigDTO> heLiBaoPayConfigCache;

    @Autowired
    private Cache<Long, MideaPayConfigDTO> mideaPayConfigCache;

    @Autowired
    private Cache<Long, WxB2bPayConfigDTO> wxB2bPayConfigDTOCache;

    @Autowired
    private Cache<Long, AreaDTO> areaDtoCache;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private Cache<Long, DcOtherSettingPolicyDTO> dcOtherSettingPolicyDTOCache;

    @Resource
    private PartnerConfigApi partnerConfigApi;

    @Resource
    private SupplierApi supplierApi;

    @Resource
    private BranchApi branchApi;

    @Resource
    private DcApi dcApi;

    @Resource
    private ColonelApi colonelApi;

    @Resource
    private PartnerPolicyApi partnerPolicyApi;

    @Resource
    private AreaApi areaApi;

    @Autowired
    private Cache<Long, PartnerMiniSettingPolicyDTO> partnerMiniSettingPolicyDTOCache;

    @PostConstruct
    public void init() {
        //自动load（read through）
        appletBaseConfigDTOCache.config().setLoader(this::loadAppletBaseConfigDtoFromApi);
        payConfigDTOCache.config().setLoader(this::loadPayConfigDTO);
        supplierDTOCache.config().setLoader(this::loadSupplierDTO);
        payAccountConfigDTOCache.config().setLoader(this::loadPayAccountConfigDTO);
        dcDTOCache.config().setLoader(this::loadDcDTO);
        colonelDTOCache.config().setLoader(this::loadColonelDTO);
        withdrawalSettingPolicyCache.config().setLoader(this::loadWithdrawalSettingPolicyCache);
        heLiBaoPayConfigCache.config().setLoader(this::loadHelibaoConfigCache);
        wxB2bPayConfigDTOCache.config().setLoader(this::loadWxB2bPayConfigCache);
        mideaPayConfigCache.config().setLoader(this::mideaPayConfigCache);
        areaDtoCache.config().setLoader(this::loadAreaDtoFromApi);
        branchDTOCache.config().setLoader(this::loadBranchDTOFromApi);
        dcOtherSettingPolicyDTOCache.config().setLoader(this::loadDcOtherSettingDTOFromApi);
        partnerMiniSettingPolicyDTOCache.config().setLoader(this::loadPartnerMiniSettingPolicyDTOFromApi);
    }

    private PartnerMiniSettingPolicyDTO loadPartnerMiniSettingPolicyDTOFromApi(Long sysCode) {
        return partnerPolicyApi.getPartnerMiniSettingPolicy(sysCode).getCheckedData();
    }

    private DcOtherSettingPolicyDTO loadDcOtherSettingDTOFromApi(Long dcId) {
        return partnerPolicyApi.getDcOtherSettingDTO(dcId).getCheckedData();
    }

    private BranchDTO loadBranchDTOFromApi(Long branchId) {
        return branchApi.getByBranchId(branchId).getCheckedData();
    }

    private AreaDTO loadAreaDtoFromApi(Long areaId) {
        return areaApi.getAreaByAreaId(areaId).getCheckedData();
    }

    private WxB2bPayConfigDTO loadWxB2bPayConfigCache(Long sysCode) {
        return partnerConfigApi.getWxB2bPayConfig(sysCode).getCheckedData();
    }

    private HeLiBaoPayConfigDTO loadHelibaoConfigCache(Long sysCode) {
        return partnerConfigApi.getHeLiBaoConfig(sysCode).getCheckedData();
    }

    private MideaPayConfigDTO mideaPayConfigCache(Long sysCode) {
        return partnerConfigApi.getMideaPayConfig(sysCode).getCheckedData();
    }

    private WithdrawalSettingPolicyDTO loadWithdrawalSettingPolicyCache(Long sysCode) {
        return partnerPolicyApi.getWithdrawalSettingPolicy(sysCode).getCheckedData();
    }

    private ColonelDTO loadColonelDTO(Long colonelId){
        return colonelApi.getByColonelId(colonelId).getCheckedData();
    }

    private DcDTO loadDcDTO(Long dcId){
        return dcApi.getDcById(dcId).getCheckedData();
    }

    private AppletBaseConfigDTO loadAppletBaseConfigDtoFromApi(Long sysCode){
        return partnerConfigApi.getAppletBaseConfig(sysCode).getCheckedData();
    }


    private PayConfigDTO loadPayConfigDTO(Long sysCode){
        return partnerConfigApi.getPayConfig(sysCode).getCheckedData();
    }

    private PayAccountConfigDTO loadPayAccountConfigDTO(Long sysCode){
        return partnerConfigApi.getPayAccountConfig(sysCode).getCheckedData();
    }

    private SupplierDTO loadSupplierDTO(Long supplierId){
        return supplierApi.getBySupplierId(supplierId).getCheckedData();
    }


    /**
     * @param sysCode   平台ID
     * @return  平台用户小程序配置
     */
    @Override
    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode) {
        return appletBaseConfigDTOCache.get(sysCode);
    }

    @Override
    public PayConfigDTO getPayConfigDTO(Long sysCode) {
        return payConfigDTOCache.get(sysCode);
    }

    @Override
    public PayAccountConfigDTO getPayAccountConfigDTO(Long sysCode) {
        return payAccountConfigDTOCache.get(sysCode);
    }

    @Override
    public SupplierDTO getSupplierDTO(Long supplierId) {
        return supplierDTOCache.get(supplierId);
    }

    @Override
    public DcDTO getDcDTO(Long dcId) {
        return dcDTOCache.get(dcId);
    }

    @Override
    public ColonelDTO getColonelDTO(Long colonelId) {
        return colonelDTOCache.get(colonelId);
    }

    @Override
    public WithdrawalSettingPolicyDTO getWithdrawalSetting(Long sysCode) {
        return withdrawalSettingPolicyCache.get(sysCode);
    }

    @Override
    public HeLiBaoPayConfigDTO getHeLiBaoConfig(Long sysCode) {
        return heLiBaoPayConfigCache.get(sysCode);
    }

    @Override
    public MideaPayConfigDTO getMideaPayConfig(Long sysCode) {
        /**
         * {@link IAccountCacheServiceImpl#mideaPayConfigCache}
         * {@link com.zksr.system.api.partnerConfig.partnerConfigImpl#getMideaPayConfig}
         */
        return mideaPayConfigCache.get(sysCode);
    }

    @Override
    public WxB2bPayConfigDTO getWxB2BPayConfigDTO(Long sysCode) {
        return wxB2bPayConfigDTOCache.get(sysCode);
    }

    @Override
    public AreaDTO getAreaDTO(Long areaId) {
        if (Objects.isNull(areaId)) {
            return new AreaDTO();
        }
        return areaDtoCache.get(areaId);
    }

    @Override
    public BranchDTO getBranchDTO(Long branchId) {
        if (Objects.isNull(branchId)) {
            return null;
        }
        return branchDTOCache.get(branchId);
    }

    @Override
    public DcOtherSettingPolicyDTO getDcOtherSettingPolicy(Long dcId) {
        if (Objects.isNull(dcId)) {
            return null;
        }
        return dcOtherSettingPolicyDTOCache.get(dcId);
    }

    @Override
    public PartnerMiniSettingPolicyDTO getPartnerMiniSettingPolicy(Long sysCode) {
        return partnerMiniSettingPolicyDTOCache.get(sysCode);
    }
}
