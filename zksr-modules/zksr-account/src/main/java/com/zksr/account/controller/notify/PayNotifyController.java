package com.zksr.account.controller.notify;

import com.zksr.account.client.PayClient;
import com.zksr.common.core.domain.PayCallBack;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.service.pay.PayChannelService;
import com.zksr.account.service.pay.PayOrderService;
import com.zksr.account.util.MideaPayUtil;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayOrderStatusRespEnum;
import com.zksr.common.core.enums.PayWayEnum;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Map;

import static com.zksr.account.enums.ErrorCodeConstants.PAY_CLIENT_NOT_EXIST;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0 交易服务 - 统一回调
 * @description:
 * @date 2024/3/10 8:12
 */
@Api(tags = "交易服务 - 统一回调", produces = "application/json")
@RestController
@ApiIgnore
@RequestMapping("/pay/notify")
@Validated
@Slf4j
public class PayNotifyController {

    @Autowired
    private PayChannelService payChannelService;

    @Autowired
    private PayOrderService payOrderService;

    @Autowired
    private AccountMqProducer accountMqProducer;

    @ApiOperation(value = "美的付支付渠道的统一【支付】回调")
    @RequestMapping(value = "/order/mideaPay")
    //!@回调支付 - 1、总入口（美的付）
    public String mideaPayNotifyOrder(@RequestParam(required = false) Map<String, String> params, @RequestBody(required = false) String body)
    {
        PayCallBack callBack = MideaPayUtil.getMideaCallBack(params, body);
        return notifyOrder(callBack.getSysCode(),callBack.getOrderType(),callBack.getAppid(),params,body);
    }
    
    @ApiOperation(value = "支付渠道的统一【支付】回调")
    @RequestMapping(value = "/order/{sysCode}/{orderType}/{appId}")
    //!@回调支付 - 1、总入口
    public String notifyOrder(
            @ApiParam(name = "sysCode", value = "平台商ID") @PathVariable("sysCode") Long sysCode,
            @ApiParam(name = "orderType", value = "订单类型") @PathVariable("orderType") String orderType,
            @ApiParam(name = "appId", value = "appid") @PathVariable("appId") String appId,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body
    ) {
        log.info("[notifyOrder][sysCode({}),orderType({}) 回调数据({}/{})]", sysCode, orderType, params, body);
        // 1. 校验支付渠道是否存在
        // PayWayEnum.OFFLINE , 在线支付才有回调, 储值支付 和 模拟支付直接回调
        //!@回调支付 - 2、获取支付客户端
        PayClient payClient = payChannelService.getPayClient(appId, sysCode, PayWayEnum.ONLINE.getPayWay(), Integer.parseInt(orderType));
        if (payClient == null) {
            log.info("[notifyOrder][sysCode({}),orderType({}) 找不到对应的支付客户端]", sysCode, orderType);
            throw exception(PAY_CLIENT_NOT_EXIST);
        }
        // 2. 解析通知数据
        //!@回调支付 - 3、解析通知数据
        PayOrderRespDTO notify = payClient.parseOrderNotify(params, body);
        // 回显订单类型
        notify.setOrderType(Integer.parseInt(orderType));
        // 非支付成功状态, 不处理, 不关心
        if (PayOrderStatusRespEnum.isSuccess(notify.getStatus())) {
            //美的付使用合单支付，回调需要自动拆分成多个notify
            if (PayChannelEnum.MIDEA_PAY.getCode().equals(notify.getPayPlatform())){
                notify.getSubPayOrderRespDtos().stream().forEach( a -> {
                    a.setOrderType(Integer.parseInt(orderType));
                    accountMqProducer.sendPayNotify(a);
                });
            }else{
                accountMqProducer.sendPayNotify(notify);
            }
        }
        return "success";
    }
    
    
    @ApiOperation(value = "美的付支付渠道的统一【退款】回调")
    @RequestMapping(value = "/refund/mideaPay")
    //!@回调退款 - 1、总入口（美的付）
    public String mideaNotifyRefund(@RequestParam(required = false) Map<String, String> params, @RequestBody(required = false) String body)
    {
        PayCallBack callBack = MideaPayUtil.getMideaCallBack(params, body);
        return notifyRefund(callBack.getSysCode(),callBack.getOrderType(),callBack.getAppid(),callBack.getPlatform(),params,body);
    }
    
    @ApiOperation(value = "支付渠道的统一【退款】回调")
    @RequestMapping(value = "/refund/{sysCode}/{orderType}/{appId}/{platform}")
    //!@回调退款 - 1、总入口
    public String notifyRefund(
            @ApiParam(name = "sysCode", value = "平台商ID") @PathVariable("sysCode") Long sysCode,
            @ApiParam(name = "orderType", value = "订单类型") @PathVariable("orderType") String orderType,
            @ApiParam(name = "appId", value = "appid") @PathVariable("appId") String appId,
            @ApiParam(name = "platform", value = "支付平台") @PathVariable("platform") String platform,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body
    ) {
        log.info("[notifyRefund][sysCode({}),orderType({}), appId({}) platform({}) 退款回调]", sysCode, orderType, appId, platform);
        log.info("formData={}", params);
        log.info("body={}", body);
        // 1. 校验支付渠道是否存在
        // PayWayEnum.OFFLINE , 在线支付才有回调, 储值支付 和 模拟支付直接回调
        PayClient payClient = payChannelService.getPayClient(appId, sysCode, PayWayEnum.ONLINE.getPayWay(), Integer.parseInt(orderType), platform);
        if (payClient == null) {
            log.info("[notifyRefund][sysCode({}),orderType({}), appId({}) platform({}) 找不到对应的支付客户端]", sysCode, orderType, appId, platform);
            throw exception(PAY_CLIENT_NOT_EXIST);
        }
        // 2. 解析通知数据
        PayRefundRespDTO notify = payClient.parseRefundNotify(params, body);
        notify.setOrderType(Integer.parseInt(orderType));
        accountMqProducer.sendRefundNotify(notify);
        return "success";
    }

}
