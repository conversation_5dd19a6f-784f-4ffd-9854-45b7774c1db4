package com.zksr.account.client.mideapay;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.client.PayClient;
import com.zksr.account.client.mideapay.vo.response.MideaPayProfitOrderNotifyRespVO;
import com.zksr.account.client.mideapay.vo.response.MideaPayRefundNotifyRespVO;
import com.zksr.account.client.mideapay.vo.response.MideaPayWechatNotifyRespVO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.DivideReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.model.profitOrder.dto.DivideOrderRepDto;
import com.zksr.account.util.MideaPayUtil;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.system.api.model.dto.MideaPayConfig;
import com.zksr.system.api.model.dto.PayConfigDTO;

import cn.hutool.core.bean.BeanUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:  美的付小程序支付
 */
@Data
@Slf4j
public class MideaPayAppletPayClient implements PayClient {

    // 对接美的付
    private MideaPaySdkClient client;

    // 支付配置
    private PayConfigDTO config;

    @Override
    public String getChannel() {return PayChannelEnum.MIDEA_PAY_WX_LITE.getCode();}

    @Override
    public String getPlatform() {return PayChannelEnum.MIDEA_PAY.getCode();}

    @Override
    public void setConfig(PayConfigDTO config, String appid) throws Exception {
        this.config = config;
        this.client = new MideaPaySdkClient();
        /**
         * 初始化支付工具
         */
        MideaPayConfig payConfig = JSON.parseObject(config.getConfig(), MideaPayConfig.class);
        this.client.setPayConfig(payConfig);
        this.client.setAppid(appid);
    }

    @Override
    public PayOrderRespDTO unifiedOrder(PayOrderDTO payOrderDTO) {
        return this.client.mideaPay(payOrderDTO);
    }

    @Override
    public PayOrderRespDTO parseOrderNotify(Map<String, String> params, String body) {
        //验签
        MideaPaySdkClient.checkSign(params,client.getPayConfig().getCheckSignUrl());

        PayOrderRespDTO orderRespDTO = new PayOrderRespDTO();
        MideaPayWechatNotifyRespVO respVO = JsonUtils.toJavaClass(params, MideaPayWechatNotifyRespVO.class);
        orderRespDTO.setCallBack(MideaPayUtil.getMideaCallBack(params, body)); ;
        
        //2025年7月3日10:11:33 改造美的付的合单支付 - 支持子取订单里面的数据
        if(MideaPayCompleteStatusEnum.COMPLETE.getCode().equals(respVO.getCompleteStatus())){
            List<MideaPayWechatNotifyRespVO.SubOrder> subOrders = respVO.getSubOrders();
            for (MideaPayWechatNotifyRespVO.SubOrder subOrder : subOrders) {
                PayOrderRespDTO.SubPayOrderRespDTO  subPay = new PayOrderRespDTO.SubPayOrderRespDTO();
                if(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(subOrder.getTradeStatus())){
                    orderRespDTO.setFlowId(Long.valueOf(subOrder.getSubOutTradeNo()))
                            .setOutTradeNo(subOrder.getSubTradeNo())
                            .setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                            .setOrderNo(subOrder.getSubAttach())
                            .setPayPlatform(getPlatform());
                    BeanUtil.copyProperties(orderRespDTO, subPay);
                    orderRespDTO.getSubPayOrderRespDtos().add(subPay);
                }
            }
        }
        
        
        if(MideaPayCompleteStatusEnum.COMPLETE.getCode().equals(respVO.getCompleteStatus())){
            orderRespDTO.setFlowId(Long.valueOf(respVO.getOutTradeNo()))
                    .setOutTradeNo(respVO.getTradeNo())
                    .setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                    .setOrderNo(respVO.getAttach())
                    .setPayPlatform(getPlatform());
        }
        return orderRespDTO;

    }


    @Override
    public PayRefundRespDTO unifiedRefund(PayRefundOrderSubmitReqVO reqDTO) {
        return this.client.mideaRefund(reqDTO);
    }

    @Override
    public PayRefundRespDTO parseRefundNotify(Map<String, String> params, String body) {
        PayRefundRespDTO refundResp = new PayRefundRespDTO();

        MideaPayRefundNotifyRespVO respVO = JsonUtils.toJavaClass(params, MideaPayRefundNotifyRespVO.class);
        if(MideaPayRefundStatusEnum.SUCCESS.getCode().equals(respVO.getRefundStatus())){
            // 退款成功
            refundResp.setOrderNo(respVO.getOutTradeNo());
            refundResp.setRefundNo(respVO.getOutRefundNo());
            refundResp.setOutRefundNo(respVO.getRefundNo());
            refundResp.setStatus(PayRefundStatusEnum.SUCCESS.getStatus());
        } else if (MideaPayRefundStatusEnum.FAIL.getCode().equals(respVO.getRefundStatus())) {
            // 退款失败
            refundResp.setOrderNo(respVO.getOutTradeNo());
            refundResp.setRefundNo(respVO.getOutRefundNo());
            refundResp.setOutRefundNo(respVO.getRefundNo());
            refundResp.setStatus(PayRefundStatusEnum.FAILURE.getStatus());
            refundResp.setErrorMsg(respVO.getRefundStatusInfo());
        }
        return refundResp;
    }

    @Override
    public BigDecimal getFeeRate() {
        return this.config.getFeeRate();
    }
    
    
    /**
     * 分账
     * @return
     */
    public CreateDivideRespVO divide(DivideReqVO submitReqVO) {
        return client.divide(submitReqVO);
    }
    
    /**
     * 解析多级分账回调
     */
    public DivideOrderRepDto parseDivideNotify(Map<String, String> params, String body) {
        //验签
        MideaPaySdkClient.checkSign(params,client.getPayConfig().getCheckSignUrl());
        //转账回调
        DivideOrderRepDto resp = new DivideOrderRepDto();
        MideaPayProfitOrderNotifyRespVO respVO = JsonUtils.toJavaClass(params, MideaPayProfitOrderNotifyRespVO.class);
        resp.setOutProfitNo(respVO.getOutProfitNo());
        resp.setProfitNo(respVO.getProfitNo());
        resp.setOutTradeNo(respVO.getOutTradeNo());
        resp.setStatus(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(respVO.getTradeStatus()) ? TransferStatusEnum.SUCCESS.getStatus() : TransferStatusEnum.FAIL.getStatus());
        resp.setMsg(MideaPayCompleteStatusEnum.SUCCESS.getCode().equals(respVO.getTradeStatus()) ? respVO.getTradeStatusInfo() : String.format("%s,%s",respVO.getErrorCode(),respVO.getErrorInfo()));
        return resp;
        
    }
    
    
    
    
}