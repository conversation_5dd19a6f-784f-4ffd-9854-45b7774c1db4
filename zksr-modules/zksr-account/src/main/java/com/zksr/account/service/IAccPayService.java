package com.zksr.account.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccPay;
import com.zksr.account.controller.pay.vo.AccPayPageReqVO;
import com.zksr.account.controller.pay.vo.AccPaySaveReqVO;

/**
 * 账户付款单Service接口
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
public interface IAccPayService {

    /**
     * 新增账户付款单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAccPay(@Valid AccPaySaveReqVO createReqVO);

    /**
     * 修改账户付款单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccPay(@Valid AccPaySaveReqVO updateReqVO);

    /**
     * 删除账户付款单
     *
     * @param payId 账户付款单id
     */
    public void deleteAccPay(Long payId);

    /**
     * 批量删除账户付款单
     *
     * @param payIds 需要删除的账户付款单主键集合
     * @return 结果
     */
    public void deleteAccPayByPayIds(Long[] payIds);

    /**
     * 获得账户付款单
     *
     * @param payId 账户付款单id
     * @return 账户付款单
     */
    public AccPay getAccPay(Long payId);

    /**
     * 获得账户付款单分页
     *
     * @param pageReqVO 分页查询
     * @return 账户付款单分页
     */
    PageResult<AccPay> getAccPayPage(AccPayPageReqVO pageReqVO);

    /**
     * 验证订单是否已经支付
     * @param orderNo
     * @return
     */
    boolean validRepeatPay(String orderNo);

    /**
     * 插入支付流水
     * @param accPay
     */
    void insertAccPay(AccPay accPay);

    /**
     * 更加
     * @param orderNo
     * @return
     */
    AccPay getByOrderNo(String orderNo);
}
