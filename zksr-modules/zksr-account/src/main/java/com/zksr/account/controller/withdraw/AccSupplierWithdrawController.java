package com.zksr.account.controller.withdraw;

import cn.hutool.core.collection.ListUtil;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.controller.withdraw.vo.*;
import com.zksr.account.convert.account.AccountConvert;
import com.zksr.account.convert.withdraw.AccWithdrawConvert;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.annotation.DataScope;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.WITHDRAW_IS_EMPTY;
import static com.zksr.account.enums.ErrorCodeConstants.WITHDRAW_NOT_SUPPLIER;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入入驻商提现单接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Api(tags = "管理后台 - 入驻商提现单接口", produces = HttpMethod.APPLICATION_JSON)
@Validated
@RestController
@RequestMapping("/withdraw-supplier")
public class AccSupplierWithdrawController {

    @Autowired
    private IAccWithdrawService accWithdrawService;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 新增入入驻商提现单
     */
    @ApiOperation(value = "新增入入驻商提现单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD_SUPPLIER)
    @RequiresPermissions(Permissions.ADD_SUPPLIER)
    @Log(title = "新增入入驻商提现单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccDirectSettleWithdrawSaveReqVO createReqVO) {
        SupplierDTO supplierDTO = accountCacheService.getSupplierDTO(SecurityUtils.getSupplierId());
        if (Objects.isNull(supplierDTO)) {
            throw exception(WITHDRAW_NOT_SUPPLIER);
        }
        // 创建入入驻商提现单
        createReqVO.setPlatform(PayChannelEnum.HLB);
        createReqVO.setPayType(PayTypeEnum.PAY);
        createReqVO.setMerchantType(MerchantTypeEnum.SUPPLIER);
        createReqVO.setMerchantId(supplierDTO.getSupplierId());
        createReqVO.setMinSettleAmt(supplierDTO.getMinSettleAmt());
        createReqVO.setSysCode(supplierDTO.getSysCode());
        AccWithdraw withdraw = accWithdrawService.insertDirectWithdraw(createReqVO);
        // 处理入入驻商结算
        accWithdrawService.directSettle(withdraw);
        return success(withdraw.getWithdrawId());
    }

    /**
     *
     * 获取入入驻商账户提现单详细信息
     */
    @ApiOperation(value = "获得入入驻商账户提现单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{withdrawId}")
    public CommonResult<AccWithdrawRespVO> getInfo(@PathVariable("withdrawId") Long withdrawId) {
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawId);
        return success(AccWithdrawConvert.INSTANCE.convert(accWithdraw));
    }

    /**
     * 获取入入驻商提现列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入入驻商账户提现单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    @DataScope(supplierAlias = "acc_withdraw", supplierFieldAlias = "merchant_id")
    public CommonResult<PageResult<AccOtherWithdrawRespVO>> getPage(@Valid AccWithdrawPageReqVO pageReqVO) {
        // 入入驻商 / 业务员
        pageReqVO.setMerchantTypeList(ListUtil.toList(MerchantTypeEnum.SUPPLIER.getType()));
        Long supplierId = SecurityUtils.getSupplierId();
        if (Objects.nonNull(supplierId)) {
            pageReqVO.setMerchantId(supplierId);
        }
        PageResult<AccWithdrawRespVO> pageResult = accWithdrawService.getAccWithdrawPage(pageReqVO);
        PageResult<AccOtherWithdrawRespVO> accountResult = AccWithdrawConvert.INSTANCE.convertOtherPage(pageResult);
        accountResult.getList().forEach(item -> AccountConvert.INSTANCE.convert(item, accountCacheService.getSupplierDTO(item.getMerchantId())));
        return success(accountResult);
    }

    /**
     * 提现单审核拒绝
     */
    @ApiOperation(value = "提现单审核拒绝", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "入驻商提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/disable")
    public CommonResult<Boolean> disable(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        // 获取提现单
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawAudit.getWithdrawId());
        // 验证是否是 入驻商提现单
        validateOtherWithdraw(accWithdraw);
        // 拒绝提现
        accWithdrawService.rejectWithdraw(withdrawAudit);
        return success(true);
    }

    /**
     * 提现单审核通过
     */
    @ApiOperation(value = "提现单审核通过", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "入驻商提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/enable")
    public CommonResult<Boolean> enable(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        // 获取提现单
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawAudit.getWithdrawId());
        // 验证是否是 入驻商提现单
        validateOtherWithdraw(accWithdraw);
        // 处理入入驻商结算
        accWithdrawService.directSettle(accWithdraw);
        return success(true);
    }

    private static void validateOtherWithdraw(AccWithdraw accWithdraw) {
        if (Objects.isNull(accWithdraw)
                || !accWithdraw.getMerchantType().equals(MerchantTypeEnum.SUPPLIER.getType())
        ) {
            // 提现单不存在
            throw exception(WITHDRAW_IS_EMPTY);
        }
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加入入驻商提现单 */
        public static final String ADD_SUPPLIER = "account:withdraw-supplier:add";
        /** 编辑 */
        public static final String EDIT = "account:withdraw-supplier:edit";
        /** 删除 */
        public static final String DELETE = "account:withdraw-supplier:remove";
        /** 列表 */
        public static final String LIST = "account:withdraw-supplier:list";
        /** 查询 */
        public static final String GET = "account:withdraw-supplier:query";
        /** 审核驳回 */
        public static final String DISABLE = "account:withdraw-supplier:disable";
        /** 审核通过 */
        public static final String ENABLE = "account:withdraw-supplier:enable";
    }
}
