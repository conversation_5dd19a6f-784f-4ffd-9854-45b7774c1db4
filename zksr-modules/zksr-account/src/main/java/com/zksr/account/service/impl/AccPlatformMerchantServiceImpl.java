package com.zksr.account.service.impl;

import com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO;
import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.client.MerchantClient;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoReqVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoRespVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bSaveReqVO;
import com.zksr.account.convert.account.PlatformMerchantConvert;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.enums.MerchantCredentialType;
import com.zksr.account.mapper.AccPlatformMerchantMapper;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.metchant.MerchantChannelService;
import com.zksr.account.service.metchant.MerchantService;
import com.zksr.account.service.metchant.PlatformMerchantService;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.bank.BankChannelApi;
import com.zksr.system.api.bank.vo.SysBankChannelRespVO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;


/**
 * 支付平台商户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class AccPlatformMerchantServiceImpl implements IAccPlatformMerchantService {

    @Autowired
    private AccPlatformMerchantMapper accPlatformMerchantMapper;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private MerchantChannelService merchantChannelService;

    @Autowired
    private BankChannelApi bankChannelApi;

    @Autowired
    private List<PlatformMerchantService> merchantService;

    /**
     * 新增支付平台商户
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long savePlatformMerchant(AccPlatformMerchantSaveReqVO createReqVO) {
        AccPlatformMerchant accPlatformMerchant = PlatformMerchantConvert.INSTANCE.convert(createReqVO);
        AccPlatformMerchant platformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(createReqVO.getMerchantId(), createReqVO.getMerchantType(), createReqVO.getPlatform());
        accPlatformMerchant.setMchStatus(StringPool.ONE);
        if (Objects.isNull(createReqVO.getSysCode())) {
            throw new ServiceException("sysCode 必填");
        }
        if (StringUtils.isEmpty(createReqVO.getAltMchNo())) {
            throw new ServiceException("altMchNo 进件商户号必填");
        }
        if (StringUtils.isEmpty(createReqVO.getThirdOrderNo())) {
            throw new ServiceException("thirdOrderNo 进件单号必填");
        }
        PayPlatformMerchantVO merchantInfo = SpringUtils.getBean(MerchantService.class).getMerchantInfo(accPlatformMerchant.getSysCode(), createReqVO.getPlatform(), createReqVO.getThirdOrderNo(), createReqVO.getAltMchNo());
        if (Objects.isNull(merchantInfo) || StringUtils.isEmpty(merchantInfo.getMerchantNo())) {
            throw exception(NOT_EXIST_MERCHANT);
        }
        // 组装信息
        accPlatformMerchant.setAccountNo(merchantInfo.getAccountNo())
                .setBankName(merchantInfo.getBankName())
                .setBankBranch(merchantInfo.getBankBranch())
                .setAltMchName(merchantInfo.getSignName())
                .setBankType(merchantInfo.getSettleBankType())
                .setThirdOrderNo(createReqVO.getThirdOrderNo())
        ;
        if (Objects.nonNull(platformMerchant)) {
            // 更新
            accPlatformMerchant.setPlatformMerchantId(platformMerchant.getPlatformMerchantId());
            accPlatformMerchantMapper.updateById(accPlatformMerchant);
        } else {
            // 插入
            if (Objects.nonNull(SecurityContextHolder.getSysCode()) && SecurityContextHolder.getSysCode() > 0L) {
                // 如果有sysCode 就移除sysCode 防止报错
                accPlatformMerchant.setSysCode(null);
            } else {
                if (MerchantTypeEnum.PARTNER.getType().equals(accPlatformMerchant.getMerchantType())) {
                    accPlatformMerchant.setSysCode(accPlatformMerchant.getMerchantId());
                }
            }
            accPlatformMerchantMapper.insert(accPlatformMerchant);
        }
        // 返回
        return accPlatformMerchant.getPlatformMerchantId();
    }

    /**
     * 修改支付平台商户
     *
     * @param updateReqVO 修改信息
     */
    @Override
    public void updateAccPlatformMerchant(AccPlatformMerchantSaveReqVO updateReqVO) {
        accPlatformMerchantMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, AccPlatformMerchant.class));
    }

    /**
     * 删除支付平台商户
     *
     * @param platformMerchantId 支付平台商户id
     */
    @Override
    public void deleteAccPlatformMerchant(Long platformMerchantId) {
        AccPlatformMerchant platformMerchant = accPlatformMerchantMapper.selectById(platformMerchantId);
        // 0-停用  1-启用
        platformMerchant.setMchStatus(StringPool.ZERO);
        // 删除
        accPlatformMerchantMapper.updateById(platformMerchant);
    }

    /**
     * 批量删除支付平台商户
     *
     * @param platformMerchantIds 需要删除的支付平台商户主键
     */
    @Override
    public void deleteAccPlatformMerchantByPlatformMerchantIds(Long[] platformMerchantIds) {
        for(Long platformMerchantId : platformMerchantIds){
            this.deleteAccPlatformMerchant(platformMerchantId);
        }
    }

    /**
     * 获得支付平台商户
     *
     * @param platformMerchantId 支付平台商户id
     * @return 支付平台商户
     */
    @Override
    public AccPlatformMerchant getAccPlatformMerchant(Long platformMerchantId) {
        return accPlatformMerchantMapper.selectById(platformMerchantId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO  分页请求
    * @return 分页数据
    */
    @Override
    public PageResult<AccPlatformMerchant> getAccPlatformMerchantPage(AccPlatformMerchantPageReqVO pageReqVO) {
        return accPlatformMerchantMapper.selectPage(pageReqVO);
    }

    @Override
    public AccPlatformMerchant getPlatformMerchant(Long merchantId, String merchantType, String platform) {
        if (PayChannelEnum.isMock(platform)) {
            // 如果是模拟支付则返回一个假的进件号, 因为模拟支付不需要进件号
           return AccPlatformMerchant.getMockAccount();
        }
        return accPlatformMerchantMapper.selectPlatformMerchant(merchantId, merchantType, platform);
    }

    @Override
    public AccPlatformMerchant getPlatformMerchant(Long merchantId, String merchantType, String platform, Long sysCode) {
        return accPlatformMerchantMapper.selectPlatformMerchant(merchantId, merchantType, platform, sysCode);
    }

    @Override
    public AccPlatformMerchant getPlatformMerchant(Long merchantId, String merchantType, Long sysCode) {
        // 获取当前平台配置platform
        PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfigDto)) {
            // 大区支付配置不存
            return null;
        }
        // 如果是入驻商就使用支付体系平台, 其他的储值体系
        String platform = MerchantTypeEnum.isSupplier(merchantType) ? payConfigDto.getStoreOrderPayPlatform() : payConfigDto.getInteriorStoredPayPlatform();
        // 如果是B2B支付, 强制B2B
        if (PayChannelEnum.isB2b(payConfigDto.getStoreOrderPayPlatform())) {
            platform = PayChannelEnum.WX_B2B_PAY.getCode();
        }
        return accPlatformMerchantMapper.selectPlatformMerchant(merchantId, merchantType, platform, sysCode);
    }

    @Override
    public boolean checkExist(Long merchantId, String merchantType, String platform) {
        return accPlatformMerchantMapper.selectCountByMerchant(merchantId, merchantType, platform) > 0L;
    }

    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_MERCHANT, condition = "#reqVO.merchantId")
    public Long register(AccPlatformMerchantRegisterReqVO reqVO) {
        // 平台商
        Long sysCode = SecurityUtils.hashSysCode() ? SecurityUtils.getLoginUser().getSysCode() : reqVO.getSysCode();

        // 如有没有设置平台就去尝试获取默认的
        if (StringUtils.isEmpty(reqVO.getPlatform())) {
            reqVO.setPlatform(getPayPlatform(reqVO.getMerchantType()));
        }
        AccPlatformMerchant accPlatformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(reqVO.getMerchantId(), reqVO.getMerchantType(), reqVO.getPlatform(), reqVO.getSysCode());

        // 验证是否已经存在
        validateRegisterState(accPlatformMerchant);

        // 获取平台商户操作client
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(
                sysCode,
                PayTypeEnum.PAY,
                reqVO.getPlatform());

        // 进件注册
        PlatformMerchantRegisterSaveRespVO respVO = merchantClient.registerMerchant(reqVO);
        if (MerchantRegisterStateEnum.fail(respVO.getAuditStatus())) {
            // 抛出异常
            throw new ServiceException(respVO.getMsg());
        }

        // 获取第三方平台商户服务
        PlatformMerchantService platformMerchantService = this.getMerchantService(reqVO.getPlatform());

        // 保存进件信息
        return platformMerchantService.saveRegisterInfo(reqVO, respVO);
    }

    private SysBankChannelRespVO validateBankChannel(String bankChannelNo) {
        if (StringUtils.isNotEmpty(bankChannelNo)) {
            SysBankChannelRespVO bankChannelRespVO = bankChannelApi.getChannelByNo(bankChannelNo).getCheckedData();
            if (Objects.isNull(bankChannelRespVO)) {
                throw exception(PLATFORM_MERCHANT_BANK_CHANNEL_NOT_EXIST);
            }
            return bankChannelRespVO;
        }
        return null;
    }

    private static void validateRegisterState(AccPlatformMerchant accPlatformMerchant) {
        if (Objects.nonNull(accPlatformMerchant)) {
            if (MerchantRegisterStateEnum.success(accPlatformMerchant.getAuditStatus()))
                throw exception(PLATFORM_MERCHANT_AUDITED_FINISH);
            if (MerchantRegisterStateEnum.wait(accPlatformMerchant.getAuditStatus()))
                throw exception(PLATFORM_MERCHANT_AUDITED_WAIT);
        }
    }

    private String getPayPlatform(String merchantType) {
        Long sysCode = SecurityContextHolder.getSysCode();
        if (Objects.isNull(sysCode)) {
            throw new ServiceException("平台信息获取到, 进件未指定平台信息");
        }
        PayConfigDTO configDTO = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(configDTO)) {
            // 大区支付配置不存在
            throw exception(PAY_PLATFORM_NOT_EXIST, sysCode);
        }
        return MerchantTypeEnum.isSupplier(merchantType) ? configDTO.getStoreOrderPayPlatform() : configDTO.getInteriorStoredPayPlatform();
    }

    @Override
    public void updateRegisterStatus(PlatformMerchantRegisterSaveRespVO respVO) {
        // 查询进件商户信息
        AccPlatformMerchant platformMerchant = accPlatformMerchantMapper.selectByThirdOrderNo(respVO.getOrderNo());
        if (Objects.isNull(platformMerchant)) {
            log.error("进件商户不存在orderNo={}", respVO.getOrderNo());
            return;
        }
        // 更新进件状态
        AccPlatformMerchant update = AccPlatformMerchant.builder()
                .auditStatus(respVO.getAuditStatus())
                .platformMerchantId(platformMerchant.getPlatformMerchantId())
                .altMchNo(respVO.getMerchantNo())
                .authMsg(respVO.getMsg())
                .build();
        accPlatformMerchantMapper.updateById(update);
    }

    @Override
    public void uploadPic(AccPlatformMerchantUploadPicReqVO reqVO) {
        // 如有没有设置平台就去尝试获取默认的
        if (StringUtils.isEmpty(reqVO.getPlatform())) {
            reqVO.setPlatform(getPayPlatform(reqVO.getMerchantType()));
        }
        AccPlatformMerchant platformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(reqVO.getMerchantId(), reqVO.getMerchantType(), reqVO.getPlatform());
        if (Objects.isNull(platformMerchant)) {
            throw exception(PLATFORM_MERCHANT_NOT_EXIST_MERCHANT);
        }
        if (!MerchantRegisterStateEnum.success(platformMerchant.getAuditStatus())) {
            throw exception(PLATFORM_MERCHANT_NOT_AUDITED);
        }
        // 获取平台商户操作client
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(
                SecurityUtils.getLoginUser().getSysCode(),
                PayTypeEnum.PAY,
                reqVO.getPlatform());

        AccPlatformMerchant updateMerchant = new AccPlatformMerchant();
        updateMerchant.setPlatformMerchantId(platformMerchant.getPlatformMerchantId());

        for (PlatformMerchantUploadSaveReqVO saveReqVO : reqVO.getPlatformMerchantUploadSaveReqVO()) {
            saveReqVO.setAltMchNo(platformMerchant.getAltMchNo());
            saveReqVO.setOrderNo(platformMerchant.getThirdOrderNo());

            MerchantCredentialType credentialType = saveReqVO.getCredentialType();

            // 身份证正面
            if (credentialType == MerchantCredentialType.FRONT_OF_ID_CARD) {
                updateMerchant.setCardPositiveUrl(saveReqVO.getCredentialUrl());
            }
            // 身份证反面
            if (credentialType == MerchantCredentialType.BACK_OF_ID_CARD) {
                updateMerchant.setCardNegativeUrl(saveReqVO.getCredentialUrl());
            }
            // 营业执照
            if (credentialType == MerchantCredentialType.UNIFIED_CODE_CERTIFICATE) {
                updateMerchant.setTradeLicenceUrl(saveReqVO.getCredentialUrl());
            }
            // 开户许可证
            if (credentialType == MerchantCredentialType.PERMIT_FOR_BANK_ACCOUNT) {
                updateMerchant.setOpenAccountLicenceUrl(saveReqVO.getCredentialUrl());
            }
            PlatformMerchantUploadSaveRespVO merchantUploadSaveRespVO;
            if (StringUtils.isNotEmpty(platformMerchant.getCardNegativeUrl())) {
                // 修改
                merchantUploadSaveRespVO = merchantClient.changePic(saveReqVO);
            } else {
                // 新增
                merchantUploadSaveRespVO = merchantClient.uploadPic(saveReqVO);
                // 如果错误信息里面有已上传, 则向下兼容使用修改接口
                if (StringUtils.isNotEmpty(merchantUploadSaveRespVO.getMsg()) && merchantUploadSaveRespVO.getMsg().contains("已上传")) {
                    merchantUploadSaveRespVO = merchantClient.changePic(saveReqVO);
                }
            }
            // 判断是否上传成功
            if (MerchantUploadPicStateEnum.fail(merchantUploadSaveRespVO.getAuditStatus())) {
                throw new ServiceException(StringUtils.format("资质变更异常: {}", merchantUploadSaveRespVO.getMsg()));
            }
            updateMerchant.setPicStatus(merchantUploadSaveRespVO.getAuditStatus());
            accPlatformMerchantMapper.updateById(updateMerchant);
        }
    }

    @Override
    public Long updateRegister(AccPlatformMerchantRegisterReqVO reqVO) {
        // 如有没有设置平台就去尝试获取默认的
        if (StringUtils.isEmpty(reqVO.getPlatform())) {
            reqVO.setPlatform(getPayPlatform(reqVO.getMerchantType()));
        }
        // 查询进件商户信息
        AccPlatformMerchant platformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(reqVO.getMerchantId(), reqVO.getMerchantType(), reqVO.getPlatform());
        if (Objects.isNull(platformMerchant)) {
            throw exception(PLATFORM_MERCHANT_NOT_EXIST_MERCHANT);
        }
        // 验证银行信息
        SysBankChannelRespVO bankChannelRespVO  = validateBankChannel(reqVO.getPlatformMerchantRegisterSaveReqVO().getBankChannelNo());

        // 如果是失败重新走进件
        if (MerchantRegisterStateEnum.fail(platformMerchant.getAuditStatus())) {
            // 重新走进件
            return this.register(reqVO);
        }
        if (!MerchantRegisterStateEnum.success(platformMerchant.getAuditStatus())) {
            throw exception(PLATFORM_MERCHANT_NOT_AUDITED);
        }
        // 获取平台商户操作client
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(
                SecurityUtils.getLoginUser().getSysCode(),
                PayTypeEnum.PAY,
                reqVO.getPlatform());

        PlatformMerchantRegisterSaveReqVO saveReqVO = reqVO.getPlatformMerchantRegisterSaveReqVO();

        PlatformMerchantRegisterSaveRespVO respVO = merchantClient.updateMerchant(saveReqVO, platformMerchant);
        if (MerchantRegisterStateEnum.fail(respVO.getAuditStatus())) {
            // 抛出异常
            throw new ServiceException(respVO.getMsg());
        }

        // 审核中, 或者审核成功, 这里保存商户信息
        AccPlatformMerchant update = AccPlatformMerchant
                .builder()
                .platformMerchantId(platformMerchant.getPlatformMerchantId())
                .mchStatus(StringPool.ONE)
                .accountName(saveReqVO.getBankAccountName())
                .accountNo(saveReqVO.getBankAccountNo())
                .bankType("0".equals(saveReqVO.getSettleBankType()) ? "TOPRIVATE" : "TOPUBLIC")
                .editStatus(respVO.getAuditStatus())
                .editOrderNo(respVO.getOrderNo())
                .editMsg(StringPool.EMPTY)
                .idCard(saveReqVO.getIdCardNo())
                .bankChannelNo(saveReqVO.getBankChannelNo())
                .contractName(saveReqVO.getBusiContactName())
                .contractPhone(saveReqVO.getBusiContactMobileNo())
                .busiMerchantType(saveReqVO.getMerchantType())
                .legalPerson(saveReqVO.getLegalPerson())
                .licenseNo(saveReqVO.getLicenseNo())
                .build();
        // 设置银行信息
        if (Objects.nonNull(bankChannelRespVO)) {
            platformMerchant.setBankName(bankChannelRespVO.getBankName());
            platformMerchant.setBankBranch(bankChannelRespVO.getBranchName());
        }
        accPlatformMerchantMapper.updateById(update);
        return platformMerchant.getPlatformMerchantId();
    }

    @Override
    public void syncData(AccPlatformMerchantSyncReqVO reqVO) {
        // 查询进件商户信息
        AccPlatformMerchant platformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(reqVO.getMerchantId(), reqVO.getMerchantType(), reqVO.getPlatform(), reqVO.getSysCode());
        if (Objects.isNull(platformMerchant)) {
            throw exception(PLATFORM_MERCHANT_NOT_EXIST_MERCHANT);
        }
        // 获取平台商户操作client
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(
                Objects.nonNull(reqVO.getSysCode()) ? reqVO.getSysCode() : SecurityUtils.getLoginUser().getSysCode(),
                PayTypeEnum.PAY,
                reqVO.getPlatform());


        // 更新进件状态
        PayPlatformMerchantVO registerMerchant = merchantClient.getRegisterMerchant(platformMerchant.getThirdOrderNo(), platformMerchant.getAltMchNo());

        // 获取第三方平台商户服务
        PlatformMerchantService platformMerchantService = this.getMerchantService(platformMerchant.getPlatform());

        // 更新最新进件状态
        platformMerchantService.syncData(registerMerchant, platformMerchant, merchantClient);
    }

    @Override
    public Long processMerchantStatus(Long minId) {
        // 查询需要处理状态的商户信息
        List<AccPlatformMerchant> platformMerchants = accPlatformMerchantMapper.selectProceeingStatusList(minId);
        for (AccPlatformMerchant platformMerchant : platformMerchants) {
            syncData(AccPlatformMerchantSyncReqVO.builder()
                            .merchantId(platformMerchant.getMerchantId())
                            .merchantType(platformMerchant.getMerchantType())
                            .platform(platformMerchant.getPlatform())
                            .sysCode(platformMerchant.getSysCode())
                            .build()
            );
        }
        if (platformMerchants.isEmpty()) {
            return null;
        }
        return platformMerchants.get(platformMerchants.size() - 1).getPlatformMerchantId();
    }

    @Override
    public AccPlatformMerchantWxb2bInfoRespVO getUpdateWxB2bPayConfig(AccPlatformMerchantWxb2bInfoReqVO reqVO) {
        return getMerchantService(PayChannelEnum.WX_B2B_PAY.getCode()).getUpdateWxB2bPayConfig(reqVO);
    }

    @Override
    @Transactional
    public Long updateWxB2bPayConfig(AccPlatformMerchantWxb2bSaveReqVO saveReqVO) {
        return getMerchantService(PayChannelEnum.WX_B2B_PAY.getCode()).updateWxB2bPayConfig(saveReqVO);
    }

    @Override
    public List<PlatformMerchantNameAndKeyDTO> getPlatformMerchantNameAndKey(String merchantType, Long sysCode, String payPlatform) {
        return accPlatformMerchantMapper.getPlatformMerchantNameAndKey(merchantType, sysCode, payPlatform);
    }

    @Override
    public List<Long> getMerchantIdByAltNo(String merchantType, String altNo, Long sysCode) {
        return accPlatformMerchantMapper.getMerchantIdByAltNo(merchantType,altNo,sysCode);
    }

    /**
     * 获取支付平台, 不同商户信息, 执行不同操作
     * @param platform
     * @return
     */
    public PlatformMerchantService getMerchantService(String platform) {
        for (PlatformMerchantService service : merchantService) {
            if (service.channel().getCode().equals(platform)) {
                return service;
            }
        }
        throw exception(PLATFORM_MERCHANT_SERVICE_UNDEFINE);
    }
}
