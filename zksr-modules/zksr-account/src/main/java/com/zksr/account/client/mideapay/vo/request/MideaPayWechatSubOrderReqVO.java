package com.zksr.account.client.mideapay.vo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayWechatSubOrderReqVO implements Serializable {

    @JsonProperty("partner")
    private String partner;

    @JsonProperty("market_acc_partner")
    private String marketAccPartner;

    @JsonProperty("sub_out_trade_no")
    private String subOutTradeNo;

    @JsonProperty("pay_amount")
    private String payAmount;

    @JsonProperty("market_amount")
    private String marketAmount;

    @JsonProperty("sub_product_name")
    private String subProductName;

    @JsonProperty("sub_product_info")
    private String subProductInfo;

    @JsonProperty("sub_product_count")
    private String subProductCount;

    @JsonProperty("sub_product_price")
    private String subProductPrice;

    @JsonProperty("sub_trade_purpose")
    private String subTradePurpose;

    @JsonProperty("sub_attach")
    private String subAttach;

    @JsonProperty("profit_params")
    private String profitParams;

    @JsonProperty("profit_sharing")
    private String profitSharing;
}
