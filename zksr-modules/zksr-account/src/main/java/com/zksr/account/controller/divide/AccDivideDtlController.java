package com.zksr.account.controller.divide;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.service.IAccDivideDtlService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.controller.divide.vo.AccDivideDtlPageReqVO;
import com.zksr.account.controller.divide.vo.AccDivideDtlSaveReqVO;
import com.zksr.account.controller.divide.vo.AccDivideDtlRespVO;
import com.zksr.account.convert.divide.AccDivideDtlConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 支付分账详情Controller
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Api(tags = "管理后台 - 支付分账详情接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/divideDtl")
public class AccDivideDtlController {

    @Autowired
    private IAccDivideDtlService accDivideDtlService;

    /**
     * 新增支付分账详情
     */
    @ApiOperation(value = "新增支付分账详情", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "支付分账详情", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccDivideDtlSaveReqVO createReqVO) {
        return success(accDivideDtlService.insertAccDivideDtl(createReqVO));
    }

    /**
     * 修改支付分账详情
     */
    @ApiOperation(value = "修改支付分账详情", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "支付分账详情", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AccDivideDtlSaveReqVO updateReqVO) {
            accDivideDtlService.updateAccDivideDtl(updateReqVO);
        return success(true);
    }

    /**
     * 删除支付分账详情
     */
    @ApiOperation(value = "删除支付分账详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "支付分账详情", businessType = BusinessType.DELETE)
    @DeleteMapping("/{divideDtlIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] divideDtlIds) {
        accDivideDtlService.deleteAccDivideDtlByDivideDtlIds(divideDtlIds);
        return success(true);
    }

    /**
     * 获取支付分账详情详细信息
     */
    @ApiOperation(value = "获得支付分账详情详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{divideDtlId}")
    public CommonResult<AccDivideDtlRespVO> getInfo(@PathVariable("divideDtlId") Long divideDtlId) {
        AccDivideDtl accDivideDtl = accDivideDtlService.getAccDivideDtl(divideDtlId);
        return success(AccDivideDtlConvert.INSTANCE.convert(accDivideDtl));
    }

    /**
     * 分页查询支付分账详情
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得支付分账详情分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccDivideDtlRespVO>> getPage(@Valid AccDivideDtlPageReqVO pageReqVO) {
        PageResult<AccDivideDtlRespVO> pageResult = accDivideDtlService.getAccDivideDtlPage(pageReqVO);
        return success(pageResult);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:divideDtl:add";
        /** 编辑 */
        public static final String EDIT = "account:divideDtl:edit";
        /** 删除 */
        public static final String DELETE = "account:divideDtl:remove";
        /** 列表 */
        public static final String LIST = "account:divideDtl:list";
        /** 查询 */
        public static final String GET = "account:divideDtl:query";
        /** 停用 */
        public static final String DISABLE = "account:divideDtl:disable";
        /** 启用 */
        public static final String ENABLE = "account:divideDtl:enable";
    }
}
