package com.zksr.account.service.impl;

import com.zksr.account.controller.flow.vo.AccPayFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccPayFlowSaveReqVO;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.mapper.AccPayFlowMapper;
import com.zksr.account.model.pay.vo.PayRefundQueryVO;
import com.zksr.account.service.IAccPayFlowService;
import com.zksr.common.core.domain.vo.openapi.AccPayReceiptReqDTO;
import com.zksr.common.core.domain.vo.openapi.ReceiptPayOpenDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-10
 */
@Service
public class AccPayFlowServiceImpl implements IAccPayFlowService {
    @Autowired
    private AccPayFlowMapper accPayFlowMapper;

    /**
     * 新增支付流水
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertAccPayFlow(AccPayFlowSaveReqVO createReqVO) {
        // 插入
        AccPayFlow accPayFlow = HutoolBeanUtils.toBean(createReqVO, AccPayFlow.class);
        accPayFlowMapper.insert(accPayFlow);
        // 返回
        return accPayFlow.getPayFlowId();
    }

    /**
     * 修改支付流水
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAccPayFlow(AccPayFlowSaveReqVO updateReqVO) {
        accPayFlowMapper.updateById(HutoolBeanUtils.toBean(updateReqVO, AccPayFlow.class));
    }

    /**
     * 删除支付流水
     *
     * @param payFlowId 支付流水id
     */
    @Override
    public void deleteAccPayFlow(Long payFlowId) {
        // 删除
        accPayFlowMapper.deleteById(payFlowId);
    }

    /**
     * 批量删除支付流水
     *
     * @param payFlowIds 需要删除的支付流水主键
     * @return 结果
     */
    @Override
    public void deleteAccPayFlowByPayFlowIds(Long[] payFlowIds) {
        for(Long payFlowId : payFlowIds){
            this.deleteAccPayFlow(payFlowId);
        }
    }

    /**
     * 获得支付流水
     *
     * @param payFlowId 支付流水id
     * @return 支付流水
     */
    @Override
    public AccPayFlow getAccPayFlow(Long payFlowId) {
        return accPayFlowMapper.selectById(payFlowId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccPayFlow> getAccPayFlowPage(AccPayFlowPageReqVO pageReqVO) {
        return accPayFlowMapper.selectPage(pageReqVO);
    }

    @Override
    public boolean validRepeatPay(String orderNo) {
        return accPayFlowMapper.selectByOrderSuccess(orderNo) > NumberPool.LONG_ZERO;
    }

    @Override
    public AccPayFlow getByOrderPayFlow(String orderNo) {
        return accPayFlowMapper.selectByOrderPayFlow(orderNo);
    }

    public List<AccPayFlow> selectByOrdersPayFlow(List<String> orderNos){
        return accPayFlowMapper.selectByOrdersPayFlow(orderNos);
    }

    @Override
    public AccPayFlow getByOrderRefundFlow(String refundNo) {
        return accPayFlowMapper.selectByOrderRefundFlow(refundNo);
    }

    @Override
    public AccPayFlow getByOrderPayFlowSuccessFlag(String orderNo) {
        return accPayFlowMapper.selectByOrderPayFlowSuccessFlag(orderNo);
    }

    @Override
    public AccPayFlow getById(Long payFlowId) {
        return accPayFlowMapper.selectById(payFlowId);
    }

    @Override
    public List<PayRefundQueryVO> getWxB2bRefundProcessAgainList(Long minId) {
        List<AccPayFlow> payFlows = accPayFlowMapper.selectByWxB2bRefundProcessAgainList(minId);
        return payFlows.stream().map(item -> PayRefundQueryVO.builder()
                .refundFlowId(item.getPayFlowId())
                .tradeNo(item.getTradeNo())
                .refundNo(item.getRefundNo())
                .build()).collect(Collectors.toList());
    }

    @Override
    public List<AccPayFlow> getByOrderRefundFlowList(String tradeNo) {
        return accPayFlowMapper.selectByOrderRefundFlowList(tradeNo);
    }

    @Override
    public List<ReceiptPayOpenDTO> getOrderPayInfoListBySyncOrder(AccPayReceiptReqDTO reqVo) {
        return accPayFlowMapper.selectOrderPayInfoListBySyncOrder(reqVo);
    }

    @Override
    public List<AccPayFlow> getWxB2bRechargeDivideFlow(Long minPayFlowId) {
        return accPayFlowMapper.selectWxB2bRechargeDivideFlow(minPayFlowId);
    }


    private void validateAccPayFlowExists(Long payFlowId) {
        /*if (accPayFlowMapper.selectById(payFlowId) == null) {
            throw exception(ACC_PAY_FLOW_NOT_EXISTS);
        }*/
    }


}
