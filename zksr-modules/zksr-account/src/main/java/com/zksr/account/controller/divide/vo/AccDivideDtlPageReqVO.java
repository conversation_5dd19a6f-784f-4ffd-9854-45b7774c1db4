package com.zksr.account.controller.divide.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 支付分账详情对象 acc_divide_dtl
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@ApiModel("支付分账详情 - acc_divide_dtl分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccDivideDtlPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 支付分账详情id */
    @ApiModelProperty(value = "订单类型 0-商城订单  1-入驻商充值  2-门店充值")
    private Long divideDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id", required = true)
    private Long merchantId;

    /** 分账方商户编号 */
    @Excel(name = "分账方商户编号")
    @ApiModelProperty(value = "分账方商户编号", required = true)
    private String altMchNo;

    /** 分账方名称 */
    @Excel(name = "分账方名称")
    @ApiModelProperty(value = "分账方名称")
    private String altMchName;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)", required = true)
    private String platform;

    /** 支付流水id */
    @ApiModelProperty(value = "支付平台(数据字典)")
    private Long payFlowId;

    /** 0-线上分账  1-线下分账 */
    @Excel(name = "0-线上分账  1-线下分账")
    @ApiModelProperty(value = "0-线上分账  1-线下分账")
    private Integer onlineOrOffline;

    /** 线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账 */
    @Excel(name = "线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账")
    @ApiModelProperty(value = "线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账")
    private Integer onlineDivideState;

    /** 线下处理状态;0-未处理 1-已处理 2-无需处理 针对分账失败或者不分账的订单的补偿措施 */
    @Excel(name = "线下处理状态;0-未处理 1-已处理 2-无需处理 针对分账失败或者不分账的订单的补偿措施")
    @ApiModelProperty(value = "线下处理状态;0-未处理 1-已处理 2-无需处理 针对分账失败或者不分账的订单的补偿措施")
    private Integer offlineProState;

    /** 线下处理单号 */
    @Excel(name = "线下处理单号")
    @ApiModelProperty(value = "线下处理单号")
    private String offlineProNo;


    /** 支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号 */
    @Excel(name = "支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号")
    @ApiModelProperty(value = "支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号")
    private String tradeNo;

    /** 子订单号 */
    @Excel(name = "子订单号")
    @ApiModelProperty(value = "子订单号")
    private String subTradeNo;

    /** 支付平台商户订单号 */
    @Excel(name = "支付平台商户订单号")
    @ApiModelProperty(value = "支付平台商户订单号")
    private String outTradeNo;

    /** 退款单号;商城退款即是售后单号 */
    @Excel(name = "退款单号;商城退款即是售后单号")
    @ApiModelProperty(value = "退款单号;商城退款即是售后单号")
    private String refundNo;

    /** 支付平台商户退款单号 */
    @Excel(name = "支付平台商户退款单号")
    @ApiModelProperty(value = "支付平台商户退款单号")
    private String outRefundNo;

    /** 支付类型 0-支付 1-退款 */
    @Excel(name = "支付类型 0-支付 1-退款")
    @ApiModelProperty(value = "支付类型 0-支付 1-退款")
    private Integer payType;

    /** 第三方支付平台分账流水号 */
    @Excel(name = "第三方支付平台分账流水号")
    @ApiModelProperty(value = "第三方支付平台分账流水号")
    private String platformDivideFlowNo;

    /** 订单类型 0-商城订单  1-入驻商充值  2-门店充值 */
    @Excel(name = "订单类型 0-商城订单  1-入驻商充值  2-门店充值")
    @ApiModelProperty(value = "订单类型 0-商城订单  1-入驻商充值  2-门店充值")
    private Integer orderType;

    /** 创建开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "创建开始时间")
    private Date startTime;

    /** 创建结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "创建结束时间")
    private Date endTime;

    /** 分账处理开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "分账处理开始时间")
    private Date divideTime;

    /** 分账处理结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "分账处理结束时间")
    private Date divideEndTime;
}
