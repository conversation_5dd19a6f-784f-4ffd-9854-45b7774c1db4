package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

@TableName(value = "acc_invoice_setting")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccInvoiceSetting extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long accInvoiceSettingId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    private Long merchantId;
    
    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;
    
    /** 是否开通电子发票（0 否 1 是) */
    @Excel(name = "是否开通电子发票")
    @ApiModelProperty("是否开通电子发票（0 否 1 是)")
    private Integer enableElectronicInvoice;
    
    /** 是否开票（0 否 1 是) */
    @Excel(name = "是否开票")
    @ApiModelProperty("是否开票（0 否 1 是)")
    private Integer enableInvoice;

    /** 开票周期(1-按天 2-按月 3-按季度 4-按年度 5-手动) */
    @Excel(name = "开票周期")
    @ApiModelProperty("开票周期(1-按天 2-按月 3-按季度 4-按年度 5-手动)")
    private Integer invoicePeriod;
    /** 单位名称 */
    @Excel(name = "单位名称")
    @ApiModelProperty("单位名称")
    private String unitName;
    
    /** 纳税人识别号 */
    @Excel(name = "纳税人识别号")
    @ApiModelProperty("纳税人识别号")
    private String taxpayerNo;
    
    /** 单位地址 */
    @Excel(name = "单位地址")
    @ApiModelProperty("单位地址")
    private String unitAddress;

    /** 单位电话 */
    @Excel(name = "单位电话")
    @ApiModelProperty("单位电话")
    private String unitPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    @ApiModelProperty("开户银行")
    private String bankName;

    /** 银行账户 */
    @Excel(name = "银行账户")
    @ApiModelProperty("银行账户")
    private String bankAccount;
    
    /** 收票人手机 */
    @Excel(name = "收票人手机")
    @ApiModelProperty("收票人手机")
    private String recipientPhone;

    /** 开票人邮箱 */
    @Excel(name = "开票人邮箱")
    @ApiModelProperty("开票人邮箱")
    private String invoiceEmail;

    /** 开票内容 */
    @Excel(name = "开票内容")
    @ApiModelProperty("开票内容")
    private String invoiceContent;
    
    


}
