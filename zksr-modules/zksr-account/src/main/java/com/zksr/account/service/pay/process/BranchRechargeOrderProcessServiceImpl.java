package com.zksr.account.service.pay.process;

import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccRechargeService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.ChargeTypeEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayWayEnum;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.SupplierApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店充值暂时和入驻商充值逻辑一样
 * @date 2024年3月22日09:40:46
 */
@Service
@Slf4j
public class BranchRechargeOrderProcessServiceImpl extends RechargeOrderProcessServiceImpl {

    @Autowired
    private IAccRechargeService rechargeService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Override
    public Integer getOrderType() {
        return OrderTypeConstants.BRANCH_CHARGE;
    }

    @Override
    public PayOrderDTO validateOrder(PayOrderSubmitReqVO reqVO) {
        // 必须参数 ============================== start
        PayOrderDTO dto = new PayOrderDTO();
        dto.setOrderType(reqVO.getOrderType());
        dto.setAppid(reqVO.getAppid());
        dto.setOrderNo(reqVO.getOrderNo());
        dto.setOpenid(reqVO.getOpenid());
        // 必须参数 ============================== end
        AccRecharge recharge = rechargeService.getRechargeByNo(reqVO.getOrderNo());
        if (Objects.isNull(recharge)) {
            throw exception(NOT_EXIST_RECHARGE);
        }
        if (!ChargeTypeEnum.BRANCH.getType().equals(recharge.getChargeType())) {
            log.warn("处理充值单异常, 充值单类型非branch, rechargeNo : {}", reqVO.getOrderNo());
            throw exception(NON_RECHARGE_TYPE);
        }
        dto.setSysCode(recharge.getSysCode());
        dto.setPayAmt(recharge.getRechargeAmt());
        dto.setBody(String.format("预充值活动资金"));
        dto.setBusiId(recharge.getRechargeId());
        // 组装分账
        // 获取平台合利宝支付配置, 目前只有合利宝, 无需判断平台目前是什么支付平台
        PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(recharge.getSysCode());
        if (Objects.isNull(payConfigDTO)) {
            log.warn("平台找不到支付配置, sysCode : {}", recharge.getSysCode());
            throw exception(NOT_EXIST_MERCHANT_CONFIG);
        }
        dto.setSettlements(rechargeService.getBranchSettles(recharge, payConfigDTO));
        reqVO.setSysCode(recharge.getSysCode());
        // 返回分账信息
        return dto;
    }
}
