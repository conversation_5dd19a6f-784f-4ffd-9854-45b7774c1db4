package com.zksr.account.api.balance;

import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.balance.vo.AccBalanceRespVO;
import com.zksr.account.service.IAccBalanceService;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@InnerAuth
@ApiIgnore
@RestController
public class BalanceApiImpl implements BalanceApi {

    @Autowired
    private IAccBalanceService accBalanceService;

    @Override
    public CommonResult<List<AccBalanceRespVO>> getBalanceInfoList(List<Long> branchIdList) {
        return CommonResult.success(accBalanceService.getBalanceInfoList(branchIdList));
    }

    @Override
    public CommonResult<String> recharge(MemBranchBalanceDTO balanceDTO) {
        return  CommonResult.success(accBalanceService.recharge(balanceDTO));
    }

    @Override
    public CommonResult<String> refund(MemBranchBalanceDTO balanceDTO) {
        return  CommonResult.success(accBalanceService.refund(balanceDTO));
    }

    @Override
    public CommonResult<String> orderPay(MemBranchBalanceDTO balanceDTO) {
        return CommonResult.success(accBalanceService.orderPay(balanceDTO));
    }

    @Override
    public CommonResult<String> OrderPayCancel(MemBranchBalanceDTO balanceDTO) {
        return CommonResult.success(accBalanceService.OrderPayCancel(balanceDTO));
    }

}
