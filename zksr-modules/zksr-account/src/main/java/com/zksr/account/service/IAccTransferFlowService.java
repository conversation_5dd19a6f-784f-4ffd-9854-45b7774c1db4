package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccTransferFlow;
import com.zksr.account.controller.flow.vo.AccTransferFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccTransferFlowSaveReqVO;

/**
 * 支付平台转账流水Service接口
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
public interface IAccTransferFlowService {

    /**
     * 新增支付平台转账流水
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAccTransferFlow(AccTransferFlow createReqVO);

    /**
     * 修改支付平台转账流水
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccTransferFlow(@Valid AccTransferFlow updateReqVO);

    /**
     * 删除支付平台转账流水
     *
     * @param transferFlowId 转账流水id
     */
    public void deleteAccTransferFlow(Long transferFlowId);

    /**
     * 批量删除支付平台转账流水
     *
     * @param transferFlowIds 需要删除的支付平台转账流水主键集合
     * @return 结果
     */
    public void deleteAccTransferFlowByTransferFlowIds(Long[] transferFlowIds);

    /**
     * 获得支付平台转账流水
     *
     * @param transferFlowId 转账流水id
     * @return 支付平台转账流水
     */
    public AccTransferFlow getAccTransferFlow(Long transferFlowId);

    /**
     * 获得支付平台转账流水分页
     *
     * @param pageReqVO 分页查询
     * @return 支付平台转账流水分页
     */
    PageResult<AccTransferFlow> getAccTransferFlowPage(AccTransferFlowPageReqVO pageReqVO);

    /**
     * 获取转账流水
     * @param transferNo    转账单号
     * @return
     */
    AccTransferFlow getTransferFlowByTransferNo(String transferNo);

    /**
     * 创建流水
     * @param reqVO
     */
    void createTransferFlow(TransferSubmitReqVO reqVO);
}
