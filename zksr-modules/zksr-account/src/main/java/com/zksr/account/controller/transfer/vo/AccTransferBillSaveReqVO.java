package com.zksr.account.controller.transfer.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;


/**
 * 交易对账单对象 acc_transfer_bill
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@ApiModel("交易对账单 - acc_transfer_bill分页 Request VO")
public class AccTransferBillSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 交易账单ID */
    @ApiModelProperty(value = "退款笔数")
    private Long transferBillId;

    /** 平台商id */
    @ApiModelProperty(value = "退款笔数")
    private Long sysCode;

    /** 账单日期 */
    @Excel(name = "账单日期")
    @ApiModelProperty(value = "账单日期")
    private String billDate;

    /** 交易笔数（包含支付和退款） */
    @Excel(name = "交易笔数", readConverterExp = "包=含支付和退款")
    @ApiModelProperty(value = "交易笔数")
    private Integer transferNum;

    /** 商户总支付金额 */
    @Excel(name = "商户总支付金额")
    @ApiModelProperty(value = "商户总支付金额")
    private BigDecimal merchantTotalPayAmt;

    /** 平台总支付金额 */
    @Excel(name = "平台总支付金额")
    @ApiModelProperty(value = "平台总支付金额")
    private BigDecimal platformTotalPayAmt;

    /** 商户总退款金额 */
    @Excel(name = "商户总退款金额")
    @ApiModelProperty(value = "商户总退款金额")
    private BigDecimal merchantTotalRefundAmt;

    /** 平台总退款金额 */
    @Excel(name = "平台总退款金额")
    @ApiModelProperty(value = "平台总退款金额")
    private BigDecimal platformTotalRefundAmt;

    /** 商户总支付手续费 */
    @Excel(name = "商户总支付手续费")
    @ApiModelProperty(value = "商户总支付手续费")
    private BigDecimal merchantTotalPayFree;

    /** 平台总支付手续费 */
    @Excel(name = "平台总支付手续费")
    @ApiModelProperty(value = "平台总支付手续费")
    private BigDecimal platformTotalPayFree;

    /** 商户总退款手续费 */
    @Excel(name = "商户总退款手续费")
    @ApiModelProperty(value = "商户总退款手续费")
    private BigDecimal merchantTotalRefundFree;

    /** 平台总退款手续费 */
    @Excel(name = "平台总退款手续费")
    @ApiModelProperty(value = "平台总退款手续费")
    private BigDecimal platformTotalRefundFree;

    /** 退款笔数 */
    @Excel(name = "退款笔数")
    @ApiModelProperty(value = "退款笔数")
    private Integer refundCount;

    /** 支付渠道, hlb-合利宝,wxb2b-微信b2b */
    @ApiModelProperty(value = "退款笔数")
    private String platform;

}
