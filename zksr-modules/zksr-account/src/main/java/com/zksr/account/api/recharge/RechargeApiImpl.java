package com.zksr.account.api.recharge;

import com.zksr.account.api.recharge.dto.RechargeSchemeDTO;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveReqVO;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveRespVO;
import com.zksr.account.service.IAccRechargeSchemeService;
import com.zksr.account.service.IAccRechargeService;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/11 15:00
 */
@RestController
@InnerAuth
@ApiIgnore
public class RechargeApiImpl implements RechargeApi{

    @Autowired
    private IAccRechargeSchemeService accRechargeSchemeService;

    @Autowired
    private IAccRechargeService accRechargeService;

    @Override
    public CommonResult<List<RechargeSchemeDTO>> getAreaRechargeSchemeList(Long areaId) {
        return success(accRechargeSchemeService.getAreaRechargeSchemeList(areaId));
    }

    @Override
    public CommonResult<BranchRechargeSaveRespVO> createBranchRecharge(BranchRechargeSaveReqVO reqVO) {
        return success(new BranchRechargeSaveRespVO(accRechargeService.insertBranchAccRecharge(reqVO)));
    }
}
