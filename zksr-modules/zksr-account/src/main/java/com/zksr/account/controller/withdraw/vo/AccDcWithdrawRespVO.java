package com.zksr.account.controller.withdraw.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
 * 账户提现单对象 acc_withdraw
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Data
@ApiModel("运营商账户提现单列表")
public class AccDcWithdrawRespVO extends AccWithdrawRespVO{

    @ApiModelProperty("运营商名称")
    private String dcName;
}
