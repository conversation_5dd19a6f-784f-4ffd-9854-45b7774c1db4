package com.zksr.account.client.mideapay.vo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayRiskReqVO {
    @JsonProperty("ip")
    private String ip;
    @JsonProperty("mac")
    private String mac;
    @JsonProperty("imei")
    private String imei;
    @JsonProperty("device_version")
    private String deviceVersion;
    @JsonProperty("cpu_info")
    private String cpuInfo;
    @JsonProperty("longitude")
    private String longitude;
    @JsonProperty("latitude")
    private String latitude;
    @JsonProperty("imsi")
    private String imsi;
    @JsonProperty("iccid")
    private String iccid;
    @JsonProperty("wifi_mac")
    private String wifiMac;
}
