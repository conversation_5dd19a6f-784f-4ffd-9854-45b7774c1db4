package com.zksr.account.api.account;

import cn.hutool.core.bean.BeanUtil;
import com.zksr.account.api.account.vo.BranchBalanceRespVO;
import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.controller.account.vo.AccAccountPageReqVO;
import com.zksr.account.convert.account.AccountConvert;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.account.dto.AccPlatformAccountDTO;
import com.zksr.account.service.IAccAccountFlowService;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.PayConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.NOT_EXIST_ACCOUNT;
import static com.zksr.common.core.web.pojo.PageParam.PAGE_SIZE_NONE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 账户api
 * @date 2024/3/23 10:55
 */
@InnerAuth
@ApiIgnore
@RestController
@SuppressWarnings("all")
public class AccountApiImpl implements AccountApi {

    @Autowired
    private IAccAccountService accountService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Autowired
    private IAccAccountFlowService accountFlowService;

    @Autowired
    private PartnerConfigApi partnerConfigApi;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Override
    public CommonResult<AccAccountDTO> getSupplierAccount(@RequestParam("supplierId") Long supplierId) {
        return CommonResult.success( accountService.getSupplierAccount(supplierId) );
    }

    @Override
    public CommonResult<AccAccountDTO> getAccountAndPlatformAccount(Long sysCode, Long merchantId, String merchantType, Long holdMerchantId) {
        // 获取当前平台配置platform
        PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfigDto)) {
            // 大区支付配置不存
            return CommonResult.success(null);
        }
        // 获取账户
        AccAccount account = accountService.getAccount(sysCode, merchantId, merchantType, holdMerchantId, payConfigDto.getInteriorStoredPayPlatform());
        if (Objects.isNull(account)) {
            return CommonResult.error(NOT_EXIST_ACCOUNT);
        }
        AccAccountDTO accountDTO = new AccAccountDTO();
        BeanUtil.copyProperties(account, accountDTO);
        // 获取入驻商的平台进件账户
        AccPlatformMerchant merchant = platformMerchantService.getPlatformMerchant(merchantId, merchantType, account.getPlatform());
        if (Objects.nonNull(merchant)) {
            // 设置平台进件商户
            accountDTO.setPlatformAccount(new AccPlatformAccountDTO(merchant.getAltMchNo(), merchant.getAltMchName()));
        }
        if (account.getPlatform().equals(PayChannelEnum.MOCK.getCode())) {
            // 如果是模拟支付则返回一个假的进件号, 因为模拟支付不需要进件号
            accountDTO.setPlatformAccount(new AccPlatformAccountDTO(PayConstants.DEFAULT_MOCK_NO, PayConstants.DEFAULT_MOCK_NAME));
        }
        return CommonResult.success(accountDTO);
    }

    @Override
    public CommonResult<List<AccAccountDTO>> getAccountList(Long sysCode, Long merchantId, String merchantType) {
        // 获取当前平台配置platform
        PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfigDto)) {
            // 大区支付配置不存
            return CommonResult.success(null);
        }
        // 获取账户
        List<AccAccount> accAccounts = accountService.getAccountListByMerchantIdAndType(merchantId, merchantType, payConfigDto.getInteriorStoredPayPlatform());
        return CommonResult.success(AccountConvert.INSTANCE.convert(accAccounts));
    }

    @Override
    public CommonResult<AccAccountDTO> getAccount(Long sysCode, Long merchantId, String merchantType) {
        // 获取当前平台配置platform
        PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfigDto)) {
            // 大区支付配置不存
            return CommonResult.success(null);
        }
        // 获取账户
        List<AccAccount> accAccounts = accountService.getAccountListByMerchantIdAndType(merchantId, merchantType, payConfigDto.getInteriorStoredPayPlatform());
        if (accAccounts.isEmpty()) {
            return CommonResult.success(null);
        }
        return CommonResult.success(AccountConvert.INSTANCE.convert(accAccounts.get(0)));
    }

    @Override
    public CommonResult<AccAccountDTO> getAccount(Long merchantId, String merchantType, String platform) {
        List<AccAccount> accAccounts = accountService.getAccountListByMerchantIdAndType(merchantId, merchantType, platform);
        if (accAccounts.isEmpty()) {
            return CommonResult.success(null);
        }
        return CommonResult.success(AccountConvert.INSTANCE.convert(accAccounts.get(0)));
    }


    @Override
    public CommonResult<List<Long>> saveAccountFlow(@RequestBody List<AccAccountFlowDTO> flowList) {
        return CommonResult.success(accountFlowService.insertAccAccountFlow(flowList));
    }

    @Override
    public CommonResult<List<Long>> saveAccountFlowAndProcess(List<AccAccountFlowDTO> flowList) {
        return CommonResult.success(accountFlowService.saveAccountFlowAndProcess(flowList));
    }

    @Override
    public CommonResult<List<Long>> saveAccountFlowAndProcessSync(List<AccAccountFlowDTO> flowList) {
        return CommonResult.success(accountFlowService.saveAccountFlowAndProcessSync(flowList));
    }

    @Override
    public CommonResult<Boolean> processFlow(@RequestBody List<Long> flowIdList) {
        accountFlowService.processFlowByIds(flowIdList);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> processFlowSync(List<Long> flowIdList) throws Exception {
        accountFlowService.processFlowByIdsSync(flowIdList);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<ColonelAccountRespVO> getColonelAccount(Long colonelId) {
        return CommonResult.success(accountService.getColonelAccount(colonelId));
    }

    @Override
    public CommonResult<List<AccAccountDTO>> getAccountListByReq(AccAccountDTO accountDTO) {
        AccAccountPageReqVO accountPageReqVO = AccountConvert.INSTANCE.convertPageReqVO(accountDTO);
        accountPageReqVO.setPageSize(PAGE_SIZE_NONE);
        return CommonResult.success(
                AccountConvert.INSTANCE.convert(
                    accountService.getAccAccountPage(accountPageReqVO).getList()
                )
        );
    }

    @Override
    public CommonResult<BranchBalanceRespVO> getBranchBalance(Long branchId) {
        BranchBalanceRespVO respVO = new BranchBalanceRespVO();
        {
            // 储值余额
            AccAccount account = accountService.getAccountByMerchantIdAndType(branchId, MerchantTypeEnum.BRANCH.getType(), PayChannelEnum.WALLET.getCode(), NumberPool.INT_ZERO);
            if (Objects.nonNull(account)) {
                respVO.setBalance(account.getValidAccountAmt());
            }
        }
        {
            // 赠送余额
            AccAccount account = accountService.getAccountByMerchantIdAndType(branchId, MerchantTypeEnum.BRANCH.getType(), PayChannelEnum.WALLET.getCode(), NumberPool.INT_ONE);
            if (Objects.nonNull(account)) {
                respVO.setGiveBalance(account.getValidAccountAmt());
            }
        }
        return CommonResult.success(respVO);
    }
    
    @Override
    public CommonResult<Boolean> checkOrderHasPay(List<String> supplierOrderNos) {
        return CommonResult.success(accountService.checkOrderHasPay(supplierOrderNos));
    }
}
