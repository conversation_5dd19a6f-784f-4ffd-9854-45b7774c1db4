package com.zksr.account.controller.withdraw;

import javax.validation.Valid;

import com.zksr.account.controller.withdraw.vo.AccWithdrawBillPageReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawBillRespVO;
import com.zksr.account.convert.withdraw.AccWithdrawBillConvert;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccWithdrawBill;
import com.zksr.account.service.IAccWithdrawBillService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 提现对账单Controller
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Api(tags = "管理后台 - 提现对账单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/withdraw")
public class AccWithdrawBillController {
    @Autowired
    private IAccWithdrawBillService accWithdrawBillService;

    /**
     * 分页查询提现对账单
     */
    @GetMapping("/getAccWithdrawBillList")
    @ApiOperation(value = "获得提现对账单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccWithdrawBillRespVO>> getPage(@Valid AccWithdrawBillPageReqVO pageReqVO) {
        PageResult<AccWithdrawBill> pageResult = accWithdrawBillService.getAccWithdrawBillPage(pageReqVO);
        return success(AccWithdrawBillConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 列表 */
        public static final String LIST = "account:withdraw:getAccWithdrawBillList";
    }
}
