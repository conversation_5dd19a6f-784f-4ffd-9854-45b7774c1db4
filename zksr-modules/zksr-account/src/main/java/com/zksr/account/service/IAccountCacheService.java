package com.zksr.account.service;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.partnerConfig.dto.*;
import com.zksr.system.api.partnerPolicy.dto.DcOtherSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.PartnerMiniSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.WithdrawalSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;

/**
 * <AUTHOR>
 * @time 2024/3/30
 * @desc 账户模块缓存
 */
public interface IAccountCacheService {

    /**
     * @param sysCode   平台ID
     * @return  根据sysCode 获取小程序配置
     */
    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode);

    /**
     * @param sysCode   平台ID
     * @return  根据sysCode 获取支付配置
     */
    public PayConfigDTO getPayConfigDTO(Long sysCode);

    /**
     * @param sysCode   平台ID
     * @return  根据sysCode 获取平台商户配置
     */
    public PayAccountConfigDTO getPayAccountConfigDTO(Long sysCode);

    /**
     * @param supplierId    入驻商ID
     * @return  入驻商缓存对象
     */
    public SupplierDTO getSupplierDTO(Long supplierId);

    /**
     * @param dcId    运营商ID
     * @return   运营商缓存对象
     */
    public DcDTO getDcDTO(Long dcId);

    /**
     * @param colonelId 业务员ID
     * @return   业务员缓存对象
     */
    public ColonelDTO getColonelDTO(Long colonelId);

    /**
     * 获取平台提现配置
     * @param sysCode   平台商编号
     * @return
     */
    public WithdrawalSettingPolicyDTO getWithdrawalSetting(Long sysCode);

    /**
     * 获取合利宝配置
     * @param sysCode
     * @return
     */
    HeLiBaoPayConfigDTO getHeLiBaoConfig(Long sysCode);

    /**
     * 获取美的付配置
     * @param sysCode
     * @return
     */
    MideaPayConfigDTO getMideaPayConfig(Long sysCode);

    /**
     * 获取微信b2b支付配置
     * @param sysCode
     * @return
     */
    WxB2bPayConfigDTO getWxB2BPayConfigDTO(Long sysCode);

    /**
     * 获取销售城市
     * @param areaId    城市ID
     * @return  销售城市
     */
    AreaDTO getAreaDTO(Long areaId);

    /**
     * 获取门店
     * @param branchId  门店ID
     * @return  门店
     */
    BranchDTO getBranchDTO(Long branchId);

    /**
     * 获取运营商其他配置
     * @param dcId  运营商ID
     * @return  运营商其他配置
     */
    DcOtherSettingPolicyDTO getDcOtherSettingPolicy(Long dcId);

    /**
     * 获取平台商商城小程序配置
     * @param sysCode 平台商编号
     * @return
     */
    PartnerMiniSettingPolicyDTO getPartnerMiniSettingPolicy(Long sysCode);
}
