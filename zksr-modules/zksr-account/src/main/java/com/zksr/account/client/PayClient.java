package com.zksr.account.client;


import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderQueryVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundQueryVO;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.system.api.model.dto.PayConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * 支付客户端，用于对接各支付渠道的 SDK，实现发起支付、退款等功能
 *
 * <AUTHOR>
 */
public interface PayClient {
    /**
     * 获取具体支付方式
     * @return 支付平台 | joinpay | hlb | joinpay_h5
     */
    String getChannel();

    /**
     * 获取支付平台标识
     * @return
     */
    String getPlatform();

    /**
     * 设置配置
     * @param config
     */
    void setConfig(PayConfigDTO config, String appid) throws Exception;

    // ============ 支付相关 ==========

    /**
     * 调用支付渠道，统一下单
     *
     * @param payOrderDTO 下单信息
     * @return 支付订单信息
     */
    PayOrderRespDTO unifiedOrder(PayOrderDTO payOrderDTO);

    /**
     * 解析 order 回调数据
     *
     * @param params HTTP 回调接口 content type 为 application/x-www-form-urlencoded 的所有参数
     * @param body HTTP 回调接口的 request body
     * @return 支付订单信息
     */
    PayOrderRespDTO parseOrderNotify(Map<String, String> params, String body);

    /**
     * 获得支付订单信息
     * @param payOrderQueryVO 支付订单信息
     * @return 支付订单信息
     */
    default PayOrderRespDTO getOrder(PayOrderQueryVO payOrderQueryVO) {
        return null;
    }

    // ============ 退款相关 ==========

    /**
     * 调用支付渠道，进行退款
     *
     * @param reqDTO  统一退款请求信息
     * @return 退款信息
     */
    PayRefundRespDTO unifiedRefund(PayRefundOrderSubmitReqVO reqDTO);

    /**
     * 解析 refund 回调数据
     *
     * @param params HTTP 回调接口 content type 为 application/x-www-form-urlencoded 的所有参数
     * @param body HTTP 回调接口的 request body
     * @return 支付订单信息
     */
    PayRefundRespDTO parseRefundNotify(Map<String, String> params, String body);

    /**
     * 获得退款订单信息
     * @param refundQueryVO 查询退款信息
     * @return 退款订单信息
     */
    default PayRefundRespDTO getRefund(PayRefundQueryVO refundQueryVO) {
        return null;
    }

    /**
     * 获取手续费
     *
     * @return
     */
    default BigDecimal getFeeRate() {
        return BigDecimal.ZERO;
    }


    @ApiModel(description = "支付客户端描述")
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ClientParam {

        @ApiModelProperty("应用appid")
        private String appid;

        @ApiModelProperty("平台商编号")
        private Long sysCode;

        @ApiModelProperty("支付方式")
        private String payWay;

        @ApiModelProperty("支付平台")
        private String platform;

        /**
         * 参加 {@link OrderTypeConstants}
         */
        @ApiModelProperty("订单类型")
        private Integer orderType;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ClientParam that = (ClientParam) o;
            return Objects.equals(appid, that.appid) && Objects.equals(sysCode, that.sysCode) && Objects.equals(payWay, that.payWay) && Objects.equals(platform, that.platform) && Objects.equals(orderType, that.orderType);
        }

        @Override
        public int hashCode() {
            return Objects.hash(appid, sysCode, payWay, platform, orderType);
        }
    }
}
