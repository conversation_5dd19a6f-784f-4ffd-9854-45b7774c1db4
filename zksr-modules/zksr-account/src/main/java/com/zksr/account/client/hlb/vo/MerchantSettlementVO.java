package com.zksr.account.client.hlb.vo;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableSet;
import com.zksr.common.core.enums.SignatureType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 交易结算 提现实体
 * @date 2024/3/11 16:45
 */
@Data
@Accessors(chain = true)
public class MerchantSettlementVO {

    @ApiModelProperty(value = "交易类型", notes = "MerchantSettlement")
    private String P1_bizType = "MerchantSettlement";

    @ApiModelProperty(value = "商户订单号", notes = "商户系统内部订单号，要求40字符以内，同一商户号下订单号唯一")
    private String P2_orderId;

    @ApiModelProperty(value = "商户商编", notes = "合利宝分配商户号", example = "C1800000002")
    private String P3_customerNumber;

    @ApiModelProperty(value = "金额", notes = "金额单位为元，最少值5", example = "0.01")
    private Double P4_amount;

    @ApiModelProperty(value = "结算备注", notes = "结算备注", example = "结算备注")
    private String P5_summary;

    @ApiModelProperty(value = "签名类型", notes = "签名类型")
    private String P6_notifyUrl;

    @ApiModelProperty(value = "签名类型", notes = "签名类型")
    private String signType = "SM3WITHSM2";

    /**
     * 签名类型(不参与签名)
     */
    private SignatureType signatureType = SignatureType.SM3WITHSM2;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of(
            "P1_bizType",
            "P2_orderId",
            "P3_customerNumber",
            "P4_amount",
            "P5_summary",
            "signType"
    );
}
