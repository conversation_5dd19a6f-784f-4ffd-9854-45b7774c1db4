package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.zksr.common.core.pool.StringPool;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 后台导入充值对象 acc_recharge_import
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@TableName(value = "acc_recharge_import")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccRechargeImport extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeImportId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 0-未删除,1-已删除 */
    @TableLogic(value = StringPool.ZERO, delval = StringPool.ONE)
    @Excel(name = "0-未删除,1-已删除")
    private Integer deleted;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 0-初始化, 1-审核成功, 2-作废 */
    @Excel(name = "0-初始化, 1-审核成功, 2-作废")
    private Integer rechargeImportState;

    /** 凭证 */
    @Excel(name = "凭证")
    private String voucher;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 计数 */
    @Excel(name = "计数")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long counter;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal rechargeAmt;

    public void setCounter(int size) {
        this.counter = Long.valueOf(size);
    }
}
