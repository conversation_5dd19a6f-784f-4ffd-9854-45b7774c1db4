package com.zksr.account.controller.transfer.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;


/**
 * 交易对账单对象 acc_transfer_bill
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@ApiModel("交易对账单 - acc_transfer_bill分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccTransferBillPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商ID */
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    /** 账单日期开始 */
    @Excel(name = "账单日期开始")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "账单日期开始")
    private String billStartDate;

    /** 账单日期结束 */
    @Excel(name = "账单日期结束")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "账单日期结束")
    private String billEndDate;

    /** 支付渠道, hlb-合利宝,wxb2b-微信b2b */
    @ApiModelProperty(value = "支付渠道")
    private String platform;
}
