package com.zksr.account.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowSaveReqVO;
import com.zksr.account.convert.flow.AccWithdrawFlowConvert;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.domain.AccWithdrawFlow;
import com.zksr.account.mapper.AccWithdrawFlowMapper;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccWithdrawFlowService;
import com.zksr.common.core.business.TransferBusiType;
import com.zksr.common.core.constant.PayConstants;
import com.zksr.common.core.constant.SystemConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.WithdrawStateEnum;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.NOT_EXIST_MERCHANT_ACCOUNT_CONFIG;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 支付平台提现流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Service
public class AccWithdrawFlowServiceImpl implements IAccWithdrawFlowService {

    @Autowired
    private AccWithdrawFlowMapper accWithdrawFlowMapper;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    /**
     * 获得支付平台提现流水
     *
     * @param withdrawFlowId 提现流水id
     * @return 支付平台提现流水
     */
    @Override
    public AccWithdrawFlow getAccWithdrawFlow(Long withdrawFlowId) {
        return accWithdrawFlowMapper.selectById(withdrawFlowId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccWithdrawFlow> getAccWithdrawFlowPage(AccWithdrawFlowPageReqVO pageReqVO) {
        return accWithdrawFlowMapper.selectPage(pageReqVO);
    }

    @Override
    public AccWithdrawFlow createFlow(TransferSettleReqVO reqVO) {
        AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(reqVO.getMerchantId(), reqVO.getMerchantType(), reqVO.getPlatform());
        if (PayChannelEnum.MOCK.getCode().equals(reqVO.getPlatform())) {
            platformMerchant = AccPlatformMerchant.getMockAccount();
        }
        if (Objects.isNull(platformMerchant)) {
            throw exception(NOT_EXIST_MERCHANT_ACCOUNT_CONFIG);
        }
        AccWithdrawFlow withdrawFlow = new AccWithdrawFlow();
        withdrawFlow.setSysCode(reqVO.getSysCode())
                    .setBusiId(reqVO.getBusiId())
                    .setBusiType(TransferBusiType.WITHDRAW.getType())
                    .setPlatform(reqVO.getPlatform())
                    .setMerchantId(reqVO.getMerchantId())
                    .setMerchantType(reqVO.getMerchantType())
                    .setAltMchNo(platformMerchant.getAltMchNo())
                    .setAltMchName(platformMerchant.getAltMchName())
                    .setWithdrawNo(reqVO.getWithdrawNo())
                    .setWithdrawAmt(reqVO.getAmount())
                    .setState(WithdrawStateEnum.INIT.getState())
                    .setInitTime(DateUtil.date())
                    .setBankAccountNo(platformMerchant.getAccountNo())
                    .setBankAccountName(platformMerchant.getBankName() + platformMerchant.getBankBranch())
                    .setRemitAmt(reqVO.getAmount())
        ;
        if (Objects.nonNull(SecurityContextHolder.hasSysCode())) {
            withdrawFlow.setSysCode(null);
        }
        accWithdrawFlowMapper.insert(withdrawFlow);
        return withdrawFlow;
    }

    @Override
    public void updateWithdrawFlow(AccWithdrawFlow updateFlow) {
        accWithdrawFlowMapper.updateById(updateFlow);
    }

    @Override
    public AccWithdrawFlow getAccWithdrawFlow(String withdrawNo) {
        return accWithdrawFlowMapper.selectFlowByWithdrawNo(withdrawNo);
    }


}
