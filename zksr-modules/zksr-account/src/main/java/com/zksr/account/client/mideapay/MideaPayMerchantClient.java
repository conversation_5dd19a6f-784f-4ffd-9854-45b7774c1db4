package com.zksr.account.client.mideapay;

import cn.hutool.core.util.RandomUtil;
import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.client.MerchantClient;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import com.zksr.common.core.enums.MerchantUploadPicStateEnum;
import com.zksr.system.api.model.dto.MideaPayConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Desciption: 美的付商户查询信息
 */
@Data
@Slf4j
public class MideaPayMerchantClient implements MerchantClient {

    // 对接美的付
    private MideaPaySdkClient client;

    // 支付配置
    private MideaPayConfig payConfig;

    public MideaPayMerchantClient(MideaPayConfig config) {
        this.payConfig = config;
        this.client = new MideaPaySdkClient();
        this.client.setPayConfig(payConfig);
    }
    @Override
    public PayPlatformAccountVO getAccountBalance(String merchantNo) {
        return client.queryBalance(merchantNo);
    }

    @Override
    public PayPlatformMerchantVO getRegisterMerchant(String orderNo, String merchantNo) {
        log.warn("{}美的付暂不支持查询商户进件信息.",merchantNo);
        PayPlatformMerchantVO merchantVO = new PayPlatformMerchantVO();
        merchantVO.setState(MerchantRegisterStateEnum.AUDITED);
        //TODO 坑爹 这里先给个 - 1
        merchantVO.setMerchantNo("-1");
        return merchantVO;
    }

    @Override
    public PlatformMerchantRegisterSaveRespVO registerMerchant(AccPlatformMerchantRegisterReqVO saveReqVO) {
        return PlatformMerchantRegisterSaveRespVO
                .builder()
                .auditStatus(MerchantRegisterStateEnum.AUDITED.getState())
                .merchantNo(saveReqVO.getPlatformMerchantRegisterSaveReqVO().getAltMchNo())
                .orderNo(RandomUtil.randomNumbers(9))
                .build();
    }

    @Override
    public PlatformMerchantUploadSaveRespVO uploadPic(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) {
        return PlatformMerchantUploadSaveRespVO
                .builder()
                .auditStatus(MerchantUploadPicStateEnum.SUCCESS.getState())
                .merchantNo(uploadSaveReqVO.getAltMchNo())
                .orderNo(RandomUtil.randomNumbers(9))
                .build();
    }

    @Override
    public PlatformMerchantUploadSaveRespVO changePic(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) {
        return PlatformMerchantUploadSaveRespVO
                .builder()
                .auditStatus(MerchantUploadPicStateEnum.SUCCESS.getState())
                .merchantNo(uploadSaveReqVO.getAltMchNo())
                .orderNo(RandomUtil.randomNumbers(9))
                .build();
    }

    @Override
    public PlatformMerchantRegisterSaveRespVO updateMerchant(PlatformMerchantRegisterSaveReqVO saveReqVO, AccPlatformMerchant platformMerchant) {
        return PlatformMerchantRegisterSaveRespVO
                .builder()
                .auditStatus(MerchantRegisterStateEnum.AUDITED.getState())
                .merchantNo(saveReqVO.getAltMchNo())
                .orderNo(RandomUtil.randomNumbers(9))
                .build();
    }

    @Override
    public PlatformMerchantUploadSaveRespVO queryUploadPicStatus(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) {
        return PlatformMerchantUploadSaveRespVO
                .builder()
                .auditStatus(MerchantUploadPicStateEnum.SUCCESS.getState())
                .merchantNo(uploadSaveReqVO.getAltMchNo())
                .orderNo(RandomUtil.randomNumbers(9))
                .build();
    }

    @Override
    public PlatformMerchantUpdateStatusRespVO queryUploadMerchantStatus(String merchantNo, String editOrderNo) {
        return PlatformMerchantUpdateStatusRespVO
                .builder()
                .auditStatus(MerchantRegisterStateEnum.AUDITED.getState())
                .merchantNo(merchantNo)
                .orderNo(editOrderNo)
                .build();
    }
}
