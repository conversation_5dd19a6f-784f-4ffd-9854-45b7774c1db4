package com.zksr.account.controller.merchant.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信B2B支付商户配置
 * @date 2024/9/24 14:58
 */
@Data
@ApiModel(description = "微信B2B支付商户配置")
public class AccPlatformMerchantWxb2bInfoReqVO {

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty("商户类型")
    private String merchantType;

    /**
     * 支付平台信息
     */
    @ApiModelProperty("支付平台信息: hlb-合利宝, mideaPay-美的支付, mock-模拟支付")
    private String platform;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty("商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;
}
