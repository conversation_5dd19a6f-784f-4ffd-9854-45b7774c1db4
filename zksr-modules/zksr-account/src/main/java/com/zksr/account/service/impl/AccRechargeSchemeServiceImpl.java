package com.zksr.account.service.impl;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import com.github.pagehelper.Page;
import com.zksr.account.api.recharge.dto.RechargeSchemeDTO;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemePageReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemeRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemeSaveReqVO;
import com.zksr.account.api.recharge.vo.RechargeSchemeContentVO;
import com.zksr.account.convert.recharge.AccRechargeSchemeConvert;
import com.zksr.account.domain.AccRechargeScheme;
import com.zksr.account.mapper.AccRechargeSchemeMapper;
import com.zksr.account.service.IAccRechargeSchemeAreaService;
import com.zksr.account.service.IAccRechargeSchemeService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.utils.DcUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.area.dto.AreaDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.RECHARGE_AREA_TIME_CONFLICT;
import static com.zksr.account.enums.ErrorCodeConstants.RECHARGE_INVALID;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 储值充值套餐配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Service
public class AccRechargeSchemeServiceImpl implements IAccRechargeSchemeService {

    @Autowired
    private AccRechargeSchemeMapper accRechargeSchemeMapper;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccRechargeSchemeAreaService schemeAreaService;

    /**
     * 新增储值充值套餐配置
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    public Long insertAccRechargeScheme(AccRechargeSchemeSaveReqVO createReqVO) {
        // 排序调整
        createReqVO.getRuleJson().sort(Comparator.comparing(RechargeSchemeContentVO::getRechargeAmt));
        AccRechargeScheme accRechargeScheme = AccRechargeSchemeConvert.INSTANCE.convert(createReqVO);
        // 验证数据
        validate(accRechargeScheme);
        // 插入
        accRechargeSchemeMapper.insert(accRechargeScheme);
        schemeAreaService.saveSchemeArea(accRechargeScheme.getRechargeSchemeId(), createReqVO.getAreaIds());
        // 返回
        return accRechargeScheme.getRechargeSchemeId();
    }

    /**
     * 修改储值充值套餐配置
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAccRechargeScheme(AccRechargeSchemeSaveReqVO updateReqVO) {
        // 排序调整
        updateReqVO.getRuleJson().sort(Comparator.comparing(RechargeSchemeContentVO::getRechargeAmt));
        AccRechargeScheme rechargeScheme = AccRechargeSchemeConvert.INSTANCE.convert(updateReqVO);
        // 验证数据
        validate(rechargeScheme);
        // 保存上架城市关系
        schemeAreaService.saveSchemeArea(updateReqVO.getRechargeSchemeId(), updateReqVO.getAreaIds());
        accRechargeSchemeMapper.updateById(rechargeScheme);
    }

    /**
     * 批量删除储值充值套餐配置
     *
     * @param rechargeSchemeIds 需要删除的储值充值套餐配置主键
     * @return 结果
     */
    @Override
    public void deleteAccRechargeSchemeByRechargeSchemeIds(Long[] rechargeSchemeIds) {
        for(Long rechargeSchemeId : rechargeSchemeIds){
            // @TODO: 严禁直接删除数据库, 所有的删除都需要兼容业务做逻辑删除
            // this.deleteAccRechargeScheme(rechargeSchemeId);
        }
    }

    /**
     * 获得储值充值套餐配置
     *
     * @param rechargeSchemeId 充值方案id
     * @return 储值充值套餐配置
     */
    @Override
    public AccRechargeScheme getAccRechargeScheme(Long rechargeSchemeId) {
        return accRechargeSchemeMapper.selectById(rechargeSchemeId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccRechargeSchemeRespVO> getAccRechargeSchemePage(AccRechargeSchemePageReqVO pageReqVO) {
        // 运营商身份兼容
        Long dcId = SecurityUtils.getLoginUser().getDcId();
        if (Objects.nonNull(dcId)) {
            pageReqVO.setAreaIdList(DcUtils.getAreaList(dcId));
        }
        // 分页查询数据
        Page<AccRechargeSchemeRespVO> page = PageUtils.startPage(pageReqVO);
        List<AccRechargeSchemeRespVO> list = AccRechargeSchemeConvert.INSTANCE.convertRespVOList(accRechargeSchemeMapper.selectPage(pageReqVO));
        LFUCache<Long, AreaDTO> areaCache = CacheUtil.newLFUCache(0);
        list.forEach(rechargeScheme -> {
            if (StringUtils.isNotEmpty(rechargeScheme.getAreaIds())) {
                List<Long> areaIds = Arrays.stream(rechargeScheme.getAreaIds().split(StringPool.COMMA)).map(Long::parseLong).collect(Collectors.toList());
                List<String> areaNames = areaIds.stream()
                        .map(areaId -> areaCache.get(areaId, () -> accountCacheService.getAreaDTO(areaId)))
                        .filter(Objects::nonNull)
                        .map(AreaDTO::getAreaName)
                        .collect(Collectors.toList());
                rechargeScheme.setAreaNames(areaNames);
            }
            // 一旦过期, 管你有没有停用, 直接标记为停用
            if (rechargeScheme.getEndTime().getTime() < System.currentTimeMillis()) {
                rechargeScheme.setStatus(NumberPool.INT_ONE);
            }
        });
        return PageResult.result(page, list);
    }

    /**
     * 停用充值配置
     * @param rechargeSchemeId  配置ID
     */
    @Override
    public void disable(Long rechargeSchemeId) {
        AccRechargeScheme rechargeScheme = accRechargeSchemeMapper.selectById(rechargeSchemeId);
        rechargeScheme.setStatus(NumberPool.INT_ONE);
        accRechargeSchemeMapper.updateById(rechargeScheme);
    }

    /**
     * 启用充值配置
     * @param rechargeSchemeId  配置ID
     */
    @Override
    public void enable(Long rechargeSchemeId) {
        AccRechargeScheme rechargeScheme = accRechargeSchemeMapper.selectById(rechargeSchemeId);
        // 验证活动时间
        if (rechargeScheme.getEndTime().getTime() < System.currentTimeMillis()) {
            throw exception(RECHARGE_INVALID);
        }
        // 验证是否有重复的数据
        validate(rechargeScheme);
        rechargeScheme.setStatus(NumberPool.INT_ZERO);
        accRechargeSchemeMapper.updateById(rechargeScheme);
    }

    @Override
    public List<RechargeSchemeDTO> getAreaRechargeSchemeList(Long areaId) {
        List<AccRechargeScheme> accRechargeSchemes = accRechargeSchemeMapper.selectValidAreaRechargeSchemeList(areaId);
        return AccRechargeSchemeConvert.INSTANCE.convertDTOList(accRechargeSchemes);
    }

    @Override
    public RechargeSchemeDTO getValidAreaRechargeScheme(Long areaId) {
        List<RechargeSchemeDTO> rechargeSchemeList = getAreaRechargeSchemeList(areaId);
        for (RechargeSchemeDTO schemeDTO : rechargeSchemeList) {
            if (schemeDTO.validate()) {
                return schemeDTO;
            }
        }
        return null;
    }


    private void validate(AccRechargeScheme rechargeScheme) {
        // 没有状态, 或者是停用的不验证
        if (Objects.isNull(rechargeScheme.getStatus()) || rechargeScheme.getStatus() == NumberPool.INT_ONE) {
            return;
        }
        List<Long> areaIds = Arrays.stream(rechargeScheme.getAreaIds().split(StringPool.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        for (Long areaId : areaIds) {
            // 有冲突的记录
            List<AccRechargeScheme> rechargeSchemes = accRechargeSchemeMapper.selectOverlappingEnabledEntries(areaId, rechargeScheme.getStartTime(), rechargeScheme.getEndTime());
            Map<Long, AccRechargeScheme> idMap = rechargeSchemes.stream().collect(Collectors.toMap(AccRechargeScheme::getRechargeSchemeId, item -> item));
            // 删除当前可能是当前记录
            idMap.remove(rechargeScheme.getRechargeSchemeId());
            if (!idMap.isEmpty()) {
                List<String> schemeNames = idMap.values().stream().map(AccRechargeScheme::getSchemeName).collect(Collectors.toList());
                throw exception(RECHARGE_AREA_TIME_CONFLICT, StringUtils.join(schemeNames, StringPool.COMMA));
            }
        }

    }
}
