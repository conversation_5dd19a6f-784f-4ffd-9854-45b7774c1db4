package com.zksr.account.client.hlb.vo;


import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableSet;
import com.zksr.common.core.enums.SignatureType;

import java.util.Set;

public class AppPayRefundOrderReqVO {


    private String P1_bizType;
    private String P2_orderId;
    private String P3_customerNumber;
    private String P4_refundOrderId;
    private String P5_amount;
    private String P6_callbackUrl;
    private String P7_desc;
    private String P8_orderSerialNumber;
    /**排除签名*/
    private String acqAddnData;
    /**排除签名*/
    private String ruleJson;
    /**签名类型(不参与签名)*/
    private SignatureType signatureType = SignatureType.MD5;
    /**sm4 key,当signatureType=SignatureType.SM3WITHSM2时用此加/解密敏感信息(不参与签名)*/
    private String encryptionKey;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of("P1_bizType", "P2_orderId",
            "P3_customerNumber", "P4_refundOrderId", "P5_amount", "P6_callbackUrl");

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("ruleJson");

    public String getP1_bizType() {
        return P1_bizType;
    }

    public void setP1_bizType(String p1_bizType) {
        P1_bizType = p1_bizType;
    }

    public String getP2_orderId() {
        return P2_orderId;
    }

    public void setP2_orderId(String p2_orderId) {
        P2_orderId = p2_orderId;
    }

    public String getP3_customerNumber() {
        return P3_customerNumber;
    }

    public void setP3_customerNumber(String p3_customerNumber) {
        P3_customerNumber = p3_customerNumber;
    }

    public String getP4_refundOrderId() {
        return P4_refundOrderId;
    }

    public void setP4_refundOrderId(String p4_refundOrderId) {
        P4_refundOrderId = p4_refundOrderId;
    }

    public String getP5_amount() {
        return P5_amount;
    }

    public void setP5_amount(String p5_amount) {
        P5_amount = p5_amount;
    }

    public String getP6_callbackUrl() {
        return P6_callbackUrl;
    }

    public void setP6_callbackUrl(String p6_callbackUrl) {
        P6_callbackUrl = p6_callbackUrl;
    }

    public String getP7_desc() {
        return P7_desc;
    }

    public void setP7_desc(String p7_desc) {
        P7_desc = p7_desc;
    }

    public String getP8_orderSerialNumber() {
        return P8_orderSerialNumber;
    }

    public void setP8_orderSerialNumber(String p8_orderSerialNumber) {
        P8_orderSerialNumber = p8_orderSerialNumber;
    }

    public String getAcqAddnData() {
        return acqAddnData;
    }

    public void setAcqAddnData(String acqAddnData) {
        this.acqAddnData = acqAddnData;
    }

    public String getRuleJson() {
        return ruleJson;
    }

    public void setRuleJson(String ruleJson) {
        this.ruleJson = ruleJson;
    }

    public SignatureType getSignatureType() {
        return signatureType;
    }

    public void setSignatureType(SignatureType signatureType) {
        this.signatureType = signatureType;
    }

    public String getEncryptionKey() {
        return encryptionKey;
    }

    public void setEncryptionKey(String encryptionKey) {
        this.encryptionKey = encryptionKey;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
