package com.zksr.account.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.account.controller.flow.vo.AccTransferFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccTransferFlowSaveReqVO;
import com.zksr.account.convert.flow.AccTransferFlowConvert;
import com.zksr.account.domain.AccTransferFlow;
import com.zksr.account.mapper.AccTransferFlowMapper;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.account.service.IAccTransferFlowService;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.TRANSFER_REPEAT;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 支付平台转账流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
@Service
public class AccTransferFlowServiceImpl implements IAccTransferFlowService {
    @Autowired
    private AccTransferFlowMapper accTransferFlowMapper;

    /**
     * 新增支付平台转账流水
     *
     * @param accTransferFlow 创建信息
     * @return 结果
     */
    @Override
    public Long insertAccTransferFlow(AccTransferFlow accTransferFlow) {
        // 插入
        accTransferFlowMapper.insert(accTransferFlow);
        // 返回
        return accTransferFlow.getTransferFlowId();
    }

    /**
     * 修改支付平台转账流水
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAccTransferFlow(AccTransferFlow updateReqVO) {
        accTransferFlowMapper.updateById(updateReqVO);
    }

    /**
     * 删除支付平台转账流水
     *
     * @param transferFlowId 转账流水id
     */
    @Override
    public void deleteAccTransferFlow(Long transferFlowId) {
        // 删除
        accTransferFlowMapper.deleteById(transferFlowId);
    }

    /**
     * 批量删除支付平台转账流水
     *
     * @param transferFlowIds 需要删除的支付平台转账流水主键
     * @return 结果
     */
    @Override
    public void deleteAccTransferFlowByTransferFlowIds(Long[] transferFlowIds) {
        for(Long transferFlowId : transferFlowIds){
            this.deleteAccTransferFlow(transferFlowId);
        }
    }

    /**
     * 获得支付平台转账流水
     *
     * @param transferFlowId 转账流水id
     * @return 支付平台转账流水
     */
    @Override
    public AccTransferFlow getAccTransferFlow(Long transferFlowId) {
        return accTransferFlowMapper.selectById(transferFlowId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccTransferFlow> getAccTransferFlowPage(AccTransferFlowPageReqVO pageReqVO) {
        return accTransferFlowMapper.selectPage(pageReqVO);
    }

    @Override
    public AccTransferFlow getTransferFlowByTransferNo(String transferNo) {
        return accTransferFlowMapper.selectTransferFlowByTransferNo(transferNo);
    }

    @Override
    public void createTransferFlow(TransferSubmitReqVO reqVO) {
        AccTransferFlow transferFlow = this.getTransferFlowByTransferNo(reqVO.getTransferNo());
        if (Objects.nonNull(transferFlow)) {
            throw exception(TRANSFER_REPEAT);
        } else {
            transferFlow = AccTransferFlowConvert.INSTANCE.convert(reqVO);
            transferFlow.setInitTime(DateUtil.date())
                    .setProcessingTime(DateUtil.date())
                    .setTransferFee(BigDecimal.ZERO)
            ;
        }
        this.insertAccTransferFlow(transferFlow);
    }

}
