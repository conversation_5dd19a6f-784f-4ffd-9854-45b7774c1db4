package com.zksr.account.service;

import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.balance.vo.AccBalanceRespVO;

import java.util.List;

/**
 * 门店账户余额接口
 */
public interface IAccBalanceService {

    // 跟据门店id列表批量获取门店账户余额数据
    List<AccBalanceRespVO> getBalanceInfoList(List<Long> branchIdList);

    // 门店充值
    String recharge(MemBranchBalanceDTO balanceDTO);

    // 门店退款
    String refund(MemBranchBalanceDTO balanceDTO);

    // 门店余额订单支付
    String orderPay(MemBranchBalanceDTO balanceDTO);

    // 回滚操作
    Boolean rollBackBalance(MemBranchBalanceDTO balanceDTO);

    // 订单取消，回退支付金额
    String OrderPayCancel(MemBranchBalanceDTO balanceDTO);
}
