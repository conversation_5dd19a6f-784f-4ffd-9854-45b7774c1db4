package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 支付分账详情对象 acc_divide_dtl
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@TableName(value = "acc_divide_dtl")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccDivideDtl extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 支付分账详情id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long divideDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 分账方商户编号 */
    @Excel(name = "分账方商户编号")
    private String altMchNo;

    /** 分账方名称 */
    @Excel(name = "分账方名称")
    private String altMchName;

    /** 订单金额 */
    @Excel(name = "订单金额")
    private BigDecimal orderAmt;

    /** 分账金额 */
    @Excel(name = "分账金额")
    private BigDecimal altAmt;

    /** 支付手续费率 */
    @Excel(name = "支付手续费率")
    private BigDecimal feeRate;

    /** 支付手续费 */
    @Excel(name = "支付手续费")
    private BigDecimal fee;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

    /** 支付流水id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long payFlowId;

    /** 0-线上分账  1-线下分账 */
    @Excel(name = "0-线上分账  1-线下分账")
    private Integer onlineOrOffline;

    /** 线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账 */
    @Excel(name = "线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账")
    private Integer onlineDivideState;

    /** 线下处理状态;0-未处理 1-已处理 2-无需处理 针对分账失败或者不分账的订单的补偿措施 */
    @Excel(name = "线下处理状态;0-未处理 1-已处理 2-无需处理 针对分账失败或者不分账的订单的补偿措施")
    private Integer offlineProState;

    /** 线下处理单号 */
    @Excel(name = "线下处理单号")
    private String offlineProNo;

    /** 分账完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "分账完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date divideTime;

    /** 支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号 */
    @Excel(name = "支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号")
    private String tradeNo;

    /** 子单号, 适用于一主多详情 */
    @Excel(name = "子单号, 适用于一主多详情")
    private String subTradeNo;

    /** 支付平台商户订单号 */
    @Excel(name = "支付平台商户订单号")
    private String outTradeNo;

    /** 退款单号;商城退款即是售后单号 */
    @Excel(name = "退款单号;商城退款即是售后单号")
    private String refundNo;

    /** 子单号, 适用于一主多详情 */
    @Excel(name = "子单号, 适用于一主多详情")
    private String subRefundNo;

    /** 支付平台商户退款单号 */
    @Excel(name = "支付平台商户退款单号")
    private String outRefundNo;

    /** 支付类型 0-支付 1-退款 */
    @Excel(name = "支付类型 0-支付 1-退款")
    private Integer payType;

    /** 第三方支付平台分账流水号 */
    @Excel(name = "第三方支付平台分账流水号")
    private String platformDivideFlowNo;

    /** 订单类型 0-商城订单  1-入驻商充值  2-门店充值 */
    @Excel(name = "订单类型 0-商城订单  1-入驻商充值  2-门店充值")
    private Integer orderType;

    /** B2B支付分账流水ID */
    @Excel(name = "B2B支付分账流水ID")
    @ApiModelProperty("B2B支付分账流水ID")
    private Long divideFlowId;

    /** 分账处理时间 */
    @Excel(name = "分账处理时间")
    @ApiModelProperty("分账处理时间")
    private Date processTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccDivideDtl divideDtl = (AccDivideDtl) o;
        return Objects.equals(divideDtlId, divideDtl.divideDtlId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(divideDtlId);
    }
}
