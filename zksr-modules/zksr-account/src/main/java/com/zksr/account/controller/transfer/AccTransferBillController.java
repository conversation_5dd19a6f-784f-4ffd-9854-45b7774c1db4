package com.zksr.account.controller.transfer;

import javax.validation.Valid;

import com.zksr.account.service.transfer.IAccTransferBillService;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccTransferBill;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.controller.transfer.vo.AccTransferBillPageReqVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillRespVO;
import com.zksr.account.convert.transfer.AccTransferBillConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易对账单Controller
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Api(tags = "管理后台 - 交易对账单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/transfer")
public class AccTransferBillController {
    @Autowired
    private IAccTransferBillService accTransferBillService;

    /**
     * 分页查询交易对账单
     */
    @GetMapping("/getAccTranList")
    @ApiOperation(value = "获得交易对账单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccTransferBillRespVO>> getPage(@Valid AccTransferBillPageReqVO pageReqVO) {
        PageResult<AccTransferBill> pageResult = accTransferBillService.getAccTransferBillPage(pageReqVO);
        return success(AccTransferBillConvert.INSTANCE.convertPage(pageResult));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 列表 */
        public static final String LIST = "account:transfer:getAccTranList";
    }
}
