package com.zksr.account.controller.withdraw;

import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.controller.withdraw.vo.*;
import com.zksr.account.convert.withdraw.AccWithdrawConvert;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 平台商账户提现单Controller
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Api(tags = "管理后台 - 平台商账户提现单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/withdraw-partner")
public class AccPartnerWithdrawController {

    @Autowired
    private IAccWithdrawService accWithdrawService;

    @Resource
    private PartnerApi partnerApi;

    /**
     * 新增平台商账户提现单
     */
    @ApiOperation(value = "新增平台商账户提现单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "平台商账户提现单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<TransferSubmitRespDTO> add(@Valid @RequestBody AccWithdrawSaveReqVO createReqVO) {
        // 设置账户类型为平台商
        createReqVO.setMerchantType(MerchantTypeEnum.PARTNER.getType());
        // 创建平台商提现单
        AccWithdraw withdraw = accWithdrawService.insertAccWithdraw(createReqVO);
        // 平台提现单直接进行处理转账
        return success(accWithdrawService.processWithdraw(withdraw));
    }
    /**
     * 获取平台商账户提现单详细信息
     */
    @ApiOperation(value = "获得平台商账户提现单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{withdrawId}")
    public CommonResult<AccWithdrawRespVO> getInfo(@PathVariable("withdrawId") Long withdrawId) {
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawId);
        return success(AccWithdrawConvert.INSTANCE.convert(accWithdraw));
    }

    /**
     * 分页查询平台商账户提现单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得平台商账户提现单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccPartnerWithdrawRespVO>> getPage(@Valid AccWithdrawPageReqVO pageReqVO) {
        pageReqVO.setMerchantType(MerchantTypeEnum.PARTNER.getType());
        PageResult<AccPartnerWithdrawRespVO> pageResult = AccWithdrawConvert.INSTANCE.convertPartnerPage(accWithdrawService.getAccWithdrawPage(pageReqVO));
        pageResult.getList().forEach(withdraw -> {
            PartnerDto partnerDto = partnerApi.getBySysCode(withdraw.getSysCode()).getCheckedData();
            withdraw.setPartnerName(partnerDto.getPartnerName());
        });
        return success(pageResult);
    }

    /**
     * 结算重试
     */
    @ApiOperation(value = "平台商提现单结算重试", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SETTLE)
    @RequiresPermissions(AccDcWithdrawController.Permissions.ENABLE)
    @Log(title = "平台商账户提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/settle")
    public CommonResult<Boolean> settle(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        // 获取提现单
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawAudit.getWithdrawId());
        // 平台提现单直接进行处理转账
        if (accWithdraw.getMerchantType().equals(MerchantTypeEnum.PARTNER.getType())) {
            // 平台提现单直接进行处理转账
            accWithdrawService.settle(accWithdraw);
        }
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:withdraw-partner:add";
        /** 编辑 */
        public static final String EDIT = "account:withdraw-partner:edit";
        /** 删除 */
        public static final String DELETE = "account:withdraw-partner:remove";
        /** 列表 */
        public static final String LIST = "account:withdraw-partner:list";
        /** 查询 */
        public static final String GET = "account:withdraw-partner:query";
        /** 停用 */
        public static final String DISABLE = "account:withdraw-partner:disable";
        /** 启用 */
        public static final String ENABLE = "account:withdraw-partner:enable";
        /** 结算重试 */
        public static final String SETTLE = "account:withdraw-partner:settle";
    }
}
