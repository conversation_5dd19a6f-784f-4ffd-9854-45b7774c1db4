package com.zksr.account.mapper;

import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccWithdrawFlow;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowPageReqVO;


/**
 * 支付平台提现流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Mapper
public interface AccWithdrawFlowMapper extends BaseMapperX<AccWithdrawFlow> {
    default PageResult<AccWithdrawFlow> selectPage(AccWithdrawFlowPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccWithdrawFlow>()
                    .eqIfPresent(AccWithdrawFlow::getWithdrawFlowId, reqVO.getWithdrawFlowId())
                    .eqIfPresent(AccWithdrawFlow::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccWithdrawFlow::getBusiType, reqVO.getBusiType())
                    .eqIfPresent(AccWithdrawFlow::getBusiId, reqVO.getBusiId())
                    .eqIfPresent(AccWithdrawFlow::getPlatform, reqVO.getPlatform())
                    .eqIfPresent(AccWithdrawFlow::getMerchantType, reqVO.getMerchantType())
                    .eqIfPresent(AccWithdrawFlow::getMerchantId, reqVO.getMerchantId())
                    .eqIfPresent(AccWithdrawFlow::getAltMchNo, reqVO.getAltMchNo())
                    .likeIfPresent(AccWithdrawFlow::getAltMchName, reqVO.getAltMchName())
                    .eqIfPresent(AccWithdrawFlow::getWithdrawNo, reqVO.getWithdrawNo())
                    .eqIfPresent(AccWithdrawFlow::getWithdrawAmt, reqVO.getWithdrawAmt())
                    .eqIfPresent(AccWithdrawFlow::getState, reqVO.getState())
                    .eqIfPresent(AccWithdrawFlow::getInitTime, reqVO.getInitTime())
                    .eqIfPresent(AccWithdrawFlow::getProcessingTime, reqVO.getProcessingTime())
                    .eqIfPresent(AccWithdrawFlow::getFinishTime, reqVO.getFinishTime())
                    .eqIfPresent(AccWithdrawFlow::getMemo, reqVO.getMemo())
                    .eqIfPresent(AccWithdrawFlow::getErrorReason, reqVO.getErrorReason())
                    .eqIfPresent(AccWithdrawFlow::getBankAccountNo, reqVO.getBankAccountNo())
                    .likeIfPresent(AccWithdrawFlow::getBankAccountName, reqVO.getBankAccountName())
                    .eqIfPresent(AccWithdrawFlow::getOutFlowNo, reqVO.getOutFlowNo())
                    .eqIfPresent(AccWithdrawFlow::getRemitAmt, reqVO.getRemitAmt())
                .orderByDesc(AccWithdrawFlow::getWithdrawFlowId));
    }

    default AccWithdrawFlow selectFlowByWithdrawNo(String withdrawNo) {
        return selectOne(new LambdaQueryWrapperX<AccWithdrawFlow>()
                .eqIfPresent(AccWithdrawFlow::getWithdrawNo, withdrawNo)
        );
    }
}
