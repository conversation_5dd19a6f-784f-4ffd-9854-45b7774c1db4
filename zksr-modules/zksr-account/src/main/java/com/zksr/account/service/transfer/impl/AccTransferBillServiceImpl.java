package com.zksr.account.service.transfer.impl;

import com.zksr.account.api.transfer.dto.AccTransferBillDTO;
import com.zksr.account.controller.transfer.vo.AccTransferBillPageReqVO;
import com.zksr.account.domain.AccTransferBill;
import com.zksr.account.mapper.AccTransferBillMapper;
import com.zksr.account.service.transfer.IAccTransferBillService;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/10/30 14:55
 */
@Service
@Slf4j
public class AccTransferBillServiceImpl implements IAccTransferBillService {

    @Autowired
    private AccTransferBillMapper accTransferBillMapper;

    @Override
    public void insertTransferBill(AccTransferBillDTO accTransferBillDTO) {
        AccTransferBill accTransferBill = HutoolBeanUtils.toBean(accTransferBillDTO, AccTransferBill.class);
        accTransferBillMapper.insert(accTransferBill);
    }

    /**
     * 查询分页数据
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<AccTransferBill> getAccTransferBillPage(AccTransferBillPageReqVO pageReqVO) {
        return accTransferBillMapper.selectPage(pageReqVO);
    }
}
