package com.zksr.account.service;

import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.domain.vo.openapi.BranchValueInfoOpenDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.controller.flow.vo.AccAccountFlowPageReqVO;

import java.util.List;

/**
 * 账户流水Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface IAccAccountFlowService {

    /**
     * 保存账户操作流水
     * @param flowList
     * @return
     */
    public List<Long> insertAccAccountFlow(List<AccAccountFlowDTO> flowList);

    /**
     * 保存账户操作流水
     * @param accAccountFlow
     */
    void insertAccAccountFlow(AccAccountFlow accAccountFlow);

    /**
     * 查询流水直接执行
     * @param flowList
     * @return
     */
    List<Long> saveAccountFlowAndProcess(List<AccAccountFlowDTO> flowList);

    /**
     * 保存账户操作流水
     * @param flowList
     * @return
     */
    List<Long> saveAccountFlowAndProcessSync(List<AccAccountFlowDTO> flowList);

    /**
     * 获得账户流水
     *
     * @param accountFlowId 账户流水id
     * @return 账户流水
     */
    public AccAccountFlow getAccAccountFlow(Long accountFlowId);


    /**
     * 根据业务ID 和 业务类型获取账户流水
     * @param tradeNo   交易单号
     * @param busitype  {@link AccountBusiType}
     * @return
     */
    public List<AccAccountFlow> getAccAccountFlowByBusiNo(String tradeNo, String busitype);

    /**
     * 获得账户流水分页
     *
     * @param pageReqVO 分页查询
     * @return 账户流水分页
     */
    PageResult<AccAccountFlow> getAccAccountFlowPage(AccAccountFlowPageReqVO pageReqVO);

    /**
     * 获取门店账户流水
     * @param accAccountFlowPageReqVO   门店账户流水
     * @return
     */
    PageResult<RechargeConsumeRespVO> getBranchAccountFlowPage(AccAccountFlowPageReqVO accAccountFlowPageReqVO);

    /**
     * 处理账户流水
     * @param flowIdList 账户流水ID集合
     */
    void processFlowByIds(List<Long> flowIdList);

    /**
     * 处理账户流水. 同步
     * @param flowIdList
     */
    void processFlowByIdsSync(List<Long> flowIdList);

    /**
     * 处理账户流水变动, 结算账户流水
     * @param accountFlow
     */
    void processFlow(AccAccountFlow accountFlow);

    /**
     * 获取结算重试流水记录
     * @param minId     批次最小ID
     * @return  每批次返回500条数据
     */
    CommonResult<List<AccAccountFlowDTO>> getTrySettleFlow(Long minId);

    /**
     * 获取账户流水数据 根据ID集合
     * @param accountFlowIds ID集合
     * @return 流水集合
     */
    List<AccAccountFlow> getAccAccountFlowListByIds(List<Long> accountFlowIds);

    /**
     * 获取对接第三方需要的门店储值充值/提现信息 根据ID集合
     * @param accountFlowIds ID集合
     * @return 流水集合
     */
    BranchValueInfoOpenDTO getBranchValueInfoByIds(List<Long> accountFlowIds);

}
