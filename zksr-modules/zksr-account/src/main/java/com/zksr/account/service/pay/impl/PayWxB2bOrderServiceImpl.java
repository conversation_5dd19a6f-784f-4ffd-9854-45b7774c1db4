package com.zksr.account.service.pay.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.client.PayClient;
import com.zksr.account.client.wx.WxB2bPayClient;
import com.zksr.account.controller.divide.vo.AccDivideFlowSaveReqVO;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.mapper.AccDivideFlowMapper;
import com.zksr.account.mapper.AccPayFlowMapper;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.vo.*;
import com.zksr.account.service.*;
import com.zksr.account.service.pay.PayChannelService;
import com.zksr.account.service.pay.PayWxB2bDivideOrderService;
import com.zksr.account.util.WxB2bPayUtil;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.DivideStateEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信B2B 支付特殊逻辑处理
 * @date 2024/9/21 8:52
 */
@Slf4j
@Service
public class PayWxB2bOrderServiceImpl implements PayWxB2bDivideOrderService {

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccPayFlowService payFlowService;

    @Autowired
    private IAccDivideDtlService divideDtlService;

    @Autowired
    private IAccDivideFlowService divideFlowService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Autowired
    private AccDivideFlowMapper accDivideFlowMapper;

    @Autowired
    private AccPayFlowMapper accPayFlowMapper;

    @Resource
    private OrderApi orderApi;

    @Override
    public PayChannelEnum getPlatform() {
        return PayChannelEnum.WX_B2B_PAY;
    }

    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_WXB2B_DIVIDE, condition = "#divideReqVO.tradeNo", tryLock = true)
    public CreateDivideRespVO divide(CreateDivideReqVO divideReqVO) {

        log.info("发起微信B2B请求分账,req={}", JSON.toJSONString(divideReqVO));
        // 验证是否支付
        AccPayFlow payFlow = payFlowService.getByOrderPayFlowSuccessFlag(divideReqVO.getTradeNo());
        if (Objects.isNull(payFlow) || payFlow.getCallbackFlag() != NumberPool.INT_ONE) {
            log.info("发起微信B2B请求分账, 支付记录不存, tradeNo:{}", divideReqVO.getTradeNo());
            throw exception(PAY_FLOW_NOT_EXIST);
        }

        // 验证是否只有一条分账数据, 那就是入驻商的, 无需分账
        if (payFlow.buildSettle().size() == 1) {
            log.warn("当前交易单号仅有一条分账信息, 说明仅发起方, 无需分账, tradeNo:{}", payFlow.getTradeNo());
            return CreateDivideRespVO.success();
        }

        // 获取支付发起者信息
        AccPlatformMerchant platformMerchant = WxB2bPayUtil.getPaySponsorMerchant(payFlow.buildSettle(), payFlow.getOrderType(), payFlow.getPlatform());;
        if (Objects.isNull(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchNo())) {
            log.error("支付发起商户号不存在, 或者无效, tradeNo:{}", divideReqVO.getTradeNo());
            return CreateDivideRespVO.fail("支付发起者商户号不存在, 或者无效");
        }

        // 支付发起者, 不需要请求分账, 最终都是它的
        if (divideReqVO.getMerchantType().equals(platformMerchant.getMerchantType())) {
            log.warn("发起微信B2B请求分账, 支付发起方不需要发起");
            return CreateDivideRespVO.success();
        }

        // 获取在线分账需要处理的数据
        List<AccDivideDtl> divideList = divideDtlService.getDivideList(
                AccDivideDtl.builder()
                        .tradeNo(divideReqVO.getTradeNo())
                        .merchantType(divideReqVO.getMerchantType())
                        .merchantId(divideReqVO.getMerchantId())
                        .onlineDivideState(DivideStateEnum.UNDEFINED.getState())
                        .build()
        );
        // 验证空
        if (divideList.isEmpty()) {
            log.info("无可结算数据时, 直接返回成功, 没有找到分账方信息 {}", divideReqVO.getTradeNo());
            return CreateDivideRespVO.success();
        }

        // 总分账金额
        BigDecimal totalAltAmt = divideList.stream().map(AccDivideDtl::getAltAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 分账接受账号
        Long merchantId = divideList.get(0).getMerchantId();

        // 允许下单的时候没有商户号, 但是分账请求分账的时候必须完成商户号进件
        // 获取最新的商户号
        String altMchNo = StringPool.EMPTY;
        // 大于零的有效分账才查询验证商户信息
        if (NumberUtil.isGreater(totalAltAmt, BigDecimal.ZERO)) {
            PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(payFlow.getSysCode());
            // 查看是否统一业务员分润账户
            if (MerchantTypeEnum.isColonel(divideReqVO.getMerchantType()) && StringPool.ONE.equals(payConfigDTO.getSwitchPartnerColonel())) {
                AccPlatformMerchant payee = platformMerchantService.getPlatformMerchant(payFlow.getSysCode(), MerchantTypeEnum.PARTNER_COLONEL.getType(), payFlow.getPlatform(), payFlow.getSysCode());
                if (Objects.isNull(payee) || StringUtils.isEmpty(payee.getAltMchNo())) {
                    return CreateDivideRespVO.fail(StringUtils.format("使用统一业务员分账账户, 未查询到进件信息, sysCode={}", payFlow.getSysCode()));
                }
                altMchNo = payee.getAltMchNo();
            } else {
                AccPlatformMerchant payee = platformMerchantService.getPlatformMerchant(merchantId, divideList.get(0).getMerchantType(), payFlow.getPlatform(), payFlow.getSysCode());
                if (Objects.isNull(payee) || StringUtils.isEmpty(payee.getAltMchNo())) {
                    return CreateDivideRespVO.fail(StringUtils.format("商户ID:{}, 未查询到进件信息", merchantId));
                }
                altMchNo = payee.getAltMchNo();
            }
        }

        // 创建分账流水
        AccDivideFlowSaveReqVO divideFlow = new AccDivideFlowSaveReqVO();
        divideFlow.setPlatform(payFlow.getPlatform());
        divideFlow.setType(NumberPool.INT_ONE);
        divideFlow.setTradeNo(payFlow.getTradeNo());
        divideFlow.setOutTradeNo(payFlow.getOutTradeNo());
        divideFlow.setTargetPlatformMerchantId(merchantId);
        divideFlow.setMchid(platformMerchant.getAltMchNo());
        divideFlow.setPayeeType(divideReqVO.getMerchantType());
        divideFlow.setPayeeNo(altMchNo);
        divideFlow.setDivideAmt(totalAltAmt);
        divideFlow.setDivideStatus(NumberPool.INT_ONE);
        divideFlow.setDivideReqStatus(NumberPool.INT_ZERO);
        divideFlow.setAccPayFlowId(payFlow.getPayFlowId());
        divideFlow.setTargetPlatformMerchantId(divideReqVO.getMerchantId());
        Long divideFlowId = divideFlowService.insertAccDivideFlow(divideFlow);

        // 根据平台 + appid 获取支付配置
        // 获取支付client
        PayClient payClient = getPayChannelService().getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
        WxB2bPayClient client = (WxB2bPayClient) payClient;

        // 请求分账
        // 1: 分账中 2: 分账完成 3：分账失败
        CreateDivideRespVO divideRespVO;
        if (payFlow.getIsDivide() == NumberPool.INT_ONE) {
            divideRespVO = CreateDivideRespVO.success();
        } else {
            divideRespVO = client.divide(
                    platformMerchant.getAltMchNo(),
                    payFlow.getPayFlowId().toString(),
                    totalAltAmt,
                    altMchNo,
                    platformMerchant.getAltMchKey()
            );
        }
        if (!divideRespVO.isSuccess()) {
            // 失败
            divideFlow.setDivideStatus(NumberPool.INT_THREE);
            divideFlow.setErrMsg(divideRespVO.getMsg());
            divideFlow.setDivideReqStatus(NumberPool.INT_ONE);
            divideFlow.setDivideFlowId(divideFlowId);
            divideFlowService.updateAccDivideFlow(divideFlow);
        } else {
            // 修改分账状态
            ArrayList<AccDivideDtl> updateList = new ArrayList<>();
            for (AccDivideDtl divideDtl : divideList) {
                divideDtl.setDivideDtlId(divideDtl.getDivideDtlId());
                divideDtl.setProcessTime(new Date());
                divideDtl.setOnlineDivideState(DivideStateEnum.PROCESSING.getState());
                divideDtl.setDivideFlowId(divideFlowId);
                divideDtl.setAltMchNo(altMchNo);
                updateList.add(divideDtl);
            }
            divideDtlService.updateBatch(updateList);
        }

        // 返回分账流水ID
        divideRespVO.setDivideFlowId(divideFlowId);
        return divideRespVO;
    }

    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_WXB2B_DIVIDE, condition = "divideReqVO.tradeNo", tryLock = true)
    public void divideCheckOver(CreateDivideReqVO divideReqVO) {
        log.info("验证订单是否可以微信支付分账完成, req={}", JSON.toJSONString(divideReqVO));
        // 验证是否支付
        AccPayFlow payFlow = payFlowService.getByOrderPayFlowSuccessFlag(divideReqVO.getTradeNo());
        if (Objects.isNull(payFlow) || payFlow.getCallbackFlag() != NumberPool.INT_ONE) {
            log.info("发起微信B2B请求分账, 支付记录不存, order:{}", divideReqVO.getTradeNo());
            return;
        }

        // 验证是否只有一条分账数据, 那就是入驻商的, 无需分账
        if (payFlow.buildSettle().size() == 1) {
            log.warn("当前交易单号仅有一条分账信息, 判定为发起方, 无需完成分账, tradeNo:{}", payFlow.getTradeNo());
            // 直接回调订单模块, 无需分账
            if (!payFlow.getOrderType().equals(OrderTypeConstants.BRANCH_CHARGE)) {
                orderApi.updateOrderDivideSettleState(payFlow.getTradeNo()).checkError();
            } else {
                this.finishAllDivide(payFlow, payFlow.buildSettle().get(0));
            }
            return;
        }

        // 获取支付发起者信息
        AccPlatformMerchant platformMerchant = WxB2bPayUtil.getPaySponsorMerchant(payFlow.buildSettle(), payFlow.getOrderType(), payFlow.getPlatform());;
        if (Objects.isNull(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchNo())) {
            log.error("支付发起商户号不存在, 或者无效, tradeNo:{}", divideReqVO.getTradeNo());
            return;
        }

        // 查看是不是只有入驻商的没有完成了
        // 如果是的, 那就可以请求完成了
        // 获取在线分账需要处理的数据
        List<AccDivideDtl> divideList = divideDtlService.getDivideList(
                AccDivideDtl.builder()
                        .tradeNo(divideReqVO.getTradeNo())
                        .build()
        );

        // 不是发起方, 并且已完成的
        long notSupplierFinishCnt = divideList.stream().filter(item ->
                item.getOnlineDivideState().equals(DivideStateEnum.FINISH.getState()) && !item.getMerchantType().equals(platformMerchant.getMerchantType())
        ).count();
        // 是发起方的
        long supplierCnt = divideList.stream().filter(item ->
                item.getMerchantType().equals(platformMerchant.getMerchantType())
        ).count();
        log.info("notSupplierFinishCnt {}, supplierCnt {}", notSupplierFinishCnt, supplierCnt);
        if (notSupplierFinishCnt + supplierCnt < divideList.size()) {
            return;
        }

        List<AccDivideDtl> supplierDivide = divideList.stream().filter(item ->
                item.getMerchantType().equals(platformMerchant.getMerchantType()) && !item.getOnlineDivideState().equals(DivideStateEnum.FINISH.getState())
        ).collect(Collectors.toList());

        // 如果入驻商已经结算了, 那就无需操作了
        if (supplierDivide.isEmpty()) {
            log.info("入驻商结算已经处理了, tradeNo={}", payFlow.getTradeNo());
            return;
        }

        // 可以来执行分账完成了
        // 总分账金额
        BigDecimal totalAltAmt = supplierDivide.stream().map(AccDivideDtl::getAltAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 分账接受账号
        String altMchNo = supplierDivide.get(0).getAltMchNo();
        Long merchantId = supplierDivide.get(0).getMerchantId();

        // 创建分账流水
        AccDivideFlowSaveReqVO divideFlow = new AccDivideFlowSaveReqVO();
        divideFlow.setPlatform(payFlow.getPlatform());
        divideFlow.setType(NumberPool.INT_ONE);
        divideFlow.setTradeNo(payFlow.getTradeNo());
        divideFlow.setOutTradeNo(payFlow.getOutTradeNo());
        divideFlow.setTargetPlatformMerchantId(merchantId);
        divideFlow.setMchid(platformMerchant.getAltMchNo());
        divideFlow.setPayeeType(platformMerchant.getMerchantType());
        divideFlow.setPayeeNo(altMchNo);
        divideFlow.setDivideAmt(totalAltAmt);
        divideFlow.setDivideStatus(NumberPool.INT_ONE);
        divideFlow.setDivideReqStatus(NumberPool.INT_ZERO);
        divideFlow.setAccPayFlowId(payFlow.getPayFlowId());
        Long divideFlowId = divideFlowService.insertAccDivideFlow(divideFlow);

        // 请求分账完成
        // 根据平台 + appid 获取支付配置
        // 获取支付client
        PayClient payClient = getPayChannelService().getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
        WxB2bPayClient client = (WxB2bPayClient) payClient;

        // 请求分账
        CreateDivideRespVO divideRespVO;
        // 如果是已分账
        if (payFlow.getIsDivide() == NumberPool.INT_ONE) {
            divideRespVO = PayWxB2bCreateDivideRespVO.success();
        } else {
            // 请求分账
            divideRespVO = client.divideOver(
                    platformMerchant.getAltMchNo(),
                    payFlow.getPayFlowId().toString(),
                    platformMerchant.getAltMchKey()
            );
        }

        // 1: 分账中 2: 分账完成 3：分账失败
        if (divideRespVO.isSuccess()) {

            // 修改分账状态
            ArrayList<AccDivideDtl> updateList = new ArrayList<>();
            // 非入驻商的
            for (AccDivideDtl divideDtl : supplierDivide) {
                divideDtl.setDivideDtlId(divideDtl.getDivideDtlId());
                divideDtl.setProcessTime(new Date());
                divideDtl.setOnlineDivideState(DivideStateEnum.FINISH.getState());
                divideDtl.setDivideFlowId(divideFlowId);
                divideDtl.setDivideTime(DateUtil.date());
                updateList.add(divideDtl);
            }
            divideDtlService.updateBatch(updateList);

            // 成功
            divideFlow.setDivideStatus(NumberPool.INT_TWO);
            divideFlow.setDivideReqStatus(NumberPool.INT_ZERO);
            divideFlow.setDivideFlowId(divideFlowId);
            divideFlowService.updateAccDivideFlow(divideFlow);

            // 更新支付流水是已完成分账
            AccPayFlow flow = new AccPayFlow();
            flow.setPayFlowId(payFlow.getPayFlowId());
            flow.setIsDivide(NumberPool.INT_ONE);
            accPayFlowMapper.updateById(flow);

            // 通知订单模版分账完成
            // 仅在非充值订单情况, 充值订单不需要通知
            if (!payFlow.getOrderType().equals(OrderTypeConstants.BRANCH_CHARGE)) {
                // 这里需要回调结算, 告诉trade模块, 结算已经实际处理完成了
                orderApi.updateOrderDivideSettleState(payFlow.getTradeNo()).checkError();
            }
        } else {
            // 失败
            divideFlow.setDivideStatus(NumberPool.INT_THREE);
            divideFlow.setErrMsg(divideRespVO.getMsg());
            divideFlow.setDivideReqStatus(NumberPool.INT_ONE);
            divideFlow.setDivideFlowId(divideFlowId);
            divideFlowService.updateAccDivideFlow(divideFlow);
        }
    }

    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_WXB2B_DIVIDE, condition = "divideFlowDTO.tradeNo", tryLock = true)
    public void updateDivideStatus(DivideFlowDTO divideFlowDTO) {
        // 验证是不是已经处理完成了
        AccDivideFlow divideFlow = divideFlowService.getAccDivideFlow(divideFlowDTO.getDivideFlowId());
        if (divideFlow.getDivideStatus() != NumberPool.INT_ONE) {
            log.warn("更新微信B2B分账最新状态, 但是这个单据已经不是待处理了, flowId={}", divideFlowDTO.getDivideFlowId());
            return;
        }
        if (MerchantTypeEnum.isSupplier(divideFlow.getPayeeType())) {
            return;
        }
        AccPayFlow payFlow = payFlowService.getAccPayFlow(divideFlow.getAccPayFlowId());

        // 请求分账完成
        // 根据平台 + appid 获取支付配置
        // 获取支付client
        PayClient payClient = getPayChannelService().getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
        WxB2bPayClient client = (WxB2bPayClient) payClient;

        // 获取支付发起者信息
        AccPlatformMerchant platformMerchant = WxB2bPayUtil.getPaySponsorMerchant(payFlow.buildSettle(), payFlow.getOrderType(), payFlow.getPlatform());;
        if (Objects.isNull(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchNo())) {
            log.error("支付发起商户号不存在, 或者无效, divideFlowId:{}", divideFlowDTO.getDivideFlowId());
            return;
        }

        // 查询分账结果
        // 1: 分账中 2: 分账完成 3：分账失败
        PayWxB2bDivideRespVO divideRespVO;
        // 是已分账, 或者分账金额是0, 就不用查询分账结果, 默认就是成功
        if (payFlow.getIsDivide() == NumberPool.INT_ONE || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, divideFlow.getDivideAmt())) {
            divideRespVO = PayWxB2bDivideRespVO.success();
        } else {
            divideRespVO = client.queryDivide(
                    platformMerchant.getAltMchNo(),
                    payFlow.getPayFlowId().toString(),
                    divideFlowDTO.getPayeeNo(),
                    platformMerchant.getAltMchKey()
            );
        }
        // 分账中无需处理
        if (divideRespVO.getStatus() == 1) {
            return;
        }
        // 失败和成功, 都更新数据
        divideFlow.setDivideStatus(divideRespVO.getStatus());
        divideFlow.setErrMsg(divideRespVO.getMsg());
        accDivideFlowMapper.updateById(divideFlow);

        // 分账数据
        List<AccDivideDtl> divideDtlList = divideDtlService.getDivideList(
                AccDivideDtl.builder()
                        .divideFlowId(divideFlow.getDivideFlowId())
                        .build()
        );
        if (divideDtlList.isEmpty()) {
            return;
        }
        ArrayList<AccDivideDtl> updateDivideDtlList = new ArrayList<>();
        for (AccDivideDtl divideDtl : divideDtlList) {
            AccDivideDtl updateDivideDtl = new AccDivideDtl();
            updateDivideDtl.setDivideDtlId(divideDtl.getDivideDtlId());
            updateDivideDtl.setOnlineDivideState(DivideStateEnum.FINISH.getState());
            updateDivideDtl.setDivideTime(DateUtil.date());
            updateDivideDtlList.add(updateDivideDtl);
        }
        divideDtlService.updateBatch(updateDivideDtlList);
    }

    @Override
    public CreateDivideRespVO refundOver(PayFlowDTO payFlow) {

        // 如果只有一条结算信息, 直接成功
        if (payFlow.buildSettle().size() == 1) {
            return CreateDivideRespVO.success();
        }

        // 入驻商结算信息, 里面有入驻商商户号
        List<OrderSettlementDTO> supplierSettleDTOList = payFlow.buildSettle().stream().filter(item -> MerchantTypeEnum.isSupplier(item.getMerchantType())).collect(Collectors.toList());
        if (supplierSettleDTOList.isEmpty()) {
            throw new ServiceException("结算流水不存在");
        }

        OrderSettlementDTO supplierSettleDTO = supplierSettleDTOList.get(0);
        // 获取入驻商进件商户信息
        AccPlatformMerchant platformMerchant = platformMerchantService.getPlatformMerchant(supplierSettleDTO.getMerchantId(), supplierSettleDTO.getMerchantType(), payFlow.getPlatform());
        if (Objects.isNull(platformMerchant) || StringUtils.isEmpty(platformMerchant.getAltMchNo())) {
            log.error("入驻商商户号不存在, 或者无效, supplierId:{}", supplierSettleDTO.getMerchantId());
            throw new ServiceException("入驻商商户号不存在");
        }

        // 请求分账完成
        // 根据平台 + appid 获取支付配置
        // 获取支付client
        PayClient payClient = getPayChannelService().getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
        WxB2bPayClient client = (WxB2bPayClient) payClient;

        // 完成订单分账
        // finishAllDivide(payFlow, supplierSettleDTO);

        // 订单分账完成
        CreateDivideRespVO divideRespVO;
        if (payFlow.getIsDivide() == NumberPool.INT_ONE) {
            divideRespVO = CreateDivideRespVO.fail("订单已分账");
        } else {
            divideRespVO = client.divideOver(
                    supplierSettleDTO.getAccountNo(),
                    payFlow.getPayFlowId().toString(),
                    platformMerchant.getAltMchKey()
            );
        }
        if (divideRespVO.isSuccess()) {
            // 更新支付流水是已完成分账
            AccPayFlow flow = new AccPayFlow();
            flow.setPayFlowId(payFlow.getPayFlowId());
            flow.setIsDivide(NumberPool.INT_ONE);
            accPayFlowMapper.updateById(flow);
        }
        // 请求分账
        return divideRespVO;
    }

    private PayChannelService getPayChannelService() {
        return SpringUtils.getBean(PayChannelService.class);
    }

    private void finishAllDivide(AccPayFlow payFlow, OrderSettlementDTO supplierSettleDTO) {
        // 分账流水
        List<AccDivideDtl> divideList = divideDtlService.getDivideList(
                AccDivideDtl.builder()
                        .tradeNo(payFlow.getTradeNo())
                        .build()
        );
        long count = divideList.stream().filter(item -> Objects.nonNull(item.getDivideTime())).count();
        if (count > 0) {
            log.info("{} 订单因为退款提前完成订单分账, 但是已经有商户提前分账了", payFlow.getTradeNo());
            return;
        }

        divideList.stream().collect(Collectors.groupingBy(AccDivideDtl::getMerchantType)).forEach((merchantType, typeList) -> {
            // 商户数据列表
            typeList.stream().collect(Collectors.groupingBy(AccDivideDtl::getMerchantId)).forEach((merchantId, merchantList) -> {
                // 总分账金额
                BigDecimal totalAltAmt = merchantList.stream().map(AccDivideDtl::getAltAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 分账接受账号
                String altMchNo = merchantList.get(0).getAltMchNo();
                // 创建分账流水
                AccDivideFlowSaveReqVO divideFlow = new AccDivideFlowSaveReqVO();
                divideFlow.setPlatform(payFlow.getPlatform());
                divideFlow.setType(NumberPool.INT_ONE);
                divideFlow.setTradeNo(payFlow.getTradeNo());
                divideFlow.setOutTradeNo(payFlow.getOutTradeNo());
                divideFlow.setTargetPlatformMerchantId(merchantId);
                divideFlow.setMchid(supplierSettleDTO.getAccountNo());
                divideFlow.setPayeeType(merchantList.get(0).getMerchantType());
                divideFlow.setPayeeNo(altMchNo);
                divideFlow.setDivideAmt(totalAltAmt);
                divideFlow.setDivideStatus(NumberPool.INT_TWO);
                divideFlow.setDivideReqStatus(NumberPool.INT_ZERO);
                divideFlow.setAccPayFlowId(payFlow.getPayFlowId());
                Long divideFlowId = divideFlowService.insertAccDivideFlow(divideFlow);

                // 分账流水
                for (AccDivideDtl divideDtl : merchantList) {
                    divideDtl.setProcessTime(DateUtil.date());
                    divideDtl.setDivideTime(DateUtil.date());
                    divideDtl.setOnlineDivideState(DivideStateEnum.FINISH.getState());
                    divideDtl.setDivideFlowId(divideFlowId);
                }
                divideDtlService.updateBatch(ListUtil.toList(merchantList));
            });
        });
        // 更新支付流水是已完成分账
        AccPayFlow flow = new AccPayFlow();
        flow.setPayFlowId(payFlow.getPayFlowId());
        flow.setIsDivide(NumberPool.INT_ONE);
        accPayFlowMapper.updateById(flow);
    }
}
