package com.zksr.account.service.pay.process;

import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderNotifyRespDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.SUPPLIER_APPID_NOT_EXIST;
import static com.zksr.account.enums.ErrorCodeConstants.USER_APPID_NOT_EXIST;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单处理器, 各种订单类型需要实现, 返回标准的支付信息
 * @date 2024/3/7 16:11
 */
public abstract class PayOrderProcessService {

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    /**
     * 获取订单类型
     * @return 订单类型 com.zksr.common.core.constant.OrderTypeConstants
     */
    public Integer getOrderType() {
        return -1;
    }


    /**
     * 验证订单, 提供订单实际支付金额
     * @param reqVO
     * @return
     */
    public PayOrderDTO processValidateOrderBefore(PayOrderSubmitReqVO reqVO) {
        // 前端不传appid, 根据平台 + 订单类型获取
        // 验证订单
        PayOrderDTO orderDTO = validateOrder(reqVO);
        // 设置扩展参数
        if (Objects.nonNull(reqVO.getExtras())) {
            if (reqVO.getExtras().containsKey(PayOrderSubmitExtras.BRANCH_ID)) {
                orderDTO.setBranchId(Long.parseLong(reqVO.getExtras().get(PayOrderSubmitExtras.BRANCH_ID)));
            }
            if (reqVO.getExtras().containsKey(PayOrderSubmitExtras.SUPPLIER_ID)) {
                orderDTO.setSupplierId(Long.parseLong(reqVO.getExtras().get(PayOrderSubmitExtras.SUPPLIER_ID)));
            }
        }
        // 设置请求方式
        orderDTO.setMethod(reqVO.getMethod());
        // 设置appid
        orderDTO.setAppid(loadAppId(reqVO));
        reqVO.setAppid(orderDTO.getAppid());
        // 设置sessionKey
        orderDTO.setSessionKey(reqVO.getSessionKey());
        return orderDTO;
    }

    /**
     * 验证订单, 提供订单实际支付金额
     * @param reqVO
     * @return
     */
    public abstract PayOrderDTO validateOrder(PayOrderSubmitReqVO reqVO);

    /**
     * 处理支付成功
     * @param notify
     */
    public abstract PayOrderNotifyRespDTO notifyOrderSuccess(PayOrderRespDTO notify);

    /**
     * 处理退款成功
     * @param notify
     */
    public abstract void notifyRefundSuccess(PayRefundRespDTO notify);

    /**
     * 处理退款失败
     * @param notify
     */
    public abstract void notifyRefundFailure(PayRefundRespDTO notify);

    /**
     * 处理支付回调 后 回调
     * @param payOrderNotifyRespDTO
     */
    public void notifyOrderSuccessCall(PayOrderNotifyRespDTO payOrderNotifyRespDTO) {

    }



    /**
     * 根据订单类型加载支付 app 支付配置
     * @param reqVO
     */
    private String loadAppId(PayOrderSubmitReqVO reqVO) {
        // 商城端平台, 货到付款自定义
        if (OrderTypeConstants.useShopAppId(reqVO.getOrderType())) {
            AppletBaseConfigDTO appletBaseConfigDTO = accountCacheService.getAppletBaseConfigDTO(reqVO.getSysCode());
            if (Objects.isNull(appletBaseConfigDTO) || StringUtils.isEmpty(appletBaseConfigDTO.getAppId())) {
                throw exception(USER_APPID_NOT_EXIST);
            }
            return appletBaseConfigDTO.getAppId();
        }
        // 入驻商端公共
        if (OrderTypeConstants.SUPPLIER_CHARGE.equals(reqVO.getOrderType())) {
            String supplierAppid = redisSysConfigService.getSupplierAppid();
            if (StringUtils.isEmpty(supplierAppid)) {
                throw exception(SUPPLIER_APPID_NOT_EXIST);
            }
            return supplierAppid;
        }
        return null;
    }
}
