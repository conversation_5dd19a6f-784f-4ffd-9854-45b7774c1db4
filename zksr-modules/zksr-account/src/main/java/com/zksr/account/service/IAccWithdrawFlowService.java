package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccWithdrawFlow;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowSaveReqVO;

/**
 * 支付平台提现流水Service接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
public interface IAccWithdrawFlowService {

    /**
     * 获得支付平台提现流水
     *
     * @param withdrawFlowId 提现流水id
     * @return 支付平台提现流水
     */
    public AccWithdrawFlow getAccWithdrawFlow(Long withdrawFlowId);

    /**
     * 获得支付平台提现流水分页
     *
     * @param pageReqVO 分页查询
     * @return 支付平台提现流水分页
     */
    PageResult<AccWithdrawFlow> getAccWithdrawFlowPage(AccWithdrawFlowPageReqVO pageReqVO);

    /**
     * 创建流水
     * @param reqVO
     */
    AccWithdrawFlow createFlow(TransferSettleReqVO reqVO);

    /**
     * 更新流水单
     * @param updateFlow
     */
    void updateWithdrawFlow(AccWithdrawFlow updateFlow);

    /**
     * 获取流水记录
     * @param withdrawNo    交易流水唯一单号
     * @return
     */
    AccWithdrawFlow getAccWithdrawFlow(String withdrawNo);
}
