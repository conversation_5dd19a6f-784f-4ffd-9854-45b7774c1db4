package com.zksr.account.service.pay;

import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.mapper.AccPayFlowMapper;
import com.zksr.common.core.enums.PayChannelEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/17 14:23
 */
@Service
public class DivideOrderService {

    @Autowired
    private List<IPayDivideOrderService> divideOrderServices;

    @Autowired
    private AccPayFlowMapper accPayFlowMapper;

    /**
     * 通过支付平台获取支付平台分账处理service
     */
    public IPayDivideOrderService getDivideOrderService(PayChannelEnum payChannel) {
        for (IPayDivideOrderService divideOrderService : divideOrderServices) {
            if (divideOrderService.getPlatform() == payChannel) {
                return divideOrderService;
            }
        }
        return null;
    }

    /**
     * 通过订单号获取支付平台分账处理service
     */
    public IPayDivideOrderService getDivideOrderService(String tradeNo) {
        AccPayFlow payFlow = accPayFlowMapper.selectByOrderPayFlowSuccessFlag(tradeNo);
        if (Objects.isNull(payFlow)) {
            throw new RuntimeException("订单支付记录不存在");
        }
        return getDivideOrderService(PayChannelEnum.fromValue(payFlow.getPlatform()));
    }
}
