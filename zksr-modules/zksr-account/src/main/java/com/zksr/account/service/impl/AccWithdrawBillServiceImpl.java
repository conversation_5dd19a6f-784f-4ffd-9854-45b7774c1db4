package com.zksr.account.service.impl;

import com.zksr.account.api.withdraw.dto.AccWithdrawBillDTO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawBillPageReqVO;
import com.zksr.account.domain.AccWithdrawBill;
import com.zksr.account.mapper.AccWithdrawBillMapper;
import com.zksr.account.service.IAccWithdrawBillService;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 提现对账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-4
 */
@Service
@Slf4j
public class AccWithdrawBillServiceImpl implements IAccWithdrawBillService {

    @Autowired
    private AccWithdrawBillMapper accWithdrawBillMapper;

    @Override
    public void insertAccWithdrawBillBatch(List<AccWithdrawBillDTO> accWithdrawBillDTO) {
        List<AccWithdrawBill> accWithdrawBills = accWithdrawBillDTO.stream()
                .map(dto -> HutoolBeanUtils.toBean(dto, AccWithdrawBill.class))
                .collect(Collectors.toList());

        // 批量插入
        accWithdrawBillMapper.insertBatch(accWithdrawBills);
    }

    /**
     * 查询分页数据
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<AccWithdrawBill> getAccWithdrawBillPage(AccWithdrawBillPageReqVO pageReqVO) {
        return accWithdrawBillMapper.selectPage(pageReqVO);
    }

    @Override
    public int countWithdrawBillsByDateAndAltNo(String dataFormatted, String altNo) {
        return accWithdrawBillMapper.countWithdrawBillsByDateAndAltNo(dataFormatted,altNo);
    }

    @Override
    public void deleteWithdrawBillsByDateAndAltNo(String dataFormatted, String altNo) {
        accWithdrawBillMapper.deleteWithdrawBillsByDateAndAltNo(dataFormatted,altNo);
    }
}
