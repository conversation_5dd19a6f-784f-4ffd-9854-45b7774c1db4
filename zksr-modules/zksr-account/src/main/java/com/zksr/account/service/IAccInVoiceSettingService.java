package com.zksr.account.service;

import java.util.List;

import javax.validation.Valid;

import com.zksr.account.api.platformMerchant.vo.AccInvoiceSettingSaveReqVO;
import com.zksr.account.domain.AccInvoiceSetting;

public interface IAccInVoiceSettingService {

    /**
     * 保存发票设置
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long saveAccInvoiceSetting(@Valid AccInvoiceSettingSaveReqVO  createReqVO);


    /**
     * 获取发票设置
     * @param merchantId    入驻商ID
     * @param merchantType  商户类型
     * @param platform      支付平台
     * @param sysCode       平台商
     * @return
     */
    AccInvoiceSetting getAccInvoiceSetting(Long merchantId, String merchantType, String platform, Long sysCode);
    /**
     * 获取发票设置列表
     * @param merchantId    入驻商ID
     * @param merchantType  商户类型
     * @param sysCode       平台商
     * @return
     */
    List<AccInvoiceSetting> getAccInvoiceSettingList(Long merchantId, String merchantType, Long sysCode);
}
