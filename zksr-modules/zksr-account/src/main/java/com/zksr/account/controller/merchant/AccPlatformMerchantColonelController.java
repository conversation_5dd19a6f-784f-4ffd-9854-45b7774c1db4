package com.zksr.account.controller.merchant;

import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.convert.account.PlatformMerchantConvert;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.colonel.vo.MemColonelPageReqVO;
import com.zksr.member.api.colonel.vo.MemColonelRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 支付平台商户Controller
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Api(tags = "管理后台 - 业务员进件商户接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/merchantColonel")
@SuppressWarnings("all")
public class AccPlatformMerchantColonelController {

    @Autowired
    private IAccPlatformMerchantService accPlatformMerchantService;

    @Resource
    private ColonelApi colonelApi;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 注册支付平台商户
     */
    @ApiOperation(value = "注册支付平台商户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.REGISTER)
    @RequiresPermissions(Permissions.REGISTER)
    @Log(title = "支付平台商户", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public CommonResult<Long> register(@Valid @RequestBody AccPlatformMerchantRegisterReqVO reqVO) {
        Long merchantId = accPlatformMerchantService.register(reqVO);
        // 注册完成以后去同步一下信息
        accPlatformMerchantService.syncData(
                AccPlatformMerchantSyncReqVO.builder()
                        .merchantId(reqVO.getMerchantId())
                        .merchantType(reqVO.getMerchantType())
                        .platform(reqVO.getPlatform())
                        .build()
        );
        return success(merchantId);
    }

    /**
     * 上传商户资质
     */
    @ApiOperation(value = "上传商户资质", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.UPLOAD_PIC + ", 目前仅需要上传 FRONT_OF_ID_CARD(身份证正面), BACK_OF_ID_CARD(身份证反面), UNIFIED_CODE_CERTIFICATE(三证合一营业执照), PERMIT_FOR_BANK_ACCOUNT(开户许可证)")
    @RequiresPermissions(Permissions.UPLOAD_PIC)
    @Log(title = "上传商户资质", businessType = BusinessType.UPDATE)
    @PostMapping("/uploadPic")
    public CommonResult<Long> uploadPic(@Valid @RequestBody AccPlatformMerchantUploadPicReqVO reqVO) {
        accPlatformMerchantService.uploadPic(reqVO);
        return success(NumberPool.LONG_ONE);
    }

    /**
     * 更新支付平台商户
     */
    @ApiOperation(value = "更新支付平台商户", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "更新支付平台商户", businessType = BusinessType.UPDATE)
    @PostMapping("/updateRegister")
    public CommonResult<Long> updateRegister(@Valid @RequestBody AccPlatformMerchantRegisterReqVO reqVO) {
        return success(accPlatformMerchantService.updateRegister(reqVO));
    }

    /**
     * 同步商户信息, 用于从支持平台查询信息保存到数据库
     */
    @ApiOperation(value = "同步商户信息", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SYNC)
    @RequiresPermissions(Permissions.SYNC)
    @Log(title = "同步商户信息", businessType = BusinessType.UPDATE)
    @PostMapping("/syncData")
    public CommonResult<Boolean> syncData(@RequestBody AccPlatformMerchantSyncReqVO reqVO) {
        accPlatformMerchantService.syncData(reqVO);
        return success(Boolean.TRUE);
    }

    /**
     * 获取支付平台商户详细信息
     */
    @ApiOperation(value = "获得支付平台商户详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{platformMerchantId}")
    public CommonResult<AccPlatformMerchantRespVO> getInfo(@PathVariable("platformMerchantId") Long platformMerchantId) {
        AccPlatformMerchant accPlatformMerchant = accPlatformMerchantService.getAccPlatformMerchant(platformMerchantId);
        return success(HutoolBeanUtils.toBean(accPlatformMerchant, AccPlatformMerchantRespVO.class));
    }

    /**
     * 进件商户列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取进件商户列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccPlatformMerchantRespVO>> getPartnerPage(@Valid AccPlatformMerchantPageReqVO pageReqVO) {
        MemColonelPageReqVO req = new MemColonelPageReqVO();
        req.setColonelName(pageReqVO.getMerchantName());
        req.setColonelId(pageReqVO.getMerchantId());
        req.setPageNo(pageReqVO.getPageNo());
        req.setPageSize(pageReqVO.getPageSize());
        // 业务员角色处理
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        if (Objects.nonNull(colonelId)) {
            req.setColonelId(colonelId);
        }
        List<AccPlatformMerchantRespVO> merchantRespVOS = new ArrayList<>();
        Long total = NumberPool.LONG_ZERO;
        if (StringUtils.isNotEmpty(pageReqVO.getAuditStatus()) || StringUtils.isNotEmpty(pageReqVO.getPicStatus())) {
            PageResult<AccPlatformMerchant> merchantPage = accPlatformMerchantService.getAccPlatformMerchantPage(pageReqVO);
            total = merchantPage.getTotal();
            merchantRespVOS = PlatformMerchantConvert.INSTANCE.convertRespVO(merchantPage.getList());
            for (AccPlatformMerchantRespVO respVO : merchantRespVOS) {
                ColonelDTO colonelDTO = accountCacheService.getColonelDTO(respVO.getMerchantId());
                if (Objects.nonNull(colonelDTO)) {
                    respVO.setMerchantName(colonelDTO.getColonelName());
                }
            }
        } else {
            PageResult<MemColonelRespVO> result = colonelApi.getPage(req).getCheckedData();
            total = result.getTotal();
            for (MemColonelRespVO colonel : result.getList()) {
                AccPlatformMerchant platformMerchant = accPlatformMerchantService.getPlatformMerchant(colonel.getColonelId(), MerchantTypeEnum.COLONEL.getType(), pageReqVO.getPlatform());
                if (Objects.nonNull(platformMerchant)) {
                    AccPlatformMerchantRespVO merchantRespVO = PlatformMerchantConvert.INSTANCE.convertRespVO(platformMerchant);
                    merchantRespVO.setMerchantName(colonel.getColonelName());
                    merchantRespVOS.add(merchantRespVO);
                } else {
                    merchantRespVOS.add(
                            AccPlatformMerchantRespVO.builder()
                                    .sysCode(colonel.getSysCode())
                                    .merchantId(colonel.getColonelId())
                                    .merchantName(colonel.getColonelName())
                                    .merchantType(MerchantTypeEnum.COLONEL.getType())
                                    .platform(pageReqVO.getPlatform())
                                    .build()
                    );
                }
            }
        }
        return success(new PageResult<AccPlatformMerchantRespVO>(merchantRespVOS, total));
    }

    /**
     * 解除商户绑定
     */
    @ApiOperation(value = "解除商户绑定", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "支付平台商商户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{platformMerchantIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] platformMerchantIds) {
        accPlatformMerchantService.deleteAccPlatformMerchantByPlatformMerchantIds(platformMerchantIds);
        return success(true);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:platform-merchant-colonel:add";
        /** 编辑 */
        public static final String EDIT = "account:platform-merchant-colonel:edit";
        /** 删除 */
        public static final String DELETE = "account:platform-merchant-colonel:remove";
        /** 列表 */
        public static final String LIST = "account:platform-merchant-colonel:list";
        /** 查询 */
        public static final String GET = "account:platform-merchant-colonel:query";
        /** 停用 */
        public static final String DISABLE = "account:platform-merchant-colonel:disable";
        /** 启用 */
        public static final String ENABLE = "account:platform-merchant-colonel:enable";
        /** 注册 */
        public static final String REGISTER = "account:platform-merchant-colonel:register";
        /** 上传资质 */
        public static final String UPLOAD_PIC = "account:platform-merchant-colonel:uploadPic";
        /** 同步信息 */
        public static final String SYNC = "account:platform-merchant-colonel:sync";
    }
}
