package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayBaseRespVO {

    /**
     * 接口名称
     */
    @JsonProperty("service")
    private String service;

    /**
     * 接口版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 签约美的支付唯一商户号
     */
    @JsonProperty("partner")
    private String partner;

    /**
     * 交易结果的代码。
     * 认证成功：1001
     * 认证失败：9位错误码
     */
    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 交易结果的描述
     */
    @JsonProperty("result_info")
    private String resultInfo;

    /**
     * 签名方式：
     *
     * MD5_RSA_TW：天威证书加密
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * 美的支付返回签名
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 编码格式，取值UTF-8
     */
    @JsonProperty("input_charset")
    private String inputCharset;

    /**
     * 语言类型，ZH-CN:中文
     */
    @JsonProperty("language")
    private String language;
}
