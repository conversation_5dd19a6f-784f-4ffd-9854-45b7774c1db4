package com.zksr.account.controller.divide;

import com.zksr.account.controller.divide.vo.AccDivideFlowPageReqVO;
import com.zksr.account.controller.divide.vo.AccDivideFlowRespVO;
import com.zksr.account.controller.divide.vo.AccDivideFlowSaveReqVO;
import com.zksr.account.convert.divideFlow.AccDivideFlowConvert;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.service.IAccDivideFlowService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)Controller
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
@Api(tags = "管理后台 - 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/divideFlow")
public class AccDivideFlowController {
    @Autowired
    private IAccDivideFlowService accDivideFlowService;

    /**
     * 新增分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     */
    @ApiOperation(value = "新增分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccDivideFlowSaveReqVO createReqVO) {
        return success(accDivideFlowService.insertAccDivideFlow(createReqVO));
    }

    /**
     * 修改分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     */
    @ApiOperation(value = "修改分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AccDivideFlowSaveReqVO updateReqVO) {
            accDivideFlowService.updateAccDivideFlow(updateReqVO);
        return success(true);
    }

    /**
     * 删除分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     */
    @ApiOperation(value = "删除分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)", businessType = BusinessType.DELETE)
    @DeleteMapping("/{divideFlowIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] divideFlowIds) {
        accDivideFlowService.deleteAccDivideFlowByDivideFlowIds(divideFlowIds);
        return success(true);
    }

    /**
     * 获取分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)详细信息
     */
    @ApiOperation(value = "获得分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{divideFlowId}")
    public CommonResult<AccDivideFlowRespVO> getInfo(@PathVariable("divideFlowId") Long divideFlowId) {
        AccDivideFlow accDivideFlow = accDivideFlowService.getAccDivideFlow(divideFlowId);
        return success(AccDivideFlowConvert.INSTANCE.convert(accDivideFlow));
    }

    /**
     * 分页查询分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccDivideFlowRespVO>> getPage(@Valid AccDivideFlowPageReqVO pageReqVO) {
        PageResult<AccDivideFlow> pageResult = accDivideFlowService.getAccDivideFlowPage(pageReqVO);
        return success(AccDivideFlowConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:divideFlow:add";
        /** 编辑 */
        public static final String EDIT = "account:divideFlow:edit";
        /** 删除 */
        public static final String DELETE = "account:divideFlow:remove";
        /** 列表 */
        public static final String LIST = "account:divideFlow:list";
        /** 查询 */
        public static final String GET = "account:divideFlow:query";
        /** 停用 */
        public static final String DISABLE = "account:divideFlow:disable";
        /** 启用 */
        public static final String ENABLE = "account:divideFlow:enable";
    }
}
