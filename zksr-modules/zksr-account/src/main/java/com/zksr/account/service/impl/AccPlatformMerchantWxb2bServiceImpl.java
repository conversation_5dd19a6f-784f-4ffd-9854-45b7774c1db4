package com.zksr.account.service.impl;

import cn.hutool.http.HttpUtil;
import com.zksr.account.api.platformMerchant.vo.WxB2bColonelOpenidBindReqVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoReqVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoRespVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bSaveReqVO;
import com.zksr.account.convert.wxb2b.AccPlatformMerchantWxb2bConvert;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.domain.AccPlatformMerchantWxb2b;
import com.zksr.account.mapper.AccPlatformMerchantMapper;
import com.zksr.account.mapper.AccPlatformMerchantWxb2bMapper;
import com.zksr.account.service.IAccPlatformMerchantWxb2bService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.metchant.PlatformMerchantService;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.utils.RedisWxTokenUtil;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 支付平台商户, 微信B2B平台商户扩展信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@Slf4j
@Service
public class AccPlatformMerchantWxb2bServiceImpl implements IAccPlatformMerchantWxb2bService {

    @Autowired
    private AccPlatformMerchantWxb2bMapper accPlatformMerchantWxb2bMapper;

    @Autowired
    private AccPlatformMerchantMapper platformMerchantMapper;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private RedisService redisService;

    /**
     * 更新业务员微信支付商户信息, 业务员无需进件, 直接使用openid 作为分账商户号
     * @param reqVO
     */
    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_WXB2B_COLONEL_BIND, condition = "#reqVO.openid", tryLock = true)
    public void updateWxB2bColonelOpenidBind(WxB2bColonelOpenidBindReqVO reqVO) {

        String[] bindKeyInfo = reqVO.getBindKey().split(StringPool.UNDERSCORE);
        if (bindKeyInfo.length < 2) {
            throw exception(PLATFORM_MERCHANT_COLONEL_BIND_KEY_NONE_EXIST);
        }
        String colonelIdStr = bindKeyInfo[0];
        String bindKey = bindKeyInfo[1];

        // 验证key是否存在. 业务员的bindKey 是否正确
        String cacheBindKey = redisService.getCacheObject(RedisConstants.COLONEL_PRE_BIND_WX_OPENID + colonelIdStr);
        if (StringUtils.isEmpty(cacheBindKey) || !bindKey.equals(cacheBindKey)) {
            throw exception(PLATFORM_MERCHANT_COLONEL_BIND_KEY_NONE_EXIST);
        }

        Long colonelId = Long.parseLong(colonelIdStr);
        ColonelDTO colonelDTO = accountCacheService.getColonelDTO(colonelId);

        // 查询是不是已经绑定了
        AccPlatformMerchant merchant = platformMerchantMapper.selectPlatformMerchant(colonelId, MerchantTypeEnum.COLONEL.getType(), PayChannelEnum.WX_B2B_PAY.getCode());
        if (Objects.nonNull(merchant)) {
            // 创建商户信息
            merchant.setMerchantId(colonelId);
            merchant.setMchStatus(StringPool.ONE);
            merchant.setPlatform(PayChannelEnum.WX_B2B_PAY.getCode());
            merchant.setAltMchNo(reqVO.getOpenid());
            merchant.setAltMchName(reqVO.getNickName());
            merchant.setAuditStatus(MerchantRegisterStateEnum.AUDITED.getState());
            platformMerchantMapper.updateById(merchant);
        } else {
            // 创建商户信息
            AccPlatformMerchant platformMerchant = new AccPlatformMerchant();
            platformMerchant.setSysCode(colonelDTO.getSysCode());
            platformMerchant.setMerchantType(MerchantTypeEnum.COLONEL.getType());
            platformMerchant.setMerchantId(colonelId);
            platformMerchant.setMchStatus(StringPool.ONE);
            platformMerchant.setPlatform(PayChannelEnum.WX_B2B_PAY.getCode());
            platformMerchant.setAltMchNo(reqVO.getOpenid());
            platformMerchant.setAltMchName(reqVO.getNickName());
            platformMerchant.setAuditStatus(MerchantRegisterStateEnum.AUDITED.getState());
            platformMerchantMapper.insert(platformMerchant);
        }

        AppletBaseConfigDTO configDTO = accountCacheService.getAppletBaseConfigDTO(colonelDTO.getSysCode());
        // 添加分账方
        String res = WxUtils.addProfitAccount(reqVO.getNickName(), reqVO.getOpenid(), RedisWxTokenUtil.getAppletAccessToken(configDTO.getAppId()));
        log.info("业务员直接添加分账方结果 {}", res);


        // 微信商户额外信息
        AccPlatformMerchantWxb2b merchantWxb2b = accPlatformMerchantWxb2bMapper.selectByMerchantTypeAndMerchantId(MerchantTypeEnum.COLONEL.getType(), colonelId);
        if (Objects.isNull(merchantWxb2b)) {
            merchantWxb2b = new AccPlatformMerchantWxb2b();
            merchantWxb2b.setSysCode(colonelDTO.getSysCode());
            merchantWxb2b.setAutoWithdraw(NumberPool.INT_ONE);
            merchantWxb2b.setMerchantType(MerchantTypeEnum.COLONEL.getType());
            merchantWxb2b.setMerchantId(colonelId);
            merchantWxb2b.setMinWithdrawAmt(BigDecimal.ZERO);
            merchantWxb2b.setProfile(reqVO.getProfile());
            merchantWxb2b.setNickName(reqVO.getNickName());
            accPlatformMerchantWxb2bMapper.insert(merchantWxb2b);
        } else {
            merchantWxb2b.setAutoWithdraw(NumberPool.INT_ONE);
            merchantWxb2b.setMerchantType(MerchantTypeEnum.COLONEL.getType());
            merchantWxb2b.setMerchantId(colonelId);
            merchantWxb2b.setMinWithdrawAmt(BigDecimal.ZERO);
            merchantWxb2b.setProfile(reqVO.getProfile());
            merchantWxb2b.setNickName(reqVO.getNickName());
            accPlatformMerchantWxb2bMapper.updateById(merchantWxb2b);
        }
    }

    @Override
    public AccPlatformMerchantWxb2b getPlatformMerchant(Long merchantId, String merchantType) {
        return accPlatformMerchantWxb2bMapper.selectByMerchantTypeAndMerchantId(merchantType, merchantId);
    }
}
