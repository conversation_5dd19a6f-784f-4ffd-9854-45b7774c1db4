package com.zksr.account.client.hlb;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.account.client.TransferClient;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.TransferStatusEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.cert.HlbCertUtil;
import com.zksr.system.api.model.dto.HlbPayConfig;
import org.apache.commons.lang.StringEscapeUtils;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝账户交易
 * @date 2024/3/11 14:36
 */
public class HlbTransferClient implements TransferClient {

    private HlbTransferSdkClient client;

    @Override
    public String getPlatform() {return PayChannelEnum.HLB.getCode();}

    @Override
    public void setConfig(String config) throws Exception {
        this.client = new HlbTransferSdkClient();
        /**
         * 初始化支付工具
         */
        HlbPayConfig payConfig = JSON.parseObject(config, HlbPayConfig.class);
        this.client.setPubCert(HlbCertUtil.getX509Certificate(payConfig.getPubCert()));
        this.client.setMerchantPrivateKey(HlbCertUtil.getPrivateKeyStr(payConfig.getMerchantPrivateKey(), payConfig.getPass()));
        this.client.setNotifyUrl(payConfig.getNotifyUrl());
        this.client.setMerchantNo(payConfig.getMerchantNo());
    }

    @Override
    public TransferSubmitRespDTO transferSubmit(TransferSubmitReqVO reqVO) {
        return client.createTransfer(reqVO);
    }

    @Override
    public TransferSubmitRespDTO parseSubmitNotify(Map<String, String> params, String body) {
        TransferSubmitRespDTO resp = new TransferSubmitRespDTO();
        resp.setTransferNo(params.get("rt7_orderId"));
        resp.setStatus("0000".equals(params.get("rt5_retCode")) ? TransferStatusEnum.SUCCESS.getStatus() : TransferStatusEnum.FAIL.getStatus());
        resp.setMsg(params.get("rt6_retMsg"));
        resp.setOutTradeNo(params.get("rt9_serialNumber"));
        // 解析转账回调
        return resp;
    }

    @Override
    public TransferSettleRespDTO transferSettle(TransferSettleReqVO reqVO) {
        return client.transferSettle(reqVO);
    }

    @Override
    public TransferSettleRespDTO parseSettleNotify(Map<String, String> params, String body) {
        JSONObject notifyBody = JSON.parseObject(body);
        // 解析提现回调
        TransferSettleRespDTO resp = new TransferSettleRespDTO();
        resp.setStatus("0000".equals(notifyBody.getString("rt5_retCode")) ? TransferStatusEnum.SUCCESS.getStatus() : TransferStatusEnum.FAIL.getStatus());
        resp.setMsg(notifyBody.getString("rt6_retMsg"));
        // 都是单笔结算
        JSONObject record = notifyBody.getJSONArray("rt4_settleRecords").getJSONObject(0);
        resp.setWithdrawNo(record.getString("orderId"));
        resp.setOutTradeNo(record.getString("orderId"));
        if (record.containsKey("settleFee")) {
            resp.setSettleFee(new BigDecimal(record.getString("settleFee")));
        }
        /**
         * INIT 已接收
         * DOING 处理中
         * DONE 成功
         * FAILED 失败
         * MANUAL人工处理
         */
        if ("INIT".equals(record.getString("orderStatus"))) {
            // 不会回调逻辑
            resp.setStatus(TransferStatusEnum.PROCESSING.getStatus());
        } else if ("DOING".equals(record.getString("orderStatus"))) {
            // 不会回调逻辑
            resp.setStatus(TransferStatusEnum.PROCESSING.getStatus());
        } else if ("DONE".equals(record.getString("orderStatus"))) {
            resp.setStatus(TransferStatusEnum.SUCCESS.getStatus());
            resp.setAccountName(record.getString("accountName"));
            resp.setAccountNo(record.getString("accountNo"));
        } else if ("FAILED".equals(record.getString("orderStatus"))) {
            resp.setStatus(TransferStatusEnum.FAIL.getStatus());
            resp.setMsg(record.getString("reason"));
        } else if ("MANUAL".equals(record.getString("orderStatus"))) {
            resp.setStatus(TransferStatusEnum.SUCCESS.getStatus());
        }
        resp.setOutTradeNo(RandomUtil.randomNumbers(18));
        // 解析转账回调
        return resp;
    }

}
