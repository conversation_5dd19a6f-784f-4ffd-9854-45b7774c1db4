
package com.zksr.account.controller.recharge.excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/25 10:43
 */
@Data
public class RechargeImportExcelRespVO {

    @ApiModelProperty("门店ID")
    private String branchId;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("充值金额")
    private String rechargeAmt;

    @ApiModelProperty("门店当前余额")
    private BigDecimal balance;
}
