package com.zksr.account.mapper;

import com.zksr.account.controller.withdraw.vo.AccWithdrawBillPageReqVO;
import com.zksr.account.domain.AccWithdrawBill;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * 提现对账单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-4
 */
@Mapper
public interface AccWithdrawBillMapper extends BaseMapperX<AccWithdrawBill> {
    default PageResult<AccWithdrawBill> selectPage(AccWithdrawBillPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccWithdrawBill>()
                .likeIfPresent(AccWithdrawBill::getAltNo, reqVO.getAltNo())
                .eqIfPresent(AccWithdrawBill::getPlatform, reqVO.getPlatform())
                .betweenIfPresent(AccWithdrawBill::getFinishTime, reqVO.getFinishStartTime(), reqVO.getFinishEndTime())
                .orderByDesc(AccWithdrawBill::getWithdrawBillId));
    }

   int countWithdrawBillsByDateAndAltNo(@Param("dataFormatted") String dataFormatted,@Param("altNo") String altNo);

    void deleteWithdrawBillsByDateAndAltNo(@Param("dataFormatted") String dataFormatted,@Param("altNo") String altNo);
}
