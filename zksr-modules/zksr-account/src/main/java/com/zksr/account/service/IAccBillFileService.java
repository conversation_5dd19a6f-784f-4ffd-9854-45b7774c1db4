package com.zksr.account.service;

import com.zksr.account.api.transfer.dto.AccBillFileDTO;
import com.zksr.account.domain.AccBillFile;

import java.util.List;

/**
 * 商户账单文件备份Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface IAccBillFileService {
    /**
     * 保存商户账单文件备份
     *
     * @return 结果
     */
    public int insertBillFileRecord(AccBillFileDTO accBillFile);

    /**
     * 判断文件记录是否已经存在
     * @param altNo
     * @param data
     * @return
     */
    List<String> checkBillFileRecordExists(String altNo, String data);
}
