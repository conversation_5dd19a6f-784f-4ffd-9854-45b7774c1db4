package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.pool.NumberPool;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 账户充值单对象 acc_recharge
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@TableName(value = "acc_recharge")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccRecharge extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 账户充值单id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long rechargeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 充值类型;0-入驻商充值 1-门店充值*/
    @Excel(name = "充值类型;0-入驻商充值 1-门店充值")
    private Integer chargeType;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String payWay;

    /** 充值金额 */
    @Excel(name = "充值金额")
    private BigDecimal rechargeAmt;

    /** 手续费金额 */
    @Excel(name = "手续费金额")
    private BigDecimal fee;

    /** 充值方类型;supplier-入驻商  branch-门店 */
    @Excel(name = "充值方类型;supplier-入驻商  branch-门店")
    private String rechargeMerchantType;

    /** 充值方id */
    @Excel(name = "充值方id")
    private Long rechargeMerchantId;

    /** 收款方类型;supplier-入驻商 partner-平台商 */
    @Excel(name = "收款方类型;supplier-入驻商 partner-平台商 dc-运营商")
    private String receiveMerchantType;

    /** 收款方id;当收款方为软件商时，不需要记录此值 */
    @Excel(name = "收款方id;当收款方为软件商时，不需要记录此值")
    private Long receiveMerhcantId;

    /** 充值状态: 0-未支付,1-已支付 */
    @Excel(name = "充值状态: 0-未支付,1-已支付")
    private Integer state;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

    /** 充值单号 */
    @Excel(name = "充值单号")
    private String rechargeNo;

    /** 充值方案ID */
    @Excel(name = "充值方案ID")
    private Long rechargeSchemeId;

    /** 充值方案内容 */
    @Excel(name = "充值方案内容")
    private String schemeRuleJson;

    /** 充值赠送金额 */
    @Excel(name = "充值赠送金额")
    private BigDecimal giveAmt;

    /** 充值来源,app-用户端 pc-后台 */
    @Excel(name = "充值来源,app-用户端 pc-后台")
    private String source;

    /**
     * 是否入驻商充值单
     * @return  true-是的, false-不是
     */
    @JsonIgnore
    public boolean isSupplier() {
        return chargeType == NumberPool.INT_ZERO;
    }
}
