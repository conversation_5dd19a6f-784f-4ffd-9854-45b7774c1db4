package com.zksr.account.controller.recharge;

import cn.hutool.core.util.StrUtil;
import com.zksr.account.controller.recharge.excel.RechargeImportExcelRespVO;
import com.zksr.account.controller.recharge.excel.RechargeImportExcelVO;
import com.zksr.account.controller.recharge.vo.*;
import com.zksr.account.convert.recharge.AccRechargeImportConvert;
import com.zksr.account.convert.recharge.AccRechargeImportDtlConvert;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccRechargeImport;
import com.zksr.account.domain.AccRechargeImportDtl;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccRechargeImportDtlService;
import com.zksr.account.service.IAccRechargeImportService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.poi.ExcelUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 后台导入充值Controller
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Api(tags = "管理后台 - 后台导入充值接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/rechargeImport")
public class AccRechargeImportController {

    @Autowired
    private IAccRechargeImportService accRechargeImportService;

    @Autowired
    private IAccRechargeImportDtlService rechargeImportDtlService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Resource
    private PartnerApi partnerApi;

    @Resource
    private BranchApi branchApi;

    @Autowired
    private IAccAccountService accountService;

    /**
     * 新增后台导入充值
     */
    @ApiOperation(value = "新增后台导入充值", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "后台导入充值", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccRechargeImportSaveReqVO createReqVO) {
        return success(accRechargeImportService.insertAccRechargeImport(createReqVO));
    }

    /**
     * 修改后台导入充值
     */
    @ApiOperation(value = "修改后台导入充值", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "后台导入充值", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AccRechargeImportSaveReqVO updateReqVO) {
        accRechargeImportService.updateAccRechargeImport(updateReqVO);
        return success(true);
    }

    /**
     * 删除后台导入充值
     */
    @ApiOperation(value = "删除后台导入充值", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "后台导入充值", businessType = BusinessType.DELETE)
    @DeleteMapping("/{rechargeImportIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] rechargeImportIds) {
        accRechargeImportService.deleteAccRechargeImportByRechargeImportIds(rechargeImportIds);
        return success(true);
    }

    /**
     * 获取后台导入充值详细信息
     */
    @ApiOperation(value = "获得后台导入充值详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{rechargeImportId}")
    public CommonResult<AccRechargeImportRespVO> getInfo(@PathVariable("rechargeImportId") Long rechargeImportId) {
        AccRechargeImport accRechargeImport = accRechargeImportService.getAccRechargeImport(rechargeImportId);
        List<AccRechargeImportDtlRespVO> dtlRespList = rechargeImportDtlService.getDtlsRespVO(rechargeImportId);
        dtlRespList.forEach(dtl -> {
            BranchDTO branchDTO = accountCacheService.getBranchDTO(dtl.getBranchId());
            dtl.setBranchName(branchDTO.getBranchName());
            dtl.setContactPhone(branchDTO.getContactPhone());
        });
        // 组装数据
        AccRechargeImportRespVO importRespVO = AccRechargeImportConvert.INSTANCE.convert(accRechargeImport);
        importRespVO.setDtlList(dtlRespList);
        // 获取平台商联系手机号, 数据脱敏
        PartnerDto partnerDto = partnerApi.getBySysCode(accRechargeImport.getSysCode()).getCheckedData();
        importRespVO.setAuditPhone(StrUtil.hide(partnerDto.getContactPhone(),3, 7));
        return success(importRespVO);
    }

    /**
     * 分页查询后台导入充值
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得后台导入充值分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccRechargeImportRespVO>> getPage(@Valid AccRechargeImportPageReqVO pageReqVO) {
        PageResult<AccRechargeImportRespVO> pageResult = accRechargeImportService.getAccRechargeImportPage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 作废充值批次
     */
    @ApiOperation(value = "作废充值批次", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "后台导入充值", businessType = BusinessType.UPDATE)
    @PostMapping("/disable")
    public CommonResult<Boolean> disable(@Valid @RequestBody AccRechargeImportAuditReqVO reqVO) {
        accRechargeImportService.disable(reqVO);
        return success(true);
    }

    /**
     * 审核通过
     */
    @ApiOperation(value = "审核通过", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "后台导入充值", businessType = BusinessType.UPDATE)
    @PostMapping("/enable")
    public CommonResult<Boolean> enable(@Valid @RequestBody AccRechargeImportAuditReqVO reqVO) {
        accRechargeImportService.enable(reqVO);
        return success(true);
    }

    /**
     * 获取审核验证码
     */
    @ApiOperation(value = "获取审核验证码", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.SMS)
    @RequiresPermissions(Permissions.SMS)
    @Log(title = "后台导入充值", businessType = BusinessType.OTHER)
    @GetMapping("/smsCode")
    public CommonResult<Boolean> smsCode(Long rechargeImportId) {
        accRechargeImportService.smsCode(rechargeImportId);
        return success(true);
    }

    @PostMapping("/importTemplate")
    @ApiOperation(value = "门店导入数据模版", httpMethod = HttpMethod.POST)
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<RechargeImportExcelVO> util = new ExcelUtil<RechargeImportExcelVO>(RechargeImportExcelVO.class);
        util.importTemplateExcel(response);
    }

    @ApiOperation(value = "门店导入数据解析", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.IMPORT)
    @Log(title = "后台导入充值数据解析", businessType = BusinessType.IMPORT)
    @RequiresPermissions(Permissions.IMPORT)
    @PostMapping("/importData")
    public CommonResult<List<RechargeImportExcelRespVO>> importData(MultipartFile file) throws Exception {
        ExcelUtil<RechargeImportExcelVO> util = new ExcelUtil<>(RechargeImportExcelVO.class);
        List<RechargeImportExcelVO> excelVOList = util.importExcel(file.getInputStream(), 1);
        List<RechargeImportExcelRespVO> respList = AccRechargeImportConvert.INSTANCE.convertExcelRespVO(excelVOList);
        respList.forEach(item -> {
            BranchDTO branchDTO = null;
            if (StringUtils.isNotEmpty(item.getBranchId())) {
                branchDTO = accountCacheService.getBranchDTO(Long.valueOf(item.getBranchId()));
            } else {
                List<Long> branchList = branchApi.getBranchIdListByBranchName(item.getBranchName(), SecurityUtils.getLoginUser().getSysCode()).getCheckedData();
                if (!branchList.isEmpty()) {
                    branchDTO = accountCacheService.getBranchDTO(branchList.get(0));
                }
            }
            if (Objects.isNull(branchDTO)) {
                item.setBranchId("-1");
                item.setBranchName(item.getBranchName() + ",门店不存在");
                return;
            }
            item.setContactPhone(branchDTO.getContactPhone());
            item.setBranchName(branchDTO.getBranchName());
            // 储值余额
            AccAccount account = accountService.getAccountByMerchantIdAndType(branchDTO.getBranchId(), MerchantTypeEnum.BRANCH.getType(), PayChannelEnum.WALLET.getCode(), NumberPool.INT_ZERO);
            if (Objects.nonNull(account)) {
                item.setBalance(account.getValidAccountAmt());
            }
        });
        // 收集错误数据
        StringBuilder failureMsg = new StringBuilder();
        for (int i = 0; i < respList.size(); i++) {
            RechargeImportExcelRespVO respVO = respList.get(i);
            if (StringUtils.isEmpty(respVO.getBranchId()) || Long.parseLong(respVO.getBranchId()) <= 0L) {
                failureMsg.append(StringUtils.format("<br/>第{}行, 门店数据异常", i + 1));
            }
        }
        if (failureMsg.length() > 0) {
            throw new ServiceException(failureMsg.toString());
        }
        return success(respList);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "recharge:rechargeImport:add";
        /**
         * 编辑
         */
        public static final String EDIT = "recharge:rechargeImport:edit";
        /**
         * 删除
         */
        public static final String DELETE = "recharge:rechargeImport:remove";
        /**
         * 列表
         */
        public static final String LIST = "recharge:rechargeImport:list";
        /**
         * 查询
         */
        public static final String GET = "recharge:rechargeImport:query";
        /**
         * 短信验证码
         */
        public static final String SMS = "recharge:rechargeImport:sms";
        /**
         * 停用
         */
        public static final String DISABLE = "recharge:rechargeImport:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "recharge:rechargeImport:enable";
        /**
         * 导入
         */
            public static final String IMPORT = "recharge:rechargeImport:import";
    }
}
