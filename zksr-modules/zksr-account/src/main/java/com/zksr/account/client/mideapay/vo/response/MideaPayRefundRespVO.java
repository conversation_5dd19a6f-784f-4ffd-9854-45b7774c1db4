package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
public class MideaPayRefundRespVO extends MideaPayBaseRespVO {
    /**
     * 退款订单号
     */
    @JsonProperty("out_refund_no")
    private String outRefundNo;

    /**
     * 订单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 平台退款订单号
     */
    @JsonProperty("refund_no")
    private String refundNo;

    /**
     * 退款订单创建时间
     */
    @JsonProperty("refund_accept_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String refundAcceptTime;




    /**
     * 货币类型
     */
    @JsonProperty("currency_type")
    private String currencyType;

    /**
     * 退款总金额
     */
    @JsonProperty("refund_total_amount")
    private String refundTotalAmount;

    /**
     * 退款金额
     */
    @JsonProperty("refund_amount")
    private String refundAmount;

    /**
     * 营销金额退款
     */
    @JsonProperty("refund_market_amount")
    private String refundMarketAmount;


    /**
     * 订单状态,
     * NOT_EXIST：订单不存在
     * WAIT_PAY：未退款
     * PAYING：退款中
     * SUCCESS：退款成功
     * FAIL：退款失败
     */
    @JsonProperty("refund_status")
    private String refundStatus;

    /**
     * 状态描述
     */
    @JsonProperty("refund_status_info")
    private String refundStatusInfo;

    /**
     * 到账状态
     */
    @JsonProperty("arrival_status")
    private String arrivalStatus;

    /**
     * 商户自定义信息
     */
    @JsonProperty("attach")
    private String attach;

}
