package com.zksr.account.controller.flow.vo;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 账户流水对象 acc_account_flow
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@ApiModel("账户流水 - acc_account_flow分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccAccountFlowPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 账户流水id */
    @ApiModelProperty(value = "支付平台(数据字典)")
    private Long accountFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 账户id */
    @Excel(name = "账户id")
    @ApiModelProperty(value = "账户id")
    private Long accountId;

    @ApiModelProperty("收入/支出, 0-收入,1-支出")
    private Integer ioType;

    /** 业务类型(待枚举，根据busi_type找busi出处) */
    @Excel(name = "业务类型(待枚举，根据busi_type找busi出处)")
    @ApiModelProperty(value = "业务类型(待枚举，根据busi_type找busi出处)")
    private String busiType;

    /** 业务id */
    @Excel(name = "业务id")
    @ApiModelProperty(value = "业务id")
    private Long busiId;

    /** 影响字段 */
    @Excel(name = "影响字段")
    @ApiModelProperty(value = "影响字段")
    private String busiFields;

    /** 处理状态 0-未处理  1-已处理 */
    @Excel(name = "处理状态 0-未处理  1-已处理")
    @ApiModelProperty(value = "处理状态 0-未处理  1-已处理")
    private Integer processFlag;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty("流水执行开始时间")
    private Date processStartTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty("流水执行结束时间")
    private Date processEndTime;

    /**
     * 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(required = true, value = "商户类型")
    private String merchantType;

    /**
     * 商户id 根据操作账户类型不同传入
     */
    @ApiModelProperty(required = true, value = "商户id")
    private Long merchantId;
}
