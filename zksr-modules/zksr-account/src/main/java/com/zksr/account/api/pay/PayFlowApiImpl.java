package com.zksr.account.api.pay;

import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.model.pay.vo.PayRefundQueryVO;
import com.zksr.account.service.IAccPayFlowService;
import com.zksr.account.service.pay.PayOrderService;
import com.zksr.common.core.domain.vo.openapi.AccPayReceiptReqDTO;
import com.zksr.common.core.domain.vo.openapi.ReceiptPayOpenDTO;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付流水处理
 * @date 2024/10/11 11:27
 */
@ApiIgnore
@RestController
@InnerAuth
public class PayFlowApiImpl implements PayFlowApi{

    @Autowired
    private IAccPayFlowService payFlowService;

    @Autowired
    private PayOrderService payOrderService;

    @Override
    public CommonResult<List<PayRefundQueryVO>> getWxB2bRefundProcessAgainList(Long minId) {
        return success(payFlowService.getWxB2bRefundProcessAgainList(minId));
    }

    @Override
    public CommonResult<Boolean> tryAgainB2bRefundProcess(PayRefundQueryVO refundQueryVO) {
        AccPayFlow payFlow = payFlowService.getByOrderPayFlowSuccessFlag(refundQueryVO.getTradeNo());
        if (Objects.nonNull(payFlow)) {
            refundQueryVO.setPayFlowId(payFlow.getPayFlowId());
        }
        payOrderService.processDelayRefundQuery(refundQueryVO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<List<ReceiptPayOpenDTO>> getOrderPayInfoListBySyncOrder(AccPayReceiptReqDTO reqDTO) {
        return success(payFlowService.getOrderPayInfoListBySyncOrder(reqDTO));
    }

    @Override
    public CommonResult<PayFlowDTO> getAccPayFlowOrder(Long payFlowId) {
        return success(HutoolBeanUtils.toBean(payFlowService.getAccPayFlow(payFlowId), PayFlowDTO.class));
    }

    @Override
    public CommonResult<PayFlowDTO> getAccPayFlowAfter(String refundNo) {
        return success(HutoolBeanUtils.toBean(payFlowService.getByOrderRefundFlow(refundNo), PayFlowDTO.class));
    }

    @Override
    public CommonResult<PayFlowDTO> getPaySuccessFlow(String tradeNo) {
        return success(HutoolBeanUtils.toBean(payFlowService.getByOrderPayFlowSuccessFlag(tradeNo), PayFlowDTO.class));
    }

    public CommonResult<PayFlowDTO> getByOrderPayFlow(String orderNo){
        return success(HutoolBeanUtils.toBean(payFlowService.getByOrderPayFlow(orderNo), PayFlowDTO.class));
    }

    public CommonResult<List<PayFlowDTO>> getByOrdersPayFlow(List<String> orderNos){
        return success(HutoolBeanUtils.toBean(payFlowService.selectByOrdersPayFlow(orderNos), PayFlowDTO.class));
    }

    @Override
    public CommonResult<List<PayFlowDTO>> getWxB2bRechargeDivideFlow(Long minPayFlowId) {
        return success(HutoolBeanUtils.toBean(payFlowService.getWxB2bRechargeDivideFlow(minPayFlowId), PayFlowDTO.class));
    }
}
