package com.zksr.account.controller.transfer.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 账户转账单对象 acc_transfer
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@ToString
@Data
@ApiModel("账户转账单 - acc_transfer分页 Request VO")
public class AccTransferSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 订单结算id */
    @Excel(name = "订单结算id")
    @ApiModelProperty(value = "订单结算id")
    private Long settleId;

    /** 转出方账户id */
    @Excel(name = "转出方账户id")
    @ApiModelProperty(value = "转出方账户id")
    @Min(1)
    private Long sourceAccountId;

    /** 转入方账户id */
    @Excel(name = "转入方账户id")
    @ApiModelProperty(value = "转入方账户id")
    @Min(1)
    private Long targetAccountId;

    /** 转出方账户id */
    @ApiModelProperty(value = "转出方账户id", required = true, notes = "注意这里是账户ID, 例如supplierId")
    @NotNull(message = "转出方账户id不能未空")
    private Long sourceMerchantId;

    /**
     * 转出方账户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty(value = "转出方账户类型", required = true)
    private String sourceMerchantType;

    /** 转入方账户id */
    @ApiModelProperty(value = "转入方账户id", required = true, notes = "转入方账户IDM, 例如 dcId")
    @NotNull(message = "转入方账户id不能未空")
    private Long targetMerchantId;

    /**
     * 转出方账户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty(value = "转入方账户类型", required = true)
    private String targetMerchantType;

    /** 转账单号 */
    @Excel(name = "转账单号")
    @ApiModelProperty(value = "转账单号")
    private String transferNo;

    /** 转账金额 */
    @Excel(name = "转账金额")
    @ApiModelProperty(value = "转账金额")
    private BigDecimal transferAmt;

    /** 转账发起方解除金额 */
    @Excel(name = "转账发起方解除金额")
    @ApiModelProperty(value = "转账发起方解除金额")
    private BigDecimal settleAmt;

    /** 支付平台(数据字典);从订单 */
    @Excel(name = "支付平台(数据字典);从订单")
    @ApiModelProperty(value = "支付平台(数据字典);从订单")
    private String platform;

}
