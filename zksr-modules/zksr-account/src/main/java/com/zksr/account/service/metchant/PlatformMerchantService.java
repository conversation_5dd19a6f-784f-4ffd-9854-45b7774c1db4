package com.zksr.account.service.metchant;

import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRegisterReqVO;
import com.zksr.account.api.platformMerchant.vo.PlatformMerchantRegisterSaveRespVO;
import com.zksr.account.client.MerchantClient;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoReqVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoRespVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bSaveReqVO;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.common.core.enums.PayChannelEnum;

/**
 * <AUTHOR>
 * @time 2024/9/19
 * @desc
 */
public interface PlatformMerchantService {

    public PayChannelEnum channel();

    /**
     * 更新最新进件状态
     * @param registerMerchant  第三方进件信息
     * @param platformMerchant  系统第三方进件信息
     * @param merchantClient    商户客户端
     */
    void syncData(PayPlatformMerchantVO registerMerchant, AccPlatformMerchant platformMerchant, MerchantClient merchantClient);


    /**
     * 保存进件信息
     * @param reqVO                 进件请求数据
     * @param respVO                进件请求第三方返回数据
     * @return  第三方进件信息ID
     */
    Long saveRegisterInfo(AccPlatformMerchantRegisterReqVO reqVO, PlatformMerchantRegisterSaveRespVO respVO);


    /**
     * 获取微信商户配置
     *
     * @param reqVO
     * @return
     */
    default AccPlatformMerchantWxb2bInfoRespVO getUpdateWxB2bPayConfig(AccPlatformMerchantWxb2bInfoReqVO reqVO) {
        return null;
    }

    /**
     * 更新微信商户配置
     *
     * @param saveReqVO
     * @return
     */
    default Long updateWxB2bPayConfig(AccPlatformMerchantWxb2bSaveReqVO saveReqVO) {
        return null;
    }

    /**
     * 直接保存支付平台商户信息
     * @param platformMerchant  商户信息
     * @return
     */
    default Long directSaveMerchant(PlatformMerchantDTO platformMerchant) {
        return null;
    }

}
