package com.zksr.account.controller.flow.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 支付平台提现流水对象 acc_withdraw_flow
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Data
@ApiModel("支付平台提现流水 - acc_withdraw_flow分页 Request VO")
public class AccWithdrawFlowSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 提现流水id */
    @ApiModelProperty(value = "实际到账金额")
    private Long withdrawFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 业务类型(数据字典) */
    @Excel(name = "业务类型(数据字典)")
    @ApiModelProperty(value = "业务类型(数据字典)")
    private String busiType;

    /** 业务单据id */
    @Excel(name = "业务单据id")
    @ApiModelProperty(value = "业务单据id")
    private Long busiId;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;

    /** 提现方商户类型 */
    @Excel(name = "提现方商户类型")
    @ApiModelProperty(value = "提现方商户类型")
    private String merchantType;

    /** 提现方商户id */
    @Excel(name = "提现方商户id")
    @ApiModelProperty(value = "提现方商户id")
    private Long merchantId;

    /** 提现方分账方商户编号 */
    @Excel(name = "提现方分账方商户编号")
    @ApiModelProperty(value = "提现方分账方商户编号")
    private String altMchNo;

    /** 提现方分账方商户名 */
    @Excel(name = "提现方分账方商户名")
    @ApiModelProperty(value = "提现方分账方商户名")
    private String altMchName;

    /** 提现单号;传给支付平台的 */
    @Excel(name = "提现单号;传给支付平台的")
    @ApiModelProperty(value = "提现单号;传给支付平台的")
    private String withdrawNo;

    /** 提现金额 */
    @Excel(name = "提现金额")
    @ApiModelProperty(value = "提现金额")
    private BigDecimal withdrawAmt;

    /** 提现状态;0-已申请 1-处理中 2-审核拒绝 3-已完成 4-提现失败 */
    @Excel(name = "提现状态;0-已申请 1-处理中 2-审核拒绝 3-已完成 4-提现失败")
    @ApiModelProperty(value = "提现状态;0-已申请 1-处理中 2-审核拒绝 3-已完成 4-提现失败")
    private Integer state;

    /** 申请时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "申请时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "申请时间")
    private Date initTime;

    /** 处理时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "处理时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "处理时间")
    private Date processingTime;

    /** 完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 失败原因 */
    @Excel(name = "失败原因")
    @ApiModelProperty(value = "失败原因")
    private String errorReason;

    /** 到账银行卡号 */
    @Excel(name = "到账银行卡号")
    @ApiModelProperty(value = "到账银行卡号")
    private String bankAccountNo;

    /** 到账银行卡账户名 */
    @Excel(name = "到账银行卡账户名")
    @ApiModelProperty(value = "到账银行卡账户名")
    private String bankAccountName;

    /** 支付平台流水号;一般回调会返回 */
    @Excel(name = "支付平台流水号;一般回调会返回")
    @ApiModelProperty(value = "支付平台流水号;一般回调会返回")
    private String outFlowNo;

    /** 实际到账金额 */
    @Excel(name = "实际到账金额")
    @ApiModelProperty(value = "实际到账金额")
    private BigDecimal remitAmt;

}
