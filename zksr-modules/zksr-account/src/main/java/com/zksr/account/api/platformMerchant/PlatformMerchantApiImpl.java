package com.zksr.account.api.platformMerchant;

import cn.hutool.core.bean.BeanUtil;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantWxb2bDTO;
import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.convert.wxb2b.AccPlatformMerchantWxb2bConvert;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccPlatformMerchantWxb2bService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.metchant.PlatformMerchantService;
import com.zksr.account.service.metchant.impl.WxB2bPlatformMerchantServiceImpl;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.NOT_EXIST_PAY_CONFIG;
import static com.zksr.account.enums.ErrorCodeConstants.PLATFORM_MERCHANT_SERVICE_UNDEFINE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付商户平台操作 - api
 * @date 2024/4/8 15:37
 */
@InnerAuth
@ApiIgnore
@Slf4j
@RestController
public class PlatformMerchantApiImpl implements PlatformMerchantApi {

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccPlatformMerchantWxb2bService merchantWxb2bService;

    @Autowired
    private List<PlatformMerchantService> merchantService;

    @Override
    public CommonResult<Long> savePlatformMerchant(PlatformMerchantDTO platformMerchant) {
        // 验证平台
        PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(platformMerchant.getSysCode());
        if (Objects.isNull(payConfigDTO)) {
            throw exception(NOT_EXIST_PAY_CONFIG);
        }

        AccPlatformMerchantSyncReqVO reqVO = AccPlatformMerchantSyncReqVO.builder()
                .merchantId(platformMerchant.getMerchantId())
                .merchantType(platformMerchant.getMerchantType())
                .platform(platformMerchant.getPlatform())
                .sysCode(platformMerchant.getSysCode())
                .build();

        // 默认跟随平台设置的支付平台方式
        // reqVO.setPlatform(payConfigDTO.getInteriorStoredPayPlatform());

        // 如果是B2B支付, 采用的是直接分账, 平台直接就是指支付平台移植
        Long platformMerchantId;
        if (PayChannelEnum.WX_B2B_PAY.getCode().equals(platformMerchant.getPlatform())) {
            reqVO.setPlatform(payConfigDTO.getStoreOrderPayPlatform());
            // 设置进件平台
            platformMerchant.setPlatform(reqVO.getPlatform());
            platformMerchantId = getMerchantService(reqVO.getPlatform()).directSaveMerchant(platformMerchant);
        } else {
            platformMerchantId = platformMerchantService.savePlatformMerchant(BeanUtil.toBean(platformMerchant, AccPlatformMerchantSaveReqVO.class));
        }

        // 保存进件信息后同步一下数据
        try {
            platformMerchantService.syncData(reqVO);
        } catch (Exception e) {
            log.error(" 同步商户信息失败，", e);
        }
        return CommonResult.success(platformMerchantId);
    }

    @Override
    public CommonResult<AccPlatformMerchantRespVO> getPlatformMerchantRespVO(String merchantType, Long merchantId, Long sysCode) {
        PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfigDto)) {
            // 大区支付配置不存
            return null;
        }
        // 返回数据
        return CommonResult.success(BeanUtil.toBean(platformMerchantService.getPlatformMerchant(
                merchantId,
                merchantType,
                MerchantTypeEnum.isSupplier(merchantType) ? payConfigDto.getStoreOrderPayPlatform() : payConfigDto.getInteriorStoredPayPlatform(),null
        ), AccPlatformMerchantRespVO.class));
    }

    @Override
    public CommonResult<PlatformMerchantDTO> getPlatformMerchant(String merchantType, Long merchantId, Long sysCode) {
        PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfigDto)) {
            // 大区支付配置不存
            return CommonResult.success(null);
        }

        // 如果是入驻商就使用支付体系平台, 其他的储值体系
        String platform = MerchantTypeEnum.isSupplier(merchantType) ? payConfigDto.getStoreOrderPayPlatform() : payConfigDto.getInteriorStoredPayPlatform();

        // 如果是B2B支付, 强制B2B
        if (PayChannelEnum.isB2b(payConfigDto.getStoreOrderPayPlatform())) {
            platform = PayChannelEnum.WX_B2B_PAY.getCode();
        }

        // 返回数据
        return CommonResult.success(BeanUtil.toBean(platformMerchantService.getPlatformMerchant(
                merchantId,
                merchantType,
                platform,
                sysCode
        ), PlatformMerchantDTO.class));
    }

    @Override
    public CommonResult<PlatformMerchantDTO> getPlatformMerchant(String merchantType, Long merchantId, String payPlatform) {
        // 返回数据
        return CommonResult.success(BeanUtil.toBean(
                platformMerchantService.getPlatformMerchant(
                        merchantId,
                        merchantType,
                        payPlatform,null
                ), PlatformMerchantDTO.class));
    }

    @Override
    public CommonResult<List<PlatformMerchantNameAndKeyDTO>> getPlatformMerchantNameAndKey(String merchantType, Long sysCode, String payPlatform) {
        // 返回数据
        return CommonResult.success(platformMerchantService.getPlatformMerchantNameAndKey(merchantType,sysCode,payPlatform));
    }

    @Override
    public CommonResult<PlatformMerchantWxb2bDTO> getWxB2bPlatformMerchant(Long merchantId, String merchantType) {
        return CommonResult.success(AccPlatformMerchantWxb2bConvert.INSTANCE.convertDTO(merchantWxb2bService.getPlatformMerchant(merchantId, merchantType)));
    }

    @Override
    public CommonResult<Boolean> updatePlatformMerchant(PlatformMerchantDTO platformMerchant) {
        platformMerchantService.updateAccPlatformMerchant(BeanUtil.toBean(platformMerchant, AccPlatformMerchantSaveReqVO.class));
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Long> register(AccPlatformMerchantRegisterReqVO reqVO) {
        Long merchantId = platformMerchantService.register(reqVO);
        // 注册完成以后去同步一下信息
        platformMerchantService.syncData(
                AccPlatformMerchantSyncReqVO.builder()
                        .merchantId(reqVO.getMerchantId())
                        .merchantType(reqVO.getMerchantType())
                        .platform(reqVO.getPlatform())
                        .build()
        );
        return CommonResult.success(merchantId);
    }

    @Override
    public CommonResult<Long> uploadPic(AccPlatformMerchantUploadPicReqVO reqVO) {
        platformMerchantService.uploadPic(reqVO);
        return CommonResult.success(NumberPool.LONG_ONE);
    }

    @Override
    public CommonResult<Long> updateRegister(AccPlatformMerchantRegisterReqVO reqVO) {
        return CommonResult.success(platformMerchantService.updateRegister(reqVO));
    }

    @Override
    public CommonResult<Long> processMerchantStatus(Long minId) {
        return CommonResult.success(platformMerchantService.processMerchantStatus(minId));
    }

    @Override
    public CommonResult<Boolean> updateWxB2bColonelOpenidBind(WxB2bColonelOpenidBindReqVO reqVO) {
        merchantWxb2bService.updateWxB2bColonelOpenidBind(reqVO);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<List<Long>> getMerchantIdByAltNo(String merchantType, String altNo, Long sysCode) {
        return CommonResult.success(platformMerchantService.getMerchantIdByAltNo(merchantType, altNo, sysCode));
    }

    @Override
    public CommonResult<List<AccPlatformMerchantRespVO>> getMerchantList(AccPlatformMerchantPageReqVO pageReqVO) {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        PageResult<AccPlatformMerchant> merchantPage = platformMerchantService.getAccPlatformMerchantPage(pageReqVO);
        return CommonResult.success(HutoolBeanUtils.toBean(merchantPage.getList(), AccPlatformMerchantRespVO.class));
    }

    /**
     * 获取支付平台, 不同商户信息, 执行不同操作
     * @param platform  支付平台
     * @return  商户服务
     */
    public PlatformMerchantService getMerchantService(String platform) {
        for (PlatformMerchantService service : merchantService) {
            if (service.channel().getCode().equals(platform)) {
                return service;
            }
        }
        throw exception(PLATFORM_MERCHANT_SERVICE_UNDEFINE);
    }
}
