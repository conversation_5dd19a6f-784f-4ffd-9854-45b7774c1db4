package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.model.pay.vo.PayRefundQueryVO;
import com.zksr.common.core.domain.vo.openapi.AccPayReceiptReqDTO;
import com.zksr.common.core.domain.vo.openapi.ReceiptPayOpenDTO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.controller.flow.vo.AccPayFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccPayFlowSaveReqVO;

import java.util.List;

/**
 * 支付流水Service接口
 *
 * <AUTHOR>
 * @date 2024-03-10
 */
public interface IAccPayFlowService {

    /**
     * 新增支付流水
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAccPayFlow(@Valid AccPayFlowSaveReqVO createReqVO);

    /**
     * 修改支付流水
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccPayFlow(@Valid AccPayFlowSaveReqVO updateReqVO);

    /**
     * 删除支付流水
     *
     * @param payFlowId 支付流水id
     */
    public void deleteAccPayFlow(Long payFlowId);

    /**
     * 批量删除支付流水
     *
     * @param payFlowIds 需要删除的支付流水主键集合
     * @return 结果
     */
    public void deleteAccPayFlowByPayFlowIds(Long[] payFlowIds);

    /**
     * 获得支付流水
     *
     * @param payFlowId 支付流水id
     * @return 支付流水
     */
    public AccPayFlow getAccPayFlow(Long payFlowId);

    /**
     * 获得支付流水分页
     *
     * @param pageReqVO 分页查询
     * @return 支付流水分页
     */
    PageResult<AccPayFlow> getAccPayFlowPage(AccPayFlowPageReqVO pageReqVO);

    /**
     * 验证是否已经支付
     * @param orderNo   交易单号
     * @return
     */
    boolean validRepeatPay(String orderNo);

    /**
     * 根据订单号获取支付流水
     * @param orderNo   交易单号
     * @return
     */
    AccPayFlow getByOrderPayFlow(String orderNo);

    List<AccPayFlow> selectByOrdersPayFlow(List<String> orderNos);

    /**
     * 获取退款流水
     * @param refundNo  退款单号
     * @return
     */
    AccPayFlow getByOrderRefundFlow(String refundNo);

    /**
     * 获取支付成功流水
     * @param orderNo
     * @return
     */
    AccPayFlow getByOrderPayFlowSuccessFlag(String orderNo);

    /**
     * 通过id查询数据
     * @param payFlowId
     * @return
     */
    AccPayFlow getById(Long payFlowId);

    /**
     * 获取B2B支付退款最新状态
     * @param minId 最小ID
     * @return  应处理数据
     */
    List<PayRefundQueryVO> getWxB2bRefundProcessAgainList(Long minId);

    /**
     * 通过支付单号, 查询支付单号关联的有效退款单号
     * @param tradeNo
     * @return
     */
    List<AccPayFlow> getByOrderRefundFlowList(String tradeNo);

    /**
     * 根据（销售/售后）订单获取订单支付信息
     *
     * @param reqVo
     * @return
     */
    List<ReceiptPayOpenDTO> getOrderPayInfoListBySyncOrder(AccPayReceiptReqDTO reqVo);

    /**
     * 获取微信B2B充值分润待分账流水
     * @param minPayFlowId  最小流水号
     * @return
     */
    List<AccPayFlow> getWxB2bRechargeDivideFlow(Long minPayFlowId);

}
