package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 账户付款单对象 acc_pay
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@TableName(value = "acc_pay")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccPay extends BaseEntity{

    private static final long serialVersionUID=1L;

    public static final int PAY_TYPE_PAY = 0;
    public static final int PAY_TYPE_REFUND = 1;
    public static final int PAY_STATE_INIT = 0;
    public static final int PAY_STATE_SUC = 1;

    /** 账户付款单id */
    @TableId(type = IdType.AUTO)
    private Long payId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 支付类型 0-支付 1-退款 */
    @Excel(name = "支付类型 0-支付 1-退款")
    private Integer payType;

    /** 商城订单号 */
    @Excel(name = "商城订单号")
    private String orderNo;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmt;

    /** 售后单号 */
    @Excel(name = "售后单号")
    private String afterNo;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmt;

    /** 账户id */
    @Excel(name = "账户id")
    private Long payAccountId;

    /** 付款状态;0-已提交 1-已完成  订单支付:订单生成完之后，调用支付接口，支付方式为储值支付，校验储值金额是否充足:1.如充足，则生成账户付款单，状态为0-已提交，同步插入一条账户流水并同时更改门店账户，减少门店账户金额，然后付款单状态变更为1-已完成；2.如不充足，则直接返回"门店储值金额不足"，支付失败售后(取消订单)退款:1.调用退款接口，退款方式为储值支付，则生成账户付款单（退款），状态为0-已提交，同步插入一条账户流水并同时增加门店账户金额，然后付款单状态变更为1-已完成； */
    @Excel(name = "付款状态;0-已提交 1-已完成  订单支付:订单生成完之后，调用支付接口，支付方式为储值支付，校验储值金额是否充足:1.如充足，则生成账户付款单，状态为0-已提交，同步插入一条账户流水并同时更改门店账户，减少门店账户金额，然后付款单状态变更为1-已完成；2.如不充足，则直接返回'门店储值金额不足'，支付失败售后(取消订单)退款:1.调用退款接口，退款方式为储值支付，则生成账户付款单", readConverterExp = "退=款")
    private Integer state;

}
