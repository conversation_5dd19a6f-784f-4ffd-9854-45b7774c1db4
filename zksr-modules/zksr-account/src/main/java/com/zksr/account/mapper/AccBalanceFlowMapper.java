package com.zksr.account.mapper;

import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.domain.AccBalanceFlow;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 门店账户余额交易流水
 */
@Mapper
public interface AccBalanceFlowMapper extends BaseMapperX<AccBalanceFlow> {

    // 跟据门店id和操作类型查询余额交易流水
    default List<AccBalanceFlow> selectBalanceFlowList(Long branchId, Integer actionType) {
        return selectList(new LambdaQueryWrapperX<AccBalanceFlow>()
                .eq(AccBalanceFlow::getBranchId, branchId).eq(AccBalanceFlow::getDeleteFlag, NumberPool.INT_ZERO)
                .eqIfPresent(AccBalanceFlow::getActionType, actionType)
                .orderByDesc(AccBalanceFlow::getBalanceFlowId));
    }

    // 分页查询门店余额交易流水
    default PageResult<AccBalanceFlow> selectPage(MemBranchBalanceFlowDTO flowDTO) {
        return selectPage(flowDTO, new LambdaQueryWrapperX<AccBalanceFlow>()
                .eq(AccBalanceFlow::getBranchId, flowDTO.getBranchId()).eq(AccBalanceFlow::getDeleteFlag, NumberPool.INT_ZERO)
                .eqIfPresent(AccBalanceFlow::getActionType, flowDTO.getActionType())
                .orderByDesc(AccBalanceFlow::getBalanceFlowId));
    }
}
