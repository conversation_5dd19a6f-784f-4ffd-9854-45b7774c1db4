package com.zksr.account.controller.divide.vo;

import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)对象 acc_divide_flow
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
@Data
@ApiModel("分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付) - acc_divide_flow Response VO")
public class AccDivideFlowRespVO {
    private static final long serialVersionUID = 1L;

    /** 分账流水id */
    @ApiModelProperty(value = "错误信息(仅失败记录)")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long divideFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;

    /** 1-请求分账  2-请求分账回退 3-分账完成 */
    @Excel(name = "1-请求分账  2-请求分账回退 3-分账完成")
    @ApiModelProperty(value = "1-请求分账  2-请求分账回退 3-分账完成")
    private Integer type;

    /** 支付平台商户订单号 */
    @Excel(name = "支付平台商户订单号")
    @ApiModelProperty(value = "支付平台商户订单号")
    private String outTradeNo;

    /** 支付平台商户退款单号 */
    @Excel(name = "支付平台商户退款单号")
    @ApiModelProperty(value = "支付平台商户退款单号")
    private String outRefundNo;

    /** 支付平台分账发起方商户id */
    @Excel(name = "支付平台分账发起方商户id")
    @ApiModelProperty(value = "支付平台分账发起方商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sourcePlatformMerchantId;

    /** 支付平台分账接收方商户id */
    @Excel(name = "支付平台分账接收方商户id")
    @ApiModelProperty(value = "支付平台分账接收方商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long targetPlatformMerchantId;

    /** 子商户id */
    @Excel(name = "子商户id")
    @ApiModelProperty(value = "子商户id")
    private String mchid;

    /** 分账接收方类型 */
    @Excel(name = "分账接收方类型")
    @ApiModelProperty(value = "分账接收方类型")
    private String payeeType;

    /** 分账接收方账号 */
    @Excel(name = "分账接收方账号")
    @ApiModelProperty(value = "分账接收方账号")
    private String payeeNo;

    /** 分账金额 */
    @Excel(name = "分账金额")
    @ApiModelProperty(value = "分账金额")
    private BigDecimal divideAmt;

    /** 仅type=1, 1: 分账中 2: 分账完成 3：分账失败 */
    @Excel(name = "仅type=1, 1: 分账中 2: 分账完成 3：分账失败")
    @ApiModelProperty(value = "仅type=1, 1: 分账中 2: 分账完成 3：分账失败")
    private Integer divideStatus;

    /** 仅type=2, 1: 分账退回中 2: 分账退回完成 3: 分账退回失败 */
    @Excel(name = "仅type=2, 1: 分账退回中 2: 分账退回完成 3: 分账退回失败")
    @ApiModelProperty(value = "仅type=2, 1: 分账退回中 2: 分账退回完成 3: 分账退回失败")
    private Integer divideReverseStatus;

    /** 错误信息(仅失败记录) */
    @Excel(name = "错误信息(仅失败记录)")
    @ApiModelProperty(value = "错误信息(仅失败记录)")
    private String errMsg;

}
