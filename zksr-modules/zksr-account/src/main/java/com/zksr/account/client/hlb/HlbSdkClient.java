package com.zksr.account.client.hlb;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zksr.account.client.hlb.vo.AppPayAppletOrderReqVO;
import com.zksr.account.client.hlb.vo.AppPayOrderQueryReqVO;
import com.zksr.account.client.hlb.vo.AppPayRefundOrderReqVO;
import com.zksr.account.client.http.HttpClientService;
import com.zksr.account.client.hlb.vo.HlbDelayedSplitQeuryReqVO;
import com.zksr.account.client.hlb.vo.HlbDelayedSplitReqVO;
import com.zksr.account.client.http.HttpClientService;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayInfoDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderQueryVO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.DivideReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayWxB2bDivideRespVO;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayOrderStatusRespEnum;
import com.zksr.common.core.enums.PayRefundStatusEnum;
import com.zksr.common.core.enums.SignatureType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.cert.CommonRequesConstants;
import com.zksr.common.core.utils.cert.HlbCertUtil;
import com.zksr.common.core.utils.cert.SM2NUtils;
import com.zksr.common.core.utils.cert.SM4Utils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝支付
 * @date 2024/3/7 18:13
 */
@Data
@Slf4j
@SuppressWarnings(StringPool.ALL)
public class HlbSdkClient {

    // 在线交易
    public static final String TRANSFER_URL = "https://transfer.trx.helipay.com/trx/app/interface.action";

    // 异步分账
    public static final String DELAYED_SPLIT_URL = "https://pay.trx.helipay.com/trx/delayedSplit/apply";

    // 异步分账查询
    public static final String DELAYED_SPLIT_QUERY_URL = "https://pay.trx.helipay.com/trx/delayedSplit/apply/query";

    @ApiModelProperty("商户公钥")
    private X509Certificate pubCert;

    @ApiModelProperty("商户私钥")
    private String merchantPrivateKey;

    @ApiModelProperty("商户私钥对象")
    private PrivateKey privateKey;

    @ApiModelProperty("商户号")
    private String merchantNo;

    @ApiModelProperty(value = "服务器地址")
    private String notifyUrl;

    @ApiModelProperty(value = "appid")
    private String appid;

    /**
     * 微信小程序支付
     * @param payOrderDTO
     * @return
     */
    public PayOrderRespDTO miniPay(PayOrderDTO payOrderDTO) {
        PayOrderRespDTO orderRespDTO = new PayOrderRespDTO();
        AppPayAppletOrderReqVO reqVo = new AppPayAppletOrderReqVO();
        reqVo.setP1_bizType("AppPayApplet");
        reqVo.setP2_orderId(String.valueOf(payOrderDTO.getFlowId()));
        reqVo.setP3_customerNumber(merchantNo);
        reqVo.setP4_payType("APPLET");
        // appid 固定 由中科 蝌蝌精选发起
        reqVo.setP5_appid(appid);
        reqVo.setP8_openid(payOrderDTO.getOpenid());
        reqVo.setP9_orderAmount(payOrderDTO.getPayAmt().toString());
        reqVo.setP10_currency("CNY");
        reqVo.setP11_appType("WXPAY");
        reqVo.setP12_notifyUrl(notifyUrl + String.format("/account/pay/notify/order/%s/%s/%s", payOrderDTO.getSysCode(), payOrderDTO.getOrderType(), payOrderDTO.getAppid()));
        reqVo.setP14_orderIp("127.0.0.1");
        if (StringUtils.isNotEmpty(payOrderDTO.getBody()) && payOrderDTO.getBody().length() > 100) {
            payOrderDTO.setBody(payOrderDTO.getBody().substring(0, 100) + "等...");
        }
        reqVo.setP15_goodsName(payOrderDTO.getBody());
        reqVo.setP18_desc(StringUtils.format("orderNo={}", payOrderDTO.getOrderNo()));
        reqVo.setSignatureType(SignatureType.SM3WITHSM2);
        if (Objects.nonNull(payOrderDTO.getSettlements())) {
            // 有结算就需要分支
            reqVo.setSplitBillType("FIXED_AMOUNT");
            // 充值的场景下, 直接分账
            if (payOrderDTO.getOrderType().equals(OrderTypeConstants.BRANCH_CHARGE) || payOrderDTO.getOrderType().equals(OrderTypeConstants.SUPPLIER_CHARGE)) {
                ArrayList<Map> rules = new ArrayList<Map>();
                // 合并分账商户资金数据
                Map<String, List<OrderSettlementDTO>> accountMap = payOrderDTO.getSettlements().stream().collect(Collectors.groupingBy(OrderSettlementDTO::getAccountNo));
                accountMap.forEach((accountNo, settles) -> {
                    HashMap<String, Object> item = new HashMap<>();
                    BigDecimal totalAmt = settles.stream().map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                    item.put("splitBillMerchantNo", accountNo);
                    item.put("splitBillAmount", totalAmt.doubleValue());
                    rules.add(item);
                });
                if (accountMap.containsKey(merchantNo)) {
                    orderRespDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus())
                            .setMessage("订单分润方商编不可与收单商编一致");
                    return orderRespDTO;
                }
                if (!rules.isEmpty()) {
                    reqVo.setRuleJson(JSON.toJSONString(rules));
                }
            }
            // 其他场景例如订单支付, 2025年3月20日 现在已经改成了延迟订单完成一次分账
        }
        log.info("合利宝支付参数 {}", JSON.toJSONString(reqVo));
        try {
            String resultMsg = reuqest(TRANSFER_URL, reqVo,  AppPayAppletOrderReqVO.NEED_SIGN_PARAMS,  AppPayAppletOrderReqVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝支付预下单结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("rt2_retCode"))) {
                orderRespDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus())
                        .setSuccessTime(new Date())
                        .setOrderNo(payOrderDTO.getOrderNo())
                        .setFlowId(payOrderDTO.getFlowId())
                        .setOutTradeNo(resultObj.getString("rt6_serialNumber"))
                        .setRawData(resultObj.getJSONObject("rt10_payInfo"));
            } else {
                orderRespDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus()).setMessage(resultObj.getString("rt3_retMsg"));
            }
        } catch (IllegalAccessException e) {
            log.error(" miniPay异常,", e);
            throw new RuntimeException(e);
        }
        // 发起支付信息
        orderRespDTO.setPayInfoDTO(
                PayInfoDTO.builder()
                        .merchantNo(this.merchantNo)
                        .openid(payOrderDTO.getOpenid())
                        .build()
        );
        return orderRespDTO;
    }

    /**
     * 支付宝app支付
     * @param payOrderDTO
     * @return
     */
    public PayOrderRespDTO aliAppPay(PayOrderDTO payOrderDTO) {
        PayOrderRespDTO orderRespDTO = new PayOrderRespDTO();
        AppPayAppletOrderReqVO reqVo = new AppPayAppletOrderReqVO();
        reqVo.setP1_bizType("AppPayApplet");
        reqVo.setP2_orderId(String.valueOf(payOrderDTO.getFlowId()));
        reqVo.setP3_customerNumber(merchantNo);
        reqVo.setP4_payType("APPLET");
        // appid 固定 由中科 蝌蝌精选发起
        reqVo.setP5_appid("1");
        reqVo.setP8_openid(payOrderDTO.getOpenid());
        reqVo.setP9_orderAmount(payOrderDTO.getPayAmt().toString());
        reqVo.setP10_currency("CNY");
        reqVo.setP11_appType("ALIPAY");
        reqVo.setP12_notifyUrl(notifyUrl + String.format("/account/pay/notify/order/%s/%s/%s", payOrderDTO.getSysCode(), payOrderDTO.getOrderType(), payOrderDTO.getAppid()));
        reqVo.setP14_orderIp("127.0.0.1");
        if (StringUtils.isNotEmpty(payOrderDTO.getBody()) && payOrderDTO.getBody().length() > 100) {
            payOrderDTO.setBody(payOrderDTO.getBody().substring(0, 100) + "等...");
        }
        reqVo.setP15_goodsName(payOrderDTO.getBody());
        reqVo.setP18_desc(StringUtils.format("orderNo={}", payOrderDTO.getOrderNo()));
        reqVo.setSignatureType(SignatureType.SM3WITHSM2);
        if (Objects.nonNull(payOrderDTO.getSettlements())) {
            ArrayList<Map> rules = new ArrayList<Map>();
            // 合并多个入驻商使用同一个分账编号, 分账兼容
            Map<String, List<OrderSettlementDTO>> accountMap = payOrderDTO.getSettlements().stream().collect(Collectors.groupingBy(OrderSettlementDTO::getAccountNo));
            accountMap.forEach((accountNo, settles) -> {
                HashMap<String, Object> item = new HashMap<>();
                BigDecimal totalAmt = settles.stream().map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                item.put("splitBillMerchantNo", accountNo);
                item.put("splitBillAmount", totalAmt.doubleValue());
                rules.add(item);
            });
            if (!rules.isEmpty()) {
                reqVo.setSplitBillType("FIXED_AMOUNT");
                reqVo.setRuleJson(JSON.toJSONString(rules));
            }
        }
        log.info("合利宝支付参数 {}", JSON.toJSONString(reqVo));
        try {
            String resultMsg = reuqest(TRANSFER_URL, reqVo,  AppPayAppletOrderReqVO.NEED_SIGN_PARAMS,  AppPayAppletOrderReqVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝支付预下单结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("rt2_retCode"))) {
                orderRespDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus())
                        .setSuccessTime(new Date())
                        .setFlowId(payOrderDTO.getFlowId())
                        .setOrderNo(payOrderDTO.getOrderNo())
                        .setOutTradeNo(resultObj.getString("rt6_serialNumber"))
                        .setRawData(resultObj.getJSONObject("rt10_payInfo"));
            } else {
                orderRespDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus()).setMessage(resultObj.getString("rt3_retMsg"));
            }
        } catch (IllegalAccessException e) {
            log.error(" aliAppPay异常,", e);
            throw new RuntimeException(e);
        }
        // 发起支付信息
        orderRespDTO.setPayInfoDTO(
                PayInfoDTO.builder()
                        .merchantNo(this.merchantNo)
                        .build()
        );
        return orderRespDTO;
    }

    public PayRefundRespDTO refund(PayRefundOrderSubmitReqVO reqDTO) {
        PayRefundRespDTO refundRespDTO = new PayRefundRespDTO();
        AppPayRefundOrderReqVO appPayRefundOrderReqVo = new AppPayRefundOrderReqVO();
        appPayRefundOrderReqVo.setP1_bizType("AppPayRefund");
        // 判断是否使用订单号退款
        if (!reqDTO.isUseOrderNoRefund()) {
            appPayRefundOrderReqVo.setP2_orderId(String.valueOf(reqDTO.getFlowId()));
        } else {
            appPayRefundOrderReqVo.setP2_orderId(reqDTO.getOrderNo());
        }
        appPayRefundOrderReqVo.setP3_customerNumber(merchantNo);
        appPayRefundOrderReqVo.setP4_refundOrderId(reqDTO.getRefundFlowDTO().getPayFlowId().toString());
        appPayRefundOrderReqVo.setP5_amount(reqDTO.getRefundAmt().toString());
        appPayRefundOrderReqVo.setP6_callbackUrl(notifyUrl +
                StringUtils.format("/account/pay/notify/refund/{}/{}/{}/{}",
                        reqDTO.getSysCode(),
                        reqDTO.getOrderType(),
                        reqDTO.getAppid(),
                        PayChannelEnum.HLB.getCode()
                )
        );
        /*if (Objects.nonNull(reqDTO.getSettlements())) {
            if (reqDTO.getSettlements().isEmpty()) {
                throw new IllegalArgumentException("settlements 分账退款信息不能为空");
            }
            ArrayList<Map> rules = new ArrayList<Map>();
            // 合并多个入驻商使用同一个分账编号, 分账兼容
            BigDecimal totalSettleAmt = BigDecimal.ZERO;
            Map<String, List<OrderSettlementDTO>> accountMap = reqDTO.getSettlements().stream().collect(Collectors.groupingBy(OrderSettlementDTO::getAccountNo));
            for (String accountNo : accountMap.keySet()) {
                List<OrderSettlementDTO> settles = accountMap.get(accountNo);
                HashMap<String, Object> item = new HashMap<>();
                BigDecimal totalAmt = settles.stream().map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (NumberUtil.equals(totalAmt, BigDecimal.ZERO)) {
                    // 如果分账金额是0, 跳过分账
                    continue;
                }
                item.put("merchantNo", accountNo);
                item.put("refundAmount", totalAmt.doubleValue());
                rules.add(item);
                totalSettleAmt = totalSettleAmt.add(totalAmt);
            }
            BigDecimal free = reqDTO.getRefundAmt().subtract(totalSettleAmt);
            if (NumberUtil.isGreater(free, BigDecimal.ZERO)) {
                HashMap<String, Object> item = new HashMap<>();
                item.put("merchantNo", merchantNo);
                item.put("refundAmount", free.doubleValue());
                rules.add(item);
            }
            appPayRefundOrderReqVo.setRuleJson(JSON.toJSONString(rules));
        }*/
        appPayRefundOrderReqVo.setSignatureType(SignatureType.SM3WITHSM2);
        log.info("合利宝退款参数 {}", JSON.toJSONString(appPayRefundOrderReqVo));
        try {
            String resultMsg = reuqest(TRANSFER_URL, appPayRefundOrderReqVo, AppPayRefundOrderReqVO.NEED_SIGN_PARAMS, AppPayRefundOrderReqVO.NEED_ENCRYPT_OR_DECRYPT_PARAMS);
            log.info("合利宝支付发起退款结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0001".equals(resultObj.getString("rt2_retCode")) || "0000".equals(resultObj.getString("rt2_retCode"))) {
                refundRespDTO.setStatus(PayRefundStatusEnum.PROCESSING.getStatus())
                        .setRefundNo(reqDTO.getRefundNo())
                        .setOrderNo(reqDTO.getOrderNo())
                        .setRefundTradeNo(reqDTO.getRefundFlowDTO().getPayFlowId().toString())
                        .setOutRefundNo(resultObj.getString("rt7_serialNumber"));
            } else {
                refundRespDTO.setStatus(PayRefundStatusEnum.FAILURE.getStatus())
                        .setRefundNo(reqDTO.getRefundNo())
                        .setOrderNo(reqDTO.getOrderNo())
                        .setRefundTradeNo(reqDTO.getRefundFlowDTO().getPayFlowId().toString())
                        .setErrorMsg(resultObj.getString("rt3_retMsg"));
            }
        } catch (IllegalAccessException e) {
            log.error(" refund异常,", e);
            throw new RuntimeException(e);
        }
        return refundRespDTO;
    }

    public PayOrderRespDTO getOrder(PayOrderQueryVO payOrderQueryVO) {
        PayOrderRespDTO orderRespDTO = new PayOrderRespDTO();
        orderRespDTO.setOrderNo(payOrderQueryVO.getTradeNo())
                .setFlowId(payOrderQueryVO.getPayFlow().getPayFlowId())
        ;
        // 请求参数
        AppPayOrderQueryReqVO reqVo = new AppPayOrderQueryReqVO();
        reqVo.setP2_orderId(String.valueOf(payOrderQueryVO.getPayFlow().getPayFlowId()));
        reqVo.setP3_customerNumber(merchantNo);
        reqVo.setSignatureType(SignatureType.SM3WITHSM2);
        log.info("查询合利宝支付订单参数 {}", JSON.toJSONString(reqVo));
        try {
            String resultMsg = reuqest(TRANSFER_URL, reqVo, AppPayOrderQueryReqVO.NEED_SIGN_PARAMS, new HashSet<>());
            log.info("查询合利宝支付订单结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("rt2_retCode")) && "SUCCESS".equals(resultObj.getString("rt7_orderStatus"))) {
                orderRespDTO.setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                        .setSuccessTime(new Date())
                        .setOutTradeNo(resultObj.getString("rt6_serialNumber"));
            } else {
                orderRespDTO.setStatus(PayOrderStatusRespEnum.CREATE_SUC.getStatus());
            }
        } catch (IllegalAccessException e) {
            log.error(" getOrder异常,", e);
            throw new RuntimeException(e);
        }
        return orderRespDTO;
    }

    public CreateDivideRespVO divide(DivideReqVO divideReqVO) {
        HlbDelayedSplitReqVO item = new HlbDelayedSplitReqVO();
        item.setOrderId(String.valueOf(divideReqVO.getDivideFlowId()));
        item.setOriginalOrderId(String.valueOf(divideReqVO.getPayFlow().getPayFlowId()));
        item.setRuleJson(ListUtil.toList(new HlbDelayedSplitReqVO.SplitBillRule(divideReqVO.getReceiveMerchantNo(), divideReqVO.getAmount())));
        try {
            String result = splitPostJsonReq(DELAYED_SPLIT_URL, item);
            JSONObject resultObj = JSON.parseObject(result);
            if (!resultObj.getString("code").equals("0000")) {
                // 调用失败
                return  CreateDivideRespVO.fail(resultObj.getString("message"));
            }
            return CreateDivideRespVO.success();
        } catch (Exception e) {
            log.error(" divide异常,", e);
            throw new RuntimeException(e);
        }
    }

    public PayWxB2bDivideRespVO queryDivide(AccDivideFlow divideFlow) {
        HlbDelayedSplitQeuryReqVO item = new HlbDelayedSplitQeuryReqVO();
        item.setOrderId(String.valueOf(divideFlow.getDivideFlowId()));
        try {
            String result = splitPostJsonReq(DELAYED_SPLIT_QUERY_URL, item);
            JSONObject resultObj = JSON.parseObject(result);
            if (!resultObj.getString("code").equals("0000")) {
                // 调用失败
                return PayWxB2bDivideRespVO.fail(resultObj.getString("message"));
            }
            JSONObject data = resultObj.getJSONObject("data");
            String status = data.getString("status");
            if (status.equals("FAILED")) {
                return PayWxB2bDivideRespVO.fail(data.getString("stateDesc"));
            }
            if (status.equals("DOING")) {
                return PayWxB2bDivideRespVO.processing();
            }
            return PayWxB2bDivideRespVO.success();
        } catch (Exception e) {
            log.error(" queryDivide异常,", e);
            throw new RuntimeException(e);
        }
    }

    private  String reuqest(
            String requestUrl,
            Object orderVo,
            Set<String> needSignParams,
            Set<String> needEncryptParams) throws IllegalAccessException {
        String resultMsg;
        Map<String, String> map = HlbCertUtil.convertBean(orderVo, new LinkedHashMap<>());
        //建议是加签/验签的固定参数,具体按照文档要求加签.因为新增参数需要历史兼容是排除签名的
        HlbCertUtil.getSignAndEncryptedByReq(map, needSignParams, needEncryptParams, pubCert, merchantPrivateKey);
        Map<String, Object> resultMap = HttpClientService.getHttpResp(map, requestUrl);
        resultMsg = (String) resultMap.get("response");
        return resultMsg;
    }


    /**
     * 针对分账接口, http post json请求
     */
    private String splitPostJsonReq(String requestUrl, Object orderVo) throws Exception {
        try {
            String dataJsonString = JSON.toJSONString(orderVo);
            //生成16位SM4随机秘钥
            String sm4Key = SM4Utils.generateRandomKey();
            //获取合利宝公钥证书，使用公钥证书对随机秘钥进行加密
            String encrytionKey = SM2NUtils.encryptToBase64(pubCert.getPublicKey(), sm4Key);
            //使用SM4随机秘钥对data数据进行加密
            String data = SM4Utils.encryptBase64(dataJsonString, sm4Key);

            //获取商户私钥证书
            //使用私钥对data密文进行签名
            String sign = SM2NUtils.sign(privateKey, data);

            Map<String, String> map = new HashMap<>();
            map.put(CommonRequesConstants.DATA, data);
            map.put(CommonRequesConstants.CUSTOMER_NUMBER, merchantNo);
            map.put(CommonRequesConstants.ENCRYPTION_KEY, encrytionKey);
            map.put(CommonRequesConstants.SIGN_TYPE, SIGN_TYPE);
            map.put(CommonRequesConstants.SIGN, sign);
            map.put(CommonRequesConstants.VERSION, VERSION);
            map.put(CommonRequesConstants.TIMESTAMP,String.valueOf(System.currentTimeMillis()));
            String jsonString = JSON.toJSONString(map);
            log.info("合利宝分账业务请求URL={}, 请求参数={}", requestUrl, dataJsonString);
            String result = HttpUtil.post(requestUrl, jsonString);
            log.info("请求结果 {} ", result);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            throw  e;
        }
    }

    private static final String SIGN_TYPE = "SM3WITHSM2";

    private static final String VERSION = "1.0";
}
