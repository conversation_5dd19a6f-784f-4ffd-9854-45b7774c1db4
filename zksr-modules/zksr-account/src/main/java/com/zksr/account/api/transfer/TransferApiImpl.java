package com.zksr.account.api.transfer;

import com.zksr.account.api.transfer.dto.*;
import com.zksr.account.api.transfer.vo.AccTransferBillPageVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderRespVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillPageReqVO;
import com.zksr.account.api.transfer.vo.AccTransferBillOrderExportPageVO;
import com.zksr.account.convert.transfer.AccTransferBillOrderConvert;
import com.zksr.account.convert.transfer.AccTransferConvert;
import com.zksr.account.domain.AccBillFile;
import com.zksr.account.domain.AccTransfer;
import com.zksr.account.domain.AccTransferBillOrder;
import com.zksr.account.mapper.AccBillFileMapper;
import com.zksr.account.mapper.AccTransferBillMapper;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.service.IAccBillFileService;
import com.zksr.account.service.IAccTransferService;
import com.zksr.account.service.transfer.IAccTransferBillOrderService;
import com.zksr.account.service.transfer.IAccTransferBillService;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 内部转账单接口
 * @date 2024/4/11 9:17
 */
@Slf4j
@InnerAuth
@ApiIgnore
@RestController
public class TransferApiImpl implements TransferApi {
    @Autowired
    private AccBillFileMapper accBillFileMapper;

    @Autowired
    private AccTransferBillMapper accTransferBillMapper;

    @Autowired
    private IAccTransferService accTransferService;

    @Autowired
    private AccountMqProducer accountMqProducer;

    @Autowired
    private IAccBillFileService accBillFileService;

    @Autowired
    private IAccTransferBillOrderService accTransferBillOrderService;

    @Autowired
    private IAccTransferBillService accTransferBillService;

    @Override
    public CommonResult<Boolean> createTransfer(TransferSaveDTO transferSaveDTO) {
        AccTransfer transfer = accTransferService.insertAccTransfer(AccTransferConvert.INSTANCE.convert(transferSaveDTO));
        accountMqProducer.sendAccountTransfer(transfer);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> createTransferBatch(@Valid @Size(min = 1, max = 100, message = "最多传输100条") List<TransferSaveDTO> transferSaveDTO) {
        // 批量保存数据
        List<AccTransfer> accTransfers = accTransferService.insertBatchAccTransfer(AccTransferConvert.INSTANCE.convertList(transferSaveDTO));
        // 处理结算
        accTransfers.forEach(accountMqProducer::sendAccountTransfer);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<List<TransferRetryDTO>> getRetryAccTransfer(Long minId) {
        return CommonResult.success(AccTransferConvert.INSTANCE.convert(accTransferService.getRetryAccTransfer(minId)));
    }

    @Override
    public CommonResult<Boolean> retryAccTransfer(List<TransferRetryDTO> retryList) {
        retryList.forEach(item -> {
            log.info("重试转账单 transferNo={}", item.getTransferNo());
            accountMqProducer.sendAccountTransfer(accTransferService.getAccTransfer(item.getTransferId()));
        });
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> insertBillFileRecord(AccBillFileDTO accBillFileDTO) {
        accBillFileService.insertBillFileRecord(accBillFileDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> insertTransferBillOrder(List<AccTransferBillOrderDTO> accBillOrderDTO) {
        accTransferBillOrderService.insertTransferBillOrder(accBillOrderDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<Boolean> insertTransferBill(AccTransferBillDTO accTransferBillDTO) {
        accTransferBillService.insertTransferBill(accTransferBillDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public List<String> checkBillFileRecordExists(String altNo, String data) {
        return accBillFileService.checkBillFileRecordExists(altNo,data);
    }

    @Override
    @Transactional
    public void deleteBillFileRecord(String billFileId) {
        accBillFileMapper.deleteBillFileRecord(billFileId);
    }

    @Override
    public List<String> countTransferBillsByDateAndAltNo(String dataFormatted, String altNo) {
        return accTransferBillOrderService.countTransferBillsByDateAndAltNo(dataFormatted,altNo);
    }

    @Override
    @Transactional
    public void deleteTransferBillsByDateAndAltNo(String dataFormatted, String altNo) {
        accTransferBillOrderService.deleteTransferBillsByDateAndAltNo(dataFormatted,altNo);
    }

    @Override
    @Transactional
    public void deleteTransferBill(String transferBillId) {
        accTransferBillMapper.deleteTransferBill(transferBillId);
    }

    @Override
    public CommonResult<List<AccTransferBillRespDTO>> getTransferBill(AccTransferBillPageVO pageVO) {
        AccTransferBillPageReqVO pageReqVO =HutoolBeanUtils.toBean( pageVO, AccTransferBillPageReqVO.class);
        return CommonResult.success(HutoolBeanUtils.toBean(accTransferBillService.getAccTransferBillPage(pageReqVO).getList(), AccTransferBillRespDTO.class));
    }

    @Override
    public CommonResult<List<AccTransferBillOrderRespDTO>> getAccTransferBillOrderDataExport(AccTransferBillOrderExportPageVO reqVo) {
        PageResult<AccTransferBillOrderRespVO> pageResult = accTransferBillOrderService.getAccTransferBillOrderPage(AccTransferBillOrderConvert.INSTANCE.convertExportReq(reqVo));
        return success(HutoolBeanUtils.toBean(pageResult.getList(), AccTransferBillOrderRespDTO.class));
    }
}
