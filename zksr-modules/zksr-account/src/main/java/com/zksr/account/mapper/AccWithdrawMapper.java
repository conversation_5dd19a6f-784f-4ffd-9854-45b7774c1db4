package com.zksr.account.mapper;

import com.zksr.account.controller.withdraw.vo.AccWithdrawRespVO;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * 账户提现单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Mapper
public interface AccWithdrawMapper extends BaseMapperX<AccWithdraw> {
    default PageResult<AccWithdraw> selectPage(AccWithdrawPageReqVO reqVO) {
        LambdaQueryWrapperX<AccWithdraw> queryWrapperX = new LambdaQueryWrapperX<AccWithdraw>()
                .eqIfPresent(AccWithdraw::getWithdrawId, reqVO.getWithdrawId())
                .eqIfPresent(AccWithdraw::getSysCode, reqVO.getSysCode())
                .eqIfPresent(AccWithdraw::getMerchantType, reqVO.getMerchantType())
                .eqIfPresent(AccWithdraw::getMerchantId, reqVO.getMerchantId())
                .gtIfPresent(AccWithdraw::getCreateBy, reqVO.getCreateTime())
                .ltIfPresent(AccWithdraw::getCreateBy, reqVO.getCreateTimeEnd())
                .inIfPresent(AccWithdraw::getState, reqVO.getState())
                .inIfPresent(AccWithdraw::getTransferState, reqVO.getTransferState())
                .inIfPresent(AccWithdraw::getSettleState, reqVO.getSettleState())
                .applyScope(reqVO.getParams())
                .orderByDesc(AccWithdraw::getWithdrawId);
        if (Objects.nonNull(reqVO.getMerchantTypeList())) {
            queryWrapperX.in(!reqVO.getMerchantTypeList().isEmpty(), AccWithdraw::getMerchantType,  reqVO.getMerchantTypeList());
        }
        // 审核时间
        queryWrapperX.gtIfPresent(AccWithdraw::getApproveTime, reqVO.getApproveTime());
        queryWrapperX.ltIfPresent(AccWithdraw::getApproveTime, reqVO.getApproveTimeEnd());
        return selectPage(reqVO, queryWrapperX);
    }

    List<AccWithdrawRespVO> selectPageExt(AccWithdrawPageReqVO pageReqVO);

    default AccWithdraw selectByTransferNo(String transferNo) {
        return selectOne(
                new LambdaQueryWrapperX<AccWithdraw>().eq(AccWithdraw::getTransferNo, transferNo)
        );
    }

    BigDecimal selectTotalWithdrawAmt(@Param("accountId") Long accountId);
}
