package com.zksr.account.service.transfer.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.zksr.account.client.TransferClient;
import com.zksr.account.convert.flow.AccTransferFlowConvert;
import com.zksr.account.domain.AccTransferFlow;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.account.service.IAccTransferFlowService;
import com.zksr.account.service.IAccWithdrawFlowService;
import com.zksr.account.service.transfer.TransferAccountService;
import com.zksr.account.service.transfer.TransferChannelService;
import com.zksr.account.service.transfer.TransferSettleProcessService;
import com.zksr.account.service.transfer.TransferSubmitProcessService;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.TransferStatusEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/11 14:55
 */
@Service
@Slf4j
public class TransferAccountServiceImpl implements TransferAccountService {

    @Autowired
    private TransferChannelService transferChannelService;

    @Autowired
    private TransferSubmitProcessService transferSubmitProcessService;

    @Autowired
    private TransferSettleProcessService transferSettleProcessService;

    @Autowired
    private IAccTransferFlowService transferFlowService;

    @Autowired
    private IAccWithdrawFlowService withdrawFlowService;

    @Autowired
    private RedisService redisService;

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_WITHDRAW_TRANSFER, condition = "#reqVO.transferNo")
    public TransferSubmitRespDTO transferSubmit(TransferSubmitReqVO reqVO) {
        TransferClient transferClient = transferChannelService.getTransferClient(reqVO.getSysCode(), reqVO.getPlatform(), reqVO.getSourceMerchantType());
        if (Objects.isNull(transferClient)) {
            throw exception(TRANSFER_NOT_EXIST);
        }
        log.info("发起转账请求 req={}", JSON.toJSONString(reqVO));
        // 创建转账流水
        transferFlowService.createTransferFlow(reqVO);
        // 转账
        TransferSubmitRespDTO transferSubmitRespDTO = transferClient.transferSubmit(reqVO);
        log.info("发起转账结果 resp={}", JSON.toJSONString(transferSubmitRespDTO));
        // 回传转账单号
        transferSubmitRespDTO.setTransferNo(reqVO.getTransferNo());
        // 回调
        if (TransferStatusEnum.isSuccess(transferSubmitRespDTO.getStatus())) {
            // 处理中
            transferSubmitProcessService.notifyProcessing(transferSubmitRespDTO);
        }
        notify(transferSubmitRespDTO);
        return transferSubmitRespDTO;
    }

    @Override
    public TransferSettleRespDTO transferSettle(TransferSettleReqVO reqVO) {
        TransferClient transferClient = transferChannelService.getTransferClient(reqVO.getSysCode(), reqVO.getPlatform(), reqVO.getMerchantType());
        if (Objects.isNull(transferClient)) {
            throw exception(TRANSFER_NOT_EXIST);
        }
        String withdrawNo = redisService.getUniqueNumber(CacheConstants.WITHDRAW_SEQUENCE);
        // 本地提现结算唯一
        reqVO.setWithdrawNo(withdrawNo);
        // 创建流水
        withdrawFlowService.createFlow(reqVO);
        log.info("发起提现请求 req={}", JSON.toJSONString(reqVO));
        // 提现 | 结算
        TransferSettleRespDTO settleResp;
        if (MerchantTypeEnum.isColonel(reqVO.getPlatform())) {
            // 兼容无需转账, 返回一个假状态
            // 目前只有业务员适应, 业务员账户T+1自动结算, 平台商和软件商都是T+1自动结算, 无需提现,
            // 只有业务员有一个钱包提现操作, 需要转账过去, 然后第二天自动到账
            settleResp = new TransferSettleRespDTO();
            settleResp.setMsg("无需转账");
            settleResp.setStatus(TransferStatusEnum.SUCCESS.getStatus());
            settleResp.setWithdrawNo(reqVO.getWithdrawNo());
        } else {
            // 执行转账
            settleResp = transferClient.transferSettle(reqVO);
        }
        log.info("发起提现请求结果 resp={}", JSON.toJSONString(settleResp));
        // 设置回调交易单号
        if (TransferStatusEnum.isSuccess(settleResp.getStatus())) {
            // 这里是同步的
            transferSettleProcessService.notifyProcessing(settleResp);
        }
        // 兼容回调
        notify(settleResp);
        return settleResp;
    }

    @Override
    public void notify(TransferSubmitRespDTO transferSubmitRespDTO) {
        if (TransferStatusEnum.isSuccess2(transferSubmitRespDTO.getStatus())) {
            // 转账成功则直接回调, 有可能是模拟支付
            transferSubmitProcessService.notifySuccess(transferSubmitRespDTO);
        } else if (TransferStatusEnum.isFail(transferSubmitRespDTO.getStatus()))  {
            // 转账失败
            transferSubmitProcessService.notifyFail(transferSubmitRespDTO);
        }
    }

    @Override
    public void notify(TransferSettleRespDTO settleRespDTO) {
        if (TransferStatusEnum.isSuccess2(settleRespDTO.getStatus())) {
            transferSettleProcessService.notifySuccess(settleRespDTO);
        } else if (TransferStatusEnum.isFail(settleRespDTO.getStatus())) {
            // 提现失败
            transferSettleProcessService.notifyFail(settleRespDTO);
        }
    }
}
