package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 线下分账处理对象 acc_offline_divide
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@TableName(value = "acc_offline_divide")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccOfflineDivide extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long offlineDivideId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 线下处理单号 */
    @Excel(name = "线下处理单号")
    private String offlineProNo;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 分账金额 */
    @Excel(name = "分账金额")
    private BigDecimal altAmt;

    /** 线下转账单号 */
    @Excel(name = "线下转账单号")
    private String offlineNo;

    /** 总分账金额 */
    @Excel(name = "总分账金额")
    private BigDecimal totalAltAmt;

    /** 总订单金额 */
    @Excel(name = "总订单金额")
    private BigDecimal totalOrderAmt;

    /** 凭证图片 */
    @Excel(name = "凭证图片")
    private String voucherPic;

}
