package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel("美的付多级分账下单响应VO")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayProfitOrderNotifyRespVO extends MideaPayBaseRespVO {
    @ApiModelProperty("商户分账订单号")
    @JsonProperty("out_profit_no")
    private String outProfitNo;

    @ApiModelProperty("原商户交易订单号")
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    @ApiModelProperty("支付系统分账订单号")
    @JsonProperty("profit_no")
    private String profitNo;

    @ApiModelProperty("分账详情")
    @JsonProperty("profit_sharing_details")
    private String profitSharingDetails;

    @ApiModelProperty("分账总金额")
    @JsonProperty("total_amount")
    private String totalAmount;

    @ApiModelProperty("释放金额")
    @JsonProperty("release_amount")
    private String releaseAmount;

    @ApiModelProperty("释放标志")
    @JsonProperty("release")
    private String release;
    
    @ApiModelProperty("订单支付状态  SUCCESS：成功 FAIL：失败")
    @JsonProperty("trade_status")
    private String tradeStatus;


    @ApiModelProperty("订单状态描述")
    @JsonProperty("trade_status_info")
    private String tradeStatusInfo;

    @ApiModelProperty("订单接收时间")
    @JsonProperty("profit_sharing_accept_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String profitSharingAcceptTime;

    @ApiModelProperty("分账成功时间")
    @JsonProperty("pay_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String payTime;
    
    @ApiModelProperty("通知时间")
    @JsonProperty("notify_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String notifyTime;
    
    
    @ApiModelProperty("商户附言")
    @JsonProperty("attach")
    private String attach;
    
    @ApiModelProperty("错误码")
    @JsonProperty("error_code")
    private String errorCode;

    @ApiModelProperty("错误描述")
    @JsonProperty("error_info")
    private String errorInfo;
} 