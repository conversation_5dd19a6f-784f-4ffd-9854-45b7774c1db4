package com.zksr.account.service.metchant.impl;

import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.client.MerchantClient;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.enums.MerchantCredentialType;
import com.zksr.account.mapper.AccPlatformMerchantMapper;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.account.service.metchant.PlatformMerchantService;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import com.zksr.common.core.enums.MerchantUploadPicStateEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.system.api.bank.BankChannelApi;
import com.zksr.system.api.bank.vo.SysBankChannelRespVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝商户信息处理
 * @date 2024/9/19 14:39
 */
@Service
public class HlbPlatformMerchantServiceImpl implements PlatformMerchantService {

    @Autowired
    private AccPlatformMerchantMapper accPlatformMerchantMapper;

    @Resource
    private BankChannelApi bankChannelApi;

    @Override
    public PayChannelEnum channel() {
        return PayChannelEnum.HLB;
    }

    /**
     * 同步合利宝进件数据状态
     * @param registerMerchant  第三方进件信息
     * @param platformMerchant  系统第三方进件信息
     * @param merchantClient    商户客户端
     */
    @Override
    public void syncData(PayPlatformMerchantVO registerMerchant, AccPlatformMerchant platformMerchant, MerchantClient merchantClient) {
        if (!PayChannelEnum.HLB.getCode().equals(platformMerchant.getPlatform())) {
            return;
        }
        AccPlatformMerchant update = new AccPlatformMerchant();
        update.setPlatformMerchantId(platformMerchant.getPlatformMerchantId());
        update.setAuditStatus(registerMerchant.getState().getState());
        // 同步合利宝进件结果
        if (MerchantRegisterStateEnum.success(registerMerchant.getState().getState())) {
            update.setAuthMsg(registerMerchant.getMsg());
            update.setAltMchNo(registerMerchant.getMerchantNo());
        } else {
            update.setAuthMsg(registerMerchant.getMsg());
        }
        if (StringUtils.isEmpty(platformMerchant.getBankName())) {
            update.setBankName(registerMerchant.getBankName());
        }
        if (StringUtils.isEmpty(platformMerchant.getBankBranch())) {
            update.setBankBranch(registerMerchant.getBankBranch());
        }
        update.setIdCard(registerMerchant.getLegalPersonID());
        update.setLicenseNo(registerMerchant.getBusinessLicense());
        update.setContractPhone(registerMerchant.getLinkPhone());
        if (StringUtils.isEmpty(platformMerchant.getAccountNo())) {
            update.setAccountNo(registerMerchant.getAccountNo());
        }
        if (StringUtils.isEmpty(platformMerchant.getBankChannelNo())) {
            update.setBankChannelNo(registerMerchant.getBankCode());
        }
        if (StringUtils.isNotEmpty(platformMerchant.getAltMchNo()) || StringUtils.isNotEmpty(update.getAltMchNo())) {
            String altNo = StringUtils.isNotEmpty(platformMerchant.getAltMchNo()) ? platformMerchant.getAltMchNo() : update.getAltMchNo();

            // 查询商户信息
            PayPlatformMerchantVO merchant = merchantClient.getMerchant(altNo);
            if (Objects.nonNull(merchant)) {
                update.setAltMchName(merchant.getSignName());
                //update.setBankName(merchant.getBankName());
                //update.setBankBranch(merchant.getBankBranch());
                //update.setAccountNo(merchant.getAccountNo());
                update.setAccountName(merchant.getAccountName());
                update.setBankType(merchant.getSettleBankType());
                update.setContractName(merchant.getLinkman());
                update.setLegalPerson(merchant.getLegalPerson());

                update.setBusiMerchantType(merchant.getMerchantType());
                // 查询商户变更审核状态
                if (StringUtils.isNotEmpty(platformMerchant.getEditOrderNo())) {
                    PlatformMerchantUpdateStatusRespVO platformMerchantUpdateStatusRespVO = merchantClient.queryUploadMerchantStatus(altNo, platformMerchant.getEditOrderNo());
                    update.setEditStatus(platformMerchantUpdateStatusRespVO.getAuditStatus());
                    update.setEditMsg(platformMerchantUpdateStatusRespVO.getMsg());
                }
                PlatformMerchantUploadSaveReqVO merchantUploadSaveReqVO = PlatformMerchantUploadSaveReqVO.builder()
                        .altMchNo(altNo)
                        .orderNo(platformMerchant.getThirdOrderNo())
                        .build();

                // 查询图片审核状态
                // 身份证正面
                if (StringUtils.isNotEmpty(platformMerchant.getCardPositiveUrl())) {
                    merchantUploadSaveReqVO.setCredentialType(MerchantCredentialType.FRONT_OF_ID_CARD);
                    PlatformMerchantUploadSaveRespVO respVO = merchantClient.queryUploadPicStatus(merchantUploadSaveReqVO);
                    update.setPicStatus(respVO.getAuditStatus());
                    if (MerchantUploadPicStateEnum.fail(respVO.getAuditStatus())) {
                        update.setPicMsg(respVO.getMsg());
                    }
                }

                // 身份证反面
                if (StringUtils.isNotEmpty(platformMerchant.getCardNegativeUrl())) {
                    merchantUploadSaveReqVO.setCredentialType(MerchantCredentialType.BACK_OF_ID_CARD);
                    PlatformMerchantUploadSaveRespVO respVO = merchantClient.queryUploadPicStatus(merchantUploadSaveReqVO);
                    update.setPicStatus(respVO.getAuditStatus());
                    if (MerchantUploadPicStateEnum.fail(respVO.getAuditStatus())) {
                        update.setPicMsg(respVO.getMsg());
                    }
                }

                // 营业执照
                if (StringUtils.isNotEmpty(platformMerchant.getTradeLicenceUrl())) {
                    merchantUploadSaveReqVO.setCredentialType(MerchantCredentialType.UNIFIED_CODE_CERTIFICATE);
                    PlatformMerchantUploadSaveRespVO respVO = merchantClient.queryUploadPicStatus(merchantUploadSaveReqVO);
                    update.setPicStatus(respVO.getAuditStatus());
                    if (MerchantUploadPicStateEnum.fail(respVO.getAuditStatus())) {
                        update.setPicMsg(respVO.getMsg());
                    }
                }

                // 营业执照
                if (StringUtils.isNotEmpty(platformMerchant.getOpenAccountLicenceUrl())) {
                    merchantUploadSaveReqVO.setCredentialType(MerchantCredentialType.PERMIT_FOR_BANK_ACCOUNT);
                    PlatformMerchantUploadSaveRespVO respVO = merchantClient.queryUploadPicStatus(merchantUploadSaveReqVO);
                    update.setPicStatus(respVO.getAuditStatus());
                    if (MerchantUploadPicStateEnum.fail(respVO.getAuditStatus())) {
                        update.setPicMsg(respVO.getMsg());
                    }
                }
            }
        }
        // 查询商户变更状态
        accPlatformMerchantMapper.updateById(update);
    }

    @Override
    public Long saveRegisterInfo(AccPlatformMerchantRegisterReqVO reqVO, PlatformMerchantRegisterSaveRespVO respVO) {
        // 查询系统是不是已经有这个商户的数据了
        AccPlatformMerchant accPlatformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(reqVO.getMerchantId(), reqVO.getMerchantType(), reqVO.getPlatform());

        PlatformMerchantRegisterSaveReqVO saveReqVO = reqVO.getPlatformMerchantRegisterSaveReqVO();
        // 商户保存信息
        saveReqVO.setPlatform(reqVO.getPlatform());

        // 根据联行号获取银行信息
        SysBankChannelRespVO bankChannelRespVO = bankChannelApi.getChannelByNo(saveReqVO.getBankChannelNo()).getCheckedData();
        // 审核中, 或者审核成功, 这里保存商户信息
        AccPlatformMerchant platformMerchant = AccPlatformMerchant
                .builder()
                .sysCode(reqVO.getSysCode())
                .merchantType(reqVO.getMerchantType())
                .merchantId(reqVO.getMerchantId())
                .platform(reqVO.getPlatform())
                .altMchNo(respVO.getMerchantNo())
                .thirdOrderNo(respVO.getOrderNo())
                .altMchName(saveReqVO.getAltMchName())
                .mchStatus(StringPool.ONE)
                .accountName(saveReqVO.getBankAccountName())
                .accountNo(saveReqVO.getBankAccountNo())
                .bankType("0".equals(saveReqVO.getSettleBankType()) ? "TOPRIVATE" : "TOPUBLIC")
                .busiMerchantType(saveReqVO.getMerchantType())
                .legalPerson(saveReqVO.getLegalPerson())
                .auditStatus(respVO.getAuditStatus())
                .editStatus(StringPool.EMPTY)
                .editMsg(StringPool.EMPTY)
                .picMsg(StringPool.EMPTY)
                .picMsg(StringPool.EMPTY)
                .idCard(saveReqVO.getIdCardNo())
                .bankChannelNo(saveReqVO.getBankChannelNo())
                .contractName(saveReqVO.getBusiContactName())
                .contractPhone(saveReqVO.getBusiContactMobileNo())
                .licenseNo(saveReqVO.getLicenseNo())
                .build();
        // 设置银行信息
        if (Objects.nonNull(bankChannelRespVO)) {
            platformMerchant.setBankName(bankChannelRespVO.getBankName());
            platformMerchant.setBankBranch(bankChannelRespVO.getBranchName());
        }
        if (Objects.nonNull(accPlatformMerchant)) {
            platformMerchant.setPlatformMerchantId(accPlatformMerchant.getPlatformMerchantId());
            accPlatformMerchantMapper.updateById(platformMerchant);
        } else {
            accPlatformMerchantMapper.insert(platformMerchant);
        }
        return platformMerchant.getPlatformMerchantId();
    }
}
