package com.zksr.account.client.wallet;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.account.client.PayClient;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.DivideReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideRespVO;
import com.zksr.account.service.IAccAccountFlowService;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccPayFlowService;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.system.api.model.dto.PayConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 钱包支付
 * @date 2024/3/10 11:14
 */
@Service
@Slf4j
@SuppressWarnings(StringPool.ALL)
public class WalletAppletPayClient implements PayClient {

    @Autowired
    private IAccAccountService accountService;

    @Autowired
    private IAccAccountFlowService accountFlowService;

    @Autowired
    private RedisService redisService;

    @Override
    public String getChannel() {
        return PayChannelEnum.WALLET.getCode();
    }

    @Override
    public String getPlatform() {
        return PayChannelEnum.WALLET.getCode();
    }

    @Override
    public void setConfig(PayConfigDTO config, String appid) throws Exception {
    }

    @Override
    public PayOrderRespDTO unifiedOrder(PayOrderDTO payOrderDTO) {
        if (Objects.isNull(payOrderDTO.getBranchId())) {
            log.warn("钱包支付门店ID不能为空, param = {}", JSON.toJSONString(payOrderDTO));
            throw exception(PARAM_CANNOT_NULL);
        }
        // 订单类型判断, 余额支付只支持商城订单
        exceptionAssert(!OrderTypeConstants.MALL.equals(payOrderDTO.getOrderType()), NONSUPPORT_ORDER_TYPE);
        // 创建返回数据
        return shelf().executePayment(payOrderDTO);
    }

    /**
     * 执行账户扣款操作
     *
     * @param payOrderDTO
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DistributedLock(lockName = RedisLockConstants.LOCK_BRANCH_WALLET, condition = "#payOrderDTO.branchId", tryLock = true)
    public PayOrderRespDTO executePayment(PayOrderDTO payOrderDTO) {
        PayOrderRespDTO payOrderRespDTO = new PayOrderRespDTO();
        List<Long> flowIds = new ArrayList<Long>();
        // 储值余额账户
        {
            AccAccount branchAccount = getBranchBaseAccount(payOrderDTO.getBranchId());
            // 断言
            exceptionAssert(Objects.isNull(branchAccount), BRANCH_ACCOUNT_NOT_EXIST);
            BigDecimal payAmt = payOrderDTO.getPayAmt();
            if (Objects.nonNull(payOrderDTO.getWalletGiveAmt()) && NumberUtil.isGreater(payOrderDTO.getWalletGiveAmt(), BigDecimal.ZERO)){
                payAmt = payAmt.subtract(payOrderDTO.getWalletGiveAmt());
            }
            // 支付金额 > 可用余额
            if (NumberUtil.isGreater(payAmt, branchAccount.getValidAccountAmt())) {
                payOrderRespDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus())
                        .setMessage(BALANCE_ERR.getMsg());
                return payOrderRespDTO;
            }
            // 创建流水
            AccAccountFlow accAccountFlow = AccAccountFlow.buildByAccount(branchAccount);
            accAccountFlow.setBusiWithdrawableAmt(BigDecimal.ZERO.subtract(payAmt))   // 0 - 支付金额
                    .setBusiType(AccountBusiType.BRANCH_BALANCE_PAY.getType())
                    .setBusiId(payOrderDTO.getBusiId())
                    .setBusiNo(payOrderDTO.getOrderNo())
                    .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField());
            accountFlowService.insertAccAccountFlow(accAccountFlow);
            flowIds.add(accAccountFlow.getAccountFlowId());
        }
        // 赠送余额账户, 0变动也需要记录, 因为需要记录赠金账户余额
        // 付款兼容无赠送账户场景
        BigDecimal walletGiveAmt = Objects.nonNull(payOrderDTO.getWalletGiveAmt()) ? payOrderDTO.getWalletGiveAmt() : BigDecimal.ZERO;
        giveBlock:
        {
            AccAccount branchAccount = getBranchGiveAccount(payOrderDTO.getBranchId());
            if (Objects.isNull(branchAccount) && NumberUtil.equals(walletGiveAmt, BigDecimal.ZERO)) {
                // 如果账户不存在, 并且赠金又是0, 那就不记录流水了, 如果账户存在的话, 还是需要记录赠金当时余额
                break giveBlock;
            }
            // 断言
            exceptionAssert(Objects.isNull(branchAccount), NOT_EXIST_MERCHANT);
            BigDecimal payAmt = walletGiveAmt;
            // 支付金额 > 可用余额
            if (NumberUtil.isGreater(payAmt, branchAccount.getValidAccountAmt())) {
                payOrderRespDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus())
                        .setMessage(BALANCE_ERR.getMsg());
                return payOrderRespDTO;
            }
            // 创建流水
            AccAccountFlow accAccountFlow = AccAccountFlow.buildByAccount(branchAccount);
            accAccountFlow.setBusiWithdrawableAmt(BigDecimal.ZERO.subtract(payAmt))   // 0 - 支付金额
                    .setBusiType(AccountBusiType.BRANCH_BALANCE_PAY.getType())
                    .setBusiId(payOrderDTO.getBusiId())
                    .setBusiNo(payOrderDTO.getOrderNo())
                    .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField());
            accountFlowService.insertAccAccountFlow(accAccountFlow);
            flowIds.add(accAccountFlow.getAccountFlowId());
        }
        // 这里可以嵌套循环去处理异常
        try {
            accountFlowService.processFlowByIdsSync(flowIds);
            payOrderRespDTO.setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                    .setFlowId(payOrderDTO.getFlowId())
                    .setOrderNo(payOrderDTO.getOrderNo())
                    .setOutTradeNo(RandomUtil.randomNumbers(18))
                    .setSuccessTime(DateUtil.date());
            return payOrderRespDTO;
        } catch (Exception e) {
            log.error(" WalletAppletPayClient.executePayment异常，", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public PayOrderRespDTO parseOrderNotify(Map<String, String> params, String body) {
        // 钱包支付无需实现
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DistributedLock(lockName = RedisLockConstants.LOCK_BRANCH_WALLET, condition = "#reqDTO.extras.branchId", tryLock = true)
    public PayRefundRespDTO unifiedRefund(PayRefundOrderSubmitReqVO reqDTO) {
        PayRefundRespDTO payRefundRespDTO = new PayRefundRespDTO();
        // 要查看这笔单是不是门店地址的
        AccPayFlow flowSuccessFlag = payFlowService().getByOrderPayFlowSuccessFlag(reqDTO.getOrderNo());
        if (Objects.isNull(flowSuccessFlag)) {
            throw exception(PAY_FLOW_NOT_EXIST);
        }
        // 处理钱包支付退款
        BigDecimal giveAmt= Objects.nonNull(reqDTO.getWalletGiveAmt()) ? reqDTO.getWalletGiveAmt() : BigDecimal.ZERO;
        BigDecimal refundAmt = reqDTO.getRefundAmt().subtract(giveAmt);
        Long branchId = StringUtils.isNotEmpty(reqDTO.getExtras().get(PayOrderSubmitExtras.BRANCH_ID)) ? Long.parseLong(reqDTO.getExtras().get(PayOrderSubmitExtras.BRANCH_ID)) : null;
        if (Objects.isNull(branchId)) {
            throw new ServiceException("钱包退款 extras.branchId 必填");
        }
        List<Long> flowIds = new ArrayList<Long>();
        for (AccAccount accAccount : ListUtil.toList(getBranchBaseAccount(branchId), getBranchGiveAccount(branchId)).stream().filter(Objects::nonNull).collect(Collectors.toList())) {
            BigDecimal busiAmt = accAccount.getAccountType() == NumberPool.INT_ZERO ? refundAmt : giveAmt;
            // 0 变动也需要记录, 不判断0了
            /*if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, busiAmt)) {
                continue;
            }*/
            AccAccountFlow accAccountFlow = AccAccountFlow.buildByAccount(accAccount);
            accAccountFlow.setBusiWithdrawableAmt(busiAmt)   // 0 - 支付金额
                    .setBusiType(AccountBusiType.BRANCH_BALANCE_REFUND.getType())
                    .setBusiId(reqDTO.getBusiId())
                    .setBusiNo(reqDTO.getRefundNo())
                    .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField());
            accountFlowService.insertAccAccountFlow(accAccountFlow);
            flowIds.add(accAccountFlow.getAccountFlowId());
        }
        // 这里可以嵌套循环去处理异常
        try {
            accountFlowService.processFlowByIdsSync(flowIds);
            payRefundRespDTO.setStatus(PayRefundStatusEnum.SUCCESS.getStatus())
                    .setOrderNo(reqDTO.getOrderNo())
                    .setRefundNo(reqDTO.getRefundNo())
                    .setOutRefundNo(RandomUtil.randomNumbers(18));
        } catch (Exception e) {
            log.error(" WalletAppletPayClient.unifiedRefund异常，", e);
            throw new RuntimeException(e);
        }
        return payRefundRespDTO;
    }


    public CreateDivideRespVO divide(DivideReqVO reqVO) {
        if (NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, reqVO.getAmount())) {
            log.info("分账金额是小于等于0, 不分账, tradeNo={}", reqVO.getTradeNo());
            return PayWxB2bCreateDivideRespVO.success();
        }
        AccAccountFlow accAccountFlow = new AccAccountFlow();
        accAccountFlow.setMerchantType(reqVO.getMerchantType())
                .setSysCode(reqVO.getSysCode())
                .setMerchantId(reqVO.getMerchantId())
                .setPlatform(reqVO.getPlatform().getCode())
                .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField())
                .setBusiWithdrawableAmt(reqVO.getAmount())
                .setBusiType(AccountBusiType.WALLET_DIVIDE.getType())
                .setBusiId(reqVO.getDivideFlowId())
                .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField())
                .setBusiNo(reqVO.getTradeNo())
                .setAccountType(NumberPool.INT_ONE)
        ;
        accountFlowService.insertAccAccountFlow(accAccountFlow);
        accountFlowService.processFlowByIdsSync(ListUtil.toList(accAccountFlow.getAccountFlowId()));
        return CreateDivideRespVO.success();
    }

    @Override
    public PayRefundRespDTO parseRefundNotify(Map<String, String> params, String body) {
        // 钱包支付无需实现
        return null;
    }

    @Override
    public BigDecimal getFeeRate() {
        // 返回钱包支付费率
        return BigDecimal.ZERO;
    }

    /**
     * 获取基本账户
     */
    private AccAccount getBranchBaseAccount(Long branchId) {
        return accountService.getAccountByMerchantIdAndType(
                branchId,
                MerchantTypeEnum.BRANCH.getType(),
                PayChannelEnum.WALLET.getCode(),
                NumberPool.INT_ZERO
        );
    }


    /**
     * 获取赠送账户
     */
    private AccAccount getBranchGiveAccount(Long branchId) {
        return accountService.getAccountByMerchantIdAndType(
                branchId,
                MerchantTypeEnum.BRANCH.getType(),
                PayChannelEnum.WALLET.getCode(),
                NumberPool.INT_ONE
        );
    }


    /**
     * 获取自己的代理对象
     *
     * @return
     */
    public WalletAppletPayClient shelf() {
        return SpringUtils.getBean(WalletAppletPayClient.class);
    }

    /**
     * payFlow
     */
    public IAccPayFlowService payFlowService() {
        return SpringUtils.getBean(IAccPayFlowService.class);
    }
}
