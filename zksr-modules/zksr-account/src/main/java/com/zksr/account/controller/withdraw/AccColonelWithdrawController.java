package com.zksr.account.controller.withdraw;

import cn.hutool.core.collection.ListUtil;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.controller.withdraw.vo.*;
import com.zksr.account.convert.account.AccountConvert;
import com.zksr.account.convert.withdraw.AccWithdrawConvert;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.WITHDRAW_IS_EMPTY;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 入业务员提现单接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Api(tags = "管理后台 - 业务员提现单接口", produces = HttpMethod.APPLICATION_JSON)
@Validated
@RestController
@RequestMapping("/withdraw-colonel")
public class AccColonelWithdrawController {

    @Autowired
    private IAccWithdrawService accWithdrawService;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 新增入业务员提现单
     */
    @ApiOperation(value = "新增入业务员提现单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "新增入业务员提现单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccWithdrawSaveReqVO createReqVO) {
        createReqVO.setMerchantId(SecurityUtils.getLoginUser().getColonelId());
        // 创建入业务员提现单
        AccWithdraw withdraw = accWithdrawService.insertAccWithdraw(createReqVO);
        return success(withdraw.getWithdrawId());
    }

    /**
     *
     * 获取入业务员账户提现单详细信息
     */
    @ApiOperation(value = "获得入业务员账户提现单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{withdrawId}")
    public CommonResult<AccWithdrawRespVO> getInfo(@PathVariable("withdrawId") Long withdrawId) {
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawId);
        return success(AccWithdrawConvert.INSTANCE.convert(accWithdraw));
    }

    /**
     * 获取入业务员提现列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得入业务员账户提现单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccOtherWithdrawRespVO>> getPage(@Valid AccWithdrawPageReqVO pageReqVO) {
        // 业务员
        pageReqVO.setMerchantTypeList(ListUtil.toList(MerchantTypeEnum.COLONEL.getType()));
        Long colonelId = SecurityUtils.getLoginUser().getColonelId();
        if (Objects.nonNull(colonelId)) {
            pageReqVO.setMerchantId(colonelId);
        }
        PageResult<AccWithdrawRespVO> pageResult = accWithdrawService.getAccWithdrawPage(pageReqVO);
        PageResult<AccOtherWithdrawRespVO> accountResult = AccWithdrawConvert.INSTANCE.convertOtherPage(pageResult);
        accountResult.getList().forEach(item -> AccountConvert.INSTANCE.convert(item, accountCacheService.getColonelDTO(item.getMerchantId())));
        return success(accountResult);
    }

    /**
     * 提现单审核拒绝
     */
    @ApiOperation(value = "提现单审核拒绝", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "业务员提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/disable")
    public CommonResult<Boolean> disable(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        // 获取提现单
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawAudit.getWithdrawId());
        // 验证是否是 业务员提现单
        validateOtherWithdraw(accWithdraw);
        // 拒绝提现
        accWithdrawService.rejectWithdraw(withdrawAudit);
        return success(true);
    }

    /**
     * 提现单审核通过
     */
    @ApiOperation(value = "提现单审核通过", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "业务员提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/enable")
    public CommonResult<Boolean> enable(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        // 获取提现单
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawAudit.getWithdrawId());
        // 验证是否是 业务员提现单
        validateOtherWithdraw(accWithdraw);
        // 处理入业务员结算
        accWithdrawService.processWalletWithdraw(accWithdraw);
        return success(true);
    }

    /**
     * 结算重试
     */
    @ApiOperation(value = "业务员提现结算重试", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SETTLE)
    @RequiresPermissions(Permissions.SETTLE)
    @Log(title = "业务员账户提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/settle")
    public CommonResult<TransferSubmitRespDTO> settle(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        // 获取提现单
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawAudit.getWithdrawId());
        // 平台提现单直接进行处理转账
        if (accWithdraw.getMerchantType().equals(MerchantTypeEnum.COLONEL.getType())) {
            // 平台提现单直接进行处理转账
            accWithdrawService.settle(accWithdraw);
        }
        return success(null);
    }

    private static void validateOtherWithdraw(AccWithdraw accWithdraw) {
        if (Objects.isNull(accWithdraw) || !accWithdraw.getMerchantType().equals(MerchantTypeEnum.COLONEL.getType())) {
            // 提现单不存在
            throw exception(WITHDRAW_IS_EMPTY);
        }
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加入业务员提现单 */
        public static final String ADD = "account:withdraw-colonel:add";
        /** 编辑 */
        public static final String EDIT = "account:withdraw-colonel:edit";
        /** 删除 */
        public static final String DELETE = "account:withdraw-colonel:remove";
        /** 列表 */
        public static final String LIST = "account:withdraw-colonel:list";
        /** 查询 */
        public static final String GET = "account:withdraw-colonel:query";
        /** 审核驳回 */
        public static final String DISABLE = "account:withdraw-colonel:disable";
        /** 审核通过 */
        public static final String ENABLE = "account:withdraw-colonel:enable";
        /** 结算重试 */
        public static final String SETTLE = "account:withdraw-colonel:settle";
    }
}
