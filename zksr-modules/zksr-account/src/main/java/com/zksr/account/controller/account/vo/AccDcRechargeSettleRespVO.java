package com.zksr.account.controller.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 运营商储值结算余额
 * @date 2025/2/15 9:25
 */
@Data
@ApiModel(description = "运营商储值结算余额")
public class AccDcRechargeSettleRespVO {

    @JsonSerialize(contentUsing = ToStringSerializer.class)
    @ApiModelProperty("运营商ID")
    private Long dcId;

    @ApiModelProperty("运营商名称")
    private String dcName;

    /** 可提现金额 */
    @ApiModelProperty(value = "可提现金额")
    private BigDecimal withdrawableAmt = BigDecimal.ZERO;

    @ApiModelProperty("当日交易冻结资金")
    private BigDecimal d0BalanceAmt = BigDecimal.ZERO;

}
