package com.zksr.account.client.hlb.vo.body;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝商户查询body
 * @date 2024/7/12 14:09
 */
@Data
public class HlbMerchantInfoBodyVO {
    private String orderNo;
    private String merchantNo;
    private String firstClassMerchantNo;

    public static HlbMerchantInfoBodyVO buildRegisterQuery(String orderNo, String firstClassMerchantNo) {
        HlbMerchantInfoBodyVO res = new HlbMerchantInfoBodyVO();
        res.setOrderNo(orderNo);
        res.setFirstClassMerchantNo(firstClassMerchantNo);
        return res;
    }


    public static HlbMerchantInfoBodyVO buildInfoQuery(String merchantNo, String firstClassMerchantNo) {
        HlbMerchantInfoBodyVO res = new HlbMerchantInfoBodyVO();
        res.setMerchantNo(merchantNo);
        res.setFirstClassMerchantNo(firstClassMerchantNo);
        return res;
    }


}
