package com.zksr.account.util;

import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.utils.SpringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/7 11:28
 */
public class WxB2bPayUtil {

    /**
     * 获取订单类型支付信息,  支付发起者信息
     * @param settlements
     * @param orderType
     * @param platform
     * @return
     */
    public static AccPlatformMerchant getPaySponsorMerchant(List<OrderSettlementDTO> settlements, Integer orderType, String platform) {
        List<OrderSettlementDTO> payOrderSettles;
        if (orderType == OrderTypeConstants.BRANCH_CHARGE) {
            // 门店充值验证非运营商总利润
            payOrderSettles = settlements.stream().filter(item -> MerchantTypeEnum.isDc(item.getMerchantType())).collect(Collectors.toList());
        } else {
            // 非入驻商总利润
            payOrderSettles = settlements.stream().filter(item -> MerchantTypeEnum.isSupplier(item.getMerchantType())).collect(Collectors.toList());
        }
        if (payOrderSettles.isEmpty()) {
            return null;
        }
        // 订单支付发起方
        OrderSettlementDTO payMerchant = payOrderSettles.get(0);
        return SpringUtils.getBean(IAccPlatformMerchantService.class).getPlatformMerchant(payMerchant.getMerchantId(), payMerchant.getMerchantType(), platform);
    }
    /**
     * 获取订单类型支付信息,  支付发起者信息
     * @return
     */
    public static AccPlatformMerchant getPlatformMerchant( long merchantId,String merchantType, String platform) {
        return SpringUtils.getBean(IAccPlatformMerchantService.class).getPlatformMerchant(merchantId, merchantType, platform);
    }
}
