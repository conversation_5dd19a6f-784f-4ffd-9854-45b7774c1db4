package com.zksr.account.controller.withdraw;

import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.controller.withdraw.vo.*;
import com.zksr.account.convert.account.AccountConvert;
import com.zksr.account.convert.withdraw.AccWithdrawConvert;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.dc.dto.DcDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 运营商账户提现单Controller
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Api(tags = "管理后台 - 运营商账户提现单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/withdraw-dc")
public class AccDcWithdrawController {

    @Autowired
    private IAccWithdrawService accWithdrawService;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 新增运营商账户提现单
     */
    @ApiOperation(value = "新增运营商账户提现单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "运营商账户提现单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccWithdrawSaveReqVO createReqVO) {
        // 设置账户类型为运营商
        createReqVO.setMerchantType(MerchantTypeEnum.DC.getType());
        // 创建运营商提现单
        AccWithdraw withdraw = accWithdrawService.insertAccWithdraw(createReqVO);
        return success(withdraw.getWithdrawId());
    }
    /**
     * 获取运营商账户提现单详细信息
     */
    @ApiOperation(value = "获得运营商账户提现单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{withdrawId}")
    public CommonResult<AccWithdrawRespVO> getInfo(@PathVariable("withdrawId") Long withdrawId) {
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawId);
        return success(AccWithdrawConvert.INSTANCE.convert(accWithdraw));
    }

    /**
     * 分页查询运营商账户提现单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得运营商账户提现单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccDcWithdrawRespVO>> getPage(@Valid AccWithdrawPageReqVO pageReqVO) {
        pageReqVO.setMerchantType(MerchantTypeEnum.DC.getType());
        PageResult<AccWithdrawRespVO> pageResult = accWithdrawService.getAccWithdrawPage(pageReqVO);
        PageResult<AccDcWithdrawRespVO> accountResult = AccWithdrawConvert.INSTANCE.convertDcPage(pageResult);
        accountResult.getList().forEach(item -> {
            // 设置运营商
            DcDTO dc = accountCacheService.getDcDTO(item.getMerchantId());
            AccountConvert.INSTANCE.convert(item, dc);
        });
        return success(accountResult);
    }

    /**
     * 提现单审核拒绝
     */
    @ApiOperation(value = "提现单审核拒绝", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DISABLE)
    @RequiresPermissions(Permissions.DISABLE)
    @Log(title = "运营商账户提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/disable")
    public CommonResult<Boolean> disable(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        accWithdrawService.rejectWithdraw(withdrawAudit);
        return success(true);
    }

    /**
     * 提现单审核通过
     */
    @ApiOperation(value = "提现单审核通过", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ENABLE)
    @RequiresPermissions(Permissions.ENABLE)
    @Log(title = "运营商账户提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/enable")
    public CommonResult<TransferSubmitRespDTO> enable(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        // 获取提现单
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawAudit.getWithdrawId());
        if (accWithdraw.getMerchantType().equals(MerchantTypeEnum.DC.getType())) {
            // 平台提现单直接进行处理转账
            return success(accWithdrawService.processWithdraw(accWithdraw));
        }
        return success(null);
    }

    /**
     * 结算重试
     */
    @ApiOperation(value = "运营商提现结算重试", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.SETTLE)
    @RequiresPermissions(Permissions.SETTLE)
    @Log(title = "运营商账户提现单", businessType = BusinessType.UPDATE)
    @PostMapping("/settle")
    public CommonResult<Boolean> settle(@Valid @RequestBody AccWithdrawAuditVO withdrawAudit) {
        // 获取提现单
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawAudit.getWithdrawId());
        // 平台提现单直接进行处理转账
        if (accWithdraw.getMerchantType().equals(MerchantTypeEnum.DC.getType())) {
            // 平台提现单直接进行处理转账
            accWithdrawService.settle(accWithdraw);
        }
        return success(true);
    }


    /**
     * 运营商直接结算(适用于运营商储值提现)
     */
    @ApiOperation(value = "运营商直接结算(适用于运营商储值提现)", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.DIRECT_SETTLE)
    @RequiresPermissions(Permissions.DIRECT_SETTLE)
    @Log(title = "运营商直接结算", businessType = BusinessType.INSERT)
    @PostMapping("/directSettle")
    public CommonResult<Long> directSettle(@Valid @RequestBody AccDirectSettleWithdrawSaveReqVO createReqVO) {
        // 创建入入驻商提现单
        createReqVO.setPayType(PayTypeEnum.PAY);
        createReqVO.setMerchantType(MerchantTypeEnum.DC);
        createReqVO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        // 创建入入驻商提现单
        AccWithdraw withdraw = accWithdrawService.insertDirectWithdraw(createReqVO);
        // 处理入入驻商结算
        accWithdrawService.directSettle(withdraw);
        return success(withdraw.getWithdrawId());
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:withdraw-dc:add";
        /** 编辑 */
        public static final String EDIT = "account:withdraw-dc:edit";
        /** 删除 */
        public static final String DELETE = "account:withdraw-dc:remove";
        /** 列表 */
        public static final String LIST = "account:withdraw-dc:list";
        /** 查询 */
        public static final String GET = "account:withdraw-dc:query";
        /** 审核驳回 */
        public static final String DISABLE = "account:withdraw-dc:disable";
        /** 审核通过 */
        public static final String ENABLE = "account:withdraw-dc:enable";
        /** 结算重试 */
        public static final String SETTLE = "account:withdraw-dc:settle";
        /** 直接结算(适用于储值支付提现) */
        public static final String DIRECT_SETTLE = "account:withdraw-dc:direct-settle";
    }
}
