package com.zksr.account.service.impl;

import com.github.pagehelper.Page;
import com.zksr.account.controller.divide.vo.AccDivideDtlRespVO;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.domain.PayCallBack;
import com.zksr.common.core.enums.DistributionModeEnum;
import com.zksr.common.core.enums.DivideStateEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.PageUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.zksr.account.mapper.AccDivideDtlMapper;
import com.zksr.account.convert.divide.AccDivideDtlConvert;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.controller.divide.vo.AccDivideDtlPageReqVO;
import com.zksr.account.controller.divide.vo.AccDivideDtlSaveReqVO;
import com.zksr.account.service.IAccDivideDtlService;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 支付分账详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Service
@Slf4j
public class AccDivideDtlServiceImpl implements IAccDivideDtlService {

    @Autowired
    private AccDivideDtlMapper accDivideDtlMapper;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 新增支付分账详情
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    public Long insertAccDivideDtl(AccDivideDtlSaveReqVO createReqVO) {
        // 插入
        AccDivideDtl accDivideDtl = AccDivideDtlConvert.INSTANCE.convert(createReqVO);
        accDivideDtlMapper.insert(accDivideDtl);
        // 返回
        return accDivideDtl.getDivideDtlId();
    }

    /**
     * 修改支付分账详情
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAccDivideDtl(AccDivideDtlSaveReqVO updateReqVO) {
        accDivideDtlMapper.updateById(AccDivideDtlConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除支付分账详情
     *
     * @param divideDtlId 支付分账详情id
     */
    @Override
    public void deleteAccDivideDtl(Long divideDtlId) {
        // 删除
        accDivideDtlMapper.deleteById(divideDtlId);
    }

    /**
     * 批量删除支付分账详情
     *
     * @param divideDtlIds 需要删除的支付分账详情主键
     * @return 结果
     */
    @Override
    public void deleteAccDivideDtlByDivideDtlIds(Long[] divideDtlIds) {
        for(Long divideDtlId : divideDtlIds){
            this.deleteAccDivideDtl(divideDtlId);
        }
    }

    /**
     * 获得支付分账详情
     *
     * @param divideDtlId 支付分账详情id
     * @return 支付分账详情
     */
    @Override
    public AccDivideDtl getAccDivideDtl(Long divideDtlId) {
        return accDivideDtlMapper.selectById(divideDtlId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccDivideDtlRespVO> getAccDivideDtlPage(AccDivideDtlPageReqVO pageReqVO) {
        Page<AccDivideDtlRespVO> page = PageUtils.startPage(pageReqVO);
        List<AccDivideDtlRespVO> divideDtls = AccDivideDtlConvert.INSTANCE.convertRespVOList(accDivideDtlMapper.selectPageExt(pageReqVO));
        for (AccDivideDtlRespVO divideDtl : divideDtls) {
            SupplierDTO supplierDTO = accountCacheService.getSupplierDTO(divideDtl.getMerchantId());
            if (Objects.nonNull(supplierDTO)) {
                divideDtl.setMerchantName(supplierDTO.getSupplierName());
            }
        }
        return PageResult.result(page, divideDtls);
    }

    @Override
    @Transactional
    //!@回调支付 - 5、记录分账(AccDivideDtl)信息[实际没什么作用]
    public void createDivide(AccPayFlow payFlow, PayOrderRespDTO orderRespDTO) {
        PayChannelEnum payPlatform = PayChannelEnum.fromValue(payFlow.getPlatform());
        // 分账失败的商户
        Map<String, List<PayOrderRespDTO.SettleDTO>> failAccountNoMap = orderRespDTO.getFailSettle().stream().collect(
                Collectors.groupingBy(PayOrderRespDTO.SettleDTO::getAccountNo)
        );
        
        //O2O模式的，不需要分账
        PayCallBack callBack = orderRespDTO.getCallBack();
        if (ToolUtil.isNotEmpty(callBack) && DistributionModeEnum.O2O.getCode().equals(callBack.getDistributionMode())){
            log.info("createDivide-> subOrderNo：{} ， O2O模式的，不需要分账 ",payFlow.getOutTradeNo());
            return;
        }
        
        ArrayList<AccDivideDtl> saveList = new ArrayList<>();
        for (OrderSettlementDTO settlementDTO : payFlow.buildSettle()) {
            // 这里判断是失败的, 或者不是在线分账
            AccDivideDtl divideDtl = AccDivideDtl.builder()
                    .sysCode(payFlow.getSysCode())
                    .merchantType(settlementDTO.getMerchantType())
                    .merchantId(settlementDTO.getMerchantId())
                    .altMchNo(settlementDTO.getAccountNo())
                    .orderAmt(payFlow.getPayAmt())
                    .altAmt(settlementDTO.getAmt())
                    .fee(payFlow.getFee())
                    .platform(payFlow.getPlatform())
                    .payFlowId(payFlow.getPayFlowId())
                    .onlineOrOffline(NumberPool.INT_ZERO)
                    // 默认分账成功
                    .onlineDivideState(DivideStateEnum.FINISH.getState())
                    .tradeNo(payFlow.getTradeNo())
                    .subTradeNo(settlementDTO.getSubOrderNo())
                    .outTradeNo(payFlow.getOutTradeNo())
                    .payType(NumberPool.INT_ZERO)
                    .build();

            // 分账失败
            if (failAccountNoMap.containsKey(settlementDTO.getAccountNo()) || !PayChannelEnum.onLineDivide(payFlow.getPlatform())) {
                // 线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账
                divideDtl.setOnlineDivideState(DivideStateEnum.FAIL.getState());
            }

            // 如果是微信支付, 提前不分账
            if (payPlatform.getDivideModel() == NumberPool.INT_ONE) {
                // 线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账
                divideDtl.setOnlineDivideState(DivideStateEnum.UNDEFINED.getState());
            }

            // 存在分账流水, 记录数据
            if (Objects.nonNull(divideDtl)) {
                saveList.add(divideDtl);
            }
        }
        if (!saveList.isEmpty()) {
            accDivideDtlMapper.insertBatch(saveList);
        }
    }

    @Override
    public void createDivide(AccPayFlow payFlow, PayRefundRespDTO refundRespDTO) {
        PayChannelEnum payPlatform = PayChannelEnum.fromValue(payFlow.getPlatform());
        // 分账失败的商户
        Map<String, List<PayRefundRespDTO.SettleDTO>> failAccountNoMap = refundRespDTO.getFailSettle().stream().collect(
                Collectors.groupingBy(PayRefundRespDTO.SettleDTO::getAccountNo)
        );
        ArrayList<AccDivideDtl> saveList = new ArrayList<>();
        for (OrderSettlementDTO settlementDTO : payFlow.buildSettle()) {
            AccDivideDtl divideDtl = AccDivideDtl.builder()
                    .sysCode(payFlow.getSysCode())
                    .merchantType(settlementDTO.getMerchantType())
                    .merchantId(settlementDTO.getMerchantId())
                    .altMchNo(settlementDTO.getAccountNo())
                    .orderAmt(payFlow.getPayAmt())
                    .altAmt(BigDecimal.ZERO.subtract(settlementDTO.getAmt()))
                    .fee(payFlow.getFee())
                    .platform(payFlow.getPlatform())
                    .payFlowId(payFlow.getPayFlowId())
                    .onlineOrOffline(NumberPool.INT_ZERO)
                    // 默认分账成功
                    .onlineDivideState(DivideStateEnum.FINISH.getState())
                    .tradeNo(payFlow.getTradeNo())
                    .outTradeNo(payFlow.getOutTradeNo())
                    .refundNo(payFlow.getRefundNo())
                    .subRefundNo(settlementDTO.getSubOrderNo())
                    .outRefundNo(payFlow.getOutRefundNo())
                    .payType(NumberPool.INT_ONE)
                    .build();
            // 这里判断是失败的, 或者不是在线分账
            if (failAccountNoMap.containsKey(settlementDTO.getAccountNo()) || !PayChannelEnum.onLineDivide(payFlow.getPlatform())) {
                // 0-线上分账  1-线下分账
                divideDtl.setOnlineOrOffline(NumberPool.INT_ONE);
                // 线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账
                divideDtl.setOnlineDivideState(DivideStateEnum.FAIL.getState());
            }

            // 如果是微信支付, 提前未分账
            if (payPlatform.getDivideModel() == NumberPool.INT_ONE) {
                // 线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账
                divideDtl.setOnlineDivideState(DivideStateEnum.UNDEFINED.getState());
            }

            // 存在分账流水, 记录数据
            if (Objects.nonNull(divideDtl)) {
                saveList.add(divideDtl);
            }
        }
        if (!saveList.isEmpty()) {
            accDivideDtlMapper.insertBatch(saveList);
        }
    }

    @Override
    public List<AccDivideDtl> getDivideList(AccDivideDtl divideDtl) {
        return accDivideDtlMapper.selectDivideList(divideDtl);
    }

    @Override
    public void updateBatch(ArrayList<AccDivideDtl> updateList) {
        accDivideDtlMapper.updateBatch(updateList);
    }

}
