package com.zksr.account.api.invoiceSetting;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.zksr.account.api.invoiceSetting.dto.AccInvoiceSettingDTO;
import com.zksr.account.api.invoiceSetting.vo.AccInvoiceSettingRespVO;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantWxb2bDTO;
import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.convert.account.AccInvoiceSetttingConvert;
import com.zksr.account.convert.wxb2b.AccPlatformMerchantWxb2bConvert;
import com.zksr.account.domain.AccInvoiceSetting;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.service.IAccInVoiceSettingService;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccPlatformMerchantWxb2bService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.metchant.PlatformMerchantService;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;
import static com.zksr.account.enums.ErrorCodeConstants.NOT_EXIST_PAY_CONFIG;
import static com.zksr.account.enums.ErrorCodeConstants.PLATFORM_MERCHANT_SERVICE_UNDEFINE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

@InnerAuth
@ApiIgnore
@Slf4j
@RestController
public class AccInvoiceSettingApiImpl implements AccInvoiceSettingApi {

    @Autowired
    private IAccInVoiceSettingService inVoiceSettingService;

    @Autowired
    private IAccountCacheService accountCacheService;


    @Autowired
    private List<PlatformMerchantService> merchantService;

    @Override
    public CommonResult<Long> saveAccInvoiceSetting(@RequestBody AccInvoiceSettingDTO accInvoiceSettingDTO){
        Long accVoiceSettingId = inVoiceSettingService.saveAccInvoiceSetting(BeanUtil.toBean(accInvoiceSettingDTO, AccInvoiceSettingSaveReqVO.class));;
        return CommonResult.success(accVoiceSettingId);
    }
    @Override
    public CommonResult<AccInvoiceSettingRespVO> getAccInvoiceSettingRespVO(String merchantType, Long merchantId, String platform ,Long sysCode) {
        AccInvoiceSetting accInvoiceSetting = inVoiceSettingService.getAccInvoiceSetting(merchantId, merchantType, platform, sysCode);
        return CommonResult.success(BeanUtil.toBean(accInvoiceSetting, AccInvoiceSettingRespVO.class));
    }
    
    @Override
    public CommonResult<List<AccInvoiceSettingRespVO>> getInvoiceSettingList(AccPlatformMerchantRespVO build) {
        List<AccInvoiceSetting> accInvoiceSettingList = inVoiceSettingService.getAccInvoiceSettingList(
                build.getMerchantId(), build.getMerchantType(), build.getSysCode());
        return CommonResult.success(AccInvoiceSetttingConvert.INSTANCE.convertToAccInvoiceSettingRespVO(accInvoiceSettingList));
    }
    
}
