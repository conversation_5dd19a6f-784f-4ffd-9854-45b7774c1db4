package com.zksr.account.mapper;

import cn.hutool.core.date.DateUtil;
import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.controller.divide.vo.AccDivideFlowPageReqVO;

import java.util.Date;
import java.util.List;


/**
 * 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
@Mapper
public interface AccDivideFlowMapper extends BaseMapperX<AccDivideFlow> {
    default PageResult<AccDivideFlow> selectPage(AccDivideFlowPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccDivideFlow>()
                    .eqIfPresent(AccDivideFlow::getDivideFlowId, reqVO.getDivideFlowId())
                    .eqIfPresent(AccDivideFlow::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccDivideFlow::getPlatform, reqVO.getPlatform())
                    .eqIfPresent(AccDivideFlow::getType, reqVO.getType())
                    .eqIfPresent(AccDivideFlow::getOutTradeNo, reqVO.getOutTradeNo())
                    .eqIfPresent(AccDivideFlow::getOutRefundNo, reqVO.getOutRefundNo())
                    .eqIfPresent(AccDivideFlow::getSourcePlatformMerchantId, reqVO.getSourcePlatformMerchantId())
                    .eqIfPresent(AccDivideFlow::getTargetPlatformMerchantId, reqVO.getTargetPlatformMerchantId())
                    .eqIfPresent(AccDivideFlow::getMchid, reqVO.getMchid())
                    .eqIfPresent(AccDivideFlow::getPayeeType, reqVO.getPayeeType())
                    .eqIfPresent(AccDivideFlow::getPayeeNo, reqVO.getPayeeNo())
                    .eqIfPresent(AccDivideFlow::getDivideAmt, reqVO.getDivideAmt())
                    .eqIfPresent(AccDivideFlow::getDivideStatus, reqVO.getDivideStatus())
                    .eqIfPresent(AccDivideFlow::getDivideReverseStatus, reqVO.getDivideReverseStatus())
                    .eqIfPresent(AccDivideFlow::getErrMsg, reqVO.getErrMsg())
                .orderByDesc(AccDivideFlow::getDivideFlowId));
    }

    default List<AccDivideFlow> selectTryDivideFlow(Long minId) {
        return selectList(new LambdaQueryWrapperX<AccDivideFlow>()
                .gt(AccDivideFlow::getDivideFlowId, minId)
                .eq(AccDivideFlow::getDivideStatus, NumberPool.INT_ONE)
                .eq(AccDivideFlow::getDivideReqStatus, NumberPool.INT_ZERO)
                // 15分钟之前的数据
                .lt(AccDivideFlow::getCreateTime, DateUtil.offsetMinute(new Date(), -10))
                .gt(AccDivideFlow::getCreateTime, DateUtil.offsetDay(new Date(), -30))
                .last("LIMIT 500")
                .orderByAsc(AccDivideFlow::getDivideFlowId));
    }
}
