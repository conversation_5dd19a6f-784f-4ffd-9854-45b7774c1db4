package com.zksr.account.controller.withdraw;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.service.IAccWithdrawService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawSaveReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawRespVO;
import com.zksr.account.convert.withdraw.AccWithdrawConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 账户提现单Controller
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Api(tags = "管理后台 - 账户提现单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/withdraw")
public class AccWithdrawController {
    @Autowired
    private IAccWithdrawService accWithdrawService;

    /**
     * 新增账户提现单
     */
    @ApiOperation(value = "新增账户提现单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "账户提现单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccWithdrawSaveReqVO createReqVO) {
        AccWithdraw withdraw = accWithdrawService.insertAccWithdraw(createReqVO);
        return success(withdraw.getWithdrawId());
    }

    /**
     * 获取账户提现单详细信息
     */
    @ApiOperation(value = "获得账户提现单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{withdrawId}")
    public CommonResult<AccWithdrawRespVO> getInfo(@PathVariable("withdrawId") Long withdrawId) {
        AccWithdraw accWithdraw = accWithdrawService.getAccWithdraw(withdrawId);
        return success(AccWithdrawConvert.INSTANCE.convert(accWithdraw));
    }

    /**
     * 分页查询账户提现单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得账户提现单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccWithdrawRespVO>> getPage(@Valid AccWithdrawPageReqVO pageReqVO) {
        PageResult<AccWithdrawRespVO> pageResult = accWithdrawService.getAccWithdrawPage(pageReqVO);
        return success(pageResult);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:withdraw:add";
        /** 编辑 */
        public static final String EDIT = "account:withdraw:edit";
        /** 删除 */
        public static final String DELETE = "account:withdraw:remove";
        /** 列表 */
        public static final String LIST = "account:withdraw:list";
        /** 查询 */
        public static final String GET = "account:withdraw:query";
        /** 停用 */
        public static final String DISABLE = "account:withdraw:disable";
        /** 启用 */
        public static final String ENABLE = "account:withdraw:enable";
    }
}
