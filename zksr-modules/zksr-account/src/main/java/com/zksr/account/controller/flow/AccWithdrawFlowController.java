package com.zksr.account.controller.flow;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccWithdrawFlow;
import com.zksr.account.service.IAccWithdrawFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.controller.flow.vo.AccWithdrawFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowSaveReqVO;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowRespVO;
import com.zksr.account.convert.flow.AccWithdrawFlowConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 支付平台提现流水Controller
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Api(tags = "管理后台 - 支付平台提现流水接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/flow")
public class AccWithdrawFlowController {
    @Autowired
    private IAccWithdrawFlowService accWithdrawFlowService;

    /**
     * 获取支付平台提现流水详细信息
     */
    @ApiOperation(value = "获得支付平台提现流水详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{withdrawFlowId}")
    public CommonResult<AccWithdrawFlowRespVO> getInfo(@PathVariable("withdrawFlowId") Long withdrawFlowId) {
        AccWithdrawFlow accWithdrawFlow = accWithdrawFlowService.getAccWithdrawFlow(withdrawFlowId);
        return success(AccWithdrawFlowConvert.INSTANCE.convert(accWithdrawFlow));
    }

    /**
     * 分页查询支付平台提现流水
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得支付平台提现流水分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccWithdrawFlowRespVO>> getPage(@Valid AccWithdrawFlowPageReqVO pageReqVO) {
        PageResult<AccWithdrawFlow> pageResult = accWithdrawFlowService.getAccWithdrawFlowPage(pageReqVO);
        return success(AccWithdrawFlowConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:flow:add";
        /** 编辑 */
        public static final String EDIT = "account:flow:edit";
        /** 删除 */
        public static final String DELETE = "account:flow:remove";
        /** 列表 */
        public static final String LIST = "account:flow:list";
        /** 查询 */
        public static final String GET = "account:flow:query";
        /** 停用 */
        public static final String DISABLE = "account:flow:disable";
        /** 启用 */
        public static final String ENABLE = "account:flow:enable";
    }
}
