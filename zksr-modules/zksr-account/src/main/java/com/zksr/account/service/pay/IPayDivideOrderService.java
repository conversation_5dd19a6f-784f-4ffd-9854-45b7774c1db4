package com.zksr.account.service.pay;


import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.model.pay.vo.CreateDivideReqVO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.common.core.enums.PayChannelEnum;

/**
 * <AUTHOR>
 * @time 2024/3/7
 * @desc 微信B2B 支付特殊逻辑处理
 */
public interface IPayDivideOrderService {

    /**
     * 支付平台
     */
    PayChannelEnum getPlatform();

    /**
     * 请求分账
     * @param divideReqVO 请求数据
     */
    CreateDivideRespVO divide(CreateDivideReqVO divideReqVO);

    /**
     * 检查分账是否可以完成
     * @param divideReqVO
     */
    void divideCheckOver(CreateDivideReqVO divideReqVO);

    /**
     * 更新分账状态
     * @param divideFlowDTO
     */
    void updateDivideStatus(DivideFlowDTO divideFlowDTO);

    /***
     * 订单全部退款了, 需要提前完成分账
     * @param payFlow   支付流水
     */
    CreateDivideRespVO refundOver(PayFlowDTO payFlow);
}
