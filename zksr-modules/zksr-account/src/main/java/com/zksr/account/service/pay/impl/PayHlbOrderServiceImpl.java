package com.zksr.account.service.pay.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.client.PayClient;
import com.zksr.account.client.hlb.HlbAppletPayClient;
import com.zksr.account.client.hlb.HlbSdkClient;
import com.zksr.account.controller.divide.vo.AccDivideFlowSaveReqVO;
import com.zksr.account.convert.divide.AccDivideDtlConvert;
import com.zksr.account.convert.pay.PayConvert;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.mapper.AccDivideFlowMapper;
import com.zksr.account.mapper.AccPayFlowMapper;
import com.zksr.account.model.pay.dto.order.PayInfoDTO;
import com.zksr.account.model.pay.vo.CreateDivideReqVO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.DivideReqVO;
import com.zksr.account.model.pay.vo.PayWxB2bDivideRespVO;
import com.zksr.account.service.IAccPayFlowService;
import com.zksr.account.service.pay.PayChannelService;
import com.zksr.common.core.enums.DivideStateEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.PAY_FLOW_NOT_EXIST;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信B2B 支付特殊逻辑处理
 * @date 2024/9/21 8:52
 */
@Slf4j
@Service
@SuppressWarnings(StringPool.ALL)
public class PayHlbOrderServiceImpl extends PayDivideOrderService {

    @Autowired
    private IAccPayFlowService payFlowService;

    @Autowired
    private AccDivideFlowMapper accDivideFlowMapper;

    @Autowired
    private AccPayFlowMapper accPayFlowMapper;

    @Resource
    private OrderApi orderApi;

    @Override
    public PayChannelEnum getPlatform() {
        return PayChannelEnum.HLB;
    }

    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_WXB2B_DIVIDE, condition = "#divideReqVO.tradeNo", tryLock = true)
    public CreateDivideRespVO divide(CreateDivideReqVO divideReqVO) {
        log.info("发起合利宝支付请求分账,req={}", JSON.toJSONString(divideReqVO));
        // 验证是否支付
        AccPayFlow payFlow = payFlowService.getByOrderPayFlowSuccessFlag(divideReqVO.getTradeNo());
        if (Objects.isNull(payFlow) || payFlow.getCallbackFlag() != NumberPool.INT_ONE) {
            log.info("发起合利宝支付请求分账, 支付记录不存, tradeNo:{}", divideReqVO.getTradeNo());
            throw exception(PAY_FLOW_NOT_EXIST);
        }
        // 查询支付商户信息
        PayInfoDTO payInfoDTO = StringUtils.isNotEmpty(payFlow.getReqInfo()) ? JSON.parseObject(payFlow.getReqInfo(), PayInfoDTO.class) : null;
        if (Objects.isNull(payInfoDTO)) {
            log.info("发起合利宝支付请求分账, 未查询到支付商户信息, tradeNo:{}", divideReqVO.getTradeNo());
            throw exception(PAY_FLOW_NOT_EXIST);
        }
        // 获取在线分账需要处理的数据
        AccDivideDtl filter = AccDivideDtlConvert.INSTANCE.convertDivideReqVO(divideReqVO);
        filter.setOnlineDivideState(DivideStateEnum.UNDEFINED.getState());
        List<AccDivideDtl> divideList = divideDtlService.getDivideList(filter);
        // 验证空
        if (divideList.isEmpty()) {
            log.info("无可结算数据时, 直接返回成功, 没有找到分账方信息 {}", divideReqVO.getTradeNo());
            return CreateDivideRespVO.success();
        }

        // 总分账金额
        BigDecimal totalAltAmt = divideList.stream().map(AccDivideDtl::getAltAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 分账接受账号
        Long merchantId = divideList.get(0).getMerchantId();
        // 允许下单的时候没有商户号, 但是分账请求分账的时候必须完成商户号进件
        // 分账的金额如果大于0, 那么就必须要求商户信息
        // 获取最新的商户号
        String altMchNo = getPayeeMerchantNo(divideReqVO, totalAltAmt, payFlow);
        if (NumberUtil.isGreater(totalAltAmt, BigDecimal.ZERO) && StringUtils.isEmpty(altMchNo)) {
            return CreateDivideRespVO.fail(StringUtils.format("商户ID:{}, 未查询到进件信息", merchantId));
        }

        // 创建分账流水
        AccDivideFlowSaveReqVO divideFlow = new AccDivideFlowSaveReqVO(payFlow, payInfoDTO, divideReqVO);
        divideFlow.setDivideAmt(totalAltAmt);
        divideFlow.setPayeeNo(altMchNo);
        divideFlowService.insertAccDivideFlow(divideFlow);

        // 根据平台 + appid 获取支付配置
        // 获取支付client
        PayClient payClient = getPayChannelService().getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
        HlbSdkClient client = ((HlbAppletPayClient) payClient).getClient();

        // 请求分账
        // 1: 分账中 2: 分账完成 3：分账失败
        CreateDivideRespVO divideRespVO;
        if (payFlow.getIsDivide() == NumberPool.INT_ONE) {
            divideRespVO = CreateDivideRespVO.success();
        } else {
            divideRespVO = client.divide(
                    DivideReqVO.builder()
                            .payFlow(PayConvert.INSTANCE.convertPayFlowDTO(payFlow))
                            .divideFlowId(divideFlow.getDivideFlowId())
                            .tradeNo(payFlow.getTradeNo())
                            .amount(totalAltAmt)
                            .payMerchantNo(payInfoDTO.getMerchantNo())
                            .receiveMerchantNo(altMchNo)
                            .build()
            );
        }
        if (!divideRespVO.isSuccess()) {
            // 失败
            this.fail(divideFlow, divideRespVO);
        } else {
            // 修改分账状态
            this.processing(divideList, divideFlow);
        }
        // 返回分账流水ID
        divideRespVO.setDivideFlowId(divideFlow.getDivideFlowId());
        return divideRespVO;
    }

    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_WXB2B_DIVIDE, condition = "divideReqVO.tradeNo", tryLock = true)
    public void divideCheckOver(CreateDivideReqVO divideReqVO) {
        log.info("验证订单是否可以合利宝支付分账完成, req={}", JSON.toJSONString(divideReqVO));
        // 验证是否支付
        AccPayFlow payFlow = payFlowService.getByOrderPayFlowSuccessFlag(divideReqVO.getTradeNo());
        if (Objects.isNull(payFlow) || payFlow.getCallbackFlag() != NumberPool.INT_ONE) {
            log.info("发起合利宝支付请求分账, 支付记录不存, order:{}", divideReqVO.getTradeNo());
            return;
        }

        // 查看是不是只有入驻商的没有完成了
        // 如果是的, 那就可以请求完成了
        // 获取在线分账需要处理的数据
        List<AccDivideDtl> divideList = divideDtlService.getDivideList(
                AccDivideDtl.builder()
                        .tradeNo(divideReqVO.getTradeNo())
                        .build()
        );
        // 查询分账完成的
        long finishCount = divideList.stream().filter(item ->item.getOnlineDivideState().equals(DivideStateEnum.FINISH.getState())).count();
        if (finishCount < divideList.size()) {
            return;
        }
        // 更新支付流水是已完成分账
        AccPayFlow flow = new AccPayFlow();
        flow.setPayFlowId(payFlow.getPayFlowId());
        flow.setIsDivide(NumberPool.INT_ONE);
        accPayFlowMapper.updateById(flow);

        // 这里需要回调结算, 告诉trade模块, 结算已经实际处理完成了
        orderApi.updateOrderDivideSettleState(payFlow.getTradeNo()).checkError();
    }

    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_WXB2B_DIVIDE, condition = "divideFlowDTO.tradeNo", tryLock = true)
    public void updateDivideStatus(DivideFlowDTO divideFlowDTO) {
        // 验证是不是已经处理完成了
        AccDivideFlow divideFlow = divideFlowService.getAccDivideFlow(divideFlowDTO.getDivideFlowId());
        if (divideFlow.getDivideStatus() != NumberPool.INT_ONE) {
            log.warn("更新合利宝分账最新状态, 但是这个单据已经不是待处理了, flowId={}", divideFlowDTO.getDivideFlowId());
            return;
        }
        AccPayFlow payFlow = payFlowService.getAccPayFlow(divideFlow.getAccPayFlowId());

        // 请求分账完成
        // 根据平台 + appid 获取支付配置
        // 获取支付client
        PayClient payClient = getPayChannelService().getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
        HlbSdkClient client = ((HlbAppletPayClient) payClient).getClient();

        // 查询分账结果
        // 1: 分账中 2: 分账完成 3：分账失败
        PayWxB2bDivideRespVO divideRespVO;
        // 是已分账, 或者分账金额是0, 就不用查询分账结果, 默认就是成功
        if (payFlow.getIsDivide() == NumberPool.INT_ONE || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, divideFlow.getDivideAmt())) {
            divideRespVO = PayWxB2bDivideRespVO.success();
        } else {
            divideRespVO = client.queryDivide(divideFlow);
        }
        // 分账成功
        this.updateDivide(divideFlow, divideRespVO);
    }

    @Override
    @Transactional
    public CreateDivideRespVO refundOver(PayFlowDTO payFlow) {
        log.info("合利宝支付, 因为全部订单退款, 所以不需要处理分账了, 标记所有的订单都是已完成, orderNo={}", payFlow.getTradeNo());
        AccDivideDtl filter = new AccDivideDtl();
        filter.setTradeNo(payFlow.getTradeNo());
        filter.setOnlineDivideState(DivideStateEnum.UNDEFINED.getState());
        List<AccDivideDtl> divideList = divideDtlService.getDivideList(filter);
        if (divideList.isEmpty()) {
            return CreateDivideRespVO.fail("合利宝支付不需要管订单退款操作");
        }

        // 标记分账流水为无需处理
        ArrayList<AccDivideDtl> updateDivideDtlList = new ArrayList<>();
        for (AccDivideDtl divideDtl : divideList) {
            AccDivideDtl updateDivideDtl = new AccDivideDtl();
            updateDivideDtl.setDivideDtlId(divideDtl.getDivideDtlId());
            updateDivideDtl.setOnlineDivideState(DivideStateEnum.NONE.getState());
            updateDivideDtl.setDivideTime(DateUtil.date());
            updateDivideDtlList.add(updateDivideDtl);
        }
        divideDtlService.updateBatch(updateDivideDtlList);

        // 退款完了直接标记订单已分账就完事了
        AccPayFlow flow = new AccPayFlow();
        flow.setPayFlowId(payFlow.getPayFlowId());
        flow.setIsDivide(NumberPool.INT_ONE);
        accPayFlowMapper.updateById(flow);
        return CreateDivideRespVO.fail("合利宝支付不需要管订单退款操作");
    }

    private PayChannelService getPayChannelService() {
        return SpringUtils.getBean(PayChannelService.class);
    }

}
