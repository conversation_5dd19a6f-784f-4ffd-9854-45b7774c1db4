package com.zksr.account.controller.recharge.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 账户充值单对象 acc_recharge
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("账户充值单 - acc_recharge 保存")
public class AccRechargeSaveReqVO {

    private static final long serialVersionUID = 1L;

    /** 充值金额 */
    @ApiModelProperty(value = "充值金额", required = true)
    private BigDecimal rechargeAmt;
}
