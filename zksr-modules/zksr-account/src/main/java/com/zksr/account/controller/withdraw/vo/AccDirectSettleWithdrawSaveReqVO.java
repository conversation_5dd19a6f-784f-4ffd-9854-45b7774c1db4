package com.zksr.account.controller.withdraw.vo;

import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 账户提现单对象 acc_withdraw
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
@Data
@ApiModel("直接结算提现单 - acc_withdraw 保存 VO")
public class AccDirectSettleWithdrawSaveReqVO {

    /** 提现金额 */
    @ApiModelProperty(value = "提现金额, 最低0.02元", required = true)
    @NotNull
    @DecimalMin(value = "0.02", message = "最低提现0.02元")
    private BigDecimal withdrawAmt;

    @ApiModelProperty(value = "支付平台", required = true)
    private PayChannelEnum platform;

    @ApiModelProperty(value = "支付体系", required = true, hidden = true)
    private PayTypeEnum payType;

    @ApiModelProperty(value = "商户类型", required = true, hidden = true)
    private MerchantTypeEnum merchantType;

    @ApiModelProperty(value = "商户ID", required = true)
    private Long merchantId;

    @ApiModelProperty(value = "平台商ID", required = true, hidden = true)
    private Long sysCode;

    @ApiModelProperty(value = "商户最小保留金")
    private BigDecimal minSettleAmt = BigDecimal.ZERO;
}
