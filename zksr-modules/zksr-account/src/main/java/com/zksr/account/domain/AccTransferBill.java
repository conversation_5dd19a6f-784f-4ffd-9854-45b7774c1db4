package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 交易对账单对象 acc_transfer_bill
 * 
 * <AUTHOR>
 * @date 2024-10-29
 */
@TableName(value = "acc_transfer_bill")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccTransferBill extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /** 交易账单ID */
    @Excel(name = "交易账单ID")
    private Long transferBillId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 账单日期 */
    @Excel(name = "账单日期")
    private String billDate;

    /** 交易笔数（包含支付和退款） */
    @Excel(name = "交易笔数")
    private Integer transferNum;

    /** 商户总支付金额 */
    @Excel(name = "商户总支付金额")
    private BigDecimal merchantTotalPayAmt;

    /** 平台总支付金额 */
    @Excel(name = "平台总支付金额")
    private BigDecimal platformTotalPayAmt;

    /** 商户总退款金额 */
    @Excel(name = "商户总退款金额")
    private BigDecimal merchantTotalRefundAmt;

    /** 平台总退款金额 */
    @Excel(name = "平台总退款金额")
    private BigDecimal platformTotalRefundAmt;

    /** 商户总支付手续费 */
    @Excel(name = "商户总支付手续费")
    private BigDecimal merchantTotalPayFree;

    /** 平台总支付手续费 */
    @Excel(name = "平台总支付手续费")
    private BigDecimal platformTotalPayFree;

    /** 商户总退款手续费 */
    @Excel(name = "商户总退款手续费")
    private BigDecimal merchantTotalRefundFree;

    /** 平台总退款手续费 */
    @Excel(name = "平台总退款手续费")
    private BigDecimal platformTotalRefundFree;

    /** 退款笔数 */
    @Excel(name = "退款笔数")
    private Integer refundCount;

    /** 支付平台, hlb-合利宝,wxb2b-微信b2b */
    @Excel(name = "支付平台")
    private String platform;
}