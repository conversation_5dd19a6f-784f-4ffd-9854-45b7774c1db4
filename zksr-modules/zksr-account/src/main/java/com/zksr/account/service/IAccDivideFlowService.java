package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.controller.divide.vo.AccDivideFlowPageReqVO;
import com.zksr.account.controller.divide.vo.AccDivideFlowSaveReqVO;

import java.util.List;

/**
 * 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)Service接口
 *
 * <AUTHOR>
 * @date 2024-09-21
 */
public interface IAccDivideFlowService {

    /**
     * 新增分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAccDivideFlow(@Valid AccDivideFlowSaveReqVO createReqVO);

    /**
     * 修改分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccDivideFlow(@Valid AccDivideFlowSaveReqVO updateReqVO);

    /**
     * 删除分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param divideFlowId 分账流水id
     */
    public void deleteAccDivideFlow(Long divideFlowId);

    /**
     * 批量删除分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param divideFlowIds 需要删除的分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)主键集合
     * @return 结果
     */
    public void deleteAccDivideFlowByDivideFlowIds(Long[] divideFlowIds);

    /**
     * 获得分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     *
     * @param divideFlowId 分账流水id
     * @return 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)
     */
    public AccDivideFlow getAccDivideFlow(Long divideFlowId);

    /**
     * 获得分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)分页
     *
     * @param pageReqVO 分页查询
     * @return 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)分页
     */
    PageResult<AccDivideFlow> getAccDivideFlowPage(AccDivideFlowPageReqVO pageReqVO);

    /**
     * 获取需要查询最新处理状态的分账流水
     * @param minId 查询最小起始ID
     * @return  每批次500条
     */
    List<AccDivideFlow> getTryDivideFlow(Long minId);

}
