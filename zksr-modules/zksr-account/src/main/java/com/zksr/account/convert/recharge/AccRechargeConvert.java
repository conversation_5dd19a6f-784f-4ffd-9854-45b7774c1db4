package com.zksr.account.convert.recharge;

import com.zksr.account.controller.recharge.vo.AccRechargeRespVO;
import com.zksr.account.domain.AccRecharge;
import com.zksr.common.core.web.pojo.PageResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 储值 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2025-02-11
*/
@Mapper
public interface AccRechargeConvert {

    AccRechargeConvert INSTANCE = Mappers.getMapper(AccRechargeConvert.class);

    PageResult<AccRechargeRespVO> convertRespPage(PageResult<AccRecharge> accRechargePage);
}