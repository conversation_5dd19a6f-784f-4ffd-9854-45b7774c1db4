package com.zksr.account.service.transfer.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.zksr.account.client.PayClient;
import com.zksr.account.client.TransferClient;
import com.zksr.account.client.hlb.HlbTransferClient;
import com.zksr.account.client.mideapay.MideaPayTransferClient;
import com.zksr.account.client.mock.MockTransferClient;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.transfer.TransferChannelService;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.model.dto.HlbPayConfig;
import com.zksr.system.api.model.dto.MideaPayConfig;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.HeLiBaoPayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

import static com.zksr.common.core.utils.CacheUtils.buildAsyncReloadingCache;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 获取交易操作对象
 * @date 2024/3/11 14:41
 */
@Slf4j
@Service
public class TransferChannelServiceImpl implements TransferChannelService {

    /**
     * {@link TransferClient} 缓存，30秒自动异步刷新
     */
    @Getter
    private final LoadingCache<TransferClient.ClientParam, TransferClient> transferClientMap = buildAsyncReloadingCache(Duration.ofSeconds(30L),
            new CacheLoader<TransferClient.ClientParam, TransferClient>() {
                @Override
                public TransferClient load(TransferClient.ClientParam param) {
                    return initClient(param.getSysCode(), param.getPlatform(), param.getMerchantType());
                }
            });

    @Autowired
    private MockTransferClient mockTransferClient;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 此方法无法失败, 商户支付体系
     * @param sysCode   平台商编号
     * @param platform  校验平台标识 {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    @Deprecated
    @Override
    public TransferClient getTransferClient(Long sysCode, String platform) {
        if (PayChannelEnum.MOCK.getCode().equals(platform)) {
            // 直接返回模拟支付支持
            return mockTransferClient;
        }
        return transferClientMap.getUnchecked(new TransferClient.ClientParam(sysCode, platform, null));
    }

    @Override
    public TransferClient getTransferClient(Long sysCode, String platform, String merchantType) {
        if (PayChannelEnum.MOCK.getCode().equals(platform)) {
            // 直接返回模拟支付支持
            return mockTransferClient;
        }
        return transferClientMap.getUnchecked(new TransferClient.ClientParam(sysCode, platform, merchantType));
    }

    private TransferClient initClient(Long sysCode, String platform, String merchantType) {
        try {
            /**
             * 2025年2月18日 , 暂不考虑入驻商充值分润模式
             */
            PayChannelEnum payChannel = PayChannelEnum.fromValue(platform);
            if(payChannel == PayChannelEnum.MIDEA_PAY){//美的付
                MideaPayConfig mideaPayConfig = redisSysConfigService.querySupplierConfig(MideaPayConfig.class);
                if(null == mideaPayConfig || mideaPayConfig.checkEmpty()){
                    throw new ServiceException("商户信息没有配置或缺失关键配置项");
                }
                MideaPayTransferClient mideaPayTransferClient = new MideaPayTransferClient();
                mideaPayTransferClient.setConfig(JSON.toJSONString(mideaPayConfig));
                return mideaPayTransferClient;
            }
            // 不管是因为什么进来的, 目前系统暂不支持 (入驻商充值分润模式) 合利宝支付提现
            // 除非SAAS平台, 入驻商按照平台商申请, 及入驻商小程序是平台商自己的
            // 原来的设计是小程序小程序是整个系统的, 由系统提供入驻商充值收款资质信息
            if (payChannel == PayChannelEnum.HLB) {
                //合利宝
                HlbTransferClient client = new HlbTransferClient();
                // 支付平台, 入驻商, 门店储值属于支付平台
                HeLiBaoPayConfigDTO heLiBaoConfig = accountCacheService.getHeLiBaoConfig(sysCode);
                HlbPayConfig payConfig = new HlbPayConfig();
                payConfig.setMerchantNo(heLiBaoConfig.getHeLiBaoMerchantPlatformAccount());
                payConfig.setNotifyUrl(heLiBaoConfig.getPayCallBackUrl());
                payConfig.setPubCert(heLiBaoConfig.getPublicKeyCertificate());
                payConfig.setMerchantPrivateKey(heLiBaoConfig.getPrivateKeyCertificate());
                payConfig.setPass(heLiBaoConfig.getPrivateKeyPassword());
                client.setConfig(JSON.toJSONString(payConfig));
                return client;
            }
        } catch (ServiceException e) {
            log.error("商户交易客户端异常", e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("商户交易客户端异常", e);
            throw new RuntimeException(e);
        }
        return null;
    }

}
