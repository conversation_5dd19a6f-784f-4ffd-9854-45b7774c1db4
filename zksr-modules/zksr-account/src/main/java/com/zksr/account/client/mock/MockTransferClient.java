package com.zksr.account.client.mock;

import cn.hutool.core.util.RandomUtil;
import com.zksr.account.client.TransferClient;
import com.zksr.account.client.hlb.HlbTransferSdkClient;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.common.core.constant.PayConstants;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.TransferStatusEnum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝账户交易
 * @date 2024/3/11 14:36
 */
@Component
public class MockTransferClient implements TransferClient {

    private HlbTransferSdkClient client;

    @Override
    public String getPlatform() {return PayChannelEnum.MOCK.getCode();}

    @Override
    public void setConfig(String config) throws Exception {
    }

    @Override
    public TransferSubmitRespDTO transferSubmit(TransferSubmitReqVO reqVO) {
        TransferSubmitRespDTO respDTO = new TransferSubmitRespDTO();
        respDTO.setStatus(TransferStatusEnum.SUCCESS.getStatus());
        respDTO.setTransferNo(reqVO.getTransferNo());
        return respDTO;
    }

    @Override
    public TransferSettleRespDTO transferSettle(TransferSettleReqVO reqVO) {
        TransferSettleRespDTO respDTO = new TransferSettleRespDTO();
        respDTO.setStatus(TransferStatusEnum.SUCCESS.getStatus());
        respDTO.setWithdrawNo(reqVO.getWithdrawNo());
        respDTO.setOutTradeNo(RandomUtil.randomNumbers(18));
        respDTO.setAccountNo(RandomUtil.randomNumbers(18));
        respDTO.setAccountName(PayConstants.DEFAULT_BANK_NAME);
        respDTO.setSettleFee(BigDecimal.ZERO);
        return respDTO;
    }

    @Override
    public TransferSettleRespDTO parseSettleNotify(Map<String, String> params, String body) {
        // 模拟支付直接成功, 不需要处理回调
        return null;
    }

    @Override
    public TransferSubmitRespDTO parseSubmitNotify(Map<String, String> params, String body) {
        // 模拟支付直接成功, 不需要处理回调
        return null;
    }
}
