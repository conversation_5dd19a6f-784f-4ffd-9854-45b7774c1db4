package com.zksr.account.controller.withdraw.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 提现单审核实体
 * @date 2024/4/13 17:15
 */
@Data
@ApiModel(description = "提现单审核实体")
public class AccWithdrawAuditVO {

    @ApiModelProperty(value = "提现单ID", required = true)
    @NotNull(message = "提现单ID不能为NULL")
    private Long withdrawId;

    @ApiModelProperty(value = "商户类型", hidden = true)
    private String merchantType;

    @ApiModelProperty(value = "提现备注")
    private String remark;

    @ApiModelProperty(value = "凭证, 最多上传3张照片")
    private String voucher;
}
