package com.zksr.account.service.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.account.controller.divide.vo.AccOfflineDividePageReqVO;
import com.zksr.account.controller.divide.vo.AccOfflineDivideSaveReqVO;
import com.zksr.account.convert.divide.AccOfflineDivideConvert;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.domain.AccOfflineDivide;
import com.zksr.account.mapper.AccDivideDtlMapper;
import com.zksr.account.mapper.AccOfflineDivideMapper;
import com.zksr.account.service.IAccOfflineDivideService;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.config.CustomIdGenerator;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.ACCOUNT_OFFLINE_DIVIDE_BATCH_MERCHANT_REPEAT;
import static com.zksr.account.enums.ErrorCodeConstants.ACCOUNT_OFFLINE_DIVIDE_REPEAT;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 线下分账处理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Service
public class AccOfflineDivideServiceImpl implements IAccOfflineDivideService {

    @Autowired
    private AccOfflineDivideMapper accOfflineDivideMapper;

    @Autowired
    private AccDivideDtlMapper accDivideDtlMapper;

    @Autowired
    private CustomIdGenerator generator;

    /**
     * 新增线下分账处理
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_OFFLINE_DIVIDE, condition = "#createReqVO.merchantId")
    public Long insertAccOfflineDivide(AccOfflineDivideSaveReqVO createReqVO) {
        // 插入
        AccOfflineDivide accOfflineDivide = AccOfflineDivideConvert.INSTANCE.convert(createReqVO);
        // 离线处理单号
        String offlineProNo = "FZ" + DateUtils.dateTime() + generator.nextId();
        accOfflineDivide.setOfflineProNo(offlineProNo);
        // 目前默认只有入驻商会有离线分账需求
        accOfflineDivide.setMerchantType(MerchantTypeEnum.SUPPLIER.getType());

        // 总分账金额
        BigDecimal totalAltAmt = BigDecimal.ZERO;
        // 总订单金额
        BigDecimal totalOrderAmt = BigDecimal.ZERO;
        // 分账金额
        BigDecimal altAmt = BigDecimal.ZERO;

        List<AccDivideDtl> updateDtlList = new ArrayList<>();
        for (Long divideDtlId : createReqVO.getDivideDtlList()) {
            AccDivideDtl divideDtl = accDivideDtlMapper.selectById(divideDtlId);
            if (StringUtils.isNotEmpty(divideDtl.getOfflineProNo())) {
                throw exception(ACCOUNT_OFFLINE_DIVIDE_REPEAT, divideDtl.getTradeNo());
            }
            // 累积分账金额
            totalAltAmt = totalAltAmt.add(divideDtl.getAltAmt());
            totalOrderAmt = totalOrderAmt.add(divideDtl.getOrderAmt());

            AccDivideDtl update = new AccDivideDtl();
            update.setDivideDtlId(divideDtlId);
            update.setOfflineProNo(offlineProNo);
            update.setOfflineProState(NumberPool.INT_ONE);
            update.setDivideTime(DateUtil.date());
            update.setMerchantId(divideDtl.getMerchantId());
            updateDtlList.add(update);
        }
        long merchantCount = updateDtlList.stream().map(AccDivideDtl::getMerchantId).filter(Objects::nonNull).distinct().count();
        if (merchantCount > NumberPool.LONG_ONE) {
            throw exception(ACCOUNT_OFFLINE_DIVIDE_BATCH_MERCHANT_REPEAT);
        }
        // 更新明细状态
        accDivideDtlMapper.updateBatch(updateDtlList);

        // 保存处理单据
        accOfflineDivide.setTotalAltAmt(totalAltAmt);
        accOfflineDivide.setAltAmt(totalAltAmt);
        accOfflineDivide.setTotalOrderAmt(totalOrderAmt);
        accOfflineDivideMapper.insert(accOfflineDivide);

        // 返回
        return accOfflineDivide.getOfflineDivideId();
    }

    /**
     * 修改线下分账处理
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAccOfflineDivide(AccOfflineDivideSaveReqVO updateReqVO) {
        accOfflineDivideMapper.updateById(AccOfflineDivideConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 删除线下分账处理
     *
     * @param offlineDivideId ${pkColumn.columnComment}
     */
    @Override
    public void deleteAccOfflineDivide(Long offlineDivideId) {
        // 删除
        accOfflineDivideMapper.deleteById(offlineDivideId);
    }

    /**
     * 批量删除线下分账处理
     *
     * @param offlineDivideIds 需要删除的线下分账处理主键
     * @return 结果
     */
    @Override
    public void deleteAccOfflineDivideByOfflineDivideIds(Long[] offlineDivideIds) {
        for(Long offlineDivideId : offlineDivideIds){
            this.deleteAccOfflineDivide(offlineDivideId);
        }
    }

    /**
     * 获得线下分账处理
     *
     * @param offlineDivideId ${pkColumn.columnComment}
     * @return 线下分账处理
     */
    @Override
    public AccOfflineDivide getAccOfflineDivide(Long offlineDivideId) {
        return accOfflineDivideMapper.selectById(offlineDivideId);
    }

    @Override
    public AccOfflineDivide getAccOfflineDivide(String offlineProNo) {
        return accOfflineDivideMapper.selectByOfflineProNo(offlineProNo);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccOfflineDivide> getAccOfflineDividePage(AccOfflineDividePageReqVO pageReqVO) {
        return accOfflineDivideMapper.selectPage(pageReqVO);
    }

}
