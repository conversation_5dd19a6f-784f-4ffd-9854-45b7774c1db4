package com.zksr.account.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.zksr.account.controller.transfer.vo.AccTransferPageReqVO;
import com.zksr.account.controller.transfer.vo.AccTransferSaveReqVO;
import com.zksr.account.convert.transfer.AccTransferConvert;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccTransfer;
import com.zksr.account.mapper.AccTransferMapper;
import com.zksr.account.service.IAccAccountFlowService;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccTransferService;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.enums.AccountTransferStateEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.trade.api.order.OrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 账户转账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@Slf4j
@Service
public class AccTransferServiceImpl implements IAccTransferService {

    @Autowired
    private AccTransferMapper accTransferMapper;

    @Autowired
    private IAccAccountFlowService accountFlowService;

    @Autowired
    private IAccAccountService accountService;

    @Resource
    private OrderApi orderApi;

    /**
     * 新增账户转账单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_ACCOUNT_TRANSFER, condition = "#createReqVO.settleId")
    public AccTransfer insertAccTransfer(AccTransferSaveReqVO createReqVO) {
        // 验证当前的转账单是否已经创建了
        Long count = accTransferMapper.selectCountBySettleId(createReqVO.getSettleId());
        if (count > 0L) {
            // settleId 重复创建转账单调整
            throw exception(SETTLE_ALREADY_CREATE);
        }
        Long sysCode = SecurityContextHolder.hasSysCode();
        // 获取转出方账户ID
        AccAccount sourceAccount = null;
        if (!MerchantTypeEnum.BRANCH.getType().equals(createReqVO.getSourceMerchantType())) {
            // 非门店转出方
            sourceAccount = accountService.getAccountByMerchantIdAndType(createReqVO.getSourceMerchantId(), createReqVO.getSourceMerchantType(), createReqVO.getPlatform());
        }  else {
            // 是门店转出方, 门店转出方, 转入方就是门店的财务持有着
            sourceAccount = accountService.getAccount(
                    sysCode,
                    createReqVO.getSourceMerchantId(),
                    createReqVO.getSourceMerchantType(),
                    createReqVO.getTargetMerchantId(),
                    createReqVO.getPlatform()
            );
        }
        if (Objects.isNull(sourceAccount)) {
            // 账户不存在
            log.warn("转账方账户不存在, transfer={}", createReqVO);
            throw exception(SOURCE_ACCOUNT_NONE_EXISTS);
        }
        // 写入转出方账户ID
        createReqVO.setSourceAccountId(sourceAccount.getAccountId());
        if (Objects.isNull(SecurityContextHolder.hasSysCode())) {
            createReqVO.setSysCode(sourceAccount.getSysCode());
        }
        // 插入
        AccTransfer accTransfer = AccTransferConvert.INSTANCE.convert(createReqVO);
        accTransferMapper.insert(accTransfer);
        // 返回
        return accTransfer;
    }

    @Override
    @Transactional
    public List<AccTransfer> insertBatchAccTransfer(List<AccTransferSaveReqVO> createReqVO) {
        ArrayList<AccTransfer> resultList = new ArrayList<>();
        for (AccTransferSaveReqVO saveReqVO : createReqVO) {
            resultList.add(shelf().insertAccTransfer(saveReqVO));
        }
        return resultList;
    }

    /**
     * 修改账户转账单
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    @Override
    public void updateAccTransfer(AccTransferSaveReqVO updateReqVO) {
        accTransferMapper.updateById(AccTransferConvert.INSTANCE.convert(updateReqVO));
    }

    /**
     * 获得账户转账单
     *
     * @param transferId 账户转账单id
     * @return 账户转账单
     */
    @Override
    public AccTransfer getAccTransfer(Long transferId) {
        return accTransferMapper.selectById(transferId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccTransfer> getAccTransferPage(AccTransferPageReqVO pageReqVO) {
        return accTransferMapper.selectPage(pageReqVO);
    }

    /**
     * 获取转账单重试列表数据
     * @param minId 最小ID
     * @return  每批次返回1000条重试数据
     */
    @Override
    public List<AccTransfer> getRetryAccTransfer(Long minId) {
        return accTransferMapper.selectRetryAccTransfer(minId);
    }

    /**
     * 处理账户转账单
     * @param transferMsg  转账单
     */
    @Override
    @Transactional
    @DistributedLock(prefix = RedisLockConstants.LOCK_ACCOUNT_TRANSFER, condition = "#transferMsg.settleId")
    public void processTransfer(AccTransfer transferMsg) {
        AccTransfer transfer = accTransferMapper.selectById(transferMsg.getTransferId());
        if (AccountTransferStateEnum.FINISH.getState().equals(transfer.getState())) {
            log.warn("转账单, 转账已经处理成功, transferNo={}", transfer.getTransferNo());
            return;
        }
        // 检查转出方余额是否足够
        AccAccount sourceAccount = accountService.getAccAccount(transfer.getSourceAccountId());
        // 账户余额不能小于0
        // 余额 - 冻结 + 授信
        if (NumberUtil.isGreater(BigDecimal.ZERO, sourceAccount.getValidAccountAmt().add(sourceAccount.getCreditAmt()).subtract(transfer.getTransferAmt()))
        ) {
            log.warn("转账单, 转出方账户余额不足, transferNo={}", transfer.getTransferNo());
            transfer.setState(AccountTransferStateEnum.PROCESSING.getState());
            transfer.setProcessingTime(DateUtil.date());
            accTransferMapper.updateById(transfer);
            return;
        }
        // 解除冻结金额
        if (Objects.isNull(transfer.getSettleAmt())) {
            // 如果没有解除解除冻结
            transfer.setSettleAmt(BigDecimal.ZERO);
        }
        // 扣减转出方余额-流水
        // 扣减转出方冻结金额
        AccAccountFlow sourceAccountFlow = new AccAccountFlow();
        sourceAccountFlow.setSysCode(transfer.getSysCode())
                .setAccountId(transfer.getSourceAccountId())
                .setMerchantId(transfer.getSourceMerchantId())
                .setMerchantType(transfer.getSourceMerchantType())
                .setBusiWithdrawableAmt(BigDecimal.ZERO.subtract(transfer.getTransferAmt()))
                .setBusiFrozenAmt(BigDecimal.ZERO.subtract(transfer.getSettleAmt()))
                .setBusiType(AccountBusiType.ACCOUNT_TRANSFER.getType())
                .setBusiId(transfer.getTransferId())
                .setBusiFields(
                        StringUtils.join(
                                ListUtil.toList(AccountBusiTypeField.WITHDRAWABLE_AMT.getField(), AccountBusiTypeField.FROZEN_AMT.getField()),
                                StringPool.COMMA
                        )
                )
                .setPlatform(transfer.getPlatform());
        // 增加转入方余额
        AccAccountFlow targetAccountFlow = new AccAccountFlow();
        targetAccountFlow.setSysCode(transfer.getSysCode())
                .setAccountId(transfer.getTargetAccountId())
                .setMerchantId(transfer.getTargetMerchantId())
                .setMerchantType(transfer.getTargetMerchantType())
                .setBusiWithdrawableAmt(transfer.getTransferAmt())
                .setBusiType(AccountBusiType.ACCOUNT_TRANSFER.getType())
                .setBusiId(transfer.getTransferId())
                .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField())
                .setPlatform(transfer.getPlatform());

        log.info("账户内部转账单流水保存, transferNo={}", transfer.getTransferNo());
        // 保存流水
        accountFlowService.insertAccAccountFlow(sourceAccountFlow);
        accountFlowService.insertAccAccountFlow(targetAccountFlow);
        // 执行账户流水
        accountFlowService.processFlow(sourceAccountFlow);
        accountFlowService.processFlow(targetAccountFlow);
        log.info("账户内部转账单流水执行成功, transferNo={}", transfer.getTransferNo());

        // 如果没报错那就是转账成功了
        // 更新转账单处理状态
        transfer.setState(AccountTransferStateEnum.FINISH.getState());
        transfer.setProcessingTime(DateUtil.date());
        transfer.setFinishTime(DateUtil.date());

        // 查询账户流水, 确定转账接受方账户ID
        AccAccountFlow accAccountFlow = accountFlowService.getAccAccountFlow(targetAccountFlow.getAccountFlowId());
        transfer.setTargetAccountId(accAccountFlow.getAccountId());
        accTransferMapper.updateById(transfer);

        // 标记结算处理状态
        Boolean success = orderApi.updateOrderSettleState(ListUtil.toList(transfer.getSettleId())).getCheckedData();
        if (!success) {
            throw exception(TRANSFER_UPDATE_SETTLE);
        }
    }

    IAccTransferService shelf() {
        return SpringUtils.getAopProxy(this);
    }
}
