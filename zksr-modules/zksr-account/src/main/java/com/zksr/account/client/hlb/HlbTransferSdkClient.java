package com.zksr.account.client.hlb;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zksr.account.client.hlb.vo.AccountPaySubReqVO;
import com.zksr.account.client.hlb.vo.MerchantSettlementVO;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.common.core.enums.TransferStatusEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.cert.HlbCertUtil;
import com.zksr.account.client.http.HttpClientService;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝支付
 * @date 2024/3/7 18:13
 */
@Data
@Slf4j
public class HlbTransferSdkClient {

    @ApiModelProperty("商户公钥")
    private X509Certificate pubCert;

    @ApiModelProperty("商户私钥")
    private String merchantPrivateKey;

    @ApiModelProperty("商户号")
    private String merchantNo;

    @ApiModelProperty(value = "服务器地址")
    private String notifyUrl;

    /**
     * 转账
     * @param reqVO
     * @return
     */
    public TransferSubmitRespDTO createTransfer(TransferSubmitReqVO reqVO) {
        AccountPaySubReqVO subReqVo = new AccountPaySubReqVO();
        subReqVo.setP1_bizType("AccountPaySub");
        subReqVo.setP2_signType("SM3WITHSM2");
        subReqVo.setP3_timestamp(new SimpleDateFormat("yyyy-MM-dd_HH:mm:ss.SSS").format(new Date()));
        subReqVo.setP4_orderId(reqVO.getTransferNo());
        subReqVo.setP5_customerNumber(reqVO.getSourceAltMchNo());


        AccountPaySubReqVO.AccountPaySubReqVoExt voExt = new AccountPaySubReqVO.AccountPaySubReqVoExt();
        voExt.setInMerchantNo(reqVO.getTargetAltMchNo());
        voExt.setOrderType("TRANSFER");
        voExt.setAmount(reqVO.getTransferAmt().doubleValue());
        voExt.setServerCallbackUrl(notifyUrl+ StringUtils.format("/account/transfer/notify/submit/{}/{}/{}", reqVO.getSysCode(), reqVO.getPlatform(), reqVO.getSourceMerchantType()));
        voExt.setGoodsName("活动资金");
        voExt.setOrderDesc(reqVO.getTransferRemark());
        // 担保信息
        //voExt.setInEscrow("FALSE");
        /**
         * 手续费承担方
         * PAYER 表示付款方承担
         *
         * RECEIVER 收款方承担
         */
        voExt.setBelongsType("RECEIVER");
        // 分账信息
        /*ArrayList<Map> maps = new ArrayList<>();
        Map split = new HashMap();
        split.put("splitBillMerchantNo", reqVO.getTargetAltMchNo());
        split.put("splitBillAmount", reqVO.getTransferAmt());
        split.put("splitBillFee", reqVO.getFree());
        maps.add(split);
        voExt.setSplitBillRules(JSON.toJSONString(maps));*/
        subReqVo.setP6_ext(JSON.toJSONString(voExt));

        TransferSubmitRespDTO transferSubmitRespDTO = new TransferSubmitRespDTO();
        try {
            String resultMsg = reuqest("http://transfer.trx.helipay.com/trx/accountPay/interface.action", subReqVo, AccountPaySubReqVO.NEED_SIGN_PARAMS, new HashSet<>());
            log.info("合利宝转账结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("rt5_retCode"))) {
                transferSubmitRespDTO.setStatus(TransferStatusEnum.CREATED.getStatus())
                        .setTransferNo(reqVO.getTransferNo())
                        .setOutTradeNo(resultObj.getString("rt9_serialNumber"));
            } else {
                transferSubmitRespDTO.setStatus(TransferStatusEnum.FAIL.getStatus())
                        .setMsg(resultObj.getString("rt6_retMsg"));
            }
        } catch (IllegalAccessException e) {
            log.error(" createTransfer异常,", e);
            throw new RuntimeException(e);
        }
        return transferSubmitRespDTO;
    }

    /**
     * 提现
     * @param reqVO
     * @return
     */
    public TransferSettleRespDTO transferSettle(TransferSettleReqVO reqVO) {
        MerchantSettlementVO settlementVO = new MerchantSettlementVO();
        settlementVO.setP2_orderId(reqVO.getWithdrawNo());
        settlementVO.setP3_customerNumber(reqVO.getCustomerNo());
        settlementVO.setP4_amount(reqVO.getAmount().doubleValue());
        settlementVO.setP5_summary(reqVO.getTips());
        settlementVO.setP6_notifyUrl(notifyUrl + StringUtils.format("/account/transfer/notify/settle/{}/{}/{}", reqVO.getSysCode(), reqVO.getPlatform(), reqVO.getMerchantType()));
        TransferSettleRespDTO transferSubmitRespDTO = new TransferSettleRespDTO();
        try {
            String resultMsg = reuqest("http://transfer.trx.helipay.com/trx/transfer/interface.action", settlementVO, MerchantSettlementVO.NEED_SIGN_PARAMS, new HashSet<>());
            log.info("合利宝结算结果 {}", resultMsg);
            JSONObject resultObj = JSONObject.parseObject(resultMsg);
            if ("0000".equals(resultObj.getString("rt2_retCode"))) {
                transferSubmitRespDTO.setStatus(TransferStatusEnum.CREATED.getStatus())
                        .setWithdrawNo(reqVO.getWithdrawNo());
            } else {
                transferSubmitRespDTO.setStatus(TransferStatusEnum.FAIL.getStatus())
                        .setWithdrawNo(reqVO.getWithdrawNo())
                        .setMsg(resultObj.getString("rt3_retMsg"));
            }
        } catch (IllegalAccessException e) {
            log.error(" transferSettle异常,", e);
            throw new RuntimeException(e);
        }
        return transferSubmitRespDTO;
    }

    private  String reuqest(
            String requestUrl,
            Object orderVo,
            Set<String> needSignParams,
            Set<String> needEncryptParams) throws IllegalAccessException {
        String resultMsg;
        Map<String, String> map = HlbCertUtil.convertBean(orderVo, new LinkedHashMap<>());
        //建议是加签/验签的固定参数,具体按照文档要求加签.因为新增参数需要历史兼容是排除签名的
        HlbCertUtil.getSignAndEncryptedByReq(map, needSignParams, needEncryptParams, pubCert, merchantPrivateKey);
        Map<String, Object> resultMap = HttpClientService.getHttpResp(map, requestUrl);
        resultMsg = (String) resultMap.get("response");
        return resultMsg;
    }

}
