package com.zksr.account.service;

import com.zksr.account.api.platformMerchant.vo.WxB2bColonelOpenidBindReqVO;
import com.zksr.account.domain.AccPlatformMerchantWxb2b;

/**
 * 支付平台商户, 微信B2B平台商户扩展信息Service接口
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
public interface IAccPlatformMerchantWxb2bService {

    /**
     * 更新业务员微信支付商户信息, 业务员无需进件, 直接使用openid 作为分账商户号
     * @param reqVO
     */
    void updateWxB2bColonelOpenidBind(WxB2bColonelOpenidBindReqVO reqVO);

    AccPlatformMerchantWxb2b getPlatformMerchant(Long merchantId, String merchantType);
}
