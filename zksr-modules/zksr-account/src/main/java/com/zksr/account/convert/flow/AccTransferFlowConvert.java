package com.zksr.account.convert.flow;

import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccTransferFlow;
import com.zksr.account.controller.flow.vo.AccTransferFlowRespVO;
import com.zksr.account.controller.flow.vo.AccTransferFlowSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 支付平台转账流水 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-04-13
*/
@Mapper
public interface AccTransferFlowConvert {

    AccTransferFlowConvert INSTANCE = Mappers.getMapper(AccTransferFlowConvert.class);

    AccTransferFlowRespVO convert(AccTransferFlow accTransferFlow);

    AccTransferFlow convert(AccTransferFlowSaveReqVO accTransferFlowSaveReq);

    PageResult<AccTransferFlowRespVO> convertPage(PageResult<AccTransferFlow> accTransferFlowPage);

    AccTransferFlow convert(TransferSubmitReqVO reqVO);
}