package com.zksr.account.service;

import com.zksr.account.controller.recharge.vo.AccRechargeImportDtlRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeImportDtlSaveReqVO;
import com.zksr.account.domain.AccRechargeImport;
import com.zksr.account.domain.AccRechargeImportDtl;

import java.util.List;

/**
 * PC后台导入充值详情
Service接口
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface IAccRechargeImportDtlService {

    void saveDtl(AccRechargeImport accRechargeImport, List<AccRechargeImportDtlSaveReqVO> rechargeImportDtls);

    List<AccRechargeImportDtl> getDtls(Long rechargeImportId);

    List<AccRechargeImportDtlRespVO> getDtlsRespVO(Long rechargeImportId);

    void updateById(AccRechargeImportDtl importDtl);
}
