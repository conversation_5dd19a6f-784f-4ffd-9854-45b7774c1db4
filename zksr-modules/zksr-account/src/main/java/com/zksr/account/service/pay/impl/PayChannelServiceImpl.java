package com.zksr.account.service.pay.impl;


import com.alibaba.fastjson.JSON;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.zksr.account.client.PayClient;
import com.zksr.account.client.hlb.HlbAppletPayClient;
import com.zksr.account.client.mideapay.MideaPayAppletPayClient;
import com.zksr.account.client.mock.MockAppletPayClient;
import com.zksr.account.client.wallet.WalletAppletPayClient;
import com.zksr.account.client.wx.WxB2bPayClient;
import com.zksr.account.convert.pay.PayConvert;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.UnifiedOrderValidateDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.pay.PayChannelService;
import com.zksr.account.service.pay.valid.PayOrderValidService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayWayEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.model.dto.HlbPayConfig;
import com.zksr.system.api.model.dto.MideaPayConfig;
import com.zksr.system.api.model.dto.PayConfigDTO;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.HeLiBaoPayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.WxB2bPayConfigDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import com.zksr.system.api.partnerConfig.dto.MideaPayConfigDTO;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.CacheUtils.buildAsyncReloadingCache;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/7 10:36
 */
@Slf4j
@Service
public class PayChannelServiceImpl implements PayChannelService {

    /**
     * {@link PayClient} 缓存，30秒自动异步刷新
     */
    @Getter
    private final LoadingCache<PayClient.ClientParam, PayClient> clientCache = buildAsyncReloadingCache(Duration.ofSeconds(30L),
            new CacheLoader<PayClient.ClientParam, PayClient>() {
                @Override
                public PayClient load(PayClient.ClientParam param) {
                    return initClient(param.getAppid(), param.getSysCode(), param.getOrderType(), param.getPlatform());
                }
            }
    );

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private MockAppletPayClient mockAppletPayClient;

    @Autowired
    private WalletAppletPayClient walletAppletPayClient;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private List<PayOrderValidService> payOrderValidService;

    /**
     * 获取支付配置
     * @param appid 根据appid 获取支付客户端
     * @param sysCode 平台ID
     * @param payWay 支付方式
     * @param orderType 订单方式 {@link com.zksr.common.core.constant.OrderTypeConstants}
     * @return
     */
    @Override
    public PayClient getPayClient(String appid, Long sysCode, String payWay, Integer orderType) {
        com.zksr.system.api.partnerConfig.dto.PayConfigDTO configDTO = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(configDTO)) {
            throw exception(NOT_EXIST_PAY_CONFIG);
        }
        // 门店充值使用门店充值支付平台
        if (orderType.equals(OrderTypeConstants.BRANCH_CHARGE)) {
            return getPayClient(appid, sysCode, payWay, orderType, configDTO.getWalletPayPlatform());
        }
        return getPayClient(appid, sysCode, payWay, orderType, configDTO.getStoreOrderPayPlatform());
    }

    @Override
    public PayClient getPayClient(String appid, Long sysCode, String payWay, Integer orderType, String platform) {
        // 退款会有指定平台
        if (PayWayEnum.WALLET.getPayWay().equals(payWay)) {
            // 储值支付
            return walletAppletPayClient;
        }
        // 根据订单类型判断 支付体系, 判断是否使用模拟支付
        if (isMockPay(sysCode, orderType)) {
            // 模拟支付
            return mockAppletPayClient;
        }
        return clientCache.getUnchecked(
                PayClient.ClientParam.builder()
                        .appid(appid)
                        .sysCode(sysCode)
                        .payWay(payWay)
                        .orderType(orderType)
                        .platform(platform)
                        .build()
        );
    }

    @Override
    public void clear() {
        clientCache.invalidateAll();
    }

    @Override
    public UnifiedOrderValidateDTO processPayBeforeValidate(PayClient payClient, PayOrderDTO payOrderDTO) {
        PayOrderValidService validService = getValidate(PayChannelEnum.parse(payClient.getPlatform()));
        if (Objects.isNull(validService)) {
            return UnifiedOrderValidateDTO.success();
        }
        return validService.processPayBeforeCommonValidate(payOrderDTO);
    }

    @Override
    public UnifiedOrderValidateDTO processRefundBeforeValidate(PayClient payClient, PayRefundOrderSubmitReqVO reqDTO) {
        PayOrderValidService validService = getValidate(PayChannelEnum.parse(payClient.getPlatform()));
        if (Objects.isNull(validService)) {
            return UnifiedOrderValidateDTO.success();
        }
        return validService.processRefundBeforeCommonValidate(reqDTO);
    }

    /**
     * 匹配验证器
     * @param payChannelEnum
     * @return
     */
    public PayOrderValidService getValidate(PayChannelEnum payChannelEnum) {
        if (Objects.isNull(payChannelEnum)) {
            return null;
        }
        for (PayOrderValidService validService : payOrderValidService) {
            if (validService.getPayChannel().equals(payChannelEnum)) {
                return validService;
            }
        }
        return null;
    }

    private boolean isMockPay(Long sysCode, Integer orderType) {
        com.zksr.system.api.partnerConfig.dto.PayConfigDTO payConfig = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfig)) {
            return true;
        } else {
            // 根据订单类型判断, 是储值体系, 还是支付体系
            // 根据 储值 | 支付  获取支付方式
            if (OrderTypeConstants.MALL.equals(orderType)) {
                // 商城
                if (payConfig.getStoreOrderPayPlatform().equals(PayChannelEnum.MOCK.getCode())) {
                    return true;
                }
            } else {
                if (payConfig.getInteriorStoredPayPlatform().equals(PayChannelEnum.MOCK.getCode())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 初始化支付
     * @param appid        appid
     * @param sysCode      平台ID
     * @param orderType    0-商场订单,1-入驻商充值,2-门店充值,3-货到付款
     * @return
     */
    private PayClient initClient(String appid, Long sysCode, Integer orderType, String platform) {
        // 根据支付方式, 获取支付client
        // 初始化 client
        try {
            if (PayChannelEnum.MIDEA_PAY.getCode().equals(platform)) {//美的付
                //初始化美的付支付客户端
                return initMideaPayAppletPayClient(appid, sysCode, orderType);
            } else if (PayChannelEnum.HLB.getCode().equals(platform)) {//合利宝
                return initHelibaoPayClient(appid, sysCode, orderType);
            } else if (PayChannelEnum.WX_B2B_PAY.getCode().equals(platform)) {//合利宝
                return initWxB2BPayClient(appid, sysCode);
            } else{
                //支付平台不存在
                throw exception(PAY_PLATFORM_NOT_EXIST, platform);
            }
        } catch (Exception e) {
            log.error("异常", e);
            throw new RuntimeException(e);
        }
    }

    private WxB2bPayClient initWxB2BPayClient(String appid, Long sysCode) {
        WxB2bPayClient client = new WxB2bPayClient();
        // 微信b2b支付配置
        WxB2bPayConfigDTO wxB2bPayConfigDTO = accountCacheService.getWxB2BPayConfigDTO(sysCode);
        //client.setMchid(wxB2bPayConfigDTO.getMchid());
        //client.setAppKey(wxB2bPayConfigDTO.getAppKey());
        client.setAppId(appid);
        return client;
    }

    @NotNull
    private HlbAppletPayClient initHelibaoPayClient(String appid, Long sysCode, Integer orderType) throws Exception {
        //适配第三方支付平台
        PayConfigDTO configData = new PayConfigDTO();
        // 根据平台获取配置
        {
            HeLiBaoPayConfigDTO rpcPayConfig = accountCacheService.getHeLiBaoConfig(sysCode);
            HlbPayConfig payConfig = new HlbPayConfig();
            payConfig.setMerchantNo(rpcPayConfig.getHeLiBaoMerchantPlatformAccount());
            payConfig.setNotifyUrl(rpcPayConfig.getPayCallBackUrl());
            payConfig.setPubCert(rpcPayConfig.getPublicKeyCertificate());
            payConfig.setMerchantPrivateKey(rpcPayConfig.getPrivateKeyCertificate());
            payConfig.setPass(rpcPayConfig.getPrivateKeyPassword());
            if (orderType == 1) {
                // 使用入驻商支付配置
                payConfig = redisSysConfigService.getSupplierConfig();
            }
            configData.setConfig(JSON.toJSONString(payConfig));
        }
        HlbAppletPayClient client = new HlbAppletPayClient();
        client.setConfig(configData, appid);
        return client;
    }

    /**
     * 初始化美的付支付客户端
     * @param appid
     * @param sysCode
     * @param orderType
     * @return
     * @throws Exception
     */
    private MideaPayAppletPayClient initMideaPayAppletPayClient(String appid, Long sysCode, Integer orderType) throws Exception {
        //适配第三方支付平台
        PayConfigDTO configData = new PayConfigDTO();
        MideaPayConfig payConfig = null;
        if (OrderTypeConstants.SUPPLIER_CHARGE.equals(orderType)) {
            // 使用入驻商支付配置，系统参数，需要按美的付的参数配置
            payConfig = redisSysConfigService.querySupplierConfig(MideaPayConfig.class);
        }else {
            // 根据平台获取配置
            MideaPayConfigDTO configDTO = accountCacheService.getMideaPayConfig(sysCode);
            payConfig = PayConvert.INSTANCE.convert2MideaPayConfig(configDTO);
        }
        configData.setConfig(JSON.toJSONString(payConfig));
        MideaPayAppletPayClient client = new MideaPayAppletPayClient();
        client.setConfig(configData, appid);
        return client;
    }
}
