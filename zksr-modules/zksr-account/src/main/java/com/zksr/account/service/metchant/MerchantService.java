package com.zksr.account.service.metchant;

import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayTypeEnum;

/**
 * <AUTHOR>
 * @time 2024/4/23
 * @desc
 */
public interface MerchantService {

    /**
     * 获取支付平台商户余额
     * @param merchantNo      进件商户编号
     * @param sysCode         平台ID
     * @param payTypeEnum     支付平台类型
     * @return
     */
    PayPlatformAccountVO getBalance(String merchantNo, Long sysCode, PayTypeEnum payTypeEnum);


    /**
     * 获取支付平台商户余额
     * @param merchantNo      进件商户编号
     * @param sysCode         平台ID
     * @param platform         支付平台
     * @return
     */
    PayPlatformAccountVO getBalance(String merchantNo, Long sysCode, PayChannelEnum platform);

    /**
     * 获取入驻商结算账户余额
     * @param supplierId    入驻商ID
     * @return 账户余额
     */
    PayPlatformAccountVO getSupplierBalance(Long supplierId);


    /**
     * 获取平衡进件信息
     * @param sysCode       平台ID
     * @param platform      支付平台
     * @param orderNo       第三方单号
     * @param merchantNo    商户号
     * @return
     */
    PayPlatformMerchantVO getMerchantInfo(Long sysCode, String platform, String orderNo, String merchantNo);
}
