package com.zksr.account.mapper;

import com.zksr.account.controller.recharge.vo.AccRechargeSchemeRespVO;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccRechargeScheme;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemePageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 储值充值套餐配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Mapper
public interface AccRechargeSchemeMapper extends BaseMapperX<AccRechargeScheme> {

    /**
     * 查询指定area_id在时间上有冲突并且启用的数据条目数
     * @param areaId 区域ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合条件的数据条目数
     */
    List<AccRechargeScheme> selectOverlappingEnabledEntries(@Param("areaId") Long areaId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<AccRechargeScheme> selectPage(AccRechargeSchemePageReqVO pageReqVO);

    List<AccRechargeScheme> selectValidAreaRechargeSchemeList(@Param("areaId") Long areaId);
}
