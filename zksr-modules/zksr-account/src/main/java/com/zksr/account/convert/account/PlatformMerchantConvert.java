package com.zksr.account.convert.account;

import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRespVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantSaveReqVO;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/12
 * @desc
 */
@Mapper
public interface PlatformMerchantConvert {

    PlatformMerchantConvert INSTANCE = Mappers.getMapper(PlatformMerchantConvert.class);

    AccPlatformMerchant convert(AccPlatformMerchantSaveReqVO createReqVO);

    AccPlatformMerchantRespVO convertRespVO(AccPlatformMerchant platformMerchant);

    SysDcPageReqVO convertDcPageReqVO(AccPlatformMerchantPageReqVO pageReqVO);

    List<AccPlatformMerchantRespVO> convertRespVO(List<AccPlatformMerchant> list);
}
