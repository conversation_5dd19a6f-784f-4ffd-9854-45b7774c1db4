package com.zksr.account.controller.divide.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 线下分账处理对象 acc_offline_divide
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@ApiModel("线下分账处理 - acc_offline_divide分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccOfflineDividePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "凭证图片")
    private Long offlineDivideId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 线下处理单号 */
    @Excel(name = "线下处理单号")
    @ApiModelProperty(value = "线下处理单号")
    private String offlineProNo;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id", required = true)
    private Long merchantId;

    /** 分账金额 */
    @Excel(name = "分账金额")
    @ApiModelProperty(value = "分账金额")
    private Long altAmt;

    /** 线下转账单号 */
    @Excel(name = "线下转账单号")
    @ApiModelProperty(value = "线下转账单号")
    private String offlineNo;

    /** 总分账金额 */
    @Excel(name = "总分账金额")
    @ApiModelProperty(value = "总分账金额")
    private Long totalAltAmt;

    /** 总订单金额 */
    @Excel(name = "总订单金额")
    @ApiModelProperty(value = "总订单金额")
    private Long totalOrderAmt;

    /** 凭证图片 */
    @Excel(name = "凭证图片")
    @ApiModelProperty(value = "凭证图片")
    private String voucherPic;


}
