package com.zksr.account.controller.transfer.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 交易对账单明细单对象 acc_transfer_bill_order
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@ApiModel("交易对账单明细单 - acc_transfer_bill_order Response VO")
public class AccTransferBillOrderRespVO {
    private static final long serialVersionUID = 1L;

    /** 交易账单id */
    @Excel(name = "交易账单id")
    @ApiModelProperty(value = "交易账单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long transferBillId;

    /** 交易时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "交易时间")
    @ApiModelProperty(value = "交易时间")
    private Date transferTime;

    /** 商户号 */
    @Excel(name = "商户号")
    @ApiModelProperty(value = "商户号")
    private String altNo;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 平台交易单号(外部) */
    @Excel(name = "平台交易单号")
    @ApiModelProperty(value = "平台交易单号")
    private String platformTradeNo;

    /** 商户交易单号*/
    @Excel(name = "商户交易单号")
    @ApiModelProperty(value = "商户交易单号")
    private String merchantTradeNo;

    /** 平台支付金额 */
    @Excel(name = "平台支付金额")
    @ApiModelProperty(value = "平台支付金额")
    private BigDecimal platformPayAmt;

    /** 商户支付金额 */
    @Excel(name = "商户支付金额")
    @ApiModelProperty(value = "商户支付金额")
    private BigDecimal merchantPayAmt;

    /** 平台退款金额 */
    @Excel(name = "平台退款金额")
    @ApiModelProperty(value = "平台退款金额")
    private BigDecimal platformRefundAmt;

    /** 商户退款金额 */
    @Excel(name = "商户退款金额")
    @ApiModelProperty(value = "商户退款金额")
    private BigDecimal merchantRefundAmt;

    /** 平台支付手续费 */
    @Excel(name = "平台支付手续费")
    @ApiModelProperty(value = "平台支付手续费")
    private BigDecimal platformPayFree;

    /** 商户支付手续费 */
    @Excel(name = "商户支付手续费")
    @ApiModelProperty(value = "商户支付手续费")
    private BigDecimal merchantPayFree;

    /** 平台退款手续费 */
    @Excel(name = "平台退款手续费")
    @ApiModelProperty(value = "平台退款手续费")
    private BigDecimal platformRefundFree;

    /** 商户退款手续费 */
    @Excel(name = "商户退款手续费")
    @ApiModelProperty(value = "商户退款手续费")
    private BigDecimal merchantRefundFree;

    /** 业务单号(订单号, 退款单号...) */
    @Excel(name = "业务单号(订单号, 退款单号...)")
    @ApiModelProperty(value = "业务单号(订单号, 退款单号...)")
    private String busiTradeNo;

    /** 订单类型 */
    @Excel(name = "订单类型")
    @ApiModelProperty(value = "订单类型 0-销售单 1-退款单")
    private Integer orderType;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态 0-正常 1-异常")
    private Integer state;

    @Excel(name = "备注信息")
    @ApiModelProperty(value = "备注信息")
    private String remark;

    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;
}
