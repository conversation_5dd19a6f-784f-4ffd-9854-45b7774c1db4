package com.zksr.account.service.profitOrder.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.zksr.account.model.profitOrder.dto.DivideOrderRepDto;
import com.zksr.account.service.DivideService;
import com.zksr.account.service.profitOrder.DivideChannelService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class ProfitOrderServiceImpl implements DivideService {
    
    @Autowired
    private DivideChannelService profitOrderChannelService;

    @Override
    public void notify(DivideOrderRepDto notify) {
        log.info("处理多级分账回调 notify={}", JSON.toJSONString(notify));
        // TODO: 业务处理，如更新分账状态、记录日志等
    }
} 