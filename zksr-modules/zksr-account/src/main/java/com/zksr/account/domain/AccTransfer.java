package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

import javax.validation.constraints.NotNull;

/**
 * 账户转账单对象 acc_transfer
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@TableName(value = "acc_transfer")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccTransfer extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 账户转账单id */
    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long transferId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 订单结算id */
    @Excel(name = "订单结算id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long settleId;

    /** 转出方账户id */
    @Excel(name = "转出方账户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sourceAccountId;

    /** 转入方账户id */
    @Excel(name = "转入方账户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long targetAccountId;

    /** 转出方账户id */
    @ApiModelProperty(value = "转出方账户id", required = true, notes = "注意这里是账户ID, 例如supplierId")
    @NotNull(message = "转出方账户id不能未空")
    private Long sourceMerchantId;

    /**
     * 转出方账户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty(value = "转出方账户类型", required = true)
    private String sourceMerchantType;

    /** 转入方账户id */
    @ApiModelProperty(value = "转入方账户id", required = true, notes = "转入方账户IDM, 例如 dcId")
    @NotNull(message = "转入方账户id不能未空")
    private Long targetMerchantId;

    /**
     * 转出方账户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty(value = "转入方账户类型", required = true)
    private String targetMerchantType;

    /** 转账单号 */
    @Excel(name = "转账单号")
    private String transferNo;

    /** 转账金额 */
    @Excel(name = "转账金额")
    private BigDecimal transferAmt;

    /** 转账发起方解除金额 */
    @Excel(name = "转账发起方解除金额")
    private BigDecimal settleAmt;

    /** 转账状态;0-已提交 1-处理中 2-已完成 3-转账失败 */
    @Excel(name = "转账状态;0-已提交 1-处理中 2-已完成 3-转账失败")
    private Integer state;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date processingTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishTime;

    /** 支付平台(数据字典);从订单 */
    @Excel(name = "支付平台(数据字典);从订单")
    private String platform;

}
