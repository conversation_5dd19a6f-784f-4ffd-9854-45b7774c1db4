package com.zksr.account.controller.notify;

import com.zksr.account.client.TransferClient;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.service.transfer.TransferAccountService;
import com.zksr.account.service.transfer.TransferChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 转账|提现|结算回调
 * @date 2024/3/11 9:09
 */
@Api(tags = "交易服务 - 转账|提现|结算回调", produces = "application/json")
@RestController
@RequestMapping("/transfer/notify")
@Validated
@Slf4j
public class TransferNotifyController {

    @Autowired
    private TransferAccountService transferAccountService;

    @Autowired
    private TransferChannelService transferChannelService;

    @ApiOperation(value = "支付渠道的统一【转账】回调")
    @RequestMapping(value = "/submit/{sysCode}/{platform}/{merchantType}")
    public String notifySubmitTransfer(
            @ApiParam(name = "sysCode", value = "平台商ID") @PathVariable("sysCode") Long sysCode,
            @ApiParam(name = "platform", value = "支付平台") @PathVariable("platform") String platform,
            @ApiParam(name = "merchantType", value = "商户类型") @PathVariable("merchantType") String merchantType,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body
    ) {
        log.info("[notifySubmitTransfer][sysCode({}),platform({}) 回调数据({}/{})]", sysCode, platform, params, body);
        // 1. 校验支付渠道是否存在
        // PayWayEnum.OFFLINE , 在线支付才有回调, 模拟支付直接回调
        TransferClient transferClient = transferChannelService.getTransferClient(sysCode, platform, merchantType);
        if (Objects.isNull(transferClient)) {
            log.info("[notifySubmitTransfer][sysCode({}),platform({}) 找不到对应的支付客户端]", sysCode, platform);
            return "success";
        }
        TransferSubmitRespDTO notify = transferClient.parseSubmitNotify(params, body);
        // 2. 解析通知数据
        transferAccountService.notify(notify);
        return "success";
    }

    @ApiOperation(value = "支付渠道的统一【结算|提现】回调")
    @RequestMapping(value = "/settle/{sysCode}/{platform}/{merchantType}")
    public String notifySettleTransfer(
            @ApiParam(name = "sysCode", value = "平台商ID") @PathVariable("sysCode") Long sysCode,
            @ApiParam(name = "platform", value = "支付平台") @PathVariable("platform") String platform,
            @ApiParam(name = "merchantType", value = "商户类型") @PathVariable("merchantType") String merchantType,
            @RequestParam(required = false) Map<String, String> params,
            @RequestBody(required = false) String body
    ) {
        log.info("[notifySettleTransfer][sysCode({}),platform({}) 回调数据({}/{})]", sysCode, platform, params, body);
        // 1. 校验支付渠道是否存在
        // PayWayEnum.OFFLINE , 在线支付才有回调, 模拟支付直接回调
        TransferClient transferClient = transferChannelService.getTransferClient(sysCode, platform, merchantType);
        if (Objects.isNull(transferClient)) {
            log.info("[notifySettleTransfer][sysCode({}),platform({}) 找不到对应的支付客户端]", sysCode, platform);
            return "success";
        }
        TransferSettleRespDTO notify = transferClient.parseSettleNotify(params, body);
        // 2. 解析通知数据
        transferAccountService.notify(notify);
        return "success";
    }

}
