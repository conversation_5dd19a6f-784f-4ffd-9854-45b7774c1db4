package com.zksr.account.controller.withdraw.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 提现对账单对象 acc_withdraw_bill
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@ApiModel("提现对账单 - acc_withdraw_bill分页 Request VO")
public class AccWithdrawBillSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 提现账单ID */
    @ApiModelProperty(value = "提现商户号")
    private Long withdrawBillId;

    /** 平台商id */
    @ApiModelProperty(value = "提现商户号")
    private Long sysCode;

    /** 平台提现金额 */
    @Excel(name = "平台提现金额")
    @ApiModelProperty(value = "平台提现金额")
    private BigDecimal platformWithdrawAmt;

    /** 商户提现金额 */
    @ApiModelProperty(value = "平台提现金额")
    private BigDecimal merchantWithdrawAmt;

    /** 平台提现手续费 */
    @ApiModelProperty(value = "平台提现金额")
    private BigDecimal platformFree;

    /** 商户提现手续费 */
    @ApiModelProperty(value = "平台提现金额")
    private BigDecimal merchantFree;

    /** 平台交易单号(外部) */
    @ApiModelProperty(value = "平台提现金额")
    private String platformTradeNo;

    /** 商户交易单号(内部) */
    @Excel(name = "商户交易单号(内部)")
    @ApiModelProperty(value = "商户交易单号(内部)")
    private String merchantTradeNo;

    /** 提现到账银行卡号 */
    @Excel(name = "提现到账银行卡号")
    @ApiModelProperty(value = "提现到账银行卡号")
    private String bankAccountNo;

    /** 提现到账银行账户名称 */
    @ApiModelProperty(value = "提现到账银行卡号")
    private String bankAccountName;

    /** 提现发起时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "提现发起时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "提现发起时间")
    private Date requestTime;

    /** 提现完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "提现完成时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "提现完成时间")
    private Date finishTime;

    /** 提现商户号 */
    @Excel(name = "提现商户号")
    @ApiModelProperty(value = "提现商户号")
    private String altNo;

    /** 0-正常,1-异常 */
    @ApiModelProperty(value = "提现商户号")
    private Integer state;

    /** 支付渠道, hlb-合利宝,wxb2b-微信b2b */
    @ApiModelProperty(value = "提现商户号")
    private String platform;

}
