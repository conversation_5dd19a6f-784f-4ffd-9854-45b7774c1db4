package com.zksr.account.mapper;

import com.zksr.account.controller.transfer.vo.AccTransferBillOrderPageReqVO;
import com.zksr.account.domain.AccTransferBillOrder;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交易对账单明细单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Mapper
public interface AccTransferBillOrderMapper extends BaseMapperX<AccTransferBillOrder> {
    default PageResult<AccTransferBillOrder> selectPage(AccTransferBillOrderPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccTransferBillOrder>()
                .eqIfPresent(AccTransferBillOrder::getTransferBillId, reqVO.getTransferBillId())
                .eqIfPresent(AccTransferBillOrder::getPlatformTradeNo, reqVO.getPlatformTradeNo())
                .eqIfPresent(AccTransferBillOrder::getState, reqVO.getState())
                .eqIfPresent(AccTransferBillOrder::getOrderType, reqVO.getOrderType())
                .eqIfPresent(AccTransferBillOrder::getSysCode, reqVO.getSysCode())
                .betweenIfPresent(AccTransferBillOrder::getTransferTime, reqVO.getTransferStartTime(), reqVO.getTransferEndTime())
                .orderByDesc(AccTransferBillOrder::getBillOrderId));
    }

    List<String> countTransferBillsByDateAndAltNo(@Param("dataFormatted") String dataFormatted, @Param("altNo") String altNo);

    void deleteTransferBillsByDateAndAltNo(@Param("dataFormatted") String dataFormatted,@Param("altNo") String altNo);
}
