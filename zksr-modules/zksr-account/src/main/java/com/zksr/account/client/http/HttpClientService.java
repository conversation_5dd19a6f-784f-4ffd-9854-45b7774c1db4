package com.zksr.account.client.http;

import cn.hutool.core.date.TimeInterval;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.security.config.AnntoProxyConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@SuppressWarnings("all")
public class HttpClientService {

    private static final Log LOG = LogFactory.getLog(HttpClientService.class);

    private static final MediaType MEDIA_TYPE_JPG = MediaType.parse("image/jpg");

    private static final MediaType MEDIA_TYPE_VIDEO_MP4 = MediaType.parse("video/mp4");

    public static OkHttpClient client =new OkHttpClient.Builder()
            .connectTimeout(50, TimeUnit.SECONDS)
            .readTimeout(2, TimeUnit.MINUTES)
            .build();

    public static Map<String, Object> getHttpResp(Map<String, String> reqMap, String httpUrl) {
        HttpClient client = new HttpClient();
        PostMethod method = new PostMethod(httpUrl);
        method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
        method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER, new DefaultHttpMethodRetryHandler());
        method.getParams().setParameter(HttpMethodParams.SO_TIMEOUT, new Integer(30000));
        String response = "";

        AnntoProxyConfig proxyConfig = SpringUtils.getBean(AnntoProxyConfig.class);
        if (Objects.nonNull(proxyConfig) && StringUtils.isNotEmpty(proxyConfig.getHost())) {
            log.info("使用安得代理接口...");
            // 设置代理
            client.getHostConfiguration().setProxy(proxyConfig.getHost(), proxyConfig.getPort());
        }
        TimeInterval interval = new TimeInterval();
        Map<String, Object> mp = new HashMap<String, Object>();
        LOG.info("url:" + httpUrl + " ,请求参数reqMap:" + JSON.toJSONString(reqMap));
        try {
            NameValuePair[] nvps = getNameValuePair(reqMap);
            method.setRequestBody(nvps);
            int rescode = client.executeMethod(method);
            mp.put("statusCode", rescode);

            LOG.info("url:" + httpUrl + " ,http rescode:" + rescode);
            if (rescode == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(method.getResponseBodyAsStream(), "UTF-8"));
                String curline = "";
                while ((curline = reader.readLine()) != null) {
                    response += curline;
                }
                mp.put("response", response);
            }
        } catch (Exception e) {
            LOG.error("http request error,url:" + httpUrl, e);
        } finally {
            method.releaseConnection();
        }
        long intervalMs = interval.intervalMs();
        LOG.info("耗时:" + intervalMs + "ms,http response:" + mp.toString());
        return mp;
    }

    public static Map<String, Object> getHttpResp(Map<String, String> reqMap, String httpUrl, Map<String, File> fileMap) {
        MultipartBody.Builder builder = new MultipartBody.Builder();
        builder.setType(MultipartBody.FORM);
        reqMap.forEach(builder::addFormDataPart);
        fileMap.forEach((fileName,file) -> builder.addFormDataPart(fileName, file.getName(),RequestBody.create(MEDIA_TYPE_JPG, file)));
        RequestBody requestBody = builder.build();
        Request request = new Request.Builder() // okHttp post
                .url(httpUrl)
                .post(requestBody)
                .build();
        Response response = null;
        try {
            response = client.newCall(request).execute();
        } catch (IOException e) {
            throw new RuntimeException("请求出错", e);
        }
        if (!response.isSuccessful()) {
            try {
                log.info(response.body().string());
            } catch (IOException e) {
                log.error(" HttpClientService.getHttpResp异常，", e);
            }
            throw new RuntimeException("请求失败了: http response code: " + response.code());
        }
        ResponseBody body = response.body();
        String str = null;
        try {
            if (body == null) {
                throw new RuntimeException("响应 body 体为空");
            }
            str = body.string();
        } catch (Exception e) {
            throw new RuntimeException("处理响应消息过程中出错了", e);
        }
        Map<String, Object> mp = new HashMap<String, Object>();
        mp.put("statusCode", response.code());
        mp.put("response", str);
        return mp;
    }

    /**
     *
     * HTTP协议POST请求方法
     */
    public static String httpMethodPost(String url, String params, String gb) {
        if (null == gb || "".equals(gb)) {
            gb = "UTF-8";
        }
        StringBuffer sb = new StringBuffer();
        URL urls;
        HttpURLConnection uc = null;
        BufferedReader in = null;
        DataOutputStream out = null;
        try {
            urls = new URL(url);
            uc = (HttpURLConnection) urls.openConnection();
            uc.setRequestMethod("POST");
            uc.setDoOutput(true);
            uc.setDoInput(true);
            uc.setUseCaches(false);
            uc.setRequestProperty("Connection", "keep-alive");
            uc.setRequestProperty("Keep-Alive", "timeout=1, max=100");
            uc.setRequestProperty("Content-Length", params.length() + "");
            uc.setRequestProperty("Content-Type","application/json");
            uc.setConnectTimeout(7000);
            uc.setReadTimeout(10000);
            uc.connect();
            out = new DataOutputStream(uc.getOutputStream());
            out.write(params.getBytes(gb));
            out.flush();
            out.close();
            in = new BufferedReader(new InputStreamReader(uc.getInputStream(), gb));
            String readLine = "";
            while ((readLine = in.readLine()) != null) {
                sb.append(readLine);
            }
        } catch (IOException e) {
            LOG.error(e.getMessage(), e);
        } finally {
            try {
                if (out != null){
                    out.close();
                }
                if (in != null){
                    in.close();
                }
                if (uc != null) {
                    uc.disconnect();
                }
            } catch (IOException e) {
                log.error(" HttpClientService.httpMethodPost异常，", e);
            }
        }
        return sb.toString();
    }

    public static NameValuePair[] getNameValuePair(Map<String, String> bean) {
        List<NameValuePair> x = new ArrayList<NameValuePair>();
        for (Iterator<String> iterator = bean.keySet().iterator(); iterator.hasNext(); ) {
            String type = (String) iterator.next();
            x.add(new NameValuePair(type, String.valueOf(bean.get(type))));
        }
        Object[] y = x.toArray();
        NameValuePair[] n = new NameValuePair[y.length];
        System.arraycopy(y, 0, n, 0, y.length);
        return n;
    }
}
