package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayTransferNotifyRespVO {
    /**
     * 接口名称
     */
    @JsonProperty("service")
    private String service;

    /**
     * 接口版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 平台商户号
     */
    @JsonProperty("partner")
    private String partner;

    /**
     * 商户网站使用的编码格式，目前只能取值UTF-8
     */
    @JsonProperty("input_charset")
    private String inputCharset;

    /**
     * 签名方式
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * 签名
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 商户单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 美的支付生成的交易单号
     */
    @JsonProperty("trade_no")
    private String tradeNo;

    /**
     * 交易类型
     * BALANCE：代付到余额
     * CARD：代付到银行卡
     */
    @JsonProperty("trade_type")
    private String tradeType;

    /**
     * 订单的状态码
     * SUCCESS：成功
     * FAIL：失败
     */
    @JsonProperty("trade_status")
    private String tradeStatus;

    /**
     * 订单状态的描述信息
     */
    @JsonProperty("trade_status_info")
    private String tradeStatusInfo;

    /**
     * 代付的总金额（单位分）
     */
    @JsonProperty("pay_amount")
    private String payAmount;

    /**
     * 货币类型：CNY，即人民币
     */
    @JsonProperty("currency_type")
    private String currency_type;

    /**
     * 出款账户类型,
     * PAYMENT：支付账户
     * ESCROW：托管账户
     */
    @JsonProperty("payer_act_type")
    private String payerActType;

    /**
     * 商户自定义信息，回调通知时美的支付将原值返回
     */
    @JsonProperty("attach")
    private String attach;

    /**
     * 代付订单失败错误码
     */
    @JsonProperty("error_code")
    private String errorCode;

    /**
     * 错误码描述
     */
    @JsonProperty("error_info")
    private String errorInfo;

    /**
     * 支付时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("pay_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String payTime;

    /**
     * 美的支付系统接收订单的时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("trade_accept_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String tradeAcceptTime;

    /**
     * 美的支付通知商户时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("notify_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String notifyTime;

}
