package com.zksr.account.service.pay.valid;

import cn.hutool.core.util.NumberUtil;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.UnifiedOrderValidateDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: B2B订单验证
 * @date 2024/10/17 9:42
 */
@Slf4j
@Service
public class PayOrderHlbValidService extends AbsPayOrderValidService{

    @Override
    public PayChannelEnum getPayChannel() {
        return PayChannelEnum.HLB;
    }

    @Override
    protected UnifiedOrderValidateDTO processPayBeforeValidate(PayOrderDTO payOrderDTO) {
        return UnifiedOrderValidateDTO.success();
    }

    @Override
    protected UnifiedOrderValidateDTO processRefundBeforeValidate(PayRefundOrderSubmitReqVO reqDTO) {
        return UnifiedOrderValidateDTO.success();
    }
}
