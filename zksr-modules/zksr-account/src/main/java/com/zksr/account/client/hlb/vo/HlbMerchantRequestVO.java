package com.zksr.account.client.hlb.vo;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableSet;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝进件商户查询
 * @date 2024/4/23 14:59
 */
@Data
public class HlbMerchantRequestVO {
    private String interfaceName = "";
    private String body;
    private String merchantNo;
    private String signType = "MD5";
    private String encryptionKey;
    private Boolean needDecrypt = true;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of(
            "body",
            "merchantNo"
    );

    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = ImmutableSet.of("body");
}
