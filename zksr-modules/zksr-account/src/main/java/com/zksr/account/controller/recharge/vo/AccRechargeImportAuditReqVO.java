package com.zksr.account.controller.recharge.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 后台导入充值对象 acc_recharge_import
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@ApiModel("后台导入充值 - 后台充值批次审核请求")
public class AccRechargeImportAuditReqVO {

    @ApiModelProperty(value = "注解ID,更新需要回传")
    private Long rechargeImportId;

    @ApiModelProperty(value = "验证码")
    private String code;
}
