package com.zksr.account.controller.recharge.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 后台导入充值对象 acc_recharge_import
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Data
@ApiModel("后台导入充值 - acc_recharge_import分页 Request VO")
public class AccRechargeImportSaveReqVO {

    @ApiModelProperty(value = "注解ID,更新需要回传")
    private Long rechargeImportId;

    /** 凭证 */
    @ApiModelProperty(value = "凭证")
    private String voucher;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "充值记录详情")
    private List<AccRechargeImportDtlSaveReqVO> rechargeImportDtls;
}
