package com.zksr.account.service.metchant;

import com.zksr.account.client.MerchantClient;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayTypeEnum;

/**
 * <AUTHOR>
 * @time 2024/4/23
 * @desc
 */
public interface MerchantChannelService {

    /**
     * 获取支付平台商户客户端
     * @param sysCode   平台ID
     * @param platform  支付平台
     * @return 支付平台商户客户端
     */
    MerchantClient getPayMerchantClient(Long sysCode, String platform);


    /**
     * 获取储值平台商户客户端
     * @param sysCode   平台ID
     * @param platform  支付平台
     * @return 储值平台商户客户端
     */
    MerchantClient getStoreMerchantClient(Long sysCode, String platform);

    /**
     * 获取商户客户端
     * @param sysCode   平台ID
     * @param payType   支付平台类型, 储值还是支付
     * @return 平台商设置的默认的支付平台
     */
    MerchantClient getMerchantClient(Long sysCode, PayTypeEnum payType);

    /**
     * 获取商户客户端
     * @param sysCode   平台ID
     * @param payType   支付平台类型, 储值还是支付
     * @param platform  支付平台
     * @return 返回指定支付平台
     */
    MerchantClient getMerchantClient(Long sysCode, PayTypeEnum payType, String platform);
}
