package com.zksr.account.service;

import com.zksr.account.api.withdraw.vo.BranchWithdrawReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawAuditVO;
import com.zksr.account.domain.AccWithdraw;

/**
 * <AUTHOR>
 * @time 2025/2/22
 * @desc
 */
public interface IAccBranchWithdrawService {

    /**
     * 门店余额提现申请
     *
     * @param withdrawReqVO 门店提现申请
     * @return 结果
     */
    AccWithdraw insertWithdraw(BranchWithdrawReqVO withdrawReqVO);

    /**
     * 门店提现审核拒绝
     * @param withdrawAudit 审核信息
     */
    void rejectWithdraw(AccWithdrawAuditVO withdrawAudit);

    /**
     * 门店审核提现通过
     * @param withdrawAudit   审核信息
     */
    void processWithdraw(AccWithdrawAuditVO withdrawAudit);
}
