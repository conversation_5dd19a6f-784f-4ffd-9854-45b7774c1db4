package com.zksr.account.service.metchant.impl;

import com.zksr.account.client.MerchantClient;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.metchant.MerchantChannelService;
import com.zksr.account.service.metchant.MerchantService;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayTypeEnum;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户操作
 * @date 2024/4/23 14:54
 */
@Service
public class MerchantServiceImpl implements MerchantService {

    @Autowired
    private MerchantChannelService merchantChannelService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Resource
    private SupplierApi supplierApi;

    @Override
    public PayPlatformAccountVO getBalance(String merchantNo, Long sysCode, PayTypeEnum payTypeEnum) {
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(sysCode, payTypeEnum);
        return merchantClient.getAccountBalance(merchantNo);
    }

    @Override
    public PayPlatformAccountVO getBalance(String merchantNo, Long sysCode, PayChannelEnum platform) {
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(sysCode, PayTypeEnum.PAY, platform.getCode());
        return merchantClient.getAccountBalance(merchantNo);
    }

    @Override
    public PayPlatformAccountVO getSupplierBalance(Long supplierId) {
        SupplierDTO supplierDTO = supplierApi.getBySupplierId(supplierId).getCheckedData();
        PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(supplierDTO.getSysCode());
        if (Objects.isNull(payConfigDTO)) {
            return PayPlatformAccountVO.DEFAULT;
        }
        payConfigDTO.setStoreOrderPayPlatform(PayChannelEnum.HLB.getCode()); // 兼容店小盟, 旧余额处理
        AccPlatformMerchant merchant = platformMerchantService.getPlatformMerchant(supplierId, MerchantTypeEnum.SUPPLIER.getType(), payConfigDTO.getStoreOrderPayPlatform());
        if (Objects.isNull(merchant)) {
            return PayPlatformAccountVO.DEFAULT;
        }
        //PayPlatformAccountVO accountVO = getBalance(merchant.getAltMchNo(), merchant.getSysCode(), PayTypeEnum.PAY);
        PayPlatformAccountVO accountVO = getBalance(merchant.getAltMchNo(), merchant.getSysCode(), PayTypeEnum.STORE); // 兼容店小盟, 旧余额处理
        if (Objects.nonNull(accountVO)) {
            accountVO.setPlatform(payConfigDTO.getStoreOrderPayPlatform());
        }
        return accountVO;
    }

    @Override
    public PayPlatformMerchantVO getMerchantInfo(Long sysCode, String platform, String orderNo, String merchantNo) {
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(sysCode, null, platform);
        return merchantClient.getRegisterMerchant(orderNo, merchantNo);
    }
}
