package com.zksr.account.service.rootMock.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.pay.PayOrderService;
import com.zksr.account.service.rootMock.RootMockPayService;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.PayRefundReqVO;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;
import static com.zksr.common.core.web.pojo.CommonResult.success;

@Service
@Slf4j
@SuppressWarnings("all")
public class RootMockPayServiceImpl implements RootMockPayService {

    @Autowired
    private PayOrderService payOrderService;

    @Autowired
    private OrderApi orderApi;
    
    @Autowired
    private IAccPlatformMerchantService platformMerchantService;
    
    @Override
    public CommonResult<PayRefundRespDTO> mockRefund(PayRefundOrderSubmitReqVO reqVO) {
        String supplierOrderNo = reqVO.getSupplierOrderNo();
        BigDecimal oldRefundAmt = reqVO.getRefundAmt();
        PayRefundReqVO vo = orderApi.getPayRefundReq(supplierOrderNo).getCheckedData();
        if (vo == null) {
            throw new ServiceException("未找到对应的付款数据");
        }
        //有输入退款金额，以输入的为准
        if (null != oldRefundAmt && oldRefundAmt.compareTo(BigDecimal.ZERO) > 0) {
            vo.setRefundAmt(oldRefundAmt);
        }
        AccPlatformMerchant payee = platformMerchantService.getPlatformMerchant(vo.getMerchantId(), MerchantTypeEnum.SUPPLIER.getType(), vo.getPlatform(), vo.getSysCode());
        if (Objects.isNull(payee) || StringUtils.isEmpty(payee.getAltMchNo())) {
            throw new ServiceException(StringUtils.format("商户ID:{}, 未查询到进件信息", vo.getMerchantId()));
        }
       String altMchNo = payee.getAltMchNo();
        
        PayRefundOrderSubmitReqVO reqDTO = new PayRefundOrderSubmitReqVO();
        List<OrderSettlementDTO> settlement = new ArrayList<>();
        OrderSettlementDTO orderSettlementDTO = new OrderSettlementDTO();
        orderSettlementDTO.setAccountNo(altMchNo);
        orderSettlementDTO.setAmt(vo.getRefundAmt());
        settlement.add(orderSettlementDTO);
        reqDTO.setSettlements(settlement);
        BeanUtil.copyProperties(vo, reqDTO);
        return success(payOrderService.createPayRefund(reqDTO));
    }
}