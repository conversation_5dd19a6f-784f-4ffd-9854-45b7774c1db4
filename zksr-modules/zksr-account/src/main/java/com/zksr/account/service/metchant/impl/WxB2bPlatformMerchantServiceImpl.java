package com.zksr.account.service.metchant.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRegisterReqVO;
import com.zksr.account.api.platformMerchant.vo.PlatformMerchantRegisterSaveRespVO;
import com.zksr.account.api.platformMerchant.vo.WxB2bPlatformMerchantRegisterSaveReqVO;
import com.zksr.account.client.MerchantClient;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoReqVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoRespVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bSaveReqVO;
import com.zksr.account.convert.wxb2b.AccPlatformMerchantWxb2bConvert;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.domain.AccPlatformMerchantWxb2b;
import com.zksr.account.mapper.AccPlatformMerchantMapper;
import com.zksr.account.mapper.AccPlatformMerchantWxb2bMapper;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.metchant.MerchantChannelService;
import com.zksr.account.service.metchant.PlatformMerchantService;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.file.api.file.vo.MchProfitRateReqVO;
import com.zksr.file.api.file.vo.MchProfitRateRespVO;
import com.zksr.system.api.bank.BankChannelApi;
import com.zksr.system.api.bank.vo.SysBankChannelRespVO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝商户信息处理
 * @date 2024/9/19 14:39
 */
@Service
public class WxB2bPlatformMerchantServiceImpl implements PlatformMerchantService {

    @Autowired
    private AccPlatformMerchantMapper accPlatformMerchantMapper;

    @Autowired
    private AccPlatformMerchantWxb2bMapper accPlatformMerchantWxb2bMapper;

    @Resource
    private BankChannelApi bankChannelApi;

    @Autowired
    private MerchantChannelService merchantChannelService;

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Override
    public PayChannelEnum channel() {
        return PayChannelEnum.WX_B2B_PAY;
    }

    @Override
    public void syncData(PayPlatformMerchantVO registerMerchant, AccPlatformMerchant platformMerchant, MerchantClient merchantClient) {
        AccPlatformMerchant update = new AccPlatformMerchant();
        update.setPlatformMerchantId(platformMerchant.getPlatformMerchantId());
        update.setAuditStatus(registerMerchant.getState().getState());
        update.setAuthMsg(registerMerchant.getMsg());
        update.setAltMchName(platformMerchant.getAltMchName());
        // 是否需要更新外链
        if (registerMerchant.getState().updateLink()) {
            update.setOutLink(registerMerchant.getOutLink());
        }

        // 更新商户号
        if (registerMerchant.getState().success()) {

            if (StringUtils.isEmpty(platformMerchant.getAltMchNo())) {
                // 设置商户号
                update.setAltMchNo(registerMerchant.getMerchantNo());
            }
            if (StringUtils.isNotEmpty(update.getAltMchNo())) {
                // 添加分账方
                merchantClient.addSharing(update);
            }
            if (StringUtils.isNotEmpty(platformMerchant.getAltMchNo())) {
                // 添加分账方
                merchantClient.addSharing(platformMerchant);
            }
        }

        // 更新数据
        accPlatformMerchantMapper.updateById(update);
    }

    @Override
    public Long saveRegisterInfo(AccPlatformMerchantRegisterReqVO reqVO, PlatformMerchantRegisterSaveRespVO respVO) {
        // 查询系统是不是已经有这个商户的数据了
        AccPlatformMerchant accPlatformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(reqVO.getMerchantId(), reqVO.getMerchantType(), reqVO.getPlatform());

        WxB2bPlatformMerchantRegisterSaveReqVO saveReqVO = reqVO.getWxB2bPlatformMerchantRegisterSaveReqVO();

        // 根据联行号获取银行信息
        SysBankChannelRespVO bankChannelRespVO = bankChannelApi.getChannelByNo(reqVO.getWxB2bPlatformMerchantRegisterSaveReqVO().getAccountInfo().getBankBranchId()).getCheckedData();

        // 审核中, 或者审核成功, 这里保存商户信息
        AccPlatformMerchant platformMerchant = AccPlatformMerchant
                .builder()
                .sysCode(reqVO.getSysCode())
                .merchantType(reqVO.getMerchantType())
                .merchantId(reqVO.getMerchantId())
                .platform(reqVO.getPlatform())
                .altMchNo(respVO.getMerchantNo())
                .thirdOrderNo(respVO.getOrderNo())
                .altMchName(saveReqVO.getBusinessLicense().getMerchantName())
                .mchStatus(StringPool.ONE)
                .accountName(saveReqVO.getAccountInfo().getAccountBank())
                .accountNo(saveReqVO.getAccountInfo().getAccountNumber())
                .bankType("75".equals(saveReqVO.getAccountInfo().getBankAccountType()) ? "TOPRIVATE" : "TOPUBLIC")
                .busiMerchantType(saveReqVO.getOrganizationType() == 0 ? "0" : "2")
                .legalPerson(saveReqVO.getIdCardInfo().getIdCardName())
                .auditStatus(respVO.getAuditStatus())
                .editStatus(StringPool.EMPTY)
                .editMsg(StringPool.EMPTY)
                .picMsg(StringPool.EMPTY)
                .picMsg(StringPool.EMPTY)
                .idCard(saveReqVO.getIdCardInfo().getIdCardNumber())
                .idCardAddress(saveReqVO.getIdCardInfo().getIdCardAddress())
                .cardPositiveUrl(saveReqVO.getIdCardInfo().getIdCardCopy())
                .cardNegativeUrl(saveReqVO.getIdCardInfo().getIdCardNational())
                .tradeLicenceUrl(saveReqVO.getBusinessLicense().getBusinessLicenseCopy())
                .bankChannelNo(saveReqVO.getAccountInfo().getBankBranchId())
                .contractName(saveReqVO.getContactInfo().getContactName())
                .contractPhone(saveReqVO.getContactInfo().getMobilePhone())
                .licenseNo(saveReqVO.getBusinessLicense().getBusinessLicenseNumber())
                .licenceAddress(saveReqVO.getBusinessLicense().getCompanyAddress())
                .build();
        // 设置银行信息
        if (Objects.nonNull(bankChannelRespVO)) {
            platformMerchant.setBankName(bankChannelRespVO.getBankName());
            platformMerchant.setBankBranch(bankChannelRespVO.getBranchName());
        }
        if (Objects.nonNull(accPlatformMerchant)) {
            platformMerchant.setPlatformMerchantId(accPlatformMerchant.getPlatformMerchantId());
            accPlatformMerchantMapper.updateById(platformMerchant);
        } else {
            accPlatformMerchantMapper.insert(platformMerchant);
        }
        return platformMerchant.getPlatformMerchantId();
    }


    /**
     * 获取商户配置
     * @param reqVO
     * @return
     */
    @Override
    public AccPlatformMerchantWxb2bInfoRespVO getUpdateWxB2bPayConfig(AccPlatformMerchantWxb2bInfoReqVO reqVO) {
        return AccPlatformMerchantWxb2bConvert.INSTANCE.convertInfoRespVO(
                accPlatformMerchantWxb2bMapper.selectByMerchantTypeAndMerchantId(reqVO.getMerchantType(), reqVO.getMerchantId())
        );
    }

    /**
     * 更新商户配置
     * @param saveReqVO
     * @return
     */
    @Override
    @Transactional
    public Long updateWxB2bPayConfig(AccPlatformMerchantWxb2bSaveReqVO saveReqVO) {
        // 查询系统是不是已经有这个商户的数据了
        AccPlatformMerchant accPlatformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(saveReqVO.getMerchantId(), saveReqVO.getMerchantType(), channel().getCode());
        if (Objects.isNull(accPlatformMerchant) || StringUtils.isEmpty(accPlatformMerchant.getAltMchNo())) {
            throw exception(PLATFORM_WX_MERCHANT_SHARING_ERR);
        }

        AccPlatformMerchantWxb2b save = AccPlatformMerchantWxb2bConvert.INSTANCE.convertPO(saveReqVO);
        AccPlatformMerchantWxb2b merchantWxb2b = accPlatformMerchantWxb2bMapper.selectByMerchantTypeAndMerchantId(saveReqVO.getMerchantType(), saveReqVO.getMerchantId());
        if (Objects.nonNull(merchantWxb2b)) {
            save.setWxB2bPlatformMerchantId(merchantWxb2b.getWxB2bPlatformMerchantId());
            accPlatformMerchantWxb2bMapper.updateById(save);
        } else {
            accPlatformMerchantWxb2bMapper.insert(save);
        }
        accPlatformMerchant.setAltMchKey(save.getAppKey());
        accPlatformMerchantMapper.updateById(accPlatformMerchant);

        // 获取平台商户操作client
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(
                SecurityUtils.getLoginUser().getSysCode(),
                PayTypeEnum.PAY,
                channel().getCode());
        saveReqVO.setAltNo(accPlatformMerchant.getAltMchNo());
        //merchantClient.updateB2bWithdraw(saveReqVO);

        // 获取 profitRateUrl
        if (Objects.nonNull(saveReqVO.getProfitRate())) {
            if (Objects.isNull(merchantWxb2b) || Objects.isNull(merchantWxb2b.getProfitRate()) || !NumberUtil.equals(merchantWxb2b.getProfitRate(), saveReqVO.getProfitRate())) {
                AppletBaseConfigDTO baseConfigDTO = accountCacheService.getAppletBaseConfigDTO(accPlatformMerchant.getSysCode());
                String profitRateUrl = redisSysConfigService.get(CacheConstants.WECHAT_SERVICE_PROVIDER_MCH_PROFIT_RATE_URL);
                if (StringUtils.isEmpty(profitRateUrl)) {
                    throw exception(PLATFORM_WX_MERCHANT_MCH_PROFIT_RATE_URL_NONE);
                }
                MchProfitRateReqVO rateReqVO = new MchProfitRateReqVO();
                rateReqVO.setSubMchid(accPlatformMerchant.getAltMchNo());
                rateReqVO.setProfitRate(saveReqVO.getProfitRate());
                rateReqVO.setAppid(baseConfigDTO.getAppId());

                // 发送白名单服务器请求设置费率
                String result = HttpUtil.post(profitRateUrl, JSON.toJSONString(rateReqVO));
                MchProfitRateRespVO respVO = JSON.parseObject(result, MchProfitRateRespVO.class);
                if (Objects.isNull(respVO.getSuccess())) {
                    throw exception(PLATFORM_WX_MERCHANT_MCH_PROFIT_RATE_ERR);
                }
                if (!respVO.getSuccess()) {
                    if (respVO.getMsg().contains("不在可以设置费率的时间范围内")) {
                        throw exception(PLATFORM_WX_MERCHANT_MCH_PROFIT_RATE_TIMEOUT);
                    }
                }
            }
        }
        return save.getWxB2bPlatformMerchantId();
    }

    @Override
    public Long directSaveMerchant(PlatformMerchantDTO platformMerchant) {
        // 获取平台商户操作client
        MerchantClient merchantClient = merchantChannelService.getMerchantClient(
                platformMerchant.getSysCode(),
                PayTypeEnum.PAY,
                platformMerchant.getPlatform());

        // 商户名称不能为空
        if (StringUtils.isEmpty(platformMerchant.getAltMchName())) {
            throw exception(PLATFORM_WX_MERCHANT_SHARING_ERR1);
        }

        // 这一步不用管有没有成功, 失败了也是成功, 只要最终添加进去就好了, 失败了也不用影响流程
        boolean sharing = merchantClient.addSharing(BeanUtil.toBean(platformMerchant, AccPlatformMerchant.class));
        if (!sharing) {
            throw exception(PLATFORM_WX_MERCHANT_SHARING_ERR);
        }

        // b2b商户信息调整
        AccPlatformMerchantWxb2b merchantWxb2b = accPlatformMerchantWxb2bMapper.selectByMerchantTypeAndMerchantId(platformMerchant.getMerchantType(), platformMerchant.getMerchantId());
        if (Objects.nonNull(merchantWxb2b)) {
            merchantWxb2b.setMerchantType(platformMerchant.getMerchantType());
            merchantWxb2b.setMerchantId(platformMerchant.getMerchantId());
            merchantWxb2b.setAppKey(platformMerchant.getAltMchKey());
            merchantWxb2b.setNickName(platformMerchant.getAltMchName());
            accPlatformMerchantWxb2bMapper.updateById(merchantWxb2b);
        } else {
            merchantWxb2b = new AccPlatformMerchantWxb2b();
            merchantWxb2b.setSysCode(platformMerchant.getSysCode());
            merchantWxb2b.setMerchantType(platformMerchant.getMerchantType());
            merchantWxb2b.setMerchantId(platformMerchant.getMerchantId());
            merchantWxb2b.setAppKey(platformMerchant.getAltMchKey());
            merchantWxb2b.setNickName(platformMerchant.getAltMchName());
            accPlatformMerchantWxb2bMapper.insert(merchantWxb2b);
        }

        // 查询系统是不是已经有这个商户的数据了
        AccPlatformMerchant accPlatformMerchant = accPlatformMerchantMapper.selectPlatformMerchant(platformMerchant.getMerchantId(), platformMerchant.getMerchantType(), platformMerchant.getPlatform());
        if (Objects.nonNull(accPlatformMerchant)) {
            accPlatformMerchant.setAltMchNo(platformMerchant.getAltMchNo());
            accPlatformMerchant.setAltMchKey(platformMerchant.getAltMchKey());
            accPlatformMerchant.setAltMchName(platformMerchant.getAltMchName());
            accPlatformMerchant.setThirdOrderNo(platformMerchant.getThirdOrderNo());
            // 直接设置成成功, 如果有商户号, 说明已经申请到了
            if (StringUtils.isNotEmpty(accPlatformMerchant.getAltMchNo())) {
                accPlatformMerchant.setAuditStatus(MerchantRegisterStateEnum.AUDITED.getState());
            }
            accPlatformMerchantMapper.updateById(accPlatformMerchant);
            return accPlatformMerchant.getPlatformMerchantId();
        } else {
            AccPlatformMerchant merchant = BeanUtil.toBean(platformMerchant, AccPlatformMerchant.class);
            merchant.setMchStatus(StringPool.ONE);
            merchant.setAuditStatus(MerchantRegisterStateEnum.AUDITED.getState());
            accPlatformMerchantMapper.insert(merchant);
            return merchant.getPlatformMerchantId();
        }
    }
}
