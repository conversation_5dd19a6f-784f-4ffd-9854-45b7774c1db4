package com.zksr.account.client.mideapay.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zksr.account.client.mideapay.vo.request.MideaPayBaseReqVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayWechatReqVO extends MideaPayBaseReqVO {
    /**
     * 担保交易标志
     */
    @JsonProperty("is_guarantee")
    private String isGuarantee;

    /**
     * 商户单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 支付类型,
     * SCAN_CODE：扫码
     * OFFICIAL_ACCT：公众号/服务窗
     * MINI_PROGRAM：小程序
     * APP：app
     * H5：H5支付
     */
    @JsonProperty("bar_code")
    private String barCode;

    @JsonProperty("app_id")
    private String appId;

    @JsonProperty("open_id")
    private String openId;

    /**
     * 货币类型
     */
    @JsonProperty("currency_type")
    private String currencyType;

    /**
     * 订单总金额
     */
    @JsonProperty("total_order_amount")
    private String totalOrderAmount;

    /**
     * 支付总金额
     */
    @JsonProperty("total_amount")
    private String totalAmount;

    /**
     * 子单数量
     */
    @JsonProperty("total_count")
    private Integer totalCount;

    /**
     * limit_acct_type
     * DC：借记卡
     * DOC：借记卡和信用卡（双卡）
     */
    @JsonProperty("limit_acct_type")
    private String limitAcctType;

    /**
     * 收银台会话的超时时间，单位：分，时间范围：1-30，不传默认为30
     * 接口版本号大于等于3.3.0，还表示订单关单时间，不传表示不限制
     */
    @JsonProperty("pay_expire_time")
    private Long payExpireTime;

    /**
     * 产品标识：
     * TRUE：虚拟交易
     * FALSE：实物交易
     */
    @JsonProperty("is_virtual_product")
    private String isVirtualProduct;

    /**
     * 商品名称
     */
    @JsonProperty("product_name")
    private String productName;

    /**
     * 商品的描述
     */
    @JsonProperty("product_info")
    private String productInfo;

    /**
     * 商户自定义的信息，回调通知的时候美的支付平台会原值返回给商户
     */
    @JsonProperty("attach")
    private String attach;

    /**
     * 风控参数
     */
    @JsonProperty("risk_params")
    private String riskParams;

    /**
     * 商户网站url（H5支付方式必传）
     */
    @JsonProperty("wap_url")
    private String wapUrl;

    /**
     * 商户网站名称（H5支付方式必传）
     */
    @JsonProperty("wap_name")
    private String wapName;

    /**
     * 子单列表，格式为json字符串
     */
    @JsonProperty("sub_orders")
    private String subOrders;

}
