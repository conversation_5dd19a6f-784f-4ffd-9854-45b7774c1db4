package com.zksr.account.service.pay.valid;

import cn.hutool.core.util.NumberUtil;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.UnifiedOrderValidateDTO;
import com.zksr.account.model.pay.vo.CreateDivideRespVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.pay.PayWxB2bDivideOrderService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.b2bAuth.B2bAuthApi;
import com.zksr.member.api.b2bAuth.dto.MemMemberOpenAuthDTO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.PartnerMiniSettingPolicyDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: B2B订单验证
 * @date 2024/10/17 9:42
 */
@Slf4j
@Service
public class PayOrderB2bValidService extends AbsPayOrderValidService{

    @Autowired
    private PayWxB2bDivideOrderService payWxB2bOrderService;

    @Resource
    private B2bAuthApi b2bAuthApi;

    @Override
    public PayChannelEnum getPayChannel() {
        return PayChannelEnum.WX_B2B_PAY;
    }

    @Override
    protected UnifiedOrderValidateDTO processPayBeforeValidate(PayOrderDTO payOrderDTO) {
        // 入驻商结算信息, 里面有入驻商商户号
        List<OrderSettlementDTO> payOrderSettles;
        BigDecimal profit;
        if (payOrderDTO.getOrderType() == OrderTypeConstants.BRANCH_CHARGE) {
            // 门店充值验证非运营商总利润
            profit = payOrderDTO.getSettlements().stream().filter(item -> !MerchantTypeEnum.isDc(item.getMerchantType())).map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            payOrderSettles = payOrderDTO.getSettlements().stream().filter(item -> MerchantTypeEnum.isDc(item.getMerchantType())).collect(Collectors.toList());
        } else {
            // 非入驻商总利润
            profit = payOrderDTO.getSettlements().stream().filter(item -> !MerchantTypeEnum.isSupplier(item.getMerchantType())).map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            payOrderSettles = payOrderDTO.getSettlements().stream().filter(item -> MerchantTypeEnum.isSupplier(item.getMerchantType())).collect(Collectors.toList());
        }
        // 是否大于分账限制29%
        if (NumberUtil.isGreater(profit.divide(payOrderDTO.getPayAmt(), 8, RoundingMode.HALF_DOWN), new BigDecimal("0.29"))) {
            log.error("{} 订单分账金额大于订单最大分账比例29%, 订单金额={},利润={}", payOrderDTO.getOrderNo(), payOrderDTO.getPayAmt(), profit);
            return UnifiedOrderValidateDTO.fail("单分账金额大于订单最大分账比例");
        }

        // 验证有没有配置商户秘钥
        OrderSettlementDTO paySettle = payOrderSettles.get(0);
        // 所有的参与方, 分账商户号不能重复
        // 允许分账方和入驻商商户一致, 如果是和入驻商商户一致的话, 不进行分账
        Map<String, List<OrderSettlementDTO>> accountMap =
                payOrderDTO.getSettlements()
                        .stream()
                        .filter(item -> StringUtils.isNotEmpty(item.getAccountNo()) && !paySettle.getAccountNo().equals(item.getAccountNo()))
                        .collect(Collectors.groupingBy(OrderSettlementDTO::getAccountNo));
        for (String accountNo : accountMap.keySet()) {
            if (accountMap.get(accountNo).size() > 1) {
                log.error("微信分账要求, {} {} 商户号重复 参与分账方商户号不能重复~", MerchantTypeEnum.getName(accountMap.get(accountNo).get(0).getMerchantType()), accountNo);
                return UnifiedOrderValidateDTO.fail("结算商户重复");
            }
        }

        // 强制门店认证验证
        PartnerMiniSettingPolicyDTO miniSettingPolicy = accountCacheService.getPartnerMiniSettingPolicy(payOrderDTO.getSysCode());
        if (Objects.nonNull(miniSettingPolicy) && StringPool.ONE.equals(miniSettingPolicy.getForceWechatMerchantAuth())) {
            if (StringUtils.isEmpty(payOrderDTO.getOpenid())) {
                return UnifiedOrderValidateDTO.fail("当前平台开启了微信门店强制认证, 请认证后再次重试~");
            }
            MemMemberOpenAuthDTO memberOpenAuth = b2bAuthApi.getBind(payOrderDTO.getAppid(), payOrderDTO.getOpenid(), payOrderDTO.getBranchId()).getCheckedData();
            if (Objects.isNull(memberOpenAuth) || memberOpenAuth.getAuthState() == NumberPool.INT_ZERO) {
                return UnifiedOrderValidateDTO.fail("当前平台开启了微信门店强制认证, 请认证后再次重试~");
            }
        }
        return UnifiedOrderValidateDTO.success();
    }

    @Override
    protected UnifiedOrderValidateDTO processRefundBeforeValidate(PayRefundOrderSubmitReqVO reqDTO) {
        // 验证这个入驻商是不是需要全部退款, 如果是全部退款
        BigDecimal payAmt = reqDTO.getPayFlowDTO().getPayAmt();
        // 验证退分账有没有多退
        List<AccPayFlow> refundFlows = payFlowService.getByOrderRefundFlowList(reqDTO.getPayFlowDTO().getTradeNo());
        // 历史退款
        BigDecimal historyRefund = refundFlows.stream().map(AccPayFlow::getRefundAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (NumberUtil.isGreaterOrEqual(historyRefund.add(reqDTO.getRefundAmt()), payAmt)) {
            // 调用完成分账, 完成所有分账
            // 因为退款导致的提前订单分账完成
            CreateDivideRespVO divideRespVO = payWxB2bOrderService.refundOver(reqDTO.getPayFlowDTO());
            UnifiedOrderValidateDTO success = UnifiedOrderValidateDTO.success();
            if (divideRespVO.isSuccess()) {
                // 你可别觉着这代码没有鸟用, 微信分账完成不会马上完成, 没完成就退款, 余额等于0时就会直接退款失败
                success.setCode(1);
            }
            return success;
        }
        return UnifiedOrderValidateDTO.success();
    }
}
