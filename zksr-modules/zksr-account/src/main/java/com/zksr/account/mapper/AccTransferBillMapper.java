package com.zksr.account.mapper;

import com.zksr.account.controller.transfer.vo.AccTransferBillPageReqVO;
import com.zksr.account.domain.AccTransferBill;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
/**
 * 交易对账单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 *//**
 * 交易对账单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Mapper
public interface AccTransferBillMapper extends BaseMapperX<AccTransferBill> {
    default PageResult<AccTransferBill> selectPage(AccTransferBillPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccTransferBill>()
                .eqIfPresent(AccTransferBill::getPlatform, reqVO.getPlatform())
                .eqIfPresent(AccTransferBill::getSysCode, reqVO.getSysCode())
                .betweenIfPresent(AccTransferBill::getBillDate, reqVO.getBillStartDate(), reqVO.getBillEndDate())
                .orderByDesc(AccTransferBill::getTransferBillId));
    }

    default int deleteTransferBill(String transferBillId){
        return delete(AccTransferBill::getTransferBillId, transferBillId);
    }
}
