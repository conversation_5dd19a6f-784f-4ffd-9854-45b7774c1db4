package com.zksr.account.api.divide;

import com.alibaba.fastjson.JSON;
import com.zksr.account.api.divide.dto.AccDivideDtlDTO;
import com.zksr.account.api.divide.dto.DivideAmtDTO;
import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.convert.divide.AccDivideDtlConvert;
import com.zksr.account.convert.divideFlow.AccDivideFlowConvert;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.mapper.AccDivideDtlMapper;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.vo.CreateDivideReqVO;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideReqVO;
import com.zksr.account.service.IAccDivideFlowService;
import com.zksr.account.service.IAccPayFlowService;
import com.zksr.account.service.pay.DivideOrderService;
import com.zksr.account.service.pay.IPayDivideOrderService;
import com.zksr.common.core.enums.DivideStateEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分账流水API
 * @date 2024/9/25 8:41
 */
@RestController
@InnerAuth
@ApiIgnore
@Slf4j
public class DivideFlowApiImpl implements DivideFlowApi {

    @Autowired
    private IAccDivideFlowService divideFlowService;

    @Autowired
    private DivideOrderService divideOrderService;

    @Autowired
    private IAccPayFlowService payFlowService;
    
    @Autowired
    private AccDivideDtlMapper accDivideDtlMapper;


    /**
     * 获取需要查询最新处理状态的分账流水
     * @param minId 查询最小起始ID
     * @return  每批次500条
     */
    @Override
    public CommonResult<List<DivideFlowDTO>> getTryDivideFlow(Long minId) {
        return success(
                AccDivideFlowConvert.INSTANCE.convertDTOList(divideFlowService.getTryDivideFlow(minId))
        );
    }

    @Override
    public void updateDivideStatus(List<DivideFlowDTO> itemList) {
        itemList.forEach(item -> {
            IPayDivideOrderService payDivideOrderService = divideOrderService.getDivideOrderService(item.getTradeNo());
            if (Objects.isNull(payDivideOrderService)) {
                log.info("在线支付分账, 没有找到据图的处理服务, 订单号: {}", item.getTradeNo());
                return;
            }
            // 更新分账状态
            payDivideOrderService.updateDivideStatus(item);
            // 检查分账是否已经全部完成
            payDivideOrderService.divideCheckOver(new PayWxB2bCreateDivideReqVO(item.getTradeNo(), item.getTargetPlatformMerchantId(), item.getPayeeType()));
        });
    }

    @Override
    public CommonResult<Boolean> createAdvideFlow(DivideAmtDTO divideAmtDTO) {
        String supplierOrderNo = divideAmtDTO.getSupplierOrderNo();
        // 获取支付流水
        AccPayFlow payFlow = payFlowService.getByOrderPayFlow(supplierOrderNo);
        if (payFlow == null) {
            log.error("未找到支付流水，supplierOrderNo: {}", supplierOrderNo);
            return success(false);
        }
        
        Map<String, BigDecimal> divideAmtMap = divideAmtDTO.getAccountDivideAmt();
        log.info("createAdvideFlow-> supplierOrderNo：{},divideAmtMap:{}",supplierOrderNo, JSON.toJSONString(divideAmtMap));
        
        ArrayList<AccDivideDtl> saveList = new ArrayList<>();
        for (OrderSettlementDTO settlementDTO : payFlow.buildSettle()) {
            String key = StringUtils.format("{}_{}", settlementDTO.getMerchantId(), settlementDTO.getMerchantType());
            if (!divideAmtMap.containsKey(key)) {
                continue;
            }
            //分账金额
            BigDecimal amt = divideAmtDTO.getAccountDivideAmt().get(key);
            AccDivideDtl divideDtl = AccDivideDtl.builder()
             .sysCode(payFlow.getSysCode())
             .merchantType(settlementDTO.getMerchantType())
             .merchantId(settlementDTO.getMerchantId())
             .altMchNo(settlementDTO.getAccountNo())
             .orderAmt(payFlow.getPayAmt())
             .altAmt(amt)
             .fee(payFlow.getFee())
             .platform(payFlow.getPlatform())
             .payFlowId(payFlow.getPayFlowId())
             .onlineOrOffline(NumberPool.INT_ZERO)
             .onlineDivideState(DivideStateEnum.UNDEFINED.getState())
             .tradeNo(payFlow.getTradeNo())
             .subTradeNo(settlementDTO.getSubOrderNo())
             .outTradeNo(payFlow.getOutTradeNo())
             .payType(NumberPool.INT_ZERO)
             .build();
            saveList.add(divideDtl);
        }
        if (!saveList.isEmpty()) {
            accDivideDtlMapper.insertBatch(saveList);
        }
        return success(true);
    }
    
    
    
    
    
    
    
    
    @Override
    public CommonResult<AccDivideDtlDTO> getAccDivideDtlById(AccDivideDtlDTO dto) {
        AccDivideDtl accDivideDtl = accDivideDtlMapper.selectById(dto.getDivideDtlId());
        if (ToolUtil.isEmpty(accDivideDtl)) {
            return null;
        }
        return success(AccDivideDtlConvert.INSTANCE.convertDTO(accDivideDtl));
    }
}
