package com.zksr.account.api.divide;

import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.convert.divideFlow.AccDivideFlowConvert;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideReqVO;
import com.zksr.account.service.IAccDivideFlowService;
import com.zksr.account.service.pay.DivideOrderService;
import com.zksr.account.service.pay.IPayDivideOrderService;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分账流水API
 * @date 2024/9/25 8:41
 */
@RestController
@InnerAuth
@ApiIgnore
@Slf4j
public class DivideFlowApiImpl implements DivideFlowApi{

    @Autowired
    private IAccDivideFlowService divideFlowService;

    @Autowired
    private DivideOrderService divideOrderService;

    /**
     * 获取需要查询最新处理状态的分账流水
     * @param minId 查询最小起始ID
     * @return  每批次500条
     */
    @Override
    public CommonResult<List<DivideFlowDTO>> getTryDivideFlow(Long minId) {
        return success(
                AccDivideFlowConvert.INSTANCE.convertDTOList(divideFlowService.getTryDivideFlow(minId))
        );
    }

    @Override
    public void updateDivideStatus(List<DivideFlowDTO> itemList) {
        itemList.forEach(item -> {
            IPayDivideOrderService payDivideOrderService = divideOrderService.getDivideOrderService(item.getTradeNo());
            if (Objects.isNull(payDivideOrderService)) {
                log.info("在线支付分账, 没有找到据图的处理服务, 订单号: {}", item.getTradeNo());
                return;
            }
            // 更新分账状态
            payDivideOrderService.updateDivideStatus(item);
            // 检查分账是否已经全部完成
            payDivideOrderService.divideCheckOver(new PayWxB2bCreateDivideReqVO(item.getTradeNo(), item.getTargetPlatformMerchantId(), item.getPayeeType()));
        });
    }
}
