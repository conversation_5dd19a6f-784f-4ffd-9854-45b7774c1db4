package com.zksr.account.controller.transfer;

import javax.validation.Valid;

import com.zksr.account.service.transfer.IAccTransferBillOrderService;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.security.utils.SecurityUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccTransferBillOrder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.controller.transfer.vo.AccTransferBillOrderPageReqVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderRespVO;
import com.zksr.account.convert.transfer.AccTransferBillOrderConvert;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 交易对账单明细单Controller
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Api(tags = "管理后台 - 交易对账单明细单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/transfer")
public class AccTransferBillOrderController {
    @Autowired
    private IAccTransferBillOrderService accTransferBillOrderService;

    /**
     * 分页查询交易对账单明细单
     */
    @GetMapping("/getAccTransferBillOrderList")
    @ApiOperation(value = "获得交易对账单明细单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccTransferBillOrderRespVO>> getPage(@Valid AccTransferBillOrderPageReqVO pageReqVO) {
        pageReqVO.setSysCode(SecurityUtils.getLoginUser().getSysCode());
        return success(accTransferBillOrderService.getAccTransferBillOrderPage(pageReqVO));
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /** 列表 */
        public static final String LIST = "account:transfer:getAccTransferBillOrderList";
    }
}
