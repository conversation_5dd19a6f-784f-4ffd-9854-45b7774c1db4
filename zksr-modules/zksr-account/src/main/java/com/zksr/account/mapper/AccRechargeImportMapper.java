package com.zksr.account.mapper;

import com.zksr.account.controller.recharge.vo.AccRechargeImportRespVO;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccRechargeImport;
import com.zksr.account.controller.recharge.vo.AccRechargeImportPageReqVO;

import java.util.List;


/**
 * 后台导入充值Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@Mapper
public interface AccRechargeImportMapper extends BaseMapperX<AccRechargeImport> {

    List<AccRechargeImportRespVO> selectPageExt(AccRechargeImportPageReqVO reqVO);
}
