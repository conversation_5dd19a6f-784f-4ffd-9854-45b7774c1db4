package com.zksr.account.service.metchant.impl;

import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.zksr.account.client.MerchantClient;
import com.zksr.account.client.PayClient;
import com.zksr.account.client.hlb.HlbMerchantClient;
import com.zksr.account.client.mideapay.MideaPayMerchantClient;
import com.zksr.account.client.mock.MockMerchantClient;
import com.zksr.account.client.wx.WxB2bMerchantClient;
import com.zksr.account.convert.pay.PayConvert;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.metchant.MerchantChannelService;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisSysConfigService;
import com.zksr.system.api.model.dto.MideaPayConfig;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.HeLiBaoPayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.MideaPayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.sysconfig.SupplierPayConfigDTO;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

import static com.zksr.account.enums.ErrorCodeConstants.NOT_EXIST_PAY_CONFIG;
import static com.zksr.account.enums.ErrorCodeConstants.USER_APPID_NOT_EXIST;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.CacheUtils.buildAsyncReloadingCache;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户操作服务
 * @date 2024/4/23 14:09
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class MerchantChannelServiceImpl implements MerchantChannelService {

    /**
     * 支付平台商户操作
     * {@link MerchantClient} 缓存，30秒自动异步刷新
     */
    @Getter
    private final LoadingCache<MerchantClient.ClientParam, MerchantClient> payMerchantClient = buildAsyncReloadingCache(Duration.ofSeconds(30L),
            new CacheLoader<MerchantClient.ClientParam, MerchantClient>() {
                @Override
                public MerchantClient load(MerchantClient.ClientParam param) {
                    return getPayMerchantClient(param.getSysCode(), param.getPlatform());
                }
            }
    );

    /**
     * 储值平台商户操作
     * {@link MerchantClient} 缓存，30秒自动异步刷新
     */
    @Getter
    private final LoadingCache<MerchantClient.ClientParam, MerchantClient> storeMerchantClient = buildAsyncReloadingCache(Duration.ofSeconds(30L),
            new CacheLoader<MerchantClient.ClientParam, MerchantClient>() {
                @Override
                public MerchantClient load(MerchantClient.ClientParam param) {
                    return getPayMerchantClient(param.getSysCode(), param.getPlatform());
                }
            }
    );

    @Autowired
    private RedisSysConfigService redisSysConfigService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Resource
    private PartnerConfigApi partnerConfigApi;

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_MERCHANT_CLIENT, condition = "#sysCode", tryLock = true)
    public MerchantClient getPayMerchantClient(Long sysCode, String platform) {
        if (PayChannelEnum.MOCK.getCode().equals(platform)) {
            // 模拟支付直接返回
            throw new ServiceException("模拟支付不支持商户信息查询");
        }
        // 支付平台商户操作
        if(PayChannelEnum.MIDEA_PAY.getCode().equals(platform)){
            //美的付
            MideaPayConfigDTO respDTO = accountCacheService.getMideaPayConfig(sysCode);
            MideaPayConfig mideaPayConfig = PayConvert.INSTANCE.convert2MideaPayConfig(respDTO);
            if(null == mideaPayConfig || mideaPayConfig.checkEmpty()){
                throw new ServiceException("商户信息没有配置或缺失关键配置项");
            }
            MerchantClient merchantClient = new MideaPayMerchantClient(mideaPayConfig);
            return merchantClient;
        } else if (PayChannelEnum.HLB.getCode().equals(platform)) {
            //合利宝
            HeLiBaoPayConfigDTO payConfig = accountCacheService.getHeLiBaoConfig(sysCode);
            if (Objects.isNull(payConfig)) {
                throw exception(NOT_EXIST_PAY_CONFIG);
            }
            MerchantClient merchantClient = new HlbMerchantClient(payConfig.getPlatformMerchantNo(), payConfig.getPublicMerchantMd5SignKey(), payConfig.getPublicEncryptKey(), payConfig.getPayCallBackUrl());
            return merchantClient;
        } else if (PayChannelEnum.WX_B2B_PAY.getCode().equals(platform)) {
            //微信B2B
            AppletBaseConfigDTO appletBaseConfigDTO = accountCacheService.getAppletBaseConfigDTO(sysCode);
            if (Objects.isNull(appletBaseConfigDTO) || StringUtils.isEmpty(appletBaseConfigDTO.getAppId())) {
                throw exception(USER_APPID_NOT_EXIST);
            }
            MerchantClient merchantClient = new WxB2bMerchantClient(appletBaseConfigDTO.getAppId());
            return merchantClient;
        } else {
            throw new ServiceException(String.format("支付平台[%s]未配置,请确认配置.",platform));
        }
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_MERCHANT_CLIENT, condition = "#sysCode", tryLock = true)
    public MerchantClient getStoreMerchantClient(Long sysCode, String platform) {
        if (PayChannelEnum.MOCK.getCode().equals(platform)) {
            // 模拟支付直接返回
            throw new ServiceException("模拟支付不支持商户信息查询");
        }
        // 储值平台商户操作
        if(PayChannelEnum.MIDEA_PAY.getCode().equals(platform)){
            //美的付
            MideaPayConfig mideaPayConfig = redisSysConfigService.querySupplierConfig(MideaPayConfig.class);
            if(null == mideaPayConfig || mideaPayConfig.checkEmpty()){
                throw new ServiceException("商户信息没有配置或缺失关键配置项");
            }
            MerchantClient merchantClient = new MideaPayMerchantClient(mideaPayConfig);
            return merchantClient;
        } else if (PayChannelEnum.HLB.getCode().equals(platform)) {
            //合利宝
            HeLiBaoPayConfigDTO payConfig = accountCacheService.getHeLiBaoConfig(sysCode);
            //合利宝
            SupplierPayConfigDTO supplierConfig = redisSysConfigService.getSupplierConfig();
            MerchantClient merchantClient = new HlbMerchantClient(supplierConfig.getPlatformMerchantNo(), supplierConfig.getPublicMerchantMd5SignKey(), supplierConfig.getPublicEncryptKey(), payConfig.getPayCallBackUrl());
            return merchantClient;
        } else if (PayChannelEnum.WX_B2B_PAY.getCode().equals(platform)) {
            //微信B2B, 没有区分支付和储值体系
            AppletBaseConfigDTO appletBaseConfigDTO = accountCacheService.getAppletBaseConfigDTO(sysCode);
            MerchantClient merchantClient = new WxB2bMerchantClient(appletBaseConfigDTO.getAppId());
            return merchantClient;
        } else {
            throw new ServiceException(String.format("支付平台[%s]未配置,请确认配置.",platform));
        }

    }

    @Override
    public MerchantClient getMerchantClient(Long sysCode, PayTypeEnum payType) {
        PayConfigDTO payPlatformConfigDTO = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payPlatformConfigDTO)) {
            throw exception(NOT_EXIST_PAY_CONFIG);
        }
        if (payType == PayTypeEnum.PAY) {
            return payMerchantClient.getUnchecked(new MerchantClient.ClientParam(sysCode, payPlatformConfigDTO.getStoreOrderPayPlatform()));
        } else {
            return storeMerchantClient.getUnchecked(new MerchantClient.ClientParam(sysCode, payPlatformConfigDTO.getInteriorStoredPayPlatform()));
        }
    }

    @Override
    public MerchantClient getMerchantClient(Long sysCode, PayTypeEnum payType, String platform) {
        if (payType == PayTypeEnum.PAY) {
            return payMerchantClient.getUnchecked(new MerchantClient.ClientParam(sysCode, platform));
        } else {
            return storeMerchantClient.getUnchecked(new MerchantClient.ClientParam(sysCode, platform));
        }
    }
}
