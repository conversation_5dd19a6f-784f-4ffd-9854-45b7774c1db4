package com.zksr.account.service.transfer.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.transfer.dto.AccTransferBillOrderDTO;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderPageReqVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderRespVO;
import com.zksr.account.convert.transfer.AccTransferBillOrderConvert;
import com.zksr.account.domain.AccTransferBillOrder;
import com.zksr.account.mapper.AccTransferBillOrderMapper;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.account.service.transfer.*;
import com.zksr.common.core.domain.vo.openapi.OrderOpenDTO;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/10/30 14:55
 */
@Service
@Slf4j
public class AccTransferBillOrderServiceImpl implements IAccTransferBillOrderService {

    @Autowired
    private AccTransferBillOrderMapper accTransferBillOrderMapper;
    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private SupplierOrderApi supplierOrderApi;

    @Resource
    private SupplierApi supplierApi;

    @Resource
    private OrderApi orderApi;

    @Autowired
    private IAccountCacheService accountCacheService;


    @Override
    public void insertTransferBillOrder(List<AccTransferBillOrderDTO> accBillOrderDTO) {
        List<AccTransferBillOrder> accTransferBillOrders = accBillOrderDTO.stream()
                .map(dto -> HutoolBeanUtils.toBean(dto, AccTransferBillOrder.class))
                .collect(Collectors.toList());

        // 批量插入
        accTransferBillOrderMapper.insertBatch(accTransferBillOrders);
    }

    /**
     * 查询分页数据
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<AccTransferBillOrderRespVO> getAccTransferBillOrderPage(AccTransferBillOrderPageReqVO pageReqVO) {
        // 先执行分页查询，获取原始数据
//        Page<AccTransferBillOrder> page = new Page<>(pageReqVO.getPageNo(), pageReqVO.getPageSize());
        PageResult<AccTransferBillOrder> resultPage = accTransferBillOrderMapper.selectPage(pageReqVO);
        // 获取查询结果
        List<AccTransferBillOrder> records = resultPage.getList();
        if (ToolUtil.isEmpty(records) || records.isEmpty()) {
            return PageResult.empty();
        }
        Long sysCode = pageReqVO.getSysCode();
        // 根据传入的条件对查询结果进行过滤
        List<AccTransferBillOrder> filteredRecords = records.stream()
                .peek(record -> {
                    // 查询商户号对应的入驻商ID
                    if (ToolUtil.isNotEmpty(record.getAltNo())) {
                        // 根据商户号获取所有绑定的入驻商ID
                        List<Long> supplierIds = platformMerchantApi.getMerchantIdByAltNo(
                                MerchantTypeEnum.SUPPLIER.getType(),
                                record.getAltNo(),
                                sysCode
                        ).getCheckedData();  // 获取绑定的入驻商ID列表

                        // 如果有绑定的入驻商ID，继续处理
                        if (ToolUtil.isNotEmpty(supplierIds)) {
                            // 获取入驻商ID对应的名称
                            List<String> partnerNames = supplierApi.getSupplierNames(supplierIds).getCheckedData();  // 假设你有一个方法根据ID获取名称

                            // 将多个入驻商名称拼接成一个字符串
                            String joinedPartnerNames = String.join(",", partnerNames);
                            record.setSupplierName(joinedPartnerNames);
                        }

                        TrdOrder trdOrder =orderApi.getOrderByOrderNo(record.getPlatformTradeNo()).getCheckedData();
                        if(ToolUtil.isNotEmpty(trdOrder)){
                            BranchDTO branchDTO =accountCacheService.getBranchDTO(trdOrder.getBranchId());
                            if(ToolUtil.isNotEmpty(branchDTO)){
                                record.setBranchName(branchDTO.getBranchName());
                            }
                        }
                    }
                })
                .filter(record -> {
                    boolean match = true;

                    // 如果传入了 supplierId，过滤商户号
                    if (ToolUtil.isNotEmpty(pageReqVO.getSupplierId())) {
                        PlatformMerchantDTO platformMerchantDTO = platformMerchantApi.getPlatformMerchant(MerchantTypeEnum.SUPPLIER.getType(), pageReqVO.getSupplierId(), sysCode).getCheckedData();// 根据supplierId获取商户号
                        if(ToolUtil.isNotEmpty(platformMerchantDTO)){
                            match &= platformMerchantDTO.getAltMchNo().equals(record.getAltNo());
                        }
                    }

                    return match;
                })
                .collect(Collectors.toList());

        // 封装返回结果
        PageResult<AccTransferBillOrder> result = new PageResult<>();
        result.setTotal(resultPage.getTotal());
        result.setList(filteredRecords);

        return AccTransferBillOrderConvert.INSTANCE.convertPage(result);
    }

    @Override
    public List<String> countTransferBillsByDateAndAltNo(String dataFormatted, String altNo) {
        return accTransferBillOrderMapper.countTransferBillsByDateAndAltNo(dataFormatted,altNo);
    }

    @Override
    @Transactional
    public void deleteTransferBillsByDateAndAltNo(String dataFormatted, String altNo) {
         accTransferBillOrderMapper.deleteTransferBillsByDateAndAltNo(dataFormatted,altNo);
    }
}
