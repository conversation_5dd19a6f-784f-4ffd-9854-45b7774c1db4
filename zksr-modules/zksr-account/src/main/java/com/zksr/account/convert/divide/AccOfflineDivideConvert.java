package com.zksr.account.convert.divide;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccOfflineDivide;
import com.zksr.account.controller.divide.vo.AccOfflineDivideRespVO;
import com.zksr.account.controller.divide.vo.AccOfflineDivideSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 线下分账处理 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-08-16
*/
@Mapper
public interface AccOfflineDivideConvert {

    AccOfflineDivideConvert INSTANCE = Mappers.getMapper(AccOfflineDivideConvert.class);

    AccOfflineDivideRespVO convert(AccOfflineDivide accOfflineDivide);

    AccOfflineDivide convert(AccOfflineDivideSaveReqVO accOfflineDivideSaveReq);

    PageResult<AccOfflineDivideRespVO> convertPage(PageResult<AccOfflineDivide> accOfflineDividePage);
}