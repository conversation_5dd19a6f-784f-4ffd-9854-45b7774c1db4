package com.zksr.account.service;

import com.zksr.account.api.withdraw.dto.AccWithdrawBillDTO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawBillPageReqVO;
import com.zksr.account.domain.AccWithdrawBill;
import com.zksr.common.core.web.pojo.PageResult;
import java.util.List;

/**
 * 提现对账单Service接口
 *
 * <AUTHOR>
 * @date 2024-11-4
 */
public interface IAccWithdrawBillService {
    /**
     * 批量插入提现单
     * @param accWithdrawBillDTO
     */
    void insertAccWithdrawBillBatch(List<AccWithdrawBillDTO> accWithdrawBillDTO);

    /**
     * 获得提现对账单分页
     *
     * @param pageReqVO 分页查询
     * @return 提现对账单分页
     */
    PageResult<AccWithdrawBill> getAccWithdrawBillPage(AccWithdrawBillPageReqVO pageReqVO);

    int countWithdrawBillsByDateAndAltNo(String dataFormatted, String altNo);

    void deleteWithdrawBillsByDateAndAltNo(String dataFormatted, String altNo);
}
