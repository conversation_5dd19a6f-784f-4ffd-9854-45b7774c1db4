package com.zksr.account.controller.pay;

import javax.validation.Valid;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccPay;
import com.zksr.account.service.IAccPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.controller.pay.vo.AccPayPageReqVO;
import com.zksr.account.controller.pay.vo.AccPaySaveReqVO;
import com.zksr.account.controller.pay.vo.AccPayRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 账户付款单Controller
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@Api(tags = "管理后台 - 账户付款单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/pay")
public class AccPayController {
    @Autowired
    private IAccPayService accPayService;

    /**
     * 新增账户付款单
     */
    @ApiOperation(value = "新增账户付款单", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "账户付款单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccPaySaveReqVO createReqVO) {
        return success(accPayService.insertAccPay(createReqVO));
    }

    /**
     * 修改账户付款单
     */
    @ApiOperation(value = "修改账户付款单", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "账户付款单", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AccPaySaveReqVO updateReqVO) {
        accPayService.updateAccPay(updateReqVO);
        return success(true);
    }

    /**
     * 删除账户付款单
     */
    @ApiOperation(value = "删除账户付款单", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "账户付款单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{payIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] payIds) {
        accPayService.deleteAccPayByPayIds(payIds);
        return success(true);
    }

    /**
     * 获取账户付款单详细信息
     */
    @ApiOperation(value = "获得账户付款单详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{payId}")
    public CommonResult<AccPayRespVO> getInfo(@PathVariable("payId") Long payId) {
        AccPay accPay = accPayService.getAccPay(payId);
        return success(HutoolBeanUtils.toBean(accPay, AccPayRespVO.class));
    }

    /**
     * 分页查询账户付款单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得账户付款单分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccPayRespVO>> getPage(@Valid AccPayPageReqVO pageReqVO) {
        PageResult<AccPay> pageResult = accPayService.getAccPayPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, AccPayRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "account:pay:add";
        /**
         * 编辑
         */
        public static final String EDIT = "account:pay:edit";
        /**
         * 删除
         */
        public static final String DELETE = "account:pay:remove";
        /**
         * 列表
         */
        public static final String LIST = "account:pay:list";
        /**
         * 查询
         */
        public static final String GET = "account:pay:query";
        /**
         * 停用
         */
        public static final String DISABLE = "account:pay:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "account:pay:enable";
    }
}
