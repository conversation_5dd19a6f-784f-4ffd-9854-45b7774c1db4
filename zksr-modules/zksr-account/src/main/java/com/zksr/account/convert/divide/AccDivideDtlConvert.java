package com.zksr.account.convert.divide;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.account.model.pay.vo.CreateDivideReqVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.controller.divide.vo.AccDivideDtlRespVO;
import com.zksr.account.controller.divide.vo.AccDivideDtlSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 支付分账详情 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-08-16
*/
@Mapper
public interface AccDivideDtlConvert {

    AccDivideDtlConvert INSTANCE = Mappers.getMapper(AccDivideDtlConvert.class);

    AccDivideDtlRespVO convert(AccDivideDtl accDivideDtl);

    AccDivideDtl convert(AccDivideDtlSaveReqVO accDivideDtlSaveReq);

    PageResult<AccDivideDtlRespVO> convertPage(PageResult<AccDivideDtl> accDivideDtlPage);

    List<AccDivideDtlRespVO> convertRespVOList(List<AccDivideDtl> accDivideDtls);

    AccDivideDtl convertDivideReqVO(CreateDivideReqVO divideReqVO);

}