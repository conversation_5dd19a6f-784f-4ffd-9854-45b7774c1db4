package com.zksr.account.service.pay;


import com.zksr.account.model.pay.dto.order.PayOrderNotifyRespDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderQueryVO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundQueryVO;

/**
 * <AUTHOR>
 * @time 2024/3/7
 * @desc 公共支付, 退款, 处理
 */
public interface PayOrderService {

    /**
     * 提交订单支付
     * @param reqVO
     * @return
     */
    PayOrderRespDTO submitOrder(PayOrderSubmitReqVO reqVO);


    /**
     * 创建退款申请
     * @param reqDTO 退款申请信息
     * @return 退款单号
     */
    PayRefundRespDTO createPayRefund(PayRefundOrderSubmitReqVO reqDTO);


    /**
     * 通知订单支付回调
     * @param notify
     * @param orderType 订单类型 {@link PayOrderSubmitReqVO orderType}
     * @return PayOrderNotifyRespDTO 回调处理结果
     */
    PayOrderNotifyRespDTO notifyOrder(PayOrderRespDTO notify, Integer orderType);

    /**
     * 通知订单支付回调 再回调
     * @param payOrderNotifyRespDTO
     * @param orderType 订单类型 {@link PayOrderSubmitReqVO orderType}
     * @return PayOrderNotifyRespDTO 回调处理结果
     */
    void notifyOrderSuccess(PayOrderNotifyRespDTO payOrderNotifyRespDTO, Integer orderType);

    /**
     * 通知订单退款回调
     * @param notify
     * @param orderType 订单类型 {@link PayOrderSubmitReqVO orderType}
     */
    void notifyRefund(PayRefundRespDTO notify, Integer orderType);

    /**
     * 处理延迟订单退款查询, 一般是无退款回调使用, 需要手动进行查询行为
     * @param refundQueryVO 查询参数
     */
    void processDelayRefundQuery(PayRefundQueryVO refundQueryVO);

    /**
     * 查询订单支付状态
     * @param payOrderQueryVO   查询参数
     * @return  支付转提供
     */
    PayOrderRespDTO queryOrder(PayOrderQueryVO payOrderQueryVO);
}
