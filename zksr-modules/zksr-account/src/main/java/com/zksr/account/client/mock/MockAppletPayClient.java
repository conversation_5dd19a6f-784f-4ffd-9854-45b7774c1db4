package com.zksr.account.client.mock;

import cn.hutool.core.util.RandomUtil;
import com.zksr.account.client.PayClient;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayOrderStatusRespEnum;
import com.zksr.common.core.enums.PayRefundStatusEnum;
import com.zksr.system.api.model.dto.PayConfigDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 模拟支付
 * @date 2024/3/10 11:14
 */
@Service
public class MockAppletPayClient implements PayClient {

    @Override
    public String getChannel() {
        return PayChannelEnum.MOCK.getCode();
    }

    @Override
    public String getPlatform() {
        return PayChannelEnum.MOCK.getCode();
    }

    @Override
    public void setConfig(PayConfigDTO config, String appid) throws Exception {}

    @Override
    public PayOrderRespDTO unifiedOrder(PayOrderDTO payOrderDTO) {
        // 模拟支付虚假支付
        // 模拟支付是钱包支付的一种, 只是不校验余额
        PayOrderRespDTO orderRespDTO = new PayOrderRespDTO();
        orderRespDTO.setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                .setSuccessTime(new Date())
                .setOrderNo(payOrderDTO.getOrderNo())
                .setOutTradeNo(RandomUtil.randomNumbers(18));
        return orderRespDTO;
    }

    @Override
    public PayOrderRespDTO parseOrderNotify(Map<String, String> params, String body) {
        // 钱包支付无需实现
        return null;
    }

    @Override
    public PayRefundRespDTO unifiedRefund(PayRefundOrderSubmitReqVO reqDTO) {
        // 处理钱包支付退款
        PayRefundRespDTO refundRespDTO = new PayRefundRespDTO();
        refundRespDTO.setStatus(PayRefundStatusEnum.SUCCESS.getStatus())
                .setRefundNo(reqDTO.getRefundNo())
                .setOrderNo(reqDTO.getOrderNo())
                .setOutRefundNo(RandomUtil.randomNumbers(18));
        return refundRespDTO;
    }

    @Override
    public PayRefundRespDTO parseRefundNotify(Map<String, String> params, String body) {
        // 钱包支付无需实现
        PayRefundRespDTO result = new PayRefundRespDTO();
        return result;
    }

    @Override
    public BigDecimal getFeeRate() {
        // 返回模拟支付费率
        return BigDecimal.ZERO;
    }
}
