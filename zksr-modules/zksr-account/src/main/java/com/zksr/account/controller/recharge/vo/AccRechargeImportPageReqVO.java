package com.zksr.account.controller.recharge.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 后台导入充值对象 acc_recharge_import
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@ApiModel("后台导入充值 - acc_recharge_import分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccRechargeImportPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 充值批次ID */
    @ApiModelProperty(value = "充值批次ID")
    private Long rechargeImportId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 审核人 */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人")
    private String auditBy;

    /** 0-初始化, 1-审核成功, 2-作废 */
    @Excel(name = "0-初始化, 1-审核成功, 2-作废")
    @ApiModelProperty(value = "0-初始化, 1-审核成功, 2-作废")
    private Integer rechargeImportState;

    /** 申请开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "申请开时间")
    private Date startTime;

    /** 申请开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "申请结束时间")
    private Date endTime;

    /** 审核开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "审核开始时间")
    private Date auditTime;

    /** 审核结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "审核结束时间")
    private Date auditEndTime;

    @ApiModelProperty(value = "门店ID")
    private Long branchId;
}
