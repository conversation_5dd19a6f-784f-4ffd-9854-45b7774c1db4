package com.zksr.account.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.domain.vo.openapi.BranchValueInfoOpenDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.controller.flow.vo.AccAccountFlowPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 账户流水Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Mapper
@SuppressWarnings("all")
public interface AccAccountFlowMapper extends BaseMapperX<AccAccountFlow> {
    default PageResult<AccAccountFlow> selectPage(AccAccountFlowPageReqVO reqVO) {
        LambdaQueryWrapper<AccAccountFlow> queryWrapper = new LambdaQueryWrapperX<AccAccountFlow>()
                .eqIfPresent(AccAccountFlow::getAccountFlowId, reqVO.getAccountFlowId())
                .eqIfPresent(AccAccountFlow::getSysCode, reqVO.getSysCode())
                .eqIfPresent(AccAccountFlow::getAccountId, reqVO.getAccountId())
                .eqIfPresent(AccAccountFlow::getMerchantId, reqVO.getMerchantId())
                .eqIfPresent(AccAccountFlow::getMerchantType, reqVO.getMerchantType())
                .eqIfPresent(AccAccountFlow::getBusiType, reqVO.getBusiType())
                .eqIfPresent(AccAccountFlow::getBusiId, reqVO.getBusiId())
                .eqIfPresent(AccAccountFlow::getBusiFields, reqVO.getBusiFields())
                .eqIfPresent(AccAccountFlow::getProcessFlag, reqVO.getProcessFlag())
                .eqIfPresent(AccAccountFlow::getPlatform, reqVO.getPlatform())
                .orderByDesc(
                        AccAccountFlow::getProcessTime,
                        AccAccountFlow::getAccountFlowId
                );
        // 查询指定流水处理时间
        if (Objects.nonNull(reqVO.getProcessStartTime())) {
            queryWrapper.between(AccAccountFlow::getProcessTime, reqVO.getProcessStartTime(), reqVO.getProcessEndTime());
        }
        // 查询指定商户变动类型
        if (StringUtils.isNotEmpty(reqVO.getBusiFields())) {
            AccountBusiTypeField busiTypeField = AccountBusiTypeField.fromValue(reqVO.getBusiFields());
            if (StringUtils.isNotEmpty(busiTypeField.getField())) {
                queryWrapper.apply(StringUtils.format(" FIND_IN_SET('{}', busi_fields) ", busiTypeField.getField()));
            }
        }
        // 查询是支出还是收入
        if (Objects.nonNull(reqVO.getIoType())) {
            if (reqVO.getIoType() == NumberPool.INT_ZERO) {
                queryWrapper.gt(AccAccountFlow::getBusiWithdrawableAmt, NumberPool.INT_ZERO);
            } else {
                queryWrapper.lt(AccAccountFlow::getBusiWithdrawableAmt, NumberPool.INT_ZERO);
            }
        }
        return selectPage(reqVO, queryWrapper);
    }

    default List<AccAccountFlow> selectAccAccountFlow(Long busiId, String busitype) {
        return selectList(
                Wrappers.lambdaQuery(AccAccountFlow.class)
                        .eq(AccAccountFlow::getBusiId, busiId)
                        .eq(AccAccountFlow::getBusiType, busitype)
        );
    }

    default List<AccAccountFlow> selectAccAccountFlowByBusiNo(String busiNo, String busitype) {
        return selectList(
                Wrappers.lambdaQuery(AccAccountFlow.class)
                        .eq(AccAccountFlow::getBusiNo, busiNo)
                        .eq(AccAccountFlow::getBusiType, busitype)
        );
    }

    default List<AccAccountFlow> selectTrySettleFlow(Long minId) {
        return selectList(
                Wrappers.lambdaQuery(AccAccountFlow.class)
                        .gt(AccAccountFlow::getAccountFlowId, minId)
                        // 5分钟前
                        .lt(AccAccountFlow::getCreateTime, new Date(System.currentTimeMillis() - 60L * 5L * 1000L))
                        .eq(AccAccountFlow::getProcessFlag, NumberPool.INT_ZERO)
                        .select(AccAccountFlow::getAccountFlowId, AccAccountFlow::getCreateTime, AccAccountFlow::getProcessFlag)
                        .last("LIMIT 500")
                        .orderByAsc(AccAccountFlow::getAccountFlowId)
        );
    }

    List<RechargeConsumeRespVO> selectBranchAccountFlowPage(AccAccountFlowPageReqVO pageReqVO);

    BranchValueInfoOpenDTO getBranchValueInfoByIds(@Param("accountFlowIds")List<Long> accountFlowIds);
}
