package com.zksr.account.domain;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.*;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.order.PayInfoDTO;
import com.zksr.common.core.enums.PayRefundStatusEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import lombok.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 支付流水对象 acc_pay_flow
 *
 * <AUTHOR>
 * @date 2024-03-10
 */
@TableName(value = "acc_pay_flow")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccPayFlow extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 支付流水id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long payFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 支付类型 0-支付 1-退款 */
    @Excel(name = "支付类型 0-支付 1-退款")
    private Integer payType;

    /** 支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号 */
    @Excel(name = "支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号")
    private String tradeNo;

    /** 支付平台商户订单号 */
    @Excel(name = "支付平台商户订单号")
    private String outTradeNo;

    /** 退款单号;商城退款即是售后单号 */
    @Excel(name = "退款单号;商城退款即是售后单号")
    private String refundNo;

    /** 支付平台商户退款单号 */
    @Excel(name = "支付平台商户退款单号")
    private String outRefundNo;

    /** 支付平台流水号;一般回调会返回 */
    @Excel(name = "支付平台流水号;一般回调会返回")
    private String outFlowNo;

    /** 支付方式;0-在线支付 1-储值支付 */
    @Excel(name = "支付方式;0-在线支付 1-储值支付")
    private String payWay;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

    /** 回调标识 0-未回调 1-已回调 */
    @Excel(name = "回调标识 0-未回调 1-已回调")
    private Integer callbackFlag;

    /** 回调时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "回调时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date callbackTime;

    /** 退款状态,0-未退款,1-发起退款成功,2-退款成功,3-退款失败,4-退款中*/
    @Excel(name = "退款状态,0-未退款,1-发起退款成功,2-退款成功,3-退款失败,4-退款中")
    private Integer refundStatus;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmt;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmt;

    /** 手续费 */
    @Excel(name = "手续费")
    private BigDecimal fee;

    /** 订单类型 0-商城订单  1-入驻商充值  2-门店充值 */
    @Excel(name = "订单类型 0-商城订单  1-入驻商充值  2-门店充值")
    private Integer orderType;

    /** 是否分账  0-否 1-是 */
    @Excel(name = "是否分账  0-否 1-是")
    private Integer isDivide;

    @Excel(name = "支付appid")
    private String appid;

    /**
     * 0-订单号支付单号合一, 1-支付流水唯一
     */
    @Excel(name = "0-订单号支付单号合一, 1-支付流水唯一")
    private Integer splitType;

    /**
     * 结算信息, JSON Array {@link com.zksr.account.model.pay.dto.OrderSettlementDTO}
     */
    @Excel(name = "结算信息")
    private String settles;

    /**
     * 支付请求信息 {@link PayInfoDTO}
     */
    @Excel(name = "支付请求信息")
    private String reqInfo;

    public List<OrderSettlementDTO> buildSettle() {
        if (StringUtils.isEmpty(settles)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(settles, OrderSettlementDTO.class);
    }

    public void setSettles(String settles) {
        this.settles = settles;
    }

    public AccPayFlow setSettles(List<OrderSettlementDTO> settles) {
        if (Objects.isNull(settles)) {
            this.settles = StringPool.EMPTY;
            return this;
        }
        this.settles = JSON.toJSONString(settles);
        return this;
    }

    /**
     * 是否使用的是订单号, 进行调用支付
     * @return
     */
    public boolean getUseOrderNo() {
        return Objects.isNull(splitType) || splitType == 0;
    }
}
