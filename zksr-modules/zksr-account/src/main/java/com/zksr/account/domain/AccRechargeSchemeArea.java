package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 储值套餐上架发布城市对象 acc_recharge_scheme_area
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@TableName(value = "acc_recharge_scheme_area")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccRechargeSchemeArea extends BaseEntity{
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long rechargeSchemeAreaId;

    /** 充值方案id */
    @Excel(name = "充值方案id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeSchemeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 发布城市ID */
    @Excel(name = "发布城市ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

}
