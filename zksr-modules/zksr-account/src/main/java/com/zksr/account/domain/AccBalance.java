package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 门店账户余额
 */
@TableName(value = "acc_balance")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccBalance extends BaseEntity {

    private static final long serialVersionUID = -7915178322827134749L;

    /**
     * 门店账户余额id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long balanceId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /**
     * 门店id
     */
    @Excel(name = "门店id")
    private Long branchId;

    /**
     * 门店余额账户金额
     */
    @Excel(name = "账户金额")
    private BigDecimal amt;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    @Version
    private Long version;

    /**
     * 适用供应商id，只能单选
     */
    @Excel(name = "版本号")
    private Long supplierId;

}
