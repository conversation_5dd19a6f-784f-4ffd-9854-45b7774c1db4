package com.zksr.account.service.impl;

import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.balance.vo.AccBalanceRespVO;
import com.zksr.account.domain.AccBalance;
import com.zksr.account.enums.balance.ActionTypeEnum;
import com.zksr.account.mapper.AccBalanceMapper;
import com.zksr.account.service.IAccBalanceFlowService;
import com.zksr.account.service.IAccBalanceService;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class AccBalanceServiceImpl implements IAccBalanceService {

    @Autowired
    private AccBalanceMapper accBalanceMapper;

    @Autowired
    private IAccBalanceFlowService accBalanceFlowService;

    @Override
    public List<AccBalanceRespVO> getBalanceInfoList(List<Long> branchIdList) {
        if (ToolUtil.isEmpty(branchIdList)) {
            return Collections.emptyList();
        }
        List<AccBalance> balanceList = accBalanceMapper.selectBalanceByBranchIdList(branchIdList);
        return HutoolBeanUtils.toBean(balanceList, AccBalanceRespVO.class);
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_BALANCE_PAY, condition = "#branchId", tryLock = true, waitTime = 15L)
    public String recharge(MemBranchBalanceDTO balanceDTO) {
        AccBalance accBalance = accBalanceMapper.selectAccBalanceByBranchId(balanceDTO.getSysCode(), balanceDTO.getBranchId());
        BigDecimal beforeAmt;
        Date now = new Date();
        if (Objects.isNull(accBalance)) {
            accBalance = new AccBalance();
            accBalance.setSysCode(balanceDTO.getSysCode()).setAmt(balanceDTO.getOpAmt()).setBranchId(balanceDTO.getBranchId());
            accBalance.setCreateBy(balanceDTO.getOperUserName());
            accBalance.setCreateTime(now);
            accBalance.setUpdateBy(balanceDTO.getOperUserName());
            accBalance.setUpdateTime(now);
            accBalanceMapper.insert(accBalance);
            balanceDTO.setBeforeAmt(BigDecimal.ZERO);
            balanceDTO.setAfterAmt(accBalance.getAmt());
        } else {
            balanceDTO.setBeforeAmt(accBalance.getAmt());
            accBalance.setAmt(accBalance.getAmt().add(balanceDTO.getOpAmt()));
            accBalance.setUpdateBy(balanceDTO.getOperUserName());
            accBalance.setUpdateTime(now);
            accBalanceMapper.updateById(accBalance);
            balanceDTO.setAfterAmt(accBalance.getAmt());
        }
        // 保存充值交易流水
        Long flowId = accBalanceFlowService.saveFlow(accBalance, balanceDTO, ActionTypeEnum.RECHARGE);
        return String.valueOf(flowId);
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_BALANCE_PAY, condition = "#branchId", tryLock = true, waitTime = 15L)
    public String refund(MemBranchBalanceDTO balanceDTO) {
        AccBalance accBalance = accBalanceMapper.selectAccBalanceByBranchId(balanceDTO.getSysCode(), balanceDTO.getBranchId());
        balanceDTO.setBeforeAmt(accBalance.getAmt());
        accBalance.setAmt(accBalance.getAmt().subtract(balanceDTO.getOpAmt()));
        accBalance.setUpdateBy(balanceDTO.getOperUserName());
        accBalance.setUpdateTime(new Date());
        accBalanceMapper.updateById(accBalance);
        balanceDTO.setAfterAmt(accBalance.getAmt());
        // 保存充值交易流水
        Long flowId = accBalanceFlowService.saveFlow(accBalance, balanceDTO, ActionTypeEnum.REFUND);
        return String.valueOf(flowId);
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_BALANCE_PAY, condition = "#branchId", tryLock = true, waitTime = 15L)
    public String orderPay(MemBranchBalanceDTO balanceDTO) {
        AccBalance accBalance = accBalanceMapper.selectAccBalanceByBranchId(balanceDTO.getSysCode(), balanceDTO.getBranchId());
        balanceDTO.setBeforeAmt(accBalance.getAmt());
        accBalance.setAmt(accBalance.getAmt().subtract(balanceDTO.getOpAmt()));
        accBalance.setUpdateBy(balanceDTO.getOperUserName());
        accBalance.setUpdateTime(new Date());
        accBalanceMapper.updateById(accBalance);
        balanceDTO.setAfterAmt(accBalance.getAmt());
        // 保存充值交易流水
        Long flowId = accBalanceFlowService.saveFlow(accBalance, balanceDTO, ActionTypeEnum.ORDER_PAY);
        return String.valueOf(flowId);
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_BALANCE_PAY, condition = "#branchId", tryLock = true, waitTime = 15L)
    public Boolean rollBackBalance(MemBranchBalanceDTO balanceDTO) {
        AccBalance accBalance = accBalanceMapper.selectAccBalanceByBranchId(balanceDTO.getSysCode(), balanceDTO.getBranchId());
        // 金额已做处理，只做相加就行
        accBalance.setAmt(accBalance.getAmt().add(balanceDTO.getOpAmt()));
        return accBalanceMapper.updateById(accBalance) > 0;
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_BALANCE_PAY, condition = "#branchId", tryLock = true, waitTime = 15L)
    public String OrderPayCancel(MemBranchBalanceDTO balanceDTO) {
        AccBalance accBalance = accBalanceMapper.selectAccBalanceByBranchId(balanceDTO.getSysCode(), balanceDTO.getBranchId());
        balanceDTO.setBeforeAmt(accBalance.getAmt());
        accBalance.setAmt(accBalance.getAmt().add(balanceDTO.getOpAmt()));
        accBalance.setUpdateBy(balanceDTO.getOperUserName());
        accBalance.setUpdateTime(new Date());
        accBalanceMapper.updateById(accBalance);
        balanceDTO.setAfterAmt(accBalance.getAmt());
        // 保存充值交易流水
        Long flowId = accBalanceFlowService.saveFlow(accBalance, balanceDTO, ActionTypeEnum.ORDER_CANCEL);
        return String.valueOf(flowId);
    }

}
