package com.zksr.account.service.transfer;

import com.zksr.account.client.TransferClient;

/**
 * <AUTHOR>
 * @time 2024/3/11
 * @desc
 */
public interface  TransferChannelService {

    /**
     * 获取交易客户端
     * @param sysCode   平台商编号
     * @param platform  校验平台标识 {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    TransferClient getTransferClient(Long sysCode, String platform);

    /**
     * 获取交易客户端
     * @param sysCode       平台商编号
     * @param platform      校验平台标识 {@link com.zksr.common.core.enums.PayChannelEnum}
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @return
     */
    TransferClient getTransferClient(Long sysCode, String platform, String merchantType);
}
