package com.zksr.account.convert.recharge;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.account.api.recharge.dto.RechargeSchemeDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccRechargeScheme;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemeRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSchemeSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 储值充值套餐配置 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2025-02-11
*/
@Mapper
public interface AccRechargeSchemeConvert {

    AccRechargeSchemeConvert INSTANCE = Mappers.getMapper(AccRechargeSchemeConvert.class);

    @Mappings({
            @Mapping(expression = "java(accRechargeScheme.getRuleJson() != null ? com.alibaba.fastjson2.JSON.parseArray(accRechargeScheme.getRuleJson(), com.zksr.account.api.recharge.vo.RechargeSchemeContentVO.class) : null)", target = "ruleJson")
    })
    AccRechargeSchemeRespVO convert(AccRechargeScheme accRechargeScheme);

    @Mappings({
            @Mapping(expression = "java(saveReqVO.getRuleJson() != null ? com.alibaba.fastjson2.JSON.toJSONString(saveReqVO.getRuleJson()) : null)", target = "ruleJson")
    })
    AccRechargeScheme convert(AccRechargeSchemeSaveReqVO saveReqVO);

    PageResult<AccRechargeSchemeRespVO> convertPage(PageResult<AccRechargeScheme> accRechargeSchemePage);

    List<AccRechargeSchemeRespVO> convertRespVOList(List<AccRechargeScheme> accRechargeSchemes);


    @Mappings({
            @Mapping(expression = "java(accRechargeScheme.getRuleJson() != null ? com.alibaba.fastjson2.JSON.parseArray(accRechargeScheme.getRuleJson(), com.zksr.account.api.recharge.vo.RechargeSchemeContentVO.class) : null)", target = "rules")
    })
    RechargeSchemeDTO convertDTO(AccRechargeScheme accRechargeScheme);

    List<RechargeSchemeDTO> convertDTOList(List<AccRechargeScheme> accRechargeSchemes);

}