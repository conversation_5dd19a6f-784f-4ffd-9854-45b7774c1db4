package com.zksr.account.mq;

import com.alibaba.fastjson.JSON;
import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.client.PayClient;
import com.zksr.account.convert.divideFlow.AccDivideFlowConvert;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccDivideFlow;
import com.zksr.account.domain.AccTransfer;
import com.zksr.account.model.pay.dto.order.PayOrderNotifyRespDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundQueryVO;
import com.zksr.account.model.pay.vo.PayWxB2bCreateDivideReqVO;
import com.zksr.account.service.IAccAccountFlowService;
import com.zksr.account.service.IAccDivideFlowService;
import com.zksr.account.service.IAccTransferService;
import com.zksr.account.service.pay.*;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayRefundStatusEnum;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/4/1 10:26
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class AccountMqConsumer {
    @Autowired
    private StreamBridge streamBridge;

    @Autowired
    private RedisService redisService;

    @Autowired
    private PayOrderService payOrderService;

    @Autowired
    private IAccTransferService accTransferService;

    @Autowired
    private IAccDivideFlowService divideFlowService;

    @Autowired
    private PayWxB2bDivideOrderService payWxB2bOrderService;

    @Autowired
    private DivideOrderService divideOrderService;

    /**
     * 结算流水
     * 来源 {@link AccountMqProducer#sendSettleFlow(AccAccountFlow)}
     * @return
     */
    @Bean
    public Consumer<Message<AccAccountFlow>> accountFlow() {
        return (data) -> {
            AccAccountFlow accountFlow = data.getPayload();
            log.info("acceptSettleFlow收到消息 : {}", JSON.toJSONString(accountFlow));
            try {
                SpringUtils.getBean(IAccAccountFlowService.class).processFlow(accountFlow);
            } catch (Exception e) {
                log.error("结算处理流水异常", e);
            }
        };
    }

    /**
     * 支付回调
     * 来源 {@link AccountMqProducer#sendPayNotify(PayOrderRespDTO)}
     * @return
     */
    @Bean
    public Consumer<Message<PayOrderRespDTO>> payNotify() {
        return (data) -> {
            PayOrderRespDTO notify = data.getPayload();
            log.info("支付回调payNotify收到消息 : {}", JSON.toJSONString(notify));
            PayOrderNotifyRespDTO respDTO = payOrderService.notifyOrder(notify, notify.getOrderType());
            log.info("支付回调处理结果 : {}", JSON.toJSONString(respDTO));
            payOrderService.notifyOrderSuccess(respDTO, notify.getOrderType());
        };
    }

    /**
     * 退款回调
     * 来源 {@link AccountMqProducer#sendRefundNotify(PayRefundRespDTO)}
     * @return
     */
    @Bean
    public Consumer<Message<PayRefundRespDTO>> refundNotify() {
        return (data) -> {
            PayRefundRespDTO notify = data.getPayload();
            log.info("退款回调refundNotify收到消息 : {}", JSON.toJSONString(notify));
            if (StringUtils.isEmpty(notify.getRefundNo())) {
                log.error("无效退款数据");
                return;
            }
            payOrderService.notifyRefund(notify, notify.getOrderType());
        };
    }

    /**
     * 处理转账
     * 来源 {@link AccountMqProducer#sendAccountTransfer(AccTransfer)}
     * @return
     */
    @Bean
    public Consumer<Message<AccTransfer>> accountTransfer() {
        return (data) -> {
            AccTransfer transfer = data.getPayload();
            log.info("accountTransfer收到消息 : {}", JSON.toJSONString(transfer));
            accTransferService.processTransfer(transfer);
        };
    }

    /**
     * 退款延迟查询结果
     * 来源 {@link AccountMqProducer#sendRefundDelayQuery(PayRefundQueryVO)}
     * @return
     */
    @Bean
    public Consumer<Message<PayRefundQueryVO>> refundDelayQuery() {
        return (data) -> {
            PayRefundQueryVO notify = data.getPayload();
            log.info("退款回调refundDelayQuery收到消息 : {}", JSON.toJSONString(notify));
            if (StringUtils.isEmpty(notify.getRefundNo())) {
                log.error("无效退款数据");
                return;
            }
            payOrderService.processDelayRefundQuery(notify);
        };
    }

    /**
     * 分账延迟查询结果
     * 来源 {@link AccountMqProducer#sendDivideDelayQuery(Long)}
     * @return
     */
    @Bean
    public Consumer<Message<Long>> divideDelayQuery() {
        return (data) -> {
            Long divideFlowId = data.getPayload();
            AccDivideFlow divideFlow = divideFlowService.getAccDivideFlow(divideFlowId);
            // 通过订单号获取匹配的分账处理服务
            IPayDivideOrderService payDivideOrderService =  divideOrderService.getDivideOrderService(PayChannelEnum.fromValue(divideFlow.getPlatform()));
            if (Objects.nonNull(payDivideOrderService)) {
                payDivideOrderService.updateDivideStatus(AccDivideFlowConvert.INSTANCE.convertDTO(divideFlow));
                payDivideOrderService.divideCheckOver(new PayWxB2bCreateDivideReqVO(divideFlow.getTradeNo(), divideFlow.getTargetPlatformMerchantId(), divideFlow.getPayeeType()));
            }
        };
    }


    @Autowired
    private PayChannelService payChannelService;

    @Autowired
    private AccountMqProducer accountMqProducer;

    /**
     * 分账延迟查询结果
     * 来源 {@link AccountMqProducer#sendWxOrderDivideDelayQuery(PayRefundOrderSubmitReqVO)}
     * @return
     */
    @Bean
    public Consumer<Message<PayRefundOrderSubmitReqVO>> wxOrderDivideDelayQuery() {
        return (data) -> {
            PayRefundOrderSubmitReqVO reqDTO = data.getPayload();
            PayFlowDTO payFlow = reqDTO.getPayFlowDTO();
            // 根据平台 + appid 获取支付配置
            // 获取支付client
            PayClient payClient = payChannelService.getPayClient(payFlow.getAppid(), payFlow.getSysCode(), payFlow.getPayWay(), payFlow.getOrderType(), payFlow.getPlatform());
            // 发起退款
            PayRefundRespDTO refundRespDTO = payClient.unifiedRefund(reqDTO);
            refundRespDTO.setOrderType(reqDTO.getOrderType());
            log.info("发起退款结果,req={}", JSON.toJSONString(refundRespDTO));
            // 消息通知
            if (PayRefundStatusEnum.isFailure(refundRespDTO.getStatus()) || PayRefundStatusEnum.isSuccess(refundRespDTO.getStatus())) {
                // 失败也直接通知
                // 处理退款结果, 如果是成功就直接回调, 兼容模拟支付, 钱包付款
                accountMqProducer.sendRefundNotify(refundRespDTO);
            }
        };
    }
}
