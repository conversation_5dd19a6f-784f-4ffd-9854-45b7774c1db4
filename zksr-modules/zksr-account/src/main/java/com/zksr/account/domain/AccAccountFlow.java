package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.enums.MerchantTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 账户流水对象 acc_account_flow
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@ApiModel(description = "账户流水表")
@TableName(value = "acc_account_flow")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccAccountFlow extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 账户流水id
     */
    @TableId(type = IdType.AUTO)
    private Long accountFlowId;

    /**
     * 平台商id
     */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /**
     * 账户id
     */
    @Excel(name = "账户id")
    private Long accountId;

    /**
     * 以前的可提现金额
     */
    @Excel(name = "以前的可提现金额")
    private BigDecimal preWithdrawableAmt;

    /**
     * 增量可提现金额
     */
    @Excel(name = "增量可提现金额")
    private BigDecimal busiWithdrawableAmt;

    /**
     * 现在的可提现金额
     */
    @Excel(name = "现在的可提现金额")
    private BigDecimal nowWithdrawableAmt;

    /**
     * 以前的冻结金额
     */
    @Excel(name = "以前的冻结金额")
    private BigDecimal preFrozenAmt;

    /**
     * 增量冻结金额
     */
    @Excel(name = "增量冻结金额")
    private BigDecimal busiFrozenAmt;

    /**
     * 现在的冻结金额
     */
    @Excel(name = "现在的冻结金额")
    private BigDecimal nowFrozenAmt;

    /**
     * 以前的授信额度
     */
    @Excel(name = "以前的授信额度")
    private BigDecimal preCreditAmt;

    /**
     * 增量授信额度
     */
    @Excel(name = "增量授信额度")
    private BigDecimal busiCreditAmt;

    /**
     * 现在的授信额度
     */
    @Excel(name = "现在的授信额度")
    private BigDecimal nowCreditAmt;

    /**
     * 业务类型(待枚举，根据busi_type找busi出处) {@link AccountBusiType}
     */
    @Excel(name = "业务类型(待枚举，根据busi_type找busi出处)")
    private String busiType;

    /**
     * 业务id
     */
    @Excel(name = "业务id")
    private Long busiId;

    /**
     * 影响字段
     * 参见 {@link AccountBusiTypeField}
     */
    @Excel(name = "影响字段")
    private String busiFields;

    /**
     * 处理状态 0-未处理  1-已处理
     */
    @Excel(name = "处理状态 0-未处理  1-已处理")
    private Integer processFlag;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date processTime;

    /**
     * 支付平台(数据字典)
     */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

    /**
     * 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店
     */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /**
     * 商户id
     */
    @Excel(name = "商户id")
    private Long merchantId;

    /**
     * 账户类型;0-储值账户 1-赠送余额
     */
    @Excel(name = "账户类型;0-储值账户 1-赠送余额")
    private Integer accountType;

    /**
     * 流水备注
     */
    @Excel(name = "流水备注")
    private String memo;

    /**
     * 业务单号
     */
    @Excel(name = "业务单号")
    private String busiNo;

    public static AccAccountFlow buildByAccount(AccAccount account) {
        AccAccountFlow accAccountFlow = new AccAccountFlow();
        accAccountFlow.setMerchantType(account.getMerchantType())
                .setSysCode(account.getSysCode())
                .setAccountId(account.getAccountId())
                .setMerchantId(account.getMerchantId())
                .setMerchantType(account.getMerchantType())
                .setAccountType(account.getAccountType())
                .setPlatform(account.getPlatform())
                .setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField());
        return accAccountFlow;
    }
}
