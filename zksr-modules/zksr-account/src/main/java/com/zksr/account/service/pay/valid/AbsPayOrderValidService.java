package com.zksr.account.service.pay.valid;

import cn.hutool.core.util.NumberUtil;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.UnifiedOrderValidateDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.service.IAccPayFlowService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayRefundStatusEnum;
import com.zksr.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付订单渠道验证抽象
 * @date 2024/10/17 9:38
 */
@Slf4j
public abstract class AbsPayOrderValidService implements PayOrderValidService{

    @Autowired
    public IAccPayFlowService payFlowService;

    @Autowired
    protected IAccountCacheService accountCacheService;

    //!@支付 - 提交支付订单 - 8、支付前校验（入驻商商户号、结算信息、分账金额）
    @Override
    public UnifiedOrderValidateDTO processPayBeforeCommonValidate(PayOrderDTO reqVO) {

        // 商城订单里面才有入驻商结算信息, 里面有入驻商商户号
        // 需要验证入驻商商户号
        if (OrderTypeConstants.supplierPaySource(reqVO.getOrderType())) {
            List<OrderSettlementDTO> supplierSettleDTOList = reqVO.getSettlements().stream().filter(item -> MerchantTypeEnum.isSupplier(item.getMerchantType())).collect(Collectors.toList());
            if (supplierSettleDTOList.isEmpty()) {
                log.error("{} 入驻商商户号不存在", reqVO.getOrderNo());
                return UnifiedOrderValidateDTO.fail("该入驻商未完成认证");
            }
        }

        // 结算信息
        if (reqVO.getSettlements().isEmpty()) {
            log.error("{} 结算信息不能为空", reqVO.getOrderNo());
            return UnifiedOrderValidateDTO.fail("结算信息不能为空");
        }

        // 总分账金额
        BigDecimal totalAmt = reqVO.getSettlements().stream().map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (NumberUtil.isGreater(totalAmt, reqVO.getPayAmt())) {
            log.error("{} 订单分账金额大于订单实付", reqVO.getOrderNo());
            return UnifiedOrderValidateDTO.fail("订单分账金额大于订单实付");
        }

        // 验证分账是否有负数
        for (OrderSettlementDTO settlement : reqVO.getSettlements()) {
            if (NumberUtil.isGreater(BigDecimal.ZERO, settlement.getAmt())) {
                log.error("{} 分账方金额不能小于0, merchantType={}, 金额={}", reqVO.getOrderNo(), settlement.getMerchantType(), settlement.getAmt());
                return UnifiedOrderValidateDTO.fail("订单分账金额异常");
            }
        }
        return this.processPayBeforeValidate(reqVO);
    }

    protected abstract UnifiedOrderValidateDTO processPayBeforeValidate(PayOrderDTO reqVO);
    
    
    //!@退款 - 提交退款 - 2、退款前验证
    @Override
    public UnifiedOrderValidateDTO processRefundBeforeCommonValidate(PayRefundOrderSubmitReqVO reqDTO) {

        if (reqDTO.getSettlements().isEmpty()) {
            return UnifiedOrderValidateDTO.fail(ORDER_REFUND_SETTLE_NONE.getMsg());
        }

        // 获取最新一条退款发起记录
        // 如果发起退款报错了, 至少还有记录, 能防止重复发起
        AccPayFlow refundFlow = payFlowService.getByOrderRefundFlow(reqDTO.getRefundNo());
        if (Objects.nonNull(refundFlow)) {
            // 如果有退款状态就需要验证, 否则就是旧退款, 旧退款退款单号唯一, 直接验证有没有发起过
            if (Objects.nonNull(refundFlow.getRefundStatus())) {
                // 如果是退款中 || 退款已发起, 实际这两个状态是一样的
                if (PayRefundStatusEnum.PROCESSING.getStatus().equals(refundFlow.getRefundStatus())) {
                    return UnifiedOrderValidateDTO.fail(REFUND_FLOW_PROCESSING.getMsg());
                }
                // 如果退款完成
                if (PayRefundStatusEnum.SUCCESS.getStatus().equals(refundFlow.getRefundStatus())) {
                    return UnifiedOrderValidateDTO.fail(REFUND_FLOW_FINISH.getMsg());
                }
            } else {
                log.info("退款已经发起过了, refundNo:{}", reqDTO.getRefundNo());
                return UnifiedOrderValidateDTO.fail(REFUND_FLOW_EXIST.getMsg());
            }
        }

        // 验证退分账有没有多退
        List<AccPayFlow> refundFlows = payFlowService.getByOrderRefundFlowList(reqDTO.getPayFlowDTO().getTradeNo());
        if (Objects.nonNull(reqDTO.getSettlements())) {
            // 已退款 settlementDTOS
            List<OrderSettlementDTO> settlementDTOS = refundFlows.stream().map(AccPayFlow::buildSettle).flatMap(Collection::stream).collect(Collectors.toList());
            // 加上本次退分账
            settlementDTOS.addAll(reqDTO.getSettlements());
            // 退款合计
            Map<String, List<OrderSettlementDTO>> refundMerchantTypeMap = settlementDTOS.stream().collect(Collectors.groupingBy(OrderSettlementDTO::getMerchantType));
            // 支付合计
            Map<String, List<OrderSettlementDTO>> payMerchantTypeMap = reqDTO.getPayFlowDTO().buildSettle().stream().collect(Collectors.groupingBy(OrderSettlementDTO::getMerchantType));
            for (String merchantType : refundMerchantTypeMap.keySet()) {
                // 结算数据
                List<OrderSettlementDTO> settleList = payMerchantTypeMap.get(merchantType);
                // 退款金额
                BigDecimal merchantRefundAmt = settleList.stream().map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (!payMerchantTypeMap.containsKey(merchantType)) {
                    log.error("退款时的分账商户号, 在支付分账里面不存在 退款单号={}, 商户类型={}, 退款金额={}", reqDTO.getRefundNo(),  merchantType, merchantRefundAmt);
                    return UnifiedOrderValidateDTO.fail(ORDER_REFUND_SPLIT_AMT_ERR.getMsg());
                }
                BigDecimal merchantPayAmt = payMerchantTypeMap.get(merchantType).stream().map(OrderSettlementDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (NumberUtil.isGreater(merchantRefundAmt, merchantPayAmt)) {
                    log.info("商户退款金额大于收款商户金额, orderNo={}, payAmt={}, refundAmt={}", reqDTO.getOrderNo(), merchantPayAmt, merchantRefundAmt);
                    return UnifiedOrderValidateDTO.fail(ORDER_REFUND_SPLIT_AMT_ERR.getMsg());
                }
            }
        }
        return this.processRefundBeforeValidate(reqDTO);
    }

    protected abstract UnifiedOrderValidateDTO processRefundBeforeValidate(PayRefundOrderSubmitReqVO reqDTO);
}
