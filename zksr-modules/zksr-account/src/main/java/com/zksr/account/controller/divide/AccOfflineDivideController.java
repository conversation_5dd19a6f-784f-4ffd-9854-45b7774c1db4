package com.zksr.account.controller.divide;

import javax.validation.Valid;

import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccOfflineDivide;
import com.zksr.account.service.IAccOfflineDivideService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.controller.divide.vo.AccOfflineDividePageReqVO;
import com.zksr.account.controller.divide.vo.AccOfflineDivideSaveReqVO;
import com.zksr.account.controller.divide.vo.AccOfflineDivideRespVO;
import com.zksr.account.convert.divide.AccOfflineDivideConvert;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 线下分账处理Controller
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Api(tags = "管理后台 - 线下分账处理接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/offLineDivide")
public class AccOfflineDivideController {

    @Autowired
    private IAccOfflineDivideService accOfflineDivideService;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 新增线下分账处理
     */
    @ApiOperation(value = "新增线下分账处理", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "线下分账处理", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccOfflineDivideSaveReqVO createReqVO) {
        return success(accOfflineDivideService.insertAccOfflineDivide(createReqVO));
    }

    /**
     * 修改线下分账处理
     */
    @ApiOperation(value = "修改线下分账处理", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "线下分账处理", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AccOfflineDivideSaveReqVO updateReqVO) {
        accOfflineDivideService.updateAccOfflineDivide(updateReqVO);
        return success(true);
    }

    /**
     * 删除线下分账处理
     */
    @ApiOperation(value = "删除线下分账处理", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "线下分账处理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{offlineDivideIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] offlineDivideIds) {
        accOfflineDivideService.deleteAccOfflineDivideByOfflineDivideIds(offlineDivideIds);
        return success(true);
    }

    /**
     * 获取线下分账处理详细信息
     */
    @ApiOperation(value = "获得线下分账处理详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{offlineDivideId}")
    public CommonResult<AccOfflineDivideRespVO> getInfo(@PathVariable("offlineDivideId") Long offlineDivideId) {
        AccOfflineDivide accOfflineDivide = accOfflineDivideService.getAccOfflineDivide(offlineDivideId);
        AccOfflineDivideRespVO respVO = AccOfflineDivideConvert.INSTANCE.convert(accOfflineDivide);
        SupplierDTO supplierDTO = accountCacheService.getSupplierDTO(accOfflineDivide.getMerchantId());
        if (Objects.nonNull(supplierDTO)) {
            respVO.setMerchantName(supplierDTO.getSupplierName());
            respVO.setContactPhone(supplierDTO.getContactPhone());
        }
        return success(respVO);
    }

    /**
     * 通过单号获得线下分账处理详情
     */
    @ApiOperation(value = "通过单号获得线下分账处理详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/infoNo/{offlineProNo}")
    public CommonResult<AccOfflineDivideRespVO> getInfoByProNo(@PathVariable("offlineProNo") String offlineProNo) {
        AccOfflineDivide accOfflineDivide = accOfflineDivideService.getAccOfflineDivide(offlineProNo);
        AccOfflineDivideRespVO respVO = AccOfflineDivideConvert.INSTANCE.convert(accOfflineDivide);
        SupplierDTO supplierDTO = accountCacheService.getSupplierDTO(accOfflineDivide.getMerchantId());
        if (Objects.nonNull(supplierDTO)) {
            respVO.setMerchantName(supplierDTO.getSupplierName());
            respVO.setContactPhone(supplierDTO.getContactPhone());
        }
        return success(respVO);
    }

    /**
     * 分页查询线下分账处理
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得线下分账处理分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccOfflineDivideRespVO>> getPage(@Valid AccOfflineDividePageReqVO pageReqVO) {
        PageResult<AccOfflineDivide> pageResult = accOfflineDivideService.getAccOfflineDividePage(pageReqVO);
        PageResult<AccOfflineDivideRespVO> result = AccOfflineDivideConvert.INSTANCE.convertPage(pageResult);
        result.getList().forEach(vo -> {
            SupplierDTO supplierDTO = accountCacheService.getSupplierDTO(vo.getMerchantId());
            if (Objects.nonNull(supplierDTO)) {
                vo.setMerchantName(supplierDTO.getSupplierName());
                vo.setContactPhone(supplierDTO.getContactPhone());
            }
        });
        return success(result);
    }

    /**
     * 权限字符
     */
    public static class Permissions {
        /**
         * 添加
         */
        public static final String ADD = "account:offLineDivide:add";
        /**
         * 编辑
         */
        public static final String EDIT = "account:offLineDivide:edit";
        /**
         * 删除
         */
        public static final String DELETE = "account:offLineDivide:remove";
        /**
         * 列表
         */
        public static final String LIST = "account:offLineDivide:list";
        /**
         * 查询
         */
        public static final String GET = "account:offLineDivide:query";
        /**
         * 停用
         */
        public static final String DISABLE = "account:offLineDivide:disable";
        /**
         * 启用
         */
        public static final String ENABLE = "account:offLineDivide:enable";
    }
}
