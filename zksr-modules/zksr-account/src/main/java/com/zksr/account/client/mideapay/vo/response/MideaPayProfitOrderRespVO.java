package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;

@ApiModel("美的付多级分账下单响应VO")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayProfitOrderRespVO extends MideaPayBaseRespVO {
    @ApiModelProperty("商户分账订单号")
    @JsonProperty("out_profit_no")
    private String outProfitNo;

    @ApiModelProperty("原商户交易订单号")
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    @ApiModelProperty("支付系统分账订单号")
    @JsonProperty("profit_no")
    private String profitNo;
    
    @ApiModelProperty("订单支付状态")
    @JsonProperty("trade_status")
    private String tradeStatus;
    
    @ApiModelProperty("订单接收时间")
    @JsonProperty("profit_sharing_accept_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String profitSharingAcceptTime;
}