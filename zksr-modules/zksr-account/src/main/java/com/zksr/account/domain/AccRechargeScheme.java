package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 储值充值套餐配置对象 acc_recharge_scheme
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@TableName(value = "acc_recharge_scheme")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccRechargeScheme extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 充值方案id */
    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeSchemeId;

    /** 方案名称 */
    @Excel(name = "方案名称")
    private String schemeName;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 适用区域城市 */
    @Excel(name = "适用区域城市")
    private String areaIds;

    /** 生效开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 生效结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "生效结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 充值金额，赠送金额 */
    @Excel(name = "充值金额，赠送金额")
    private String ruleJson;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private Integer status;

}
