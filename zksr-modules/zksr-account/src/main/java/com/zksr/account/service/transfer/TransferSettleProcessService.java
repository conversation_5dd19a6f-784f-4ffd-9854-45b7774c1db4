package com.zksr.account.service.transfer;

import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;

/**
 * <AUTHOR>
 * @time 2024/3/11
 * @desc  提现操作流程控制
 */
public interface TransferSettleProcessService {

    /**
     * 提现处理中
     * @param transferSettleRespDTO
     */
    void notifyProcessing(TransferSettleRespDTO transferSettleRespDTO);

    /**
     * 提现成功回调
     * @param transferSettleRespDTO
     */
    void notifySuccess(TransferSettleRespDTO transferSettleRespDTO);

    /**
     * 提现转账失败
     * @param transferSettleRespDTO
     */
    void notifyFail(TransferSettleRespDTO transferSettleRespDTO);

}
