package com.zksr.account.service;

import javax.validation.*;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccOfflineDivide;
import com.zksr.account.controller.divide.vo.AccOfflineDividePageReqVO;
import com.zksr.account.controller.divide.vo.AccOfflineDivideSaveReqVO;

/**
 * 线下分账处理Service接口
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
public interface IAccOfflineDivideService {

    /**
     * 新增线下分账处理
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAccOfflineDivide(@Valid AccOfflineDivideSaveReqVO createReqVO);

    /**
     * 修改线下分账处理
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccOfflineDivide(@Valid AccOfflineDivideSaveReqVO updateReqVO);

    /**
     * 删除线下分账处理
     *
     * @param offlineDivideId ${pkColumn.columnComment}
     */
    public void deleteAccOfflineDivide(Long offlineDivideId);

    /**
     * 批量删除线下分账处理
     *
     * @param offlineDivideIds 需要删除的线下分账处理主键集合
     * @return 结果
     */
    public void deleteAccOfflineDivideByOfflineDivideIds(Long[] offlineDivideIds);

    /**
     * 获得线下分账处理
     *
     * @param offlineDivideId ${pkColumn.columnComment}
     * @return 线下分账处理
     */
    public AccOfflineDivide getAccOfflineDivide(Long offlineDivideId);

    /**
     * 获得线下分账处理
     *
     * @param offlineProNo  处理单号
     * @return 线下分账处理
     */
    AccOfflineDivide getAccOfflineDivide(String offlineProNo);

    /**
     * 获得线下分账处理分页
     *
     * @param pageReqVO 分页查询
     * @return 线下分账处理分页
     */
    PageResult<AccOfflineDivide> getAccOfflineDividePage(AccOfflineDividePageReqVO pageReqVO);
}
