package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.annotation.Excel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 商户账单文件备份对象 acc_bill_file
 * 
 * @date 2024-03-22
 */
@TableName(value = "acc_bill_file")
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccBillFile {
    private static final long serialVersionUID = 1L;

    /** 文件ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long billFileId;
    /**
     * 创建时间
     */
    private Date createTime;

    /** 账单日期 */
    @Excel(name = "账单日期")
    private Date date;

    /** 文件 */
    @Excel(name = "文件")
    private String file;

    /** 商户号 */
    @Excel(name = "商户号")
    private String altNo;

    /** 文件类型 */
    @Excel(name = "文件类型")
    private String type;
}