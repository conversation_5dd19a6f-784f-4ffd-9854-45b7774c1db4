package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.enums.PayChannelEnum;
import lombok.*;

import java.math.BigDecimal;
import java.util.Objects;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * 账户对象 acc_account
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@TableName(value = "acc_account")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccAccount extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 账户id */
    @TableId(type = IdType.AUTO)
    private Long accountId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 账户类型;0-储值账户 1-赠送余额 */
    @Excel(name = "账户类型;0-储值账户 1-赠送余额")
    private Integer accountType;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    private Long merchantId;

    /** 可提现金额 */
    @Excel(name = "可提现金额")
    private BigDecimal withdrawableAmt;

    /** 冻结金额 */
    @Excel(name = "冻结金额")
    private BigDecimal frozenAmt;

    /** 授信额度 */
    @Excel(name = "授信额度")
    private BigDecimal creditAmt;

    /** 版本号 */
    @Excel(name = "版本号")
    @Version
    private Long version;

    /** 账务持有方类型;supplier-入驻商 partner-平台商 */
    @Excel(name = "账务持有方类型;supplier-入驻商 partner-平台商")
    private String holdMerchantType;

    /** 账务持有方id */
    @Excel(name = "账务持有方id")
    private String holdMerchantId;

    /** 支付平台(数据字典);当merchant_type=branch时，无此值 */
    @Excel(name = "支付平台(数据字典);当merchant_type=branch时，无此值")
    private String platform;

    public PayChannelEnum payChannel() {
        return PayChannelEnum.fromValue(platform);
    }

    /**
     * 获取有效金额
     * @return
     */
    public BigDecimal getValidAccountAmt() {
        if (Objects.nonNull(withdrawableAmt)) {
            return  withdrawableAmt.subtract(frozenAmt);
        }
        return BigDecimal.ZERO;
    }
}
