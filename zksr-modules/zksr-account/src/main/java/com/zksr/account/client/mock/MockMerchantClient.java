package com.zksr.account.client.mock;

import com.zksr.account.client.MerchantClient;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝商户操作
 * @date 2024/4/23 14:11
 */
@Data
@Slf4j
public class MockMerchantClient implements MerchantClient {

    @Override
    public PayPlatformAccountVO getAccountBalance(String merchantNo) {
        PayPlatformAccountVO accountVO = new PayPlatformAccountVO();
        accountVO.setBalance(new BigDecimal("1899.00"));
        accountVO.setFrozenBalance(new BigDecimal("99.00"));
        accountVO.setSettleAmt(new BigDecimal("1899.00"));
        return accountVO;
    }

    @Override
    public PayPlatformMerchantVO getRegisterMerchant(String orderNo, String merchantNo) {
        return new PayPlatformMerchantVO();
    }
}
