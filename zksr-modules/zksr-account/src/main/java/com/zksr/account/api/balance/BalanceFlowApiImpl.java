package com.zksr.account.api.balance;

import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.api.balance.vo.AccBalanceFlowRespVO;
import com.zksr.account.service.IAccBalanceFlowService;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@InnerAuth
@ApiIgnore
@RestController
public class BalanceFlowApiImpl implements BalanceFlowApi {

    @Autowired
    private IAccBalanceFlowService accBalanceFlowService;

    @Override
    public CommonResult<PageResult<AccBalanceFlowRespVO>> getFlowPageList(MemBranchBalanceFlowDTO pageReqDTO) {
        return CommonResult.success(accBalanceFlowService.getFlowPageList(pageReqDTO));
    }

    @Override
    public CommonResult<Boolean> rollBackByFlowId(String balanceFlowId) {
        return CommonResult.success(accBalanceFlowService.rollBackByFlowId(balanceFlowId));
    }

}
