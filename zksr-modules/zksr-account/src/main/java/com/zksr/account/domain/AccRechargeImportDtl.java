package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * PC后台导入充值详情
对象 acc_recharge_import_dtl
 *
 * <AUTHOR>
 * @date 2025-03-24
 */
@TableName(value = "acc_recharge_import_dtl")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccRechargeImportDtl extends BaseEntity {
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeImportDtlId;

    @ApiModelProperty("平台商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    @ApiModelProperty("导入批次ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeImportId;

    @ApiModelProperty("门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty("充值ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeId;

    @ApiModelProperty("充值金额")
    private BigDecimal rechargeAmt;
}
