package com.zksr.account.client.mideapay.vo.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayBalanceQryReqVO extends MideaPayBaseReqVO {
    /**
     * 待查询商户号
     */
    @JsonProperty("query_partner")
    private String queryPartner;

}
