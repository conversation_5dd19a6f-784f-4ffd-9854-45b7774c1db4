package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.controller.divide.vo.AccDivideDtlRespVO;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.common.core.enums.DivideStateEnum;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccDivideDtl;
import com.zksr.account.controller.divide.vo.AccDivideDtlPageReqVO;
import com.zksr.account.controller.divide.vo.AccDivideDtlSaveReqVO;

import java.util.ArrayList;
import java.util.List;

/**
 * 支付分账详情Service接口
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
public interface IAccDivideDtlService {

    /**
     * 新增支付分账详情
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long insertAccDivideDtl(@Valid AccDivideDtlSaveReqVO createReqVO);

    /**
     * 修改支付分账详情
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccDivideDtl(@Valid AccDivideDtlSaveReqVO updateReqVO);

    /**
     * 删除支付分账详情
     *
     * @param divideDtlId 支付分账详情id
     */
    public void deleteAccDivideDtl(Long divideDtlId);

    /**
     * 批量删除支付分账详情
     *
     * @param divideDtlIds 需要删除的支付分账详情主键集合
     * @return 结果
     */
    public void deleteAccDivideDtlByDivideDtlIds(Long[] divideDtlIds);

    /**
     * 获得支付分账详情
     *
     * @param divideDtlId 支付分账详情id
     * @return 支付分账详情
     */
    public AccDivideDtl getAccDivideDtl(Long divideDtlId);

    /**
     * 获得支付分账详情分页
     *
     * @param pageReqVO 分页查询
     * @return 支付分账详情分页
     */
    PageResult<AccDivideDtlRespVO> getAccDivideDtlPage(AccDivideDtlPageReqVO pageReqVO);

    /**
     * 通过支付流水创建分账
     * @param payFlow
     */
    void createDivide(AccPayFlow payFlow, PayOrderRespDTO orderRespDTO);

    /**
     * 通过退款流水创建分账
     * @param payFlow
     * @param notify
     */
    void createDivide(AccPayFlow payFlow, PayRefundRespDTO notify);

    /**
     * 获取订单分账列表
     * @return
     */
    List<AccDivideDtl> getDivideList(AccDivideDtl divideDtl);

    /**
     * 批量更新数据
     * @param updateList
     */
    void updateBatch(ArrayList<AccDivideDtl> updateList);

}
