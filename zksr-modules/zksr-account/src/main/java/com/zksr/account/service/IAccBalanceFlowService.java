package com.zksr.account.service;

import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.api.balance.vo.AccBalanceFlowRespVO;
import com.zksr.account.domain.AccBalance;
import com.zksr.account.enums.balance.ActionTypeEnum;
import com.zksr.common.core.web.pojo.PageResult;

import java.util.List;

/**
 * 门店账户余额流水
 */
public interface IAccBalanceFlowService {

    // 保存交易流水
    Long saveFlow(AccBalance accBalance, MemBranchBalanceDTO balanceDTO, ActionTypeEnum typeEnum);

    // 查询交易流水
    List<AccBalanceFlowRespVO> getBalanceFlowList(Long branchId, Integer actionType);

    // 分页查询门店余额交易流水
    PageResult<AccBalanceFlowRespVO> getFlowPageList(MemBranchBalanceFlowDTO pageReqDTO);

    // 跟据交易流水id回滚余额
    Boolean rollBackByFlowId(String balanceFlowId);
}
