package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO;
import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoReqVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bInfoRespVO;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bSaveReqVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccPlatformMerchant;

import java.util.List;

/**
 * 支付平台商户Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface IAccPlatformMerchantService {

    /**
     * 保存支付平台商户
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    public Long savePlatformMerchant(@Valid AccPlatformMerchantSaveReqVO createReqVO);

    /**
     * 修改支付平台商户
     *
     * @param updateReqVO 修改信息
     * @return 结果
     */
    public void updateAccPlatformMerchant(@Valid AccPlatformMerchantSaveReqVO updateReqVO);

    /**
     * 删除支付平台商户
     *
     * @param platformMerchantId 支付平台商户id
     */
    public void deleteAccPlatformMerchant(Long platformMerchantId);

    /**
     * 批量删除支付平台商户
     *
     * @param platformMerchantIds 需要删除的支付平台商户主键集合
     * @return 结果
     */
    public void deleteAccPlatformMerchantByPlatformMerchantIds(Long[] platformMerchantIds);

    /**
     * 获得支付平台商户
     *
     * @param platformMerchantId 支付平台商户id
     * @return 支付平台商户
     */
    public AccPlatformMerchant getAccPlatformMerchant(Long platformMerchantId);

    /**
     * 获得支付平台商户分页
     *
     * @param pageReqVO 分页查询
     * @return 支付平台商户分页
     */
    PageResult<AccPlatformMerchant> getAccPlatformMerchantPage(AccPlatformMerchantPageReqVO pageReqVO);

    /**
     * 获取平台进件商户信息
     * @param merchantId    入驻商ID
     * @param merchantType  商户类型
     * @param platform      支付平台
     * @return
     */
    AccPlatformMerchant getPlatformMerchant(Long merchantId, String merchantType, String platform);

    /**
     * 获取平台进件商户信息
     * @param merchantId    入驻商ID
     * @param merchantType  商户类型
     * @param platform      支付平台
     * @param sysCode       平台商
     * @return
     */
    AccPlatformMerchant getPlatformMerchant(Long merchantId, String merchantType, String platform, Long sysCode);

    /**
     * 获取平台进件商户信息
     * @param merchantId    入驻商ID
     * @param merchantType  商户类型
     * @param sysCode       平台ID
     * @return
     */
    AccPlatformMerchant getPlatformMerchant(Long merchantId, String merchantType, Long sysCode);

    /**
     * 验证商户是否已经进件
     * @param merchantId    商户ID
     * @param merchantType  商户类型
     * @param platform      支付平台
     * @return
     */
    boolean checkExist(Long merchantId, String merchantType, String platform);

    /**
     * 商户进件
     * @param reqVO 进件商户请求
     * @return 支付商户平台信息ID
     */
    Long register(AccPlatformMerchantRegisterReqVO reqVO);

    /**
     * 更新商户进件状态
     * @param respVO  进件结果
     */
    void updateRegisterStatus(PlatformMerchantRegisterSaveRespVO respVO);

    /**
     * 商户资质上传
     * @param reqVO 商户资质上传请求
     */
    void uploadPic(AccPlatformMerchantUploadPicReqVO reqVO);

    /**
     * 更新进件信息
     * @param reqVO 进件信息
     * @return
     */
    Long updateRegister(AccPlatformMerchantRegisterReqVO reqVO);

    /**
     * 同步商户信息
     * @param reqVO 商户信息
     */
    void syncData(AccPlatformMerchantSyncReqVO reqVO);

    /**
     * 处理进件商户审核中状态更新
     * @param minId    批次最小ID
     * @return  批次最大ID
     */
    Long processMerchantStatus(Long minId);

    /**
     * 获取微信B2B支付商户配置
     * @param reqVO
     * @return
     */
    AccPlatformMerchantWxb2bInfoRespVO getUpdateWxB2bPayConfig(AccPlatformMerchantWxb2bInfoReqVO reqVO);

    /**
     * 更新微信B2B支付商户配置
     * @param saveReqVO
     * @return
     */
    Long updateWxB2bPayConfig(AccPlatformMerchantWxb2bSaveReqVO saveReqVO);

    /**
     * 根据平台编号 获取支付平台商户的分账方商户编号和商户key(去重)
     * @param merchantType
     * @param sysCode
     * @param payPlatform
     * @return
     */
    List<PlatformMerchantNameAndKeyDTO> getPlatformMerchantNameAndKey(String merchantType, Long sysCode, String payPlatform);

    List<Long> getMerchantIdByAltNo(String merchantType, String altNo, Long sysCode);
}
