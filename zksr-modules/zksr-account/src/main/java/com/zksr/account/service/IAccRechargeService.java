package com.zksr.account.service;

import com.zksr.account.controller.recharge.vo.AccRechargeRespVO;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveReqVO;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.controller.recharge.vo.AccRechargePageReqVO;
import com.zksr.account.controller.recharge.vo.AccRechargeSaveReqVO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;

import java.util.List;

/**
 * 账户充值单Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface IAccRechargeService {

    /**
     * 获得账户充值单
     *
     * @param rechargeId 账户充值单id
     * @return 账户充值单
     */
    public AccRecharge getAccRecharge(Long rechargeId);

    /**
     * 获得账户充值单分页
     *
     * @param pageReqVO 分页查询
     * @return 账户充值单分页
     */
    PageResult<AccRecharge> getAccRechargePage(AccRechargePageReqVO pageReqVO);

    /**
     * 获得账户充值单分页
     *
     * @param pageReqVO 分页查询
     * @return 账户充值单分页
     */
    PageResult<AccRechargeRespVO> getAccRechargeExtPage(AccRechargePageReqVO pageReqVO);

    /**
     * 新增入驻商充值单
     * @param createReqVO
     * @return
     */
    String insertSupplierAccRecharge(AccRechargeSaveReqVO createReqVO);

    /**
     * 新增门店充值单
     * @param createReqVO
     * @return
     */
    String insertBranchAccRecharge(BranchRechargeSaveReqVO createReqVO);

    /**
     * 根据充值单号获取充值单
     * @param rechargeNo    充值单号
     * @return  充值单
     */
    AccRecharge getRechargeByNo(String rechargeNo);

    /**
     * 获取充值结算信息
     * @param recharge      充值单
     * @param payConfigDTO  支付配置
     * @return  支付结算信息
     */
    List<OrderSettlementDTO> getBranchSettles(AccRecharge recharge, PayConfigDTO payConfigDTO);

    /**
     * 入驻商充值成功
     * @param recharge
     */
    List<AccAccountFlow> rechargeSuccess(AccRecharge recharge);

}
