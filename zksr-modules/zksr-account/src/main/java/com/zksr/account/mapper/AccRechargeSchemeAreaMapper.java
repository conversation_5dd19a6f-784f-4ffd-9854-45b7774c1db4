package com.zksr.account.mapper;

import com.zksr.account.domain.AccRechargeSchemeArea;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;


/**
 * 储值套餐上架发布城市Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Mapper
public interface AccRechargeSchemeAreaMapper extends BaseMapperX<AccRechargeSchemeArea> {
    default void deleteBySchemeId(Long rechargeSchemeId) {
        delete(new LambdaQueryWrapperX<AccRechargeSchemeArea>().eq(AccRechargeSchemeArea::getRechargeSchemeId, rechargeSchemeId));
    }
}
