package com.zksr.account.controller.merchant.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信B2B支付商户配置
 * @date 2024/9/24 14:58
 */
@Data
@ApiModel(description = "微信B2B支付商户配置")
public class AccPlatformMerchantWxb2bInfoRespVO {

    /** 最小账户保留金 */
    @Excel(name = "最小账户保留金")
    @ApiModelProperty("最小账户保留金")
    private BigDecimal minWithdrawAmt;

    /** 0-未开启,1-已开启 */
    @Excel(name = "0-未开启,1-已开启")
    @ApiModelProperty("0-未开启,1-已开启")
    private Integer autoWithdraw;

    /** 支付秘钥 */
    @Excel(name = "支付秘钥")
    @ApiModelProperty("支付秘钥")
    private String appKey;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty("商户类型")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty("商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 技术手续费比例 */
    @ApiModelProperty(value = "技术手续费比例最小0.003, 最大0.006", hidden = true)
    private BigDecimal profitRate;
}
