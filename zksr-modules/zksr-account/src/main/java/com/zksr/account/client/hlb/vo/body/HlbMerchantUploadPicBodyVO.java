package com.zksr.account.client.hlb.vo.body;

import com.zksr.account.enums.MerchantCredentialType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户进件信息
 * @date 2024/7/12 14:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "合利宝商户资质上传")
public class HlbMerchantUploadPicBodyVO {

    @ApiModelProperty(value = "商户号", notes = "进件注册审核以后才有")
    private String merchantNo;

    @ApiModelProperty(value = "进件商户订单号", notes = "商户内部编号")
    private String orderNo;

    /**
     * {@link MerchantCredentialType}
     */
    @ApiModelProperty(value = "资质类型")
    private String credentialType;

    @ApiModelProperty(value = "资质全url")
    private String credentialUrl;
}
