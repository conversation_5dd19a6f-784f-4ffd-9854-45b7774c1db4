package com.zksr.account.controller.recharge.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 账户充值单对象 acc_recharge
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@ApiModel("账户充值单 - acc_recharge分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccRechargePageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 账户充值单id */
    @ApiModelProperty(value = "充值单号")
    private Long rechargeId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 充值类型;0-入驻商充值 1-门店充值*/
    @Excel(name = "充值类型;0-入驻商充值 1-门店充值")
    @ApiModelProperty(value = "充值类型;0-入驻商充值 1-门店充值")
    private Integer chargeType;

    /** 充值方类型;supplier-入驻商  branch-门店 */
    @Excel(name = "充值方类型;supplier-入驻商  branch-门店")
    @ApiModelProperty(value = "充值方类型;supplier-入驻商  branch-门店")
    private String rechargeMerchantType;

    /** 充值方id */
    @Excel(name = "充值方id")
    @ApiModelProperty(value = "充值方id")
    private Long rechargeMerchantId;

    /** 充值状态: 0-未支付,1-已支付 */
    @Excel(name = "充值状态: 0-未支付,1-已支付")
    @ApiModelProperty(value = "充值状态: 0-未支付,1-已支付")
    private Integer state;

    /** 充值单号 */
    @Excel(name = "充值单号")
    @ApiModelProperty(value = "充值单号")
    private String rechargeNo;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "充值开始时间")
    private Date startTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "充值结束时间")
    private Date endTime;

    /** 收款方类型;supplier-入驻商 partner-平台商 */
    @Excel(name = "收款方类型;supplier-入驻商 partner-平台商 dc-运营商")
    private String receiveMerchantType;

    /** 收款方id;当收款方为软件商时，不需要记录此值 */
    @Excel(name = "收款方id;当收款方为软件商时，不需要记录此值")
    private Long receiveMerhcantId;
}
