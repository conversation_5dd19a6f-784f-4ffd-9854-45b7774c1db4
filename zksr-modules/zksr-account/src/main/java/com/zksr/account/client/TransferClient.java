package com.zksr.account.client;

import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.model.transfer.vo.TransferSettleReqVO;
import com.zksr.account.model.transfer.vo.TransferSubmitReqVO;
import com.zksr.common.core.constant.OrderTypeConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @time 2024/3/7
 * @desc 第三方账户交易操作
 */
public interface TransferClient {

    /**
     * 获取支付平台标识
     * @return
     */
    String getPlatform();

    /**
     * 设置配置
     * @param config
     */
    void setConfig(String config) throws Exception;

    /**
     * 交易转账
     * @param reqVO
     * @return
     */
    TransferSubmitRespDTO transferSubmit(TransferSubmitReqVO reqVO);

    /**
     * 解析回调参数处理
     * @param params
     * @param body
     * @return
     */
    TransferSubmitRespDTO parseSubmitNotify(Map<String, String> params, String body);


    /**
     * 交易结算 (提现)
     * @param reqVO
     * @return
     */
    TransferSettleRespDTO transferSettle(TransferSettleReqVO reqVO);

    /**
     * 解析交易结算 (提现) 回调
     * @param params
     * @param body
     * @return
     */
    TransferSettleRespDTO parseSettleNotify(Map<String, String> params, String body);


    @ApiModel(description = "客户端描述")
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ClientParam {

        @ApiModelProperty("平台商编号")
        private Long sysCode;

        @ApiModelProperty("支付方式")
        private String platform;

        @ApiModelProperty("商户类型")
        private String merchantType;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ClientParam that = (ClientParam) o;
            return Objects.equals(sysCode, that.sysCode) && Objects.equals(platform, that.platform) && Objects.equals(merchantType, that.merchantType);
        }

        @Override
        public int hashCode() {
            return Objects.hash(sysCode, platform, merchantType);
        }
    }
}
