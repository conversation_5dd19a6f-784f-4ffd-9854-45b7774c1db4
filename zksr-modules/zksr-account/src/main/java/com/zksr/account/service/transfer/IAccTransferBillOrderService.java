package com.zksr.account.service.transfer;

import com.zksr.account.api.transfer.dto.AccBillFileDTO;
import com.zksr.account.api.transfer.dto.AccTransferBillOrderDTO;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderPageReqVO;
import com.zksr.account.controller.transfer.vo.AccTransferBillOrderRespVO;
import com.zksr.account.domain.AccTransferBillOrder;
import com.zksr.common.core.web.pojo.PageResult;

import java.util.List;

/**
 * 交易对账单明细单Service接口
 *
 * <AUTHOR>
 * @date 2024-10-30
 */
public interface IAccTransferBillOrderService {
    /**
     * 新增交易对账单明细单
     * @return 结果
     */
    public void insertTransferBillOrder(List<AccTransferBillOrderDTO> accBillOrderDTO);

    /**
     * 获得交易对账单明细单分页
     *
     * @param pageReqVO 分页查询
     * @return 交易对账单明细单分页
     */
    PageResult<AccTransferBillOrderRespVO> getAccTransferBillOrderPage(AccTransferBillOrderPageReqVO pageReqVO);

    List<String> countTransferBillsByDateAndAltNo(String dataFormatted, String altNo);

    void deleteTransferBillsByDateAndAltNo(String dataFormatted, String altNo);
}
