package com.zksr.account.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zksr.account.api.account.vo.ColonelAccountInfoVO;
import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.controller.account.vo.*;
import com.zksr.account.convert.account.AccountConvert;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.mapper.AccAccountMapper;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.account.dto.AccPlatformAccountDTO;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.service.*;
import com.zksr.account.service.metchant.MerchantService;
import com.zksr.common.core.constant.PayConstants;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.MERCHANT_TYPE_ERR;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * 账户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Service
@SuppressWarnings(StringPool.ALL)
public class AccAccountServiceImpl implements IAccAccountService {

    @Autowired
    private AccAccountMapper accAccountMapper;

    @Resource
    private SupplierApi supplierApi;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;

    @Autowired
    private IAccPlatformMerchantService accPlatformMerchantService;

    @Resource
    private DcApi dcApi;

    @Autowired
    private MerchantService merchantService;

    /**
     * 新增账户
     *
     * @param accAccount 创建信息
     * @return 结果
     */
    @Override
    public Long insertAccAccount(AccAccount accAccount) {
        // 插入
        accAccountMapper.insert(accAccount);
        // 返回
        return accAccount.getAccountId();
    }

    /**
     * 修改账户
     *
     * @param accAccount 修改信息
     * @return 结果
     */
    @Override
    public boolean updateAccAccount(AccAccount accAccount) {
        return accAccountMapper.updateById(accAccount) > 0;
    }

    /**
     * 删除账户
     *
     * @param accountId 账户id
     */
    @Override
    public void deleteAccAccount(Long accountId) {
        // 删除
        accAccountMapper.deleteById(accountId);
    }

    /**
     * 批量删除账户
     *
     * @param accountIds 需要删除的账户主键
     * @return 结果
     */
    @Override
    public void deleteAccAccountByAccountIds(Long[] accountIds) {
        for(Long accountId : accountIds){
            this.deleteAccAccount(accountId);
        }
    }

    /**
     * 获得账户
     *
     * @param accountId 账户id
     * @return 账户
     */
    @Override
    public AccAccount getAccAccount(Long accountId) {
        return accAccountMapper.selectById(accountId);
    }

    /**
    * 查询分页数据
    * @param pageReqVO
    * @return
    */
    @Override
    public PageResult<AccAccount> getAccAccountPage(AccAccountPageReqVO pageReqVO) {
        return accAccountMapper.selectPage(pageReqVO);
    }

    /**
     * 获取账户
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform     支付平台  {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    @Override
    public AccAccount getAccountByMerchantIdAndType(Long merchantId, String merchantType, String platform) {
        return accAccountMapper.selectAccountByMerchantIdAndType(merchantId, merchantType, platform);
    }

    @Override
    public AccAccount getAccountByMerchantIdAndType(Long merchantId, String merchantType, String platform, Integer accountType) {
        return accAccountMapper.selectAccountByMerchantIdAndAccountType(merchantId, merchantType, platform, accountType);
    }

    /**
     * 获取账户 默认平台
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @return
     */
    @Override
    public AccAccount getAccountByMerchantIdAndTypeByDefPlatform(Long merchantId, String merchantType, Long sysCode) {
        // 获取当前平台配置platform
        PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(sysCode);
        if (Objects.isNull(payConfigDto)) {
            // 大区支付配置不存
            return null;
        }
        return accAccountMapper.selectAccountByMerchantIdAndType(merchantId, merchantType, payConfigDto.getInteriorStoredPayPlatform());
    }

    @Override
    public List<AccAccount> getAccountListByMerchantIdAndType(Long merchantId, String merchantType, String platform) {
        AccAccountInfoReqVO reqVo = new AccAccountInfoReqVO();
        reqVo.setMerchantId(merchantId);
        reqVo.setMerchantType(merchantType);
        reqVo.setPlatform(platform);
        return accAccountMapper.selectAccountInfoLis(reqVo);
    }

    /**
     *
     * @param sysCode       平台编号
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param holdMerchantId 账户持有方
     * @param platform     支付平台  {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    @Override
    public AccAccount getAccount(Long sysCode, Long merchantId, String merchantType, Long holdMerchantId, String platform) {
        return accAccountMapper.selectAccount(sysCode, merchantId, merchantType, holdMerchantId, platform);
    }

    /**
     * 获取不同支付平台的账户
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @return
     */
    @Override
    public List<AccAccount> getAccountByMerchantIdAndType(Long merchantId, String merchantType) {
        return accAccountMapper.selectAccountByMerchantIdAndTypeList(merchantId, merchantType);
    }

    @Override
    public List<AccAccount> getAccountInfoLis(AccAccountInfoReqVO reqVo) {
        return accAccountMapper.selectAccountInfoLis(reqVo);
    }

    @Override
    public AccAccountDTO getSupplierAccount(Long supplierId) {
        SupplierDTO supplierInfo = accountCacheService.getSupplierDTO(supplierId);
        // 获取当前平台配置platform
        PayConfigDTO payConfigDto = accountCacheService.getPayConfigDTO(supplierInfo.getSysCode());
        if (Objects.isNull(payConfigDto)) {
            // 大区支付配置不存
            return null;
        }
        AccAccountDTO accountDTO = new AccAccountDTO();
        // 获取系统登记簿账户
        AccAccount account = this.getAccountByMerchantIdAndType(supplierId, MerchantTypeEnum.SUPPLIER.getType(), payConfigDto.getInteriorStoredPayPlatform());
        BeanUtil.copyProperties(account, accountDTO);
        if (Objects.isNull(accountDTO.getWithdrawableAmt())) {
            accountDTO.setWithdrawableAmt(BigDecimal.ZERO);
        }
        if (Objects.isNull(accountDTO.getCreditAmt())) {
            accountDTO.setCreditAmt(BigDecimal.ZERO);
        }
        if (Objects.isNull(accountDTO.getFrozenAmt())) {
            accountDTO.setFrozenAmt(BigDecimal.ZERO);
        }
        // 获取入驻商的平台进件账户
        // 入驻商进件账户是支付平台的
        AccPlatformMerchant merchant = platformMerchantService.getPlatformMerchant(supplierId, MerchantTypeEnum.SUPPLIER.getType(), payConfigDto.getStoreOrderPayPlatform());
        if (Objects.nonNull(merchant)) {
            // 设置平台进件商户
            accountDTO.setPlatformAccount(new AccPlatformAccountDTO(merchant.getAltMchNo(), merchant.getAltMchName()));
        }
        if (payConfigDto.getInteriorStoredPayPlatform().equals(PayChannelEnum.MOCK.getCode())) {
            // 如果是模拟支付则返回一个假的进件号, 因为模拟支付不需要进件号
            accountDTO.setPlatformAccount(new AccPlatformAccountDTO(PayConstants.DEFAULT_MOCK_NO, PayConstants.DEFAULT_MOCK_NAME));
        }
        return accountDTO;
    }

    /**
     * 修改账户授信金额
     * 增加事务, 事务自带锁机制
     * @param saveReqVO
     */
    @Override
    @Transactional
    public void updateAccountCreditAmt(AccAccountSaveCreditReqVO saveReqVO) {
        if (StringUtils.isEmpty(saveReqVO.getPlatform())) {
            PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(SecurityUtils.getLoginUser().getSysCode());
            saveReqVO.setPlatform(payConfigDTO.getInteriorStoredPayPlatform());
        }
        AccAccountFlow accountFlow = new AccAccountFlow();
        accountFlow.setBusiCreditAmt(saveReqVO.getCreditAmt())
                .setBusiId(NumberPool.LOWER_GROUND_LONG)
                .setBusiType(AccountBusiType.UPDATE_CREDIT.getType())
                .setBusiFields(AccountBusiTypeField.CREDIT_AMT.getField())
                .setPlatform(saveReqVO.getPlatform())
                .setMerchantId(saveReqVO.getMerchantId())
                .setMerchantType(saveReqVO.getMerchantType());
        IAccAccountFlowService accountFlowService = getAccountFlowService();
        // 保存流水
        accountFlowService.insertAccAccountFlow(accountFlow);
        // 执行流水
        accountFlowService.processFlow(accountFlow);
    }

    @Override
    public AccAccount getAccAccount(Long accountId, String merchantType) {
        return accAccountMapper.selectAccountByIdAndMerchantTypeAndPlatform(accountId, merchantType);
    }

    @Override
    public ColonelAccountRespVO getColonelAccount(Long colonelId) {
        ColonelAccountRespVO colonelAccountRespVO = new ColonelAccountRespVO();
        ColonelDTO colonelDTO = accountCacheService.getColonelDTO(colonelId);
        // 钱包支付平台
        PayConfigDTO payConfigDTO = accountCacheService.getPayConfigDTO(colonelDTO.getSysCode());
        if (Objects.nonNull(payConfigDTO)) {
            colonelAccountRespVO.setStoreOrderPayPlatform(payConfigDTO.getStoreOrderPayPlatform());
        }
        // 入驻商充值分润账户
        {
            // 账户余额查询
            AccAccount accAccount = this.getAccountByMerchantIdAndTypeByDefPlatform(colonelId, MerchantTypeEnum.COLONEL.getType(), colonelDTO.getSysCode());
            // 转换成返回类型
            ColonelAccountInfoVO accountInfoVO = AccountConvert.INSTANCE.buildColonelAccount(
                    accAccount,
                    Objects.nonNull(accAccount) ? getWithdrawService().getTotalWithdrawAmt( accAccount.getAccountId() ) : BigDecimal.ZERO,
                    accPlatformMerchantService.getPlatformMerchant(colonelId, MerchantTypeEnum.COLONEL.getType(), colonelDTO.getSysCode()),
                    accountCacheService.getWithdrawalSetting(colonelDTO.getSysCode())
            );
            colonelAccountRespVO.setStorageAccount(accountInfoVO);
        }
        // 门店储值账户余额
        {
            // 账户余额查询
            AccAccount accAccount = this.getAccountByMerchantIdAndType(colonelId, MerchantTypeEnum.COLONEL.getType(), PayChannelEnum.WALLET.getCode());
            // 转换成返回类型
            ColonelAccountInfoVO accountInfoVO = AccountConvert.INSTANCE.buildColonelAccount(
                    accAccount,
                    Objects.nonNull(accAccount) ? getWithdrawService().getTotalWithdrawAmt( accAccount.getAccountId() ) : BigDecimal.ZERO,
                    accPlatformMerchantService.getPlatformMerchant(colonelId, MerchantTypeEnum.COLONEL.getType(), payConfigDTO.getStoreOrderPayPlatform()),
                    accountCacheService.getWithdrawalSetting(colonelDTO.getSysCode())
            );
            colonelAccountRespVO.setBranchRechargeAccount(accountInfoVO);
        }
        // 为了兼容业务员以前的接口, 现在业务员只需要提现门店储值余额, 其他的都通过在线分账直接分走了
        ColonelAccountInfoVO branchRechargeAccount = colonelAccountRespVO.getBranchRechargeAccount();
        if (Objects.nonNull(branchRechargeAccount)) {
            colonelAccountRespVO.setAccountId(branchRechargeAccount.getAccountId());
            colonelAccountRespVO.setWithdrawableAmt(branchRechargeAccount.getWithdrawableAmt());
            colonelAccountRespVO.setFrozenAmt(branchRechargeAccount.getFrozenAmt());
            colonelAccountRespVO.setCreditAmt(branchRechargeAccount.getCreditAmt());
            colonelAccountRespVO.setAccountName(branchRechargeAccount.getAccountName());
            colonelAccountRespVO.setAccountNo(branchRechargeAccount.getAccountNo());
            colonelAccountRespVO.setBankBranch(branchRechargeAccount.getBankBranch());
            if (StringUtils.isNotEmpty(branchRechargeAccount.getMinimumWithdrawalAmount())) {
                colonelAccountRespVO.setMinimumWithdrawalAmount(new BigDecimal(branchRechargeAccount.getMinimumWithdrawalAmount()));
            } else {
                colonelAccountRespVO.setMinimumWithdrawalAmount(BigDecimal.ZERO);
            }
        }
        return colonelAccountRespVO;
    }

    @Override
    public PageResult<AccDcRechargeSettleRespVO> getDcRechargeSettleList(AccDcRechargeSettlePageReqVO rechargeSettlePageReqVO) {
        PageResult<SysDcRespVO> pageResult = dcApi.getPage(rechargeSettlePageReqVO).getCheckedData();
        ArrayList<AccDcRechargeSettleRespVO> list = new ArrayList<>();
        for (SysDcRespVO sysDcRespVO : pageResult.getList()) {
            AccDcRechargeSettleRespVO respVO = new AccDcRechargeSettleRespVO();
            respVO.setDcId(sysDcRespVO.getDcId());
            respVO.setDcName(sysDcRespVO.getDcName());
            // 查询运营商支付平台商户信息
            AccPlatformMerchant platformMerchant = accPlatformMerchantService.getPlatformMerchant(sysDcRespVO.getDcId(), MerchantTypeEnum.DC.getType(), rechargeSettlePageReqVO.getPlatform().getCode());
            // 商户信息都没有还查个毛线
            if (Objects.nonNull(platformMerchant)) {
                // 查询支付平台商户余额
                PayPlatformAccountVO merchantServiceBalance = merchantService.getBalance(platformMerchant.getAltMchNo(), sysDcRespVO.getSysCode(), rechargeSettlePageReqVO.getPlatform());
                respVO.setWithdrawableAmt(merchantServiceBalance.getSettleAmt());
                respVO.setD0BalanceAmt(merchantServiceBalance.getD0BalanceAmt());
            }
            list.add(respVO);
        }
        return PageResult.result(pageResult.getTotal(), list);
    }

    /**
     * 防止依赖循环
     * @return
     */
    IAccAccountFlowService getAccountFlowService() {
        return SpringUtils.getBean(IAccAccountFlowService.class);
    }


    /**
     * 防止依赖循环
     * @return
     */
    IAccWithdrawService getWithdrawService() {
        return SpringUtils.getBean(IAccWithdrawService.class);
    }
}
