package com.zksr.account.client.hlb;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.ImmutableSet;
import com.zksr.account.client.PayClient;
import com.zksr.account.client.hlb.vo.RefundRuleJsonRespVO;
import com.zksr.account.client.hlb.vo.RuleJsonRespVO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderQueryVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.PayMethodEnum;
import com.zksr.common.core.enums.PayOrderStatusRespEnum;
import com.zksr.common.core.enums.PayRefundStatusEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.cert.HlbCertUtil;
import com.zksr.common.core.utils.cert.SM2Utils;
import com.zksr.common.core.utils.html.EscapeUtil;
import com.zksr.system.api.model.dto.HlbPayConfig;
import com.zksr.system.api.model.dto.PayConfigDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:  合利宝小程序支付
 * @date 2024/3/7 18:07
 */
@Data
@Slf4j
public class HlbAppletPayClient implements PayClient {

    // 支付
    private HlbSdkClient client;

    // 支付配置
    private PayConfigDTO config;

    @Override
    public String getChannel() {return PayChannelEnum.HLB_WX_LITE.getCode();}

    @Override
    public String getPlatform() {return PayChannelEnum.HLB.getCode();}

    @Override
    public void setConfig(PayConfigDTO config, String appid) throws Exception {
        this.config = config;
        this.client = new HlbSdkClient();
        /**
         * 初始化支付工具
         */
        HlbPayConfig payConfig = JSON.parseObject(config.getConfig(), HlbPayConfig.class);
        this.client.setPubCert(HlbCertUtil.getX509Certificate(payConfig.getPubCert()));
        this.client.setMerchantPrivateKey(HlbCertUtil.getPrivateKeyStr(payConfig.getMerchantPrivateKey(), payConfig.getPass()));
        this.client.setPrivateKey(HlbCertUtil.getPrivateKeyForSm2N(payConfig.getMerchantPrivateKey(), payConfig.getPass()));
        this.client.setNotifyUrl(payConfig.getNotifyUrl());
        this.client.setMerchantNo(payConfig.getMerchantNo());
        this.client.setAppid(appid);
    }

    @Override
    public PayOrderRespDTO unifiedOrder(PayOrderDTO payOrderDTO) {
        if (PayMethodEnum.wx(payOrderDTO.getMethod())) {
            return this.client.miniPay(payOrderDTO);
        } else if (PayMethodEnum.alipay(payOrderDTO.getMethod())) {
            return this.client.aliAppPay(payOrderDTO);
        }
        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        respDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus()).setMessage("不存在的支付方式");
        return respDTO;
    }

    @Override
    public PayOrderRespDTO parseOrderNotify(Map<String, String> params, String body) {
        PayOrderRespDTO orderRespDTO = new PayOrderRespDTO();

        // 验证签名
        boolean checkSignature = this.checkSignature(params, ImmutableSet.of("rt1_customerNumber", "rt2_orderId", "rt3_systemSerial", "rt4_status", "rt5_orderAmount", "rt6_currency", "rt7_timestamp", "rt8_desc"));
        if (!checkSignature) {
            log.info("验签失败 {}", params);
            orderRespDTO.setStatus(PayOrderStatusRespEnum.FAIL.getStatus())
                    .setMessage("验签失败");
            return orderRespDTO;
        }

        // 合利宝是表单回调, 非POST JSON, 直接从request 里面获取数据
        String status = params.get("rt4_status");
        if (StringPool.SUCCESS.equals(status)) {
            Map<String, String> stringStringMap = EscapeUtil.parseQueryString(params.get("rt8_desc"));
            orderRespDTO.setFlowId(Long.parseLong(params.get("rt2_orderId")))
                    .setOrderNo(stringStringMap.get("orderNo"))
                    .setOutTradeNo(params.get("rt17_outTransactionOrderId"))
                    .setStatus(PayOrderStatusRespEnum.FINISH.getStatus())
                    .setPayPlatform(getPlatform());

            // 查询有没有失败的分账
            if (Objects.nonNull(params.get("ruleJson")) && StringUtils.isNotEmpty(params.get("ruleJson"))) {
                List<RuleJsonRespVO> jsonRespVOS = JSON.parseArray(params.get("ruleJson"), RuleJsonRespVO.class);
                for (RuleJsonRespVO respVO : jsonRespVOS) {
                    if (!StringPool.SUCCESS.equals(respVO.getSplitBillOrderStatus())) {
                        // 加入分账失败记录
                        orderRespDTO.getFailSettle().add(
                                PayOrderRespDTO.SettleDTO.builder()
                                        .accountNo(respVO.getSplitBillMerchantNo())
                                        .amt(respVO.getSplitBillAmount())
                                        .build()
                        );
                    }
                }
            }
        }
        return orderRespDTO;
    }

    @Override
    public PayOrderRespDTO getOrder(PayOrderQueryVO payOrderQueryVO) {
        PayOrderRespDTO orderRespDTO = this.client.getOrder(payOrderQueryVO);
        if (Objects.nonNull(orderRespDTO)) {
            orderRespDTO.setPayPlatform(this.getPlatform());
        }
        return orderRespDTO;
    }


    @Override
    public PayRefundRespDTO unifiedRefund(PayRefundOrderSubmitReqVO reqDTO) {
        return this.client.refund(reqDTO);
    }

    @Override
    public PayRefundRespDTO parseRefundNotify(Map<String, String> params, String body) {
        PayRefundRespDTO refundResp = new PayRefundRespDTO();
        if (StringPool.SUCCESS.equals(params.get("rt5_status"))) {
            // 退款成功
            refundResp.setOrderNo(params.get("rt2_orderId"));
            refundResp.setRefundNo(params.get("rt3_refundOrderId"));
            refundResp.setOutRefundNo(params.get("rt4_systemSerial"));
            refundResp.setStatus(PayRefundStatusEnum.SUCCESS.getStatus());

            // 查询有没有失败的分账
            if (Objects.nonNull(params.get("ruleJson")) && StringUtils.isNotEmpty(params.get("ruleJson"))) {
                List<RefundRuleJsonRespVO> jsonRespVOS = JSON.parseArray(params.get("ruleJson"), RefundRuleJsonRespVO.class);
                for (RefundRuleJsonRespVO respVO : jsonRespVOS) {
                    if (!StringPool.SUCCESS.equals(respVO.getRefundStatus())) {
                        // 加入分账失败记录
                        refundResp.getFailSettle().add(
                                PayRefundRespDTO.SettleDTO.builder()
                                        .accountNo(respVO.getMerchantNo())
                                        .amt(respVO.getRefundAmount())
                                        .build()
                        );
                    }
                }
            }
        } else if (StringPool.FAIL.equals(params.get("rt5_status"))){
            // 退款失败
            refundResp.setOrderNo(params.get("rt2_orderId"));
            refundResp.setRefundNo(params.get("rt3_refundOrderId"));
            refundResp.setRefundTradeNo(params.get("rt3_refundOrderId"));
            refundResp.setOutRefundNo(params.get("rt4_systemSerial"));
            refundResp.setStatus(PayRefundStatusEnum.FAILURE.getStatus());
            refundResp.setErrorMsg(params.get("rt11_desc"));
            if (StringUtils.isEmpty(refundResp.getErrorMsg())) {
                refundResp.setErrorMsg(params.get("retReasonDesc"));
            }
        }
        return refundResp;
    }

    @Override
    public BigDecimal getFeeRate() {
        return this.config.getFeeRate();
    }

    public boolean checkSignature(Map<String, String> signature, Set<String> needSignParams) {
        String resSign = String.valueOf(signature.get("sign"));
        HashMap<String, String> map = new LinkedHashMap<>();
        for (Object key : signature.keySet()) {
            map.put(String.valueOf(key), String.valueOf(signature.get(key)));
        }
        StringBuffer sb = HlbCertUtil.doSignStrAppendSort(map, needSignParams);

        String sign = SM2Utils.sign(sb.toString(), this.client.getMerchantPrivateKey());
        System.out.println(sign);
        return SM2Utils.verify(sb.toString().trim(), resSign, this.client.getPubCert());
    }
}