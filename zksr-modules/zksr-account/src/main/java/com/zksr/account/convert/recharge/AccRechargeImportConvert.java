package com.zksr.account.convert.recharge;

import java.math.BigDecimal;

import com.zksr.account.controller.recharge.excel.RechargeImportExcelRespVO;
import com.zksr.account.controller.recharge.excel.RechargeImportExcelVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccRechargeImport;
import com.zksr.account.controller.recharge.vo.AccRechargeImportRespVO;
import com.zksr.account.controller.recharge.vo.AccRechargeImportSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 后台导入充值 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2025-03-24
*/
@Mapper
public interface AccRechargeImportConvert {

    AccRechargeImportConvert INSTANCE = Mappers.getMapper(AccRechargeImportConvert.class);

    AccRechargeImportRespVO convert(AccRechargeImport accRechargeImport);

    AccRechargeImport convert(AccRechargeImportSaveReqVO accRechargeImportSaveReq);

    PageResult<AccRechargeImportRespVO> convertPage(PageResult<AccRechargeImport> accRechargeImportPage);

    List<RechargeImportExcelRespVO> convertExcelRespVO(List<RechargeImportExcelVO> excelVOList);
}