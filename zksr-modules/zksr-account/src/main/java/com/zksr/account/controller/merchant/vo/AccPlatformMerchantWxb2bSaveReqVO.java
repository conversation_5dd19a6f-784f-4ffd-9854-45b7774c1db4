package com.zksr.account.controller.merchant.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信B2B支付商户配置
 * @date 2024/9/24 14:58
 */
@Data
@ApiModel(description = "微信B2B支付商户配置")
public class AccPlatformMerchantWxb2bSaveReqVO {

    /** 最小账户保留金 */
    @Excel(name = "最小账户保留金")
    @ApiModelProperty(value = "最小账户保留金", required = true)
    private BigDecimal minWithdrawAmt;

    /** 0-未开启,1-已开启 */
    @Excel(name = "0-未开启,1-已开启")
    @ApiModelProperty("0-未开启,1-已开启")
    private Integer autoWithdraw;

    /** 支付秘钥 */
    @Excel(name = "支付秘钥")
    @ApiModelProperty(value = "支付秘钥", required = true)
    private String appKey;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id", required = true)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 商户号 */
    @Excel(name = "商户号")
    @ApiModelProperty(value = "商户号", hidden = true)
    private String altNo;

    /** 技术手续费比例 */
    @ApiModelProperty(value = "技术手续费比例最小0.022, 最大0.004")
//    @DecimalMin(value = "0.0022", message = "技术手续费比例最少0.22%")
//    @DecimalMax(value = "0.004", message = "技术手续费比例最大0.40%")
    private BigDecimal profitRate;

}
