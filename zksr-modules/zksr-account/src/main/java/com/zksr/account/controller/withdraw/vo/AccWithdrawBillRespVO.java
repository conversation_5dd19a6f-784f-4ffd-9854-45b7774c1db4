package com.zksr.account.controller.withdraw.vo;

import lombok.*;
import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 提现对账单对象 acc_withdraw_bill
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@ApiModel("提现对账单 - acc_withdraw_bill Response VO")
public class AccWithdrawBillRespVO {
    private static final long serialVersionUID = 1L;
    /** 提现日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "提现日期")
    @ApiModelProperty(value = "提现日期")
    private Date finishTime;

    /** 提现商户号 */
    @Excel(name = "提现商户号")
    @ApiModelProperty(value = "提现商户号")
    private String altNo;

    /** 发起时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发起时间")
    @ApiModelProperty(value = "发起时间")
    private Date requestTime;

    /** 提现单号 */
    @Excel(name = "提现单号")
    @ApiModelProperty(value = "提现单号")
    private String platformTradeNo;

    /** 提现到账银行卡号 */
    @Excel(name = "提现到账银行卡号")
    @ApiModelProperty(value = "提现到账银行卡号")
    private String bankAccountNo;

    /** 平台提现金额 */
    @Excel(name = "平台提现金额")
    @ApiModelProperty(value = "平台提现金额")
    private BigDecimal platformWithdrawAmt;
}
