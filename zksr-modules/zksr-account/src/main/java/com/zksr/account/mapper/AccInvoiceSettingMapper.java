package com.zksr.account.mapper;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import com.zksr.account.domain.AccInvoiceSetting;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import com.zksr.common.core.enums.MerchantUploadPicStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.database.mapper.BaseMapperX;
import com.zksr.common.database.query.LambdaQueryWrapperX;

import cn.hutool.core.util.ObjectUtil;

@Mapper
public interface AccInvoiceSettingMapper extends BaseMapperX<AccInvoiceSetting> {

}
