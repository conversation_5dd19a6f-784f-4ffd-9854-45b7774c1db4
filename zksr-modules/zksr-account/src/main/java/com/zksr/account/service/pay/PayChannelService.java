package com.zksr.account.service.pay;


import com.zksr.account.client.PayClient;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.UnifiedOrderValidateDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;

/**
 * <AUTHOR>
 * @time 2024/3/7
 * @desc
 */
public interface PayChannelService {

    /**
     * 根据appid 获取支付客户端
     *
     * @param appid 根据appid 获取支付客户端
     * @param sysCode 平台ID
     * @param payWay 支付方式
     * @param orderType 订单方式 {@link com.zksr.common.core.constant.OrderTypeConstants}
     * @return 支付客户端
     */
    PayClient getPayClient(
            String appid,
            Long sysCode,
            String payWay,
            Integer orderType
    );

    /**
     * 根据appid 获取支付客户端
     *
     * @param appid 根据appid 获取支付客户端
     * @param sysCode 平台ID
     * @param payWay 支付方式
     * @param orderType 订单方式 {@link com.zksr.common.core.constant.OrderTypeConstants}
     * @return 支付客户端
     */
    PayClient getPayClient(
            String appid,
            Long sysCode,
            String payWay,
            Integer orderType,
            String platform
    );

    /**
     * 移除缓存配置
     */
    void clear();

    /**
     * 支付前验证
     * @param payClient
     * @param reqVO
     * @return
     */
    UnifiedOrderValidateDTO processPayBeforeValidate(PayClient payClient, PayOrderDTO reqVO);

    /**
     * 退款前验证
     * @param payClient
     * @param reqDTO
     * @return
     */
    UnifiedOrderValidateDTO processRefundBeforeValidate(PayClient payClient, PayRefundOrderSubmitReqVO reqDTO);
}
