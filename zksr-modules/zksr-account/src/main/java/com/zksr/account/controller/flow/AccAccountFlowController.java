package com.zksr.account.controller.flow;

import javax.validation.Valid;

import com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.service.IAccAccountFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.controller.flow.vo.AccAccountFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccAccountFlowSaveReqVO;
import com.zksr.account.controller.flow.vo.AccAccountFlowRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 账户流水Controller
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Api(tags = "管理后台 - 账户流水接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/accountFlow")
public class AccAccountFlowController {

    @Autowired
    private IAccAccountFlowService accAccountFlowService;

    @Autowired
    private IAccountCacheService accountCacheService;

    /**
     * 获取账户流水详细信息
     */
    @ApiOperation(value = "获得账户流水详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{accountFlowId}")
    public CommonResult<AccAccountFlowRespVO> getInfo(@PathVariable("accountFlowId") Long accountFlowId) {
        AccAccountFlow accAccountFlow = accAccountFlowService.getAccAccountFlow(accountFlowId);
        return success(HutoolBeanUtils.toBean(accAccountFlow, AccAccountFlowRespVO.class));
    }

    /**
     * 分页查询账户流水
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得账户流水分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccAccountFlowRespVO>> getPage(@Valid AccAccountFlowPageReqVO pageReqVO) {
        PageResult<AccAccountFlow> pageResult = accAccountFlowService.getAccAccountFlowPage(pageReqVO);
        PageResult<AccAccountFlowRespVO> resultPage = HutoolBeanUtils.toBean(pageResult, AccAccountFlowRespVO.class);
        for (AccAccountFlowRespVO accountFlow : resultPage.getList()) {
            // 渲染门店名称
            MerchantTypeEnum merchantType = MerchantTypeEnum.fromValue(accountFlow.getMerchantType());
            if (merchantType == MerchantTypeEnum.BRANCH) {
                BranchDTO branchDTO = accountCacheService.getBranchDTO(accountFlow.getMerchantId());
                if (Objects.nonNull(branchDTO)) {
                    accountFlow.setMerchantName(branchDTO.getBranchName());
                }
            }
        }
        return success(resultPage);
    }


    /**
     * 分页查询账户流水
     */
    @GetMapping("/getBranchFlowPage")
    @ApiOperation(value = "获取门店账户流水", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.BRANCH_FLOW_LIST)
    @RequiresPermissions(Permissions.BRANCH_FLOW_LIST)
    public CommonResult<PageResult<RechargeConsumeRespVO>> getBranchFlowPage(@Valid AccAccountFlowPageReqVO pageReqVO) {
        pageReqVO.setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField());
        PageResult<RechargeConsumeRespVO> pageResult = accAccountFlowService.getBranchAccountFlowPage(pageReqVO);
        for (RechargeConsumeRespVO accountFlow : pageResult.getList()) {
            BranchDTO branchDTO = accountCacheService.getBranchDTO(accountFlow.getBranchId());
            accountFlow.setBranchName(branchDTO.getBranchName());
        }
        return success(pageResult);
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:flow:add";
        /** 编辑 */
        public static final String EDIT = "account:flow:edit";
        /** 删除 */
        public static final String DELETE = "account:flow:remove";
        /** 列表 */
        public static final String LIST = "account:flow:list";
        /** 列表 */
        public static final String BRANCH_FLOW_LIST = "account:branchFlow:list";
        /** 查询 */
        public static final String GET = "account:flow:query";
        /** 停用 */
        public static final String DISABLE = "account:flow:disable";
        /** 启用 */
        public static final String ENABLE = "account:flow:enable";
    }
}
