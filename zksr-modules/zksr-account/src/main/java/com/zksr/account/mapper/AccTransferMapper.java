package com.zksr.account.mapper;

import com.zksr.common.core.enums.AccountTransferStateEnum;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccTransfer;
import com.zksr.account.controller.transfer.vo.AccTransferPageReqVO;

import java.util.Date;
import java.util.List;


/**
 * 账户转账单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@Mapper
public interface AccTransferMapper extends BaseMapperX<AccTransfer> {
    default PageResult<AccTransfer> selectPage(AccTransferPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccTransfer>()
                    .eqIfPresent(AccTransfer::getTransferId, reqVO.getTransferId())
                    .eqIfPresent(AccTransfer::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccTransfer::getSettleId, reqVO.getSettleId())
                    .eqIfPresent(AccTransfer::getSourceAccountId, reqVO.getSourceAccountId())
                    .eqIfPresent(AccTransfer::getTargetAccountId, reqVO.getTargetAccountId())
                    .eqIfPresent(AccTransfer::getTransferNo, reqVO.getTransferNo())
                    .eqIfPresent(AccTransfer::getTransferAmt, reqVO.getTransferAmt())
                    .eqIfPresent(AccTransfer::getSettleAmt, reqVO.getSettleAmt())
                    .eqIfPresent(AccTransfer::getState, reqVO.getState())
                    .eqIfPresent(AccTransfer::getProcessingTime, reqVO.getProcessingTime())
                    .eqIfPresent(AccTransfer::getFinishTime, reqVO.getFinishTime())
                    .eqIfPresent(AccTransfer::getPlatform, reqVO.getPlatform())
                .orderByDesc(AccTransfer::getTransferId));
    }

    default Long selectCountBySettleId(Long settleId) {
        return selectCount(
            new LambdaQueryWrapperX<AccTransfer>()
                    .eq(AccTransfer::getSettleId, settleId)
        );
    }

    /**
     * 获取重试转账单数据, 30天内创建的转账单
     * @param minId
     * @return
     */
    default List<AccTransfer> selectRetryAccTransfer(Long minId) {
        return selectList(
                new LambdaQueryWrapperX<AccTransfer>()
                        .eq(AccTransfer::getState, AccountTransferStateEnum.PROCESSING.getState())
                        .gt(AccTransfer::getCreateBy, new Date(System.currentTimeMillis() - 30 * (86400L)))
                        .gt(AccTransfer::getTransferId, minId)
                        .last("LIMIT 1000")
        );
    }
}
