package com.zksr.account.service.impl;

import com.zksr.account.domain.AccRechargeSchemeArea;
import com.zksr.account.mapper.AccRechargeSchemeAreaMapper;
import com.zksr.account.service.IAccRechargeSchemeAreaService;
import com.zksr.common.core.pool.StringPool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 储值套餐上架发布城市Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Service
public class AccRechargeSchemeAreaServiceImpl implements IAccRechargeSchemeAreaService {

    @Autowired
    private AccRechargeSchemeAreaMapper accRechargeSchemeAreaMapper;

    @Override
    public void saveSchemeArea(Long rechargeSchemeId, String areaIds) {
        // 先删除数据
        accRechargeSchemeAreaMapper.deleteBySchemeId(rechargeSchemeId);
        // 然后新增数据
        List<AccRechargeSchemeArea> schemeAreas = Arrays.stream(areaIds.split(StringPool.COMMA))
                .map(Long::parseLong)
                .map(areaId -> AccRechargeSchemeArea
                        .builder()
                        .areaId(areaId)
                        .rechargeSchemeId(rechargeSchemeId)
                        .build()
                ).collect(Collectors.toList());
        accRechargeSchemeAreaMapper.insertBatch(schemeAreas);
    }
}
