package com.zksr.account.client.hlb.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 合利宝分账请求
 * @date 2025/2/27 11:08
 */
@Data
public class HlbDelayedSplitReqVO {

    @ApiModelProperty("同一商户号下唯一商户请求分账流水号")
    private String orderId;

    @ApiModelProperty("原收款交易商户请求流水号")
    private String originalOrderId;

    @ApiModelProperty("原交易产品类型")
    private String originalProductCode = "APPPAY";

    @ApiModelProperty("分账字段")
    private List<SplitBillRule> ruleJson;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SplitBillRule {

        @NotEmpty(message = "分账规则收账商户号不能为空")
        private String splitBillMerchantNo;

        @NotNull(message = "分账规则收账金额不能为空")
        private BigDecimal splitBillAmount;
    }
}
