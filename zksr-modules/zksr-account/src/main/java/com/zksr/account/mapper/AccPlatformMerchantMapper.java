package com.zksr.account.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantNameAndKeyDTO;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import com.zksr.common.core.enums.MerchantUploadPicStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 支付平台商户Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Mapper
public interface AccPlatformMerchantMapper extends BaseMapperX<AccPlatformMerchant> {
    default PageResult<AccPlatformMerchant> selectPage(AccPlatformMerchantPageReqVO reqVO) {
        LambdaQueryWrapperX<AccPlatformMerchant> queryWrapperX = new LambdaQueryWrapperX<AccPlatformMerchant>()
                .eqIfPresent(AccPlatformMerchant::getPlatformMerchantId, reqVO.getPlatformMerchantId())
                .eqIfPresent(AccPlatformMerchant::getSysCode, reqVO.getSysCode())
                .eqIfPresent(AccPlatformMerchant::getMerchantType, reqVO.getMerchantType())
                .eqIfPresent(AccPlatformMerchant::getMerchantId, reqVO.getMerchantId())
                .eqIfPresent(AccPlatformMerchant::getAltMchNo, reqVO.getAltMchNo())
                .likeIfPresent(AccPlatformMerchant::getAltMchName, reqVO.getAltMchName())
                .eqIfPresent(AccPlatformMerchant::getPlatform, reqVO.getPlatform())
                .eqIfPresent(AccPlatformMerchant::getAuditStatus, reqVO.getAuditStatus())
                .eqIfPresent(AccPlatformMerchant::getPicStatus, reqVO.getPicStatus())
                .betweenIfPresent(AccPlatformMerchant::getCreateTime, reqVO.getStartTime(), reqVO.getEndTime())
                .eq(AccPlatformMerchant::getMchStatus, NumberPool.INT_ONE)
                .orderByDesc(AccPlatformMerchant::getPlatformMerchantId);
        if (ObjectUtil.isNotEmpty(reqVO.getMerchantTypes())) {
            queryWrapperX.in(AccPlatformMerchant::getMerchantType, reqVO.getMerchantTypes());
        }
        return selectPage(reqVO, queryWrapperX);
    }

    /**
     * 获取平台进件商户信息
     *
     * @param merchantId   商户ID
     * @param merchantType 商户类型    {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform     支付平台    {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    default AccPlatformMerchant selectPlatformMerchant(Long merchantId, String merchantType, String platform) {
        return selectOne(
                Wrappers.lambdaQuery(AccPlatformMerchant.class)
                        .eq(AccPlatformMerchant::getMerchantId, merchantId)
                        .eq(AccPlatformMerchant::getMerchantType, merchantType)
                        .eq(AccPlatformMerchant::getPlatform, platform)
                        .eq(AccPlatformMerchant::getMchStatus, NumberPool.INT_ONE)
        );
    }

    /**
     * 获取平台进件商户信息
     * @param merchantId   商户ID
     * @param merchantType 商户类型    {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform     支付平台    {@link com.zksr.common.core.enums.PayChannelEnum}
     * @param sysCode      平台商编号
     * @return
     */
    default AccPlatformMerchant selectPlatformMerchant(Long merchantId, String merchantType, String platform, Long sysCode) {
        return selectOne(
                new LambdaQueryWrapperX<AccPlatformMerchant>()
                        .eq(AccPlatformMerchant::getMerchantId, merchantId)
                        .eq(AccPlatformMerchant::getMerchantType, merchantType)
                        .eq(AccPlatformMerchant::getPlatform, platform)
                        .eqIfPresent(AccPlatformMerchant::getSysCode, sysCode)
                        .eq(AccPlatformMerchant::getMchStatus, NumberPool.INT_ONE)
        );
    }

    default long selectCountByMerchant(Long merchantId, String merchantType, String platform) {
        return selectCount(
                Wrappers.lambdaQuery(AccPlatformMerchant.class)
                        .eq(AccPlatformMerchant::getMerchantId, merchantId)
                        .eq(AccPlatformMerchant::getMerchantType, merchantType)
                        .eq(AccPlatformMerchant::getPlatform, platform)
                        .eq(AccPlatformMerchant::getMchStatus, NumberPool.INT_ONE)
        );
    }

    default AccPlatformMerchant selectByThirdOrderNo(String orderNo) {
        return selectOne(
                Wrappers.lambdaQuery(AccPlatformMerchant.class)
                        .eq(AccPlatformMerchant::getThirdOrderNo, orderNo)
                        .eq(AccPlatformMerchant::getMchStatus, NumberPool.INT_ONE)
        );
    }

    default List<AccPlatformMerchant> selectProceeingStatusList(Long minId) {
        return selectList(
                new LambdaQueryWrapperX<AccPlatformMerchant>()
                        .eq(AccPlatformMerchant::getMchStatus, NumberPool.INT_ONE)
                        .gt(AccPlatformMerchant::getPlatformMerchantId, minId)
                        .and(and -> and.in(AccPlatformMerchant::getAuditStatus,
                                        MerchantRegisterStateEnum.INIT.getState()
                                )
                                .or()
                                .in(AccPlatformMerchant::getEditStatus,
                                        MerchantRegisterStateEnum.INIT.getState()
                                )
                                .or()
                                .in(AccPlatformMerchant::getPicStatus,
                                        MerchantUploadPicStateEnum.DOING
                                )
                        )
                        .orderByAsc(
                                AccPlatformMerchant::getPlatformMerchantId
                        )
                        .last("LIMIT 100")

        );
    }

    List<PlatformMerchantNameAndKeyDTO> getPlatformMerchantNameAndKey(@Param("merchantType") String merchantType,@Param("sysCode") Long sysCode,@Param("payPlatform") String payPlatform);

    default List<Long> getMerchantIdByAltNo(String merchantType, String altNo, Long sysCode){
        return selectList(
                new LambdaQueryWrapperX<AccPlatformMerchant>()
                        .eq(AccPlatformMerchant::getMerchantType, merchantType)
                        .eq(AccPlatformMerchant::getAltMchNo, altNo)
                        .eq(AccPlatformMerchant::getSysCode, sysCode)
                        .eq(AccPlatformMerchant::getMchStatus, NumberPool.INT_ONE)
                        .select(AccPlatformMerchant::getMerchantId)
        ).stream()
                .map(AccPlatformMerchant::getMerchantId)  // 提取 MerchantId
                .distinct()  // 去重
                .collect(Collectors.toList());  // 收集到 List
    }
}
