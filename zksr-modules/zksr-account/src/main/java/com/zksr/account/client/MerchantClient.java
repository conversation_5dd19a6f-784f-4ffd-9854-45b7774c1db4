package com.zksr.account.client;

import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bSaveReqVO;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.common.core.constant.OrderTypeConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户 || 进件操作
 * @date 2024/4/23 14:01
 */
public interface MerchantClient {

    /**
     * 获取账户余额
     * @param merchantNo    商户编号
     * @return
     */
    PayPlatformAccountVO getAccountBalance(String merchantNo);

    /**
     * 获取进件商户
     * @param orderNo    订单号
     * @param merchantNo 子商编
     * @return  进件商户信息
     */
    PayPlatformMerchantVO getRegisterMerchant(String orderNo, String merchantNo);

    /**
     * 获取商户信息
     * @param merchantNo  商编
     * @return
     */
    default PayPlatformMerchantVO getMerchant(String merchantNo) { return null; }
    /**
     * 注册商户, 商户进件
     * @param saveReqVO 进件信息
     * @return 进件信息结果
     */
    default PlatformMerchantRegisterSaveRespVO registerMerchant(AccPlatformMerchantRegisterReqVO saveReqVO) { return null; }

    /**
     * 解析 进件 回调数据
     * @param params HTTP 回调接口 content type 为 application/x-www-form-urlencoded 的所有参数
     * @param body HTTP 回调接口的 request body
     * @return 进件结果
     */
    default PlatformMerchantRegisterSaveRespVO parseRegisterNotify(Map<String, String> params, String body) { return null; }

    /**
     * 新增商户资质
     * @param uploadSaveReqVO   资质信息
     */
    default PlatformMerchantUploadSaveRespVO uploadPic(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) { return null; }

    /**
     * 更新商户资质
     * @param uploadSaveReqVO   资质信息
     */
    default PlatformMerchantUploadSaveRespVO changePic(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) { return null; }

    /**
     * 修改商户信息
     * @param saveReqVO 进件信息
     * @return 进件信息结果
     */
    default PlatformMerchantRegisterSaveRespVO updateMerchant(PlatformMerchantRegisterSaveReqVO saveReqVO, AccPlatformMerchant platformMerchant) { return null; }

    /**
     * 查询商户资质上传结果
     * @param uploadSaveReqVO   资质信息
     */
    default PlatformMerchantUploadSaveRespVO queryUploadPicStatus(PlatformMerchantUploadSaveReqVO uploadSaveReqVO) { return null; }

    /**
     * 查询商户变更状态
     * @param merchantNo    进件商户号
     * @param editOrderNo   修改订单号
     * @return
     */
    default PlatformMerchantUpdateStatusRespVO queryUploadMerchantStatus(String merchantNo, String editOrderNo) { return null; }

    /**
     * (微信B2B) 添加分账方
     * @param platformMerchant  第三方账户
     */
    default boolean addSharing(AccPlatformMerchant platformMerchant) {
        return true;
    };

    /**
     * (微信B2B) 微信提现配置
     * @param saveReqVO  提现配置
     */
    default void updateB2bWithdraw(AccPlatformMerchantWxb2bSaveReqVO saveReqVO) {}


    @ApiModel(description = "客户端参数")
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class ClientParam {

        @ApiModelProperty("平台商编号")
        private Long sysCode;

        @ApiModelProperty("支付平台")
        private String platform;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            ClientParam that = (ClientParam) o;
            return Objects.equals(sysCode, that.sysCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(sysCode);
        }
    }
}
