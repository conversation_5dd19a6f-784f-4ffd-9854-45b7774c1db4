package com.zksr.account.controller.transfer.vo;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 交易对账单明细单对象 acc_transfer_bill_order
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@ApiModel("交易对账单明细单 - acc_transfer_bill_order分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccTransferBillOrderPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;
    /**
     * 交易账单ID
     */
    @ApiModelProperty(value = "交易账单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long transferBillId;

    /**
     * 下单开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = YYYY_MM_DD)
    @ApiModelProperty(value = "下单开始时间")
    private Date transferStartTime;

    /**
     * 下单结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = YYYY_MM_DD)
    @ApiModelProperty(value = "下单结束时间")
    private Date transferEndTime;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "订单号")
    private String platformTradeNo;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态 0-正常 1-异常")
    private Integer state;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型 0-销售单 1-退款单")
    private Integer orderType;

    @ApiModelProperty(value = "入驻商")
    private Long supplierId;

    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;
}
