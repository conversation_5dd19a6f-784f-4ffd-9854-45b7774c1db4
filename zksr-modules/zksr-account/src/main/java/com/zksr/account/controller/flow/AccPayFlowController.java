package com.zksr.account.controller.flow;

import javax.validation.Valid;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.service.IAccPayFlowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import com.zksr.account.controller.flow.vo.AccPayFlowPageReqVO;
import com.zksr.account.controller.flow.vo.AccPayFlowSaveReqVO;
import com.zksr.account.controller.flow.vo.AccPayFlowRespVO;
import com.zksr.common.core.web.page.TableDataInfo;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 支付流水Controller
 *
 * <AUTHOR>
 * @date 2024-03-10
 */
@Api(tags = "管理后台 - 支付流水接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/payFlow")
public class AccPayFlowController {
    @Autowired
    private IAccPayFlowService accPayFlowService;

    /**
     * 新增支付流水
     */
    @ApiOperation(value = "新增支付流水", httpMethod = "POST", notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "支付流水", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccPayFlowSaveReqVO createReqVO) {
        return success(accPayFlowService.insertAccPayFlow(createReqVO));
    }

    /**
     * 修改支付流水
     */
    @ApiOperation(value = "修改支付流水", httpMethod = "PUT", notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "支付流水", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AccPayFlowSaveReqVO updateReqVO) {
            accPayFlowService.updateAccPayFlow(updateReqVO);
        return success(true);
    }

    /**
     * 删除支付流水
     */
    @ApiOperation(value = "删除支付流水", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.DELETE)
    @RequiresPermissions(Permissions.DELETE)
    @Log(title = "支付流水", businessType = BusinessType.DELETE)
    @DeleteMapping("/{payFlowIds}")
    public CommonResult<Boolean> remove(@PathVariable Long[] payFlowIds) {
        accPayFlowService.deleteAccPayFlowByPayFlowIds(payFlowIds);
        return success(true);
    }

    /**
     * 获取支付流水详细信息
     */
    @ApiOperation(value = "获得支付流水详情", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{payFlowId}")
    public CommonResult<AccPayFlowRespVO> getInfo(@PathVariable("payFlowId") Long payFlowId) {
        AccPayFlow accPayFlow = accPayFlowService.getAccPayFlow(payFlowId);
        return success(HutoolBeanUtils.toBean(accPayFlow, AccPayFlowRespVO.class));
    }

    /**
     * 分页查询支付流水
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得支付流水分页列表", httpMethod = "GET", notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccPayFlowRespVO>> getPage(@Valid AccPayFlowPageReqVO pageReqVO) {
        PageResult<AccPayFlow> pageResult = accPayFlowService.getAccPayFlowPage(pageReqVO);
        return success(HutoolBeanUtils.toBean(pageResult, AccPayFlowRespVO.class));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:flow:add";
        /** 编辑 */
        public static final String EDIT = "account:flow:edit";
        /** 删除 */
        public static final String DELETE = "account:flow:remove";
        /** 列表 */
        public static final String LIST = "account:flow:list";
        /** 查询 */
        public static final String GET = "account:flow:query";
        /** 停用 */
        public static final String DISABLE = "account:flow:disable";
        /** 启用 */
        public static final String ENABLE = "account:flow:enable";
    }
}
