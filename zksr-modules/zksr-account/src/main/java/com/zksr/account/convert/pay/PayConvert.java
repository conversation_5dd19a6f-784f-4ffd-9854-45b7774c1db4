package com.zksr.account.convert.pay;

import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.domain.AccPayFlow;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.system.api.model.dto.MideaPayConfig;
import com.zksr.system.api.partnerConfig.dto.MideaPayConfigDTO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/5/29
 * @desc
 */
@Mapper
public interface PayConvert {

    PayConvert INSTANCE = Mappers.getMapper(PayConvert.class);

    List<OrderSettlementDTO> convert(List<HdfkPaySaveRespVO.OrderSettlementDTO> settlementDTOS);

    MideaPayConfig convert2MideaPayConfig(MideaPayConfigDTO payConfig);

    @Mappings({
            @Mapping(source = "payFlow.sysCode", target = "sysCode"),
            @Mapping(source = "payFlow.orderType", target = "orderType"),
            @Mapping(source = "payFlow.payFlowId", target = "flowId"),
            @Mapping(source = "payFlow.useOrderNo", target = "useOrderNoRefund"),
            @Mapping(source = "payFlow.appid", target = "appid"),
            @Mapping(source = "payFlow", target = "payFlowDTO"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSetRefundOrderSubmitReqVO(@MappingTarget PayRefundOrderSubmitReqVO reqDTO, AccPayFlow payFlow);

    PayFlowDTO convertPayFlowDTO(AccPayFlow payFlow);
}
