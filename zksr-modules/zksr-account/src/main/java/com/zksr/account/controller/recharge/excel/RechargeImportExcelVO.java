package com.zksr.account.controller.recharge.excel;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.annotation.ExcelHead;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/25 10:43
 */
@Data
@ExcelHead(
        sheetName = "门店储值导入",
        description = "填表说明：\n" +
        "1、门店ID选填, 优先使用门店ID；\n" +
        "2、门店名称必填, 门店名称可能匹配多条记录, 优先使用最早的记录；\n" +
        "3、充值金额必填 \n" +
        "4、导入数据时请保留当前说明信息"
)
public class RechargeImportExcelVO {

    @Excel(name = "门店ID" , headerColor = IndexedColors.RED)
    private String branchId;

    @Excel(name = "门店名称" , headerColor = IndexedColors.RED)
    private String branchName;

    @Excel(name = "充值金额" , headerColor = IndexedColors.RED)
    private String rechargeAmt;
}
