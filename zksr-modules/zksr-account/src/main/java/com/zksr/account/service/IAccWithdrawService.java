package com.zksr.account.service;

import javax.validation.*;

import com.zksr.account.api.withdraw.vo.BranchWithdrawReqVO;
import com.zksr.account.controller.withdraw.vo.AccDirectSettleWithdrawSaveReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawAuditVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawRespVO;
import com.zksr.account.domain.AccWithdrawFlow;
import com.zksr.account.model.transfer.dto.TransferSettleRespDTO;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawSaveReqVO;

import java.math.BigDecimal;

/**
 * 账户提现单Service接口
 *
 * <AUTHOR>
 * @date 2024-04-12
 */
public interface IAccWithdrawService {

    /**
     * 新增账户提现单
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    AccWithdraw insertAccWithdraw(AccWithdrawSaveReqVO createReqVO);

    /**
     * 钱包余额提现
     *
     * @param createReqVO 创建信息
     * @return 结果
     */
    AccWithdraw insertWalletWithdraw(AccWithdrawSaveReqVO createReqVO);

    /**
     * 直接计算提现单, 提现单只是做记录, 不存在转账功能
     * @param createReqVO
     * @return
     */
    AccWithdraw insertDirectWithdraw(AccDirectSettleWithdrawSaveReqVO createReqVO);

    /**
     * 获得账户提现单
     *
     * @param withdrawId 账户提现单
     * @return 账户提现单
     */
    public AccWithdraw getAccWithdraw(Long withdrawId);

    /**
     * 获取提现单
     * @param transferNo    提现单号
     * @return
     */
    AccWithdraw getAccWithdraw(String transferNo);

    /**
     * 获得账户提现单分页
     *
     * @param pageReqVO 分页查询
     * @return 账户提现单分页
     */
    PageResult<AccWithdrawRespVO> getAccWithdrawPage(AccWithdrawPageReqVO pageReqVO);

    /**
     * 处理提现单
     * @param withdraw
     */
    TransferSubmitRespDTO processWithdraw(AccWithdraw withdraw);

    /**
     * 处理钱包资金提现单
     * @param withdraw
     */
    TransferSubmitRespDTO processWalletWithdraw(AccWithdraw withdraw);


    /**
     * (1) 提现 - 转账处理中
     * @param transferNo    提现单号
     */
    void processWithdrawTransferProcessing(String transferNo);

    /**
     * (2) 提现 - 转账失败
     * @param transferNo    提现单号
     * @param failMessage   失败信息
     */
    void processWithdrawTransferFail(String transferNo, String failMessage);

    /**
     * (3) 提现 - 转账成功
     * @param transferNo    提现单号
     */
    void processWithdrawTransferSuccess(String transferNo);

    /**
     * (4) 结算 - 结算发起中
     * @param withdrawFlow  结算流水
     */
    void processWithdrawProcessing(AccWithdrawFlow withdrawFlow);

    /**
     * (5) 结算 - 结算失败
     * @param withdrawFlow  结算流水
     */
    void processWithdrawFail(AccWithdrawFlow withdrawFlow);

    /**
     * (6) 结算 - 结算成功
     * @param withdrawFlow  结算流水
     */
    void processWithdrawSuccess(AccWithdrawFlow withdrawFlow);

    /**
     * 拒绝提现单
     * @param withdrawAudit 审核信息
     */
    void rejectWithdraw(AccWithdrawAuditVO withdrawAudit);

    /**
     * 结算提现单
     * @param accWithdraw
     */
    TransferSettleRespDTO settle(AccWithdraw accWithdraw);

    /**
     * 直接结算, 不转账
     * @param accWithdraw   提现单
     */
    void directSettle(AccWithdraw accWithdraw);

    /**
     * 获取账户已提现金额
     * @param accountId
     * @return
     */
    BigDecimal getTotalWithdrawAmt(Long accountId);

}
