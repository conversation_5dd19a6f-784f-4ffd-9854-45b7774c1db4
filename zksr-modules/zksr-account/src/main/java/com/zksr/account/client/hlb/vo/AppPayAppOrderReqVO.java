package com.zksr.account.client.hlb.vo;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableSet;
import com.zksr.common.core.enums.SignatureType;
import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/7 18:19
 */
@Data
public class AppPayAppOrderReqVO {

    private String P1_bizType;
    private String P2_customerNumber;
    private String P3_orderId;
    private String P4_goodsName;
    private String P5_orderAmount;
    private String P6_currency;
    private String P7_orderIp;
    private String P8_notifyUrl;
    private String P9_isRaw;
    private String P10_appPayType;
    private String P11_limitCreditPay;
    private String P12_deviceInfo;
    private String P13_appid;
    private String P14_desc;
    private String P16_goodsTag;
    private String P17_guid;
    private String P18_marketingRule;
    private String P19_identity;
    private String splitBillType;
    private String ruleJson;
    private String timeExpire;
    private String terminalSysBindNo;
    private String sceneInfo;
    private String nonRawMode;
    private String successToUrl;
    private String encryptionKey;
    /**
     * 特殊参数,排除签名
     */

    /**
     * 签名类型(不参与签名)
     */
    private SignatureType signatureType = SignatureType.MD5;
    private String sign;

    /**
     * 需要加签的属性参数,要求加签的参数空值也签名
     * 看接口文档
     */
    public static final Set<String> NEED_SIGN_PARAMS = ImmutableSet.of(
            "P1_bizType",
            "P2_customerNumber",
            "P3_orderId",
            "P4_goodsName",
            "P5_orderAmount",
            "P6_currency",
            "P7_orderIp",
            "P8_notifyUrl",
            "P9_isRaw",
            "P10_appPayType",
            "P11_limitCreditPay",
            "P12_deviceInfo",
            "P13_appid",
            "P14_desc"
        );
    /**
     * 需要加/解密的属性参数
     */
    public static final Set<String> NEED_ENCRYPT_OR_DECRYPT_PARAMS = new HashSet<>();//ImmutableSet.of("ruleJson");

}
