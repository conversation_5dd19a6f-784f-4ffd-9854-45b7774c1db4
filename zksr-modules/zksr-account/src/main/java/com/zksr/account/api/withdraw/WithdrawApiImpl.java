package com.zksr.account.api.withdraw;

import com.zksr.account.api.withdraw.dto.AccWithdrawBillDTO;
import com.zksr.account.api.withdraw.dto.SaveWithdrawDTO;
import com.zksr.account.api.withdraw.dto.WithdrawDTO;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.api.withdraw.vo.BranchWithdrawReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawRespVO;
import com.zksr.account.convert.withdraw.AccWithdrawConvert;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccBranchWithdrawService;
import com.zksr.account.service.IAccWithdrawBillService;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.InnerAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 提现api
 * @date 2024/5/8 9:21
 */
@RestController
@InnerAuth
@ApiIgnore
public class WithdrawApiImpl implements WithdrawApi{

    @Autowired
    private IAccWithdrawService withdrawService;

    @Autowired
    private IAccBranchWithdrawService branchWithdrawService;

    @Autowired
    private IAccWithdrawBillService accWithdrawBillService;

    @Autowired
    private IAccAccountService accountService;

    @Override
    public CommonResult<Long> createColonelWithdraw(SaveWithdrawDTO saveWithdraw) {
        AccAccount account = accountService.getAccAccount(saveWithdraw.getAccountId());
        if (PayChannelEnum.isWallet(account.getPlatform())) {
            AccWithdraw withdraw = withdrawService.insertWalletWithdraw(AccWithdrawConvert.INSTANCE.convertReqVO(saveWithdraw));
            return CommonResult.success(withdraw.getWithdrawId());
        } else {
            AccWithdraw withdraw = withdrawService.insertAccWithdraw(AccWithdrawConvert.INSTANCE.convertReqVO(saveWithdraw));
            return CommonResult.success(withdraw.getWithdrawId());
        }
    }

    @Override
    public CommonResult<Boolean> createBranchWithdraw(BranchWithdrawReqVO withdrawReqVO) {
        branchWithdrawService.insertWithdraw(withdrawReqVO);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public CommonResult<WithdrawDTO> getWithdrawInfo(Long withdrawId) {
        return CommonResult.success(AccWithdrawConvert.INSTANCE.convertDTO(withdrawService.getAccWithdraw(withdrawId)));
    }

    @Override
    public CommonResult<PageResult<WithdrawDTO>> getWithdrawPage(AccWithdrawPageReqVO pageReqVO) {
        PageResult<AccWithdrawRespVO> accWithdrawPage = withdrawService.getAccWithdrawPage(pageReqVO);
        return CommonResult.success(AccWithdrawConvert.INSTANCE.convertDTOPage(accWithdrawPage));
    }

    @Override
    public CommonResult<Boolean> insertAccWithdrawBillBatch(List<AccWithdrawBillDTO> accWithdrawBillDTO) {
        accWithdrawBillService.insertAccWithdrawBillBatch(accWithdrawBillDTO);
        return CommonResult.success(Boolean.TRUE);
    }

    @Override
    public int countWithdrawBillsByDateAndAltNo(String dataFormatted, String altNo) {
        return accWithdrawBillService.countWithdrawBillsByDateAndAltNo(dataFormatted,altNo);
    }

    @Override
    public void deleteWithdrawBillsByDateAndAltNo(String dataFormatted, String altNo) {
        accWithdrawBillService.deleteWithdrawBillsByDateAndAltNo(dataFormatted,altNo);
    }
}
