package com.zksr.account.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.account.domain.AccBalance;
import com.zksr.common.database.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 门店账户余额接口
 */
@Mapper
public interface AccBalanceMapper extends BaseMapperX<AccBalance> {

    // 跟据门店id列表查询门店账户余额数据
    default List<AccBalance> selectBalanceByBranchIdList(List<Long> branchIdList) {
        return selectList(Wrappers.lambdaQuery(AccBalance.class).in(AccBalance::getBranchId, branchIdList));
    }

    // 跟据门店id查询门店账户余额
    default AccBalance selectAccBalanceByBranchId(Long sysCode, Long branchId) {
        return selectOne(Wrappers.lambdaQuery(AccBalance.class).eq(AccBalance::getBranchId, branchId)
                .eq(AccBalance::getSysCode, sysCode));
    }

}
