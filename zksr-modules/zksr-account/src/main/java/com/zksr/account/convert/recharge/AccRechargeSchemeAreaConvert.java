package com.zksr.account.convert.recharge;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
* 储值套餐上架发布城市 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2025-02-11
*/
@Mapper
public interface AccRechargeSchemeAreaConvert {

    AccRechargeSchemeAreaConvert INSTANCE = Mappers.getMapper(AccRechargeSchemeAreaConvert.class);
}