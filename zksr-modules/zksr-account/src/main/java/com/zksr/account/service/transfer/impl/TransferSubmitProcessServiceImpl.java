package com.zksr.account.service.transfer.impl;

import cn.hutool.core.date.DateUtil;
import com.zksr.account.domain.AccTransferFlow;
import com.zksr.account.model.transfer.dto.TransferSubmitRespDTO;
import com.zksr.account.service.IAccTransferFlowService;
import com.zksr.account.service.IAccWithdrawService;
import com.zksr.account.service.transfer.TransferSubmitProcessService;
import com.zksr.common.core.enums.AccountTransferStateEnum;
import com.zksr.common.core.utils.SpringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 转账流程控制
 * @date 2024/3/11 15:30
 */
@Service
public class TransferSubmitProcessServiceImpl implements TransferSubmitProcessService {

    @Autowired
    private IAccTransferFlowService transferFlowService;

    /**
     * 转账处理中
     * @param transferSubmitRespDTO
     */
    @Override
    @Transactional
    public void notifyProcessing(TransferSubmitRespDTO transferSubmitRespDTO) {
        AccTransferFlow transferFlow = transferFlowService.getTransferFlowByTransferNo(transferSubmitRespDTO.getTransferNo());
        transferFlow.setProcessingTime(DateUtil.date())
                    .setOutFlowNo(transferSubmitRespDTO.getOutTradeNo())
                    .setState(AccountTransferStateEnum.PROCESSING.getState())
                    ;
        transferFlowService.updateAccTransferFlow(transferFlow);
        // 提现处理中
        getWithdrawService().processWithdrawTransferProcessing(transferFlow.getTransferNo());
    }

    /**
     * 转账成功
     * @param transferSubmitRespDTO
     */
    @Override
    public void notifySuccess(TransferSubmitRespDTO transferSubmitRespDTO) {
        AccTransferFlow transferFlow = transferFlowService.getTransferFlowByTransferNo(transferSubmitRespDTO.getTransferNo());
        transferFlow.setFinishTime(DateUtil.date())
                .setState(AccountTransferStateEnum.FINISH.getState())
        ;
        transferFlowService.updateAccTransferFlow(transferFlow);
        // 退回账户冻结, 修改提现单状态
        getWithdrawService().processWithdrawTransferSuccess(transferFlow.getTransferNo());
    }

    /**
     * 转账失败
     * @param transferSubmitRespDTO
     */
    @Override
    @Transactional
    public void notifyFail(TransferSubmitRespDTO transferSubmitRespDTO) {
        AccTransferFlow transferFlow = transferFlowService.getTransferFlowByTransferNo(transferSubmitRespDTO.getTransferNo());
        transferFlow.setFinishTime(DateUtil.date())
                .setErrorReason(transferSubmitRespDTO.getMsg())
                .setState(AccountTransferStateEnum.FAIL.getState())
        ;
        transferFlowService.updateAccTransferFlow(transferFlow);
        // 退回账户冻结, 修改提现单状态
        getWithdrawService().processWithdrawTransferFail(transferFlow.getTransferNo(), transferSubmitRespDTO.getMsg());

    }

    IAccWithdrawService getWithdrawService() {
        return SpringUtils.getBean(IAccWithdrawService.class);
    }
}
