package com.zksr.account.mapper;

import com.zksr.account.domain.AccBillFile;
import com.zksr.common.database.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商户账单文件备份Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Mapper
public interface AccBillFileMapper extends BaseMapperX<AccBillFile> {
    List<String> checkBillFileRecordExists(@Param("altNo") String altNo,@Param("data") String data);

    void deleteBillFileRecord(@Param("billFileId") String billFileId);
}
