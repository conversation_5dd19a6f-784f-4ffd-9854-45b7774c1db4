package com.zksr.account.service.impl;

import java.util.List;
import java.util.Objects;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zksr.account.api.platformMerchant.vo.AccInvoiceSettingSaveReqVO;
import com.zksr.account.convert.account.AccInvoiceSetttingConvert;
import com.zksr.account.domain.AccInvoiceSetting;
import com.zksr.account.mapper.AccInvoiceSettingMapper;
import com.zksr.account.service.IAccInVoiceSettingService;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.database.query.LambdaQueryWrapperX;

import lombok.extern.slf4j.Slf4j;

/**
 * 支付平台商户Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class AccInVoiceSettingServiceImpl implements IAccInVoiceSettingService {

    @Autowired
    private AccInvoiceSettingMapper accInvoiceSettingMapper;
    /**
     * 新增发票设置
     */
    @Override
    public Long saveAccInvoiceSetting(@Valid AccInvoiceSettingSaveReqVO  createReqVO) {
        AccInvoiceSetting accPlatformMerchant = AccInvoiceSetttingConvert.INSTANCE.convert(createReqVO);
        
        if (Objects.isNull(accPlatformMerchant.getSysCode())) {
            throw new ServiceException("sysCode 必填");
        }
        
        if (Objects.isNull(accPlatformMerchant.getMerchantId())) {
            throw new ServiceException("merchantId 必填");
        }
        
        if (Objects.isNull(accPlatformMerchant.getMerchantType())) {
            throw new ServiceException("merchantType 必填");
        }
        if (Objects.isNull(accPlatformMerchant.getPlatform())) {
            throw new ServiceException("platform 必填");
        }
        AccInvoiceSetting accInvoiceSetting = this.getAccInvoiceSetting(createReqVO.getMerchantId(), createReqVO.getMerchantType(), createReqVO.getPlatform(),createReqVO.getSysCode());
        
        if (Objects.nonNull(accInvoiceSetting)) {
            // 更新
            accPlatformMerchant.setAccInvoiceSettingId(accInvoiceSetting.getAccInvoiceSettingId());
            accInvoiceSettingMapper.updateById(accPlatformMerchant);
        } else {
            accInvoiceSettingMapper.insert(accPlatformMerchant);
        }
        // 返回
        return accPlatformMerchant.getAccInvoiceSettingId();
    }


    @Override
    public AccInvoiceSetting getAccInvoiceSetting(Long merchantId, String merchantType, String platform, Long sysCode) {
        return accInvoiceSettingMapper.selectOne(new LambdaQueryWrapperX<AccInvoiceSetting>()
        .eq(AccInvoiceSetting::getMerchantId, merchantId)
        .eq(AccInvoiceSetting::getMerchantType, merchantType)
        .eq(AccInvoiceSetting::getPlatform, platform)
        .eq(AccInvoiceSetting::getSysCode, sysCode));
    }
    
    @Override
    public List<AccInvoiceSetting> getAccInvoiceSettingList(Long merchantId, String merchantType, Long sysCode) {
        return accInvoiceSettingMapper.selectList(new LambdaQueryWrapperX<AccInvoiceSetting>()
        .eq(AccInvoiceSetting::getMerchantId, merchantId)
        .eq(AccInvoiceSetting::getMerchantType, merchantType)
        .eq(AccInvoiceSetting::getSysCode, sysCode));
    }
}
