package com.zksr.account.client.mideapay.vo.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MideaPayRefundNotifyRespVO {
    /**
     * 接口名称
     */
    @JsonProperty("service")
    private String service;

    /**
     * 接口版本号
     */
    @JsonProperty("version")
    private String version;

    /**
     * 平台商户号
     */
    @JsonProperty("partner")
    private String partner;

    /**
     * 商户网站使用的编码格式，目前只能取值UTF-8
     */
    @JsonProperty("input_charset")
    private String inputCharset;

    /**
     * 签名方式
     */
    @JsonProperty("sign_type")
    private String signType;

    /**
     * 签名
     */
    @JsonProperty("sign")
    private String sign;

    /**
     * 商户退款单号
     */
    @JsonProperty("out_refund_no")
    private String outRefundNo;

    /**
     * 平台创建退款交易的订单号
     */
    @JsonProperty("refund_no")
    private String refundNo;

    /**
     * 商户单号
     */
    @JsonProperty("out_trade_no")
    private String outTradeNo;

    /**
     * 退款单创建时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("refund_accept_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String refundAcceptTime;

    /**
     * 美的支付通知商户时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("notify_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String notifyTime;

    /**
     * 支付金额的货币类型：
     * CNY：人民币
     */
    @JsonProperty("currency_type")
    private String currencyType;

    /**
     * 退款总金额
     */
    @JsonProperty("refund_total_amount")
    private String refundTotalAmount;

    /**
     * 退款金额
     */
    @JsonProperty("refund_amount")
    private String refundAmount;

    /**
     * 营销金额退款
     */
    @JsonProperty("refund_market_amount")
    private String refundMarketAmount;

    /**
     * 退款交易执行的时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("pay_time")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String payTime;

    /**
     * 订单状态,
     * NOT_EXIST：订单不存在
     * WAIT_PAY：未退款
     * PAYING：退款中
     * SUCCESS：退款成功
     * FAIL：退款失败
     */
    @JsonProperty("refund_status")
    private String refundStatus;

    /**
     * 状态描述
     */
    @JsonProperty("refund_status_info")
    private String refundStatusInfo;

    /**
     * 到账状态
     */
    @JsonProperty("arrival_status")
    private String arrivalStatus;

    /**
     * 到账时间，格式为yyyyMMddHHmmss
     */
    @JsonProperty("arrivalTime")
    @JsonFormat(pattern = "yyyyMMddHHmmss")
    private String arrivalTime;

    /**
     * 商户自定义信息，原值返回给商户
     */
    @JsonProperty("attach")
    private String attach;

    /**
     * 优惠券参数
     */
    @JsonProperty("coupon_params")
    private String couponParams;

    /**
     * 美的支付下游渠道响应信息
     */
    @JsonProperty("channel_rsp_info")
    private String channelRspInfo;

}
