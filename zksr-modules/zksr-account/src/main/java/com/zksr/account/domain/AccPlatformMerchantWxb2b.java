package com.zksr.account.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 支付平台商户, 微信B2B平台商户扩展信息对象 acc_platform_merchant_wxb2b
 *
 * <AUTHOR>
 * @date 2024-09-24
 */
@TableName(value = "acc_platform_merchant_wxb2b")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccPlatformMerchantWxb2b extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** $column.columnComment */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long wxB2bPlatformMerchantId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 最小账户保留金 */
    @Excel(name = "最小账户保留金")
    private BigDecimal minWithdrawAmt;

    /** 0-未开启,1-已开启 */
    @Excel(name = "0-未开启,1-已开启")
    private Integer autoWithdraw;

    /** 支付秘钥 */
    @Excel(name = "支付秘钥")
    private String appKey;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 支付秘钥 */
    @Excel(name = "支付秘钥")
    private String profile;

    /** 微信昵称 */
    @Excel(name = "微信昵称")
    private String nickName;

    /** 支付手续费费率 */
    @Excel(name = "支付手续费费率")
    private BigDecimal profitRate;
}
