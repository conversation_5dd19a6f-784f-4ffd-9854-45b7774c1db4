package com.zksr.account.client.wx;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.account.api.platformMerchant.vo.*;
import com.zksr.account.client.MerchantClient;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.security.utils.WxHttpUtil;
import com.zksr.account.controller.merchant.vo.AccPlatformMerchantWxb2bSaveReqVO;
import com.zksr.account.domain.AccPlatformMerchant;
import com.zksr.account.model.merchant.vo.PayPlatformAccountVO;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.common.core.enums.MerchantRegisterStateEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.utils.RedisWxTokenUtil;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.file.api.file.FileApi;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信b2b商家助手
 * @date 2024/7/31 17:18
 */
@Data
@Slf4j
@ApiModel(description = "微信b2b商家助手支付")
@NoArgsConstructor
public class WxB2bMerchantClient implements MerchantClient {

    /**
     * 小程序appid
     */
    private String appid;

    public WxB2bMerchantClient(String appid) {
        this.appid = appid;
    }

    @Override
    public PayPlatformAccountVO getAccountBalance(String merchantNo) {
        // 微信B2B支付暂未实现余额查询
        return new PayPlatformAccountVO();
    }

    @Override
    public PayPlatformMerchantVO getRegisterMerchant(String orderNo, String merchantNo) {
        // 小程序accessToken
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(this.appid);
        if (StringUtils.isEmpty(accessToken)) {
            throw exception(PLATFORM_MERCHANT_ACCESS_TOKEN_NOT_EXIST);
        }
        // 实现B2B支付商户信息查询
        String requestUrl = StringUtils.format("https://api.weixin.qq.com/retail/B2b/retailgetmchorder?access_token={}", accessToken);

        /**
         * {
         *     "errcode": 0,
         *     "errmsg": "OK",
         *     "list": [
         *         {
         *             "status": 6,
         *             "inner_resp": {
         *                 "status": 0,
         *                 "code": "SUCCESS",
         *                 "msg": "success",
         *                 "inner_registration_id": "1451723715952777549944",
         *                 "out_registration_id": "regorder202408151759082438917395",
         *                 "registration_status": "REGISTRATION_STATUS_SUCCESS",
         *                 "sub_merchant_registration_status": {
         *                     "applyment_state": "FINISH",
         *                     "applyment_state_desc": "完成",
         *                     "sign_state": "SIGNED",
         *                     "sub_mchid": "1683318270",
         *                     "audit_detail": [],
         *                     "out_request_no": "1451723715952777549944",
         *                     "applyment_id": 2000002529645565
         *                 },
         *                 "sub_res_offer_id": "8002138225",
         *                 "sub_offer_id": "1450270638"
         *             }
         *         }
         *     ],
         *     "total": 1
         * }
         */
        // 没有进件单号, 或者进件单号有吴
        if (StringUtils.isEmpty(orderNo) || !orderNo.contains("regorder")) {
            throw exception(PLATFORM_MERCHANT_THIRD_NO_NONE_EXIST);
        }

        // 发起请求
        String request = JSON.toJSONString(MapUtil.of("out_registration_id", orderNo));
        log.info("查询微信B2B商户开通状态请求, request={}", request);
        String result = WxHttpUtil.post(requestUrl, request);
        log.info("查询微信B2B商户开通状态结果, response={}", result);

        // 处理返回结果
        JSONObject resultObj = JSON.parseObject(result);
        if (resultObj.containsKey("list")) {
            JSONArray jsonArray = resultObj.getJSONArray("list");
            if (jsonArray.isEmpty()) {
                throw exception(PLATFORM_MERCHANT_WX_MERCHANT_NONE_EXIST);
            }
            JSONObject merchant = jsonArray.getJSONObject(0).getJSONObject("inner_resp");
            Integer status = jsonArray.getJSONObject(0).getInteger("status");

            /**
             * 0-初始化
             * 1-资料校验中
             * 2-待账户验证
             * 3-审核中
             * 4-已驳回
             * 5-待签约
             * 6-完成
             * 6-已冻结
             * 8-已作废
             * 9-完成前平台额外准备
             *
             * CHECKING: 资料校验中
             * ACCOUNT_NEED_VERIFY: 待账户验证
             * AUDITING: 审核中
             * REJECTED: 已驳回
             * NEED_SIGN: 待签约
             * FINISH: 完成
             * FROZEN: 已冻结
             * CANCELED: 已作废
             *
             */
            //String applymentState = merchant.getString("applyment_state");

            PayPlatformMerchantVO payPlatformMerchantVO = new PayPlatformMerchantVO();
            // 资料校验中
            if (status == 0 || status == 1 || status == 3) {
                payPlatformMerchantVO.setState(MerchantRegisterStateEnum.AUDITING);
            }
            // 待账户验证
            if (status == 2) {
                payPlatformMerchantVO.setOutLink(merchant.getJSONObject("sub_merchant_registration_status").getString("legal_validation_url"));
                payPlatformMerchantVO.setState(MerchantRegisterStateEnum.ACCOUNT_NEED_VERIFY);
            }
            // 待签约
            if (status == 5) {
                payPlatformMerchantVO.setOutLink(merchant.getJSONObject("sub_merchant_registration_status").getString("sign_url"));
                payPlatformMerchantVO.setState(MerchantRegisterStateEnum.UNSIGNED);
            }
            // 完成
            if (status == 6 || status == 9) {
                payPlatformMerchantVO.setState(MerchantRegisterStateEnum.AUDITED);

                // 商户信息
                JSONObject subMerchantRegistrationStatus = merchant.getJSONObject("sub_merchant_registration_status");

                // 商户号
                String subMchid = subMerchantRegistrationStatus.getString("sub_mchid");
                payPlatformMerchantVO.setMerchantNo(subMchid);
            }
            // 失败
            if (status == 4 || status == 7 || status == 8) {
                // 商户信息
                JSONObject subMerchantRegistrationStatus = merchant.getJSONObject("sub_merchant_registration_status");
                if (Objects.nonNull(subMerchantRegistrationStatus)) {
                    JSONArray auditDetail = subMerchantRegistrationStatus.getJSONArray("audit_detail");
                    ArrayList<String> errString = new ArrayList<>();
                    if (Objects.nonNull(auditDetail)) {
                        for (int i = 0; i < auditDetail.size(); i++) {
                            JSONObject rejectReason = auditDetail.getJSONObject(i);
                            if (rejectReason.containsKey("reject_reason")) {
                                errString.add(rejectReason.getString("reject_reason"));
                            }
                        }
                    }
                    if (!errString.isEmpty()) {
                        String msg = StringUtils.join(errString, StringPool.COMMA);
                        if (msg.length() > 200) {
                            msg = StrUtil.maxLength(msg, 100);
                        }
                        payPlatformMerchantVO.setMsg(msg);
                    }
                }
                payPlatformMerchantVO.setState(MerchantRegisterStateEnum.OVERRULE);
            }
            return payPlatformMerchantVO;
        } else {
            throw new ServiceException(resultObj.getString("errmsg"));
        }
    }

    @Override
    public PlatformMerchantRegisterSaveRespVO registerMerchant(AccPlatformMerchantRegisterReqVO reqVO) {
        // 微信支付不支持业务员个人进件
        if (MerchantTypeEnum.fromValue(reqVO.getMerchantType()) == MerchantTypeEnum.COLONEL) {
            throw exception(PLATFORM_WX_MERCHANT_CAN_NOT_PRIVATE);
        }
        // 小程序accessToken
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(this.appid);
        if (StringUtils.isEmpty(accessToken)) {
            throw exception(PLATFORM_MERCHANT_ACCESS_TOKEN_NOT_EXIST);
        }

        FileApi fileApi = SpringUtils.getBean(FileApi.class);

        WxB2bPlatformMerchantRegisterSaveReqVO saveReqVO = reqVO.getWxB2bPlatformMerchantRegisterSaveReqVO();

        HashMap<String, Object> req = new HashMap<>();
        // 0-默认即大陆身份证，1-大陆身份证，2-其他国家或地区居民护照，3-中国香港居民来往内地通行证，4-中国澳门居民–来往内地通行证，5-中国台湾居民–来往大陆通行证
        req.put("id_doc_type_num", saveReqVO.getIdDocTypeNum());
        //  1、主体为"小微/个人卖家"时，不填。
        //  2、主体为"个体工商户/企业"时，请上传营业执照。
        //  3、主体为"党政、机关及事业单位/其他组织"时，请上传登记证书。
        req.put("merchant_shortname", saveReqVO.getMerchantShortname());   //  商户名缩写
        req.put("organization_type", saveReqVO.getOrganizationType());   //  个人-0，企业-1

        // 经营者/法人身份证信息
        HashMap<String, Object> idCardInfo = new HashMap<>();
        {
            WxB2bPlatformMerchantRegisterSaveReqVO.IdCardInfo cardInfo = saveReqVO.getIdCardInfo();

            // 下载base64 文件
            String cardCopy = fileApi.downloadByBase64(cardInfo.getIdCardCopy()).getCheckedData();
            String idCardNational = fileApi.downloadByBase64(cardInfo.getIdCardNational()).getCheckedData();

            idCardInfo.put("id_card_copy", WxUtils.uploadB2bPicByBase64(cardInfo.getIdCardCopy(), cardCopy, accessToken)); // 身份证人像面照片id, 通过上传商户资料api获取
            idCardInfo.put("id_card_national", WxUtils.uploadB2bPicByBase64(cardInfo.getIdCardNational(), idCardNational, accessToken)); // 身份证国徽面照片id, 通过上传商户资料api获取
            idCardInfo.put("id_card_name", cardInfo.getIdCardName()); // 身份证姓名
            idCardInfo.put("id_card_number", cardInfo.getIdCardNumber()); // 身份证号码
            idCardInfo.put("id_card_valid_time_begin", cardInfo.getIdCardValidTimeBegin()); // 身份证有效期开始日期
            idCardInfo.put("id_card_valid_time", cardInfo.getIdCardValidTime()); // 身份证有效期结束日期
            idCardInfo.put("id_card_address", cardInfo.getIdCardAddress()); // 身份证地址
        }

        // 结算信息
        HashMap<String, Object> accountInfo = new HashMap<>();
        WxB2bPlatformMerchantRegisterSaveReqVO.AccountInfo voAccountInfo = saveReqVO.getAccountInfo();
        accountInfo.put("bank_account_type", voAccountInfo.getBankAccountType());   //账户类型，若主体为企业/党政、机关及事业单位/其他组织，可填"74"，表示对公账户；若主体为"小微/个人卖家"，可填"75"，表示对私账户；若主体为个体工商户，可填"74"或"75"
        accountInfo.put("account_bank", voAccountInfo.getAccountBank());    //开户银行，比如"工商银行"
        accountInfo.put("account_name", voAccountInfo.getAccountName());    //开户名称
        accountInfo.put("bank_address_code", voAccountInfo.getBankAddressCode());    //开户银行省市编码，例如"110000"
        accountInfo.put("bank_branch_id", voAccountInfo.getBankBranchId());    //开户银行联行号，17 家直连银行无需填写，如为其他银行，开户银行全称（含支行）和开户银行联行号二选一
        accountInfo.put("bank_name", voAccountInfo.getBankName());    //开户银行全称
        accountInfo.put("account_number", voAccountInfo.getAccountNumber());    //银行帐号

        // 商户管理员信息
        HashMap<String, Object> contactInfo = new HashMap<>();
        {
            WxB2bPlatformMerchantRegisterSaveReqVO.ContactInfo voContactInfo = saveReqVO.getContactInfo();
            contactInfo.put("contact_type", voContactInfo.getContactType());    //主体为"小微/个人卖家"，可填"65"; 主体为"个体工商户/企业/党政、机关及事业单位/其他组织"，可填"65"表示经营者/法人，或者"66"表示负责人
            contactInfo.put("contact_name", voContactInfo.getContactName());    //超级管理员姓名

            // 不是法人 就需要上传管理员身份证
            if (!voContactInfo.getContactType().equals("65")) {
                // 下载base64 文件
                String cardCopy = fileApi.downloadByBase64(voContactInfo.getContactIdDocCopy()).getCheckedData();
                String idCardNational = fileApi.downloadByBase64(voContactInfo.getContactIdDocCopyBack()).getCheckedData();

                contactInfo.put("contact_id_doc_type", voContactInfo.getContactIdDocType());    //超级管理员证件类型 当超级管理员类型是经办人时，请上传超级管理员证件类型
                contactInfo.put("contact_id_card_number", voContactInfo.getContactIdCardNumber());    //超级管理员身份证件号码
                contactInfo.put("contact_id_doc_copy", WxUtils.uploadB2bPicByBase64(voContactInfo.getContactIdDocCopy(), cardCopy, accessToken));    //超级管理员证件正面照片id，当超级管理员类型是经办人时，请上传超级管理员证件的正面照片
                contactInfo.put("contact_id_doc_copy_back", WxUtils.uploadB2bPicByBase64(voContactInfo.getContactIdDocCopyBack(), idCardNational, accessToken));    //超级管理员证件反面照片,当超级管理员类型是经办人时，请上传超级管理员证件的正面照片
                contactInfo.put("contact_id_doc_period_begin", voContactInfo.getContactIdDocPeriodBegin());    //超级管理员证件有效期开始时间 当超级管理员类型是经办人时，请上传证件有效期开始时间
                contactInfo.put("contact_id_doc_period_end", voContactInfo.getContactIdDocPeriodEnd());    //级管理员证件有效期结束时间 当超级管理员类型是经办人时，请上传证件有效期结束时间
                contactInfo.put("business_authorization_letter", voContactInfo.getBusinessAuthorizationLetter());    //业务办理授权函。1、当超级管理员类型是经办人时，请上传业务办理授权函。2、请参照示例图打印业务办理授权函，全部信息需打印，不支持手写商户信息，并加盖公章
            }
            contactInfo.put("mobile_phone", voContactInfo.getMobilePhone());    // 超级管理员手机,
            contactInfo.put("contact_email", voContactInfo.getContactEmail());    // 超级管理员邮箱,主体类型为"小微商户/个人卖家"可选填，其他主体需必填,
        }

        // 营业执照
        HashMap<String, Object> businessLicense = new HashMap<>();
        {
            WxB2bPlatformMerchantRegisterSaveReqVO.BusinessLicense voBusinessLicense = saveReqVO.getBusinessLicense();

            // 下载base64 文件
            String businessLicenseCopy = fileApi.downloadByBase64(voBusinessLicense.getBusinessLicenseCopy()).getCheckedData();
            if (StringUtils.isNotEmpty(voBusinessLicense.getBusinessLicenseCopy())) {
                businessLicense.put("business_license_copy", WxUtils.uploadB2bPicByBase64(voBusinessLicense.getBusinessLicenseCopy(), businessLicenseCopy, accessToken));    //证件扫描件图片id，可通过上传商户资料 api 获取
            }
            businessLicense.put("business_license_number", voBusinessLicense.getBusinessLicenseNumber());    //证件注册号
            businessLicense.put("merchant_name", voBusinessLicense.getMerchantName());    //商户名称
            businessLicense.put("legal_person", voBusinessLicense.getLegalPerson());    //经营者/法定代表人姓名
            businessLicense.put("company_address", voBusinessLicense.getCompanyAddress());    //注册地址，主体为"党政、机关及事业单位/其他组织"时必填，请填写登记证书的注册地址
            businessLicense.put("business_time", voBusinessLicense.getBusinessTime());    //营业期限，主体为"党政、机关及事业单位/其他组织"时必填。
            businessLicense.put("cert_type", voBusinessLicense.getCertType());    //1、主体为"政府机关/事业单位/社会组织"时，请上传登记证书类型。2、主体为"个体工商户/企业"时，不填。当主体为事业单位时，填枚举值："CERTIFICATE_TYPE_2388", 表示事业单位法人证书；当主体为政府机关，填枚举值："CERTIFICATE_TYPE_2389"，表示统一社会信用代码证书
        }
        req.put("id_card_info", idCardInfo);
        req.put("account_info", accountInfo);
        req.put("contact_info", contactInfo);
        req.put("business_license", businessLicense);

        String request = JSON.toJSONString(req);
        log.info("发起微信B2B商户进件请求, request={}", request);
        //String result = "{\"errcode\":0,\"errmsg\":\"OK\",\"order_no\":\"regorder202409201513182173761554\"}";
        String result = WxHttpUtil.post(StringUtils.format("https://api.weixin.qq.com/retail/B2b/retailregistermch?access_token={}", accessToken), request);
        log.info("发起微信B2B商户进件结果, response={}", result);
        JSONObject resultObj = JSON.parseObject(result);

        if (resultObj.containsKey("order_no")) {
            return PlatformMerchantRegisterSaveRespVO
                    .builder()
                    .msg(resultObj.getString("errmsg"))
                    .orderNo(resultObj.getString("order_no"))
                    .auditStatus(MerchantRegisterStateEnum.UNSIGNED.getState())
                    .build();
        } else {
            return PlatformMerchantRegisterSaveRespVO
                    .builder()
                    .msg(resultObj.getString("errmsg"))
                    .auditStatus(MerchantRegisterStateEnum.OVERRULE.getState())
                    .build();
        }
    }

    @Override
    public boolean addSharing(AccPlatformMerchant platformMerchant) {
        // 小程序accessToken
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(this.appid);
        if (StringUtils.isEmpty(accessToken)) {
            throw exception(PLATFORM_MERCHANT_ACCESS_TOKEN_NOT_EXIST);
        }
        // 添加分账方
        String addSharingUrl = StringUtils.format("https://api.weixin.qq.com/retail/B2b/addprofitsharingaccount?access_token={}", accessToken);
        HashMap<String, String> sharingData = new HashMap<>();
        sharingData.put("env", "0");
        sharingData.put("profit_sharing_relation_type", "RELATION_TYPE_OTHERS");
        sharingData.put("payee_type", "PAYEE_TYPE_EXTERNAL_MERCHANT");
        if (NumberUtil.isNumber(platformMerchant.getAltMchNo())) {
            // 纯数字是商户号
            sharingData.put("payee_type", "PAYEE_TYPE_EXTERNAL_MERCHANT");
        } else {
            // 否则是openid对私
            sharingData.put("payee_type", "PAYEE_TYPE_EXTERNAL_USER");
        }
        sharingData.put("payee_id", platformMerchant.getAltMchNo());

        // 商户名称
        if (StringUtils.isNotEmpty(platformMerchant.getAltMchName())) {
            sharingData.put("payee_name", platformMerchant.getAltMchName());
        }

        // 重复也添加, 不讲究结果, 只讲究在分账方列表里面就行
        String request = JSON.toJSONString(sharingData);
        log.info("发起微信B2B添加分账方, request={}", request);
        String result = WxHttpUtil.post(addSharingUrl, request);
        log.info("发起微信B2B添加分账方结果, response={}", result);

        JSONObject jsonObject = JSONObject.parseObject(result);
        return jsonObject.getInteger("errcode") == 0 || jsonObject.getInteger("errcode").intValue() == 210004;
    }

    @Override
    public void updateB2bWithdraw(AccPlatformMerchantWxb2bSaveReqVO saveReqVO) {
        // 小程序accessToken
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(this.appid);
        if (StringUtils.isEmpty(accessToken)) {
            throw exception(PLATFORM_MERCHANT_ACCESS_TOKEN_NOT_EXIST);
        }
        // 添加分账方
        HashMap<String, String> signData = new HashMap<>();
        signData.put("mchid", saveReqVO.getAltNo());
        signData.put("type", "1");
        signData.put("retain_amt", saveReqVO.getMinWithdrawAmt().multiply(new BigDecimal("100")).toString());

        // 是否开启自动提现，枚举值 1：开启自动提现功能 2：关闭自动提现功能
        if (saveReqVO.getAutoWithdraw() == 0) {
            signData.put("status", "2");
        } else {
            signData.put("status", "1");
        }
        String signStr = JSON.toJSONString(signData);
        String paySig = calcPaySig("/retail/B2b/setautowithdraw", signStr, saveReqVO.getAppKey());
        String url = String.format("https://api.weixin.qq.com/retail/B2b/setautowithdraw?access_token=%s&pay_sig=%s", accessToken, paySig);

        log.info("请求微信b2b修改提现配置参数={}", signStr);
        String response = WxHttpUtil.post(url, signStr);
        log.info("请求微信b2b修改提现配置结果={}", response);
        JSONObject resultObj = JSON.parseObject(response);
        if (resultObj.getInteger("errcode") != 0) {
            throw new ServiceException(resultObj.getString("errmsg"));
        }
    }

    /**
     * 计算 pay_sig 签名
     *
     * @param uri       当前请求的 API 的 URI 部分，不带 query_string。
     * @param postBody  HTTP POST 数据包体。
     * @param appKey    对应环境的 AppKey。
     * @return          计算出的支付请求签名 pay_sig。
     */
    public static String calcPaySig(String uri, String postBody, String appKey) {
        try {
            String needSignMsg = uri + '&' + postBody;
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(appKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] hash = sha256Hmac.doFinal(needSignMsg.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("计算 HMAC 时发生错误", e);
        }
    }

    /**
     * 计算用户登录态签名signature。
     *
     * @param postBody     HTTP POST的数据包体。
     * @param sessionKey   当前用户有效的session_key，参考auth.code2Session接口。
     * @return             计算出的用户登录态签名signature。
     */
    public static String calcSignature(String postBody, String sessionKey) {
        try {
            String needSignMsg = postBody;
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(sessionKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256Hmac.init(secretKey);
            byte[] hash = sha256Hmac.doFinal(needSignMsg.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xFF & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("计算 HMAC 时发生错误", e);
        }
    }
}
