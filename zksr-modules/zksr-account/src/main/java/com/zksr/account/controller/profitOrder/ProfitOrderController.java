/*
package com.zksr.account.controller.profitOrder;

import com.zksr.account.model.profitOrder.dto.DivideOrderRepDto;
import com.zksr.account.model.profitOrder.vo.MideaDivideOrderReq;
import com.zksr.account.service.ProfitOrderService;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@Api(tags = "交易服务 - 多级分账", produces = "application/json")
@RestController
@RequestMapping("/profitOrder")
@Validated
public class ProfitOrderController {

    @Autowired
    private ProfitOrderService profitOrderService;

    @ApiOperation(value = "提交多级分账订单", notes = StringPool.PERMISSIONS_FIX+ Permissions.PROFIT_ORDER)
    //@RequiresPermissions(Permissions.PROFIT_ORDER)
    @PostMapping("/submit")
    public CommonResult<DivideOrderRepDto> submitProfitOrder(@RequestBody MideaDivideOrderReq reqVO) {
        DivideOrderRepDto respVO = profitOrderService.profitOrder(reqVO);
        return success(respVO);
    }

    */
/**
     * 权限字符
     *//*

    public static class Permissions {
        */
/** 多级分账 *//*

        public static final String PROFIT_ORDER = "account:profit:order";
    }

}*/
