package com.zksr.account.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.zksr.account.api.withdraw.vo.BranchWithdrawReqVO;
import com.zksr.account.controller.withdraw.vo.AccWithdrawAuditVO;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccWithdraw;
import com.zksr.account.mapper.AccWithdrawMapper;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.service.IAccAccountFlowService;
import com.zksr.account.service.IAccAccountService;
import com.zksr.account.service.IAccBranchWithdrawService;
import com.zksr.account.service.IAccountCacheService;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.enums.WithdrawStateEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.WithdrawUtil;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.partnerPolicy.dto.WithdrawalSettingPolicyDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.WITHDRAW_BALANCE_ERR;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 处理门店充值余额提现
 * @date 2025/2/22 10:07
 */
@Service
@Slf4j
public class AccBranchWithdrawServiceImpl implements IAccBranchWithdrawService {

    @Autowired
    private AccWithdrawMapper accWithdrawMapper;

    @Autowired
    private IAccAccountService accountService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private IAccountCacheService accountCacheService;

    @Autowired
    private IAccAccountFlowService accountFlowService;

    @Autowired
    private AccountMqProducer accountMqProducer;

    @Override
    @Transactional
    @DistributedLock(lockName = RedisLockConstants.LOCK_BRANCH_WALLET, condition = "#withdrawReqVO.branchId", tryLock = true)
    public AccWithdraw insertWithdraw(BranchWithdrawReqVO withdrawReqVO) {
        // 储值余额
        AccAccount account = accountService.getAccountByMerchantIdAndType(withdrawReqVO.getBranchId(), MerchantTypeEnum.BRANCH.getType(), PayChannelEnum.WALLET.getCode(), NumberPool.INT_ZERO);
        AccAccount giveAccount = accountService.getAccountByMerchantIdAndType(withdrawReqVO.getBranchId(), MerchantTypeEnum.BRANCH.getType(), PayChannelEnum.WALLET.getCode(), NumberPool.INT_ONE);
        // 有效可提现余额
        BigDecimal validAmt = account.getValidAccountAmt();
        if (NumberUtil.isGreater(withdrawReqVO.getWithdrawAmt(), validAmt)) {
            throw exception(WITHDRAW_BALANCE_ERR);
        }
        // 门店只能对应一个业务城市, 一个业务城市只对应一个主运营商
        BranchDTO branchDTO = accountCacheService.getBranchDTO(account.getMerchantId());
        AreaDTO areaDTO = accountCacheService.getAreaDTO(branchDTO.getAreaId());
        // 插入
        AccWithdraw accWithdraw = new AccWithdraw();
        //accWithdraw.
        String uniqueNumber =  WithdrawUtil.getWithdrawNo(account.getMerchantType(), redisService.getUniqueNumber(CacheConstants.WITHDRAW_SEQUENCE));
        // 填充提现参数
        accWithdraw.setMerchantType(account.getMerchantType())
                .setMerchantId(account.getMerchantId())
                // 出账方为运营商ID
                .setBsiMchNo(areaDTO.getDcId().toString())
                .setAltMchNo(withdrawReqVO.getAccountNo())
                .setAltMchName(withdrawReqVO.getAccountName())
                .setBankName(withdrawReqVO.getBankName())
                .setBankAccountNo(withdrawReqVO.getAccountNo())
                .setAccountId(account.getAccountId())
                .setPlatform(account.getPlatform())
                .setTransferType(StringPool.TWO)
                .setApplyAmt(withdrawReqVO.getWithdrawAmt())
                .setWithdrawAmt(withdrawReqVO.getWithdrawAmt())
                .setTransferAmt(BigDecimal.ZERO)
                .setState(WithdrawStateEnum.INIT.getState())
                .setTransferNo(uniqueNumber)
                .setFee(BigDecimal.ZERO)
                .setApplyTip(withdrawReqVO.getApplyTip())
        ;
        // 计算提现手续费
        WithdrawalSettingPolicyDTO withdrawalSetting = accountCacheService.getWithdrawalSetting(account.getSysCode());
        if (Objects.nonNull(withdrawalSetting) && StringUtils.isNotEmpty(withdrawalSetting.getBranchWithdrawRate())) {
            BigDecimal free = accWithdraw.getWithdrawAmt().multiply(new BigDecimal(withdrawalSetting.getBranchWithdrawRate()).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
            accWithdraw.setFee(free);
            accWithdraw.setOperFee(BigDecimal.ZERO);
            accWithdraw.setWithdrawAmt(accWithdraw.getApplyAmt().subtract(free));
            accWithdraw.setTransferAmt(accWithdraw.getApplyAmt().subtract(free));
        }
        // 不用计算手续费, 回调以后会计算
        accWithdrawMapper.insert(accWithdraw);
        // 冻结提现金额, 本金
        {
            AccAccountFlow accountFlow = new AccAccountFlow();
            accountFlow.setAccountId(account.getAccountId())
                    .setSysCode(accWithdraw.getSysCode())
                    .setBusiFrozenAmt(accWithdraw.getApplyAmt())
                    .setBusiType(AccountBusiType.WITHDRAW_FROZEN.getType())
                    .setBusiId(accWithdraw.getWithdrawId())
                    .setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField())
                    .setMerchantId(account.getMerchantId())
                    .setMerchantType(account.getMerchantType())
                    .setAccountType(account.getAccountType())
                    .setPlatform(account.getPlatform())
            ;
            accountFlowService.insertAccAccountFlow(accountFlow);
            accountFlowService.processFlow(accountFlow);
        }
        // 赠送金额, 全部冻结
        if (Objects.nonNull(giveAccount)) {
            AccAccountFlow accountFlow = new AccAccountFlow();
            accountFlow.setAccountId(giveAccount.getAccountId())
                    .setSysCode(accWithdraw.getSysCode())
                    .setBusiFrozenAmt(giveAccount.getWithdrawableAmt())
                    .setBusiType(AccountBusiType.WITHDRAW_FROZEN.getType())
                    .setBusiId(accWithdraw.getWithdrawId())
                    .setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField())
                    .setMerchantId(giveAccount.getMerchantId())
                    .setMerchantType(giveAccount.getMerchantType())
                    .setAccountType(giveAccount.getAccountType())
                    .setPlatform(giveAccount.getPlatform())
            ;
            accountFlowService.insertAccAccountFlow(accountFlow);
            accountFlowService.processFlow(accountFlow);
        }
        return accWithdraw;
    }

    @Override
    @Transactional
    public void rejectWithdraw(AccWithdrawAuditVO withdrawAudit) {
        // 修改提现单状态
        AccWithdraw withdraw = accWithdrawMapper.selectById(withdrawAudit.getWithdrawId());
        AccWithdraw update = new AccWithdraw();
        update.setState(WithdrawStateEnum.REJECT.getState())
                .setWithdrawId(withdraw.getWithdrawId())
                .setRejectTime(DateUtil.date())
                .setApproveTime(DateUtil.date())
                .setRejectReason(withdrawAudit.getRemark())
                .setVoucher(withdrawAudit.getVoucher())
        ;
        LambdaQueryWrapperX<AccWithdraw> qw = new LambdaQueryWrapperX<>();
        qw.eq(AccWithdraw::getState, WithdrawStateEnum.INIT.getState());
        qw.eq(AccWithdraw::getWithdrawId, withdraw.getWithdrawId());
        int updated = accWithdrawMapper.update(update, qw);
        if (updated > 0) {
            AccAccount account = accountService.getAccAccount(withdraw.getAccountId());
            // 本金退回
            {
                AccAccountFlow accountFlow = new AccAccountFlow();
                accountFlow.setAccountId(withdraw.getAccountId())
                        .setSysCode(withdraw.getSysCode())
                        .setBusiFrozenAmt(BigDecimal.ZERO.subtract(withdraw.getApplyAmt()))
                        .setBusiType(AccountBusiType.WITHDRAW_FAIL_FROZEN.getType())
                        .setBusiId(withdraw.getWithdrawId())
                        .setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField())
                        .setMerchantId(withdraw.getMerchantId())
                        .setMerchantType(withdraw.getMerchantType())
                        .setPlatform(withdraw.getPlatform())
                        .setBusiNo(withdraw.getTransferNo())
                ;
                accountFlowService.insertAccAccountFlow(accountFlow);
                accountFlowService.processFlow(accountFlow);
            }
            // 退回全部赠金冻结
            AccAccount giveAccount = accountService.getAccountByMerchantIdAndType(account.getMerchantId(), account.getMerchantType(), account.getPlatform(), NumberPool.INT_ONE);
            if (Objects.nonNull(giveAccount)) {
                AccAccountFlow accountFlow = new AccAccountFlow();
                accountFlow.setAccountId(giveAccount.getAccountId())
                        .setSysCode(withdraw.getSysCode())
                        .setBusiFrozenAmt(BigDecimal.ZERO.subtract(giveAccount.getFrozenAmt()))
                        .setBusiType(AccountBusiType.WITHDRAW_FAIL_FROZEN.getType())
                        .setBusiId(withdraw.getWithdrawId())
                        .setBusiFields(AccountBusiTypeField.FROZEN_AMT.getField())
                        .setMerchantId(withdraw.getMerchantId())
                        .setMerchantType(withdraw.getMerchantType())
                        .setAccountType(giveAccount.getAccountType())
                        .setPlatform(withdraw.getPlatform())
                        .setBusiNo(withdraw.getTransferNo())
                ;
                accountFlowService.insertAccAccountFlow(accountFlow);
                accountFlowService.processFlow(accountFlow);
            }
        }
    }

    @Override
    @Transactional
    public void processWithdraw(AccWithdrawAuditVO withdrawAudit) {
        //推送第三方储值提现信息
        List<Long> syncFlowIdList = new ArrayList<>();

        // 修改提现单状态
        AccWithdraw withdraw = accWithdrawMapper.selectById(withdrawAudit.getWithdrawId());
        AccWithdraw update = new AccWithdraw();
        update.setState(WithdrawStateEnum.FINISH.getState())
                .setWithdrawId(withdraw.getWithdrawId())
                .setTransferState(WithdrawStateEnum.TRANSFER_SUCCESS.getState())
                .setTransferInitTime(DateUtil.date())
                .setTransferFinishTime(DateUtil.date())
                .setApproveTime(DateUtil.date())
                .setSettleFinishTime(DateUtil.date())
                .setSettleState(WithdrawStateEnum.SETTLE_SUCCESS.getState())
                .setSettleMsg("结算成功")
                .setVoucher(withdrawAudit.getVoucher())
        ;
        LambdaQueryWrapperX<AccWithdraw> qw = new LambdaQueryWrapperX<>();
        qw.eq(AccWithdraw::getState, WithdrawStateEnum.INIT.getState());
        qw.eq(AccWithdraw::getWithdrawId, withdraw.getWithdrawId());
        int updated = accWithdrawMapper.update(update, qw);
        if (updated > 0) {
            String busiFields = StringUtils.join(ListUtil.toList(AccountBusiTypeField.FROZEN_AMT.getField(), AccountBusiTypeField.WITHDRAWABLE_AMT.getField()), StringPool.COMMA);
            AccAccount account = accountService.getAccAccount(withdraw.getAccountId());
            // 扣除提现本金
            {
                AccAccountFlow accountFlow = new AccAccountFlow();
                accountFlow.setAccountId(withdraw.getAccountId())
                        .setSysCode(withdraw.getSysCode())
                        .setBusiWithdrawableAmt(BigDecimal.ZERO.subtract(withdraw.getApplyAmt()))
                        .setBusiFrozenAmt(BigDecimal.ZERO.subtract(withdraw.getApplyAmt()))
                        .setBusiType(AccountBusiType.WITHDRAW_SUCCESS.getType())
                        .setBusiId(withdraw.getWithdrawId())
                        .setBusiFields(busiFields)
                        .setMerchantId(withdraw.getMerchantId())
                        .setMerchantType(withdraw.getMerchantType())
                        .setPlatform(withdraw.getPlatform())
                        .setBusiNo(withdraw.getTransferNo())
                ;
                accountFlowService.insertAccAccountFlow(accountFlow);
                accountFlowService.processFlow(accountFlow);

                syncFlowIdList.add(accountFlow.getAccountFlowId());
            }
            // 扣除全部赠送金额
            AccAccount giveAccount = accountService.getAccountByMerchantIdAndType(account.getMerchantId(), account.getMerchantType(), account.getPlatform(), NumberPool.INT_ONE);
            if (Objects.nonNull(giveAccount)) {
                AccAccountFlow accountFlow = new AccAccountFlow();
                accountFlow.setAccountId(giveAccount.getAccountId())
                        .setSysCode(withdraw.getSysCode())
                        .setBusiWithdrawableAmt(BigDecimal.ZERO.subtract(giveAccount.getWithdrawableAmt()))
                        .setBusiFrozenAmt(BigDecimal.ZERO.subtract(giveAccount.getFrozenAmt()))
                        .setBusiType(AccountBusiType.WITHDRAW_SUCCESS.getType())
                        .setBusiId(withdraw.getWithdrawId())
                        .setBusiFields(busiFields)
                        .setMerchantId(withdraw.getMerchantId())
                        .setMerchantType(withdraw.getMerchantType())
                        .setAccountType(giveAccount.getAccountType())
                        .setPlatform(withdraw.getPlatform())
                        .setBusiNo(withdraw.getTransferNo())
                ;
                accountFlowService.insertAccAccountFlow(accountFlow);
                accountFlowService.processFlow(accountFlow);

                syncFlowIdList.add(accountFlow.getAccountFlowId());
            }
        }

        //发送门店储值提现通知--至第三方
        try{
            if(ToolUtil.isNotEmpty(syncFlowIdList)){
                accountMqProducer.sendSyncBranchValueInfo(syncFlowIdList);
            }

        }catch (Exception e){
            log.error("发送门店储值提现通知--至第三方异常，推送异常资金流失ID集合：{}，",syncFlowIdList,e);
        }
    }

}
