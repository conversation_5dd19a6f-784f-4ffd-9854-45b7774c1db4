package com.zksr.account.service;

import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.controller.account.vo.*;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.common.core.web.pojo.PageResult ;
import com.zksr.account.domain.AccAccount;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;

import java.util.List;

/**
 * 账户Service接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
public interface IAccAccountService {

    /**
     * 新增账户
     *
     * @param accAccount 创建信息
     * @return 结果
     */
    public Long insertAccAccount(AccAccount accAccount);

    /**
     * 修改账户
     *
     * @param accAccount 修改信息
     * @return 结果
     */
    public boolean updateAccAccount(AccAccount accAccount);

    /**
     * 删除账户
     *
     * @param accountId 账户id
     */
    public void deleteAccAccount(Long accountId);

    /**
     * 批量删除账户
     *
     * @param accountIds 需要删除的账户主键集合
     * @return 结果
     */
    public void deleteAccAccountByAccountIds(Long[] accountIds);

    /**
     * 获得账户
     *
     * @param accountId 账户id
     * @return 账户
     */
    public AccAccount getAccAccount(Long accountId);

    /**
     * 获得账户分页
     *
     * @param pageReqVO 分页查询
     * @return 账户分页
     */
    PageResult<AccAccount> getAccAccountPage(AccAccountPageReqVO pageReqVO);

    /**
     * 获取账户
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform     支付平台  {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    AccAccount getAccountByMerchantIdAndType(Long merchantId, String merchantType, String platform);

    /**
     * 获取账户
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform     支付平台  {@link com.zksr.common.core.enums.PayChannelEnum}
     * @param accountType  账户类型  {@link com.zksr.common.core.business.AccountType}
     * @return
     */
    AccAccount getAccountByMerchantIdAndType(Long merchantId, String merchantType, String platform, Integer accountType);

    /**
     * 获取账户, 默认平台
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param sysCode       平台编号
     * @return
     */
    AccAccount getAccountByMerchantIdAndTypeByDefPlatform(Long merchantId, String merchantType, Long sysCode);

    /**
     * 获取账户
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform     支付平台  {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    List<AccAccount> getAccountListByMerchantIdAndType(Long merchantId, String merchantType, String platform);

    /**
     * 获取账户
     * @param sysCode       平台编号
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param holdMerchantId 账户持有方
     * @param platform     支付平台  {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    AccAccount getAccount(Long sysCode, Long merchantId, String merchantType, Long holdMerchantId, String platform);

    /**
     * 获取账户
     * @param merchantId    商户ID
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @return 返回各种拼
     */
    List<AccAccount> getAccountByMerchantIdAndType(Long merchantId, String merchantType);

    /**
     * 获取账户信息, 条件过滤
     * @param reqVo
     * @return
     */
    List<AccAccount> getAccountInfoLis(AccAccountInfoReqVO reqVo);

    /**
     * 获取入驻商系统账户 和 第三方平台信息
     * @param supplierId
     * @return
     */
    AccAccountDTO getSupplierAccount(Long supplierId);

    /**
     * 修改账户授信金额
     * @param saveReqVO
     */
    void updateAccountCreditAmt(AccAccountSaveCreditReqVO saveReqVO);

    /**
     * 获取账户
     * @param accountId     商户ID
     * @param merchantType  商户类型
     * @return
     */
    AccAccount getAccAccount(Long accountId, String merchantType);

    /**
     * 获取业务员账户
     * @param colonelId
     * @return
     */
    ColonelAccountRespVO getColonelAccount(Long colonelId);

    /**
     * 获取运营商储值结算余额
     * @param rechargeSettlePageReqVO    运营商查询
     * @return
     */
    PageResult<AccDcRechargeSettleRespVO> getDcRechargeSettleList(AccDcRechargeSettlePageReqVO rechargeSettlePageReqVO);
    
    /**
     * 检查订单是否已支付
     * @param supplierOrderNos
     * @return
     */
	Boolean checkOrderHasPay(List<String> supplierOrderNos);
}
