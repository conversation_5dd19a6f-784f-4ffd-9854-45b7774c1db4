package com.zksr.account.api.pay;

import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.*;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.service.pay.DivideOrderService;
import com.zksr.account.service.pay.IPayDivideOrderService;
import com.zksr.account.service.pay.PayOrderService;
import com.zksr.account.service.pay.PayWxB2bDivideOrderService;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Objects;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付对外服务
 * @date 2024/3/23 10:51
 */
@ApiIgnore
@RestController
@InnerAuth
public class PayApiImpl implements PayApi {

    @Autowired
    private PayOrderService payOrderService;

    @Autowired
    private DivideOrderService divideOrderService;

    @Autowired
    private AccountMqProducer accountMqProducer;

    @Override
    public CommonResult<PayOrderRespDTO> submitPayOrder(@RequestBody PayOrderSubmitReqVO reqVO) {
        // 1. 提交支付
        reqVO.setIpAddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
        PayOrderRespDTO respVO = payOrderService.submitOrder(reqVO);
        return success(respVO);
    }

    @Override
    public CommonResult<PayRefundRespDTO> submitPayOrder(PayRefundOrderSubmitReqVO reqVO) {
        // 1. 创建退货单
        PayRefundRespDTO respVO = payOrderService.createPayRefund(reqVO);
        return success(respVO);
    }

    @Override
    public CommonResult<Boolean> payCallBack(PayOrderRespDTO respDTO) {
        accountMqProducer.sendPayNotify(respDTO);
        return success(Boolean.TRUE);
    }

    @Override
    public CommonResult<CreateDivideRespVO> divide(CreateDivideReqVO divideReqVO) {
        // 通过订单号获取匹配的分账处理服务
        IPayDivideOrderService payDivideOrderService =  divideOrderService.getDivideOrderService(divideReqVO.getTradeNo());
        // 处理分账
        CreateDivideRespVO respVO = payDivideOrderService.divide(divideReqVO);
        if (respVO.isSuccess()) {
            if (Objects.nonNull(respVO.getDivideFlowId())) {
                // 异步延迟查询处理结果
                accountMqProducer.sendDivideDelayQuery(respVO.getDivideFlowId());
            } else {
                // 验证是否可以分账完成
                payDivideOrderService.divideCheckOver(divideReqVO);
            }
        }
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<PayOrderRespDTO> queryOrder(PayOrderQueryVO payOrderQueryVO) {
        return CommonResult.success(payOrderService.queryOrder(payOrderQueryVO));
    }
}
