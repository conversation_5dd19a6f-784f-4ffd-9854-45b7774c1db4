package com.zksr.account.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zksr.account.controller.account.vo.AccAccountInfoReqVO;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccAccount;
import com.zksr.account.controller.account.vo.AccAccountPageReqVO;

import java.util.List;
import java.util.Objects;


/**
 * 账户Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Mapper
public interface AccAccountMapper extends BaseMapperX<AccAccount> {
    default PageResult<AccAccount> selectPage(AccAccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccAccount>()
                    .eqIfPresent(AccAccount::getAccountId, reqVO.getAccountId())
                    .eqIfPresent(AccAccount::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccAccount::getAccountType, reqVO.getAccountType())
                    .eqIfPresent(AccAccount::getMerchantType, reqVO.getMerchantType())
                    .eqIfPresent(AccAccount::getMerchantId, reqVO.getMerchantId())
                    .eqIfPresent(AccAccount::getHoldMerchantType, reqVO.getHoldMerchantType())
                    .eqIfPresent(AccAccount::getHoldMerchantId, reqVO.getHoldMerchantId())
                    .eqIfPresent(AccAccount::getPlatform, reqVO.getPlatform())
                .orderByDesc(AccAccount::getAccountId));
    }

    /**
     * 获取账户
     * @param merchantId    supplierId || dcId  || supplierId......
     * @param merchantType  商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform     支付平台  {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return
     */
    default AccAccount selectAccountByMerchantIdAndType(Long merchantId, String merchantType, String platform) {
        return selectOne(
                Wrappers.lambdaQuery(AccAccount.class)
                        .eq(AccAccount::getMerchantId, merchantId)
                        .eq(AccAccount::getMerchantType, merchantType)
                        .eq(AccAccount::getPlatform, platform)
        );
    }

    default AccAccount selectAccountByMerchantIdAndAccountType(Long merchantId, String merchantType, String platform, Integer accountType) {
        return selectOne(
                new LambdaQueryWrapperX<AccAccount>()
                        .eq(AccAccount::getMerchantId, merchantId)
                        .eq(AccAccount::getMerchantType, merchantType)
                        .eq(AccAccount::getPlatform, platform)
                        .eqIfPresent(AccAccount::getAccountType, accountType)
        );
    }

    /**
     * 获取不同支付平台类型的账户
     * @param merchantId
     * @param merchantType
     * @return 集合账户
     */
    default List<AccAccount> selectAccountByMerchantIdAndTypeList(Long merchantId, String merchantType) {
        return selectList(
                Wrappers.lambdaQuery(AccAccount.class)
                        .eq(AccAccount::getMerchantId, merchantId)
                        .eq(AccAccount::getMerchantType, merchantType)
        );
    }

    /**
     * 根据条件检索账户
     *
     * @param reqVo
     * @return
     */
    default List<AccAccount> selectAccountInfoLis(AccAccountInfoReqVO reqVo) {
        return selectList(
                new LambdaQueryWrapperX<AccAccount>()
                        .eq(AccAccount::getMerchantId, reqVo.getMerchantId())
                        .eqIfPresent(AccAccount::getSysCode, reqVo.getSysCode())
                        .eqIfPresent(AccAccount::getMerchantType, reqVo.getMerchantType())
                        .eqIfPresent(AccAccount::getPlatform, reqVo.getPlatform())
                        .applyScope(reqVo.getParams())
        );
    }

    default AccAccount selectAccount(Long sysCode, Long merchantId, String merchantType, Long holdMerchantId, String platform) {
        return selectOne(
                Wrappers.lambdaQuery(AccAccount.class)
                        .eq(AccAccount::getMerchantId, merchantId)
                        .eq(AccAccount::getMerchantType, merchantType)
                        .eq(AccAccount::getPlatform, platform)
                        .eq(Objects.nonNull(holdMerchantId), AccAccount::getHoldMerchantId, holdMerchantId)
                        .eq(AccAccount::getPlatform, platform)
        );
    }

    default AccAccount selectAccountByIdAndMerchantTypeAndPlatform(Long accountId, String merchantType) {
        return selectOne(
                new LambdaQueryWrapperX<AccAccount>()
                        .eq(AccAccount::getAccountId, accountId)
                        .eq(AccAccount::getMerchantType, merchantType)
        );
    }
}
