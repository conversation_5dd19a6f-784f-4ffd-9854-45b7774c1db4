package com.zksr.account.mapper;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.database.mapper.BaseMapperX ;
import com.zksr.common.database.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccOfflineDivide;
import com.zksr.account.controller.divide.vo.AccOfflineDividePageReqVO;


/**
 * 线下分账处理Mapper接口
 *
 * <AUTHOR>
 * @date 2024-08-16
 */
@Mapper
public interface AccOfflineDivideMapper extends BaseMapperX<AccOfflineDivide> {
    default PageResult<AccOfflineDivide> selectPage(AccOfflineDividePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AccOfflineDivide>()
                    .eqIfPresent(AccOfflineDivide::getOfflineDivideId, reqVO.getOfflineDivideId())
                    .eqIfPresent(AccOfflineDivide::getSysCode, reqVO.getSysCode())
                    .eqIfPresent(AccOfflineDivide::getOfflineProNo, reqVO.getOfflineProNo())
                    .eqIfPresent(AccOfflineDivide::getMerchantType, reqVO.getMerchantType())
                    .eqIfPresent(AccOfflineDivide::getMerchantId, reqVO.getMerchantId())
                    .eqIfPresent(AccOfflineDivide::getAltAmt, reqVO.getAltAmt())
                    .eqIfPresent(AccOfflineDivide::getOfflineNo, reqVO.getOfflineNo())
                    .eqIfPresent(AccOfflineDivide::getTotalAltAmt, reqVO.getTotalAltAmt())
                    .eqIfPresent(AccOfflineDivide::getTotalOrderAmt, reqVO.getTotalOrderAmt())
                    .eqIfPresent(AccOfflineDivide::getVoucherPic, reqVO.getVoucherPic())
                .orderByDesc(AccOfflineDivide::getOfflineDivideId));
    }

    default AccOfflineDivide selectByOfflineProNo(String offlineProNo) {
        return selectOne(new LambdaQueryWrapperX<AccOfflineDivide>()
                .eq(AccOfflineDivide::getOfflineProNo, offlineProNo)
                .last(StringPool.LIMIT_ONE)
        );
    }
}
