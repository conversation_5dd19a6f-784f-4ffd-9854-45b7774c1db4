package com.zksr.account.convert.flow;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.account.domain.AccWithdrawFlow;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowRespVO;
import com.zksr.account.controller.flow.vo.AccWithdrawFlowSaveReqVO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
* 支付平台提现流水 对象转换器
* 参考文档 {@inheritDoc https://blog.csdn.net/qq_40194399/article/details/*********}
* <AUTHOR>
* @date 2024-04-12
*/
@Mapper
public interface AccWithdrawFlowConvert {

    AccWithdrawFlowConvert INSTANCE = Mappers.getMapper(AccWithdrawFlowConvert.class);

    AccWithdrawFlowRespVO convert(AccWithdrawFlow accWithdrawFlow);

    AccWithdrawFlow convert(AccWithdrawFlowSaveReqVO accWithdrawFlowSaveReq);

    PageResult<AccWithdrawFlowRespVO> convertPage(PageResult<AccWithdrawFlow> accWithdrawFlowPage);
}