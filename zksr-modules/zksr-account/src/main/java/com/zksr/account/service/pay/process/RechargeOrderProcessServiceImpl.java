package com.zksr.account.service.pay.process;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.domain.AccAccountFlow;
import com.zksr.account.domain.AccRecharge;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderNotifyRespDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.service.IAccPlatformMerchantService;
import com.zksr.account.service.IAccRechargeService;
import com.zksr.account.service.pay.process.PayOrderProcessService;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.supplier.SupplierApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.zksr.account.enums.ErrorCodeConstants.NOT_EXIST_RECHARGE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 入驻商充值单数据提供
 * @date 2024年3月22日09:40:46
 */
@Service
@Slf4j
public class RechargeOrderProcessServiceImpl extends PayOrderProcessService {

    @Resource
    private SupplierApi supplierApi;

    @Resource
    private PartnerConfigApi partnerConfigApi;

    @Autowired
    private IAccRechargeService rechargeService;

    @Autowired
    private AccountMqProducer accountFlowMqChannel;

    @Autowired
    private IAccPlatformMerchantService platformMerchantService;


    @Override
    public PayOrderDTO validateOrder(PayOrderSubmitReqVO reqVO) {

        // 返回分账信息
        return null;
    }

    /**
     * 支付成功
     * @param notify
     * @return
     */
    @Override
    public PayOrderNotifyRespDTO notifyOrderSuccess(PayOrderRespDTO notify) {
        // 支付成功
        AccRecharge recharge = rechargeService.getRechargeByNo(notify.getOrderNo());
        if (Objects.isNull(recharge)) {
            log.warn("充值单不存在, rechargeNo : {}", notify.getOrderNo());
            throw exception(NOT_EXIST_RECHARGE);
        }
        recharge.setPayWay(notify.getPayWay());
        recharge.setPlatform(notify.getPayPlatform());
        List<AccAccountFlow> accountFlow = rechargeService.rechargeSuccess(recharge);
        return PayOrderNotifyRespDTO.success(JSON.toJSONString(accountFlow));
    }


    /**
     * 支付成功回调后回调 , 和支付回调不是同一个事务, 支付成功回调事务处理完成以后回调
     * @param payOrderNotifyRespDTO
     */
    @Override
    public void notifyOrderSuccessCall(PayOrderNotifyRespDTO payOrderNotifyRespDTO) {
        // 获取业务数据
        if (payOrderNotifyRespDTO.getSuccess()) {
            // 发送mq处理数据
            List<AccAccountFlow> accountFlows = JSON.parseArray(payOrderNotifyRespDTO.getBusinessInfo(), AccAccountFlow.class);
            accountFlows.forEach(item -> {
                accountFlowMqChannel.sendSettleFlow(item);
            });
        }
    }


    @Override
    public void notifyRefundSuccess(PayRefundRespDTO notify) {
        // 退款成功
    }

    @Override
    public void notifyRefundFailure(PayRefundRespDTO notify) {
        // 退款失败
    }
}
