package com.zksr.account.controller.transfer;

import com.zksr.account.controller.transfer.vo.AccTransferPageReqVO;
import com.zksr.account.controller.transfer.vo.AccTransferRespVO;
import com.zksr.account.controller.transfer.vo.AccTransferSaveReqVO;
import com.zksr.account.convert.transfer.AccTransferConvert;
import com.zksr.account.domain.AccTransfer;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.account.service.IAccTransferService;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.log.annotation.Log;
import com.zksr.common.log.enums.BusinessType;
import com.zksr.common.security.annotation.RequiresPermissions;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 账户转账单Controller
 *
 * <AUTHOR>
 * @date 2024-04-11
 */
@Api(tags = "管理后台 - 账户转账单接口", produces = "application/json")
@Validated
@RestController
@RequestMapping("/transfer")
public class AccTransferController {

    @Autowired
    private IAccTransferService accTransferService;

    @Autowired
    private AccountMqProducer accountMqProducer;

    /**
     * 新增账户转账单
     */
    @ApiOperation(value = "新增账户转账单", httpMethod = HttpMethod.POST, notes = StringPool.PERMISSIONS_FIX + Permissions.ADD)
    @RequiresPermissions(Permissions.ADD)
    @Log(title = "账户转账单", businessType = BusinessType.INSERT)
    @PostMapping
    public CommonResult<Long> add(@Valid @RequestBody AccTransferSaveReqVO createReqVO) {
        AccTransfer transfer = accTransferService.insertAccTransfer(createReqVO);
        accountMqProducer.sendAccountTransfer(transfer);
        return success(transfer.getTransferId());
    }

    /**
     * 修改账户转账单
     */
    @ApiOperation(value = "修改账户转账单", httpMethod = HttpMethod.PUT, notes = StringPool.PERMISSIONS_FIX + Permissions.EDIT)
    @RequiresPermissions(Permissions.EDIT)
    @Log(title = "账户转账单", businessType = BusinessType.UPDATE)
    @PutMapping
    public CommonResult<Boolean> edit(@Valid @RequestBody AccTransferSaveReqVO updateReqVO) {
        accTransferService.updateAccTransfer(updateReqVO);
        return success(true);
    }

    /**
     * 获取账户转账单详细信息
     */
    @ApiOperation(value = "获得账户转账单详情", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.GET)
    @RequiresPermissions(Permissions.GET)
    @GetMapping(value = "/{transferId}")
    public CommonResult<AccTransferRespVO> getInfo(@PathVariable("transferId") Long transferId) {
        AccTransfer accTransfer = accTransferService.getAccTransfer(transferId);
        return success(AccTransferConvert.INSTANCE.convert(accTransfer));
    }

    /**
     * 分页查询账户转账单
     */
    @GetMapping("/list")
    @ApiOperation(value = "获得账户转账单分页列表", httpMethod = HttpMethod.GET, notes = StringPool.PERMISSIONS_FIX + Permissions.LIST)
    @RequiresPermissions(Permissions.LIST)
    public CommonResult<PageResult<AccTransferRespVO>> getPage(@Valid AccTransferPageReqVO pageReqVO) {
        PageResult<AccTransfer> pageResult = accTransferService.getAccTransferPage(pageReqVO);
        return success(AccTransferConvert.INSTANCE.convertPage(pageResult));
    }


    /**
     * 权限字符
     */
    public static class Permissions {
        /** 添加 */
        public static final String ADD = "account:transfer:add";
        /** 编辑 */
        public static final String EDIT = "account:transfer:edit";
        /** 删除 */
        public static final String DELETE = "account:transfer:remove";
        /** 列表 */
        public static final String LIST = "account:transfer:list";
        /** 查询 */
        public static final String GET = "account:transfer:query";
        /** 停用 */
        public static final String DISABLE = "account:transfer:disable";
        /** 启用 */
        public static final String ENABLE = "account:transfer:enable";
    }
}
