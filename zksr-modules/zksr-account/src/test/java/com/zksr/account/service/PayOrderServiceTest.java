package com.zksr.account.service;

import com.zksr.account.ZksrMallAccountApplication;
import com.zksr.account.client.PayClient;
import com.zksr.account.client.wx.WxB2bPayClient;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.model.pay.vo.PayRefundOrderSubmitReqVO;
import com.zksr.account.service.pay.PayOrderService;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.system.api.model.dto.PayConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: chenyj8
 * @Desciption: 类功能描述
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallAccountApplication.class)
public class PayOrderServiceTest {
    @Resource
    private PayOrderService payOrderService;


    @Test
    public void testSubmitOrder(){
        PayOrderSubmitReqVO reqVO = new PayOrderSubmitReqVO();
        reqVO.setOrderNo("********************");
        reqVO.setSysCode(4L);
        reqVO.setOrderType(0);
        reqVO.setOpenid("obEsJ7S72CAw0ntH-FKRmoqMvVAk");
        reqVO.setPayWay("0");

        PayOrderRespDTO payOrderRespDTO = payOrderService.submitOrder(reqVO);

        System.out.println(JsonUtils.toJsonString(payOrderRespDTO));
    }


    @Test
    public void testUnifiedOrderNew(){
        WxB2bPayClient payClient = new WxB2bPayClient() {
            @Override
            public String getChannel() {
                return null;
            }

            @Override
            public String getPlatform() {
                return PayChannelEnum.WX_B2B_PAY.getCode();
            }

            @Override
            public void setConfig(PayConfigDTO config, String appid) throws Exception {

            }

            @Override
            public PayOrderRespDTO unifiedOrder(PayOrderDTO payOrderDTO) {
                return null;
            }

            @Override
            public PayOrderRespDTO parseOrderNotify(Map<String, String> params, String body) {
                return null;
            }

            @Override
            public PayRefundRespDTO unifiedRefund(PayRefundOrderSubmitReqVO reqDTO) {
                return null;
            }

            @Override
            public PayRefundRespDTO parseRefundNotify(Map<String, String> params, String body) {
                return null;
            }
        };

        PayOrderDTO payOrderDTO = new PayOrderDTO();
        payOrderDTO.setAppid("wx7e30a513ad4d15fa");
        payOrderDTO.setBody("SP00114");
        payOrderDTO.setBranchId(484063576146575362L);
        payOrderDTO.setBusiId(627301452804915200L);
        payOrderDTO.setCombinePay(true);
        payOrderDTO.setFlowId(627301469990682624L);
        payOrderDTO.setMethod("wx");
        payOrderDTO.setOpenid("obEsJ7fJHJoVPeNJHWzlZvdlOZgs");
        payOrderDTO.setOrderNo("********************");
        payOrderDTO.setOrderType(0);
        payOrderDTO.setPayAmt(BigDecimal.valueOf(4.00));
        payOrderDTO.setSessionKey("5MdyAdZz10hJfiLSbpG8Zg==");
        payOrderDTO.setSysCode(4L);
        payOrderDTO.setWalletGiveAmt(BigDecimal.valueOf(0.000000));
        List<OrderSettlementDTO> settlements = new ArrayList<>();

        OrderSettlementDTO orderSettlementDTO = new OrderSettlementDTO();
        orderSettlementDTO.setAccountNo("**********");
        orderSettlementDTO.setAmt(BigDecimal.valueOf(3.98));
        orderSettlementDTO.setMerchantId(15580493955L);
        orderSettlementDTO.setMerchantType("supplier");
        orderSettlementDTO.setSubOrderNo("XSS250614570000027569");

        settlements.add(orderSettlementDTO);
        payOrderDTO.setSettlements(settlements);

        payClient.unifiedOrderNew2(payOrderDTO);
    }

    @Before
    public void mockTenant(){
//        PayOrderRespDTO payOrderRespDTO = new PayOrderRespDTO();
//        Mockito.when(payOrderService.submitOrder((Mockito.any(PayOrderSubmitReqVO.class)))).thenReturn(payOrderRespDTO);
    }
}
