package com.zksr.account.service;


import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.zksr.account.ZksrMallAccountApplication;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.account.service.pay.PayChannelService;
import com.zksr.account.service.pay.PayOrderService;
import com.zksr.common.core.config.AnntoProxyConfig;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: chenyj8
 * @Desciption: 财务结算模块测试类
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallAccountApplication.class)
public class AccountServiceTest {
    @Autowired
    PayChannelService payChannelService;

    @MockBean
    PayOrderService payOrderService;

    @Resource
    AnntoProxyConfig anntoProxyConfig;

    @Test
    public void testSubmitOrder() {
        PayOrderSubmitReqVO reqVO = new PayOrderSubmitReqVO();
        reqVO.setOrderNo("********************");
        payOrderService.submitOrder(reqVO);

    }


    @Test
    public void testJsonUtils() {
        List list = new ArrayList();
        for (int i = 0; i < 5; i++) {
            PayOrderSubmitReqVO vo = new PayOrderSubmitReqVO();
            vo.setOrderNo(i+"");
            OrderSettlementDTO dto = new OrderSettlementDTO();
            dto.setAccountNo(i+1+"");
            dto.setAmt(BigDecimal.ZERO);
            vo.setSettlements(Lists.newArrayList(dto));
            list.add(vo);
        }

        System.out.println(JsonUtils.toJsonString(list));
    }

    @Test
    public void testLog(){
//        HttpUtils.simplePost("https://monitor.midea.com/",null,null,null);
        log.info("http请求代理信息: {}", JSONUtil.toJsonStr(anntoProxyConfig));

//        log.info("http请求代理信息: {}", JsonUtils.toJsonString(anntoProxyConfig));
    }

    @Before
    public void mockTenant(){
        PayOrderRespDTO payOrderRespDTO = new PayOrderRespDTO();
        Mockito.when(payOrderService.submitOrder((Mockito.any(PayOrderSubmitReqVO.class)))).thenReturn(payOrderRespDTO);
    }
}