import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.account.ZksrMallAccountApplication;
import com.zksr.account.client.hlb.HlbMerchantClient;
import com.zksr.account.client.hlb.HlbSdkClient;
import com.zksr.account.model.merchant.vo.PayPlatformMerchantVO;
import com.zksr.account.model.pay.dto.PayOrderDTO;
import com.zksr.account.mq.AccountMqProducer;
import com.zksr.common.core.utils.cert.HlbCertUtil;
import com.zksr.system.api.model.dto.HlbPayConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrMallAccountApplication.class)
@Slf4j
public class ZksrMallAccountApplicationTests {


    @Autowired
    private AccountMqProducer accountMqProducer;

    @Test
    public void testBranchValueInfo() throws Exception {

    }

}
