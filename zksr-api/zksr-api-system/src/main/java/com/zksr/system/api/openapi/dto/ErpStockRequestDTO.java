package com.zksr.system.api.openapi.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class ErpStockRequestDTO {
    private String sheetSource;
    private String b2bSheetNo;
    private String orgCode;
    private Integer stockShortFlag;
    private List<ErpStockDetailDTO> subList;

    @Data
    @Accessors(chain = true)
    public static class ErpStockDetailDTO {
        private String itemNo;
        private Integer lineNo;
        private BigDecimal minDetailQty;
    }
}
