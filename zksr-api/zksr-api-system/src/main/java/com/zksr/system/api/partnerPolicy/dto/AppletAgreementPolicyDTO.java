package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 平台商小程序配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AppletAgreementPolicyDTO {
    /**
     * 用户协议
     */
    @ApiModelProperty("用户协议")
    private String userAgreement;
    /**
     * 用户隐私协议
     */
    @ApiModelProperty("用户隐私协议")
    private String userPrivacyAgreement;

    /**
     * 平台商logo
     */
    @ApiModelProperty("平台商logo")
    private String logo;

    /**
     * 是否校验设备(0 禁用 1 启用)
     */
    @ApiModelProperty("是否校验设备(0 禁用 1 启用)")
    private String checkDevice;

    /**
     * 游客模式归属城市ID
     */
    @ApiModelProperty("游客模式归属城市ID")
    private String cityId;

    /**
     * 平台商名称
     */
    @ApiModelProperty("平台商名称")
    private String partnerName;

    /**
     * 商场小程序分享配置
     */
    @ApiModelProperty("商场小程序分享配置")
    private String shopShareBgImgConfig;

    /**
     * 无库存商品多少天以后隐藏, 单位/天, 如果是0, 那就是不显示无库存商品
     */
    @ApiModelProperty("无库存商品多少天以后隐藏, 单位/天")
    private String hideNoStockProductDay;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;
}
