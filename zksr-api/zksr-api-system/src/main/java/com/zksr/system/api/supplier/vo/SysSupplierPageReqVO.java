package com.zksr.system.api.supplier.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 入驻商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@ApiModel("入驻商信息 - sys_supplier分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysSupplierPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 入驻商编号 */
    @ApiModelProperty(value = "入驻商编号")
    private String supplierCode;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 状态-数据字典 */
    @Excel(name = "状态-数据字典")
    @ApiModelProperty(value = "状态-数据字典")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否是电子围栏入驻商 */
    @Excel(name = "是否是电子围栏入驻商")
    @ApiModelProperty(value = "是否是电子围栏入驻商")
    private Long dzwlFlag;

    /** 电子围栏 */
    @Excel(name = "电子围栏")
    @ApiModelProperty(value = "电子围栏")
    private String dzwlInfo;

    /** 运营商id;运营商id */
    @Excel(name = "运营商id;运营商id")
    @ApiModelProperty(value = "运营商id;运营商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 营业执照图片地址 */
    @Excel(name = "营业执照图片地址")
    @ApiModelProperty(value = "营业执照图片地址")
    private String licenceUrl;

    @Excel(name = "入驻商头像地址")
    @ApiModelProperty(value = "入驻商头像地址")
    private String avatar;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;
}
