package com.zksr.system.api.amap.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *  地址经纬度查询返回实体
 */
@Data
public class LongitudeAndLatitudeResult {

    /**
     * 0代表成功，其他参考错误码说明
     */
    @ApiModelProperty("返回状态码 返回值为 0 或 1，0 表示请求失败；1 表示请求成功")
    private Integer status;

    @ApiModelProperty("返回结果数目")
    private Long count;

    @ApiModelProperty("返回状态说明 当 status 为 0 时，info 会返回具体错误原因，否则返回“OK”。详情可以参阅 info 状态表")
    private String info;

    @ApiModelProperty("返回对象")
    private List<Geocode> geocodes;

    @ApiModel(value = "地理编码信息列表")
    @Data
    public static class Geocode {
        @ApiModelProperty("国家")
        private String country;

        @ApiModelProperty("地址所在的省份名 中国的四大直辖市也算作省级单位")
        private String province;

        @ApiModelProperty("地址所在的城市名")
        private String city;

        @ApiModelProperty("城市编码")
        private String citycode;

        @ApiModelProperty("地址所在的区")
        private String district;

        @ApiModelProperty("街道")
        private String street;

        @ApiModelProperty("门牌")
        private String number;

        @ApiModelProperty("区域编码")
        private String adcode;

        @ApiModelProperty("坐标点")
        private String location;

        @ApiModelProperty("匹配级别")
        private String level;
    }
}
