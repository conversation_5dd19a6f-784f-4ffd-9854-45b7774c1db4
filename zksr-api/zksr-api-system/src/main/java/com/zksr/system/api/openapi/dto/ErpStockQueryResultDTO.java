package com.zksr.system.api.openapi.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ErpStockQueryResultDTO {
    private String itemNo;
    private String branchNo;
    private BigDecimal leaveQty;
    private String batchNo;
    private BigDecimal occupyOutQty;
    private BigDecimal occupySaleQty;
    private BigDecimal usableSaleQty;
    private BigDecimal usableOutQty;
}
