package com.zksr.system.api.commonMessage.dto;

import com.zksr.common.core.enums.MerchantTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户凭证
 * @date 2024/12/5 15:26
 */
@Data
public class MessageCertificate {

    @ApiModelProperty("小程序openid")
    private String appletOpenid;

    @ApiModelProperty("公众号openid")
    private String publishOpenid;

    @ApiModelProperty(value = "系统商户ID", notes = "门店, 入驻商, 业务员, 软件商等")
    private Long merchantId;

    @ApiModelProperty("商户类型")
    private MerchantTypeEnum merchantType;
}
