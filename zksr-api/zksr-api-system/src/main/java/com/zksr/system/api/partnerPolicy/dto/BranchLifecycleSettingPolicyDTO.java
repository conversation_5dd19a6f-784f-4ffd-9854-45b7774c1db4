package com.zksr.system.api.partnerPolicy.dto;

import com.zksr.common.core.utils.ToolUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* 门店生命周期  -- 运营商级别
* @date 2025/3/7 11:07
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "门店生命周期")
public class BranchLifecycleSettingPolicyDTO {

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    /**新客天数定义 ： 注册天数小于等于新客天数*/
    @ApiModelProperty("新客天数定义")
    private String newCustomerDays = "30";

    /**活跃天数定义 ： 下单天数小于等于活跃天数*/
    @ApiModelProperty("活跃天数定义")
    private String activeDays = "30";

    /**沉默天数定义 ： 未下单天数大于活跃天数 ， 小于等于沉默天数*/
    @ApiModelProperty("沉默天数定义")
    private String silentDays = "60";

    /**流失天数定义 ： 未下单天数大于沉默天数  当大于沉默天数后 流失客户会有一个系统保护期，保护期内属于当前业务员，如未在保护期内下单则客户会再次进入公海*/
    @ApiModelProperty("流失保护期天数定义")
    private String lostProtectionPeriodDays = "3";

    /**是否禁用登录账号*/
    @ApiModelProperty("是否禁用登录账号 0 否 1 是")
    private String isStopLogin = "0";

    public Integer convertNewCustomerDays() {
        return null == newCustomerDays ? null : Integer.valueOf(newCustomerDays);
    }

    public Integer convertActiveDays() {
        return null == activeDays ? null :Integer.valueOf(activeDays);
    }

    public Integer convertSilentDays() {
        return null == silentDays ? null :Integer.valueOf(silentDays);
    }

    public Integer convertLostProtectionPeriodDays() {
        return null == lostProtectionPeriodDays ? null :Integer.valueOf(lostProtectionPeriodDays);
    }

    public Integer convertIsStopLogin() {
        return null == isStopLogin ? null :Integer.valueOf(isStopLogin);
    }

    public boolean isNullByBranchLifecycle(){
        return ToolUtil.isEmpty(this.convertNewCustomerDays())
                || ToolUtil.isEmpty(this.convertActiveDays())
                || ToolUtil.isEmpty(this.convertSilentDays())
                || ToolUtil.isEmpty(this.convertLostProtectionPeriodDays());
    }
}
