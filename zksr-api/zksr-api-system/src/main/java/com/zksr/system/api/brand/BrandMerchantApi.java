package com.zksr.system.api.brand;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.brand.vo.SysBrandMerchantRespVO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @time 2024/8/5
 * @desc 品牌商api
 */
@FeignClient(contextId = "brandMerchantApi", value = ApiConstants.NAME)
public interface BrandMerchantApi {

    String PREFIX = ApiConstants.PREFIX + "/brandMerchant";

    @GetMapping(PREFIX + "/getByUser")
    CommonResult<SysBrandMerchantRespVO> getByUser(@RequestParam("sysUserId") Long sysUserId);
}
