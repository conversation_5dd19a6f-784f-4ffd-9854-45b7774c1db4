package com.zksr.system.api.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@Data
public class SysUserUpdatePwdVO {
    @ApiModelProperty(value = "手机号", required = true)
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "用户密码  必填", required = true)
    @NotBlank(message = "用户密码不能为空")
    private String password;

    @ApiModelProperty(value = "数据来源：colonel(业务员)、supplier（入驻商）", required = true)
    @NotBlank(message = "source不能为空")
    private String source;

    @ApiModelProperty(value = "用户账号", hidden = true)
    private String userName;
}
