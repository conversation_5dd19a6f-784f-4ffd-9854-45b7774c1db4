package com.zksr.system.api.wx;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.domain.vo.openapi.receive.SysSupplierDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.api.wx.dto.WxQrData;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 微信二维码
 */
@FeignClient(
        contextId = "remoteWxQrApi",
        value = ApiConstants.NAME
)
public interface WxQrApi {

    String PREFIX = ApiConstants.PREFIX + "/wxQr";

    @GetMapping(PREFIX + "/add")
    CommonResult<WxQrData> add(@RequestParam("qrValue") String qrValue);

    @GetMapping(PREFIX + "/getByQrKey")
    CommonResult<WxQrData> getByQrKey(@RequestParam("qrKey") String qrKey);

}
