package com.zksr.system.api.model.dc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 运营商城市关联组
 * @date 2024/3/15 16:40
 */
@Data
public class DcAreaGroupDTO implements Serializable {
    @ApiModelProperty("运营商ID")
    private Long dcId;
    @ApiModelProperty("区域城市ID")
    private List<Long> areaIds;

    public DcAreaGroupDTO() {}

    public DcAreaGroupDTO(Long dcId, List<Long> areaIds) {
        this.dcId = dcId;
        this.areaIds = areaIds;
    }

}
