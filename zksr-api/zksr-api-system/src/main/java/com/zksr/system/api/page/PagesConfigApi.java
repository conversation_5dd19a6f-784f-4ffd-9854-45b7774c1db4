package com.zksr.system.api.page;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/13
 * @desc
 */

@FeignClient(
        contextId = "remotePagesConfigApiApi",
        value = ApiConstants.NAME
)
public interface PagesConfigApi {

    String PREFIX = ApiConstants.PREFIX + "/pagesConfig";

    @GetMapping(PREFIX + "/getPagesConfig")
    CommonResult<List<PagesConfigDTO>> getPagesConfig(@RequestParam("pageKey") String pageKey);
}
