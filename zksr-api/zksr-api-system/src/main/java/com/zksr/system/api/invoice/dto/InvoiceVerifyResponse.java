package com.zksr.system.api.invoice.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Description: 发票查验响应DTO
 * @Date: 2025/07/16
 */
@Data
public class InvoiceVerifyResponse {

    /**
     * 页码
     */
    private Integer pageNumber;

    /**
     * 页大小
     */
    private Integer pageSize;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 开票日期
     */
    private String invoiceDate;

    /**
     * 发票-不含税金额
     */
    private String invoiceAmt;

    /**
     * 发票金额总计
     */
    private String invoiceAmtTotal;

    /**
     * 发票-含税金额
     */
    private String totalAmount;

    /**
     * 发票-总税额
     */
    private String taxAmount;

    /**
     * 校验码
     */
    private String checkCode;

    /**
     * 发票类型回显
     * 此发票类型为航信验真的发票类型
     * 增值税专用发票：01（旧航信渠道，和不需要走税局接口的旧系统，可以继续传01）
     * 新接入系统和需要走税局的渠道，需要传91或者92
     * 增值税专用发票-纸质 91
     * 增值税专用发票-电子 92
     * 新接入系统必须传91或92， 01已废弃，只兼容旧接入系统，后续会废弃掉01
     * 货运运输业增值税专用发票：02
     * 机动车销售统一发票：03
     * 增值税普通发票：04
     * 增值税普通发票（电子）：10
     * 增值税普通发票（卷式）：11
     * 通行费发票：14
     * 二手车发票：15
     * 全电专票：31
     * 全电普票：32
     * 全电纸票专票：33
     * 全电纸票普票：34
     */
    private String invoiceType;

    /**
     * 查验结果
     * 1：一致/成功
     * 0：不一致
     * 2：其他
     */
    private Integer checkRes;

    /**
     * 错误信息
     */
    private String checkMsg;

    /**
     * 错误信息码
     * 一致/成功(checkRes=1) 0000
     * 不一致(checkRes=0) 0005 请求不合法,修改数据后进行查验
     * 0006 发票信息不一致
     * 0009 所查发票不存在
     * 1002 发票代码长度应为12位或10位;发票号码长度应为8位;发票校验码应为后6位;发票校验码应为数字;等
     * 1005 查询发票不规范
     * 1008 参数不能为空
     * 1009 参数长度不正确
     * 1 校验号码不一致发票金额不一致
     * 005 该请求不是内网地址！请求参数不正确请求参数不正确,发票号码格式不正确!
     * 406 查验成功发票不一致
     * 409 所查发票不存在
     * 70001 参数错误(发票号码不能为空;开票日期不能为空;开票类型不能为空;发票代码不能为空;不含税金额不能为空;含税金额不能为空;此类发票校验码不能为空)
     * 70002 参数不一致(校验码不正确;不含税金额不正确;含税金额不正确)
     * 其他(checkRes=2) -1 未知错误
     * 907 未开通查验服务，请开通再进行查验
     * 1000 非本公司发票，不能查询
     * 099 获取锁失败
     * 410 查验失败：发票查验系统发生异常，请稍后再试!
     * 421 税局服务已关闭,详情请见本省税局官网公告
     * 411 查验通道繁忙，请稍后再试！
     * 402 超过该张票当天查验次数
     * 513 本税号为空或不存在
     * 910 未开通进项发票服务，请开通后使用
     * 416 查验异常
     * 703 税号不存在
     * 1004 已超过最大查验量
     * 1006 查验异常
     * 1007 鉴权失败
     * 1011 该批次已查验
     * 1013 批次号不存在
     * 1020 没有查验权限
     * 1021 网络超时
     * 1014 日期当天的不能查验
     * 1015 超过一年的不能查验
     * 0002 超过该张票当天查验次数
     * 9999 系统异常;服务繁忙,稍后重试;查验交互异常;查验返回异常
     * 60 未知错误
     * 100000 缺少系统参数;请检测输入参数的正确性
     * 100001 参数取值范围错误;检查参数的值
     * 100002 token失效或者无效 重新登录获取token
     * 100003 appKey错误或无访问权限;联系运营
     * 100005 请求过于频繁;默认一个app最大并发为10，请控制并发或者联系运营
     * 100007 无效的签名;注意sign参数的正确性
     * 100008 系统内部超时;请稍后重试
     * 100009 超过系统最大容流量;请控制并发或者过段时间重试
     * 100010 请求超时;请稍后重试
     * 100011 不支持GET方法;请使用POST方法
     * 100021 非法请求;请发送合法请求
     * 100022 服务升级中;请稍后再试
     * 100403 该appkey已被冻结;联系运营人员
     * 100413 请求报文超长;请求报文缩减（目前服务端设置报文大小限制为100M）
     */
    private String checkMsgCode;

    /**
     * 销售方税号
     */
    private String recTaxpayerCode;

    /**
     * 销售方名称
     */
    private String recTaxpayerName;

    /**
     * 销售方单位地址、电话号码
     */
    private String recUnitAddressPhoneNumber;

    /**
     * 销售方开户行、银行账户
     */
    private String recBankNameAccount;

    /**
     * 购买方税号
     */
    private String payTaxpayerCode;

    /**
     * 购买方名称
     */
    private String payTaxpayerName;

    /**
     * 购买方地址、电话号码
     */
    private String payUnitAddressPhoneNumber;

    /**
     * 购买方开户行、银行账户
     */
    private String payBankNameAccount;

    /**
     * 查验时间
     */
    private Date createDate;

    /**
     * 查验人
     */
    private String createBy;

    /**
     * 发票文件id
     */
    private String fileId;

    /**
     * 税号
     */
    private String taxpayerCode;

    /**
     * 序号
     */
    private String numBer;

    /**
     * 备注
     */
    private String remark;

    /**
     * 发票状态
     */
    private String invoiceState;

    /**
     * 商品详情字段
     */
    private List<OfdInvoiceDetailDTO> commodityDetailList;
}
