package com.zksr.system.api.invoice.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 发票查询响应DTO
 * @Date: 2025/07/16
 */
@Data
public class InvoiceBaseQueryResDTO {

    /**
     * 发票信息
     * 查询的发票业务信息的集合
     */
    private List<InvoiceQueryResDTO> list;

    /**
     * 流水号中发票的状态
     * 蓝票（0待确认 1待开，2开票中，3开票成功，4已撤销，5开票失败, 8已作废）
     * 红票（0待确认 1处理中，3处理成功，4已撤销，5处理失败 8已作废）
     */
    private Integer status;

    /**
     * 失败原因
     * 请求失败原因
     */
    private String failReason;

    /**
     * 开票流水号
     */
    private String bizId;

    /**
     * 交易类型
     * 1：蓝票；2：红票
     */
    private Integer transType;

    /**
     * 开票渠道
     */
    private String requestChannel;

    /**
     * 系统来源
     */
    private Integer sysSource;

    /**
     * 发票查询结果DTO
     */
    @Data
    public static class InvoiceQueryResDTO {

        /**
         * 发票含税金额
         */
        private BigDecimal amtContainTax;

        /**
         * 发票代码
         */
        private String invoiceCode;

        /**
         * 发票号码
         */
        private String invoiceNo;

        /**
         * 开票日期
         */
        private Date invoiceDate;

        /**
         * 发票pdf下载url
         */
        private String pdfUrl;

        /**
         * 子状态
         * 蓝票（2开票中，3开票成功，5开票失败,8已作废）
         * 红票（1处理中，3处理成功，5处理失败 ,8已作废）
         */
        private Integer subStatus;

        /**
         * 状态信息
         */
        private String resultMsg;
    }
}
