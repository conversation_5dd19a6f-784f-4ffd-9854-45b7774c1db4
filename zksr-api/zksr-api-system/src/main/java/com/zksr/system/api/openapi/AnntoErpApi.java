package com.zksr.system.api.openapi;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.openapi.dto.AnntoErpRequestDTO;
import com.zksr.system.api.openapi.dto.AnntoErpResultDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 对接ERP API
 */
@FeignClient(
        contextId = "remoteAnntoErpApi",
        value = ApiConstants.NAME
)
public interface AnntoErpApi {
    String PREFIX = ApiConstants.PREFIX + "/anntoErp";

    @PostMapping(value = PREFIX + "/sendErp")
    CommonResult<AnntoErpResultDTO<String>> sendErp(@RequestBody AnntoErpRequestDTO requestDTO);
}
