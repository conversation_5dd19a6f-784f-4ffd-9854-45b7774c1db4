package com.zksr.system.api.partnerPolicy.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.StringPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 入驻商其他配置类
 * @Author: chen<PERSON>qing
 * @Date: 2024/12/11 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SupplierOtherSettingPolicyDTO {

    /**
     * 入驻商 - 赠品取价算法
     */
    @ApiModelProperty("入驻商 - 赠品取价算法")
    private String giftPriceType;


    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    @ApiModelProperty("入驻商ID")
    private String supplierId;

    @ApiModelProperty("商城公告")
    private String announcement;

    @ApiModelProperty("商品配送标签")
    private String productDistributionLabel;

    @ApiModelProperty("入驻商 - 暂停营业开始时间")
    private String shuttingStartTime;

    @ApiModelProperty("入驻商 - 暂停营业结束时间")
    private String shuttingEndTime;

    @ApiModelProperty("是否开启钱包支付, 0-关闭, 1-开启")
    private String switchWalletPay;

    @ApiModelProperty("是否开启加单, 0-关闭, 1-开启 (开启的时候才可以设置截单时间)")
    private String betOrder = StringPool.ZERO;

    @ApiModelProperty("截单时间格式HH:mm")
    private String cutTime;
}
