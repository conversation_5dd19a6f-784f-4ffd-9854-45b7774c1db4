package com.zksr.system.api.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.system.api.domain.SysUser;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
public class LoginUser implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 平台商id
     */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /**
     * 运营商ID
     */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /**
     * 业务员ID
     */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    /**
     * 用户名id
     */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long userid;

    /**
     * 用户名
     */
    private String username;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 权限列表
     */
    private Set<String> permissions;

    /**
     * 角色列表
     */
    private Set<String> roles;

    /**
     * 用户信息
     */
    private SysUser sysUser;

    /**用来区分 是软件商(software)，平台商(partner)，运营商(dc)，入驻商(supplier)) ，新增和修改的时候做判断func_scop是否相等，不相等则允许做分配*/
    private String funcScop;

    //平台名称
    private String partnerName;

    //平台log
    private String partnerLog;

    /**
     * 软件商可能存在
     */
    @Getter
    @Setter
    private List<Long> sysCodeList;

    public Long getDcId() {
        return dcId;
    }

    public void setDcId(Long dcId) {
        this.dcId = dcId;
    }

    public String getToken()
    {
        return token;
    }

    public void setToken(String token)
    {
        this.token = token;
    }

    public Long getUserid()
    {
        return userid;
    }

    public void setUserid(Long userid)
    {
        this.userid = userid;
    }

    public String getUsername()
    {
        return username;
    }

    public void setUsername(String username)
    {
        this.username = username;
    }

    public Long getLoginTime()
    {
        return loginTime;
    }

    public void setLoginTime(Long loginTime)
    {
        this.loginTime = loginTime;
    }

    public Long getExpireTime()
    {
        return expireTime;
    }

    public void setExpireTime(Long expireTime)
    {
        this.expireTime = expireTime;
    }

    public String getIpaddr()
    {
        return ipaddr;
    }

    public void setIpaddr(String ipaddr)
    {
        this.ipaddr = ipaddr;
    }

    public Set<String> getPermissions()
    {
        return permissions;
    }

    public void setPermissions(Set<String> permissions)
    {
        this.permissions = permissions;
    }

    public Set<String> getRoles()
    {
        return roles;
    }

    public void setRoles(Set<String> roles)
    {
        this.roles = roles;
    }

    public SysUser getSysUser()
    {
        return sysUser;
    }

    public void setSysUser(SysUser sysUser)
    {
        this.sysUser = sysUser;
    }

    public Long getSysCode() {
        return sysCode;
    }

    public void setSysCode(Long sysCode) {
        this.sysCode = sysCode;
    }

    public Long getColonelId() {
        return colonelId;
    }

    public void setColonelId(Long colonelId) {
        this.colonelId = colonelId;
    }

    public String getFuncScop() {
        return funcScop;
    }

    public void setFuncScop(String funcScop) {
        this.funcScop = funcScop;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }

    public String getPartnerLog() {
        return partnerLog;
    }

    public void setPartnerLog(String partnerLog) {
        this.partnerLog = partnerLog;
    }
}
