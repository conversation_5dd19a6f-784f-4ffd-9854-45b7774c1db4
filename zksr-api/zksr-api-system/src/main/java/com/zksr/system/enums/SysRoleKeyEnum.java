package com.zksr.system.enums;

import com.zksr.common.core.domain.LongArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum SysRoleKeyEnum implements LongArrayValuable {

    ADMIN_ROLE(1L, "admin"),
    SOFTWARE_ADMIN_ROLE(2L, "software-admin"),
    PARTNER_ADMIN_ROLE(3L, "partner-admin"),
    DC_ADMIN_ROLE(4L, "dc-admin"),
    SUPPLIER_ADMIN_ROLE(5L, "supplier-admin"),
    COLONEL_ADMIN_ROLE(6L, "colonel-admin"),
    BRAND_ADMIN_ROLE(-1L, "brand-admin"),
    ;

    public static final long[] ARRAYS = Arrays.stream(values()).mapToLong(SysRoleKeyEnum::getRoleId).toArray();

    private final Long roleId;
    private final String roleKey;

    @Override
    public long[] array() {
        return ARRAYS;
    }


    /**
     * 是否是预制权限
     * @param roleId
     * @return
     */
    public static boolean isPrefabricateRole(Long roleId) {
        return Objects.equals(roleId, PARTNER_ADMIN_ROLE.getRoleId())
                || Objects.equals(roleId, DC_ADMIN_ROLE.getRoleId())
                || Objects.equals(roleId, COLONEL_ADMIN_ROLE.getRoleId())
                || Objects.equals(roleId, SUPPLIER_ADMIN_ROLE.getRoleId());
    }
    /**
     * 是否是预制权限
     * @param roleKey
     * @return
     */
    public static boolean isPrefabricateRole(String roleKey) {
        return Objects.equals(roleKey, PARTNER_ADMIN_ROLE.getRoleKey())
                || Objects.equals(roleKey, DC_ADMIN_ROLE.getRoleKey())
                || Objects.equals(roleKey, COLONEL_ADMIN_ROLE.getRoleKey())
                || Objects.equals(roleKey, BRAND_ADMIN_ROLE.getRoleKey())
                || Objects.equals(roleKey, SUPPLIER_ADMIN_ROLE.getRoleKey());
    }
}
