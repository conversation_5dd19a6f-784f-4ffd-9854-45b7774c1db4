package com.zksr.system.api.bank;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.bank.vo.SysBankChannelPageReqVO;
import com.zksr.system.api.bank.vo.SysBankChannelRespVO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @time 2024/7/24
 * @desc
 */
@FeignClient(contextId = "bankChannelApi", value = ApiConstants.NAME)
public interface BankChannelApi {

    String PREFIX = ApiConstants.PREFIX + "/bankChannel";

    @PostMapping("/getPage")
    CommonResult<PageResult<SysBankChannelRespVO>> getPage(@RequestBody SysBankChannelPageReqVO pageReqVO);

    @PostMapping("/getChannelByNo")
    CommonResult<SysBankChannelRespVO> getChannelByNo(@RequestParam("bankChannelNo") String bankChannelNo);
}
