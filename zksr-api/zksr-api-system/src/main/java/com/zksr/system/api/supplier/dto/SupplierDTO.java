package com.zksr.system.api.supplier.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@NoArgsConstructor
public class SupplierDTO {

    /** 入驻商id */
    private Long supplierId;

    /** 入驻商编号 */
    private String supplierCode;

    /** 平台商id */
    private Long sysCode;

    /** 入驻商名称 */
    private String supplierName;

    /** 联系人 */
    private String contactName;

    /** 联系电话 */
    private String contactPhone;

    /** 联系地址 */
    private String contactAddress;

    /** 状态-数据字典 */
    private Long status;

    /** 备注 */
    private String memo;

    /** 是否是电子围栏入驻商 */
    private Long dzwlFlag;

    /** 电子围栏 */
    private String dzwlInfo;

    /** 运营商id;运营商id */
    private Long dcId;

    private String licenceUrl;

    /** 猎鹰围栏ID */
    private Long gfid;

    /** 本地起送价 */
    private BigDecimal minAmt;

    @ApiModelProperty("全国起送价")
    private BigDecimal globalMinAmt;

    @ApiModelProperty(value = "支付账户最小保留金", example = "1000")
    private BigDecimal minSettleAmt;

    /** 门店ID集合 */
    private List<Long> branchIdList;

    @Excel(name = "公众号openid")
    private String publishOpenid;

    /** 入驻商头像地址 */
    @ApiModelProperty(value = "入驻商头像地址")
    private String avatar;

    @ApiModelProperty(value = "是否是对接第三方的入驻商 false 否  true 是")
    private boolean syncFlag;

    @ApiModelProperty("入驻商要货优先级,0最高, 1次之")
    private Long supplierYhSort;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;
}
