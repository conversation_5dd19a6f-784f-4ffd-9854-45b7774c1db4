package com.zksr.system.api.fileImport;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.domain.SysFileImport;
import com.zksr.system.api.domain.SysFileImportDtl;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "fileImportApi", value = ApiConstants.NAME)
public interface FileImportApi {

    String PREFIX = ApiConstants.PREFIX + "/fileImport";

    @GetMapping(PREFIX + "/getFileImportById")
    CommonResult<SysFileImport> getFileImportById(@RequestParam("fileImportId") Long fileImportId);

    @PostMapping(PREFIX + "/updateFileImport")
    CommonResult<Integer> updateFileImport(@RequestBody SysFileImport sysFileImport);

    @GetMapping(PREFIX + "/updateImportStatus")
    CommonResult<Integer> updateImportStatus(@RequestParam("fileImportId") Long fileImportId, @RequestParam("importStatus") Integer importStatus);

    @PostMapping(PREFIX + "/batchAddFileImportDtl")
    CommonResult<Boolean> batchAddFileImportDtl(@RequestBody List<SysFileImportDtl> list);

    @GetMapping(PREFIX + "/findGoingEvent")
    CommonResult<List<SysFileImport>> findGoingEvent();

}
