package com.zksr.system.api.invoice.dto;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONType;
import lombok.Data;

/**
 * @Description: 发票查验请求DTO
 * @Date: 2025/07/16
 */
@Data
@JSONType(alphabetic = false,           // 坑：一定要设置，不然还是按ascii码顺序排列
        serializeFeatures = {
                JSONWriter.Feature.WriteNulls,  // 保留 null 字段
                JSONWriter.Feature.FieldBased   // 按字段声明顺序
        })
public class InvoiceVerifyRequest {

    /**
     * 发票代码（必填）
     * 除全电专票，全电普票外必传
     */
    private String invoiceCode;

    /**
     * 发票号码（必填）
     * 0109,0110传纸票发票号码
     */
    private String invoiceNo;

    /**
     * 开票日期（必填）
     * 日期格式：yyyyMMdd 举例：20200404
     */
    private String invoiceDate;

    /**
     * 开票不含税金额（可选）
     * 发票种类为 0100，0103，0200,0109,0110时此项不可为空
     * 示例值：2475.25
     */
    private String taxExclusiveAmount;

    /**
     * 开票含税金额（可选）
     * 发票种类为0201，0107，0108,0109,0110时此项不可为空
     * 示例值：2500.00
     */
    private String taxInclusiveAmount;

    /**
     * 发票类型（必填）
     * 增值税专用发票：0100
     * 机动车销售统一发票：0200
     * 增值税普通发票：0101
     * 增值税电子普通发票：0102
     * 增值税电子专用发票：0103
     * 增值税普通发票（卷式）：0300
     * 通行费发票：0313
     * 二手车发票：0201
     * 全电专票：0107
     * 全电普票：0108
     * 全电纸票专票：0109
     * 全电纸票普票：0110
     */
    private String invoiceType;

    /**
     * 校验码（可选）
     * 发票校验码后 6 位。
     * 发票种类为 0101，0102，0300，0313 ,0110时此项不可为空
     * 全电纸票普票:0110传校验密码区的全电发票号码后六位
     */
    private String checkCode;
}
