package com.zksr.system.api.area;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(
        contextId = "remoteAreaApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface AreaApi {
    String PREFIX = ApiConstants.PREFIX + "/area";

    @GetMapping(PREFIX + "/getAreaByAreaId")
    public CommonResult<AreaDTO> getAreaByAreaId(@RequestParam("areaId") Long areaId);

    @GetMapping(PREFIX + "/getAreaListBySysCode")
    public CommonResult<Map<Long, AreaDTO>> getAreaMapBySysCode(@RequestParam(value = "sysCode",required = false) Long sysCode, @RequestParam(value =  "dcId",required = false) Long dcId);

    /**
    * @Description: 根据sysCode获取区域城市
    * @Author: liuxingyu
    * @Date: 2024/3/26 9:09
    */
    @GetMapping(PREFIX + "/getListBySysCode")
    CommonResult<List<AreaDTO>> getListBySysCode(@RequestParam("sysCode") Long sysCode);

    @GetMapping("/checkAreaByAreaId")
    CommonResult<Boolean> checkAreaByAreaId(@RequestParam("areaId") Long areaId);
    /**
    * @Description: 根据入驻商获取区域城市
    * @Author: liuxingyu
    * @Date: 2024/4/18 9:34
    */
    @GetMapping(PREFIX + "/getBySupplierId")
    CommonResult<List<AreaDTO>> getBySupplierId(@RequestParam("supplierId") Long supplierId);

    @GetMapping(PREFIX + "/getDefaultBySyscode")
    CommonResult<AreaDTO> getDefaultBySyscode(@RequestParam(value = "sysCode") Long sysCode);

    @GetMapping(PREFIX + "/getAreaBySyscodeAndDcId")
    CommonResult<List<AreaDTO>> getAreaBySyscodeAndDcId(@RequestParam(value = "sysCode") Long sysCode, @RequestParam(value =  "dcId") Long dcId);

    @GetMapping(PREFIX + "/getAreaListByDcId")
    CommonResult<List<AreaDTO>> getAreaListByDcId( @RequestParam(value =  "dcId") Long dcId);
}
