package com.zksr.system.api.commonMessage.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/7 15:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SubscribeOrderAfterCreateVO {

    @ApiModelProperty("订单ID")
    private Long orderId;

    @ApiModelProperty("售后单号")
    private String afterNo;

    @ApiModelProperty("平台ID")
    private Long sysCode;

    @ApiModelProperty("售后详情ID集合")
    private List<Long> supplierAfterDtlIdList;

    @ApiModelProperty("售后备注")
    private String memo;
}
