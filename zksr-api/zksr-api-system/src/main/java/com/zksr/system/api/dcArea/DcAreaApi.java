package com.zksr.system.api.dcArea;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.dcArea.dto.DcAreaDTO;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(contextId = "dcAreaApi", value = ApiConstants.NAME)
public interface DcAreaApi {

    String PREFIX = ApiConstants.PREFIX + "/dcArea";

    /**
     * 根据运营商id或区域城市id 获取运营商--区域城市关系信息
     * @param dcId
     * @param areaId
     * @param sysCode
     * @return
     */
    @GetMapping(PREFIX + "/getDcAreaByDcIdOrAreaId")
    public CommonResult<DcAreaDTO> getDcAreaByDcIdOrAreaId(@RequestParam(value = "dcId",required = false) Long dcId,@RequestParam(value = "areaId",required = false) Long areaId,@RequestParam(value = "sysCode",required = false) Long sysCode);


    /**
    * @Description: 根据运营商ID获取绑定的区域
    * @Author: liuxingyu
    * @Date: 2024/4/12 10:57
    */
    @GetMapping(PREFIX + "/getByDcId")
    CommonResult<DcAreaGroupDTO> getByDcId(@RequestParam("dcId") Long dcId);
}
