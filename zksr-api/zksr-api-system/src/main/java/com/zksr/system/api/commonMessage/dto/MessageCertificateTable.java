package com.zksr.system.api.commonMessage.dto;

import cn.hutool.core.collection.ListUtil;
import com.zksr.common.core.enums.CommonMessagePushModeEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户凭证存储
 * @date 2024/12/5 15:23
 */
@Data
public class MessageCertificateTable {

    /**
     * 用户凭证
     */
    private Map<MerchantTypeEnum, Map<Long, MessageCertificate>> table = new HashMap<>();

    /**
     * 增加用户凭证信息
     * @param certificate   凭证
     */
    public void add(MessageCertificate certificate) {
        if (table.containsKey(certificate.getMerchantType())) {
            table.get(certificate.getMerchantType()).put(certificate.getMerchantId(), certificate);
        } else {
            HashMap<Long, MessageCertificate> map = new HashMap<>();
            map.put(certificate.getMerchantId(), certificate);
            table.put(certificate.getMerchantType(), map);
        }
    }

    /**
     * 获取凭证信息
     * @param merchantType  商户类型
     * @param merchantId    商户ID
     * @return  凭证
     */
    public MessageCertificate get(MerchantTypeEnum merchantType, Long merchantId) {
        if (!table.containsKey(merchantType)) {
            return null;
        }
        return table.get(merchantType).get(merchantId);
    }
}
