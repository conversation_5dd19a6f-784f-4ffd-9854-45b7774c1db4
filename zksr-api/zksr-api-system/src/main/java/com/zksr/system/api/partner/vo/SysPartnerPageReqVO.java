package com.zksr.system.api.partner.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 平台商信息对象 sys_partner
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@ApiModel("平台商信息 - sys_partner分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SysPartnerPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @ApiModelProperty(value = "平台商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 平台名称 */
    @Excel(name = "平台名称")
    @ApiModelProperty(value = "平台名称")
    private String partnerName;

    /** 关联平台商管理员的账号id */
    @Excel(name = "关联平台商管理员的账号id")
    @ApiModelProperty(value = "关联平台商管理员的账号id")
    private Long partnerUserId;

    /** 负责人姓名 */
    @Excel(name = "负责人姓名")
    @ApiModelProperty(value = "负责人姓名")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 公司地址 */
    @Excel(name = "公司地址")
    @ApiModelProperty(value = "公司地址")
    private String contactAddress;

    /** 状态 数据字典sys_partner_status */
    @Excel(name = "状态 数据字典sys_partner_status")
    @ApiModelProperty(value = "状态 数据字典sys_partner_status")
    private Integer status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 来源 */
    @Excel(name = "来源")
    @ApiModelProperty(value = "来源")
    private String source;

    /** 平台编码 */
    @ApiModelProperty(value = "平台编码")
    private String partnerCode;

    /** 软件商ID */
    @Excel(name = "软件商ID")
    private Long softwareId;
}
