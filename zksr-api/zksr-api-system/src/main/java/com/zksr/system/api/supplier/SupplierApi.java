package com.zksr.system.api.supplier;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.vo.openapi.receive.SpuReceiveVO;
import com.zksr.common.core.domain.vo.openapi.receive.SysSupplierDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.system.api.supplier.vo.SysSupplierRespVO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@FeignClient(
        contextId = "remoteSupplierApi",
        value = ApiConstants.NAME
)
/**
 *
 *  入驻商服务
 * <AUTHOR>
 * @date 2024/3/16 11:29
 */
public interface SupplierApi {

    String PREFIX = ApiConstants.PREFIX + "/supplier";

    @GetMapping(PREFIX + "/getBySupplierId")
    public CommonResult<SupplierDTO> getBySupplierId(@RequestParam("supplierId") Long supplierId);

    /**
     * 根据入驻商Id
     * @param supplierIds
     * @return
     */
    @PostMapping(PREFIX + "/getSupplierNames")
    public CommonResult<List<String>> getSupplierNames(@RequestParam("supplierIds") List<Long> supplierIds);

    /**
     * 根据运营商、平台商查询入驻商信息Map集合
     *
     * @param dcId 运营商ID
     * @return
     */
    @GetMapping(PREFIX + "/userInfoByMap")
    public CommonResult<Map<Long, SupplierDTO>> getUserInfoByMap(@RequestParam(value = "dcId", required = false) Long dcId, @RequestParam(value = "supplierNo", required = false) Long supplierNo);

    /**
     * 根据运营商、平台商查询入驻商信息List集合
     *
     * @param dcId 运营商ID
     * @return
     */
    @GetMapping(PREFIX + "/userInfoByList")
    public CommonResult<List<SupplierDTO>> getUserInfoByList(@RequestParam(value = "dcId", required = false) Long dcId, @RequestParam(value = "supplierNo", required = false) Long supplierNo);

    /**
     * 根据运营商、平台商查询入驻商信息
     *
     * @param dcId 运营商ID
     * @return
     */
    @GetMapping(PREFIX + "/listByUser")
    public CommonResult<Map<Long, SupplierDTO>> getUserInfo(@RequestParam(value = "dcId", required = false) Long dcId);

    /**
     * @Description: 根据运营商ID获取区域下所有的入驻商
     * @Author: liuxingyu
     * @Date: 2024/4/9 17:30
     */
    @GetMapping(PREFIX + "/getByOrder")
    CommonResult<List<Long>> getByOrder(@RequestParam("dcId") Long dcId);

    /**
     * 获取分页请求
     * @param pageReqVO
     * @return
     */
    @PostMapping(PREFIX + "/getPage")
    CommonResult<PageResult<SysSupplierRespVO>> getPage(@RequestBody SysSupplierPageReqVO pageReqVO);

    /**
     * 新增入驻商信息  新增或者修改
     *
     * @param sysSupplierDTO
     */
    @PostMapping(PREFIX + "/addSupplier")
    CommonResult<Boolean> addSupplier(
            @RequestParam("supplierId") Long supplierId,
            @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
            @RequestBody SysSupplierDTO sysSupplierDTO);

    /**
     * 校验该入驻商是否对接第三方系统
     *
     * @param supplierId 入驻商id
     * @return
     */
    @GetMapping(PREFIX + "/checkSyncConfig")
    CommonResult<Boolean> checkSyncConfig(@RequestParam("supplierId")Long supplierId);

    /**
     * 获取分页请求
     * @param pageReqVO
     * @return
     */
    @PostMapping(PREFIX + "/getSupplierPage")
    CommonResult<PageResult<SupplierDTO>> getSupplierPage(@RequestBody SysSupplierPageReqVO pageReqVO);
}
