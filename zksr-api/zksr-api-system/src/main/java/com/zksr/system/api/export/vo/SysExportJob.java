package com.zksr.system.api.export.vo;

import cn.hutool.poi.excel.BigExcelWriter;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 导出任务
 * @date 2024/1/15 10:57
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class SysExportJob extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "任务名称", hidden = true)
    private String name;

    @ApiModelProperty(value = "备注", hidden = true)
    private String remark;

    @ApiModelProperty(value = "任务KEY", hidden = true)
    private String jobKey;

    @ApiModelProperty(value = "用户信息", hidden = true)
    private String userInfo;

    /**
     * 参见 {@link com.zksr.file.api.constant.ReportState}
     */
    @ApiModelProperty(value = "状态", hidden = true)
    private String state;


    @ApiModelProperty(value = "文件地址", hidden = true)
    private String file;

    /**
     * 参加 {@link com.zksr.file.api.constant.ExportType}
     */
    @ApiModelProperty("导出类型")
    private String exportType;

    @ApiModelProperty("导出参数")
    private String queryData;

    @ApiModelProperty(value = "执行时间",hidden = true)
    private Date executeTime;

    @ApiModelProperty(value = "完成时间", hidden = true)
    private Date finishTime;

    @ApiModelProperty(value = "请求IP", hidden = true)
    private String remoteIp;

    @ApiModelProperty(value = "用户key",hidden = true)
    private String userKey;

    /**
     * 平台商编号
     */
    @ApiModelProperty(value = "平台商编号", hidden = true)
    private Long sysCode;

    @ApiModelProperty(value = "导出列配置sys_user_column", hidden = true)
    private String colConfig;

    public void setRemark(String remark) {
        // 处理异常过长
        if (StringUtils.isNotEmpty(remark) && remark.length() > 128) {
            this.remark = remark.substring(0, 128);
        } else {
            this.remark = remark;
        }
    }

    public static SysExportJob getInstance() {
        SysExportJob job = new SysExportJob();
        job.setCreateTime(new Date());
        job.setUpdateTime(new Date());
        return job;
    }

    public <T> T getQueryData(Class<T> tClass) {
        return JSON.parseObject(queryData, tClass);
    }
}
