package com.zksr.system.api.sms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:   短信消息发送结果
 * @date 2024/4/30 9:57
 */
@Builder
@Data
@ApiModel(description = "标准验证码短信发送结果")
@NoArgsConstructor
public class SmsCodeRespDTO {

    @ApiModelProperty(value = "验证码")
    private String code;

    public SmsCodeRespDTO(String code) {
        this.code = code;
    }

    public static SmsCodeRespDTO success(String code) {
        return new SmsCodeRespDTO(code);
    }
}
