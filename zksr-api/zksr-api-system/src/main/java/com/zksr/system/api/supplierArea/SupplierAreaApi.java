package com.zksr.system.api.supplierArea;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.supplierArea.dto.SupplierAreaDTO;
import com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(
        contextId = "remoteSupplierAreaApi",
        value = ApiConstants.NAME
)
/**
 *
 *  入驻商服务
 * <AUTHOR>
 * @date 2024/3/16 11:29
 */
public interface SupplierAreaApi {

    String PREFIX = ApiConstants.PREFIX + "/supplierArea";

    @GetMapping(PREFIX + "/getSupplierAreaByAreaId")
    public CommonResult<List<SupplierAreaDTO>> getSupplierAreaByAreaId(@RequestParam("areaId") Long areaId);

    /**
     * 获取电子围栏绑定的入驻商
     * @param areaId
     * @return
     */
    @GetMapping(PREFIX + "/getDzwlSupplierInfoByAreaId")
    public CommonResult<List<SupplierDzwlAreaDTO>> getDzwlSupplierInfoByAreaId(@RequestParam("areaId") Long areaId);

    /**
     * 查询入驻商运营区域
     * @param supplierId
     * @return
     */
    @GetMapping(PREFIX + "/selectListBySupplierId")
    CommonResult<List<Long>> selectListBySupplierId(@RequestParam("supplierId")Long supplierId);
}
