package com.zksr.system.api.supplier.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
public class SupplierOrderVO {

    @ApiModelProperty("订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("门店id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long branchId;

    @ApiModelProperty("支付方式")
    private String payWay;

    @ApiModelProperty("订单类型 0：全国商品 1：本地商品")
    private Integer orderType;

    @ApiModelProperty("平台编号")
    private Long sysCode;

    @ApiModelProperty("入驻商订单Id（子订单Id）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierOrderId;

    @ApiModelProperty("入驻商订单号（子订单号）")
    private String supplierOrderNo;

    @ApiModelProperty("入驻商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createDate;


    @ApiModelProperty("订单备注")
    private String memo;

    @ApiModelProperty("业务员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long colonelId;

    @ApiModelProperty("运营商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dcId;

    @ApiModelProperty("入驻商订单备注")
    private String supplierMemo;

    @ApiModelProperty("上级业务员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pcolonelId;

    @ApiModelProperty("支付状态 数据字典：sys_pay_status")
    private String payState;

    @Excel(name = "状态")
    @ApiModelProperty(value = "异常状态")
    private String errorState = "正常";

    @ApiModelProperty(value = "异常说明")
    private String errorMemo;

    @Excel(name = "推送状态")
    @ApiModelProperty("推送状态 0未推送 1已推送 2已接收")
    private Integer pushStatus;

    @ApiModelProperty(value = "入驻商订单外部订单号")
    private String sourceOrderNo;

    @ApiModelProperty("订单支付时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date payTime;

    @ApiModelProperty(name = "司机ID")
    private Long driverId;

    @ApiModelProperty(name = "司机评价状态")
    private Integer driverRatingFlag;

    @ApiModelProperty(name = "司机评价ID")
    private Long driverRatingId;

    @ApiModelProperty(name = "打印状态（0：未打印，1：已打印）")
    private Integer printState;

    @ApiModelProperty("打印次数")
    private Long printQty;

    @ApiModelProperty("优惠金额")
    private BigDecimal subDiscountAmt;

    @ApiModelProperty("支付金额")
    private BigDecimal subPayAmt;

    @ApiModelProperty("订单金额")
    private BigDecimal subOrderAmt;

    @ApiModelProperty("订单数量")
    private Integer subOrderNum;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("门店地址")
    private String branchAddr;

    @ApiModelProperty("门店门头照片")
    private String branchImages;

    @ApiModelProperty("门店联系人名称")
    private String branchContactName;

    @ApiModelProperty("门店联系人电话")
    private String branchContactPhone;

    @ApiModelProperty("业务员名称")
    private String colonelName;

    @ApiModelProperty("业务员联系电话")
    private String colonelPhone;

    @ApiModelProperty("业务员经理")
    private String pcolonelName;

    @ApiModelProperty("上级业务员联系电话")
    private String pcolonelPhone;

}
