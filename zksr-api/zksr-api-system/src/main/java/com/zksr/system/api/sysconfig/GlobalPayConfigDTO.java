package com.zksr.system.api.sysconfig;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 全局支付平台参数
 * @date 2024/4/20 16:47
 */

@Data
@ApiModel(description = "全局支付平台参数")
public class GlobalPayConfigDTO {

    @ApiModelProperty("提现配置")
    private Withdraw withdraw;

    @ApiModelProperty("支付配置")
    private Pay pay;

    @ApiModelProperty("商户配置")
    private Merchant merchant;

    @Data
    @ApiModel(description = "提现配置")
    public static class Withdraw {

        @ApiModelProperty("对私提现/笔")
        private BigDecimal privateMuch;

        @ApiModelProperty("对公提现/笔")
        private BigDecimal publicMuch;
    }

    @Data
    @ApiModel(description = "支付配置")
    public static class Pay {

        @ApiModelProperty(value = "支付手续费比例", notes = "不需要除100, 可直接乘")
        private BigDecimal freeRate;
    }

    @Data
    @ApiModel(description = "商户配置")
    public static class Merchant {

        @ApiModelProperty(value = "公共产品商户签名秘钥", notes = "公共产品商户签名秘钥")
        private String md5SignKey;
    }
}
