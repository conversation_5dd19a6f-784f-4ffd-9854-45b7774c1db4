package com.zksr.system.api.partnerConfig.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 合利宝支付配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class HeLiBaoPayConfigDTO {
    /**
     * 支付启用状态(0 禁用 1 启用)
     */
    @ApiModelProperty("支付启用状态(0 禁用 1 启用)")
    private String payEnableState;

    /**
     * 公钥证书
     */
    @ApiModelProperty("公钥证书")
    private String publicKeyCertificate;

    /**
     * 私钥证书
     */
    @ApiModelProperty("私钥证书")
    private String privateKeyCertificate;

    /**
     * 私钥密码
     */
    @ApiModelProperty("私钥密码")
    private String privateKeyPassword;

    /**
     * 支付回调地址
     */
    @ApiModelProperty("支付回调地址")
    private String payCallBackUrl;

    /**
     * 合利宝平台商户号
     */
    @ApiModelProperty("合利宝平台商户号")
    private String heLiBaoMerchantPlatformAccount;

    /**
     * 合利宝子商户号
     */
    @ApiModelProperty("合利宝子商户号")
    private String heLiBaoMerchantBypassAccount;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    /**
     * 合利宝平台商户号
     */
    @ApiModelProperty("合利宝平台商户号")
    private String platformMerchantNo;

    /**
     * 公共平台md5签名秘钥
     */
    @ApiModelProperty("公共产品MD5签名秘钥")
    private String publicMerchantMd5SignKey;

    /**
     * 公共产品Des3加密key
     */
    @ApiModelProperty("公共产品Des3加密key")
    private String publicEncryptKey;


    @ApiModelProperty("支付宝小程序appid")
    private String alipayXcxAppId;

    @ApiModelProperty("支付宝私钥")
    private String alipayXcxPrivateKey;

    @ApiModelProperty("支付宝公钥")
    private String alipayXcxPublicKey;
}
