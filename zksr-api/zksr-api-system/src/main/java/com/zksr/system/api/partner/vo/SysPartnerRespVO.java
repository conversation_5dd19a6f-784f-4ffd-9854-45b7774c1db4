package com.zksr.system.api.partner.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 平台商信息对象 sys_partner
 *
 * <AUTHOR>
 * @date 2024-01-22
 */
@Data
@ApiModel("平台商信息 - sys_partner Response VO")
public class SysPartnerRespVO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @ApiModelProperty(value = "来源")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 平台名称 */
    @Excel(name = "平台名称")
    @ApiModelProperty(value = "平台名称")
    private String partnerName;

    /** 关联平台商管理员的账号id */
    @Excel(name = "关联平台商管理员的账号id")
    @ApiModelProperty(value = "关联平台商管理员的账号id")
    private Long partnerUserId;

    /** 负责人姓名 */
    @Excel(name = "负责人姓名")
    @ApiModelProperty(value = "负责人姓名")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 公司地址 */
    @Excel(name = "公司地址")
    @ApiModelProperty(value = "公司地址")
    private String contactAddress;

    /** 状态 数据字典sys_partner_status */
    @Excel(name = "状态 数据字典sys_partner_status")
    @ApiModelProperty(value = "状态 数据字典sys_partner_status")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 来源 */
    @Excel(name = "来源")
    @ApiModelProperty(value = "来源")
    private String source;

    /** 平台编码 */
    @ApiModelProperty(value = "平台编码")
    private String partnerCode;

    /** 城市数量 */
    @ApiModelProperty(value = "城市数量")
    private Integer areaNum;

    /** 运营商数量 */
    @ApiModelProperty(value = "运营商数量")
    private Integer dcNum;

    /** 入驻商数量 */
    @ApiModelProperty(value = "入驻商数量")
    private Integer supplierNum;

    /** 银行名称 */
    @ApiModelProperty(value = "银行名称", required = true, example = "浦发银行")
    private String bankName;

    /** 银行支行 */
    @ApiModelProperty(value = "银行支行", required = true, example = "浦发银行麓谷支行")
    private String bankBranch;

    /** 银行卡号 */
    @ApiModelProperty(value = "银行卡号", required = true, example = "***************")
    private String accountNo;


    @ApiModelProperty(value = "平台商账户")
    private String partnerAccount;

    @ApiModelProperty(value = "平台商密码")
    private String partnerAccountPwd;

    /** 软件商ID */
    @Excel(name = "软件商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long softwareId;

    /** 软件商分润比例 */
    @Excel(name = "软件商分润比例")
    private BigDecimal softwareRate;

    /** 租户编码 */
    private String saasTenantCode;

    //软件商支付账号配置
    /** 商户绑定信息 */
    @ApiModelProperty(value = "商户绑定信息")
    private List<PlatformSimpleBindVO> platformSimpleBindList;
}
