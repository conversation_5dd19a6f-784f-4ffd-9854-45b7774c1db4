package com.zksr.system.api.dictData;

import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/1 9:59
 */
@FeignClient(contextId = "DictDataApi", value = ApiConstants.NAME)
public interface DictDataApi {

    String PREFIX = ApiConstants.PREFIX + "/dictData";

    @PostMapping(PREFIX + "/selectDictDataList")
    List<SysDictData> selectDictDataList(@RequestBody SysDictData dictData);

    @GetMapping(PREFIX + "/selectDictLabel")
    String selectDictLabel(@RequestParam("dictType") String dictType, @RequestParam("dictValue")String dictValue);

    @GetMapping(PREFIX + "/selectDictDataById")
    SysDictData selectDictDataById(@RequestParam("dictCode")Long dictCode);

    @GetMapping(PREFIX + "/selectDictDataByType")
    void deleteDictDataByIds(Long[] dictCodes);

    @PostMapping(PREFIX + "/insertDictData")
    int insertDictData(@RequestBody SysDictData dictData);

    @PostMapping(PREFIX + "/updateDictData")
    int updateDictData(@RequestBody SysDictData dictData);


    @GetMapping(PREFIX + "/getUnitByName")
    SysDictData getUnitByName(@RequestParam("dictType")String dictType, @RequestParam("smallUnit")String smallUnit);

    @GetMapping(PREFIX + "/getMaxdictValue")
    String getMaxdictValue(@RequestParam("dictType") String dictType);
}
