package com.zksr.system.api;

import com.zksr.system.api.domain.JSONFile;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import com.zksr.common.core.constant.ServiceNameConstants;
import com.zksr.common.core.domain.R;
import com.zksr.system.api.domain.SysFile;
import com.zksr.system.api.factory.RemoteFileFallbackFactory;

/**
 * 文件服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteFileService", value = ServiceNameConstants.FILE_SERVICE, fallbackFactory = RemoteFileFallbackFactory.class)
public interface RemoteFileService
{
    /**
     * 上传文件
     *
     * @param file 文件信息
     * @return 结果
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<SysFile> upload(@RequestPart(value = "file") MultipartFile file);

    /**
     * 上传JSON
     * @param jsonFile 文件信息
     * @return 结果
     */
    @PostMapping(value = "/uploadJson")
    public R<SysFile> uploadJson(@RequestBody JSONFile jsonFile);
}
