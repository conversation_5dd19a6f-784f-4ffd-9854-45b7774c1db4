package com.zksr.system.api.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 美的付请求实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MideaPayConfig {

    @ApiModelProperty("回调域名前缀")
    private String notifyUrl;

    /**
     * 美的付平台商户号
     */
    @ApiModelProperty("美的付平台商户号")
    private String merchantNo;

    /**
     * 美的付入驻商充值商户号
     */
    @ApiModelProperty("美的付入驻商充值商户号")
    private String subMerchantNo;

    /**
     * 签名地址
     */
    @ApiModelProperty("签名地址")
    private String signUrl;

    /**
     * 验签地址
     */
    @ApiModelProperty("验签地址")
    private String checkSignUrl;

    /**
     * 美的付地址
     */
    @ApiModelProperty("美的付地址")
    private String payUrl;

    private String partnerKey;

    public boolean checkEmpty(){
        return StringUtils.isEmpty(this.getMerchantNo())
                || StringUtils.isEmpty(this.getSubMerchantNo())
                || StringUtils.isEmpty(this.getSignUrl())
                || StringUtils.isEmpty(this.getPayUrl())
                || StringUtils.isEmpty(this.getCheckSignUrl())
                || StringUtils.isEmpty(this.getNotifyUrl());
    }
}
