package com.zksr.system.api.openapi.dto.hisense;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class InvoiceUpdateRequest {
    // 销售单号
    private String orderCode;
    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    // 修改原因
    private String reason;
    // 修改人
    private String userId;
    // 发票抬头
    private String title;
    // 手机号
    private String phoneNumber;
    // 扩展字段备用
    private String extra;
    // 发票类型 1表示电子发票,2表示增值税发票
    private String type;
    // 公司名称
//    private String companyName;
    // 抬头类型 1表示个人,2表示公司
    private String titleType;

    // 税号
    private String taxRegisterNo;

    // 注册地址
    private String registerAddress;

    // 注册单位电话
    private String registerPhone;

    // 开户行
    private String registerBank;

    // 银行账户
    private String bankAccount;

    // 收件人姓名
    private String receiverName;

    // 收件人地址
    private String receiverAddress;

}
