package com.zksr.system.api.commonMessage;

import com.zksr.common.core.enums.CommonMessageSceneEnum;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderAfterFinishVO;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderAfterCreateVO;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderCancelVO;
import com.zksr.system.api.commonMessage.vo.SubscribeOrderReceiveVO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @time 2024/6/13
 * @desc 公众号小程序等订阅消息
 */
@FeignClient(
        contextId = "SubscribeApi",
        value = ApiConstants.NAME
)
public interface SubscribeApi {

    String PREFIX = ApiConstants.PREFIX + "/subscribe";


    /**
     * 用户新订单通知(公众号)
     * 参见 {@link CommonMessageSceneEnum#NEW_ORDER_PUBLISH}
     */
    @PutMapping(PREFIX + "/sendNewOrder")
    CommonResult<Boolean> sendNewOrder(@RequestParam("orderId") Long orderId, @RequestParam("sysCode") Long sysCode);

    /**
     * 门店订单发货通知(商家助手)
     * 参见 {@link CommonMessageSceneEnum#LOCAL_DELIVERY_ORDER}
     */
    @PutMapping(PREFIX + "/sendDeliveryOrder")
    CommonResult<Boolean> sendWxMerchantDeliveryOrder(@RequestParam("orderId") Long orderId, @RequestParam("supplierId") Long supplierId, @RequestParam("sysCode") Long sysCode);

    /**
     * 订单发货前取消, 含未付款取消
     */
    @PostMapping(PREFIX + "/orderCancel")
    CommonResult<Boolean> orderCancel(@RequestBody SubscribeOrderCancelVO cancelVO);

    /**
     * 全国订单发货通知
     */
    @PutMapping(PREFIX + "/globalOrderDelivery")
    CommonResult<Boolean> globalOrderDelivery(@RequestParam("supplierOrderDtlId") Long supplierOrderDtlId, @RequestParam("sysCode") Long sysCode);

    /**
     * 订单收货
     */
    @PostMapping(PREFIX + "/orderReceive")
    CommonResult<Boolean> orderReceive(@RequestBody SubscribeOrderReceiveVO receiveVO);

    /**
     * 售后创建, 不是取消, 需要审核的
     */
    @PostMapping(PREFIX + "/afterCreate")
    CommonResult<Boolean> afterCreate(@RequestBody SubscribeOrderAfterCreateVO cancelVO);

    /**
     *  售后审核
     */
    @PutMapping(PREFIX + "/afterAudit")
    CommonResult<Boolean> afterAudit(@RequestParam("supplierAfterId") Long supplierAfterId, @RequestParam("sysCode") Long sysCode);

    /**
     * 售后完成 (退款完成) 或者货到付款未付款
     */
    @PostMapping(PREFIX + "/afterFinish")
    CommonResult<Boolean> afterFinish(@RequestBody SubscribeOrderAfterFinishVO afterVO);

    /**
     * 门店注册事件
     */
    @PutMapping(PREFIX + "/branchCreate")
    CommonResult<Boolean> branchCreate(@RequestParam("branchId") Long branchId, @RequestParam("sysCode") Long sysCode);
}
