package com.zksr.system.api.EmailMessage.dto;

import com.zksr.system.api.opensource.dto.OpensourceDto;
import lombok.*;

import java.util.List;

/**
* 同步数据报表统计Email实体
* @date 2024/7/18 15:32
* <AUTHOR>
*/
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncEmailReportData {
    /** 文件地址 */
    private String fileUrl;

    /** 总发送条数 */
    private Integer sendCount;

    /** 总失败条数 */
    private Integer failCount;

    /** 文件名 */
    private String fileName;

    /** 开放能力信息 */
    private List<OpensourceDto> opensourceDtoList;


}
