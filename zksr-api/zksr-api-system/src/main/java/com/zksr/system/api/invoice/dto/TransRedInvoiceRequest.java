package com.zksr.system.api.invoice.dto;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONType;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 红票开具/红冲请求DTO
 * @Date: 2025/07/16
 */
@Data
@JSONType(alphabetic = false,           // 坑：一定要设置，不然还是按ascii码顺序排列
        serializeFeatures = {
                JSONWriter.Feature.WriteNulls,  // 保留 null 字段
                JSONWriter.Feature.FieldBased   // 按字段声明顺序
        })
public class TransRedInvoiceRequest {

    /**
     * 上游传入唯一流水号（必填）
     */
    private String redBizId;

    /**
     * 临时蓝票编号（可选）
     */
    private String bizId;

    /**
     * 发票类型（必填）
     * 1: 电子普票
     * 3: 纸质普票
     * 4: 纸质专票
     */
    private Integer invoiceType;

    /**
     * 红冲金额（必填）
     */
    private BigDecimal redAmount;

    /**
     * 红冲明细列表的数量（必填）
     * 用于校验传入明细数量是否正确
     */
    private Integer redCount;

    /**
     * 主体税号-开票方（必填）
     * 主体税号/统一编码
     */
    private String taxpayerCode;

    /**
     * 发票抬头（必填）
     * 1: 个人
     * 2: 企业
     */
    private Integer invoiceHead;

    /**
     * 受票方税号（可选）
     * 专票必填
     */
    private String payTaxpayerCode;

    /**
     * 受票方（可选）
     * 专票必填
     */
    private String payTaxpayerName;

    /**
     * 客户地址（可选）
     * 专票必填
     */
    private String payUnitAddress;

    /**
     * 客户电话号码（可选）
     * 专票必填
     */
    private String payFixedPhoneNumber;

    /**
     * 客户开户行（可选）
     * 专票必填
     */
    private String payBankName;

    /**
     * 客户银行账户（可选）
     * 专票必填
     */
    private String payBankAccount;

    /**
     * 邮箱（可选）
     */
    private String mail;

    /**
     * OU编码（可选）
     */
    private String ouCode;

    /**
     * OU名称（可选）
     */
    private String ouName;

    /**
     * 省份中心（可选）
     */
    private String provinceCentre;

    /**
     * 原蓝票号码（可选）
     * 普票红冲必填
     */
    private String blueInvoiceNo;

    /**
     * 原蓝票代码（可选）
     * 普票红冲必填
     */
    private String blueInvoiceCode;

    /**
     * 原蓝票开票日期（可选）
     * 示例值：2021-09-14
     */
    private String blueInvoiceDate;

    /**
     * 原蓝票校验码（可选）
     */
    private String blueInvoiceCheckCode;

    /**
     * 原蓝票不含税金额（可选）
     */
    private BigDecimal blueNotContainTaxAmt;

    /**
     * 原蓝票税额（可选）
     */
    private BigDecimal blueTaxAmt;

    /**
     * 原蓝票类型（可选）
     */
    private Integer blueInvoiceType;

    /**
     * 票面备注/描述（可选）
     */
    private String remarks;

    /**
     * 红字通知单号（可选）
     * 税局申请的红字通知单单号，如有可传，没有也必须在发票平台补充该信息后才可开票
     */
    private String redNoticeNo;

    /**
     * 蓝字发票消费税用途（可选）
     * 1: 勾选抵扣
     * 2: 出口退税
     * 3: 代办出口退税
     * 4: 不抵扣
     */
    private String blueSaleTaxType;

    /**
     * 发票入账状态（可选）
     * 0: 未入账
     * 1: 已入账
     */
    private String billingState;

    /**
     * 冲红原因（可选）
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    private String redReason;

    /**
     * 开票成功接收短信的手机号（可选）
     * 开票成功后接收发票链接手机
     */
    private String messagePhone;

    /**
     * 主业务编号（必填）
     * 主业务单号，可在开票系统查询开票情况
     */
    private String businessNo;

    /**
     * 发票明细（必填）
     * 红冲明细，多条，不能为空
     */
    private List<SpecialRedInfoDetailDTO> detailList;

    /**
     * 是否可修改（必填）
     * 0: 不可修改，直接进入开票流程
     * 1: 可修改，需确认票面信息
     * 不传时，普通电票进入发票管理列表（状态为开票中），其它发票类型待确认
     * 说明：不可修改，非专票进入开票管理列表，专票待确认
     * 说明：可修改，所有发票待确认
     */
    private Integer editable;

    /**
     * @Description: 红票明细DTO
     */
    @Data
    @JSONType(alphabetic = false,           // 坑：一定要设置，不然还是按ascii码顺序排列
            serializeFeatures = {
                    JSONWriter.Feature.WriteNulls,  // 保留 null 字段
                    JSONWriter.Feature.FieldBased   // 按字段声明顺序
            })
    public static class SpecialRedInfoDetailDTO {

        /**
         * 开票项唯一id（必填）
         * bizDetailId长度不能超过32(相当于明细主键）
         */
        private String bizDetailId;

        /**
         * 业务单号（必填）
         * 业务单号businessNo字段不能为空（可以重复）
         */
        private String businessNo;

        /**
         * 商品编码（可选）
         * 商品编码code长度不能超过32
         * 1、如果此项如果传入数据，那么以下黄底字段可以为空，开票项税码税率等信息以发票系统为准
         *    （此时需要保证商品等信息在发票平台有配置，并且传入的编码要和在发票平台配置的编码一样）
         * 2、如果此不传数据，那么以下黄底字段部分必填（除了单位），开票项税码税率等信息以以下黄底字段为准。
         */
        private String code;

        /**
         * 开票项目/商品名称（必填）
         * 示例值：电饭煲
         */
        private String goodsName;

        /**
         * 商品单位（可选）
         * 示例值：台
         */
        private String goodsUnit;

        /**
         * 税码（必填）
         * 长短码都可传，支持长、短税码，长税码固定19位，确保在主数据平台有维护
         */
        private String taxCode;

        /**
         * 税率（必填）
         * 示例值：3%的税率，请传入0.03
         */
        private BigDecimal taxRate;

        /**
         * 规格编码（可选）
         */
        private String standards;

        /**
         * 数量（必填）
         */
        private BigDecimal goodCount;

        /**
         * 含税单价（必填）
         * 单价, 必须为正数，最多支持8位小数，但开票金额只支持两位
         */
        private BigDecimal taxUnitPrice;

        /**
         * 含税金额（必填）
         * 含税金额amtContainTax必须为两位小数
         * 示例值：150.00
         */
        private BigDecimal amtContainTax;

        /**
         * 税额（可选）
         * 如果上游传了就用上游的税额，没传系统会进行反算，反算可能会产生一定误差，所以建议上游都传
         */
        private BigDecimal amtTax;

        /**
         * 扩展字段（可选）
         * 额外扩展字段(软著字段等)
         */
        private String softwareTitle;

        /**
         * 是否享受优惠政策（可选）
         * 是否享受特殊优惠政策开票
         * 0：不享受
         * 1：享受
         */
        private Integer discountStatus;

        /**
         * 优惠政策类型（可选）
         * 0：普通零税
         * 1：不征税
         * 2：免税
         * 5：简易征收
         * 6：按5%简易征收
         */
        private String discountType;
    }

}
