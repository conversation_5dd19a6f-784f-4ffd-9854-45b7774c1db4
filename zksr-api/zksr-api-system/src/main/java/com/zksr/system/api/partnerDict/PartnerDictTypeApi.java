package com.zksr.system.api.partnerDict;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "partnerDictTypeApi", value = ApiConstants.NAME)
public interface PartnerDictTypeApi {

    String PREFIX = ApiConstants.PREFIX + "/partnerDict/type";

    /**
     * 根据字典类型查询字典数据
     * @param dictType
     * @return
     */
    @GetMapping(PREFIX + "/selectDictDataByType")
    CommonResult<List<SysPartnerDictData>> selectDictDataByType(@RequestParam("dictType") String dictType,@RequestParam("sysSource") String sysSource);

}
