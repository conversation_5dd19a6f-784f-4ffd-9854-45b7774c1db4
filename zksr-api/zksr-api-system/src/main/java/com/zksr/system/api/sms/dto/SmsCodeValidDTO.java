package com.zksr.system.api.sms.dto;

import com.zksr.common.core.utils.ServletUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:   验证验证码是否正确
 * @date 2024/4/30 9:57
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(description = "验证码短信验证码")
public class SmsCodeValidDTO {

    @ApiModelProperty(value = "手机号", required = true)
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "验证码", required = true)
    @NotEmpty(message = "验证码")
    private String code;

    /**
     * 参见 {@linkplain ServletUtils#getClientIP()}
     */
    @ApiModelProperty(value = "发送IP", required = true)
    @NotEmpty(message = "发送 IP 不能为空")
    private String createIp;
}
