package com.zksr.system.api.partnerConfig.dto;

import com.zksr.common.core.pool.StringPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信小程序b2b支付, 服务器授权状态
 * @date 2024/8/13 10:26
 */
@Data
@ApiModel(description = "服务器授权状态, 配置项wxB2bPayAppletStatus.appId")
public class WxB2bPayAppletStatusDTO {

    @ApiModelProperty("授权认证状态, 0-未获取, 1-已认证, 如果是未获取的状态下, 没有权限替小程序调用相关api")
    private String authComponentStatus = StringPool.ZERO;

    @ApiModelProperty("开启b2b支付状态, 0-未开通, 1-已开通")
    private String openB2bPayStatus = StringPool.ZERO;

    @ApiModelProperty("报名技术服务费商户号")
    private String subMchid;

    @ApiModelProperty("技术服务费万分比，比如 40 指的是 0.40%")
    private String profitRate;
}
