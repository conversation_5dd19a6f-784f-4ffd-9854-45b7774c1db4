package com.zksr.system.api.amap;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.amap.dto.LongitudeAndLatitudeResult;
import com.zksr.system.api.amap.vo.LongitudeAndLatitudeParam;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        contextId = "remoteAmapApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface AmapApi {
    String PREFIX = ApiConstants.PREFIX + "/amap";

    @PostMapping (PREFIX + "/getBranchLongitudeAndLatitude")
    public CommonResult<LongitudeAndLatitudeResult> getBranchLongitudeAndLatitude(@RequestBody LongitudeAndLatitudeParam longitudeAndLatitudeParam);
}
