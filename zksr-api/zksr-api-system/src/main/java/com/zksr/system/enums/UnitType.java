package com.zksr.system.enums;

import lombok.Getter;

/**
 * @Description: 单位枚举类
 * @Author: liuxingyu
 * @Date: 2024/4/22 10:50
 */
public enum UnitType {
    bottle("瓶", 1L),
    piece("件", 2L),
    carton("箱", 3L),
    Pail("桶", 4L),
    ;

    @Getter
    private String label;
    @Getter
    private Long value;

    UnitType(String label, Long value) {
        this.label = label;
        this.value = value;
    }

    public static String matchingLabel(Long code) {
        UnitType[] unitTypes = values();
        for (UnitType unitType : unitTypes) {
            if (unitType.getValue().equals(code)) {
                return unitType.getLabel();
            }
        }
        return null;
    }
}
