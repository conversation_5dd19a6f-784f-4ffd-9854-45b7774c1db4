package com.zksr.system.api.commonMessage.dto;

import com.zksr.common.core.enums.CommonMessagePushModeEnum;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.system.api.commonMessage.vo.SubscribeEventBodyVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订阅消息模版配置详情
 * @date 2024/6/13 9:22
 */
@Data
@NoArgsConstructor
@ApiModel(description = "消息通知上下文对象")
public class CommonMessageContext<EventData> {

    /**
     * 事件参数
     */
    private SubscribeEventBodyVO<EventData> eventBodyVO;

    /**
     * 消息模版
     */
    private MessageTemplateDTO messageTemplateDTO;

    /**
     * 用户信息凭证
     */
    private MessageCertificateTable messageCertificateTable = new MessageCertificateTable();

    /**
     * 流程上下文
     */
    private IFlowContext flowContext;

    /**
     * 消息容器, 存储消息可选内容
     */
    private MessageTable messageTable;

    public CommonMessageContext(SubscribeEventBodyVO<EventData> eventBodyVO, MessageTemplateDTO messageTemplateDTO) {
        this.eventBodyVO = eventBodyVO;
        this.messageTemplateDTO = messageTemplateDTO;
    }

    public EventData getEventData() {
        return getEventBodyVO().getEventData();
    }

    public void setFlowContext(IFlowContext context) {
        this.flowContext = context;
    }

    public <T> T getFlowContext(Class<T> tClass) {
        return (T) flowContext;
    }
}
