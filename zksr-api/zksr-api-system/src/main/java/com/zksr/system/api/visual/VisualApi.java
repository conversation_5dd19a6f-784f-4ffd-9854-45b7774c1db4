package com.zksr.system.api.visual;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.visual.dto.VisualSettingDetailDto;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
* 可视化feign调用
* @date 2024/7/30 16:07
* <AUTHOR>
*/
@FeignClient(
        contextId = "VisualApi",
        value = ApiConstants.NAME
)
public interface VisualApi {

    String PREFIX = ApiConstants.PREFIX + "/visual";

    @GetMapping(PREFIX + "/getVisualMasterById")
    public CommonResult<VisualSettingMasterDto> getVisualMasterById(@RequestParam("visualMasterId") Long visualMasterId);

    @GetMapping(PREFIX + "/getVisualDetailByMasterIdAndTemplate")
    public CommonResult<VisualSettingDetailDto> getVisualDetailByMasterIdAndTemplate(@RequestParam("masterId") Long masterId,@RequestParam("templateType")Long templateType);
}
