package com.zksr.system.api.area.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class AreaDTO implements Serializable {
    @ApiModelProperty(value = "区域城市id")
    private Long areaId;

    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "父id")
    private Long pid;

    @ApiModelProperty(value = "区域城市名")
    private String areaName;

    @ApiModelProperty(value = "状态")
    private Long status;

    @ApiModelProperty(value = "运营商id")
    private Long dcId;

    @ApiModelProperty(value = "是否开通本地配送业务 1-是 0-否")
    private String localFlag;

    @ApiModelProperty(value = "平台商城市分组id")
    private Long groupId;

    /** 排序号 */
    @Excel(name = "排序号")
    @ApiModelProperty(value = "排序号")
    private Integer sortNum;

    /** 级别 */
    @Excel(name = "级别")
    @ApiModelProperty(value = "级别")
    private Integer level;

    @ApiModelProperty("三级区域城市ID, (行政区域ID, 例如: 岳麓区areaCityId)")
    private Long threeAreaCityId;
}
