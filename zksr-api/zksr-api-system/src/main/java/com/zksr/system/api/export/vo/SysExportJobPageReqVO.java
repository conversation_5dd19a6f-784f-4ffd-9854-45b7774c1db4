package com.zksr.system.api.export.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 导出任务分页查询数据
 * @date 2024/10/24 14:46
 */
@ApiModel(description = "导出任务查询VO")
@Data
public class SysExportJobPageReqVO extends PageParam {

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("谁导出的")
    private String username;

    /**
     * 参加 {@link com.zksr.file.api.constant.ExportType}
     */
    @ApiModelProperty("导出类型")
    private String exportType;

    @ApiModelProperty("请求IP")
    private String remoteIp;

    /**
     * 参见 {@link com.zksr.file.api.constant.ReportState}
     */
    @ApiModelProperty(value = "状态,0-等待中,1-执行中,2-成功,3-失败")
    private String state;
}
