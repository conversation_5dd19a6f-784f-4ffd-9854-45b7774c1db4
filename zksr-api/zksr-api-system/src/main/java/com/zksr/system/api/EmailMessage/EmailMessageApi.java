package com.zksr.system.api.EmailMessage;

import com.zksr.common.core.domain.vo.openapi.SysInterfaceLogVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportData;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @Date 2024/5/31 15:37
 * @注释
 */
@FeignClient(
        contextId = "EmailMessageApi",
        value = ApiConstants.NAME
)
public interface EmailMessageApi {
    String PREFIX = ApiConstants.PREFIX + "/emailMessage";

    /**
     * 发送同步日志日同步报告
     * @param emailReportData
     * @return
     */
    @PostMapping(PREFIX + "/sendDaySyncReportDataEmail")
    CommonResult<Boolean> sendDaySyncReportDataEmail(@Valid @RequestBody SyncEmailReportData emailReportData);
}
