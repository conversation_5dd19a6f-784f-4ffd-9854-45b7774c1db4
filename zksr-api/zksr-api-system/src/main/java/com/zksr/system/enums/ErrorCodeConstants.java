package com.zksr.system.enums;

import com.zksr.common.core.exception.ErrorCode;

/**
 * system 错误码枚举类
 * <p>
 * system 系统，使用 1_100_000_000 段
 */
public interface ErrorCodeConstants {

    // ========== partner模块 1-001-001-000 ==========
    ErrorCode USERNAME_EXISTS = new ErrorCode(1_100_001_000, "登录账号已存在");
    ErrorCode MOBILE_EXISTS = new ErrorCode(1_100_001_001, "手机号码已存在");
    ErrorCode EMAIL_EXISTS = new ErrorCode(1_100_001_002, "邮箱账号已存在");
    ErrorCode PARTNER_CODE_EXISTS = new ErrorCode(1_100_001_003, "平台商编号已经存在");
    ErrorCode JSON_UPLOAD_ERR = new ErrorCode(1_100_001_004, "首页配置JSON保存失败");
    ErrorCode USERNAME_FIAL = new ErrorCode(1_100_001_005, "获取用户账号失败");
    ErrorCode PASSWORD_FIAL = new ErrorCode(1_100_001_006, "必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");

    ErrorCode USERNAME_NOT_EXISTS = new ErrorCode(1_100_001_007, "登录账号不存在");

    ErrorCode SAAS_TENANT_CODE_IS_NULL = new ErrorCode(1_100_001_008, "租户编码不可为空");
    ErrorCode SAAS_TENANT_CODE_EXISTS = new ErrorCode(1_100_001_009, "租户编码已存在");

    // ========== area模块 1-001-007-000 ==========
    // YYMMddHHm
    // 24_01_25 15:5, 防止编号冲突
    // ========== 平台商城市分组 ==========
    ErrorCode SYS_GROUP_NOT_EXISTS = new ErrorCode(1_100_002_000, "平台商城市分组不存在");
    ErrorCode SYS_GROUP_EXISTS = new ErrorCode(1_100_002_000, "平台商城市分组重复");

    // ========== 渠道信息 ==========
    ErrorCode SYS_CHANNEL_NOT_EXISTS = new ErrorCode(1_100_003_000, "渠道信息不存在");
    ErrorCode SYS_CHANNEL_EXISTS = new ErrorCode(1_100_003_001, "渠道名称重复");

    // ========== 区域城市  ==========
    ErrorCode SYS_AREA_NOT_EXISTS = new ErrorCode(1_100_004_000, "区域城市不存在");
    ErrorCode SYS_AREA_BIND_EXISTS = new ErrorCode(1_100_004_001, "区域城市绑定异常");
    ErrorCode SYS_AREA_LEVEL_EXCEED_MAX = new ErrorCode(1_100_004_002, "区域城市级别超出最大值");
    ErrorCode SYS_AREA_LEVEL_NOT_TWO = new ErrorCode(1_100_004_003, "运营商只允许绑定二级区域");
    ErrorCode SYS_AREA_PID_ERROR = new ErrorCode(1_100_004_004, "区域城市上级区域异常");
    ErrorCode SYS_AREA_NOT_UPDATE = new ErrorCode(1_100_004_005, "区域城市绑定了运营商不允许修改级别");
    ErrorCode SYS_AREA_IS_NULL = new ErrorCode(1_100_004_006, "区域城市不能为空");
    ErrorCode SYS_AREA_NAME_COLLIDE = new ErrorCode(1_100_004_007, "区域城市名称已存在");
    ErrorCode SYS_SUB_AREA_LEVEL_NOT_TWO = new ErrorCode(1_100_004_003, "子级区域绑定了运营商不允许修改");
    ErrorCode SYS_AREA_IS_SUB_EXISTS = new ErrorCode(1_101_018_012, "当前区域城市存在子级不允许变更级别");
    ErrorCode SYS_DC_CAN_NOT_CHANGE = new ErrorCode(1_100_004_010, "销售城市运营商不允许修改");

    // ========== 入驻商信息 ==========
    ErrorCode SYS_SUPPLIER_NOT_EXISTS = new ErrorCode(1_100_005_000, "入驻商信息不存在");
    ErrorCode SYS_SUPPLIER_SUPPLIER_NAME_NOT_UNIQUE = new ErrorCode(1_100_005_001, "入驻商名称重复");
    ErrorCode SYS_SUPPLIER_SUPPLIER_ROLE_NULL = new ErrorCode(1_100_005_002, "入驻商运营商角色权限未查询到");
    ErrorCode SYS_SUPPLIER_BIND_CATGORY_ERROR= new ErrorCode(1_100_005_003, "入驻商绑定管理类别失败");
    ErrorCode SYS_SUPPLIER_UPDATE_GEOFENCE_ERROR= new ErrorCode(1_100_005_004, "修改电子围栏失败");
    ErrorCode SYS_SUPPLIER_ADD_GEOFENCE_ERROR= new ErrorCode(1_100_005_005, "保存电子围栏失败");
    ErrorCode SYS_SUPPLIER_EXCEED_GEOFENCE_MAX= new ErrorCode(1_100_005_006, "电子围栏超过最大区域");
    ErrorCode JUST_SUPPLIER_ROLE = new ErrorCode(1_100_005_007, "仅入驻商权限可操作");

    ErrorCode SYS_SUPPLIER_SUPPLIER_ID_NOT_EXISTS = new ErrorCode(1_100_005_008, "入驻商ID不能为空");


    // ========== 入驻商-区域城市关联关系 ==========
    ErrorCode SYS_SUPPLIER_AREA_NOT_EXISTS = new ErrorCode(1_100_008_000, "入驻商-区域城市关联关系不存在");

    // ========== dc模块 1-001-008-000 ==========
    ErrorCode SYS_DC_REPT_NAME = new ErrorCode(1_001_008_001, "运营商名称重复");
    ErrorCode SYS_DC_REPT_CODE = new ErrorCode(1_001_008_002, "运营商编号重复");
    ErrorCode SYS_DC_REPT_PHONE = new ErrorCode(1_001_008_003, "运营商联系手机号重复");
    ErrorCode SYS_DC_ROLE_NULL = new ErrorCode(1_001_008_004, "运营商角色权限未查询到");
    ErrorCode SYS_DC_EDIT_PHONE = new ErrorCode(1_001_008_005, "手机号(管理员账号)不可修改");
    ErrorCode SYS_DC_IS_NULL = new ErrorCode(1_001_008_006, "运营商不存在");
    ErrorCode SYS_DC_AREA_IS_NULL = new ErrorCode(1_001_008_007, "区域城市为空");
    ErrorCode SYS_DC_AREA_IS_BIND = new ErrorCode(1_001_008_008, "区域城市已绑定其他运营商");

    // ========== 运营商-区域城市关联关系  ==========
    ErrorCode SYS_DC_AREA_NOT_EXISTS = new ErrorCode(1_100_006_000, "运营商-区域城市关联关系不存在");

    // ========== 平台商配置(由软件商设置) ==========
    ErrorCode SYS_PARTNER_CONFIG_NOT_EXISTS = new ErrorCode(1_100_008_000, "平台商配置(由软件商设置)不存在");

    // ========== 平台商政策(由平台商设置) ==========
    ErrorCode SYS_PARTNER_POLICY_NOT_EXISTS = new ErrorCode(1_100_009_000, "平台商政策(由平台商设置)不存在");
    ErrorCode SYS_PARTNER_POLICY_NONE_EXITS  = new ErrorCode(1_100_009_001, "同一个城市只能存在一个搜索配置");

    // ========== 首页配置 1_100_010_001 ==========
    ErrorCode PAGES_CONFIG_NONE_EXITS = new ErrorCode(1_100_010_001, "平台首页配置不存在");
    ErrorCode PAGES_SECOND_NOT_DEFAULT = new ErrorCode(1_100_010_002, "二级页面不能设置默认首页");
    ErrorCode PAGES_DEFAULT_REPEAT = new ErrorCode(1_100_010_003, "请检查首页装修模版是否有重复的配置");
    ErrorCode PAGES_PRE_TEMPLATE_NOT_DEFAULT = new ErrorCode(1_100_010_004, "预设模版不能设置成默认首页");
    ErrorCode PAGES_PRE_TIME_EXPIRE = new ErrorCode(1_100_010_005, "检查当前模版时间是否已过期");
    ErrorCode PAGES_DEFAULT_CAN_NOT_DEL = new ErrorCode(1_100_010_006, "默认模版无法操作删除");


    // ========== 短信消息 1_100_011_001 ==========
    ErrorCode SMS_TEMPLATE_NONE_EXITS = new ErrorCode(1_100_011_001, "平台短信模版未配置");
    ErrorCode SMS_LIMIT = new ErrorCode(1_100_011_002, "请求过于频繁，请稍后再试");
    ErrorCode SMS_CODE_NONE_EXITS = new ErrorCode(1_100_011_003, "验证码已过期");
    ErrorCode SMS_CODE_ERR_OR_NONE_EXITS = new ErrorCode(1_100_011_004, "验证码输入错误");
    ErrorCode SMS_CODE_VALID_LIMIT = new ErrorCode(1_100_011_005, "验证次数超过限制请重新获取验证码");
    ErrorCode SMS_CONFIG_NOT_EXITS = new ErrorCode(1_100_011_006, "平台短信配置不存在");

    // ========== 开放能力 1_100_012_001 ==========
    ErrorCode SYS_OPENABILITY_NOT_EXISTS = new ErrorCode(1_100_012_001, "开放能力不存在");
    ErrorCode SYS_OPENABILITY_HAS_CHILD = new ErrorCode(1_100_012_002, "存在子菜单,不允许删除");
    ErrorCode SYS_OPENABILITY_NAME = new ErrorCode(1_100_012_002, "开放能力名称重复");
    ErrorCode SYS_OPENABILITY_KEY = new ErrorCode(1_100_012_002, "开放能力Key重复");

    // ========== 开放资源 1_100_013_001 ==========
    ErrorCode SYS_OPENSOURCE_NOT_EXISTS = new ErrorCode(1_100_013_001, "开放资源不存在");
//    ErrorCode SYS_OPENABILITY_HAS_CHILD = new ErrorCode(1_100_012_002, "存在子菜单,不允许删除");
//    ErrorCode SYS_OPENABILITY_NAME = new ErrorCode(1_100_012_002, "开放能力名称重复");
    ErrorCode SYS_OPENSOURCE_NOT_MULTIPLE =  new ErrorCode(1_100_013_002, "开放资源不能重复");
    ErrorCode SYS_OPENSOURCE_SETTING_EXISTS =  new ErrorCode(1_100_013_003, "请先设置公共资源");

    ErrorCode SYS_OPENSOURCE_SUPER_SUPPLIER_EXISTS =  new ErrorCode(1_100_013_004, "该统配入驻商的开发能力配置不存在");

    // ========== 接口同步日志 1_100_014_001 ==========
    ErrorCode SYS_INTERFACE_LOG_NOT_EXISTS = new ErrorCode(1_100_014_001, "接口同步日志类型不存在/不匹配");

    ErrorCode SYS_INTERFACE_LOG_SUCCES = new ErrorCode(1_100_014_002, "已成功的数据信息不允许再次重发");

    ErrorCode SYS_INTERFACE_LOG_NOT_REQUEST_TYEP = new ErrorCode(1_100_014_003, "接口日志");

    // ========== 消息模版配置 1_100_015_001 ==========
    ErrorCode SYS_MESSAGE_SCENE_REPEAT = new ErrorCode(1_100_015_001, "同场景消息模版只能配置一个启用的");
    ErrorCode SYS_MESSAGE_PUBLISH_ID_ERR = new ErrorCode(1_100_015_002, "系统参数supplier_publish_appid未配置");
    ErrorCode SYS_MESSAGE_PUBLISH_SECRET_ERR = new ErrorCode(1_100_015_002, "系统参数supplier_publish_app_secret未配置");
    ErrorCode SYS_MESSAGE_PUS_MODE_NONE = new ErrorCode(1_100_015_003, "未匹配到消息处理器");


    // ========== 平台商配置(软件商设置) 1_100_016_001 ==========
    ErrorCode SYS_PARTNER_CONFIG_COMPONENT_URL_GET_ERR = new ErrorCode(1_100_016_001, "凭证中心获取componentAccessToken失败");
    ErrorCode SYS_PARTNER_CONFIG_COMPONENT_GET_ERR = new ErrorCode(1_100_016_002, "没有获取到调用凭证componentAccessToken");
    ErrorCode SYS_PARTNER_CONFIG_COMPONENT_PRE_AUTH_CODE_ERR = new ErrorCode(1_100_016_003, "获取preAuthCode失败");
    ErrorCode SYS_PARTNER_CONFIG_COMPONENT_AUTHORIZER_ACCESS_TOKEN_URL_ERR = new ErrorCode(1_100_016_004, "未配置authorizerAccessToken配置URL");
    ErrorCode SYS_PARTNER_CONFIG_LOCAL_DISABLE = new ErrorCode(1_100_016_005, "当前平台商未开通本地商品上架功能");
    ErrorCode SYS_PARTNER_CONFIG_GLOBAL_DISABLE = new ErrorCode(1_100_016_006, "当前平台商未开通全国商品上架功能");


    // ========== 平台商配置(软件商设置) 1_100_017_001 ==========
    ErrorCode SYS_PRINTER_SETTER_TYPE_REPEAT = new ErrorCode(1_100_017_001, "打印模版设置重复");
    ErrorCode SYS_PRINTER_TEMPLATE_TYPE_REPEAT = new ErrorCode(1_100_017_002, "同一模版类型,纸张类型只能设置一个模版");


    // ========== 对外开放接口配置 1_100_018_001 ==========
    ErrorCode OPENAPI_CHECK_REQVO_EXISTS = new ErrorCode(1_100_018_001, "OPENAPI - {}:入参为空");


    // ========== 对外同步数据 1_100_019_001 ==========
    ErrorCode SYNC_ASSEMBLE_B2B_DATA_ERR = new ErrorCode(1_100_019_001, "对外推送数据--组装B2B数据失败，失败原因：{}");

    ErrorCode SYNC_ASSEMBLE_BODY_DATA_ERR = new ErrorCode(1_100_019_002, "对外推送数据--组装请求体数据失败，失败原因：{}");

    ErrorCode SYNC_ASSEMBLE_HEADER_DATA_ERR = new ErrorCode(1_100_019_003, "对外推送数据--组装请求头数据失败，失败原因：{}");

    ErrorCode SYNC_ASSEMBLE_VERIFY_DATA_ERR = new ErrorCode(1_100_019_004, "对外推送数据--组装鉴权校验数据失败，失败原因：{}");

    ErrorCode SYNC_ASSEMBLE_EXECUTE_DATA_ERR = new ErrorCode(1_100_019_005, "对外推送数据--执行发送数据失败，失败原因：{}");

    ErrorCode SYNC_ASSEMBLE_RESPONSE_DATA_ERR = new ErrorCode(1_100_019_006, "对外推送数据--组装响应数据失败，响应数据：{}，失败原因：{}");

    ErrorCode SYNC_ASSEMBLE_RESPONSE_SUCCESS_DATA_ERR = new ErrorCode(1_100_019_007, "对外推送数据--响应推送消息成功后的操作处理失败，失败原因：{}");

    ErrorCode SYS_SYNC_EMAIL_MESSAGE_DATA_ERR = new ErrorCode(1_100_019_008, "同步数据异常，发送邮箱信息时，组装模板数据异常：{}");

    ErrorCode SYS_SYNC_ORDER_CANCEL_CHECK = new ErrorCode(1_100_019_009, "同步入驻商销售订单信息失败，该订单的订单详情已全部取消，不推送");

    // ========== 软件商模块 1_100_020_001 ==========
    ErrorCode SYS_SOFTWARE_NAME_REPEAT = new ErrorCode(1_100_020_001, "软件商名称重复");
    ErrorCode SYS_SOFTWARE_PHONE_REPEAT = new ErrorCode(1_100_020_002, "软件商联系电话重复");
    ErrorCode SYS_SOFTWARE_ROLE_NOT_EXISTS = new ErrorCode(1_100_020_003, "软件商角色权限未查询到");
    ErrorCode SYS_SOFTWARE_RATIO_ERR = new ErrorCode(1_100_020_004, "软件商分润比例超出范围限制");


}
