package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 运营商订单参数配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ColonelSettingPolicyDTO {
    /**
     * 业务员拓店是否自动审核(0 否 1 是)
     */
    @ApiModelProperty("业务员拓店是否自动审核(0 否 1 是)")
    private String colonelExpandBranchType;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;
}
