package com.zksr.system.api.factory;

import com.zksr.common.core.domain.R;
import com.zksr.system.api.RemoteExportService;
import com.zksr.system.api.export.vo.SysExportJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文件服务降级处理
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class RemoteExportFallbackFactory implements FallbackFactory<RemoteExportService>
{

    @Override
    public RemoteExportService create(Throwable throwable) {
        log.error(" 导出服务调用失败,", throwable);
//        throwable.printStackTrace();
//        log.error("导出服务调用失败:{}", throwable.getMessage());
        return new RemoteExportService() {
            @Override
            public R<List<SysExportJob>> waitJobList(String source) {
                return R.fail("调用失败");
            }

            @Override
            public R<SysExportJob> getJob(Integer jobId, String source) {
                return R.fail("调用失败");
            }

            @Override
            public R<Boolean> updateJob(SysExportJob wmsReportJob, String source) {
                return R.fail("调用失败");
            }
        };
    }
}
