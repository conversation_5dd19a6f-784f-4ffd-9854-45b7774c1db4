package com.zksr.system.api;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.domain.SysUserUpdatePwdVO;
import com.zksr.system.api.model.dc.dto.DcAreaGroupDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.constant.ServiceNameConstants;
import com.zksr.common.core.domain.R;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.factory.RemoteUserFallbackFactory;
import com.zksr.system.api.model.LoginUser;

import java.util.List;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 测试导出
     * @return
     */
    @GetMapping("/user/exportTest")
    R<List<SysUser>> exportTest();

    /**
     * 获取运营商 拥有的城市列表
     * @param dcId  运营商ID
     * @return
     */
    @GetMapping("/dcArea/getDcAreaGroup")
    CommonResult<DcAreaGroupDTO> getDcAreaGroup(@RequestParam("dcId") Long dcId);

    /**
     * 根据用户ID获取用户账号
     * @param userId
     * @return
     */
    @GetMapping("/user/getBySysUserId")
    CommonResult<String> getBySysUserId(@RequestParam("userId") Long userId);

    /**
     * 获取用户
     * @param userId
     * @return
     */
    @GetMapping("/user/getSysUserApi")
    CommonResult<SysUser> getSysUser(@RequestParam("userId") Long userId);

    /**
     * 根据手机号获取用户
     * @param phone
     * @return
     */
    @GetMapping("/user/getSysUserByPhone")
    CommonResult<SysUser> getSysUserByPhone(@RequestParam("phone") String phone);


    /**
     * 更新用户密码
     * @param updatePwdVO
     * @return
     */
    @PostMapping("/user/updateUserPwd")
    CommonResult<Integer> updateUserPwd(@RequestBody SysUserUpdatePwdVO updatePwdVO);

    @GetMapping("/user/innerMove")
    CommonResult<Long> deleteByUserId(@RequestParam("userId") Long userId);
}
