package com.zksr.system.enums;

import lombok.Getter;

public enum PayType {
    onlinePay("0", "在线支付"),
    storedValuePay("1", "储值支付"),
    simulatedPay("2", "货到付款"),
    ;
    @Getter
    private String value;
    @Getter
    private String label;

    PayType(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public static String matchingLabel(String code){
        PayType[] values = values();
        for (PayType payType : values) {
            if (payType.getValue().equals(code)){
                return payType.getLabel();
            }
        }
        return null;
    }
}
