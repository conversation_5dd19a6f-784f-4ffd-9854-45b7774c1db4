package com.zksr.system.api.log;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.domain.vo.openapi.SysInterfaceLogVO;
import com.zksr.system.api.EmailMessage.dto.SyncEmailReportExcel;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/31 15:37
 * @注释
 */
@FeignClient(
        contextId = "LogApi",
        value = ApiConstants.NAME
)
public interface LogApi {
    String PREFIX = ApiConstants.PREFIX + "/log";

    /**
     * 新增同步接口的发送/接收日志
     * @param createReqVO
     * @return
     */
    @PostMapping(PREFIX + "/insertSysInterfaceLog")
    CommonResult<Boolean> insertSysInterfaceLog(@Valid @RequestBody SysInterfaceLogVO createReqVO);

    /**
     * 修改同步接口的发送/接收日志
     * @param updateReqVO
     * @return
     */
    @PostMapping(PREFIX + "/updateSysInterfaceLog")
    CommonResult<Boolean> updateSysInterfaceLog(@Valid @RequestBody SysInterfaceLogVO updateReqVO);

    /**
     * 修改同步接口的发送/接收日志
     * @param reqId
     * @return
     */
    @GetMapping(PREFIX + "/getSysInterfaceLogByReqId")
    CommonResult<SysInterfaceLogVO> getSysInterfaceLogByReqId(@RequestParam("reqId")String reqId);

    /**
     * 根据返回结果修改同步接口的发送/接收日志
     * @param updateReqVO
     * @return
     */
    @PostMapping(PREFIX + "/updateLogByResult")
    CommonResult<Boolean> updateLogByResult(@Valid @RequestBody SysInterfaceLogVO updateReqVO);

    /**
     * 每天同步第三方数据情况汇总统计(日同步报告)
     * @param sysCode
     * @param date
     * @return
     */
    @GetMapping(PREFIX + "/getLogByDaySyncReport")
    CommonResult<List<SyncEmailReportExcel>> getLogByDaySyncReport(@RequestParam("sysCode")Long sysCode, @RequestParam("date")String date);
}
