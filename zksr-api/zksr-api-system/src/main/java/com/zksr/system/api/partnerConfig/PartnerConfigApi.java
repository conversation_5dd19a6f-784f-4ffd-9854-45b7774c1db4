package com.zksr.system.api.partnerConfig;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.partnerConfig.dto.*;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        contextId = "PartnerConfigApi",
        value = ApiConstants.NAME
)
public interface PartnerConfigApi {
    String PREFIX = ApiConstants.PREFIX + "/partnerConfig";

    /**
    * @Description: 获取合利宝配置信息
    * @Author: liuxingyu
    * @Date: 2024/3/22 11:04
    */
    @GetMapping(PREFIX + "/getHeLiBaoConfig")
    CommonResult<HeLiBaoPayConfigDTO> getHeLiBaoConfig(@RequestParam("sysCode") Long sysCode);

    /**
     * 获取美的付配置
     * @param sysCode
     * @return
     */
    @GetMapping(PREFIX + "/getMideaPayConfig")
    CommonResult<MideaPayConfigDTO> getMideaPayConfig(@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 获取App配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:04
     */
    @GetMapping(PREFIX + "/getAppletBaseConfig")
    CommonResult<AppletBaseConfigDTO> getAppletBaseConfig(@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 获取支付配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:04
     */
    @GetMapping(PREFIX + "/getPayConfig")
    CommonResult<PayConfigDTO> getPayConfig(@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 获取支付账号配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:04
     */
    @GetMapping(PREFIX + "/getPayAccountConfig")
    CommonResult<PayAccountConfigDTO> getPayAccountConfig(@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 获取快递配置信息
     * @Author: liuxingyu
     * @Date: 2024/3/22 11:04
     */
    @GetMapping(PREFIX + "/getCourierConfig")
    CommonResult<CourierConfigDTO> getCourierConfig(@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 获取平台商设备配置信息
     * @Author: liuxingyu
     * @Date: 2024/4/22 8:45
     */
    @GetMapping(PREFIX + "/getDeviceSettingPolicy")
    CommonResult<DeviceSettingConfigDTO> getDeviceSettingConfig(@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 获取短信配置信息
     * @Author: liuxingyu
     * @Date: 2024/5/9 9:45
     */
    @GetMapping(PREFIX + "/getSmsConfig")
    CommonResult<SmsConfigDTO> getSmsConfig(@RequestParam("sysCode") Long sysCode);

    /**
     * 获取微信b2b支付配置
     * @param sysCode
     * @return
     */
    @GetMapping(PREFIX + "/getWxB2bPayConfig")
    CommonResult<WxB2bPayConfigDTO> getWxB2bPayConfig(@RequestParam("sysCode") Long sysCode);
}
