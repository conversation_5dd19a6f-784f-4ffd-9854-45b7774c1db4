package com.zksr.system.api.supplierArea.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("返还当前区域内绑定电子围栏的入驻商")
public class SupplierDzwlAreaDTO {

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "城市ID")
    private Long areaId;

    @ApiModelProperty(value = "电子围栏信息")
    private String dzwlInfo;
}
