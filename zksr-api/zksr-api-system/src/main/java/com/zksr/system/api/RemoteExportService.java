package com.zksr.system.api;

import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.constant.ServiceNameConstants;
import com.zksr.common.core.domain.R;
import com.zksr.system.api.export.vo.SysExportJob;
import com.zksr.system.api.factory.RemoteExportFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/1/15
 * @desc
 */
@FeignClient(contextId = "remoteExportService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteExportFallbackFactory.class)
public interface RemoteExportService {

    @GetMapping("/sexport/waitJobList")
    R<List<SysExportJob>> waitJobList(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/sexport/getById")
    R<SysExportJob> getJob(@RequestParam("jobId") Integer jobId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/sexport/updateJob")
    R<Boolean> updateJob(@RequestBody SysExportJob wmsReportJob, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
