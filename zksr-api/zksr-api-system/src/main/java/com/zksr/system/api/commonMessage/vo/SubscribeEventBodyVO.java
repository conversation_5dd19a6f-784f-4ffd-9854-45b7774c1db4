package com.zksr.system.api.commonMessage.vo;

import com.zksr.common.core.enums.CommonMessageSceneEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订阅消息模版配置详情
 * @date 2024/6/13 9:22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "公众号小程序订阅消息通知事件")
public class SubscribeEventBodyVO<EventData> {

    @ApiModelProperty("事件参数")
    private EventData eventData;

    @ApiModelProperty("平台ID")
    private Long sysCode;

    /**
     * 参加 {@link CommonMessageSceneEnum}
     */
    @ApiModelProperty("消息场景")
    private Integer scene;
}
