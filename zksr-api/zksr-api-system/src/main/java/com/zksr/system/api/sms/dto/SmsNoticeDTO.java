package com.zksr.system.api.sms.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:   验证验证码是否正确
 * @date 2024/4/30 9:57
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(description = "发送通知短信DTO")
public class SmsNoticeDTO {
    @ApiModelProperty(value = "手机号", required = true)
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "通知内容", required = true)
    @NotEmpty(message = "通知内容")
    private String context;

    private Long systemCode;
}
