package com.zksr.system.api.partnerConfig.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 快递配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CourierConfigDTO {
    /**
     * 快递查询平台
     */
    @ApiModelProperty("快递查询平台")
    private String courierPlatform;

    /**
     * 商户ID
     */
    @ApiModelProperty("商户ID")
    private String merchantId;

    /**
     * appKey
     */
    @ApiModelProperty("appKey")
    private String appKey;


    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

}
