package com.zksr.system.enums;

import lombok.Getter;

/**
 * @Description: 单位枚举类
 * @Author: liuxingyu
 * @Date: 2024/4/22 10:50
 */
public enum LogOperationType {
    logAdd("新增", "1"),
    logEdit("修改", "2"),
    ;

    @Getter
    private String label;
    @Getter
    private String value;

    LogOperationType(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public static String matchingLabel(String code) {
        LogOperationType[] unitTypes = values();
        for (LogOperationType unitType : unitTypes) {
            if (unitType.getValue().equals(code)) {
                return unitType.getLabel();
            }
        }
        return null;
    }
}
