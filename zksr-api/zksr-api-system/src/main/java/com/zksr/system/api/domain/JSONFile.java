package com.zksr.system.api.domain;

import cn.hutool.core.codec.Base64;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: json对象
 * @date 2024/4/11 16:27
 */
@Data
@ApiModel
public class JSONFile {

    @ApiModelProperty("json对象")
    private String json;

    @ApiModelProperty("文件名称")
    private String name;

    public JSONFile(String json, String name) {
        this.json = json;
        this.name = name;
    }

    public JSONFile() {

    }
}
