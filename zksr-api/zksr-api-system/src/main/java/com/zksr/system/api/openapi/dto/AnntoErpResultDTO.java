package com.zksr.system.api.openapi.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * 响应信息主体
 *
 * <AUTHOR>
 */
public class AnntoErpResultDTO<T> implements Serializable {

    /**
     * 成功
     */
    public static final int SUCCESS = 200;

    /**
     * 失败
     */
    public static final int FAIL = 500;

    private static final long serialVersionUID = 1L;

    private int responseCode;

    private String responseMessage;

    private T responseContent;

    public static <T> AnntoErpResultDTO<T> ok() {
        return restResult(null, SUCCESS, null);
    }

    public static <T> AnntoErpResultDTO<T> ok(String message) {
        return restResult(null, SUCCESS, message);
    }

    public static <T> AnntoErpResultDTO<T> ok(T data) {
        return restResult(data, SUCCESS, null);
    }

    public static <T> AnntoErpResultDTO<T> ok(T data, String msg) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> AnntoErpResultDTO<T> fail() {
        return restResult(null, FAIL, null);
    }

    public static <T> AnntoErpResultDTO<T> fail(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> AnntoErpResultDTO<T> fail(T data) {
        return restResult(data, FAIL, null);
    }

    public static <T> AnntoErpResultDTO<T> fail(T data, String msg) {
        return restResult(data, FAIL, msg);
    }

    public static <T> AnntoErpResultDTO<T> fail(int code, String msg) {
        return restResult(null, code, msg);
    }

    private static <T> AnntoErpResultDTO<T> restResult(T data, int code, String msg) {
        AnntoErpResultDTO<T> apiResult = new AnntoErpResultDTO<>();
        apiResult.setResponseCode(code);
        apiResult.setResponseContent(data);
        apiResult.setResponseMessage(msg);
        return apiResult;
    }

    public int getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(int responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    public T getResponseContent() {
        return responseContent;
    }

    public void setResponseContent(T responseContent) {
        this.responseContent = responseContent;
    }

    @JsonIgnore
    public boolean isOk() {
        return this.responseCode == SUCCESS;
    }

    @JsonIgnore
    public boolean isFail() {
        return this.responseCode == FAIL;
    }

    @Override
    public String toString() {
        return "Result{" +
                "responseCode=" + responseCode +
                ", responseMessage='" + responseMessage + '\'' +
                ", responseContent=" + responseContent +
                '}';
    }
}
