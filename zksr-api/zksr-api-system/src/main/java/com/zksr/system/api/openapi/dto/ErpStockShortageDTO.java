package com.zksr.system.api.openapi.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: ERP库存不足响应DTO
 * @Date: 2025/07/15
 */
@Data
@Accessors(chain = true)
@ApiModel("ERP库存不足响应DTO")
public class ErpStockShortageDTO {

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("组织编码")
    private String orgCode;

    @ApiModelProperty("库存不足标识：1-库存不足，0-库存充足")
    private Integer stockShortFlag;

    @ApiModelProperty("单据来源")
    private String sheetSource;

    @ApiModelProperty("库存不足明细列表")
    private List<ErpStockShortageDetailDTO> detailVos;

    /**
     * ERP库存不足明细DTO
     */
    @Data
    @Accessors(chain = true)
    @ApiModel("ERP库存不足明细DTO")
    public static class ErpStockShortageDetailDTO {

        @ApiModelProperty("行号")
        private Integer lineNo;

        @ApiModelProperty("商品编码")
        private String itemNo;

        @ApiModelProperty("需求数量")
        private BigDecimal minDetailQty;

        @ApiModelProperty("可用库存数量")
        private BigDecimal enableQty;

        @ApiModelProperty("库存不足标识：1-库存不足，0-库存充足")
        private Integer stockShortFlag;

        @ApiModelProperty("skuId")
        private Long skuId;

        @ApiModelProperty("换算关系")
        private BigDecimal orderUnitSize;
    }
}
