package com.zksr.system.api.area;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.area.vo.SysAreaCitySaveReqVO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/7/30
 * @desc 省市区api
 */

@FeignClient(
        contextId = "remoteAreaCityApi",
        value = ApiConstants.NAME
)
public interface AreaCityApi {

    String PREFIX = ApiConstants.PREFIX + "/areaCity";

    @PostMapping(PREFIX + "/getPage")
    CommonResult<PageResult<SysAreaCityRespVO>> getPage(@RequestBody SysAreaCityPageReqVO pageReqVO);

    @GetMapping(PREFIX + "/getById")
    CommonResult<SysAreaCityRespVO> getById(@RequestParam("areaCityId") Long areaCityId);

    @GetMapping(PREFIX + "/getByNameAndParent")
    CommonResult<SysAreaCityRespVO> getByNameAndParent(
            @RequestParam("name") String name,
            @RequestParam(value = "pid", required = false) Long pid,
            @RequestParam("deep") Integer deep
    );

    @PostMapping(PREFIX + "/updateBatch")
    CommonResult<Boolean> updateBatch(@RequestBody List<SysAreaCitySaveReqVO> updateReqVOList);

    @PostMapping(PREFIX + "/insert")
    CommonResult<Long> insert(@RequestBody SysAreaCitySaveReqVO insertVO);
}
