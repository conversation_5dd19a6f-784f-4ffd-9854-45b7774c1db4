package com.zksr.system.api.supplier.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
* @Description: 校验电子围栏消息队列实体类
* @Author: liuxingyu
* @Date: 2024/3/28 16:15
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckGeofenceDTO {
    /**
     * 平台编号
     */
    private Long sysCode;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 门店编号
     */
    private Long branchId;
}
