package com.zksr.system.api.invoice.dto;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONType;
import lombok.Data;

/**
 * @Description: 发票查询请求DTO
 * @Date: 2025/07/16
 */
@Data
@JSONType(alphabetic = false,           // 坑：一定要设置，不然还是按ascii码顺序排列
        serializeFeatures = {
                JSONWriter.Feature.WriteNulls,  // 保留 null 字段
                JSONWriter.Feature.FieldBased   // 按字段声明顺序
        })
public class InvoiceQueryRequest {

    /**
     * 开票请求流水号（必填）
     */
    private String bizId;

    /**
     * 发票类型（必填）
     * 1: 蓝票
     * 2: 红票
     */
    private Integer transType;
}
