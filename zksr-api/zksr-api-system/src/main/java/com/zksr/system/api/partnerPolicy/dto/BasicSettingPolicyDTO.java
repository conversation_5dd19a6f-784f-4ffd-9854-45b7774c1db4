package com.zksr.system.api.partnerPolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 运营商基础设置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class BasicSettingPolicyDTO {

    /**
     * 运营商展示分类一级分类展示在顶部或左侧, 1顶部2左侧
     */
    @ApiModelProperty("运营商展示分类一级分类展示在顶部或左侧, 1顶部2左侧")
    private String classShow;

    /**
     * 门店自动审核(0 禁用 1 启用)
     */
    @ApiModelProperty("门店自动审核(0 禁用 1 启用)")
    private String shopAutoCheck;

    /**
     * 门店天数
     */
    @ApiModelProperty("门店天数")
    private String shopDay;

    /**
     * 用户自动审核(0 禁用 1 启用)
     */
    @ApiModelProperty("用户自动审核(0 禁用 1 启用)")
    private String userAutoCheck;

    /**
     * 用户天数
     */
    @ApiModelProperty("用户天数")
    private String userDay;

    /**
     * 门店货到付款默认最大可欠款金额
     */
    @ApiModelProperty("门店货到付款默认最大可欠款金额")
    private String branchDefaultHdfkMaxAmt;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    @ApiModelProperty("是否开启公海客户(0 禁用 1 启用)")
    private String isSeas;

    @ApiModelProperty("进入公海的判断类型(0 未下单时长 1 为下单且未拜访时长)")
    private String seasCondition;

    @ApiModelProperty("进入公海的时长()")
    private String seasDay;

    @ApiModelProperty("业务员认领后的保护期()")
    private String seasProtection;

}
