package com.zksr.system.api.channel;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(
        contextId = "remoteChannelApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)

/**
*
 * 渠道服务
* <AUTHOR>
* @date 2024/3/22 18:02
*/
public interface ChannelApi {
    String PREFIX = ApiConstants.PREFIX + "/channel";

    @GetMapping(PREFIX + "/getByChannelId")
    public CommonResult<ChannelDTO> getByChannelId(@RequestParam("channelId") Long channelId);

    @GetMapping(PREFIX + "/getChannelListByDcIdAndSysCode")
    public CommonResult<Map<Long, ChannelDTO>> getChannelListByDcIdAndSysCode(@RequestParam(value = "sysCode",required = false) Long sysCode, @RequestParam(value =  "dcId",required = false) Long dcId);


    @GetMapping(PREFIX + "/getChannelList")
    public CommonResult<List<ChannelDTO>> getChannelList();

    @GetMapping(PREFIX + "/getChannelListBySysCode")
    public CommonResult<List<ChannelDTO>> getChannelListBySysCode(@RequestParam(value = "sysCode") Long sysCode,
                                                                  @RequestParam(value = "channelName",  required = false) String channelName);
}
