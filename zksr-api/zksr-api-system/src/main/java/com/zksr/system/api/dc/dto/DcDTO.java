package com.zksr.system.api.dc.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 运营商对象 sys_dc
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DcDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 运营商编号
     */
    private Long dcId;

    /**
     * 平台编号
     */
    private Long sysCode;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 删除状态  (0正常  2已删除)
     */
    private String delFlag;

    /**
     * 区域备注
     */
    private String memo;

    /**
     * 运营商地址
     */
    private String address;

    /**
     * 运营商编号
     */
    private String dcCode;

    /**
     * 运营商名称
     */
    private String dcName;

    /**
     * 联系人
     */
    private String contractName;

    /**
     * 联系电话
     */
    private String contractPhone;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 本地起送价
     */
    private BigDecimal minAmt;

    /**
     * 全国起送价
     */
    private BigDecimal globalMinAmt;
}
