package com.zksr.system.api.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.web.domain.BaseEntity;
import com.zksr.common.core.web.pojo.PageParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 导入记录
 */
@Data
public class SysFileImportForm extends PageParam {

    /**
     * 导入记录id
     */
    private Long fileImportId;

    /**
     * 平台编号
     */
    private Long sysCode;

    /**
     * 导入文件名
     */
    private String fileName;

    /**
     * 导入文件下载地址
     */
    private String fileUrl;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 导入总数
     */
    private Integer totalNum;

    /**
     * mq发送数量
     */
    private Integer mqSendNum;

    /**
     * mq接收数量
     */
    private Integer mqReceiveNum;

    /**
     * 成功条数
     */
    private Integer successNum;

    /**
     * 失败条数
     */
    private Integer failNum;

    /**
     * 是否更新已存在的数据;是否更新已存在的数据
     */
    private Integer updateSupport;

    /**
     * 导入类型
     */
    private String importType;

    /**
     * 导入状态 0成功 1失败 2进行中
     */
    private Integer importStatus;

    private Date startTime;

    private Date endTime;
}
