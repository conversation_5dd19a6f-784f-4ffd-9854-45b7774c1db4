package com.zksr.system.api;

import com.zksr.system.api.opensource.dto.OpenlimitDto;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@NoArgsConstructor
public class LoginOpensource implements Serializable {

    private static final long serialVersionUID = 1L;

    private String token;//登录的token码

    private OpensourceDto opensourceDto;

    /**
     * 用户id
     */
    private Long opensourceId;

    /**
     * 接口限制
     */
    private Set<String> abilityKeys;

    /**
     * 限流设置
     */
    private Set<OpenlimitDto> openlimitList;

    /**
     * 平台商id
     */
    private Long sysCode;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 商城显示物流信息 0第三方  1本地
     */
    private String logisticsInfo;
}
