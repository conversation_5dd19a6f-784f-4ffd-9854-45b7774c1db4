package com.zksr.system.api.software.dto;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.baomidou.mybatisplus.annotation.TableId;
import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 软件商信息对象 sys_software
 *
 * <AUTHOR>
 * @date 2024-11-19
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SoftwareDTO extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 软件商id */
    @TableId(type = IdType.ASSIGN_ID)
    private Long softwareId;

    /** 软件商名 */
    @Excel(name = "软件商名")
    private String softwareName;

    /** 联系人 */
    @Excel(name = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    private String contactAddress;

    /** 关联软件商管理员的账号id */
    @Excel(name = "关联软件商管理员的账号id")
    private Long softwareUserId;

    /** 软件商分润比例 */
    @Excel(name = "软件商分润比例")
    private BigDecimal softwareRate;

    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    @TableField(exist = false)
    private String userName;

}