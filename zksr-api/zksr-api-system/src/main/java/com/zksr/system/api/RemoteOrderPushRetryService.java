package com.zksr.system.api;

import com.zksr.common.core.constant.ServiceNameConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.domain.OrderPushRetryParam;
import com.zksr.system.api.domain.OrderPushRetryResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description: 订单推送重试远程服务接口
 * @Date: 2025/07/22
 */
@FeignClient(contextId = "remoteOrderPushRetryService", value = ServiceNameConstants.SYSTEM_SERVICE)
public interface RemoteOrderPushRetryService {

    /**
     * 执行订单推送失败重推任务
     * 
     * @param param 重推参数
     * @return 执行结果
     */
    @PostMapping("/orderPushRetry/retry")
    CommonResult<OrderPushRetryResult> retryFailedOrderPush(@RequestBody OrderPushRetryParam param);

}
