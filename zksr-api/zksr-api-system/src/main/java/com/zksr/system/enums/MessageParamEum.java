package com.zksr.system.enums;

import lombok.Getter;


@Getter
public enum MessageParamEum {
    ORDER_NO("orderNo", "订单号"),
    MEMBER_NAME("memberName", "下单用户"),
    MEMBER_PHONE("memberPhone", "下单用户手机号"),
    BRANCH_NAME("branchName", "门店名称"),
    BRANCH_ADDR("branchAddr", "门店地址"),
    SPU_NAME("spuName", "商品名称"),
    PAY_AMT("payAmt", "订单金额"),
    ORDER_CREATE_TIME("orderCreateTime", "下单时间"),
    SUPPLIER_NAME("supplierName", "入驻商名称"),
    SUPPLIER_CONTACT_PHONE("supplierContactPhone", "入驻商联系方式"),
    ORDER_MEMO("orderMemo", "订单备注"),
    ORDER_CANCEL_TIME("orderCancelTime", "订单取消时间"),
    ORDER_DELIVERY_TIME("orderDeliveryTime", "订单发货时间"),
    ORDER_EXPRESS_NAME("orderExpressName", "订单快递公司"),
    ORDER_EXPRESS_NO("orderExpressNo", "订单快递单号"),
    ORDER_RECEIVE_TIME("orderReceiveTime", "订单收货时间"),
    AFTER_MEMO("afterMemo", "退货备注"),
    AFTER_AUDIT_MEMO("afterAuditMemo", "退货审核备注"),
    AFTER_CREATE_TIME("afterCreateTime", "退货申请时间"),
    AFTER_FINISH_TIME("afterFinishTime", "退货完成时间(退款时间)"),
    AFTER_NO("afterNo", "退货申请单号"),
    AFTER_AUDIT_STATUS("afterAuditStatus", "退货审核状态"),
    AFTER_REFUND_AMT("afterRefundAmt", "退款金额"),
    BRANCH_CREATE_TIME("branchCreateTime", "门店注册时间"),
    BRANCH_CONTRACT_PHONE("branchContractPhone", "门店手机号"),
    ACTIVITY_NAME("activityName", "促销名称"),
    ACTIVITY_START_TIME("activityStartTime", "促销开始时间"),
    ACTIVITY_END_TIME("activityEndTime", "促销结束时间"),
    ;
    private String filed;
    private String name;

    MessageParamEum(String filed, String name) {
        this.filed = filed;
        this.name = name;
    }
}
