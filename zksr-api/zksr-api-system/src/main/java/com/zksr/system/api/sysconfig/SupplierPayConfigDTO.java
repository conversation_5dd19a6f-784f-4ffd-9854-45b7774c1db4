package com.zksr.system.api.sysconfig;

import com.zksr.system.api.model.dto.HlbPayConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 入驻商全局参数配置
 * @date 2024/4/20 16:43
 */
@Data
public class SupplierPayConfigDTO extends HlbPayConfig {

    /**
     * 合利宝平台商户号
     */
    @ApiModelProperty("合利宝平台商户号")
    private String platformMerchantNo;

    /**
     * 公共平台md5签名秘钥
     */
    @ApiModelProperty("公共产品MD5签名秘钥")
    private String publicMerchantMd5SignKey;

    /**
     * 公共产品Des3加密key
     */
    @ApiModelProperty("公共产品Des3加密key")
    private String publicEncryptKey;
}
