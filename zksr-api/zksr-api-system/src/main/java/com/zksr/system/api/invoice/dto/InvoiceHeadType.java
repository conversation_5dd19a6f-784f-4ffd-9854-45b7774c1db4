package com.zksr.system.api.invoice.dto;


/**
 * 发票抬头类型枚举
 */
public enum InvoiceHeadType {
    PERSONAL(1, "个人"),
    ENTERPRISE(2, "企业");

    private final Integer code;
    private final String desc;

    InvoiceHeadType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
