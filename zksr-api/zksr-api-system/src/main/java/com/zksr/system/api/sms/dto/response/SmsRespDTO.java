package com.zksr.system.api.sms.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 安得消息中心响应实体
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(description = "安得消息中心请求实体")
public class SmsRespDTO {

    @ApiModelProperty(value = "响应code，0表示成功")
    @NotEmpty(message = "响应code")
    private String code;

    @ApiModelProperty(value = "响应内容")
    @NotEmpty(message = "响应内容")
    private String msg;

}
