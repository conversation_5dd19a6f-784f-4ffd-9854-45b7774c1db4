package com.zksr.system.api.supplier.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.domain.vo.accInvoiceSetting.AccInvoiceSetttingSimpleBindVO;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 入驻商信息对象 sys_supplier
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("入驻商信息 - sys_supplier Response VO")
public class SysSupplierRespVO {
    private static final long serialVersionUID = 1L;

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 入驻商编号 */
    @ApiModelProperty(value = "入驻商编号")
    private String supplierCode;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称", example = "示例值")
    private String supplierName;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人", example = "示例值")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话", example = "示例值")
    private String contactPhone;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址", example = "示例值")
    private String contactAddress;

    /** 状态-数据字典 */
    @Excel(name = "状态-数据字典")
    @ApiModelProperty(value = "状态-数据字典", example = "示例值")
    private Long status;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "示例值")
    private String memo;

    /** 是否是电子围栏入驻商 */
    @Excel(name = "是否是电子围栏入驻商")
    @ApiModelProperty(value = "是否是电子围栏入驻商", example = "示例值")
    private Long dzwlFlag;

    /** 电子围栏 */
    @Excel(name = "电子围栏")
    @ApiModelProperty(value = "电子围栏", example = "示例值")
    private String dzwlInfo;

    /** 运营商id;运营商id */
    @Excel(name = "运营商id;运营商id")
    @ApiModelProperty(value = "运营商id;运营商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 营业执照图片地址 */
    @Excel(name = "营业执照图片地址")
    @ApiModelProperty(value = "营业执照图片地址")
    private String licenceUrl;

    /** 城市id集合 */
    @Excel(name = "城市id集合")
    @ApiModelProperty(value = "城市id集合")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> areaIds;

    /**
     * 管理类别id集合
     */
    @Excel(name = "管理类别id集合")
    @ApiModelProperty(value = "管理类别id集合", required = true)
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> catgoryIds;

    /** 运营商名称 */
    @Excel(name = "运营商名称")
    @ApiModelProperty(value = "运营商名称")
    private String dcName;


    @ApiModelProperty("用户账号")
    private String userName;

    /** 银行名称 */
    @ApiModelProperty(value = "银行名称", required = true, example = "浦发银行")
    private String bankName;

    /** 银行支行 */
    @ApiModelProperty(value = "银行支行", required = true, example = "浦发银行麓谷支行")
    private String bankBranch;

    /** 银行卡号 */
    @ApiModelProperty(value = "银行卡号", required = true, example = "***************")
    private String accountNo;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "本地起送价")
    private BigDecimal minAmt;

    @ApiModelProperty("全国起送价")
    private BigDecimal globalMinAmt;

    @ApiModelProperty(value = "支付账户最小保留金")
    private BigDecimal minSettleAmt;

    @ApiModelProperty(value = "入驻商头像地址")
    private String avatar;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;

    /** 一级管理分润, 销售占比 */
    @Excel(name = "一级管理分润, 销售占比")
    @ApiModelProperty(value = "一级管理分润, 销售占比")
    private List<PrdtSupplierClassRateDTO> supplierClassRateDTOS;

    @ApiModelProperty("入驻商其他配置")
    private SupplierOtherSettingPolicyDTO supplierOtherSettingPolicyDTO;

    //软件商支付账号配置
    /** 商户绑定信息 */
    @ApiModelProperty(value = "商户绑定信息")
    private List<PlatformSimpleBindVO> platformSimpleBindList;
    /** 发票绑定信息 */
    @ApiModelProperty(value = "发票绑定信息")
    private List<AccInvoiceSetttingSimpleBindVO> invoceSettingBindList;

    @ApiModelProperty(value = "门店储值软件商分佣比例")
    private BigDecimal walletSoftwareRate = BigDecimal.ZERO;

    @ApiModelProperty(value = "门店储值平台商分佣比例")
    private BigDecimal walletPartnerRate;

    @Data
    public static class PrdtSupplierClassRateDTO {

        @Excel(name = "平台商管理分类id;平台商管理分类id")
        @ApiModelProperty("平台商管理分类id;平台商管理分类id")
        private Long catgoryId;

        /**
         * 入驻商id;入驻商id
         */
        @Excel(name = "入驻商id;入驻商id")
        @ApiModelProperty("入驻商id;入驻商id")
        private Long supplierId;

        /**
         * 销售分润占比, 最大0.29 = 29%
         */
        @Excel(name = "销售分润占比, 最大0.29 = 29%")
        @ApiModelProperty("销售分润占比, 最大0.29 = 29%")
        private BigDecimal saleTotalRate;
    }
}
