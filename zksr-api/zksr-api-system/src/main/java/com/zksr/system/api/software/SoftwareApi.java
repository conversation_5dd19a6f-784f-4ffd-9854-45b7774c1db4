package com.zksr.system.api.software;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.software.dto.SoftwareDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 软件商API
 * @date 2024/11/26 15:31
 */
@FeignClient(
        contextId = "remoteSoftwareApi",
        value = ApiConstants.NAME
)
public interface SoftwareApi {
    String PREFIX = ApiConstants.PREFIX + "/software";

    @GetMapping(PREFIX + "/getById")
    CommonResult<SoftwareDTO> getById(@RequestParam("softwareId") Long softwareId);

}
