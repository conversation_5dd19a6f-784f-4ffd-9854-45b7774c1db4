package com.zksr.system.api.partner.dto;

import com.zksr.common.core.annotation.Excel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class PartnerDto {

    /** 平台商id */
    private Long sysCode;

    /** 平台名称 */
    private String partnerName;

    /** 负责人姓名 */
    private String contactName;

    /** 联系电话 */
    private String contactPhone;

    /** 公司地址 */
    private String contactAddress;

    /** 状态 数据字典sys_partner_status */
    private Integer status;

    /** 备注 */
    private String memo;

    /** 来源 */
    private String source;

        /** 猎鹰服务ID */
    private Long sid;

    /** 软件商ID */
    @Excel(name = "软件商ID")
    private Long softwareId;

    @Excel(name = "软件商分润比例")
    private BigDecimal softwareRate;

    /**
     * saas 租户编码
     */
    private String saasTenantCode;
}
