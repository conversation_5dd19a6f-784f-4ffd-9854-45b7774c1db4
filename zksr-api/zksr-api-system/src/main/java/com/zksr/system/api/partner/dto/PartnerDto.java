package com.zksr.system.api.partner.dto;

import com.zksr.common.core.annotation.Excel;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class PartnerDto {

    /** 平台商id */
    private Long sysCode;

    /** 平台名称 */
    private String partnerName;

    /** 负责人姓名 */
    private String contactName;

    /** 联系电话 */
    private String contactPhone;

    /** 公司地址 */
    private String contactAddress;

    /** 状态 数据字典sys_partner_status */
    private Integer status;

    /** 备注 */
    private String memo;

    /** 来源 */
    private String source;

        /** 猎鹰服务ID */
    private Long sid;

    /** 软件商ID */
    @Excel(name = "软件商ID")
    private Long softwareId;

    @Excel(name = "软件商分润比例")
    private BigDecimal softwareRate;

    /**
     * saas 租户编码
     */
    private String saasTenantCode;
    
    /** 开启零售（0 否 1 是) */
    @ApiModelProperty(value = "开启零售（0 否 1 是)")
    private Integer enableRetail;
    
    /** 开启O2O（0 否 1 是) */
    @ApiModelProperty(value = "开启O2O（0 否 1 是)")
    private Integer enableO2o;
}
