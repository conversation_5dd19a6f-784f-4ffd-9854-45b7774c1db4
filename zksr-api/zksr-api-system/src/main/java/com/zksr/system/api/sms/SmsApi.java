package com.zksr.system.api.sms;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.sms.dto.*;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 短信消息API
 * @date 2024/4/30 9:55
 */

@FeignClient(
        contextId = "SmsApi",
        value = ApiConstants.NAME
)
public interface SmsApi {

    String PREFIX = ApiConstants.PREFIX + "/sms";

    /**
     * 发送标准验证码 (5分钟内有效)
     */
    @PostMapping(PREFIX + "/sendSmsCode")
    public CommonResult<SmsCodeRespDTO> sendSmsCode(@Valid @RequestBody SmsCodeReqDTO codeReqDTO);

    /**
     * 验证短信验证码 (最多验证3次)
     * 验证错误会直接抛异常
     */
    @PostMapping(PREFIX + "/validateSmsCode")
    public CommonResult<SmsCodeValidRespDTO> validateSmsCode(@Valid @RequestBody SmsCodeValidDTO codeReqDTO);

    /**
     * 验证短信验证码 (最多验证3次)
     * 验证错误会直接抛异常
     */
    @PostMapping(PREFIX + "/sendNoticeSms")
    public CommonResult<String> sendNoticeSms(@RequestBody SmsNoticeDTO smsNoticeDTO);
}
