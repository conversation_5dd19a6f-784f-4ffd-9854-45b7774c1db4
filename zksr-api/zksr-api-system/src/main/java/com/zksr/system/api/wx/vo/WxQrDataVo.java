package com.zksr.system.api.wx.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WxQrDataVo{

    private Long qrId;

    @ApiModelProperty("二维码key")
    private String qrKey;

    @ApiModelProperty("二维码value")
    private String qrValue;

    private Long sysCode;

}
