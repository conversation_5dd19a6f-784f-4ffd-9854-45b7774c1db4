package com.zksr.system.api.invoice.dto;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票信息
 */
@Data
public class Invoice {
    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 开票日期
     */
    private Date invoiceDate;

    /**
     * 发票校验码
     */
    private String checkCode;

    /**
     * 发票pdf的下载url（文件生成需要时间）
     */
    private String pdfUrl;

    /**
     * 发票pdf的下载url（短链）
     */
    private String pdfURLInvoice;

    /**
     * 发票ofd的下载url
     */
    private String ofdUrl;

    /**
     * 发票xml的下载url
     */
    private String xmlUrl;

    /**
     * 发票含税金额
     */
    private BigDecimal amtContainTax;

    /**
     * 发票不含税金额
     */
    private BigDecimal amtNotContainTax;

    /**
     * 发票抬头：1=个人，2=企业
     */
    private Integer invoiceHead;

    /**
     * 蓝票发票号码（红票开票成功时返回）
     */
    private String blueInvoiceNo;
}
