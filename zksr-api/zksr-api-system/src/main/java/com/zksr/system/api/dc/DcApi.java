package com.zksr.system.api.dc;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.system.api.dc.dto.ColonelUserDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.dc.vo.SysDcPageReqVO;
import com.zksr.system.api.dc.vo.SysDcRespVO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(contextId = "dcApi", value = ApiConstants.NAME)
public interface DcApi {
    String PREFIX = ApiConstants.PREFIX + "/dc";

    /**
     * @Description: 根据Id获取运营商信息
     * @Param: Long dcId
     * @return: CommonResult<DcDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 10:24
     */
    @GetMapping(PREFIX + "/getDcById")
    public CommonResult<DcDTO> getDcById(@RequestParam("dcId") Long dcId);

    /**
     * 根据平台商id查询运营商信息
     * @param sysCode 平台商ID
     * @return
     */
    @GetMapping(PREFIX + "/getDcBySysCode")
    public CommonResult<List<DcDTO>> getDcBySysCode(@RequestParam("sysCode") Long sysCode);

    @PostMapping(PREFIX + "/insertColonelUser")
    public CommonResult<Long> insertColonelUser(@RequestBody ColonelUserDTO colonelDTO );

    @PostMapping(PREFIX + "/updateColonelUser")
    public CommonResult<Boolean> updateColonelUser(@RequestBody ColonelUserDTO colonelDTO );

    @GetMapping(PREFIX + "/updateColonelUserStatus")
    public CommonResult<Boolean> updateColonelUserStatus(@RequestParam("userId")Long userId,@RequestParam("status") String status);

    /**
     * 获取运营商下管理的入驻商ID
     * @param dcId
     * @return
     */
    @GetMapping(PREFIX + "/getDcSupplierList")
    public CommonResult<List<Long>> getDcSupplierList(@RequestParam("dcId") Long dcId);

    /**
     * 获取运营商下管理的区域ID
     * @param dcId
     * @return
     */
    @GetMapping(PREFIX + "/getDcAreaList")
    public CommonResult<List<Long>> getDcAreaList(@RequestParam("dcId") Long dcId);

    /**
     *  校验业务员账号和手机号是否已经注册过
     * @param userName
     * @param colonelPhone
     * @return true :存在   false: 不存在
     */
    @GetMapping(PREFIX + "/checkColonelUser")
    public CommonResult<Boolean> checkColonelUser(@RequestParam("userName") String userName, @RequestParam("colonelPhone") String colonelPhone);

    /**
     * 获取运营商分页数据
     * @param pageReqVO
     * @return
     */
    @PostMapping(PREFIX + "/getPage")
    CommonResult<PageResult<SysDcRespVO>> getPage(@RequestBody SysDcPageReqVO pageReqVO);
}
