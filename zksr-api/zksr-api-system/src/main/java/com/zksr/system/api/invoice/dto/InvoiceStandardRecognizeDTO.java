package com.zksr.system.api.invoice.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class InvoiceStandardRecognizeDTO {
    /**
     * 发票类型
     * 0100-增值税专用发票 0101-增值税普通发票 0102-增值税电子普通发票
     * 0103-增值税电子专用发票 0107-增值税专票（全电专票） 0108-普通发票（全电普票）
     * 0200-机动车销售统一发票 0201-二手车销售统一发票 0300-增值税普通发票（卷票)
     * 0301-火车票 0302-飞机票（电子客票行程单）0303-出租车 0304-客运汽车
     * 0305-定额发票 0306-机打发票 0307-过路费发票 0308-客运船票 0310-滴滴行程单
     * 0311-火车票退票费 0316数电票（铁路电子客票） 0317数电票（航空运输电子客票行程单）
     * 0318数电票（铁路电子客票）退票费 0319数电票（航空运输电子客票行程单）退改费
     * 0320机票退改费 0700-区块链电子发票 0800-机打电子发票 9998-非发票 9999-其他发票
     */
    private String invoiceType;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码或飞机票的票号、火车票的票号
     */
    private String invoiceNo;

    /**
     * 开票日期（yyyy-MM-dd）
     */
    private String invoiceDate;

    /**
     * 金额（不含税金额，火车票金额，飞机票价）
     */
    private String invoiceAmt;

    /**
     * 合计税额（总税额，飞机票/出租车燃油费）
     */
    private String taxAmount;

    /**
     * 价税合计（飞机合计金额，火车等总金额）
     */
    private String totalAmount;

    /**
     * 其他税费
     */
    private String otherTax;

    /**
     * 税率（机动车、二手车）
     */
    private String taxRate;

    /**
     * 民航发展基金
     */
    private String civilAviationFund;

    /**
     * 校验码
     */
    private String checkCode;

    /**
     * 销方税号（即收款方税号）
     */
    private String recTaxpayerCode;

    /**
     * 销售方名称（即开票人）
     */
    private String recTaxpayerName;

    /**
     * 销售方名称
     */
    private String saleName;

    /**
     * 购方税号（即受票方税号）
     */
    private String payTaxpayerCode;

    /**
     * 购买方名称（即受票方名称）
     */
    private String purchaserName;

    /**
     * 买方单位代码,个人身份证号
     */
    private String buyerIdentification;

    /**
     * 销方税号
     */
    private String saleTaxNo;

    /**
     * 二手车市场税号
     */
    private String marketTaxNo;

    /**
     * 卖方单位代码,个人身份证号
     */
    private String sellerId;

    /**
     * 开票人，乘车人，乘机人
     */
    private String drawer;

    /**
     * 出发城市，发车城市，出发站
     */
    private String leaveCity;

    /**
     * 到达城市，到达站
     */
    private String arriveCity;

    /**
     * 车次号或航班号
     */
    private String trainNumber;

    /**
     * 身份证号
     */
    private String idNum;

    /**
     * 座位、座舱等级
     */
    private String trainSeat;

    /**
     * 出发时间，起飞时间，乘车开始时间（HH:mm）
     */
    private String leaveTime;

    /**
     * 到达时间，乘车结束时间（HH:mm）
     */
    private String arriveTime;

    /**
     * 里程
     */
    private String mileage;

    /**
     * 发票在影像中的旋转角度（顺时针）
     */
    private Integer orientation;

    /**
     * 是否有公司印章（0：无；1：有）
     */
    private String hasSeal;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 车架号/车辆识别代码（机动车、二手车）
     */
    private String carCode;

    /**
     * 发动机号码（机动车）
     */
    private String carEngineCode;

    /**
     * 机打号码
     */
    private String machineNum;

    /**
     * 机打代码
     */
    private String machineInvoiceCode;

    /**
     * 发票联次
     */
    private String invoiceTemplateType;

    /**
     * 发票联次名称
     */
    private String invoiceTemplateName;

    /**
     * 发票密文
     */
    private String invoiceCiphertext;

    /**
     * 机器编号
     */
    private String machineCode;

    /**
     * 备注
     */
    private String remark;

    /**
     * 主管税务机关代码
     */
    private String taxAuthoritiesCode;

    /**
     * 主管税务机关名称
     */
    private String taxAuthoritiesName;

    /**
     * 厂牌型号
     */
    private String carModel;

    /**
     * 合格证号
     */
    private String certificateNo;

    /**
     * 二手车市场
     */
    private String marketName;

    /**
     * 登记证号
     */
    private String registrationNo;

    /**
     * 序列号
     */
    private String serialNum;

    /**
     * 保险费
     */
    private BigDecimal premium;

    /**
     * 印刷序号
     */
    private String printNumber;

    /**
     * 航班信息（航空电子票行程单）
     */
    private List<FlightInfoDTO> flightList;

    /**
     * 开票时间（例：12:22:22）
     */
    private String invoiceTime;

    /**
     * 入口（高速入口）
     */
    private String entrance;

    /**
     * 出口（高速出口）
     */
    private String roadExit;

    /**
     * 高速标识（0否 1是）
     */
    private String isHighway;

    /**
     * 申请日期（滴滴行程单申请日期）
     */
    private String applyDate;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 省份名称
     */
    private String province;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 行程开始日期（示例：2020年01月10日）
     */
    private String travelStartDate;

    /**
     * 行程结束日期（示例：2020年01月10日）
     */
    private String travelEndDate;

    /**
     * 发票详情（增值税发票明细）
     */
    private List<OcrInvoiceDetailDTO> detailList;

    /**
     * 行程单详情（滴滴行程单信息）
     */
    private List<CarTravelDTO> carList;

    /**
     * 通用机打发票标识（1是，0否）
     */
    private String printedMark;

    /**
     * 通行费标志（电子通行费标识 0:非通行费 1:通行费）
     */
    private String transitMark;
}