package com.zksr.system.api.partnerConfig.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 软件商支付账号配置类
 * @Author: liuxingyu
 * @Date: 2024/3/26 9:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayAccountConfigDTO {
    /**
     * 收款商户编号, 业务员, 平台商, 等等的分佣, 要从这个商编出, 因为入驻商充值到这里了
     */
    @ApiModelProperty("收款商户编号")
    private String gatheringTenantCode;

    /**
     * 提现商户商户编号, 平台商自己提现用的商户号
     */
    @ApiModelProperty("提现商户商户编号")
    private String withdrawTenantCode;

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;
}
