package com.zksr.system.api.EmailMessage.dto;

import com.zksr.common.core.annotation.Excel;
import lombok.*;

/**
* 同步数据报表统计Email实体
* @date 2024/7/18 15:32
* <AUTHOR>
*/
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SyncEmailReportExcel {

    /** 系统类型  枚举类型： SyncSourceType*/
    private Integer sourceType;

    /** 系统名称*/
    @Excel(name = "系统名称", cellType = Excel.ColumnType.STRING)
    private String sourceName;

    /** 资源级别类型 supplier/partner 入驻商级别/平台商级别*/
    @Excel(name = "资源级别类型", cellType = Excel.ColumnType.STRING)
    private String merchantType;

    /** 资源id 入驻商ID/平台商ID*/
    private String merchantId;

    /** 资源名称 入驻商名称/平台商名称*/
    @Excel(name = "资源名称", cellType = Excel.ColumnType.STRING)
    private String merchantName;

    /** 平台商ID*/
    private String sysCode;

    /** 平台商名称*/
    @Excel(name = "平台商名称", cellType = Excel.ColumnType.STRING)
    private String partnerName;

    /** 接口类型 */
    private Integer requestType;

    /** 接口信息 */
    @Excel(name = "接口信息", cellType = Excel.ColumnType.STRING)
    private String requestInfo;

    /** 发送次数 */
    @Excel(name = "发送次数", cellType = Excel.ColumnType.STRING)
    private Integer sendCount;

    /** 成功次数 */
    @Excel(name = "成功次数", cellType = Excel.ColumnType.STRING)
    private Integer successCount;

    /** 失败次数 */
    @Excel(name = "失败次数", cellType = Excel.ColumnType.STRING)
    private Integer failCount;

}
