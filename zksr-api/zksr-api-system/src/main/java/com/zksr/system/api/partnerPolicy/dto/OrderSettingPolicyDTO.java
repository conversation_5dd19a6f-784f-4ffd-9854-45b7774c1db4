package com.zksr.system.api.partnerPolicy.dto;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.utils.ToolUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * @Description: 运营商订单参数配置类
 * @Author: liuxingyu
 * @Date: 2024/3/12 11:14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OrderSettingPolicyDTO {
    /**
     * 未支付订单失效时间
     */
    @ApiModelProperty("未支付订单失效时间")
    private String orderExpiryDate;

    /**
     * 未支付订单失效时间单位(0 小时 1 分钟)
     */
    @ApiModelProperty("未支付订单失效时间单位(0 小时 1 分钟)")
    private String orderExpiryDateType;

    /**
     * 收货后订单完成时间
     */
    @ApiModelProperty("收货后订单完成时间")
    private String orderFinishDate;

    /**
     * 未支付订单失效时间单位(0 小时 1 分钟)
     */
    @ApiModelProperty("收货后订单完成时间单位(0 小时 1 分钟)")
    private String orderFinishDateType;

    /**
     * 发货后自动确认收获时间
     */
    @ApiModelProperty("发货后自动确认收获时间")
    private String orderVerifyTime;

    /**
     * 未支付订单失效时间单位(0 小时 1 分钟)
     */
    @ApiModelProperty("发货后自动确认收获时间单位(0 小时 1 分钟)")
    private String orderVerifyTimeType;

    /**
     * 是否展示订单售后时间
     */
    @ApiModelProperty("是否展示订单售后时间(默认不显示). 0-不显示, 1-显示")
    private String showAfterHour;

    @ApiModelProperty("订单发货超时时间")
    private String orderDeliveryOvertimeDay;


    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;


    /**
     * 获取收货后订单完成时间（分钟）
     * @return
     */
    public Long getOrderFinishDateMinute() {
        long orderDate  = 7200L; // 默认5天 》 120小时 》 7200分钟
        if (ToolUtil.isNotEmpty(orderFinishDate) && ToolUtil.isNotEmpty(orderFinishDateType)) {
            orderDate = Objects.equals(orderFinishDateType, "0") ? Long.parseLong(orderFinishDate) * 60 : orderDate;
        }
        return orderDate;
    }


    /**
     * 未支付订单失效时间（秒）
     * @return
     */
    public Integer getOrderExpiryDateSecond() {
        int orderValidity = 30 * 60;//默认30分钟  （单位：秒）
        if (ToolUtil.isNotEmpty(orderExpiryDate) && ToolUtil.isNotEmpty(orderExpiryDateType)) {
            orderValidity = (Objects.equals(orderExpiryDateType, "0") ? 60 : 1) * Integer.parseInt(orderExpiryDate) * 60;
        }
        return orderValidity;
    }
}
