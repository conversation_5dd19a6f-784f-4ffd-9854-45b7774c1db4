package com.zksr.system.api.partnerConfig.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Description: 美的付配置类
 * @Author: chenyj
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MideaPayConfigDTO {
    /**
     * 支付启用状态(0 禁用 1 启用)
     */
    @ApiModelProperty("支付启用状态(0 禁用 1 启用)")
    private String payEnableState;

    /**
     * 支付回调地址
     */
    @ApiModelProperty("支付回调地址")
    private String notifyUrl;


    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("配置类型 数据字典：sys_partner_config_type")
    private String configType;

    /**
     * 美的付平台商户号
     */
    @ApiModelProperty("美的付平台商户号")
    private String merchantNo;

    /**
     * 美的付子商户号
     */
    @ApiModelProperty("美的付子商户号")
    private String subMerchantNo;

    /**
     * 签名地址
     */
    @ApiModelProperty("签名地址")
    private String signUrl;

    /**
     * 验签地址
     */
    @ApiModelProperty("验签地址")
    private String checkSignUrl;

    /**
     * 美的付地址
     */
    @ApiModelProperty("美的付地址")
    private String payUrl;

    private String partnerKey;
}
