package com.zksr.system.api.visual.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 可视化配置主对象 visual_setting_master
 *
 * <AUTHOR>
 * @date 2024-06-29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class VisualSettingTemplateDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** id */
    private Long visualTemplateId;

    /** 状态（0 停用  1启用） */
    @Excel(name = "状态", readConverterExp = "0=,停=用,1=启用")
    private Integer status;

    /** 接口模板名称 */
    @Excel(name = "接口模板名称")
    private String templateName;

    /** 接口模板JSON */
    @Excel(name = "接口模板JSON")
    private String apiTemplate;

    /** 接口模板类型 */
    @Excel(name = "接口模板类型")
    private Long templateType;

    /** 系统类型(数据字典sync_source_type 枚举：syncSourceType) */
    @Excel(name = "系统类型(安得ERP、安得WMS、新ERP)")
    private Integer sourceType;

}
