package com.zksr.system.api.wx.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信二维码参数
 */
@Data
public class WxQrData extends BaseEntity {

    @TableId(type = IdType.AUTO)
    private Long qrId;

    @ApiModelProperty("二维码key")
    private String qrKey;

    @ApiModelProperty("二维码value")
    private String qrValue;

    private Long sysCode;

}
