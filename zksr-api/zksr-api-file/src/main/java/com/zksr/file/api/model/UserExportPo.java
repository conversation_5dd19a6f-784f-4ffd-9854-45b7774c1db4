package com.zksr.file.api.model;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户导出
 * @date 2024/1/15 15:39
 */
@Data
public class UserExportPo extends BaseEntity {
    @Excel(name = "用户名称")
    private String userName;
    @Excel(name = "用户手机号")
    private String phonenumber;
}
