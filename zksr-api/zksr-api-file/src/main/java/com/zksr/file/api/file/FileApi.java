package com.zksr.file.api.file;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.file.api.file.vo.ImageInfoReqVO;
import com.zksr.file.api.file.vo.MchProfitRateReqVO;
import com.zksr.file.api.file.vo.SysFileImportVO;
import com.zksr.file.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/4/24 10:36
*/
@FeignClient(
        contextId = "remoteFileApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface FileApi {

    String PREFIX = ApiConstants.PREFIX ;


    /**
     * 文件上传
     * @param file
     * @return
     */
    @PostMapping(value = PREFIX + "/uploadFileByMail", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CommonResult<String> uploadFileByMail(@RequestPart(value = "file") MultipartFile file) throws Exception;

    /**
     * 下载选中的图片 打包成zip
     * @param images
     * @return
     */
    @PostMapping(value = PREFIX + "/downloadImages")
    public byte[] downloadImages(@RequestBody List<ImageInfoReqVO> images) throws IOException;


    /**
     * 下载base64 图片
     * @param imgUrl    图片地址
     * @return  base64
     */
    @PostMapping(value = PREFIX + "/downloadByBase64")
    public CommonResult<String> downloadByBase64(@RequestParam("imgUrl") String imgUrl);

    /**
     * 刷新微信令牌
     * @return
     */
    @GetMapping(value = PREFIX + "/refreshWxToken")
    public CommonResult<Boolean> refreshWxToken();

    @PostMapping(value = PREFIX + "/importJobExecute")
    CommonResult<Boolean> importJobExecute(@RequestBody SysFileImportVO sysFileImport);
}
