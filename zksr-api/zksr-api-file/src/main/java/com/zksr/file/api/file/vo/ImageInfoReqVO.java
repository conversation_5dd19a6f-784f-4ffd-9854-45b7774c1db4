package com.zksr.file.api.file.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date 2024/5/11 15:11
 * 图片信息 ----Request VO
 */
@Data
@ApiModel("图片信息 ----Request VO")
public class ImageInfoReqVO {

    @Excel(name = "图片地址")
    @ApiModelProperty(value = "图片地址")
    private String url;

    @Excel(name = "图片名称")
    @ApiModelProperty(value = "图片名称")
    private String name;

    @Excel(name = "图片日期")
    @ApiModelProperty(value = "图片日期")
    private String date;
}
