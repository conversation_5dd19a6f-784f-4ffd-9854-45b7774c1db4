package com.zksr.file.api.constant;

public enum ImportTypeEnum {
    MEM_BRANCH_IMPORT("mem_branch_import","门店信息"),
    BRAND_IMPORT("brand_import","品牌信息"),
    CATEGORY_IMPORT("category_import","平台管理类别信息"),
    AREA_CLASS_IMPORT("area_class_import","城市展示分类数据"),
    PRD_AREA_ITEM_IMPORT("prd_area_item_import","城市上架商品信息导入---------"),
    MEM_COLONEL_IMPORT("mem_colonel_import","业务员信息"),
    TRD_DRIVER_IMPORT("trd_driver_import","司机"),
    PRODUCT_PLAT_FORM("product_plat_form","平台商品库"),
    PRD_SPU_IMPORT("prd_spu_import","商品");

    private String code;

    private String name;

    ImportTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
