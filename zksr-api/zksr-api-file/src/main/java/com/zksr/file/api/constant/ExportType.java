package com.zksr.file.api.constant;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldNameConstants;

/**
 * wangke
 * 导出类型
 */
@Getter
@FieldNameConstants(onlyExplicitlyIncluded = true)
public enum ExportType {
    USER("user_export", "系统用户导出"),
    MERCHANT_SETTLE("merchant_settle_export", "商户结算明细导出"),
    SKU_PRICES("sku_prices_export", "商品SKU价格导出"),
    ORDER_STATEMENT_OF_ACCOUNT("order_statement_of_account_export", "订单分佣对账单导出"),
    SPU("spu_export", "商品SPU导出","spuListCol"),
    HELLO("hello", "测试导出"),
    BRANCH("own_branch_statement_export", "门店导出", "branchListCol"),
    ACC_TRANSFER_BILL("acc_transfer_bill_export","交易对账单导出","transferBillCol"),
    TRD_ORDER("trd_order_export","订单导出","OperationOrder"),
    BRANCH_SALES_MONTH("branch_sales_month_export", "门店月销售数据导出"),
    COLONEL_SALES_MONTH("colonel_sales_month_export", "业务员月销售数据导出"),
    ACC_TRANSFER_BILL_ORDER("acc_transfer_bill_order_export", "交易对账单明细导出", "transferBillOrderCol"),
    MEM_COLONEL_VISIT_LOG("mem_colonel_visit_log_export","业务员拜访日志导出"),
    AREA_CLASS("prdt_area_class_export","城市展示类别导出","areaClassListCol"),
    SALE_CLASS("prdt_sale_class_export","平台展示类别导出","areaClassListCol"),
    COUPON_RECEIVE_EXPORT("prm_coupon_receive_export","优惠券领取记录导出"),
    DEBT_ORDER("debt_order_export","欠款订单导出","debtOrder"),
    SKU_PRICE_ORDER("sku_price_export","城市价格导出","skuPrice"),
    COMPLAIN("complain_export","投诉管理数据导出","complainCol"),
    CATEGORY("category_export","平台商管理分类导出","categoryCol"),
    AFTER_SALESA("after_order_export","售后管理导出","afterSalesa"),
    TRD_SUPPLIER_ORDER("trd_supplier_order_export","入驻商订单导出");

    public static final String USER_EXPORT = "user_export";
    public static final String MERCHANT_SETTLE_EXPORT = "merchant_settle_export";
    public static final String SKU_PRICES_EXPORT = "sku_prices_export";
    public static final String ORDER_STATEMENT_OF_ACCOUNT_EXPORT = "order_statement_of_account_export";
    public static final String SPU_EXPORT = "spu_export";
    public static final String OWN_BRANCH_STATEMENT_EXPORT = "own_branch_statement_export";
    public static final String TRD_ORDER_EXPORT = "trd_order_export";
    public static final String ACC_TRANSFER_BILL_EXPORT = "acc_transfer_bill_export";
    public static final String BRANCH_SALES_MONTH_EXPORT = "branch_sales_month_export";
    public static final String COLONEL_SALES_MONTH_EXPORT = "colonel_sales_month_export";
    public static final String ACC_TRANSFER_BILL_ORDER_EXPORT = "acc_transfer_bill_order_export";
    public static final String PRDT_AREA_CLASS_EXPORT = "prdt_area_class_export";
    public static final String PRDT_SALE_CLASS_EXPORT = "prdt_sale_class_export";
    public static final String MEM_COLONEL_VISIT_LOG_EXPORT = "mem_colonel_visit_log_export";
    public static final String PRM_COUPON_RECEIVE_EXPORT = "prm_coupon_receive_export";
    public static final String DEBT_ORDER_EXPORT = "debt_order_export";
    public static final String SKU_PRICE_EXPORT = "sku_price_export";
    public static final String COMPLAIN_EXPORT = "complain_export";
    public static final String CATEGORY_EXPORT = "category_export";
    public static final String AFTER_ORDER_EXPORT = "after_order_export";
    public static final String TRD_SUPPLIER_ORDER_EXPORT = "trd_supplier_order_export";

    @ApiModelProperty("导出任务名称(EN)")
    private String type;

    @ApiModelProperty("导出任务名称(CN)")
    private String name;

    @ApiModelProperty("导出任务配置")
    private String colTableCode;

    ExportType(String type, String name) {
        this.type = type;
        this.name = name;
    }

    ExportType(String type, String name, String colTableCode) {
        this.type = type;
        this.name = name;
        this.colTableCode = colTableCode;
    }

    public static ExportType fromValue(String value) {
        for (ExportType myEnum : ExportType.values()) {
            if (myEnum.type.equals(value)) {
                return myEnum;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
