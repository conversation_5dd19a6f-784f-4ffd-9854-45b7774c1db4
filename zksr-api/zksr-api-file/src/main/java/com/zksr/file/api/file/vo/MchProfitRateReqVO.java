package com.zksr.file.api.file.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 报名微信技术服务费
 * @date 2024/10/14 11:10
 */
@Data
public class MchProfitRateReqVO {

    @ApiModelProperty("appid")
    private String appid;

    @ApiModelProperty("微信商户号")
    private String subMchid;

    @ApiModelProperty(value = "服务费率", name = "最大0.6%")
    private BigDecimal profitRate;
}
