package com.zksr.report.api.branch.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户异常拜访判断规则
 * @date 2024/11/12 8:53
 */
@Data
@ApiModel(description = "客户异常拜访判断规则")
public class BranchAbnormalVisitConfigDTO {

    @ApiModelProperty("同一个客户一天内拜访次数超过")
    private Integer dayCnt;

    @ApiModelProperty("同一个客一个月内拜访次数超过")
    private Integer monthVisitDay;

    @ApiModelProperty("同一个客户2次拜访时间小于")
    private Integer secondCntLtTime;
}
