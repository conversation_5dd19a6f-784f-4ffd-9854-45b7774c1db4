package com.zksr.report.api.homePages.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("PC首页获取销售退货数据返回 - HomePagesAfterSalesDataRespDTO Response VO")
public class HomePagesOrderAfterDataRespDTO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 运营商ID */
    @ApiModelProperty(value = "运营商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 入驻商ID */
    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 订单退单金额 */
    @ApiModelProperty(value = "订单退单金额")
    private BigDecimal returnAmt;

    /** 上次订单退单金额 */
    @ApiModelProperty(value = "上次订单退单金额")
    private BigDecimal beforeReturnAmt;

    /** 订单退单金额同比上升/下降率 */
    @ApiModelProperty(value = "订单退单金额同比上升/下降率")
    private BigDecimal returnAmtRate;

    /** 订单退单数量 */
    @ApiModelProperty(value = "订单退单数量")
    private Long returnQty;

    /** 上次订单退单数量 */
    @ApiModelProperty(value = "上次订单退单数量")
    private Long beforeReturnQty;

    /** 订单退单数量同比上升/下降率 */
    @ApiModelProperty(value = "订单退单数量同比上升/下降率")
    private BigDecimal returnQtyRate;

    /** 退单门店数量 */
    @ApiModelProperty(value = "退单门店数量")
    private Long returnBranchQty;

    /** 上次退单门店数量 */
    @ApiModelProperty(value = "上次退单门店数量")
    private Long beforeReturnBranchQty;

    /** 退单门店数量同比上升/下降率 */
    @ApiModelProperty(value = "退单门店数量同比上升/下降率")
    private BigDecimal returnBranchQtyRate;

    /** 退单SKU数  */
    @ApiModelProperty(value = "退单SKU数")
    private Long returnSkuQty;

    /** 上次退单SKU数  */
    @ApiModelProperty(value = "上次退单SKU数")
    private Long beforeReturnSkuQty;

    /** 退单SKU数量同比上升/下降率 */
    @ApiModelProperty(value = "退单SKU数量同比上升/下降率")
    private BigDecimal returnSkuQtyRate;

    /** 待处理退单金额  */
    @ApiModelProperty(value = "待处理退单金额")
    private BigDecimal pendingReturnAmt;

    /** 待处理退单数量  */
    @ApiModelProperty(value = "待处理退单数量")
    private Long pendingReturnQty;

    /** 待处理退单门店数量  */
    @ApiModelProperty(value = "待处理退单门店数量")
    private Long pendingeturnBranchQty;

}
