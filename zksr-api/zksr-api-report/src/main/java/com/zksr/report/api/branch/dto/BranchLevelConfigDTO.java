package com.zksr.report.api.branch.dto;

import com.zksr.common.core.enums.ConditionEnum;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户等级定义
 * @date 2024/11/12 8:43
 */
@Data
@ApiModel(description = "客户等级定义")
public class BranchLevelConfigDTO {

    @ApiModelProperty(value = "用户标签ID", hidden = true)
    private Long tagDefId;

    @ApiModelProperty("用户标签")
    private BranchTagEnum branchTag;

    @ApiModelProperty("条件")
    private ConditionEnum condition;

    @ApiModelProperty("0-优先级最高")
    private Integer sort;

    @ApiModelProperty(value = "条件值1, (百分比0.03最大1), (数值, 任意)", example = "0.03, 1")
    private BigDecimal valA;

    @ApiModelProperty(value = "条件值2, (百分比0.03最大1), (数值, 任意)", example = "0.03, 1")
    private BigDecimal valB;

    @ApiModelProperty(value = "是否有指定管理分类", hidden = true)
    private List<String> catgoryList;
}
