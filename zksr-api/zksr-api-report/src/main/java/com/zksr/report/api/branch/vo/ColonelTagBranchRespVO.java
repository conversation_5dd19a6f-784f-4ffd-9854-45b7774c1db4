package com.zksr.report.api.branch.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员标签客户数据,
 * @date 2024/11/25 9:59
 */
@Data
@NoArgsConstructor
@ApiModel(description = "业务员标签客户数据 - response")
public class ColonelTagBranchRespVO {

    @ApiModelProperty("门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("销售额")
    private BigDecimal saleAmt;

    @ApiModelProperty("任务金额")
    private BigDecimal targetAmt;

    @ApiModelProperty("上次拜访时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date beforeVisitTime;

    @ApiModelProperty("上次下单时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date beforeOrderTime;

    @ApiModelProperty("约拜访次数")
    private Long monthVisitCnt;

    @ApiModelProperty("距离, 单位米")
    private Double distance;

    @ApiModelProperty("销售等级, level_a=A级, level_b=B级, level_c=C级, level_d=D级, level_e=E级")
    private String levelTagType;

    @ApiModelProperty("订货频次, create_order_a=高频次, create_order_b=中频次, create_order_c=低频次")
    private String freqLevelTagType;

    @ApiModelProperty("类占比, class_a=高类占, class_b=中类占, class_c=低类占")
    private String lzLevelTagType;

    @ApiModelProperty("毛利率, profit_a=高毛利, profit_b=中毛利, profit_c=低毛利")
    private String profitLevelTagType;

    @ApiModelProperty("活跃用户, active_cust=活跃用户")
    private String activeTagType;
}
