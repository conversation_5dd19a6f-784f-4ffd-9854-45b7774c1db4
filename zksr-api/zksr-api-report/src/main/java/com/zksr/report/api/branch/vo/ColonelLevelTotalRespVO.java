package com.zksr.report.api.branch.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员管理门店标签统计,
 * @date 2024/11/25 9:59
 */
@Data
@NoArgsConstructor
@ApiModel(description = "业务员管理门店标签统计 - response")
public class ColonelLevelTotalRespVO {

    @ApiModelProperty(value = "标签门店总数", hidden = true)
    private Long tagTotal;

    @ApiModelProperty("业务员标签统计")
    private List<ColonelTotal> list = new ArrayList<>();

    public ColonelLevelTotalRespVO(Long tagTotal) {
        this.tagTotal = tagTotal;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ColonelTotal {

        @ApiModelProperty("业务员ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long colonelId;

        @ApiModelProperty("业务员名称")
        private String colonelName;

        @ApiModelProperty("标签门店总数")
        private Long tagTotal;

        @ApiModelProperty("门店数量")
        private Long branchNum;
    }
}
