package com.zksr.report.api.branch.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店数据分析详情 response
 * @date 2024/11/23 9:07
 */
@Data
@ApiModel(description = "门店数据分析详情 response")
public class BranchAnalyseInfoRespVO {

    @ApiModelProperty("月销售任务列表")
    private List<BranchTargetProcessVO> targetProcesses;

    @ApiModelProperty("客户余额 (暂无效)")
    private BigDecimal branchBalance = BigDecimal.ZERO;

    @ApiModelProperty("客户欠款")
    private BigDecimal branchDebt = BigDecimal.ZERO;

    @ApiModelProperty("客户积分 (暂无效)")
    private BigDecimal branchPoint = BigDecimal.ZERO;

    @ApiModelProperty("有效优惠券张数")
    private Long validCouponTotal;

    @ApiModelProperty("月周份订货习惯")
    private AdsBranchTradeWeekdayDistributionRespVO weekdayDistribution;

    @ApiModelProperty("上次下单时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date lastOrderTime;

    @ApiModelProperty("上次下单金额")
    private BigDecimal lastOrderAmt;

    @ApiModelProperty("月标签数据")
    private AdsBranchTagMonthRespVO tagMonthRespVO;

    @ApiModelProperty("门店一级管理分类订货占比")
    private List<CategorySaleTotalVO> branchCategorySaleList = new ArrayList<>();

    @ApiModelProperty("24小时下单分布")
    private List<HoursSaleVO> hoursSaleList;
}
