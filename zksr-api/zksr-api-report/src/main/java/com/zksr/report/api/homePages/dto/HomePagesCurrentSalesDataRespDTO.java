package com.zksr.report.api.homePages.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel("PC首页当前销售、欠款、退单实时数据返回 - CurrentSalesDataRespDTO Response VO")
public class HomePagesCurrentSalesDataRespDTO {
    private static final long serialVersionUID = 1L;

    /** 平台商Id */
    @ApiModelProperty(value = "平台商Id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 运营商ID */
    @ApiModelProperty(value = "运营商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 入驻商ID */
    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 当天订单实际销售额 */
    @ApiModelProperty(value = "当天订单实际销售额")
    private BigDecimal todayOrderTotalAmt;

    /** 咋天订单实际销售额 */
    @ApiModelProperty(value = "咋天订单实际销售额")
    private BigDecimal yesterdayOrderTotalAmt;

    /** 销售金额同比上升率（当天订单金额 - 咋天订单金额）/ 咋天订单金额 * 100 */
    @ApiModelProperty(value = "销售金额同比上升率")
    private BigDecimal orderAmtRate;

    /** 欠款总金额（待回款） */
    @ApiModelProperty(value = "欠款总金额")
    private BigDecimal debtTotalAmt;

    /** 总欠款订单数量（待回款）  */
    @ApiModelProperty(value = "总欠款订单数量")
    private Long debtOrderTotalQty;

    /** 总欠款门店数量（待回款）  */
    @ApiModelProperty(value = "总欠款门店数量")
    private Long debtBranchTotalQty;

    /** 总退单金额（待处理单据） */
    @ApiModelProperty(value = "总退单金额")
    private BigDecimal afterTotalAmt;

    /** 总退单数量（待处理单据）  */
    @ApiModelProperty(value = "总退单数量")
    private Long afterTotalQty;

    /** 总退单门店数量（待处理单据）  */
    @ApiModelProperty(value = "总退单门店数量")
    private Long afterBranchTotalQty;

    //================-------月销售信息-------================
    /** 月销售金额 */
    @ApiModelProperty(value = "月销售金额")
    private BigDecimal monthOrderAmt;

    /** 上月销售金额  */
    @ApiModelProperty(value = "上月销售金额")
    private BigDecimal beforeMonthOrderAmt;

    /** 月销售金额金额同比上升/下降率  */
    @ApiModelProperty(value = "月销售金额金额同比上升/下降率")
    private BigDecimal monthOrderRate;

    /** 刷新时间 */
    @ApiModelProperty(value = "更新ES时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refreshEsTime;
}
