package com.zksr.report.api.branch.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/28 16:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HoursSaleTotalVO {

    @ApiModelProperty("销售格式HH")
    private String hours;

    @ApiModelProperty("单据数量")
    private Long orderCnt;

    @ApiModelProperty("客次数量")
    private Long orderUserCnt;
}
