package com.zksr.report.api.branch.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/29 10:01
 */
@Data
@ApiModel(description = "任务完成度")
public class BranchTargetProcessVO {

    @ApiModelProperty("月份格式, yyyy-MM")
    private String monthDate;

    @ApiModelProperty("销售额")
    private BigDecimal saleAmt;

    @ApiModelProperty("目标销售额")
    private BigDecimal targetAmt;

    @ApiModelProperty("代金券金额")
    private BigDecimal couponAmt;

    public String getYear() {
        return monthDate.split("-")[0];
    }

    public String getMonth() {
        return monthDate.split("-")[1];
    }
}
