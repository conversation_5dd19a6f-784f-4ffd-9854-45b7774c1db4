package com.zksr.report.api.homePages.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

@ApiModel("PC后台主页报表 - HomePageReqVO Request VO")
@Data
//@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class HomePagesReqVO {
    @ApiModelProperty(value = "查询年月日;字符串格式（yyyy-mm-dd）")
    private String startDate;

    @ApiModelProperty(value = "查询年月日;字符串格式（yyyy-mm-dd）")
    private String endDate;

    @ApiModelProperty(value = "运营商ID")
    private Long dcId;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "查询年月;字符串格式（yyyymm或yyyymmdd）", hidden = true)
    private String dateId;

//    @ApiModelProperty(value = "订单类型：0 全国订单 1：本地订单")
//    private Integer orderType;

    @ApiModelProperty(value = "平台商ID", hidden = true)
    private Long sysCode;

    @ApiModelProperty(value = "是否是运营商", hidden = true)
    private Integer isDc;

    @ApiModelProperty(value = "是否是入驻商", hidden = true)
    private Integer isSupplier;

    @ApiModelProperty(value = "城市ID集合", hidden = true)
    private Set<Long> areaIds;

    @ApiModelProperty(value = "三级管理分类ID集合", hidden = true)
    private Set<Long> categoryIds;

    /** 销售TOP10类型（区域：area，门店：branch，一级品类：category，运营商：dc，商品：itme，入驻商：supplier，业务员：colonel） */
    @ApiModelProperty(value = "销售TOP10类型", hidden = true)
    private String salesType;
}
