package com.zksr.report.api.homePages.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@ApiModel("PC首页获取门店数据返回 - HomePagesBranchDataRespDTO Response VO")
@Accessors(chain = true)
public class HomePagesBranchDataRespDTO {
    private static final long serialVersionUID = 1L;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 运营商ID */
    @ApiModelProperty(value = "运营商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /** 区域城市 */
    @ApiModelProperty(value = "区域城市")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    /** 新增门店数量 */
    @ApiModelProperty(value = "新增门店数量")
    private Long branchAddQty = NumberPool.LONG_ZERO;

    /** 上次新增门店数量 */
    @ApiModelProperty(value = "上次新增门店数量")
    private Long beforeBranchAddQty = NumberPool.LONG_ZERO;

    /** 新增门店同比上升/下降率  */
    @ApiModelProperty(value = "新增门店同比上升/下降率")
    private BigDecimal branchAddRate = BigDecimal.ZERO;

    /** 动销门店数量 */
    @ApiModelProperty(value = "动销门店数量")
    private Long branchSalesQty = NumberPool.LONG_ZERO;

    /** 上次动销门店数量 */
    @ApiModelProperty(value = "上次动销门店数量")
    private Long beforeBranchSalesQty = NumberPool.LONG_ZERO;

    /** 动销门店数量同比上升/下降率  */
    @ApiModelProperty(value = "动销门店数量同比上升/下降率")
    private BigDecimal branchSalesRate = BigDecimal.ZERO;

    /** 动销率  */
    @ApiModelProperty(value = "动销率")
    private BigDecimal salesRate = BigDecimal.ZERO;

    /** 总计门店数量 */
    @ApiModelProperty(value = "总计门店数量")
    private Long branchTotalQty = NumberPool.LONG_ZERO;

    /** 上次总计门店数 */
    @ApiModelProperty(value = "上次总计门店数")
    private Long beforeBranchTotalQty = NumberPool.LONG_ZERO;

    /** 总计门店数量同比上升/下降率  */
    @ApiModelProperty(value = "总计门店数量同比上升/下降率")
    private BigDecimal branchTotalRate = BigDecimal.ZERO;

    /** 业务员总计数量  */
    @ApiModelProperty(value = "业务员总计数量")
    private Long colonelTotalQty = NumberPool.LONG_ZERO;

    /** 拜访门店数量 */
    @ApiModelProperty(value = "拜访门店数量")
    private Long visitBranchQty = NumberPool.LONG_ZERO;

    /** 上次拜访门店数量  */
    @ApiModelProperty(value = "上次拜访门店数量")
    private Long beforeVisitBranchQty = NumberPool.LONG_ZERO;

    /** 拜访门店数量同比上升/下降率 */
    @ApiModelProperty(value = "拜访门店数量同比上升/下降率")
    private BigDecimal visitBranchRate = BigDecimal.ZERO;

    /** 登录门店数量 */
    @ApiModelProperty(value = "登录门店数量")
    private Long loginBranchQty = NumberPool.LONG_ZERO;

    /** 上次登录门店数量  */
    @ApiModelProperty(value = "上次登录门店数量")
    private Long beforeLoginBranchQty = NumberPool.LONG_ZERO;

    /** 登录门店数量同比上升/下降率 */
    @ApiModelProperty(value = "登录门店数量同比上升/下降率")
    private BigDecimal loginBranchRate = BigDecimal.ZERO;

    /** 拜访门店率 */
    @ApiModelProperty(value = "拜访门店率")
    private BigDecimal visitRate;
}
