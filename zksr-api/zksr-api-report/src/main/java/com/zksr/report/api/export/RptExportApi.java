package com.zksr.report.api.export;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.report.api.export.dto.BranchSalesSummaryExportDTO;
import com.zksr.report.api.export.dto.ColonelSalesSummaryExportDTO;
import com.zksr.report.api.export.vo.BranchSalesSummaryExportPageVO;
import com.zksr.report.api.export.vo.ColonelSalesSummaryExportPageVO;
import com.zksr.report.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 报表模块导出API接口
 */
@FeignClient(
        contextId = "RptExportApi",
        value = ApiConstants.NAME
)
public interface RptExportApi {
    String PREFIX = ApiConstants.PREFIX + "/rptExport";

    /**
     * 获取门店月销售报表导出数据 - month
     * @param reqVo
     */
    @PostMapping(value = PREFIX + "/getMonthBranchSalesSummaryExport")
    public CommonResult<List<BranchSalesSummaryExportDTO>> getMonthBranchSalesSummaryExport(@RequestBody BranchSalesSummaryExportPageVO reqVo);

    /**
     * 获取业务员月销售报表导出数据 - month
     * @param reqVo
     */
    @PostMapping(value = PREFIX + "/getMonthColonelSalesSummaryExport")
    public CommonResult<List<ColonelSalesSummaryExportDTO>> getMonthColonelSalesSummaryExport(@RequestBody ColonelSalesSummaryExportPageVO reqVo);
}
