package com.zksr.report.api.branch.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/28 9:28
 */
@Data
@ApiModel(description = "业务员标签门店列表数据")
public class TagBranchListReqVO extends ColonelBranchTagPageReqVO{

    @ApiModelProperty(value = "当前业务员纬度坐标, 计算相对距离必须传入")
    private Double lat;

    @ApiModelProperty(value = "当前业务员精度坐标, 计算相对距离必须传入")
    private Double lon;

    @ApiModelProperty("销售区间起始值")
    private BigDecimal saleAmtStart;

    @ApiModelProperty("销售区间结束值")
    private BigDecimal saleAmtEnd;

    @ApiModelProperty("排序规则,1-销售额降序(默认), 2-销售额升序")
    private Integer sortType = 1;

    @ApiModelProperty("门店名称")
    private String branchName;
}
