package com.zksr.report.api.branch.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 月门店周下单统计,
 * @date 2024/11/25 9:59
 */
@Data
@ApiModel(description = "月门店周下单统计 - response")
public class ColonelBranchMonthWeekTotalRespVO {

    @ApiModelProperty("周下单数据")
    private List<ChatLine> weekList;

    @Data
    @NoArgsConstructor
    public static class ChatLine {

        @ApiModelProperty("星期几")
        private String week;

        @ApiModelProperty("门店数量")
        private Integer dxBranchQty;

        @ApiModelProperty("订单数")
        private Integer orderQty;

        @ApiModelProperty("订单金额")
        private BigDecimal orderAmt;

        public ChatLine(String week, Integer dxBranchQty, Integer orderQty, BigDecimal orderAmt) {
            this.week = week;
            this.dxBranchQty = Objects.nonNull(dxBranchQty) ? dxBranchQty : 0;
            this.orderQty = Objects.nonNull(orderQty) ? orderQty : 0;
            this.orderAmt = Objects.nonNull(orderAmt) ? orderAmt : BigDecimal.ZERO;
        }
    }
}
