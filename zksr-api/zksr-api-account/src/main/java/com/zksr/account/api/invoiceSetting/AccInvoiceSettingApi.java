package com.zksr.account.api.invoiceSetting;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.zksr.account.api.invoiceSetting.dto.AccInvoiceSettingDTO;
import com.zksr.account.api.invoiceSetting.vo.AccInvoiceSettingRespVO;
import com.zksr.account.api.platformMerchant.dto.PlatformMerchantDTO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantPageReqVO;
import com.zksr.account.api.platformMerchant.vo.AccPlatformMerchantRespVO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;

@FeignClient(
        contextId = "remoteAccInvoiceSettingApi",
        value = ApiConstants.NAME
)
public interface AccInvoiceSettingApi {

    String PREFIX = ApiConstants.PREFIX + "";
    /**
     * 新增发票设置
     * @return  商户ID
     */
    @PostMapping(PREFIX + "/saveAccInvoiceSetting")
    CommonResult<Long> saveAccInvoiceSetting(@RequestBody AccInvoiceSettingDTO accInvoiceSettingDTO);


    /**
     * 获取发票设置信息
     * @param merchantType  商户类型参加 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param merchantId    商户ID, 入驻商ID, 业务员ID....
     * @param sysCode       平台ID
     * @return  商户信息
     */
    @PostMapping(PREFIX + "/getAccInvoiceSettingRespVO")
    CommonResult<AccInvoiceSettingRespVO> getAccInvoiceSettingRespVO(
            @RequestParam("merchantType") String merchantType,
            @RequestParam("merchantId") Long merchantId,
            @RequestParam("platform") String platform,
            @RequestParam("sysCode") Long sysCode
    );
    
    @PostMapping(PREFIX + "/getInvoiceSettingList")
    CommonResult<List<AccInvoiceSettingRespVO>> getInvoiceSettingList(@RequestBody AccPlatformMerchantRespVO build);
}
