package com.zksr.account.enums.balance;

import com.zksr.common.core.pool.StringPool;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum ActionTypeEnum {

    RECHARGE(0, "充值"),

    ORDER_PAY(1, "订单支付"),

    ORDER_CANCEL(2, "取消订单"),

    REFUND(3, "后台退款");

    private final Integer code;

    private final String value;

    ActionTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 转换操作类型编码为value值
     */
    public static String getValueByCode(Integer code) {
        return Arrays.stream(ActionTypeEnum.values()).filter(t -> Objects.nonNull(t) && Objects.nonNull(code)
                && code.equals(t.getCode())).map(ActionTypeEnum::getValue).findFirst().orElse(StringPool.EMPTY);
    }

    /*
     * 操作类型是否为充值或订单取消
     */
    public static boolean isRechargeOrOrderCancel(Integer code) {
        return Objects.nonNull(code) && (RECHARGE.getCode().equals(code) || ORDER_CANCEL.getCode().equals(code));
    }

}
