package com.zksr.account.model.pay.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/17 14:04
 */
@Data
@ApiModel(description = "请求分账")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateDivideReqVO {

    @ApiModelProperty(value = "实际支付单号", notes = "这里是必须是实际支付单号, 并且只接受微信B2B支付订单", required = true)
    private String tradeNo;

    /**
     * 微信B2B不支持此项业务,
     * 合利宝支付在线分账支持此项业务, 同一个订单允许多次分账, 且合利宝支付, 入驻商也需要参与最终分账
     */
    @ApiModelProperty(value = "入驻商订单号", notes = "微信B2B不支持此项业务, 合利宝支付在线分账支持此项业务")
    private String subTradeNo;

    @ApiModelProperty(value = "商户ID", required = true)
    private Long merchantId;

    /**
     * 商户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;
    
    /**
     * 分销模式
     */
    @ApiModelProperty(value = "分销模式")
    private String distributionMode;

    public CreateDivideReqVO(String tradeNo, Long merchantId, String merchantType) {
        this.tradeNo = tradeNo;
        this.merchantId = merchantId;
        this.merchantType = merchantType;
    }
}
