package com.zksr.account.model.pay.dto;

import cn.hutool.core.util.StrUtil;
import com.zksr.common.core.constant.OrderTypeConstants;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付订单抽象
 * @date 2024/3/7 16:36
 */
@Data
public class PayOrderDTO {
    @ApiModelProperty(value = "业务ID", notes = "订单ID, 充值单ID")
    private Long busiId;

    @ApiModelProperty(value = "支付ID", notes = "目的用于抽离支付")
    private Long flowId;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "openid")
    private String openid;

    @ApiModelProperty(value = "sessionKey")
    private String sessionKey;

    @ApiModelProperty(value = "退款单号")
    private String refundNo;

    @ApiModelProperty(value = "支付金额,单位/元")
    private BigDecimal payAmt;

    @ApiModelProperty(value = "门店储值赠送余额承担金额", notes = "仅在储值支付时有效")
    private BigDecimal walletGiveAmt;

    @ApiModelProperty(value = "手续费")
    private BigDecimal fee;

    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    @ApiModelProperty(value = "商品描述", notes = "长度限制100")
    private String body = "";

    public void setBody(String body) {
        this.body = body;
        if (StringUtils.isNotEmpty(this.body)) {
            this.body = StrUtil.maxLength(this.body, 20);
        }
    }

    /**
     * {@link OrderTypeConstants}
     */
    @ApiModelProperty(value = "订单类型", hidden = true)
    private Integer orderType;

    @ApiModelProperty(value = "appid", hidden = true)
    private String appid;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "商户ID", notes = "发起支付商户ID, 微信B2B使用")
    private String mchId;

    /**
     * {@link com.zksr.common.core.enums.PayMethodEnum}
     */
    @ApiModelProperty(value = "wx-微信支付, alipay-支付宝支付")
    private String method;

    @ApiModelProperty(value = "分账信息")
    private List<OrderSettlementDTO> settlements;

    @ApiModelProperty(value = "发起支付ip")
    private String remoteIp;

    private boolean isCombinePay;
}
