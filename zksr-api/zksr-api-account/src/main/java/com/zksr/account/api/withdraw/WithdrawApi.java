package com.zksr.account.api.withdraw;

import com.zksr.account.api.transfer.dto.AccTransferBillOrderDTO;
import com.zksr.account.api.transfer.dto.TransferSaveDTO;
import com.zksr.account.api.withdraw.dto.AccWithdrawBillDTO;
import com.zksr.account.api.withdraw.dto.SaveWithdrawDTO;
import com.zksr.account.api.withdraw.dto.WithdrawDTO;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.api.withdraw.vo.BranchWithdrawReqVO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/15
 * @desc
 */
@FeignClient(
        contextId = "remoteWithdrawApi",
        value = ApiConstants.NAME
)
public interface WithdrawApi {

    String PREFIX = ApiConstants.PREFIX + "/withdraw";

    /**
     *  创建业务员提现单
     */
    @PostMapping(PREFIX + "/createColonelWithdraw")
    CommonResult<Long> createColonelWithdraw(@RequestBody SaveWithdrawDTO saveWithdraw);

    /**
     *  创建门店提现单
     */
    @PostMapping(PREFIX + "/createBranchWithdraw")
    CommonResult<Boolean> createBranchWithdraw(@RequestBody BranchWithdrawReqVO withdrawReqVO);

    /**
     *  获取提现单详情
     */
    @GetMapping(PREFIX + "/getWithdrawInfo")
    CommonResult<WithdrawDTO> getWithdrawInfo(@RequestParam("withdrawId") Long withdrawId);

    /**
     *  获取提现单详情
     */
    @PostMapping(PREFIX + "/getWithdrawPage")
    CommonResult<PageResult<WithdrawDTO>> getWithdrawPage(@RequestBody AccWithdrawPageReqVO pageReqVO);

    /**
     * 批量保存提现对账单明细单
     */
    @PostMapping(PREFIX + "/insertAccWithdrawBillBatch")
    CommonResult<Boolean> insertAccWithdrawBillBatch(@RequestBody List<AccWithdrawBillDTO> accWithdrawBillDTO);

    /**
     * 提现对账单 检查数据库中是否已经存在对应日期和商户号的数据
     * @param dataFormatted
     * @param altNo
     */
    @GetMapping(PREFIX + "/countWithdrawBillsByDateAndAltNo")
    int countWithdrawBillsByDateAndAltNo(@RequestParam("dataFormatted")String dataFormatted,@RequestParam("altNo") String altNo);

    /**
     * 删除提现对账单数据
     * @param dataFormatted
     * @param altNo
     */
    @GetMapping(PREFIX + "/deleteWithdrawBillsByDateAndAltNo")
    void deleteWithdrawBillsByDateAndAltNo(@RequestParam("dataFormatted") String dataFormatted,@RequestParam("altNo") String altNo);

}
