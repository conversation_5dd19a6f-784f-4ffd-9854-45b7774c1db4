package com.zksr.account.model.pay.dto.refund;

import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/7 9:39
 */
@Data
@Accessors(chain = true)
public class PayRefundRespDTO {

    /**
     * 退款状态
     *
     * 枚举 {@link com.zksr.common.core.enums.PayRefundStatusEnum}
     */
    @ApiModelProperty("退款状态,0-未退款,1-发起退款成功,2-退款成功,3-退款失败")
    private Integer status;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 售后单号
     */
    @ApiModelProperty("售后退款单号")
    private String refundNo;

    /**
     * 发起退款单号
     */
    @ApiModelProperty("发起退款单号")
    private String refundTradeNo;

    /**
     * 外部退款号
     */
    @ApiModelProperty("商户退款编号")
    private String outRefundNo;

    /**
     * 调用渠道报错时，错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMsg;

    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型")
    private Integer orderType;

    /**
     * 结算失败数据
     * 用于记录没有分账成功的数据, 使用离线分账处理
     */
    @ApiModelProperty(value = "结算失败分账数据")
    private List<SettleDTO> failSettle = new ArrayList<>();

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SettleDTO {

        @ApiModelProperty("进件方账户")
        private String accountNo;

        @ApiModelProperty("分账金额")
        private BigDecimal amt;
    }
}
