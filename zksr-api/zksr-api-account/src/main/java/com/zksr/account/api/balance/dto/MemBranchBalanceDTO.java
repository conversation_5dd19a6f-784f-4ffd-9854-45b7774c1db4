package com.zksr.account.api.balance.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 门店-余额管理参数
 */
@ApiModel("门店-余额管理参数Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class MemBranchBalanceDTO extends PageParam {

    private static final long serialVersionUID = -3862958416392362426L;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("门店ID")
    private Long branchId;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "交易单号")
    private String tradeNo;

    @ApiModelProperty(value = "操作金额")
    private BigDecimal opAmt;

    @ApiModelProperty(value = "源交易单号")
    private String sourceTradeNo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "操作前金额")
    private BigDecimal beforeAmt;

    @ApiModelProperty(value = "操作后金额")
    private BigDecimal afterAmt;

    @ApiModelProperty(value = "操作人")
    private String operUserName;

}
