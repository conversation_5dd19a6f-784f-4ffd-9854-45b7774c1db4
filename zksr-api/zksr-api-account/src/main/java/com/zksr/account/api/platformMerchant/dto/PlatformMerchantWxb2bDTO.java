package com.zksr.account.api.platformMerchant.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信B2B商户配置信息
 * @date 2024/9/26 14:18
 */

@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlatformMerchantWxb2bDTO {

    /** $column.columnComment */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long wxB2bPlatformMerchantId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 最小账户保留金 */
    @Excel(name = "最小账户保留金")
    private BigDecimal minWithdrawAmt;

    /** 0-未开启,1-已开启 */
    @Excel(name = "0-未开启,1-已开启")
    private Integer autoWithdraw;

    /** 支付秘钥 */
    @Excel(name = "支付秘钥")
    private String appKey;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 支付秘钥 */
    @Excel(name = "支付秘钥")
    private String profile;

    /** 微信昵称 */
    @Excel(name = "微信昵称")
    private String nickName;
}
