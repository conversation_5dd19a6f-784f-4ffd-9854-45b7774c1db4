package com.zksr.account.model.transfer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 交易计算结果
 * @date 2024/3/11 14:15
 */
@Data
@Accessors(chain = true)
public class TransferSettleRespDTO {
    /**
     * 交易状态 {@link com.zksr.common.core.enums.TransferStatusEnum}
     */
    @ApiModelProperty("交易状态")
    private Integer status;

    @ApiModelProperty(value = "交易信息", notes = "在交易失败时返回")
    private String msg;

    @ApiModelProperty("提现单号")
    private String withdrawNo;

    @ApiModelProperty(value = "第三方系统支付流水号", notes = "回调返回")
    private String outTradeNo;

    @ApiModelProperty(value = "结算银行账号", notes = "xxxxxx******xxxx")
    private String accountNo;

    @ApiModelProperty(value = "结算账户名称", notes = "xxxxxx******xxxx")
    private String accountName;

    @ApiModelProperty(value = "结算手续费", notes = "回调返回")
    private BigDecimal settleFee;
}
