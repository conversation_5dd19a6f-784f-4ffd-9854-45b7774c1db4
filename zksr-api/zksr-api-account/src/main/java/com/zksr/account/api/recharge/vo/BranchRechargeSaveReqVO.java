package com.zksr.account.api.recharge.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店创建充值单
 * @date 2024/3/23 15:19
 */
@Data
@ApiModel(description = "门店创建充值单")
public class BranchRechargeSaveReqVO {

    /** 充值金额 */
    @ApiModelProperty(value = "充值金额", required = true)
    @DecimalMin(value = "0.02", message = "至少充值0.02元")
    private BigDecimal rechargeAmt;

    /** 门店充值单 */
    @ApiModelProperty(value = "门店ID", required = true, hidden = true)
    private Long branchId;
}
