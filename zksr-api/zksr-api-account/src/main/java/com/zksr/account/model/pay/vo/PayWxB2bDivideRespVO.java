package com.zksr.account.model.pay.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 微信B2B支付分账结果
 * @date 2024/9/21 14:22
 */
@Data
@ApiModel(description = "微信B2B支付分账结果")
public class PayWxB2bDivideRespVO {

    @ApiModelProperty(value = "1-处理中,2-处理成功,3-处理失败")
    private Integer status = 0;

    @ApiModelProperty("如果是失败, 失败原因是什么")
    private String msg;

    public static PayWxB2bDivideRespVO success() {
        PayWxB2bDivideRespVO respVO = new PayWxB2bDivideRespVO();
        respVO.setStatus(2);
        return respVO;
    }

    public static PayWxB2bDivideRespVO processing() {
        PayWxB2bDivideRespVO respVO = new PayWxB2bDivideRespVO();
        respVO.setStatus(1);
        return respVO;
    }

    public static PayWxB2bDivideRespVO fail(String msg) {
        PayWxB2bDivideRespVO respVO = new PayWxB2bDivideRespVO();
        respVO.setStatus(3);
        respVO.setMsg(msg);
        return respVO;
    }
}
