package com.zksr.account.model.profitOrder.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
* @description: 分账结果
* @author: 陈永培
* @createtime: 2025/6/23 21:50
*/
@Data
@Accessors(chain = true)
public class DivideOrderRepDto {
    /**
     * 交易状态 {@link com.zksr.common.core.enums.TransferStatusEnum}
     */
    @ApiModelProperty("交易状态,0-未发起,1-发起成功,2-交易成功,3-交易失败,4-交易处理中,5-人工干预")
    private Integer status;

    @ApiModelProperty(value = "交易信息", notes = "在交易失败时返回")
    private String msg;

    @ApiModelProperty(value ="该交易在美的支付中的交易流水号", notes = "回调返回")
    private String profitNo;

    @ApiModelProperty(value = "商户系统内部的分账单号 - 对应我们的 分账流水 acc_divide_flow.divide_flow_id")
    private String outProfitNo;
    
    @ApiModelProperty(value = "原商户交易订单号 - 对应我们的 分账流水 acc_pay_flow.pay_flow_id")
    private String outTradeNo;
}
