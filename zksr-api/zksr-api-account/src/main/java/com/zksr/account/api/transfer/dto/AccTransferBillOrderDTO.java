package com.zksr.account.api.transfer.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.ExceptionType;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 交易对账单明细单对象 acc_transfer_bill_order
 * 
 * @date 2024-03-22
 */
@TableName(value = "acc_transfer_bill_order")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccTransferBillOrderDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 账单ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long billOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 交易账单id */
    @Excel(name = "交易账单id")
    private Long transferBillId;

    /** 交易时间 */
    @Excel(name = "交易时间")
    private Date transferTime;

    /** 平台支付金额 */
    @Excel(name = "平台支付金额")
    private BigDecimal platformPayAmt;

    /** 商户支付金额 */
    @Excel(name = "商户支付金额")
    private BigDecimal merchantPayAmt;

    /** 平台退款金额 */
    @Excel(name = "平台退款金额")
    private BigDecimal platformRefundAmt;

    /** 商户退款金额 */
    @Excel(name = "商户退款金额")
    private BigDecimal merchantRefundAmt;

    /** 平台支付手续费 */
    @Excel(name = "平台支付手续费")
    private BigDecimal platformPayFree;

    /** 商户支付手续费 */
    @Excel(name = "商户支付手续费")
    private BigDecimal merchantPayFree;

    /** 平台退款手续费 */
    @Excel(name = "平台退款手续费")
    private BigDecimal platformRefundFree;

    /** 商户退款手续费 */
    @Excel(name = "商户退款手续费")
    private BigDecimal merchantRefundFree;

    /** 平台交易单号 */
    @Excel(name = "平台交易单号")
    private String platformTradeNo;

    /** 商户交易单号 */
    @Excel(name = "商户交易单号")
    private String merchantTradeNo;

    /** 商户号 */
    @Excel(name = "商户号")
    private String altNo;

    /** 业务单号 */
    @Excel(name = "业务单号")
    private String busiTradeNo;

    /** 状态 */
    @Excel(name = "状态")
    private Integer state;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 支付平台 */
    @Excel(name = "支付平台")
    private String platform;

    @Excel(name = "订单状态")
    private Integer orderType;

    // 其他字段省略
    private List<ExceptionType> exceptionTypes;
}