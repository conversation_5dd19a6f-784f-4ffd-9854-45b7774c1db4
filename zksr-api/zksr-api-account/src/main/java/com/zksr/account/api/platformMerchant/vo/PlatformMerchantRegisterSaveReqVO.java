package com.zksr.account.api.platformMerchant.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户进件注册
 * @date 2024/7/12 11:13
 */
@ApiModel(description = "商户进件注册实体")
@Data
public class PlatformMerchantRegisterSaveReqVO {

    /** 分账方商户编号 */
    @Excel(name = "分账方商户编号")
    private String altMchNo;

    /** 支付平台进件单号 */
    @ApiModelProperty(value = "支付平台进件单号")
    private String thirdOrderNo;

    @ApiModelProperty(value = "支付平台")
    private String platform;

    /**
     * 子商户类型
     */
    @ApiModelProperty("子商户类型,0-个人, 1-个人工商, 2-企业")
    private String merchantType;

    /**
     * 结算卡类型
     */
    @ApiModelProperty("结算卡类型, 0-对私,1-对公")
    private String settleBankType;

    @ApiModelProperty(value = "分账方全称")
    private String altMchName;//分账方全称

    @ApiModelProperty(value = "业务联系人姓名")
    private String busiContactName;//业务联系人姓名

    @ApiModelProperty(value = "业务联系人手机")
    private String busiContactMobileNo;//业务联系人手机

    @ApiModelProperty(value = "法人手机号")
    private String phoneNo;//法人手机号

    @ApiModelProperty(value = "营业执照编号  分账方类型为个体工商户和企业类型必填，商户类型为个人则不填")
    private String licenseNo;//营业执照编号  分账方类型为个体工商户和企业类型必填，商户类型为个人则不填

    @ApiModelProperty(value = "法人姓名")
    private String legalPerson;//法人姓名

    @ApiModelProperty(value = "身份证号")
    private String idCardNo;//身份证号

    @ApiModelProperty(value = "银行账号  银行类型对公账户，银行账号则为企业的对公银行账号； 银行类型为借记卡，则为个人的借记（储蓄卡）银行卡号。")
    private String bankAccountNo;//银行账号  银行类型对公账户，银行账号则为企业的对公银行账号； 银行类型为借记卡，则为个人的借记（储蓄卡）银行卡号。

    @ApiModelProperty(value = "如果账户类型是 对公账户，则银行账户名称须与 企业全称（分账方全称）保持一致； 如果账户类型是 借记卡对私，则银行账户名称须为法人姓名（个人姓名）")
    private String bankAccountName;//如果账户类型是 对公账户，则银行账户名称须与 企业全称（分账方全称）保持一致； 如果账户类型是 借记卡，则银行账户名称须为法人姓名（个人姓名）

    @ApiModelProperty(value = "联行号必填")
    private String bankChannelNo;//联行号必填

    @ApiModelProperty(value = "邮箱地址")
    private String email;
}
