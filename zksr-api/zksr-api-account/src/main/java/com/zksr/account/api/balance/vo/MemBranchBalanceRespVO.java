package com.zksr.account.api.balance.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel("门店余额信息-Response VO")
@Data
public class MemBranchBalanceRespVO implements Serializable {

    private static final long serialVersionUID = 731267113044241992L;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("门店编号")
    private String branchNo;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "状态：1正常,0停用")
    private Integer status;

    @ApiModelProperty(value = "账户余额")
    private BigDecimal amt;

    @ApiModelProperty(value = "联系人")
    private String contactName;

    @ApiModelProperty(value = "门店用户")
    private String contactPhone;

    @ApiModelProperty(value = "交易单号")
    private String tradeNo;

}
