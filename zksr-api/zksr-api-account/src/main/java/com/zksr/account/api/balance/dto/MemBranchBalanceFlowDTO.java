package com.zksr.account.api.balance.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 门店-余额管理流水分页查询参数
 */
@ApiModel("门店-余额管理流水分页查询参数Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class MemBranchBalanceFlowDTO extends PageParam {

    private static final long serialVersionUID = -2969048705268368858L;

    @NotNull(message = "门店Id不能为空")
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("操作类型:0=充值,1=订单支付,2=取消订单,3=后台退款")
    private Integer actionType;

}
