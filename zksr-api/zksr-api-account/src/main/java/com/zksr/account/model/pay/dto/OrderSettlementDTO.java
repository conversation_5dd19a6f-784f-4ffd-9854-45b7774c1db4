package com.zksr.account.model.pay.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分账信息  - 把账单分开 - 订单上多个入驻商的分账信息（钱要进入哪些入驻商的账号）
 * @date 2024/3/7 17:16
 */
@Data
@AllArgsConstructor
@Builder
public class OrderSettlementDTO {

    @ApiModelProperty("进件方账户")
    private String accountNo;

    @ApiModelProperty("商户ID")
    private Long merchantId;

    /**
     * 商户类型 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty("商户类型")
    private String merchantType;

    @ApiModelProperty("分账金额")
    private BigDecimal amt;

    @ApiModelProperty("入驻商订单号")
    private String subOrderNo;

    @ApiModelProperty("入驻商支付金额")
    private BigDecimal subAmt;

    @ApiModelProperty(value = "支付ID", notes = "目的用于抽离支付")
    private Long flowId;
    public OrderSettlementDTO() {
    }
    public OrderSettlementDTO(String accountNo, BigDecimal amt) {
        this.accountNo = accountNo;
        this.amt = amt;
    }

    public OrderSettlementDTO(String accountNo, Long merchantId, String merchantType, BigDecimal amt) {
        this.accountNo = accountNo;
        this.merchantId = merchantId;
        this.merchantType = merchantType;
        this.amt = amt;
    }
}
