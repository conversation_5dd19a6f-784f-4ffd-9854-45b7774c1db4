package com.zksr.account.model.pay.vo;

import com.zksr.account.api.pay.dto.PayFlowDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 退款查询
 * @date 2024/8/15 8:46
 */
@Data
@ApiModel(description = "退款查询")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayRefundQueryVO {

    @ApiModelProperty(value = "payFlowId", notes = "支付流水记录")
    private Long payFlowId;

    @ApiModelProperty(value = "refundFlowId", notes = "退款流水记录")
    private Long refundFlowId;

    @ApiModelProperty("支付单号")
    private String tradeNo;

    @ApiModelProperty("退款单号")
    private String refundNo;

    @ApiModelProperty(value = "支付payFlow", hidden = true)
    private PayFlowDTO payFlowDTO;
}
