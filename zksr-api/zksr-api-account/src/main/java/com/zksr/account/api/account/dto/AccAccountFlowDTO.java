package com.zksr.account.api.account.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.business.AccountBusiTypeField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 账户流水数据传递
 * @date 2024/3/23 10:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "账户流水数据传递")
public class AccAccountFlowDTO {

    @ApiModelProperty(value = "账户流水ID")
    private Long accountFlowId;

    /**
     * 平台商id
     */
    @ApiModelProperty(required = true, value = "平台商id")
    private Long sysCode;

    /**
     * 增加余额
     */
    @ApiModelProperty(required = true, value = "增加余额")
    private BigDecimal busiWithdrawableAmt;

    /**
     * 冻结金额
     */
    @ApiModelProperty(required = true, value = "冻结金额")
    private BigDecimal busiFrozenAmt;

    /**
     * 授信金额
     */
    @ApiModelProperty(required = true, value = "授信金额")
    private BigDecimal busiCreditAmt;

    /**
     * 现在的可提现金额
     */
    @Excel(name = "现在的可提现金额")
    private BigDecimal nowWithdrawableAmt;

    /**
     * 业务类型(待枚举，根据busi_type找busi出处) {@link AccountBusiType}
     */
    @ApiModelProperty(required = true, value = "业务类型(待枚举，根据busi_type找busi出处)")
    private String busiType;

    /**
     * 业务id
     */
    @ApiModelProperty(required = true, value = "业务id")
    private Long busiId;

    /**
     * 影响字段 , 分割
     * {@link AccountBusiTypeField}
     */
    @ApiModelProperty(required = true, value = "影响字段")
    private String busiFields;


    /**
     * 支付平台(数据字典)
     * {@link com.zksr.common.core.enums.PayChannelEnum}
     */
    @ApiModelProperty(required = true, value = "支付平台(数据字典)")
    private String platform;

    /**
     * 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(required = true, value = "商户类型")
    private String merchantType;

    /**
     * 商户id 根据操作账户类型不同传入
     */
    @ApiModelProperty(required = true, value = "商户id")
    private Long merchantId;

    /**
     * 流水备注
     */
    @ApiModelProperty("流水备注")
    private String memo;

    /**
     * 业务单号
     */
    @ApiModelProperty("业务单号")
    private String busiNo;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("变动时间")
    private Date processTime;

    /**
     * 账户类型;0-储值账户 1-赠送余额
     */
    @Excel(name = "账户类型;0-储值账户 1-赠送余额")
    private Integer accountType;
}
