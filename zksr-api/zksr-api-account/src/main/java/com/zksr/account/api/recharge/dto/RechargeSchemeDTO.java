package com.zksr.account.api.recharge.dto;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.account.api.recharge.vo.RechargeSchemeContentVO;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 充值配置
 * @date 2025/2/11 14:55
 */
@Data
public class RechargeSchemeDTO {

    /** 充值方案id */
    @ApiModelProperty(value = "充值方案id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long rechargeSchemeId;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty(value = "生效开始时间")
    private Date startTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty(value = "生效结束时间")
    private Date endTime;

    @ApiModelProperty(value = "方案名称")
    private String schemeName;

    @ApiModelProperty(value = "充值金额，赠送金额")
    private List<RechargeSchemeContentVO> rules;

    public boolean validate() {
        if (Objects.isNull(this.startTime) && Objects.isNull(this.endTime)) {
            return true;
        }
        return this.getStartTime().getTime() < System.currentTimeMillis() && this.getEndTime().getTime() > System.currentTimeMillis();
    }

    /**
     * 计算套餐赠送金额
     * @param rechargeAmt   充值金额
     * @return  赠送金额
     */
    public RechargeSchemeContentVO buildGiveAmt(BigDecimal rechargeAmt) {
        for (RechargeSchemeContentVO rule : rules) {
            if (NumberUtil.equals(rule.getRechargeAmt(), rechargeAmt)) {
                return rule;
            }
        }
        return null;
    }
}
