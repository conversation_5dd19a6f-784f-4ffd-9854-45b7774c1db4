package com.zksr.account.api.pay;

import com.zksr.account.enums.ApiConstants;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.dto.refund.PayRefundRespDTO;
import com.zksr.account.model.pay.vo.*;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 支付接口
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/23 10:48
 */
@FeignClient(
        contextId = "remotePayApi",
        value = ApiConstants.NAME
)
public interface PayApi {

    /**
     * 发起支付请求
     * @param reqVO 支付请求
     * @return  支付请求结果
     */
    @PostMapping(ApiConstants.PREFIX + "/pay/order/submit")
    CommonResult<PayOrderRespDTO> submitPayOrder(@RequestBody PayOrderSubmitReqVO reqVO);

    /**
     * 创建退款单
     * @param reqVO 退款请求
     * @return  退款结果
     */
    @PostMapping(ApiConstants.PREFIX + "/pay/order/refund")
    CommonResult<PayRefundRespDTO> submitPayOrder(@RequestBody PayRefundOrderSubmitReqVO reqVO);

    /**
     * 支付回调
     * @param respDTO   支付回调结果
     * @return
     */
    @PostMapping(ApiConstants.PREFIX + "/pay/order/payCallBack")
    CommonResult<Boolean> payCallBack(@RequestBody PayOrderRespDTO respDTO);

    /**
     * 在线支付请求分账, 会判断分账方是否都已经请求分账, 如果都已经全部请求分账, 那么会自动完成分账
     * 此分账适用于, 使用在线分账模式, 订单款项直接结算到订单商户余额里面
     * @param divideReqVO   分账请求
     * @return  分账结果
     */
    @PostMapping(ApiConstants.PREFIX + "/pay/order/divide")
    CommonResult<CreateDivideRespVO> divide(@RequestBody CreateDivideReqVO divideReqVO);
    
    /**
     * 美的付分账
     */
    @PostMapping(ApiConstants.PREFIX + "/pay/order/mideaDivide")
	CommonResult<CreateDivideRespVO> mideaPayDivide(@RequestBody CreateDivideReqVO divideReqVO);
	
	/**
     * 查询订单支付状态
     * @param payOrderQueryVO   查询参数
     * @return  支付转提供
     */
    @PostMapping(ApiConstants.PREFIX + "/pay/order/queryOrder")
    CommonResult<PayOrderRespDTO> queryOrder(@RequestBody PayOrderQueryVO payOrderQueryVO);
}
