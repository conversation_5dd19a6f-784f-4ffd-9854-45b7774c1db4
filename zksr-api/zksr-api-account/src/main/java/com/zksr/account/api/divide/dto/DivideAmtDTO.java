package com.zksr.account.api.divide.dto;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)对象 acc_divide_flow
 * @date 2024/9/25 8:39
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DivideAmtDTO {

    /** 入驻商订单号 */
    @Excel(name = "入驻订单号")
    private String supplierOrderNo;
    
    /** 商户分账金额 */
    @Excel(name = "商户分账金额")
    private Map<String,BigDecimal> accountDivideAmt = new HashMap<>();

}
