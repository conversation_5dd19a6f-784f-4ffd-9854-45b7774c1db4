package com.zksr.account.api.withdraw.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店余额提现
 * @date 2025/2/21 11:16
 */
@Data
public class BranchWithdrawReqVO {

    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;

    @ApiModelProperty("提现金额")
    @DecimalMin(value = "0.01", message = "最小提现1分钱")
    private BigDecimal withdrawAmt;

    @ApiModelProperty("银行卡号")
    private String accountNo;

    @ApiModelProperty("账户名称")
    private String accountName;

    @ApiModelProperty("银行名称")
    private String bankName;

    @ApiModelProperty("申请备注")
    private String applyTip;
}
