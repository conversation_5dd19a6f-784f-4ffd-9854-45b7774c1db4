package com.zksr.account.model.pay.dto.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付回调以后, 处理支付成功回调以后回调
 * @date 2024/3/22 14:59
 */
@Data
@ApiModel(description = "支付回调处理结果")
public class PayOrderNotifyRespDTO<T> {
    @ApiModelProperty(value = "业务信息", notes = "用来处理, 例如支付回调事务处理完成以后, 再进行其他的操作")
    private String businessInfo;
    @ApiModelProperty(value = "回调是否处理成功", notes = "默认都是成功")
    private Boolean success = Boolean.TRUE;

    public static PayOrderNotifyRespDTO fail() {
        PayOrderNotifyRespDTO respDTO = new PayOrderNotifyRespDTO();
        respDTO.setSuccess(Boolean.FALSE);
        return respDTO;
    }

    public static PayOrderNotifyRespDTO success(String businessInfo) {
        PayOrderNotifyRespDTO respDTO = new PayOrderNotifyRespDTO();
        respDTO.setBusinessInfo(businessInfo);
        return respDTO;
    }
}
