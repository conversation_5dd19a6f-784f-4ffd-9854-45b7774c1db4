package com.zksr.account.api.balance.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 门店账户余额
 */
@Data
@ApiModel("门店账户余额-acc_account Response VO")
public class AccBalanceRespVO implements Serializable {

    private static final long serialVersionUID = 7263352242497748820L;

    @ApiModelProperty(value = "门店余额账户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long balanceId;

    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sysCode;

    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long branchId;

    @ApiModelProperty(value = "门店余额账户金额")
    private BigDecimal amt;
}
