package com.zksr.account.model.profitOrder.vo;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* @description: 分账信息
* @author: 陈永培
* @createtime: 2025/6/24 10:32
*/
@Data
@NoArgsConstructor  // 添加无参构造方法
@AllArgsConstructor // 可选：全参构造方法
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProfitOrderDetailDTO {
    
    @ApiModelProperty("收钱的人或商户的编号")
    private String userLoginName;
    
    @ApiModelProperty("用户类型，B=商户，C=个人")
    private String merchantType;
    
    @ApiModelProperty("分给这个人的钱，单位分")
    private BigDecimal payAmount;
    
    @ApiModelProperty("分账描述")
    private String desc;
    
    @ApiModelProperty("下级分账列表")
    private List<ProfitOrderDetailDTO> receivers;
    
}
