package com.zksr.account.api.balance.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;

/**
 * 门店账户余额流水VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class AccBalanceFlowRespVO implements Serializable {

    private static final long serialVersionUID = -7803601584979955593L;

    @ApiModelProperty(value = "余额流水id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long balanceFlowId;

    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "门店id")
    private Long branchId;

    @ApiModelProperty(value = "门店账户余额id")
    private Long balanceId;

    @ApiModelProperty(value = "操作类型:0=充值,1=订单支付,2=取消订单,3=后台退款")
    private Integer actionType;

    @ApiModelProperty(value = "操作类型:0=充值,1=订单支付,2=取消订单,3=后台退款")
    private String actionTypeDesc;

    @ApiModelProperty(value = "关联单号")
    private String referenceNo;

    @ApiModelProperty(value = "关联子单号")
    private String referenceSubNo;

    @ApiModelProperty(value = "操作金额")
    private BigDecimal opAmt;

    @ApiModelProperty(value = "操作前金额")
    private BigDecimal beforeAmt;

    @ApiModelProperty(value = "操作后金额")
    private BigDecimal afterAmt;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = TIMEZONE)
    private Date createTime;

    @ApiModelProperty(value = "更新者")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = TIMEZONE)
    private Date updateTime;

}
