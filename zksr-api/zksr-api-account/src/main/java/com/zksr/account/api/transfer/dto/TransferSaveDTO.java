package com.zksr.account.api.transfer.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 内部转账单实体
 * @date 2024/4/11 9:00
 */
@Data
@ApiModel(description = "内部转账单实体")
@Accessors(chain = true)
public class TransferSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 订单结算id */
    @ApiModelProperty(value = "订单结算id", required = true, notes = "订单结算表ID")
    @NotNull(message = "订单结算表ID不能为空")
    private Long settleId;

    /** 转出方账户id */
    @ApiModelProperty(value = "转出方账户id", required = true, notes = "注意这里是账户ID, 例如supplierId")
    @NotNull(message = "转出方账户id不能未空")
    private Long sourceMerchantId;

    /**
     * 转出方账户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty(value = "转出方账户类型", required = true)
    private String sourceMerchantType;

    /** 转入方账户id */
    @ApiModelProperty(value = "转入方账户id", required = true, notes = "转入方账户IDM, 例如 dcId")
    @NotNull(message = "转入方账户id不能未空")
    private Long targetMerchantId;

    /**
     * 转出方账户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty(value = "转入方账户类型", required = true)
    private String targetMerchantType;

    /** 转账单号 */
    @Excel(name = "转账单号")
    @ApiModelProperty(value = "转账单号", required = true, notes = "唯一转账单号")
    @NotNull(message = "转账单号唯一")
    @NotEmpty(message = "转账单号不能为空")
    private String transferNo;

    /** 转账金额 */
    @Excel(name = "转账金额")
    @ApiModelProperty(value = "转账金额")
    @DecimalMin(value = "0.01",message = "最低转账金额0.01")
    @NotNull(message = "转账金额不能为空")
    private BigDecimal transferAmt;

    /** 转账发起方解除金额 */
    @ApiModelProperty(value = "转账发起方解除金额")
    @DecimalMin(value = "0.00",message = "最低转账金额0.00")
    @NotNull(message = "解除金额不能为空")
    private BigDecimal settleAmt;

    /**
     * 支付平台(数据字典);从订单
     * 参见 {@link com.zksr.common.core.enums.PayChannelEnum}
     * */
    @NotEmpty(message = "支付平台不能为空")
    @ApiModelProperty(value = "支付平台")
    private String platform;
}
