package com.zksr.account.model.pay.vo;

import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/7 10:42
 */
@Data
@Accessors(chain = true)
public class PayRefundOrderSubmitReqVO {
    @ApiModelProperty(value = "业务ID", notes = "商场订单退款时需要传入")
    private Long busiId;

    @ApiModelProperty(value = "flowId", notes = "支付流水ID", hidden = true)
    private Long flowId;

    @ApiModelProperty(value = "订单编号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "子订单编号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "退款单号", required = true)
    private String refundNo;

    @ApiModelProperty(value = "退款金额", required = true)
    private BigDecimal refundAmt;

    @ApiModelProperty(value = "门店储值赠送余额承担金额", notes = "仅在储值支付时有效")
    private BigDecimal walletGiveAmt;

    @ApiModelProperty(value = "平台商ID", hidden = true)
    private Long sysCode;

    @ApiModelProperty(value = "订单类型", hidden = true)
    private Integer orderType;

    @ApiModelProperty(value = "使用订单号进行退款", hidden = true, notes = "新策略已经是使用flowId进件退款了, 此举为了兼容旧数据")
    private boolean useOrderNoRefund;

    @ApiModelProperty(value = "appid", hidden = true)
    private String appid;

    @ApiModelProperty(value = "支付payFlow", hidden = true)
    private PayFlowDTO payFlowDTO;

    @ApiModelProperty(value = "退款payFlow", hidden = true)
    private PayFlowDTO refundFlowDTO;

    @ApiModelProperty(value = "分账信息", notes = "如果有分账则需要传入", hidden = true)
    private List<OrderSettlementDTO> settlements;

    /**
     * 结构参考
     * {
     *     {@linkplain PayOrderSubmitExtras#MERCHANT_ID }       : "门店ID",
     *     {@linkplain PayOrderSubmitExtras#SUPPLIER_ID }       : "入驻商ID",
     *     {@linkplain PayOrderSubmitExtras#SUPPLIER_ORDER_NO } : "入驻商订单号",
     * }
     */
    @ApiModelProperty(value = "额外参数", required = true)
    private Map<String, String> extras = new HashMap<>();
}
