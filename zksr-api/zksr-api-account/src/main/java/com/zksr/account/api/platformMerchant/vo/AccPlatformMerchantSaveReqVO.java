package com.zksr.account.api.platformMerchant.vo;

import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 支付平台商户对象 acc_platform_merchant
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("支付平台商户 - acc_platform_merchant分页 Request VO")
public class AccPlatformMerchantSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 支付平台商户id */
    @ApiModelProperty(value = "支付平台(数据字典)")
    private Long platformMerchantId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id", required = true)
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id", required = true)
    private Long merchantId;

    /** 分账方商户编号 */
    @Excel(name = "分账方商户编号")
    @ApiModelProperty(value = "分账方商户编号", required = true)
    private String altMchNo;

    /** 分账方名称 */
    @Excel(name = "分账方名称")
    @ApiModelProperty(value = "分账方名称")
    private String altMchName;

    /** 0-停用  1-启用 */
    @Excel(name = "0-停用  1-启用")
    @ApiModelProperty(value = "0-停用  1-启用", required = true)
    private String mchStatus;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)", required = true)
    private String platform;

    /** 银行名称 */
    @ApiModelProperty(value = "银行名称", required = true, example = "浦发银行")
    private String bankName;

    /** 银行支行 */
    @ApiModelProperty(value = "银行支行", required = true, example = "浦发银行麓谷支行")
    private String bankBranch;

    /** 银行卡号 */
    @ApiModelProperty(value = "银行卡号", required = true, example = "***************")
    private String accountNo;

    /** 支付平台进件单号 */
    @ApiModelProperty(value = "支付平台进件单号", required = true, example = "***************")
    private String thirdOrderNo;
}
