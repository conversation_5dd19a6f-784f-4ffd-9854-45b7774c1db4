package com.zksr.account.api.account.vo;

import com.zksr.common.core.constant.StatusConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/13 15:08
 */
@Data
public class BranchBalanceRespVO {

    @ApiModelProperty("储值账户余额")
    private BigDecimal balance = BigDecimal.ZERO;

    @ApiModelProperty("赠送账户余额")
    private BigDecimal giveBalance = BigDecimal.ZERO;


    /**
     * 赠送账户余额占比
     * giveBalance / (balance + giveBalance) * 100
     * @return
     */
    public BigDecimal getBiveBalanceRate () {
        if (balance.add(giveBalance).compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return giveBalance.divide(
                        balance.add(giveBalance), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP
                ).multiply(BigDecimal.valueOf(100))
                .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
    }

    /**
     * 储值账户余额占比
     * 100 - 赠送账户余额占比
     * @return
     */
    public BigDecimal getBalanceRate () {
        return BigDecimal.valueOf(100).subtract(getBiveBalanceRate());
    }

    /**
     * 获取当前储值总金额
     * @return
     */
    public BigDecimal getTotalBalanceAmt() {
        return balance.add(giveBalance);
    }
}
