package com.zksr.account.api.platformMerchant.vo;

import com.zksr.account.enums.MerchantCredentialType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户进件注册
 * @date 2024/7/12 11:13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "商户进件注册实体")
public class PlatformMerchantUploadSaveRespVO {

    /**
     * {@link com.zksr.common.core.enums.MerchantUploadPicStateEnum}
     */
    @ApiModelProperty("进件状态: SUCCESS-成功, DOING-处理中, FAIL-失败")
    private String auditStatus;

    @ApiModelProperty("进件商户批次单号")
    private String orderNo;

    @ApiModelProperty("进件商户号")
    private String merchantNo;

    @ApiModelProperty("错误信息")
    private String msg;

}
