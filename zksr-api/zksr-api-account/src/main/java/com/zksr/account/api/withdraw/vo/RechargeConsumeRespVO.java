package com.zksr.account.api.withdraw.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.business.AccountBusiType;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/13 14:17
 */
@Data
public class RechargeConsumeRespVO {


    /**
     * 增加余额
     */
    @ApiModelProperty(required = true, value = "合计增加余额")
    private BigDecimal busiWithdrawableAmt;

    /**
     * 冻结金额
     */
    @ApiModelProperty(required = true, value = "合计冻结金额")
    private BigDecimal busiFrozenAmt;

    /**
     * 赠送金额
     */
    @ApiModelProperty(required = true, value = "赠送金额")
    private BigDecimal giveAmt;

    /**
     * 变动后本金账户余额
     */
    @ApiModelProperty(required = true, value = "变动后本金账户余额")
    private BigDecimal baseAccountBalance;

    /**
     * 变动后增金账户余额
     */
    @ApiModelProperty(required = true, value = "变动后增金账户余额")
    private BigDecimal giveAccountBalance;

    /**
     * 流水备注
     */
    @ApiModelProperty("流水备注")
    private String memo;

    /**
     * 业务单号
     */
    @ApiModelProperty("业务单号")
    private String busiNo;

    /**
     * 业务类型(待枚举，根据busi_type找busi出处) {@link AccountBusiType}
     */
    @ApiModelProperty("业务类型, 参考字典account_flow_busi_type")
    private String busiType;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("变动时间")
    private Date processTime;

    /**
     * 账户类型;0-储值账户 1-赠送余额
     */
    @Excel(name = "账户类型;0-储值账户 1-赠送余额")
    @ApiModelProperty("账户类型;0-储值账户 1-赠送余额")
    private Integer accountType;

    /**
     * 现在的可提现金额
     */
    @Excel(name = "现在的可提现金额")
    @ApiModelProperty("现在的可提现金额")
    private BigDecimal nowWithdrawableAmt;

    /**
     * 业务id
     */
    @Excel(name = "业务id")
    @ApiModelProperty("业务id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long busiId;

    /**
     * 门店ID
     */
    @Excel(name = "门店ID")
    @ApiModelProperty("门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty("门店账户名称")
    private String branchName;
}
