package com.zksr.account.api.recharge;

import com.zksr.account.api.recharge.dto.RechargeSchemeDTO;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveReqVO;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveRespVO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2025/2/11
 * @desc    充值api
 */
@FeignClient(
        contextId = "remoteRechargeApi",
        value = ApiConstants.NAME
)
public interface RechargeApi {

    String PREFIX = ApiConstants.PREFIX + "/recharge";

    /**
     * 获取城市充值配置
     * @param areaId    城市区域ID
     * @return  配置集合
     */
    @GetMapping(PREFIX + "/getAreaRechargeSchemeList")
    CommonResult<List<RechargeSchemeDTO>> getAreaRechargeSchemeList(@RequestParam("areaId") Long areaId);

    /**
     * 创建门店充值单
     * @param reqVO 门店充值请求
     * @return  充值结果
     */
    @PostMapping(PREFIX + "/createBranchRecharge")
    CommonResult<BranchRechargeSaveRespVO> createBranchRecharge(@RequestBody BranchRechargeSaveReqVO reqVO);

}
