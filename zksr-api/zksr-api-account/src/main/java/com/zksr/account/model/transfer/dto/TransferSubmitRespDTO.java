package com.zksr.account.model.transfer.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 转账返回结果
 * @date 2024/3/11 14:15
 */
@Data
@Accessors(chain = true)
public class TransferSubmitRespDTO {
    /**
     * 交易状态 {@link com.zksr.common.core.enums.TransferStatusEnum}
     */
    @ApiModelProperty("交易状态,0-未发起,1-发起成功,2-交易成功,3-交易失败,4-交易处理中,5-人工干预")
    private Integer status;

    @ApiModelProperty(value = "交易信息", notes = "在交易失败时返回")
    private String msg;

    @ApiModelProperty("交易单号")
    private String transferNo;

    @ApiModelProperty(value = "第三方系统支付流水号", notes = "回调返回")
    private String outTradeNo;
}
