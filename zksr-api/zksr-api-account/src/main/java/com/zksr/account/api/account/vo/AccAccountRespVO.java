package com.zksr.account.api.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;

import static com.zksr.common.core.utils.DateUtils.*;


/**
 * 账户对象 acc_account
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@Data
@ApiModel("账户 - acc_account Response VO")
public class AccAccountRespVO {
    private static final long serialVersionUID = 1L;

    /** 账户id */
    @ApiModelProperty(value = "支付平台(数据字典);当merchant_type=branch时，无此值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long accountId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 账户类型;0-储值账户 1-赠送余额 */
    @Excel(name = "账户类型;0-储值账户 1-赠送余额")
    @ApiModelProperty(value = "账户类型;0-储值账户 1-赠送余额")
    private Integer accountType;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id")
    private Long merchantId;

    /** 可提现金额 */
    @Excel(name = "可提现金额")
    @ApiModelProperty(value = "可提现金额")
    private BigDecimal withdrawableAmt;

    /** 冻结金额 */
    @Excel(name = "冻结金额")
    @ApiModelProperty(value = "冻结金额")
    private BigDecimal frozenAmt;

    /** 授信额度 */
    @Excel(name = "授信额度")
    @ApiModelProperty(value = "授信额度")
    private BigDecimal creditAmt;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private Long version;

    /** 账务持有方类型;supplier-入驻商 partner-平台商 */
    @Excel(name = "账务持有方类型;supplier-入驻商 partner-平台商")
    @ApiModelProperty(value = "账务持有方类型;supplier-入驻商 partner-平台商")
    private String holdMerchantType;

    /** 账务持有方id */
    @Excel(name = "账务持有方id")
    @ApiModelProperty(value = "账务持有方id")
    private String holdMerchantId;

    /** 支付平台(数据字典);当merchant_type=branch时，无此值 */
    @Excel(name = "支付平台(数据字典);当merchant_type=branch时，无此值")
    @ApiModelProperty(value = "支付平台(数据字典);当merchant_type=branch时，无此值")
    private String platform;

    /**
     * 提现费率
     */
    @ApiModelProperty("提现费率, 需要/100")
    private String withdrawalFeeRate;

    /**
     * 最小提现金额
     */
    @ApiModelProperty("最小提现金额")
    private String minimumWithdrawalAmount;
}
