package com.zksr.account.api.account;

import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.account.vo.BranchBalanceRespVO;
import com.zksr.account.api.account.vo.ColonelAccountRespVO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/23
 * @desc
 */
@FeignClient(
        contextId = "remoteAccountApi",
        value = ApiConstants.NAME
)
public interface AccountApi {

    String PREFIX = ApiConstants.PREFIX + "/account";

    /**
     * 获取入驻商账户信息
     * @param supplierId
     * @return
     */
    @PostMapping(PREFIX + "/getSupplierAccount")
    CommonResult<AccAccountDTO> getSupplierAccount(@RequestParam("supplierId") Long supplierId);

    /**
     * 获取账户信息
     * @param sysCode       平台商ID
     * @param merchantId    商户ID
     * @param merchantType   商户类型   {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param holdMerchantId 财务持有方ID  默认null
     * @return  返回带有支付平台进件信息的账户
     */
    @PostMapping(PREFIX + "/getAccountAndPlatformAccount")
    CommonResult<AccAccountDTO> getAccountAndPlatformAccount(
            @ApiParam(name = "sysCode", value = "平台编号") @RequestParam("sysCode") Long sysCode,
            @ApiParam(name = "merchantId", value = "商户ID") @RequestParam("merchantId") Long merchantId,
            @ApiParam(name = "merchantType", value = "商户类型") @RequestParam("merchantType") String merchantType,
            @ApiParam(name = "holdMerchantId", value = "账户持有方") @RequestParam("holdMerchantId") Long holdMerchantId);

    /**
     * 获取账户信息
     * @param sysCode       平台商ID
     * @param merchantId    商户ID
     * @param merchantType   商户类型   {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @return
     */
    @PostMapping(PREFIX + "/getAccountList")
    CommonResult<List<AccAccountDTO>> getAccountList(
            @ApiParam(name = "sysCode", value = "平台编号") @RequestParam("sysCode") Long sysCode,
            @ApiParam(name = "merchantId", value = "商户ID") @RequestParam("merchantId") Long merchantId,
            @ApiParam(name = "merchantType", value = "商户类型") @RequestParam("merchantType") String merchantType);

    /**
     * 获取账户信息, 适用于非门店账户查询
     * @param sysCode       平台商ID
     * @param merchantId    商户ID
     * @param merchantType   商户类型   {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @return
     */
    @GetMapping(PREFIX + "/getAccount")
    CommonResult<AccAccountDTO> getAccount(
            @ApiParam(name = "sysCode", value = "平台编号") @RequestParam("sysCode") Long sysCode,
            @ApiParam(name = "merchantId", value = "商户ID") @RequestParam("merchantId") Long merchantId,
            @ApiParam(name = "merchantType", value = "商户类型") @RequestParam("merchantType") String merchantType);


    /**
     * 获取账户
     * @param merchantId    商户ID
     * @param merchantType  商户类型    {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform      支付平台    {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return  账户资金情况
     */
    @GetMapping(PREFIX + "/getAccount02")
    CommonResult<AccAccountDTO> getAccount(@RequestParam("merchantId") Long merchantId, @RequestParam("merchantType") String merchantType, @RequestParam("platform") String platform);

    /**
     * 保存账户流水
     * @param flowList
     * @return 返回保存流水对应的下标ID
     */
    @PostMapping(PREFIX + "/saveAccountFlow")
    CommonResult<List<Long>> saveAccountFlow(@RequestBody List<AccAccountFlowDTO> flowList);

    /**
     * 保存账户流水并且执行流水 **异步**
     * @param flowList
     * @return 返回保存流水对应的下标ID
     */
    @PostMapping(PREFIX + "/saveAccountFlowAndProcess")
    CommonResult<List<Long>> saveAccountFlowAndProcess(@RequestBody List<AccAccountFlowDTO> flowList);

    /**
     * 保存账户流水并且执行流水 **同步**
     * @param flowList
     * @return 返回保存流水对应的下标ID
     */
    @PostMapping(PREFIX + "/saveAccountFlowAndProcessSync")
    CommonResult<List<Long>> saveAccountFlowAndProcessSync(@RequestBody List<AccAccountFlowDTO> flowList);

    /**
     * 处理账户流水 (**异步**结算结算)
     * @param flowIdList 流水记录ID集合
     * @return 返回保存流水对应的下标ID
     */
    @PostMapping(PREFIX + "/processFlow")
    CommonResult<Boolean> processFlow(@RequestBody List<Long> flowIdList);

    /**
     * 处理账户流水 (**同步**结算结算)
     * @param flowIdList 流水记录ID集合
     * @return 返回保存流水对应的下标ID
     */
    @PostMapping(PREFIX + "/processFlowSync")
    CommonResult<Boolean> processFlowSync(@RequestBody List<Long> flowIdList) throws Exception;

    /**
     * 获取业务员账户
     * @param colonelId
     * @return
     */
    @GetMapping(PREFIX + "/getColonelAccount")
    CommonResult<ColonelAccountRespVO> getColonelAccount(@RequestParam("colonelId") Long colonelId);


    /**
     * 获取账户信息
     * @return accountDTO 查询条件
     */
    @PostMapping(PREFIX + "/getAccountListByReq")
    CommonResult<List<AccAccountDTO>> getAccountListByReq(@RequestBody AccAccountDTO accountDTO);


    /**
     * 获取门店储值账户
     */
    @PostMapping(PREFIX + "/getBranchBalance")
    CommonResult<BranchBalanceRespVO> getBranchBalance(@RequestParam("branchId") Long branchId);
}
