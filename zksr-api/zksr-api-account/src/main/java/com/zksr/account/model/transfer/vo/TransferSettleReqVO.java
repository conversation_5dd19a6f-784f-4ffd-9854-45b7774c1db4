package com.zksr.account.model.transfer.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 交易结算 (提现)
 * @date 2024/3/11 14:07
 */
@Data
public class TransferSettleReqVO {

    @ApiModelProperty(value = "交易单号", required = true)
    private String transferNo;

    @ApiModelProperty(value = "提现单号", hidden = true, notes = "提现流水自动生成")
    private String withdrawNo;

    @ApiModelProperty(value = "业务ID", required = true)
    private Long busiId;

    @ApiModelProperty(value = "平台商ID", required = true)
    private Long sysCode;

    @ApiModelProperty(value = "平台进件账户编号", required = true)
    private String customerNo;

    @ApiModelProperty(value = "商户名称", required = true)
    private String altMchName;

    @ApiModelProperty(value = "账户-商户", required = true)
    private String accountNo;

    /** 预计导致银行卡号;从平台方分账方查询接口获取 */
    @ApiModelProperty(value = "预计到账银行卡号")
    private String bankAccountNo;

    /** 预计导致银行卡号;从平台方分账方查询接口获取 */
    @ApiModelProperty(value = "预计到账银行卡户名")
    private String bankAccountName;

    /** 预计到账银行 */
    @ApiModelProperty(value = "预计到账银行")
    private String bankName;

    /** 结算卡类型 */
    @ApiModelProperty(value = "结算卡类型", notes = "TOPRIVATE-对私,TOPUBLIC-对公")
    private String bankType;

    @ApiModelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    @ApiModelProperty(value = "交易金额/单位:元", required = true, notes = "最低5元")
    private BigDecimal amount;

    @ApiModelProperty(value = "支付平台", required = true, notes = "hlb-合利宝,mock-模拟支付")
    private String platform;

    @ApiModelProperty(value = "交易备注")
    private String tips;

    /**
     * 无需转账适用于, 账户结算类型为自动结算, 只需要确保账户金额转过去了就行了
     */
    @Excel(name = "转账方式;0-平台商转提现方 1-平台商转软件商再转提现方 2-无需转账")
    @ApiModelProperty(value = "转账方式;0-平台商转提现方 1-平台商转软件商再转提现方 2-无需转账")
    private String transferType;
}
