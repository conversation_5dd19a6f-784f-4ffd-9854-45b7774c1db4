package com.zksr.account.api.divide.dto;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * @description 分账发票
 * @date 2024/9/25 8:39
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DivideInvoiceDTO {


    /** 入驻商单号 */
    @ApiModelProperty(value = "入驻商单号", required = true)
    private String supplierOrderNo;
    
    @ApiModelProperty(value = "商户ID", required = true)
    private Long merchantId;
    
    /**
     * 商户类型
     * 参见 {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型", required = true)
    private String merchantType;
    
    
    /** 支付类型 0-支付 1-退款 */
    @Excel(name = "支付类型 0-支付 1-退款")
    private Integer payType;
}
