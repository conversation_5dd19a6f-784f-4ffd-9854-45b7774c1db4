package com.zksr.account.api.platformMerchant.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 支付平台商户对象 acc_platform_merchant
 *
 * <AUTHOR>
 * @date 2024-03-22
 */
@ApiModel("支付平台商户 - acc_platform_merchant分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AccPlatformMerchantPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 支付平台商户id */
    @ApiModelProperty(value = "支付平台(数据字典)")
    private Long platformMerchantId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @Excel(name = "商户类型", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户id")
    private Long merchantId;

    /** 商户id */
    @Excel(name = "商户id")
    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    /** 分账方商户编号 */
    @Excel(name = "分账方商户编号")
    @ApiModelProperty(value = "分账方商户编号")
    private String altMchNo;

    /** 分账方名称 */
    @Excel(name = "分账方名称")
    @ApiModelProperty(value = "分账方名称")
    private String altMchName;

    /** 0-停用  1-启用 */
    @Excel(name = "0-停用  1-启用")
    @ApiModelProperty(value = "0-停用  1-启用")
    private String mchStatus;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    @ApiModelProperty(value = "支付平台(数据字典)", required = true)
    private String platform;

    /** 图片审核状态: SUCCESS-成功, DOING-处理中, FAIL-失败 */
    @ApiModelProperty("图片审核状态: SUCCESS-成功, DOING-处理中, FAIL-失败")
    private String picStatus;

    /** 审核状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过 */
    @ApiModelProperty("审核状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过")
    private String auditStatus;

    /** 变更状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过 */
    @ApiModelProperty("变更状态: INIT-待审核, OVERRULE-驳回, AUDITED-审核通过")
    private String editStatus;

    @ApiModelProperty(value = "进件开始时间:yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date startTime;

    @ApiModelProperty(value = "进件结束时间格式:yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date endTime;

    @ApiModelProperty(value = "商户集合")
    private List<String> merchantTypes;
}
