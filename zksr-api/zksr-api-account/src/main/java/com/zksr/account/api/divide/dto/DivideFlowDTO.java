package com.zksr.account.api.divide.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分账流水(应付支付和分账分开的支付平台，目前仅B2B）支付)对象 acc_divide_flow
 * @date 2024/9/25 8:39
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DivideFlowDTO {

    /** 分账流水id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long divideFlowId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 支付平台(数据字典) */
    @Excel(name = "支付平台(数据字典)")
    private String platform;

    /** 1-请求分账  2-请求分账回退 3-分账完成 */
    @Excel(name = "1-请求分账  2-请求分账回退 3-分账完成")
    private Integer type;

    /** 订单号 */
    @Excel(name = "订单号")
    @ApiModelProperty(value = "订单号")
    private String tradeNo;

    /** 子订单号 */
    @Excel(name = "子订单号")
    private String subTradeNo;

    /** 支付平台商户订单号 */
    @Excel(name = "支付平台商户订单号")
    private String outTradeNo;

    /** 支付平台商户退款单号 */
    @Excel(name = "支付平台商户退款单号")
    private String outRefundNo;

    /** 支付平台分账发起方商户id */
    @Excel(name = "支付平台分账发起方商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sourcePlatformMerchantId;

    /** 支付平台分账接收方商户id */
    @Excel(name = "支付平台分账接收方商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long targetPlatformMerchantId;

    /** 子商户id */
    @Excel(name = "子商户id")
    private String mchid;

    /** 分账接收方类型 */
    @Excel(name = "分账接收方类型")
    private String payeeType;

    /** 分账接收方账号 */
    @Excel(name = "分账接收方账号")
    private String payeeNo;

    /** 分账金额 */
    @Excel(name = "分账金额")
    private BigDecimal divideAmt;

    /** 分账请求发起状态, 0-成功, 1-失败 */
    @Excel(name = "分账请求发起状态, 0-成功, 1-失败")
    @ApiModelProperty(name = "分账请求发起状态, 0-成功, 1-失败")
    private Integer divideReqStatus;

    /** 仅type=1, 1: 分账中 2: 分账完成 3：分账失败 */
    @Excel(name = "仅type=1, 1: 分账中 2: 分账完成 3：分账失败")
    private Integer divideStatus;

    /** 仅type=2, 1: 分账退回中 2: 分账退回完成 3: 分账退回失败 */
    @Excel(name = "仅type=2, 1: 分账退回中 2: 分账退回完成 3: 分账退回失败")
    private Integer divideReverseStatus;

    /** 错误信息(仅失败记录) */
    @Excel(name = "错误信息(仅失败记录)")
    private String errMsg;

    /** 支付流水表ID */
    @Excel(name = "支付流水表ID")
    @ApiModelProperty(value = "支付流水表ID")
    private Long accPayFlowId;
}
