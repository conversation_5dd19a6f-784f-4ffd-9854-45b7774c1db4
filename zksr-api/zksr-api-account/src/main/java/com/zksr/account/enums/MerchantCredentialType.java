package com.zksr.account.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户资质类型
 * @date 2024/7/15 15:37
 */
@Getter
public enum MerchantCredentialType {
    /**
     *
     */
    UNIFIED_CODE_CERTIFICATE("三证合一营业执照", 0),

    ORG_CERTIFICATE("组织机构证", 1),

    TAX_REG_CERTIFICATE("税务登记证", 2),

    BUSINESS_LICENSE("营业执照", 3),

    PERMIT_FOR_BANK_ACCOUNT("开户许可证", 4),

    SIGN_BOARD("门头照", 5),

    SIGN_BOARD_ACTIVITY("门头照_活动报名", 45),

    FRONT_OF_ID_CARD("身份证正面", 6),

    BACK_OF_ID_CARD("身份证反面", 7),

    HANDHELD_OF_ID_CARD("手持身份证照", 9),

    HANDHELD_OF_BANK_CARD("手持银行卡照", 10),

    ACCOUNT_OPENING_CERTIFICATE("银行开户证明", 11),

    CHECKOUT_COUNTER("收银台照", 12),

    INTERIOR_PHOTO("室内照", 13),

    INTERIOR_PHOTO_ACTIVITY("室内照_活动报名", 44),

    ATTACHMENTINFO_1("附件一", 14),

    ATTACHMENTINFO_2("附件二", 15),

    ATTACHMENTINFO_3("附件三", 16),

    ATTACHMENTINFO_4("附件四", 17),

    ATTACHMENTINFO_5("附件五", 18),

    BANK_CARD("结算卡", 19),

    SETTLE_FRONT_OF_ID_CARD("结算人身份证正面", 20),

    SETTLE_BACK_OF_ID_CARD("结算人身份证反面", 21),

    ACTIVITY_CHECKOUT_COUNTER("绿洲活动收银台照", 22),

    SUBLEASE_CERTIFICATE("转租证明", 23),

    CONTRACT("电子签章合同", 24),

    FINANCE_ROOM("财务室",25),

    INSTITUTIONAL_ORGANIZATION_PIC("法人登记证书",26),

    RUN_SCHOOL_LICENSE_PIC("办学资质图片",27),

    CHARGE_SAMPLE("收费样本",28),

    PRIVATE_NONENTERPRISE_UNITS("民办非企业单位登记证书图片",29),

    DIPLOMATIC_NOTE("照会",30),

    CERTIFICATE_FILE("证明文件图片",31),

    MEDICAL_INSTRUMENT_PRACTICE_LICENSE_PIC("医疗执业许可证图片",32),

    UNIONPAY_CHECKOUT_COUNTER("银联标识收银台照",33),

    UNIONPAY_SIGNBOARD("银联标识门头照",34),

    UNIONPAY_AGREEMENT("加盟协议",35),

    UNIONPAY_TERMINAL("银联轻型终端照",36),

    UNIONPAY_STUFF("银联物料回传照片",37),

    UNIONPAY_DEVICE("银联语音报备设备照片",38),

    AUTHORIZATION_FOR_SETTLEMENT("结算账户指定书", 39),

    HANDHELD_OF_BANK_CARD_BACK("手持银行卡照反面",40),

    ORGANIZATION_FOUND("党组织成立文件",41),

    ACTIVITY_RATE_COMMITMENT("优惠费率承诺函照",42),
    EID_HEAD_IMG("头像照",43),

    FOOD_SERVICE_LIC("餐饮服务许可证照", 46),
    FOOD_HEALTH_LIC("食品卫生许可证照", 47),
    FOOD_BUSINESS_LIC("食品经营许可证照", 48),
    FOOD_CIRCULATE_LIC("食品流通许可证照", 49),
    FOOD_PRODUCTION_LIC("食品生产许可证照", 50),
    TOBACCO_LIC("烟草专卖零售许可证照", 51),
    BUSINESS_CERTIFICATE("经营许可证照(一般)", 52),
    VOUCHER_IMG("凭证",53),
    MERCHANT_ENTERPRISE_MEALS_COOPERATION("商户与企业团餐合作协议(支付宝)", 54),
    BANK_COOPERATION_AGREEMENT("机构银行合作协议照", 55),

    LINKMAN_FRONT_OF_ID_CARD("联系人证件正面照片", 56),

    LINKMAN_BACK_OF_ID_CARD("联系人证件反面照片", 57),

    BUSINESS_AUTHORIZATION_LETTER("业务办理授权函", 58),

    LEGALPERSON_AUTHORIZE_LETTER("法定代表人说明函", 59),

    BENEFLEGALPERSON_FRONT_OF_ID_CARD("受益人证件正面照片", 60),

    BENEFLEGALPERSON_BACK_OF_ID_CARD("受益人证件反面照片", 61),

    ALIPAY_LEGALPERSON_AUTHORIZE_LETTER("支付宝法人授权函", 64),

    AFFILIATED_AUTHORIZE_LETTER("商户挂靠授权函", 66),

    PARKING_CERTIFICATE_FILE("停车场资质证明图片", 67);

    ;

    private final String desc;
    private final Integer index;

    MerchantCredentialType(String desc, Integer index) {
        this.desc = desc;
        this.index = index;
    }

    public static MerchantCredentialType parseValue(String code) {
        for (MerchantCredentialType value : values()) {
            if (value.name().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
