package com.zksr.account.api.divide;

import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.divide.dto.AccDivideDtlDTO;
import com.zksr.account.api.divide.dto.DivideAmtDTO;
import com.zksr.account.api.divide.dto.DivideFlowDTO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/9/25
 * @desc    分账流水异步查询处理结果
 */
@FeignClient(
        contextId = "remoteDivideFlowApi",
        value = ApiConstants.NAME
)
public interface DivideFlowApi {

    String PREFIX = ApiConstants.PREFIX + "/divideFlow";

    /**
     * 获取查询最新结果数据
     * @param minId 查询最小起始ID
     * @return 返回待结算流水对应的下标ID, 每批次500条
     */
    @PostMapping(PREFIX + "/getTryDivideFlow")
    CommonResult<List<DivideFlowDTO>> getTryDivideFlow(@RequestParam("minId") Long minId);

    /**
     * 更新分账流水处理状态
     * @param itemList
     */
    @PostMapping(PREFIX + "/updateDivideStatus")
    void updateDivideStatus(@RequestBody List<DivideFlowDTO> itemList);

    /**
     * 创建分账流水
     */
    @PostMapping(PREFIX + "/createAdvideFlow")
    CommonResult<Boolean> createAdvideFlow(@RequestBody DivideAmtDTO divideAmtDTO);
    
    
    
    
    
    
    
    
    
    
    /**
     * 根据分账流水ID获取分账明细
     * @param dto
     * @return
     */
    @PostMapping(PREFIX + "/getAccDivideDtlById")
    CommonResult<AccDivideDtlDTO> getAccDivideDtlById(@RequestBody AccDivideDtlDTO dto);
}
