package com.zksr.account.api.platformMerchant.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户修改状态返回结果
 * @date 2024/7/12 11:13
 */
@ApiModel(description = "商户修改状态返回结果")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlatformMerchantUpdateStatusRespVO {

    /**
     * {@link com.zksr.common.core.enums.MerchantRegisterStateEnum}
     */
    @ApiModelProperty("修改状态: INIT-待审核, OVERRULE-审核失败/驳回, AUDITED-审核通过")
    private String auditStatus;

    @ApiModelProperty("修改批次单号")
    private String orderNo;

    @ApiModelProperty("进件商户号")
    private String merchantNo;

    @ApiModelProperty("错误信息")
    private String msg;
}
