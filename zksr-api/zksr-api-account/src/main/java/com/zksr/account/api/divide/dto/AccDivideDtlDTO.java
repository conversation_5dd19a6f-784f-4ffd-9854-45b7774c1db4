package com.zksr.account.api.divide.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分账数据 (目前主要用于记录入驻商分账)
 * @date 2024/8/16 15:29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccDivideDtlDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 支付分账详情id */
    @ApiModelProperty(value = "订单类型 0-商城订单  1-入驻商充值  2-门店充值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long divideDtlId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    /** 商户id */
    @ApiModelProperty(value = "商户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    /** 分账方商户编号 */
    @ApiModelProperty(value = "分账方商户编号")
    private String altMchNo;

    /** 分账方名称 */
    @ApiModelProperty(value = "分账方名称")
    private String altMchName;

    /** 订单金额 */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmt;

    /** 分账金额 */
    @ApiModelProperty(value = "分账金额")
    private BigDecimal altAmt;

    /** 支付手续费率 */
    @ApiModelProperty(value = "支付手续费率")
    private BigDecimal feeRate;

    /** 支付手续费 */
    @ApiModelProperty(value = "支付手续费")
    private BigDecimal fee;

    /** 支付平台(数据字典) */
    @ApiModelProperty(value = "支付平台(数据字典)")
    private String platform;

    /** 支付流水id */
    @ApiModelProperty(value = "支付平台(数据字典)")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long payFlowId;

    /** 0-线上分账  1-线下分账 */
    @ApiModelProperty(value = "0-线上分账  1-线下分账")
    private Integer onlineOrOffline;

    /** 线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账 */
    @ApiModelProperty(value = "线上分账状态(数据字典);0-未分账 1-已分账 2-分账中 3-分账失败 4-不分账")
    private Integer onlineDivideState;

    /** 线下处理状态;0-未处理 1-已处理 2-无需处理 针对分账失败或者不分账的订单的补偿措施 */
    @ApiModelProperty(value = "线下处理状态;0-未处理 1-已处理 2-无需处理 针对分账失败或者不分账的订单的补偿措施")
    private Integer offlineProState;

    /** 线下处理单号 */
    @ApiModelProperty(value = "线下处理单号")
    private String offlineProNo;

    /** 分账完成时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "分账完成时间")
    private Date divideTime;

    /** 支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号 */
    @ApiModelProperty(value = "支付订单号;商城订单就是商城的订单号，入驻商充值就是入驻商单号，门店充值就是门店充值单号")
    private String tradeNo;

    /** 支付平台商户订单号 */
    @ApiModelProperty(value = "支付平台商户订单号")
    private String outTradeNo;

    /** 退款单号;商城退款即是售后单号 */
    @ApiModelProperty(value = "退款单号;商城退款即是售后单号")
    private String refundNo;

    /** 支付平台商户退款单号 */
    @ApiModelProperty(value = "支付平台商户退款单号")
    private String outRefundNo;

    /** 支付类型 0-支付 1-退款 */
    @ApiModelProperty(value = "支付类型 0-支付 1-退款")
    private Integer payType;

    /** 第三方支付平台分账流水号 */
    @ApiModelProperty(value = "第三方支付平台分账流水号")
    private String platformDivideFlowNo;

    /** 订单类型 0-商城订单  1-入驻商充值  2-门店充值 3-货到付款支付*/
    @ApiModelProperty(value = "订单类型 0-商城订单  1-入驻商充值  2-门店充值 3-货到付款支付")
    private Integer orderType;
}
