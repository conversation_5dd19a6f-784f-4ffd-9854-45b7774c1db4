package com.zksr.account.api.balance;

import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.account.api.balance.vo.AccBalanceRespVO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(
        contextId = "remoteBalanceApi",
        value = ApiConstants.NAME
)
public interface BalanceApi {

    String PREFIX = ApiConstants.PREFIX + "/balance";

    // 跟据门店编码批量获取
    @PostMapping(PREFIX + "/getBalanceInfoList")
    CommonResult<List<AccBalanceRespVO>> getBalanceInfoList(@RequestBody List<Long> branchIdList);

    // 门店余额充值,返回交易流水id
    @PostMapping(PREFIX + "/recharge")
    CommonResult<String> recharge(@RequestBody MemBranchBalanceDTO balanceDTO);

    // 门店余额退款,返回交易流水id
    @PostMapping(PREFIX + "/refund")
    CommonResult<String> refund(@RequestBody MemBranchBalanceDTO balanceDTO);

    // 门店余额订单支付,返回交易流水id
    @PostMapping(PREFIX + "/orderPay")
    CommonResult<String> orderPay(@RequestBody MemBranchBalanceDTO balanceDTO);

    // 订单取消，回退支付金额
    @PostMapping(PREFIX + "/OrderPayCancel")
    CommonResult<String> OrderPayCancel(@RequestBody MemBranchBalanceDTO balanceDTO);
}
