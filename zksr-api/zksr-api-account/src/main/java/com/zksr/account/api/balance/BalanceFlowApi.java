package com.zksr.account.api.balance;

import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.api.balance.vo.AccBalanceFlowRespVO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        contextId = "remoteBalanceFlowApi",
        value = ApiConstants.NAME
)
public interface BalanceFlowApi {

    String PREFIX = ApiConstants.PREFIX + "/balanceFlow";

    // 跟据门店编码分页查询
    @PostMapping(PREFIX + "/getFlowPageList")
    CommonResult<PageResult<AccBalanceFlowRespVO>> getFlowPageList(@RequestBody MemBranchBalanceFlowDTO pageReqDTO);

    // 通过流水id回滚库存
    @GetMapping(PREFIX + "/rollBackByFlowId")
    CommonResult<Boolean> rollBackByFlowId(@RequestParam(value = "balanceFlowId") String balanceFlowId);
}
