package com.zksr.account.api.platformMerchant.vo;

import com.zksr.account.enums.MerchantCredentialType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商户进件注册
 * @date 2024/7/12 11:13
 */
@Data
@ApiModel(description = "商户资质上传实体")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PlatformMerchantUploadSaveReqVO {

    @ApiModelProperty(value = "商户号", notes = "进件注册审核以后才有", hidden = true)
    private String altMchNo;

    @ApiModelProperty(value = "进件商户订单号", notes = "商户内部编号", hidden = true)
    private String orderNo;

    @ApiModelProperty(value = "资质类型")
    private MerchantCredentialType credentialType;

    @ApiModelProperty(value = "资质全url")
    private String credentialUrl;

}
