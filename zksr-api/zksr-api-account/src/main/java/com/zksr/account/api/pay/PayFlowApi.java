package com.zksr.account.api.pay;

import com.zksr.account.api.pay.dto.PayFlowDTO;
import com.zksr.account.enums.ApiConstants;
import com.zksr.account.model.pay.vo.*;
import com.zksr.common.core.domain.vo.openapi.AccPayReceiptReqDTO;
import com.zksr.common.core.domain.vo.openapi.ReceiptPayOpenDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 支付流水接口
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/23 10:48
 */
@FeignClient(
        contextId = "remotePayFlowApi",
        value = ApiConstants.NAME
)
public interface PayFlowApi {

    String PREFIX = ApiConstants.PREFIX + "/pay/payFlow";

    /**
     * 查询微信B2B支付退款单重试列表
     * @param minId
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getWxB2bRefundProcessAgainList")
    CommonResult<List<PayRefundQueryVO>> getWxB2bRefundProcessAgainList(@RequestParam("minId") Long minId);

    /**
     * 重试微信退款单状态
     * @param refundQueryVO
     * @return
     */
    @PostMapping(ApiConstants.PREFIX + "/tryAgainB2bRefundProcess")
    CommonResult<Boolean> tryAgainB2bRefundProcess(@RequestBody PayRefundQueryVO refundQueryVO);

    /**
     * @param reqDTO
     * @return
     * @Description: 根据（销售/售后退款单集合/货到付款单集合）订单获取订单支付集合信息
     */
    @PostMapping(value =PREFIX + "/getOrderPayInfoBySyncOrder")
    CommonResult<List<ReceiptPayOpenDTO>> getOrderPayInfoListBySyncOrder(@RequestBody AccPayReceiptReqDTO reqDTO);


    /**
     * 根据支付流水id 获得支付流水详情(订单)
     * @param payFlowId
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getAccPayFlowOrder")
    CommonResult<PayFlowDTO> getAccPayFlowOrder(@RequestParam("payFlowId") Long payFlowId);

    /**
     * 根据支付流水id 获得支付流水详情(退款)
     * @param refundNo
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getAccPayFlowAfter")
    CommonResult<PayFlowDTO> getAccPayFlowAfter(@RequestParam("refundNo") String refundNo);

    /**
     * 获取支付成功流水
     * @param tradeNo
     * @return
     */
    @GetMapping(PREFIX + "/getPaySuccessFlow")
    CommonResult<PayFlowDTO> getPaySuccessFlow(@RequestParam("tradeNo") String tradeNo);

    @GetMapping(PREFIX + "/getByOrderPayFlow")
    CommonResult<PayFlowDTO> getByOrderPayFlow(@RequestParam("orderNo") String orderNo);

    @PostMapping(PREFIX + "/getByOrdersPayFlow")
    CommonResult<List<PayFlowDTO>> getByOrdersPayFlow(@RequestBody List<String> orderNos);

    /**
     * 获取微信B2B充值分润待分账流水
     * @param minPayFlowId  最小流水号
     * @return
     */
    @GetMapping(PREFIX + "/getWxB2bRechargeDivideFlow")
    CommonResult<List<PayFlowDTO>> getWxB2bRechargeDivideFlow(@RequestParam("minPayFlowId") Long minPayFlowId);
}
