package com.zksr.account.model.pay.dto.order;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.domain.PayCallBack;
import com.zksr.common.core.enums.PayOrderStatusRespEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发起支付返回结果
 * @date 2024/3/7 9:38
 */
@Data
@Accessors(chain = true)
public class PayOrderRespDTO {
    /**
     * 支付状态
     *
     * 枚举：{@link PayOrderStatusRespEnum}
     */
    @ApiModelProperty(value = "支付状态 0-未支付,1-支付发起成功,2-支付成功(针对储值和模拟支付方式),3-支付失败,4-已退款,5-支付关闭", notes = "0-未支付,1-支付发起成功,2-支付成功(针对储值和模拟支付方式),3-支付失败,4-已退款,5-支付关闭")
    private Integer status;

    /**
     * 返回处理信息
     */
    @ApiModelProperty("返回处理信息, status=3 返回原因")
    private String message;

    /**
     * flowId
     */
    @ApiModelProperty(value = "flowId", notes = "实际发起支付号")
    private Long flowId;

    /**
     * 订单号
     */
    @ApiModelProperty(value = "支付订单", notes = "订单返回")
    private String orderNo;

    /**
     * 外部订单号, 第三方系统支付流水号
     */
    @ApiModelProperty(value = "第三方系统支付流水号", notes = "回调返回")
    private String outTradeNo;

    /**
     * 支付成功时间
     */
    @ApiModelProperty(value = "支付成功时间")
    private Date successTime;

    /**
     * 支付平台标识 com.zksr.account.client.PayClient#getPlatform()
     */
    @ApiModelProperty("支付平台标识 hlb-合利宝 wallet-钱包(余额) mock-模拟支付")
    private String payPlatform;

    /**
     * 支付方式
     */
    @Excel(name = "支付方式, 0-在线支付 1-储值支付, 2-模拟支付")
    private String payWay;

    /**
     * 有效返回的数据
     */
    @ApiModelProperty(value = "有效返回的数据")
    private Object rawData;

    /**
     * 订单类型
     */
    @ApiModelProperty(value = "订单类型")
    private Integer orderType;

    /**
     * 结算失败数据
     * 用于记录没有分账成功的数据, 使用离线分账处理
     */
    @ApiModelProperty(value = "结算失败分账数据")
    private List<SettleDTO> failSettle = new ArrayList<>();
    
    @ApiModelProperty(value = "子订单的分账信息 - 暂时 只有【美的付】 有")
    private List<SubPayOrderRespDTO> subPayOrderRespDtos = new ArrayList<>();

    /**
     * 支付发起信息
     */
    @ApiModelProperty(value = "订单类型")
    private PayInfoDTO payInfoDTO;
    
    /**
     * 美的付回调
     */
    private PayCallBack callBack;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SettleDTO {

        @ApiModelProperty("进件方账户")
        private String accountNo;

        @ApiModelProperty("分账金额")
        private BigDecimal amt;
    }
    
    
    @Data
    @Builder
    public static class SubPayOrderRespDTO extends PayOrderRespDTO{
        public SubPayOrderRespDTO() {
            super(); // 调用父类（PayOrderRespDTO）的无参构造函数
        }
    }
}
