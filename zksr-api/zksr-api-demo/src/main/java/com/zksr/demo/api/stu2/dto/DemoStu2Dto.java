package com.zksr.demo.api.stu2.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

@ApiModel("学生信息2 DTO对象")
public class DemoStu2Dto {

    /** 用户ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long stuId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 学生姓名 */
    @ApiModelProperty(value = "学生姓名")
    private String stuName;

    /** 手机号码 */
    @ApiModelProperty(value = "手机号码")
    private String phone;

    /** 登录日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "登录日期", example = "2024-01-01 00:00:00")
    private Date stuDate;

    /** 状态（0正常 1停用） */
    @ApiModelProperty(value = "状态 0=正常,1=停用")
    private Integer status;

    public Long getStuId() {
        return stuId;
    }

    public void setStuId(Long stuId) {
        this.stuId = stuId;
    }

    public Long getSysCode() {
        return sysCode;
    }

    public void setSysCode(Long sysCode) {
        this.sysCode = sysCode;
    }

    public String getStuName() {
        return stuName;
    }

    public void setStuName(String stuName) {
        this.stuName = stuName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Date getStuDate() {
        return stuDate;
    }

    public void setStuDate(Date stuDate) {
        this.stuDate = stuDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
