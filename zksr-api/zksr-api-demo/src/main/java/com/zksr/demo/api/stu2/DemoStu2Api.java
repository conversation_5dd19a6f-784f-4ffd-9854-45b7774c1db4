package com.zksr.demo.api.stu2;

import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.web.domain.AjaxResultBase;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.demo.api.stu2.dto.DemoStu2Dto;
import com.zksr.demo.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(
        contextId = "remoteDemoStu2Api",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface DemoStu2Api {

    String PREFIX = ApiConstants.PREFIX + "/stu2";

    @GetMapping(PREFIX + "/{stuId}")
    public CommonResult<DemoStu2Dto> getStu2Info(@PathVariable("stuId") Long stuId);
}
