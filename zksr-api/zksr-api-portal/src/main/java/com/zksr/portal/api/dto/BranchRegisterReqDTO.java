package com.zksr.portal.api.dto;

//import com.zksr.common.core.annotation.Excel;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
//@ApiModel("register用户注册 接口请求")
public class BranchRegisterReqDTO {

    /** 平台商id */
    private Long sysCode;

//    @ApiModelProperty(value = "sysSource  必填")
//    @NotBlank(message = "sysSource不能为空")
    private String sysSource;

    //验证码  非必填
    /** 验证码 */
//    @ApiModelProperty(value = "验证码  非必填")
    private String validateCode;

    //用户名  memberName 必填
    /** 用户名 */
//    @ApiModelProperty(value = "用户名  必填")
//    @NotBlank(message = "用户名不能为空")
    private String memberName;

    /**
     * 门店编码
     */
//    @ApiModelProperty(value = "门店编码")
    private String branchNo;

    //门店名称 branch_name 必填
    /** 门店名称 */
//    @ApiModelProperty(value = "门店名称  必填")
//    @NotBlank(message = "门店名称不能为空")
    private String branchName;

    //区域城市 必填
    /** 区域城市 */
//    @ApiModelProperty(value = "区域城市ID  必填")
    private Long areaId;

    //门店地址 branch_addr 必填
    /** 门店地址 */
//    @ApiModelProperty(value = "门店地址  必填")
//    @NotBlank(message = "门店地址不能为空")
    private String branchAddr;

    //经度 longitude 必填
    /** 经度 */
//    @ApiModelProperty(value = "经度  必填")
    private BigDecimal longitude;

    //纬度 latitude 必填
    /** 纬度 */
//    @ApiModelProperty(value = "纬度  必填")
    private BigDecimal latitude;

    /** 用户账号 */
//    @ApiModelProperty(value = "用户账号")
//    @NotBlank(message = "用户账号不能为空")
    private String userName;

    /** 用户密码 */
//    @ApiModelProperty(value = "用户密码")
//    @NotBlank(message = "用户密码不能为空")
    private String password;

    /** 门头照 */
//    @ApiModelProperty(value = "门头照")
    private String branchImages;

    /** 备注 */
//    @ApiModelProperty(value = "备注")
    private String memo;

//    @ApiModelProperty("三级区域城市ID, 省市区关联")
    private Long threeAreaCityId;

//    @ApiModelProperty(value = "推广业务员ID")
    private Long colonelId;

//    @ApiModelProperty(value = "渠道id")
    private Long channelId;

    /** 省份 */
//    @Excel(name = "省份")
//    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
//    @Excel(name = "城市")
//    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
//    @Excel(name = "区县")
//    @ApiModelProperty(value = "区县")
    private String districtName;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;
}
