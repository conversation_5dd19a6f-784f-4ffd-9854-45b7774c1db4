package com.zksr.promotion.api.activity.dto;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 特价活动规则对象 prm_sp_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@ToString
public class SpRuleDTO {
    private static final long serialVersionUID=1L;

    /** 特价活动规则id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 特价活动id */
    @Excel(name = "特价活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** sku_id */
    @Excel(name = "sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 促销价 */
    @Excel(name = "促销价")
    private BigDecimal spPrice;

    /** 中单位促销价 */
    @Excel(name = "中单位促销价")
    private BigDecimal midSpPrice;

    /** 大单位促销价 */
    @Excel(name = "大单位促销价")
    private BigDecimal largeSpPrice;

    /** 单次限购数量 */
    @Excel(name = "单次限购数量")
    private Integer onceBuyLimit;

    /** 总限量 */
    @Excel(name = "总限量")
    private Integer totalLimitQty;

    /** 状态 1-启用 0-停用 */
    @Excel(name = "状态 1-启用 0-停用")
    private Integer status;

    /** 中单位限购数量 */
    @Excel(name = "中单位限购数量")
    @ApiModelProperty(value = "中单位限购数量")
    private Integer midLimit;

    /** 大单位限购数量 */
    @Excel(name = "大单位限购数量")
    @ApiModelProperty(value = "大单位限购数量")
    private Integer largeLimit;

    /** 中单位总限量 */
    @Excel(name = "中单位总限量")
    private Integer midTotalLimitQty;

    /** 大单位总限量 */
    @Excel(name = "大单位总限量")
    private Integer largeTotalLimitQty;

    /**
     * 根据大中小单位获取不同的特价价格
     * @param unitType
     * @return 对应单位的特价价格
     */
    public BigDecimal getSpPriceByUnitType(Integer unitType){
        // 中单位
        if (unitType == UnitTypeEnum.UNIT_MIDDLE.getType().intValue())    return midSpPrice;
        // 大单位
        if (unitType == UnitTypeEnum.UNIT_LARGE.getType().intValue())  return largeSpPrice;
        // 小单位
        return spPrice;
    }

    public boolean containsUnit(ActivityVerifyItemDTO activityVerifyItemDTO) {
        if (UnitTypeEnum.S(activityVerifyItemDTO.getUnitSize()) && Objects.nonNull(this.spPrice) && NumberUtil.isGreater(this.spPrice, BigDecimal.ZERO)) {
            return true;
        }
        if (UnitTypeEnum.M(activityVerifyItemDTO.getUnitSize()) && Objects.nonNull(this.midSpPrice) && NumberUtil.isGreater(this.midSpPrice, BigDecimal.ZERO)) {
            return true;
        }
        if (UnitTypeEnum.L(activityVerifyItemDTO.getUnitSize()) && Objects.nonNull(this.largeSpPrice) && NumberUtil.isGreater(this.largeSpPrice, BigDecimal.ZERO)) {
            return true;
        }
        return false;
    }
}
