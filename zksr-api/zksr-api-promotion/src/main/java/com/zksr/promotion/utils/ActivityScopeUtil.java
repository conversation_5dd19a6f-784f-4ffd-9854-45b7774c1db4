package com.zksr.promotion.utils;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.ActivitySpuScopeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销商品参与范围验证工具
 * @date 2024/12/3 9:09
 */
public class ActivityScopeUtil {

    public static boolean validateSpuScope(ActivitySpuScopeValidVO scopeValidVO, List<ActivitySpuScopeDTO> activitySpuScopeList) {
        // 没有限定范围
        if (ObjectUtil.isEmpty(activitySpuScopeList)) {
            return true;
        }
        // 需要判断限定范围
        Map<Long, List<ActivitySpuScopeDTO>> applyTypeMap = activitySpuScopeList.stream().collect(Collectors.groupingBy(ActivitySpuScopeDTO::getApplyType));
        for (Long applyType : applyTypeMap.keySet()) {
            List<ActivitySpuScopeDTO> spuScopeDTOS = applyTypeMap.get(applyType);
            Set<Long> applyIds = spuScopeDTOS.stream().map(ActivitySpuScopeDTO::getApplyId).filter(Objects::nonNull).collect(Collectors.toSet());
            Integer whiteOrBlack = spuScopeDTOS.get(0).getWhiteOrBlack();

            Long applyId = null;
            switch (ActivitySpuScopeEnum.formValue(applyType)) {
                case SUPPLIER:
                    applyId = scopeValidVO.getSupplierId();
                    break;
                case CLASS:
                    applyId = scopeValidVO.getCategoryId();
                    break;
                case BRAND:
                    applyId = scopeValidVO.getBrandId();
                    break;
                case SKU:
                    applyId = scopeValidVO.getSkuId();
                    break;
                default:
                    applyId = NumberPool.LOWER_GROUND_LONG;
                    break;
            }
            // 范围ID都没有, 直接 false
            if (Objects.isNull(applyId)) {
                return false;
            }
            // 1-白名单 0-黑名单
            if (whiteOrBlack == NumberPool.INT_ONE) {
                // 白名单不包含
                if (!applyIds.contains(applyId)) {
                    return false;
                }
            } else {
                // 黑名单包含
                if (applyIds.contains(applyId)) {
                    return false;
                }
            }
        }
        return true;
    }

    @Data
    @ApiModel(description = "促销商品参与范围验证")
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ActivitySpuScopeValidVO {
        private Long supplierId;
        private Long categoryId;
        private Long brandId;
        private Long skuId;

        public static ActivitySpuScopeValidVO build(ActivityVerifyItemDTO itemDTO) {
            ActivitySpuScopeValidVO validVO = new ActivitySpuScopeValidVO();
            validVO.supplierId = itemDTO.getSupplierId();
            validVO.categoryId = itemDTO.getCategoryId();
            validVO.brandId = itemDTO.getBrandId();
            validVO.skuId = itemDTO.getSkuId();
            return validVO;
        }
    }
}
