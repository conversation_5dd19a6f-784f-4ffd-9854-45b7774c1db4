package com.zksr.promotion.api.coupon.handler.applySpuScope;

import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.enums.CouponSpuScopeEnum;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.promotion.api.coupon.handler.CouponSpuScopeStrategy;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 全场优惠券具体策略
 * @date 2024/4/3 16:03
 */
public class QcCouponSpuScopeStrategy implements CouponSpuScopeStrategy {

    @Override
    public void productFilter(List<OrderValidItemDTO> items, Set<CouponDTO> coupons, Map<Long, CouponTemplateDTO> couponTemplateMap, Set<CouponDTO> availableSet) {
        // 优惠券按照入驻商分组
        Map<Long, List<OrderValidItemDTO>> supplierMap = items.stream().collect(Collectors.groupingBy(OrderValidItemDTO::getSupplierId));
        // 开始筛选符合要求的优惠券
        coupons.stream().filter(item -> CouponSpuScopeEnum.ALL.getScope().equals(item.getSpuScope()) && Objects.nonNull(item.getSupplierId())).forEach(coupon -> {
            CouponTemplateDTO couponTemplate = couponTemplateMap.get(coupon.getCouponId());
            // 商品是否有包含当前优惠券入驻商
            if (!supplierMap.containsKey(couponTemplate.getSupplierId())) {
                return;
            }
            // 入驻商金额是否达到要求
            BigDecimal supplierAmt = supplierMap.get(couponTemplate.getSupplierId()).stream().map(OrderValidItemDTO::getAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 全场券, 是否达到启用金额
            if (NumberUtil.isGreaterOrEqual(supplierAmt, couponTemplate.getTriggerAmt())) {
                availableSet.add(coupon);
            }
        });
        coupons.removeAll(availableSet);
    }
}
