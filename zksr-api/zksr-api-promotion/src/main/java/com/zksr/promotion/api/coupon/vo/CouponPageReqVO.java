package com.zksr.promotion.api.coupon.vo;

import com.zksr.common.core.enums.CouponStateEnum;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 我的优惠券列表 - request VO
 * @date 2024/4/2 16:17
 */
@Data
@ApiModel(description = "优惠券分页请求对象")
public class CouponPageReqVO extends PageParam {
    @ApiModelProperty(value = "门店ID", hidden = true)
    private Long branchId;

    /**
     * 参见 {@link CouponStateEnum}
     */
    @ApiModelProperty(value = "优惠券状态 1-未使用 2-已使用 3-已过期")
    private Integer state;

    @ApiModelProperty(value = "优惠券批次ID")
    private Long couponBatchId;

}
