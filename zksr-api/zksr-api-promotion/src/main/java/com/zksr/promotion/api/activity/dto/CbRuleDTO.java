package com.zksr.promotion.api.activity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 组合商品上架规则
 * @date 2024/12/28 14:12
 */
@Data
@ApiModel(description = "组合商品规格ID")
public class CbRuleDTO {

    @ApiModelProperty("组合商品规则ID")
    private Long cbRuleId;

    @ApiModelProperty("组合促销商品ID")
    private Long spuCombineId;

    @ApiModelProperty("活动ID")
    private Long activityId;

    @ApiModelProperty("上架上级分类ID")
    private Long shelfClassId;
}
