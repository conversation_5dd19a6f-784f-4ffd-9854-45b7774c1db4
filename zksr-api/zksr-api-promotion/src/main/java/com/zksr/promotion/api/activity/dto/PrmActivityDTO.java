package com.zksr.promotion.api.activity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024年05月21日 10:19
 * @description: PrmActivityDTO
 */
@Data
@ApiModel(description = "促销活动 - 单个促销")
public class PrmActivityDTO extends ActivityDTO{
    /**
     * 指定城市范围, 城市是必填的
     */
    @ApiModelProperty(value = "指定城市范围")
    private Set<ActivityCityScopeDTO> cityScopes;

    @ApiModelProperty(value = "指定门店范围")
    private Set<ActivityBranchScopeDTO> branchScopes;

    @ApiModelProperty(value = "指定渠道范围")
    private Set<ActivityChannelScopeDTO> channelScopes;

    @ApiModelProperty(value = "指定范围展示分类ID,品牌ID,SKU_ID")
    private Set<Long> spuScopes;
}
