package com.zksr.promotion.api.activity;

import com.zksr.promotion.api.activity.dto.*;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活动促销
 * @date 2024/5/18 8:49
 */

@FeignClient(
        contextId = "remoteActivityApi",
        value = ApiConstants.NAME
)
public interface ActivityApi {

    String PREFIX = ApiConstants.PREFIX + "/activity";

    @GetMapping(ApiConstants.PREFIX + "/getSupplierActivity")
    CommonResult<List<SupplierActivityDTO>> getSupplierActivity(@RequestParam("supplierId") Long supplierId);

    @GetMapping(ApiConstants.PREFIX + "/getActivitySpRule")
    CommonResult<List<SpRuleDTO>> getActivitySpRule(@RequestParam("activityId") Long activityId);

    @GetMapping(ApiConstants.PREFIX + "/getActivitySkRule")
    CommonResult<List<SkRuleDTO>> getActivitySkRule(@RequestParam("activityId") Long activityId);

    @GetMapping(ApiConstants.PREFIX + "/getActivitySpuScopeList")
    CommonResult<List<ActivitySpuScopeDTO>> getActivitySpuScopeList(@RequestParam("activityId") Long activityId);

    @GetMapping(ApiConstants.PREFIX + "/getActivityBranchScopeList")
    CommonResult<List<ActivityBranchScopeDTO>> getActivityBranchScopeList(@RequestParam("activityId") Long activityId);

    @GetMapping(ApiConstants.PREFIX + "/getActivityCityScopeList")
    CommonResult<List<ActivityCityScopeDTO>> getActivityCityScopeList(@RequestParam("activityId") Long activityId);

    @GetMapping(ApiConstants.PREFIX + "/getActivityChannelScopeList")
    CommonResult<List<ActivityChannelScopeDTO>> getActivityChannelScopeList(@RequestParam("activityId") Long activityId);

    /**
     * 获取活动信息(包括活动使用范围)
     * @param activityId 活动ID
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getActivityDto")
    CommonResult<PrmActivityDTO> getActivityDto(@RequestParam("activityId") Long activityId);

    /**
     * 获取满减促销规则
     * @param activityId 活动ID
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getActivityFdRule")
    CommonResult<List<FdRuleDTO>> getActivityFdRule(@RequestParam("activityId") Long activityId);

    /**
     * 获取满赠促销规则
     * @param activityId 活动ID
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getActivityFgRule")
    CommonResult<List<FgRuleDTO>> getActivityFgRule(@RequestParam("activityId") Long activityId);

    /**
     * 获取买赠促销规则
     * @param activityId 活动ID
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getActivityBgRule")
    CommonResult<List<BgRuleDTO>> getActivityBgRule(@RequestParam("activityId") Long activityId);

    /**
     * 获取组合商品促销规则
     * @param activityId 活动ID
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getActivityCbRule")
    CommonResult<List<CbRuleDTO>> getActivityCbRule(@RequestParam("activityId") Long activityId);

    @GetMapping(ApiConstants.PREFIX + "/getSeckillSpuIds")
    CommonResult<List<Long>> getSeckillSpuIds(@RequestParam("prmNo") String prmNo, @RequestParam("prmNo") Integer funcScope);

    @GetMapping(ApiConstants.PREFIX + "/getSeckillSkuIds")
    CommonResult<List<Long>> getSeckillSkuIds(@RequestParam("prmNo") String prmNo, @RequestParam("prmNo") Integer funcScope);

    /**
     * 获取 sku 绑定的进行中或者未开始的活动
     * @param skuId skuId
     * @return  活动role总数
     */
    @GetMapping(ApiConstants.PREFIX + "/countBySkuId")
    CommonResult<Long> countBySkuId(@RequestParam("skuId") Long skuId);

    /**
     * 获取城市, 有在搞组合促销的入驻商集合
     * @param areaId    城市ID
     * @return  入驻商ID集合
     */
    @GetMapping(ApiConstants.PREFIX + "/getAreaCbSupplierIds")
    CommonResult<List<Long>> getAreaCbSupplierIds(@RequestParam("areaId") Long areaId);

    /**
     * 获取全国, 有在搞组合促销的入驻商集合
     * @param sysCode    大区Code
     * @return  入驻商ID集合
     */
    @GetMapping(ApiConstants.PREFIX + "/getGlobalCbSupplierIds")
    CommonResult<List<Long>> getGlobalCbSupplierIds(@RequestParam("sysCode") Long sysCode);

    @GetMapping(ApiConstants.PREFIX + "/getActivityCbByStatus")
    CommonResult<List<Long>> getActivityCbByStatus(@RequestParam("sysCode") Long sysCode,@RequestParam("funcScope") Integer  funcScope, @RequestParam(value = "status" ,required = false) Integer status);


    /**
     * 获取活动时间已过期的组合促销ID集合
     * @return  活动ID集合
     */
    @GetMapping(ApiConstants.PREFIX + "/getExpiredCbActivityIds")
    CommonResult<List<Long>> getExpiredCbActivityIds();

    /**
     * 停用组合促销活动
     * @param activityId  活动ID
     */
    @GetMapping(ApiConstants.PREFIX + "/stopCbActivity")
    CommonResult<Boolean> stopCbActivity(@RequestParam("activityId") Long activityId);



    /**
     * 获取活动主表信息
     * @param activityId 活动ID
     * @return
     */
    @GetMapping(ApiConstants.PREFIX + "/getActivityById")
    CommonResult<ActivityDTO> getActivityById(@RequestParam("activityId") Long activityId);

    /**
     * 获取促销主表信息列表
     * @param reqVO
     * @return
     */
    @PostMapping(PREFIX + "/getActivityList")
    CommonResult<List<ActivityDTO>> getActivityList(@RequestBody ActivityDTO reqVO);

    /**
     * 检验商品是否关联活动
     * @return
     */
    @PostMapping(PREFIX + "/checkActivityByItem")
    CommonResult<String> checkActivityByItem(@RequestParam("skuIdArray") Long[] skuIdArray);

    @GetMapping(ApiConstants.PREFIX + "/getActivitySupplierScopeList")
    CommonResult<List<ActivitySupplierScopeDTO>> getActivitySupplierScopeList(@RequestParam("activityId") Long activityId);
}
