package com.zksr.promotion.api.couponBatch.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @Date 2024/12/7 9:11
 * @注释
 */
@Data
public class CouponBatchDTO {

    @ApiModelProperty(value = "优惠券批量发送id")
    private Long couponBatchId;

    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "全国或者本地(数据字典);1-全国商品可用")
    private Long funcScope;

    @ApiModelProperty(value = "优惠券模板数量")
    private Long couponTemplateQty;

    @ApiModelProperty(value = "门店数量")
    private Long branchQty;

    @ApiModelProperty(value = "门店发放数量")
    private Long branchSendQty;

    @ApiModelProperty(value = "总计发券数量")
    private Long totalQty;

    @ApiModelProperty(value = "生效类型：0-定时生效，1-立即生效")
    private Integer validType;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "生效时间")
    private Date validTime;

    @ApiModelProperty(value = "实际发券成功数量")
    private Long realSendQty;

    @ApiModelProperty(value = "执行状态 0-未执行，1-已执行")
    private Integer taskExecuteStatus;

    @ApiModelProperty(value = "审核状态 0-未审核，1-已审核")
    private Integer auditStatus;

    @ApiModelProperty(value = "备注")
    private String remark;
}
