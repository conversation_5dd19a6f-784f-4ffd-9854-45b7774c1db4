package com.zksr.promotion.api.coupon.dto;

import com.zksr.common.core.pool.StringPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券返回
 * @date 2024/4/7 11:37
 */
@Data
@ToString
@ApiModel(description = "优惠券返回数据支持")
public class CouponReturnDTO {

    @ApiModelProperty(value = "优惠券列表", required = true)
    private List<CouponApplyDTO> couponList;

    @ApiModelProperty(value = "返回原因", required = true, notes = "自定义字符串")
    private String memo = StringPool.EMPTY;
}
