package com.zksr.promotion.api.coupon.vo;

import com.zksr.promotion.api.coupon.dto.CouponDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 下单可用优惠券
 * @date 2024/4/3 11:23
 */
@ApiModel(description = "下单可用优惠券")
@Data
public class OrderAvailableRespVO {

    @ApiModelProperty("有效优惠券")
    private List<CouponDTO> avaliableList = new ArrayList<>();

    @ApiModelProperty("无效优惠券")
    private List<CouponDTO> unavaliableList = new ArrayList<>();
}
