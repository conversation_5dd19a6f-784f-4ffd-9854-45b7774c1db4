package com.zksr.promotion.api.activity.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zksr.common.core.enums.PrmNoEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动
 * @date 2024/5/13 15:07
 */
@Data
@ApiModel(description = "促销活动")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SupplierActivityDTO extends ActivityDTO {

    /**
     * 指定城市范围, 城市是必填的
     */
    @ApiModelProperty(value = "指定城市范围", hidden = true)
    private Set<ActivityCityScopeDTO> cityScopeList;

    @ApiModelProperty(value = "指定门店范围", hidden = true)
    private Set<ActivityBranchScopeDTO> branchScopeList;

    @ApiModelProperty(value = "指定渠道范围", hidden = true)
    private Set<ActivityChannelScopeDTO> channelScopeList;

    /**
     * 有单独缓存 参见{@link com.zksr.common.redis.bean.SpuScopeListCacheBean}
     * 需要从这里面加载具体使用范围
     */
    @ApiModelProperty(value = "指定使用范围", notes = "展示分类ID,品牌ID,SKU_ID", hidden = true)
    private Set<Long> spuScopeList;

    /**
     * 有单独缓存 参见{@link com.zksr.common.redis.bean.SpuScopeListCacheBean}
     * 需要从这里面加载具体使用范围
     */
    @ApiModelProperty(value = "指定使用范围", notes = "展示分类ID,品牌ID,SKU_ID", hidden = true)
    private List<ActivitySpuScopeDTO> activitySpuScopeList;


    @ApiModelProperty(value = "满减规则", hidden = true)
    private List<FdRuleDTO> fdRules;

    /**
     * 有单独缓存 参见{@link com.zksr.common.redis.bean.ActivityFgRuleCacheBean}
     * 需要从这里面加载具体使用范围
     */
    @ApiModelProperty(value = "满赠规则", hidden = true)
    private List<FgRuleDTO> fgRules;

    /**
     * 有单独缓存 参见{@link com.zksr.common.redis.bean.ActivityBgRuleCacheBean}
     * 需要从这里面加载具体使用范围
     */
    @ApiModelProperty(value = "买赠规则", hidden = true)
    private List<BgRuleDTO> bgRules;

    /**
     * 有单独缓存 参见{@link com.zksr.common.redis.bean.ActivitySkRuleCacheBean}
     * 需要从这里面加载具体使用范围
     */
    @ApiModelProperty(value = "秒杀规则")
    private List<SkRuleDTO> skRules;

    /**
     * 有单独缓存 参见{@link com.zksr.common.redis.bean.ActivitySpRuleCacheBean}
     * 需要从这里面加载具体使用范围
     */
    @ApiModelProperty(value = "特价规则")
    private List<SpRuleDTO> spRules;

    /**
     * 有单独缓存 参见{@link com.zksr.common.redis.bean.ActivitySpRuleCacheBean}
     * 需要从这里面加载具体使用范围
     */
    @ApiModelProperty(value = "特价规则")
    private List<CbRuleDTO> cbRules;

    @ApiModelProperty(value = "活动入驻商列表")
    private List<Long> supplierIdList;

    public boolean notSpuCombinePrm() {
        return !PrmNoEnum.isCb(this.getPrmNo());
    }

    public boolean spuCombinePrm() {
        return PrmNoEnum.isCb(this.getPrmNo());
    }
}
