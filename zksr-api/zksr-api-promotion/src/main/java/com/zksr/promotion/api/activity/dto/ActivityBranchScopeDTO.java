package com.zksr.promotion.api.activity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 促销活动门店适用范围对象 prm_activity_branch_scope
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityBranchScopeDTO {
    /**
     * 门店id
     */
    @Excel(name = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /**
     * 1-白名单 0-黑名单
     */
    @ApiModelProperty("1-白名单 0-黑名单")
    private Integer whiteOrBlack = NumberPool.INT_ONE;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActivityBranchScopeDTO that = (ActivityBranchScopeDTO) o;
        return Objects.equals(branchId, that.branchId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(branchId);
    }
}
