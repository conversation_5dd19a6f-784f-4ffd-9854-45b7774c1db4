package com.zksr.promotion.api.coupon.dto;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.CouponReceiveScope;
import com.zksr.common.core.enums.CouponSpuScopeEnum;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.LongArrayToStringSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:    优惠模版1:1缓存
 * @date 2024/3/31 14:51
 */
@Data
@Accessors(chain = true)
public class CouponTemplateDTO {

    /** 优惠券模板id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 平台商id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商ID */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 优惠券模板名称 */
    private String couponName;

    /** 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定） */
    private Integer funcScope;

    /** 状态 */
    @ApiModelProperty(value = "0=正常,1未发布,2-下架")
    private Integer status;

    /** 模板开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = DateUtils.TIMEZONE)
    private Date templateStartDate;

    /** 模板结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = DateUtils.TIMEZONE)
    private Date templateEndDate;

    /** 商品适用范围(数据字典);0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券） */
    private Integer spuScope;

    /** 商品适用范围之适用ids;spu_scope=入驻商券时设定，入驻商id逗号分隔;spu_scope=品类券时设定，管理分类id逗号分隔;spu_scope=品牌券时设定，品牌id逗号分隔;spu_scope=商品券券时设定，SPU id逗号分隔 */
    private String spuScopeApplyIds;

    /** 领取范围;0-全部可领 1-指定渠道可领（func_scope=2可选）2-指定渠道可领（func_scope=2可选） 2-指定平台商城市分组可领（func_scope=2可选） 3-指定城市可领   4-指定业务员下的门店可领 */
    private Integer receiveScope;

    /** 领取范围之适用ids;receive_scope=指定渠道时设定，渠道id逗号分隔;receive_scope=指定平台商城市分组时设定，平台商城市分组id逗号分隔;receive_scope=指定城市时设定，城市id逗号分隔;receive_scope=指定业务员时设定，业务员id逗号分隔 */
    private String receiveScopeApplyIds;

    /** 优惠类型(数据字典);0-满减券  1-折扣券 */
    private Integer discountType;

    /** 优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用 */
    private BigDecimal triggerAmt;

    /** 优惠券面额;满多少元可以减多少元(满减券设置) */
    private BigDecimal discountAmt;

    /** 优惠折扣;折，如99折记9.9折(折扣券设置) */
    private BigDecimal discountPercent;

    /** 最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。 */
    private BigDecimal discountLimitAmt;

    /** 有效期类型(数据字典);0-固定日期1-领取之后 */
    private Integer expirationType;

    /** 有效期开始时间;当expiration_type=0设定 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDateStart;

    /** 有效期结束时间;当expiration_type=0设定 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDateEnd;

    /** 领取优惠券后禁用天数（默认值为0）;当expiration_type=1设定 ，第x天至第y天有效，本值为x */
    private Integer disableDays;

    /** 领取优惠券后有效期天数;当expiration_type=1设定 ，第x天至第y天有效，本值为y */
    private Integer expireDays;

    /** 领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券（返券规则表）3-新用户注册  4-门店积分兑换 */
    private Integer receiveType;

    /** 是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP */
    private Integer costFlag;

    /** 优惠券数量 （0-不限制数量） */
    private Integer couponQty;

    /** 每人限领数量（0-不限制数量） */
    private Integer limit;

    /** 领取优惠券的数量;统计信息 */
    private Integer receiveCount;

    /** 使用优惠券的次数;统计信息 */
    @Excel(name = "使用优惠券的次数;统计信息")
    private Integer useCount;

    /** 不同类型优惠券是否排它 */
    private String excludable;

    /** 是否重复;0-不重复（默认） 1-重复（需要设定重复规则(扩展表)，生成后面的重复模板，生成的重复模板repeat_flag为不重复，根据设定的重复次数生成对应条数的模板） */
    private String repeatFlag;

    /** 父重复模板id;由重复模板生成的，记录重复模板的id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long repeatPid;

    /** 重复的第几个 */
    private Integer repeatSeq;

    /**
     * 同类型优惠券是否排它
     */
    private Integer sameTypeExcludable;

    /** 优惠券重复规则 */
    private PrmCouponTemplateRepeatRuleDTO repeatRule;

    /**
     * 多入驻商id列表
     */
    @JsonSerialize(using = LongArrayToStringSerialize.class)
    private List<Long> supplierIdList;

    public CouponNormalCacheDTO convertCacheNormal() {
        return BeanUtil.toBean(this, CouponNormalCacheDTO.class);
    }

    /**
     * 获取缓存有效期建议
     * @return
     */
    public Long expireTimes() {
        // 缓存key有效期为过期时间 + 3天
        Date expireTime = new Date();
        if (Objects.nonNull(expirationDateEnd)) {
            // 有效期结束时间
            expireTime = expirationDateEnd;
        } else if (Objects.nonNull(expirationType) && Objects.nonNull(templateEndDate) && Objects.nonNull(expireDays) && expirationType == 1) {
            // 优惠券领取活动结束以后 + 领取后多少天可使用
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(templateEndDate);
            calendar.add(Calendar.DAY_OF_MONTH, expireDays);
            expireTime = calendar.getTime();
        } else if (Objects.nonNull(templateEndDate)) {
            // 优惠券发券活动结束时间
            expireTime = templateEndDate;
        }
        return ((expireTime.getTime() - System.currentTimeMillis()) / 1000L) + (3 * 86400L);
    }

    /**
     * 是否包含使用范围
     */
    public boolean hashSpuScope(CouponSpuScopeEnum couponSpuScopeEnum) {
        if (Objects.isNull(this.spuScope)) {
            return false;
        }
        // 获取使用范围
        String spuScopes = String.valueOf(this.getSpuScope());
        Set<String> set = Arrays.stream(spuScopes.split(StringPool.COMMA)).collect(Collectors.toSet());
        return set.contains(couponSpuScopeEnum.getScope().toString());
    }

    public boolean hashReceiveScope(CouponReceiveScope couponReceiveScope) {
        if (Objects.isNull(this.receiveScope)) {
            return false;
        }
        // 获取使用范围
        String receiveScopes = String.valueOf(this.getReceiveScope());
        Set<String> set = Arrays.stream(receiveScopes.split(StringPool.COMMA)).collect(Collectors.toSet());
        return set.contains(couponReceiveScope.getScope().toString());
    }
}
