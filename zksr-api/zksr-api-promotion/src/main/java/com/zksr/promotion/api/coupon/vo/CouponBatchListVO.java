package com.zksr.promotion.api.coupon.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 获取门店指定优惠券ID集合
 * @date 2024/4/7 16:11
 */
@ToString
@Data
@ApiModel(description = "获取门店指定优惠券ID集合")
public class CouponBatchListVO {
    @ApiModelProperty("门店ID")
    private Long branchId;
    @ApiModelProperty("优惠券id集合")
    private List<Long> couponIdList;

    public CouponBatchListVO() {
    }

    public CouponBatchListVO(Long branchId, List<Long> couponIdList) {
        this.branchId = branchId;
        this.couponIdList = couponIdList;
    }
}
