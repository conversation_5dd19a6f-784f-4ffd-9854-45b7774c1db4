package com.zksr.promotion.utils;

import com.zksr.common.core.enums.CouponReceiveScope;
import com.zksr.common.core.enums.CouponSpuScopeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.promotion.api.coupon.dto.CouponReceiveScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.vo.CouponSpuScopeValidReqVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券使用范围验证工具
 * @date 2024/12/3 9:09
 */
public class CouponScopeUtil {

    /**
     * 判断商品是否在使用范围黑白名单规则内
     * @param validReqVO                商品验证数据
     * @param couponSpuScopeList        黑白名单
     * @return  是否有效
     */
    public static boolean conditionSpuCopeApplyId(CouponSpuScopeValidReqVO validReqVO, List<CouponSpuScopeDTO> couponSpuScopeList) {
        if (couponSpuScopeList.isEmpty()) {
            return true;
        }
        // 按照使用范围分组
        Map<Integer, List<CouponSpuScopeDTO>> scopeMap = couponSpuScopeList.stream().collect(Collectors.groupingBy(CouponSpuScopeDTO::getSpuScope));
        return conditionSpuCopeApplyId(validReqVO, scopeMap);
    }

    /**
     * 判断商品是否在使用范围黑白名单规则内
     * @param validReqVO    商品验证数据
     * @param scopeMap      黑白名单
     * @return  是否有效
     */
    public static boolean conditionSpuCopeApplyId(CouponSpuScopeValidReqVO validReqVO, Map<Integer, List<CouponSpuScopeDTO>> scopeMap) {
        if (scopeMap.isEmpty()) {
            return true;
        }
        if (Objects.isNull(validReqVO)) {
            return true;
        }
        // 需要全部为ture
        for (Integer scope : scopeMap.keySet()) {
            List<CouponSpuScopeDTO> scopeDTOS = scopeMap.get(scope);
            Set<Long> applyIds = scopeDTOS.stream().map(CouponSpuScopeDTO::getApplyId).collect(Collectors.toSet());
            // 1-白名单 0-黑名单
            Integer whiteOrBlack = scopeDTOS.get(0).getWhiteOrBlack();
            Long applyId = null;
            switch (CouponSpuScopeEnum.formValue(scope)) {
                case SUPPLIER:
                    applyId = validReqVO.getSupplierId();
                    break;
                case CLASS:
                    applyId = validReqVO.getCategoryId();
                    break;
                case BRAND:
                    applyId = validReqVO.getBrandId();
                    break;
                case SKU:
                    applyId = validReqVO.getSkuId();
                    break;
                default:
                    applyId = NumberPool.LOWER_GROUND_LONG;
                    break;
            }
            // 范围ID都没有, 直接 false
            if (Objects.isNull(applyId)) {
                return false;
            }
            if (whiteOrBlack == 1) {
                // 白名单不包含
                if (!applyIds.contains(applyId)) {
                    return false;
                }
            } else {
                // 黑名单包含
                if (applyIds.contains(applyId)) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 判断门店是否可领取优惠券
     * @param scopeValidVO              验证数据
     * @param couponSpuScopeList      领取黑白名单
     * @return  是否有效
     */
    public static boolean conditionReceiveCopeApplyId(ReceiveScopeValidVO scopeValidVO, List<CouponReceiveScopeDTO> couponSpuScopeList) {
        if (couponSpuScopeList.isEmpty()) {
            return true;
        }
        if (Objects.isNull(scopeValidVO)) {
            return true;
        }
        // 按照使用范围分组
        Map<Integer, List<CouponReceiveScopeDTO>> scopeMap = couponSpuScopeList.stream().collect(Collectors.groupingBy(CouponReceiveScopeDTO::getReceiveScope));
        // 需要全部为ture
        for (Integer scope : scopeMap.keySet()) {
            List<CouponReceiveScopeDTO> scopeDTOS = scopeMap.get(scope);
            Set<Long> applyIds = scopeDTOS.stream().map(CouponReceiveScopeDTO::getApplyId).collect(Collectors.toSet());
            Long applyId;
            switch (CouponReceiveScope.formValue(scope)) {
                case CHANNEL:
                    applyId = scopeValidVO.getChannelId();
                    break;
                case AREA:
                    applyId = scopeValidVO.getAreaId();
                    break;
                case BRANCH:
                    applyId = scopeValidVO.getBranchId();
                    break;
                default:
                    applyId = NumberPool.LOWER_GROUND_LONG;
                    break;
            }
            // 1-白名单 0-黑名单
            Integer whiteOrBlack = scopeDTOS.get(0).getWhiteOrBlack();
            if (whiteOrBlack == 1) {
                // 白名单不包含
                if (!applyIds.contains(applyId)) {
                    return false;
                }
            } else {
                // 黑名单包含
                if (applyIds.contains(applyId)) {
                    return false;
                }
            }
        }
        return true;
    }


    @Data
    @ApiModel(description = "优惠券领取范围验证")
    public static class ReceiveScopeValidVO {
        private Long channelId;
        private Long areaId;
        private Long branchId;
    }
}
