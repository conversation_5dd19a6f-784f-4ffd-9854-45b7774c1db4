package com.zksr.promotion.api.activity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/21 8:32
 */
@Data
public class GiveRuleDTO {

    /** 买赠活动id */
    @Excel(name = "买赠活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** 赠品类型;0-商品 1-优惠券 */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    private Integer giftType;

    /** sku_id */
    @Excel(name = "sku_id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 返券模板id */
    @Excel(name = "返券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段 */
    @Excel(name = "总赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer totalGiftQty;

    /** 赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段 */
    @Excel(name = "赠送数量;赠品类型为商品时设定，优惠券取优惠券对应字段")
    private Integer onceGiftQty;

    /** 赠品商品单位大小 */
    @Excel(name = "赠品商品单位大小 1:小 2：中 3：大")
    private Integer giftSkuUnitType;

    /**
     * 赠品信息
     */
    @Excel(name = "赠品信息")
    @ApiModelProperty(name = "赠品信息")
    private Object giftObj;
}
