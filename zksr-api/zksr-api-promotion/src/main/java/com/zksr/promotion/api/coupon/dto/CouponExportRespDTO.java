package com.zksr.promotion.api.coupon.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @Date 2025/2/26 15:46
 * @优惠券领取记录导出实体
 */
@Data
@ApiModel("业务员拜访明细  Response VO")
public class CouponExportRespDTO extends BaseEntity {

    /**
     * 门店名称
     */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @Excel(name = "优惠券名称")
    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 优惠类型(数据字典);0-满减券  1-折扣券
     */
    @Excel(name = "优惠类型", readConverterExp = "0=满减券,1=折扣券")
    @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券")
    private Integer discountType;

    /**
     * 优惠券面额
     */
    @Excel(name = "优惠券面额")
    @ApiModelProperty(value = "优惠券面额")
    private BigDecimal discountAmt;

    /**
     * 领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券 4-注册发券 5-业务员发券 6-批次发券
     */
    @Excel(name = "领取方式", readConverterExp = "0=用户领取,1=主动发放,2=下单返券,4=注册发券,5=业务员发券,6=批次发券")
    @ApiModelProperty(value = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券 4-注册发券 5-业务员发券 6-批次发券")
    private Integer receiveType;

    /**
     * 优惠券使用规则说明
     */
    @Excel(name = "优惠券使用规则说明")
    @ApiModelProperty(value = "优惠券使用规则说明")
    private String couponRule;

    /**
     * 状态（数据字典）;1-正常 0-作废 2-已使用 3-已过期
     */
    @Excel(name = "状态", readConverterExp = "1=正常,0=作废,2=已使用,3=已过期")
    @ApiModelProperty(value = "状态（数据字典）;1-正常 0-作废 2-已使用 3-已过期")
    private Integer couponState;

    @ApiModelProperty(value = "领取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 使用时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "使用时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "使用时间")
    private Date useTime;

    /**
     * 使用单号
     */
    @Excel(name = "使用单号")
    @ApiModelProperty(value = "使用单号")
    private String relateOrderNo;


}
