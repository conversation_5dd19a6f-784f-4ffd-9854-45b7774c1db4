package com.zksr.promotion.api.activity.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 促销活动spu适用范围对象 prm_activity_spu_scope
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivitySpuScopeDTO {
    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /**
     * 适用id
     */
    @Excel(name = "适用id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long applyId;

    /**
     * 适用范围名称
     */
    @Excel(name = "适用范围名称")
    private String applyName;

    /**
     * 适用类型;2-管理分类，3-品牌，4-商品
     */
    @Excel(name = "适用类型;2-管理分类，3-品牌，4-商品")
    private Long applyType;

    @ApiModelProperty(value = "spuId", notes = "非数据库字段, 来自其他数据加载, 商品时会有SPUID")
    private Long spuId;

    /**
     * 1-白名单 0-黑名单
     */
    @ApiModelProperty("1-白名单 0-黑名单")
    private Integer whiteOrBlack = NumberPool.INT_ONE;
}
