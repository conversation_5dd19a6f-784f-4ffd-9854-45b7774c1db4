package com.zksr.promotion.api.couponBatch;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.promotion.api.couponBatch.dto.CouponBatchDTO;
import com.zksr.promotion.api.couponBatch.dto.CouponBatchDtlDTO;
import com.zksr.system.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/7 9:07
 * @批次发券rpc服务
 */
@FeignClient(
        contextId = "remoteCouponBatchApi",
        value = ApiConstants.NAME
)
public interface CouponBatchApi {

    String PREFIX = ApiConstants.PREFIX + "/couponBatch-api";

     /**
     * 获取已审核的生效类型为定时生效的批次发券集合
     */
     @GetMapping(PREFIX + "/getValidCouponBatchList")
     CommonResult<List<CouponBatchDTO>> getValidCouponBatchList();

     /**
     * 根据批次发券ID和发券类型获取优惠券批量发送详情集合
     */
     @GetMapping(PREFIX + "/getCouponBatchDtlList")
     CommonResult<List<CouponBatchDtlDTO>> getCouponBatchDtlList(@RequestParam("couponBatchId") Long couponBatchId, @RequestParam("scopeType") Integer scopeType);


     /**
     * 根据批次发券ID和门店ID删除优惠券批量发送详情数据
     */
     @GetMapping(PREFIX + "/deleteByBatchCouponIdAndBranchId")
     void deleteByBatchCouponIdAndBranchId(@RequestParam("couponBatchId") Long couponBatchId, @RequestParam("branchId") Long branchId);


     /**
     * 修改批次发券
     */
     @PostMapping(PREFIX + "/updateCouponBatch")
     void updateCouponBatch(@RequestBody CouponBatchDTO couponBatch);


}
