package com.zksr.promotion.api.coupon.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.LongArrayToStringSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 我的优惠券列表 - response VO
 * @date 2024/4/2 16:17
 */
@ToString
@Data
@ApiModel(description = "优惠券列表数据")
public class CouponDTO {

    @TableId(type = IdType.AUTO)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponId;

    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    @ApiModelProperty(value = "优惠券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    @ApiModelProperty(value = "优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "0-全部可用,1-全国商品可用,2-本地商品可用", notes = "全国券和本地券不可叠加使用")
    private Integer funcScope;

    @ApiModelProperty(value = "状态, 1-未使用,2-已使用,3-已过期")
    private Integer state;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "有效期开始时间")
    private Date expirationDateStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "有效期结束时间")
    private Date expirationDateEnd;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "使用时间")
    private Date useTime;

    @ApiModelProperty(value = "对应订单号")
    private String relateOrderNo;

    @ApiModelProperty(value = "商品适用范围(数据字典);0-所有商品可用")
    private Integer spuScope;

    @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券")
    private Integer discountType;

    @ApiModelProperty(value = "优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用")
    private BigDecimal triggerAmt;

    @ApiModelProperty(value = "优惠券面额;满多少元可以减多少元(满减券设置)")
    private BigDecimal discountAmt;

    @ApiModelProperty(value = "优惠折扣;折，如99折记9.9折(折扣券设置)")
    private BigDecimal discountPercent;

    @ApiModelProperty(value = "最多优惠;折扣券设置，例如，折扣上限为 20 元，当使用 8 折优惠券，订单金额为 1000 元时，最高只可折扣 20 元，而非 80  元。")
    private BigDecimal discountLimitAmt;

    @ApiModelProperty(value = "领取方式(数据字典);0-用户领取 1-主动发放 2-下单返券")
    private Integer receiveType;

    @ApiModelProperty(value = "是否分摊;用来计算订单表的discount_amt1,discount_amt2, 需要传输给ERP")
    private Integer costFlag;

    @ApiModelProperty(value = "不同类型优惠券是否排它")
    private Integer excludable;

    @ApiModelProperty(value = "具体使用范围别称, 比如入驻商名称")
    private List<CouponSpuScopeDTO> spuScopeAs;

    @ApiModelProperty("入驻商ID")
    private Long supplierId;

    @ApiModelProperty("优惠券批次ID")
    private Long couponBatchId;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;

    @ApiModelProperty(value = "同类型优惠券是否排它")
    private Integer sameTypeExcludable;

    @ApiModelProperty(value = "多入驻商id列表")
    @JsonSerialize(using = LongArrayToStringSerialize.class)
    private List<Long> supplierIdList;

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    public boolean timeValidate() {
        if (Objects.isNull(this.expirationDateStart) || Objects.isNull(this.expirationDateEnd)) {
            return false;
        }
        return this.expirationDateStart.getTime() < System.currentTimeMillis() && this.expirationDateEnd.getTime() > System.currentTimeMillis();
    }
}
