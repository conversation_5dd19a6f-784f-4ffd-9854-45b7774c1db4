package com.zksr.promotion.api.coupon.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

/**
 * 优惠券模板-重复规则对象 prm_coupon_template_repeat_rule
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@TableName(value = "prm_coupon_template_repeat_rule")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ColonelAppPrmCouponTemplateRepeatRule extends BaseEntity {
    private static final long serialVersionUID=1L;

    /** 优惠券模板重复规则 */
    @TableId(type = IdType.ASSIGN_ID)
    private Long couponTemplateRepeatRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 优惠券模板id */
    @Excel(name = "优惠券模板id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    /** 重复类型(数据字典);repeat_flag=重复时设定 1-每天 2-周一至周五 3-每周（每周的今天）  4-每月(每月的今天) 5-每月（每月的第几个周几（以今天为基准））  6-每年的今天 7-自定义 */
    @Excel(name = "重复类型(数据字典);repeat_flag=重复时设定 1-每天 2-周一至周五 3-每周", readConverterExp = "每=周的今天")
    private Integer repeatType;

    /** 重复频率单位(数据字典);repeat_type=自定义时设定 0-天，1-周，2-月，3-年 */
    @Excel(name = "重复频率单位(数据字典);repeat_type=自定义时设定 0-天，1-周，2-月，3-年")
    private Integer repeatFrequencyUnit;

    /** 重复周期(数据字典);repeat_type=自定义时设定 0：重复频率单位为天时，可选范围为0~99，即表示每几天重复；1：重复频率单位为周时，可选范围为周一~周日，可多选，即表示为每周的周几重复；2：重复频率单位为月时，有两种可选范围，一种是选日期，1号~31号，可多选。另一种是选星期，第1~4、最后一个（数据字典一），周一~周日、周末、周一至周五（数据字典二）；3：重复频率为月时，可选范围为1月~12月，可多选，即表示为每年的几月的今天重复 */
    @Excel(name = "重复周期(数据字典);repeat_type=自定义时设定 0：重复频率单位为天时，可选范围为0~99，即表示每几天重复；1：重复频率单位为周时，可选范围为周一~周日，可多选，即表示为每周的周几重复；2：重复频率单位为月时，有两种可选范围，一种是选日期，1号~31号，可多选。另一种是选星期，第1~4、最后一个", readConverterExp = "数=据字典一")
    private String repeatFrequencyPeriod;

    /** 总重复次数 */
    @Excel(name = "总重复次数")
    private Integer repaeatTimes;

}