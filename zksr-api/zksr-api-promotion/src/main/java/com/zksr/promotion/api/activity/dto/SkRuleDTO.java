package com.zksr.promotion.api.activity.dto;

import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 秒杀规则对象 prm_sk_rule
 *
 * <AUTHOR>
 * @date 2024-05-13
 */
@TableName(value = "prm_sk_rule")
@Data
@ToString(callSuper = true)
public class SkRuleDTO {
    private static final long serialVersionUID=1L;

    /** 秒杀规则id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skRuleId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 活动id */
    @Excel(name = "活动id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    /** SPU id */
    @Excel(name = "SPU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** SKU id */
    @Excel(name = "SKU id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 单次限量 */
    @Excel(name = "单次限量")
    private Integer onceLimit;

    /** 秒杀库存 */
    @Excel(name = "秒杀库存")
    private Integer seckillStock;

    /** 大单位秒杀价 */
    @Excel(name = "小单位秒杀价")
    private BigDecimal seckillPrice;

    /** 中单位秒杀价 */
    @Excel(name = "中单位秒杀价")
    private BigDecimal midSeckillPrice;

    /** 大单位秒杀价 */
    @Excel(name = "大单位秒杀价")
    private BigDecimal largeSeckillPrice;

    /** 状态：0-停用，1-启用 */
    @Excel(name = "状态：0-停用，1-启用")
    private Integer skStatus;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sortOrder;

    /** 中单位限购数量 */
    @Excel(name = "中单位限购数量")
    @ApiModelProperty(value = "中单位限购数量")
    private Integer midLimit;

    /** 大单位限购数量 */
    @Excel(name = "大单位限购数量")
    @ApiModelProperty(value = "大单位限购数量")
    private Integer largeLimit;

    /** 中单位秒杀库存 */
    @Excel(name = "中单位秒杀库存")
    private Integer seckillMidStock;

    /** 大单位秒杀库存 */
    @Excel(name = "大单位秒杀库存")
    private Integer seckillLargeStock;

    /**
     * 根据大中小单位获取不同的秒杀价格
     * @param unitType
     * @return 对应单位的秒杀价格
     */
    public BigDecimal getSeckilPriceByUnitType(Integer unitType){
        // 中单位
        if (unitType == UnitTypeEnum.UNIT_MIDDLE.getType().intValue())    return midSeckillPrice;
        // 大单位
        if (unitType == UnitTypeEnum.UNIT_LARGE.getType().intValue())  return largeSeckillPrice;
        // 小单位
        return seckillPrice;
    }

    public boolean containsUnit(ActivityVerifyItemDTO activityVerifyItemDTO) {
        if (UnitTypeEnum.S(activityVerifyItemDTO.getUnitSize()) && Objects.nonNull(this.seckillPrice) && NumberUtil.isGreater(this.seckillPrice, BigDecimal.ZERO)) {
            return true;
        }
        if (UnitTypeEnum.M(activityVerifyItemDTO.getUnitSize()) && Objects.nonNull(this.midSeckillPrice) && NumberUtil.isGreater(this.midSeckillPrice, BigDecimal.ZERO)) {
            return true;
        }
        if (UnitTypeEnum.L(activityVerifyItemDTO.getUnitSize()) && Objects.nonNull(this.largeSeckillPrice) && NumberUtil.isGreater(this.largeSeckillPrice, BigDecimal.ZERO)) {
            return true;
        }
        return false;
    }
}
