package com.zksr.promotion.api.activity.vo;

import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 单位特价
 * @date 2024/12/19 18:56
 */
@Data
@ApiModel(description = "sku单位参加促销数据")
@AllArgsConstructor
public class UnitSuperPriceVO {

    @ApiModelProperty("促销活动ID")
    private Long activityId;

    @ApiModelProperty("单位大小")
    private UnitTypeEnum unitType;

    @ApiModelProperty("促销价格")
    private BigDecimal superPrice;

    @ApiModelProperty("skuId")
    private Long skuId;

    /**
     * 封装数据
     */
    public UnitSuperPriceVO(UnitTypeEnum unitType, SpRuleDTO spRuleDTO) {
        this.unitType = unitType;
        this.skuId = spRuleDTO.getSkuId();
        switch (unitType) {
            case UNIT_SMALL:
                this.activityId = spRuleDTO.getActivityId();
                this.superPrice = spRuleDTO.getSpPrice();
                break;
            case UNIT_MIDDLE:
                this.activityId = spRuleDTO.getActivityId();
                this.superPrice = spRuleDTO.getMidSpPrice();
                break;
            case UNIT_LARGE:
                this.activityId = spRuleDTO.getActivityId();
                this.superPrice = spRuleDTO.getLargeSpPrice();
                break;
            default:
                break;
        }
    }

    /**
     * 封装数据
     */
    public UnitSuperPriceVO(UnitTypeEnum unitType, SkRuleDTO skRuleDTO) {
        this.unitType = unitType;
        this.skuId = skRuleDTO.getSkuId();
        switch (unitType) {
            case UNIT_SMALL:
                this.activityId = skRuleDTO.getActivityId();
                this.superPrice = skRuleDTO.getSeckillPrice();
                break;
            case UNIT_MIDDLE:
                this.activityId = skRuleDTO.getActivityId();
                this.superPrice = skRuleDTO.getMidSeckillPrice();
                break;
            case UNIT_LARGE:
                this.activityId = skRuleDTO.getActivityId();
                this.superPrice = skRuleDTO.getLargeSeckillPrice();
                break;
            default:
                break;
        }
    }
}
