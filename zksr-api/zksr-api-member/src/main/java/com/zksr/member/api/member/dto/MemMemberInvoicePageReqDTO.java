package com.zksr.member.api.member.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 用户发票对象 mem_member_invoice
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@ApiModel("用户发票 - mem_member_invoice分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemMemberInvoicePageReqDTO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    private Long id;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id", required = true)
    private Long memberId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 发票类型,10电子普通发票20专用发票 */
    @Excel(name = "发票类型,10电子普通发票20专用发票")
    @ApiModelProperty(value = "发票类型,10电子普通发票20专用发票", required = true)
    private Long invoiceType;

    /** 发票抬头类型,10个人20单位 */
    @Excel(name = "发票抬头类型,10个人20单位")
    @ApiModelProperty(value = "发票抬头类型,10个人20单位", required = true)
    private Long titleType;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    /** 纳税人识别码 */
    @Excel(name = "纳税人识别码")
    @ApiModelProperty(value = "纳税人识别码")
    private String taxpayerCode;

    /** 单位地址 */
    @Excel(name = "单位地址")
    @ApiModelProperty(value = "单位地址")
    private String companyAddress;

    /** 单位电话 */
    @Excel(name = "单位电话")
    @ApiModelProperty(value = "单位电话", required = true)
    private String companyPhone;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人", required = true)
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话", required = true)
    private String contactPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    @ApiModelProperty(value = "开户银行", required = true)
    private String bankName;

    /** 银行账户 */
    @Excel(name = "银行账户")
    @ApiModelProperty(value = "银行账户", required = true)
    private String bankAccount;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "银行账户", required = true)
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号", required = true)
    private Long version;


    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

}
