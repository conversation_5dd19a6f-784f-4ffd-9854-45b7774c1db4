package com.zksr.member.api.branch.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 会员门店列表请求数据
 * @date 2024/6/8 15:38
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@ApiModel(description = "查看会员下的门店数据列表")
public class MemberBranchReqVO {

    @ApiModelProperty(value = "会员ID", hidden = true, notes = "不需要前端传入, 后台从token中获取")
    private Long memberId;

    @ApiModelProperty(value = "城市ID, 应该要传2级城市")
    private Long areaId;

    @ApiModelProperty(value = "查询条件, 支持门店名称,地址模糊匹配")
    private String condition;
}
