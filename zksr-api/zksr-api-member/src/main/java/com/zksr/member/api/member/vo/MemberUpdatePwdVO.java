package com.zksr.member.api.member.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户更新密码 接口请求")
public class MemberUpdatePwdVO {

    @ApiModelProperty(value = "手机号", required = true)
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "用户密码  必填")
    @NotBlank(message = "用户密码不能为空")
    private String password;

    @ApiModelProperty(value = "sysSource  必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;
}
