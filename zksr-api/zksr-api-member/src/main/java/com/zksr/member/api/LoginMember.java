package com.zksr.member.api;

import com.zksr.member.api.member.dto.MemberDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class LoginMember {

    private String token;//登录的token码

    private MemberDTO member;

    /**
     * 平台商id
     */
    private Long sysCode;

    /**
     * 用户id
     */
    private Long memberId;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 门店id
     */
    private Long branchId;

    /**
     * 城市id
     */
    private Long areaId;

    /**
     * 过期时间
     */
    private Long expireTime;
}
