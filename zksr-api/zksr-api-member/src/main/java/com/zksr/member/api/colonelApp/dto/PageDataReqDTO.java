package com.zksr.member.api.colonelApp.dto;

import com.zksr.member.constant.MemberConstant;
import com.zksr.trade.api.order.dto.TrdSettleDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
*
 * 业务员APP 首页信息请求DTO
* <AUTHOR>
* @date 2024/5/11 15:47
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageDataReqDTO {

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 平台商ID */
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    /** 门店ID */
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    /** 门店下单金额 */
    @ApiModelProperty(value = "门店下单金额")
    private BigDecimal branchOrderAmt;

    /** 门店退货金额 */
    @ApiModelProperty(value = "门店退货金额")
    private BigDecimal branchRefundAmt;

    /** 订单结算信息 业务员分成 */
//    @ApiModelProperty(value = "订单结算信息 业务员分成")
//    private List<TrdSettleDTO> trdSettleList;

    @ApiModelProperty(value = "业务员提成金额(销售为正数，售后为负数)")
    private BigDecimal colonelSettleAmt;

    @ApiModelProperty(value = "是否是业务员下单 1：是 0 否")
    private Integer colonelFlag;

    /**
     * 刷新ES 类型 消息发送类型 1、业务员签到 2、业务员拓店  3、客户下单 4、客户售后退货
     */
    @ApiModelProperty(value = "类型")
    private Integer type;

    public PageDataReqDTO(Long colonelId, Long sysCode, Long branchId, Integer type) {
        this.colonelId = colonelId;
        this.sysCode = sysCode;
        this.branchId = branchId;
        this.type = type;
    }

    public PageDataReqDTO(Long colonelId, Long sysCode, Integer type) {
        this.colonelId = colonelId;
        this.sysCode = sysCode;
        this.type = type;
    }

    public PageDataReqDTO(Long colonelId, Long sysCode, Long branchId, BigDecimal amt, BigDecimal colonelSettleAmt, Integer type) {
        this.colonelId = colonelId;
        this.sysCode = sysCode;
        this.branchId = branchId;
        this.colonelSettleAmt = colonelSettleAmt;
        this.type = type;
        if(MemberConstant.COLONEL_APP_Page_DATA_TYPE_3 == type){
            this.branchOrderAmt = amt;
        }else if(MemberConstant.COLONEL_APP_Page_DATA_TYPE_4 == type){
            this.branchRefundAmt = amt;
        }

    }

    public PageDataReqDTO(Long colonelId, Long sysCode, Long branchId, BigDecimal amt, BigDecimal colonelSettleAmt, Integer colonelFlag, Integer type) {
        this.colonelId = colonelId;
        this.sysCode = sysCode;
        this.branchId = branchId;
        this.colonelSettleAmt = colonelSettleAmt;
        this.type = type;
        this.colonelFlag = colonelFlag;
        if(MemberConstant.COLONEL_APP_Page_DATA_TYPE_3 == type){
            this.branchOrderAmt = amt;
        }else if(MemberConstant.COLONEL_APP_Page_DATA_TYPE_4 == type){
            this.branchRefundAmt = amt;
        }

    }


}
