package com.zksr.member.api.searchHis;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.searchHis.vo.SearchHisReqVO;
import com.zksr.member.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2025/1/16 17:25
 * @搜索词条rpc服务
 */
@FeignClient(
        contextId = "remoteSearchHisApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface SearchHisApi {

    String PREFIX = ApiConstants.PREFIX + "/searchHis";

    /**
     * 新增搜索词条
     * @param searchHisReqVO 搜索词条
     */
    @PostMapping(PREFIX +  "/addSearchHis")
    CommonResult<Long> addSearchHis(@RequestBody SearchHisReqVO searchHisReqVO);
}
