package com.zksr.member.api.member.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户信息对象 mem_member
 *
 * <AUTHOR>
 * @date 2024-02-28
 */
@Data
@ApiModel("用户信息 - mem_member分页 Request VO")
public class MemMemberSaveReqVO {
    private static final long serialVersionUID = 1L;

    /** 用户id */
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    @ApiModelProperty(value = "sysCode",  example = "11")
    private Long sysCode;

    /** 用户手机号 */
    @ApiModelProperty(value = "用户手机号",  example = "13412344321")
    @NotNull(message = "用户手机号不能为空")
    @Length(min=1, max=16, message = "长度最大16")
    private String memberPhone;

    /** 用户名 */
    @ApiModelProperty(value = "用户名",  example = "小明")
    @NotNull(message = "用户名不能为空")
    @Length(min=1, max=32, message = "长度最大32")
    private String memberName;

    /** 微信unionid */
    @ApiModelProperty(value = "微信unionid",  example = "12412321321")
    @Length(min=1, max=100, message = "长度最大100")
    private String wxUnionid;

    /** 头像 */
    @ApiModelProperty(value = "头像",  example = "sdfadsf.jpg")
    private String avatar;

    /** 微信小程序openid */
    @ApiModelProperty(value = "微信小程序openid",  example = "51232321")
    @Length(min=1, max=32, message = "微信小程序openid，长度最大100")
    private String xcxOpenid;

    /** 注册业务员id */
    @ApiModelProperty(value = "微信小程序openid",  example = "51232321")
    private Long registerColonelId;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 门店编号集合 */
    @ApiModelProperty(value = "门店编号集合")
    private List<Long> branchIds;


    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    private String userName;

    /** 用户密码 */
    @ApiModelProperty(value = "用户密码")
    private String password;

    @ApiModelProperty("操作的渠道 0.b2b商城 1.客户通")
    private Integer channel = 0;
}
