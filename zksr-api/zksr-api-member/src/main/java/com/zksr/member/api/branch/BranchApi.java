package com.zksr.member.api.branch;

import com.zksr.common.core.domain.vo.openapi.syncCall.SyncBranchCallDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.BranchSupplierDTO;
import com.zksr.member.api.branch.dto.ColonelBranchCountDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branch.excel.MemBranchImportExcel;
import com.zksr.member.api.branch.form.BranchImportForm;
import com.zksr.member.api.branch.vo.BranchListForReqVO;
import com.zksr.member.api.branch.vo.MemberBranchReqVO;
import com.zksr.member.enums.ApiConstants;
import com.zksr.trade.api.order.vo.TrdOrder;
import com.zksr.trade.api.orderSettle.vo.BranchStatementExportVo;
import com.zksr.trade.api.orderSettle.vo.BranchStatementPageVo;
import com.zksr.report.api.homePages.dto.HomePagesBranchDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient(
        contextId = "remoteBranchApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface BranchApi {

    String PREFIX = ApiConstants.PREFIX + "/branch";


    @GetMapping(PREFIX + "/getByBranchId")
    public CommonResult<BranchDTO> getByBranchId(@RequestParam("branchId") Long branchId);

    /**
     * @Description: 门店绑定入驻商
     * @Author: liuxingyu
     * @Date: 2024/3/28 16:45
     */
    @PostMapping(PREFIX + "/bindSupplier")
    void bindSupplier(@RequestBody BranchSupplierDTO branchSupplierDTO);

    /**
     * @Description: 根据门店获取绑定的入驻商
     * @Author: liuxingyu
     * @Date: 2024/3/28 17:08
     */
    @GetMapping(PREFIX + "/bindSupplier")
    CommonResult<List<Long>> getSupplierByBranchId(@RequestParam("branchId") Long branchId);

    /**
     * @Description: 根据入驻商ID获取门店ID
     * @Author: liuxingyu
     * @Date: 2024/3/28 18:14
     */
    @GetMapping(PREFIX + "/getBySupplier")
    CommonResult<List<Long>> getBySupplier(@RequestParam("supplierId") Long supplierId);


    /**
     * 根据用户ID 获取关联的默认门店
     *
     * @param memberId
     * @param sysCode
     * @return
     */
    @GetMapping(PREFIX + "/getDefaultBranchByMemberId")
    CommonResult<BranchDTO> getDefaultBranchByMemberId(@RequestParam("memberId") Long memberId, @RequestParam("sysCode") Long sysCode);

    /**
     * 根据用户ID 获取关联的门店列表
     *
     * @param memberId
     * @return
     */
    @PostMapping(PREFIX + "/getBranchListByMemberId")
    CommonResult<List<BranchDTO>> getBranchListByMemberId(@RequestBody MemberBranchReqVO memberBranchReqVO);

    /**
     * 根据用户ID 获取关联的门店列表
     *
     * @param memberBranchReqVO
     * @return
     */
    @PostMapping(PREFIX + "/getUnauditedBranchListByMemberId")
    CommonResult<List<BranchDTO>> getUnauditedBranchListByMemberId(@RequestBody MemberBranchReqVO memberBranchReqVO);

    /**
     * 获取半年内所有已过期的门店信息
     *
     * @param sysCode@return
     */
    @GetMapping(PREFIX + "/getAllExpirationDateBranchList")
    CommonResult<List<BranchDTO>> getAllExpirationDateBranchList(@RequestParam("sysCode") Long sysCode);

    /**
     * 批量修改门店的状态
     *
     * @param branchDTO
     * @return
     */
    @PostMapping(PREFIX + "/updateStatusByBranchIds")
    CommonResult<Boolean> updateStatusByBranchIds(@RequestBody BranchDTO branchDTO);

    /**
     * 修改门店信息
     *
     * @param updateReqVO
     * @return
     */
    @PutMapping(PREFIX + "/editBranch")
    CommonResult<Boolean> edit(@RequestBody MemBranchSaveReqVO updateReqVO);

    /**
     * 修改门店信息的首单相关信息
     *
     * @param branchDTO
     * @return
     */
    @PostMapping(PREFIX + "/updateFirstOrder")
    CommonResult<Boolean> updateFirstOrder(@RequestBody BranchDTO branchDTO);

    /**
     * 新增门店信息
     *
     * @param createReqVO
     * @return
     */
    @PostMapping(PREFIX + "/addBranch")
    CommonResult<Long> add(@RequestBody MemBranchSaveReqVO createReqVO);


    /**
     * 更新门店最后一次登陆时间
     *
     * @param sysCode
     * @param branchId
     * @return
     */
    @PostMapping(PREFIX + "/updateLastLoginTimeByApi")
    CommonResult<Boolean> updateLastLoginTimeByApi(@RequestParam("sysCode") Long sysCode, @RequestParam("branchId") Long branchId);

    /**
     * @Description: 入驻商绑定门店
     * @Author: liuxingyu
     * @Date: 2024/5/15 10:57
     */
    @PostMapping(PREFIX + "/bindBranch")
    CommonResult<Integer> bindBranch(@RequestBody BranchSupplierDTO branchSupplierDTO);

    /**
     * 获取所有的门店ID
     * @param minBranchId   最小门店ID
     * @return  门店ID集合
     */
    @GetMapping(PREFIX + "/getAllBranchIdList")
    CommonResult<List<Long>> getAllBranchIdList(@RequestParam("minBranchId") Long minBranchId);

    /**
     * 更新微信商户认证信息
     * @param branchId  门店ID
     * @param openid    认证openid
     * @return  结果
     */
    @PutMapping(PREFIX + "/updateWechatMerchantAuthOpenid")
    CommonResult<Boolean> updateWechatMerchantAuthOpenid(@RequestParam("branchId") Long branchId, @RequestParam("openid") String openid);

    /**
     * 刷新ES门店下单数据信息
     * @param sysCode
     * @return
     */
    @PostMapping(PREFIX + "/refreshEsBranchSalesInfoJobHandler")
    CommonResult<Boolean> refreshEsBranchSalesInfoJobHandler(@RequestBody Long sysCode);

    @PostMapping(PREFIX + "/initEsBranchSalesInfoHandler")
    CommonResult<Boolean> initEsBranchSalesInfoHandler(@RequestBody  Long sysCode, @RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate);

    /**
     * 刷新 ES 门店基础信息
     * @param branchId  门店ID
     * @return
     */
    @PutMapping(PREFIX + "/refreshEsBranchBase")
    CommonResult<Boolean> refreshEsBranchBase(@RequestParam("branchId") Long branchId);

    /**
     * 根据时间范围查询门店数据
     * @return
     */
    @PostMapping(PREFIX + "/getBranchDataPage")
    CommonResult<PageResult<SyncBranchCallDTO>> getBranchDataPage(@RequestParam("pageNum")Integer pageNum,@RequestParam("pageSize") Integer pageSize);

    /**
     * 根据条件获取门店列表
     * @param reqVO 条件
     * @return
     */
    @PostMapping(PREFIX + "/getBranchListForBy")
    CommonResult<List<BranchDTO>> getBranchListForBy(@RequestBody BranchListForReqVO reqVO);

    /**
     * 根据业务员编号查询管理门店数量
     * @param colonelId 条件
     * @return
     */
    @GetMapping(PREFIX + "/selectBranchCountByColonel")
    CommonResult<Long> selectBranchCountByColonel(@RequestParam("colonelId") Long colonelId);

    /**
     * 根据业务员ID获取门店数量
     * @param colonelIds   业务员ID集合
     * @param currentMonthId  当前月份ID
     * @return
     */
    @GetMapping(PREFIX + "/getBranchCountFromOtherSource")
    CommonResult<List<ColonelBranchCountDTO>> getBranchCountFromOtherSource(@RequestParam("colonelIds") List<String> colonelIds, @RequestParam("currentMonthId") String currentMonthId);

    @PostMapping(PREFIX + "/listByBranchIds")
    CommonResult<Map<Long, BranchDTO>> listByBranchIds(@RequestBody List<Long> branchIds);

    /**
     * 获取门店信息数据
     * @param pageVO
     * @return
     */
    @PostMapping(value =PREFIX + "/getBranchStatementPage")
    CommonResult<List<BranchStatementExportVo>> getBranchStatementPage(@RequestBody BranchStatementPageVo pageVO);

    /**
     * 获取PC首页门店数据
     * @param reqVO
     * @return
     */
    @PostMapping(PREFIX + "/getHomePagesBranchData")
    CommonResult<List<HomePagesBranchDataRespDTO>> getHomePagesBranchData(@RequestBody HomePagesReqVO reqVO);

    /**
     * 根据区域获取门店
     * @param dcAreaId 区域id
     * @return
     */
    @PostMapping(PREFIX + "/getBranchListByArea")
    CommonResult<List<BranchDTO>> getBranchListByArea(@RequestBody Long dcAreaId);

    /**
     * 修改门店的seas时间
     */
    @PutMapping(PREFIX + "/editSeasTime")
    CommonResult<Boolean> editSeasTime(@RequestBody MemBranchSaveReqVO updateReqVO);
    @GetMapping(PREFIX + "/deleteBranchById")
    void deleteBranchById(@RequestParam("colonelId") Long branchId);


    /**
     * 根据平台商获取所有的门店ID
     * @param sysCode   平台商ID
     * @return  门店ID集合
     */
    @GetMapping(PREFIX + "/getAllBranchIdListBySysCode")
    CommonResult<List<Long>> getAllBranchIdListBySysCode(@RequestParam("sysCode") Long sysCode);

    @PostMapping(PREFIX + "/importDataEvent")
    CommonResult<String> importDataEvent(@RequestBody BranchImportForm form);

    /**
     * 根据平台商和渠道Id获取所有的门店信息
     */
    @GetMapping(PREFIX + "/getBranchListByChannelId")
    CommonResult<List<BranchDTO>> getBranchListByChannelId(@RequestParam("channelId") Long channelId,@RequestParam("sysCode") Long sysCode);

    /**
     * 查询该areaId是否生成过数据
     */
    @GetMapping(PREFIX + "/getAreaIdExistBranch")
    CommonResult<List<BranchDTO>> getAreaIdExistBranch(@RequestParam("areaId") Long areaId,@RequestParam("sysCode") Long sysCode);

    /**
     * @Description: 根据门店名称查询门店ID集合
     * @Author: liyi
     */
    @GetMapping(PREFIX + "/getBranchIdListByBranchName")
    CommonResult<List<Long>> getBranchIdListByBranchName(@RequestParam("branchName") String branchName,@RequestParam("sysCode") Long sysCode);


    /**
     * 刷新门店生命周期
     * @param sysCode  门店ID
     * @return
     */
    @PutMapping(PREFIX + "/refreshBranchLifecycleZipHandler")
    CommonResult<Boolean> refreshBranchLifecycleZipHandler(@RequestParam("sysCode") Long sysCode);


    /**
     * 更新门店生命周期
     * @param branchId
     * @param operationType
     * @param orderId
     * @return
     */
    @PutMapping(PREFIX + "/updateBranchLifecycle")
    CommonResult<Boolean> updateBranchLifecycle(@RequestParam("branchId")Long branchId, @RequestParam("operationType")Integer operationType,@RequestParam(value = "orderId",required = false) Long orderId,@RequestParam(value = "afterId",required = false) Long afterId);

    /**
     * 根据外部门店编码，查询门店信息
     */
    @PostMapping(PREFIX + "/checkBranchUserOrCreate")
    CommonResult<Boolean> checkBranchUserOrCreate(@RequestParam("sysCode") Long sysCode, @RequestParam("memberId") Long memberId, @RequestParam("branchId") Long branchId);

    /**
     * 根据外部门店编码，查询门店信息
     */
    @GetMapping(PREFIX + "/getBranchByOuterMerchantCode")
    CommonResult<BranchDTO> getBranchByOuterMerchantCode(@RequestParam("sysCode") Long sysCode, @RequestParam("outerMerchantCode") String outerMerchantCode);
}
