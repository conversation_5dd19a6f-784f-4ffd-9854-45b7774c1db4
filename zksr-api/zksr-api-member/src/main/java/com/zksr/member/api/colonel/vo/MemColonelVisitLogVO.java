package com.zksr.member.api.colonel.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/17 17:17
 * @注释
 */
@Data
@ApiModel("业务员拜访明细 req VO")
public class MemColonelVisitLogVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long colonelVisitLogId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 业务员ID */
    @Excel(name = "业务员ID")
    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    /** 门店ID */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    /** 拜访状态 0签到，1签退，2作废 */
    @Excel(name = "拜访状态 0签到，1签退，2作废")
    @ApiModelProperty(value = "拜访状态 0签到，1签退，2作废")
    private Integer visitFlag;


    /** 搜索关键字 */
    @Excel(name = "搜索关键字")
    @ApiModelProperty(value = "搜索关键字")
    private String keywords;

    @ApiModelProperty(value = "门店Id集合")
    private List<Long> branchIdList;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private String startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private String endTime;

    /** 城市Id */
    @Excel(name = "城市Id")
    @ApiModelProperty(value = "城市Id")
    private Long areaId;

    /** 主键ID的集合 */
    @ApiModelProperty(value = "主键ID的集合")
    private List<String> colonelVisitLogIdList;

}
