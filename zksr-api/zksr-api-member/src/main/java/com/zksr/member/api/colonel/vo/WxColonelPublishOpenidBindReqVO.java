package com.zksr.member.api.colonel.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员授权公众号绑定信息
 * @date 2024/9/24 14:03
 */
@Data
@ApiModel(description = "业务员授权公众号绑定信息")
public class WxColonelPublishOpenidBindReqVO {

    @ApiModelProperty(value = "绑定key, 有效期30分钟, 1800秒", required = true)
    @NotNull(message = "bindKey 不能为空")
    private String bindKey;

    @ApiModelProperty(value = "微信openid", required = true)
    @NotNull(message = "openid 不能为空")
    private String openid;
}
