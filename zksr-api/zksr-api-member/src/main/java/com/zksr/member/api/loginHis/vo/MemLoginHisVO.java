package com.zksr.member.api.loginHis.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/19 14:39
 * @注释
 */
@Data
@ApiModel("登录历史 -  Request VO")
public class MemLoginHisVO {

    /**
     * 日期;yyyyMMdd
     */
    @ApiModelProperty(value = "日期;yyyyMMdd")
    private Long dateId;

    /**
     * 微信openid;后台登录信息获取
     */
    @ApiModelProperty(value = "微信openid;后台登录信息获取")
    private String wxOpenid;

    /**
     * 用户手机号;后台登录信息获取
     */
    @ApiModelProperty(value = "用户手机号;后台登录信息获取")
    private String memberPhone;

    /**
     * 用户名;后台登录信息获取
     */
    @ApiModelProperty(value = "用户名;后台登录信息获取")
    private String memberUsername;

    /**
     * 用户id;后台登录信息获取
     */
    @ApiModelProperty(value = "用户id;后台登录信息获取")
    private Long memberId;

    /**
     * 门店id;后台登录信息获取
     */
    @ApiModelProperty(value = "门店id;后台登录信息获取")
    private Long branchId;

    /**
     * ip地址;http_request
     */
    @ApiModelProperty(value = "ip地址;http_request")
    private String ip;

    /**
     * ip地址归属地;http_request
     */
    @ApiModelProperty(value = "ip地址归属地;http_request")
    private String district;

    /**
     * 类型（数据字典）;0-登陆  1-访问
     */
    @ApiModelProperty(value = "类型")
    private String tp;

    /**
     * 设备id;前端传（HttpHeader）
     */
    @ApiModelProperty(value = "设备id;前端传")
    private String deviceId;

    /**
     * 设备类型;前端传（HttpHeader）
     */
    @ApiModelProperty(value = "设备类型;前端传")
    private String deviceType;

    /**
     * 设备品牌;前端传（HttpHeader）
     */
    @ApiModelProperty(value = "设备品牌;前端传")
    private String deviceBrand;

    /**
     * 设备型号;前端传（HttpHeader）
     */
    @ApiModelProperty(value = "设备型号;前端传")
    private String deviceModel;

    /**
     * 系统名称;前端传（HttpHeader）
     */
    @ApiModelProperty(value = "系统名称;前端传")
    private String osName;

    /**
     * 操作系统版本;前端传（HttpHeader）
     */
    @ApiModelProperty(value = "操作系统版本;前端传")
    private String osVersion;

    /**
     * pc app xcx
     */
    @ApiModelProperty(value = "pc app xcx")
    private String port;

    /**
     * spu_id
     */
    @ApiModelProperty(value = "spu_id")
    private Long spuId;

}
