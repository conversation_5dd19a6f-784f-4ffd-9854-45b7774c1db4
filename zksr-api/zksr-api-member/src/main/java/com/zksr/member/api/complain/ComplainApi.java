package com.zksr.member.api.complain;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.complain.dto.MemComplainDTO;
import com.zksr.member.api.complain.excel.MemComplainExcel;
import com.zksr.member.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024/5/7 11:38
 */
@FeignClient(
        contextId = "remoteComplainApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface ComplainApi {

    String PREFIX = ApiConstants.PREFIX + "/complain";

    /**
     * 查询投诉列表
     * @param memComplainVO
     * @return
     */
    @PostMapping(PREFIX + "/list")
     PageResult<MemComplainVO> getComplainList(@RequestBody MemComplainVO memComplainVO);



    @GetMapping(PREFIX + "/{complainId}")
     MemComplainVO getComplainById(@PathVariable("complainId") String complainId);


    @PostMapping(PREFIX + "/addComplain")
    CommonResult<String> addComplain(@RequestBody MemComplainDTO memComplainDTO);

    @PostMapping(PREFIX + "/getComplainExcel")
    CommonResult<List<MemComplainExcel>> getComplainExcel(@RequestBody MemComplainVO memComplainVO);
}
