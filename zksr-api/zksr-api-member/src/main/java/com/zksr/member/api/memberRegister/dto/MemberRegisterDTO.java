package com.zksr.member.api.memberRegister.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 用户注册信息
 * <AUTHOR>
 * @date 2024/4/23 19:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberRegisterDTO {

    /** 用户注册信息ID */
    private Long memberRegisterId;

    /** 平台商id */
    private Long sysCode;

    /** 用户名 */
    private String memberName;

    /**
     * 门店编码
     */
    private String branchNo;

    /** 门店名称 */
    private String branchName;

    /** 城市id */
    private Long areaId;

    /** 门店地址 */
    private String branchAddr;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 用户账号 */
    private String userName;

    /** 用户密码 */
    private String password;

    /** 门头照 */
    private String branchImages;

    /** 审核人 */
    private Long approveMan;

    /** 审核标识 */
    private Integer approveFlag;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 用户自动审核标识 */
    private Integer memberApproveFlag;

    /** 用户过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date memberExpirationDate;

    /** 门店自动审核标识 */
    private Integer branchApproveFlag;

    /** 门店过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date branchExpirationDate;

    /** 备注 */
    private String memo;

    /** 状态 */
    private Integer status;

    @ApiModelProperty("三级区域城市ID, 省市区关联")
    private Long threeAreaCityId;

    @ApiModelProperty(value = "推广业务员ID")
    private Long colonelId;

    @ApiModelProperty(value = "渠道id")
    private Long channelId;

    /** 省份 */
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @ApiModelProperty(value = "区县")
    private String districtName;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;
}
