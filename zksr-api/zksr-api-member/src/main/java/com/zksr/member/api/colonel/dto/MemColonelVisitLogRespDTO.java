package com.zksr.member.api.colonel.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @Date 2025/2/17 17:03
 * @业务员拜访明细
 */
@Data
@ApiModel("业务员拜访明细  Response VO")
public class MemColonelVisitLogRespDTO extends BaseEntity{

    /**
     * 门店编码
     */
    @Excel(name = "门店编码")
    @ApiModelProperty(value = "门店编码")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /**
     * 签到经度
     */
    @Excel(name = "签到经度")
    @ApiModelProperty(value = "签到经度")
    private String signInLongitude;

    /**
     * 签到纬度
     */
    @Excel(name = "签到纬度")
    @ApiModelProperty(value = "签到纬度")
    private String signInLatitude;

    /**
     * 签到地址
     */
    @Excel(name = "签到地址")
    @ApiModelProperty(value = "签到地址")
    private String signInAddress;

    /**
     * 签到距离
     */
    @Excel(name = "签到距离")
    @ApiModelProperty(value = "签到距离")
    private String signInDistance;

    /**
     * 签到图片链接：多个以英文,隔开
     */
    @Excel(name = "签到图片链接：多个以英文,隔开")
    @ApiModelProperty(value = "签到图片链接：多个以英文,隔开")
    private String signInImgUrls;

    /**
     * 签到时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "签到时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "签到时间")
    private Date signInDate;


    /**
     * 签退时间
     */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "签退时间", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "签退时间")
    private Date signOutDate;

    /**
     * 拜访状态 0签到，1签退，2作废
     */
    @Excel(name = "拜访状态 0签到，1签退，2作废")
    @ApiModelProperty(value = "拜访状态 0签到，1签退，2作废")
    private Integer visitFlag;

    /**
     * 拜访间隔时间
     */
    @Excel(name = "拜访间隔时间")
    @ApiModelProperty(value = "拜访间隔时间")
    private String visitIntervalTime;

    /**
     * 门店名称
     */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /**
     * 业务员名称
     */
    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;


    /**
     * 门店经度
     */
    @Excel(name = "门店经度")
    @ApiModelProperty(value = "门店经度")
    private BigDecimal branchLongitude;

    /**
     * 门店纬度
     */
    @Excel(name = "门店纬度")
    @ApiModelProperty(value = "门店纬度")
    private BigDecimal branchLatitude;
}
