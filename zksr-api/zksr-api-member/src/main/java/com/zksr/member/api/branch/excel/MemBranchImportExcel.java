package com.zksr.member.api.branch.excel;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.domain.ImportErrorVo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;

@Data
@ApiModel(description = "用于门店信息导入")
public class MemBranchImportExcel extends ImportErrorVo {

    @Excel(name = "门店编号", headerColor = IndexedColors.RED)
    private String branchNo;

    @Excel(name = "门店名称", headerColor = IndexedColors.RED)
    private String branchName;

    @Excel(name = "门店联系人", headerColor = IndexedColors.RED)
    private String contactName;

    @Excel(name = "联系电话", headerColor = IndexedColors.RED)
    private String contactPhone;

    @Excel(name = "联系地址(需详细到门牌号)", headerColor = IndexedColors.RED)
    private String branchAddr;

    @Excel(name = "渠道类型")
    private String channelName;

    @Excel(name = "是否启用")
    private String statusName;

    @Excel(name = "城市区域", headerColor = IndexedColors.RED)
    private String areaName;

    @Excel(name = "业务员", headerColor = IndexedColors.RED)
    private String colonelName;

    @Excel(name = "是否支持货到付款")
    private String hdfkSupportName;

    @Excel(name = "货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    @Excel(name = "备注")
    private String memo;

    @Excel(name = "平台城市分组")
    private String groupName;

    @Excel(name = "城市(省/市/县区)", headerColor = IndexedColors.RED)
    private String city;
}
