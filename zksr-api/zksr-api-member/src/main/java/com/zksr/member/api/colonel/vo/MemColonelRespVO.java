package com.zksr.member.api.colonel.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.domain.vo.account.PlatformSimpleBindVO;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;


/**
 * 业务员信息对象 mem_colonel
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Data
@ApiModel("业务员信息 - mem_colonel Response VO")
public class MemColonelRespVO {
    private static final long serialVersionUID = 1L;

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 业务员手机号 */
    @Excel(name = "业务员手机号")
    @ApiModelProperty(value = "业务员手机号")
    private String colonelPhone;

    /** 业务员名 */
    @Excel(name = "业务员名")
    @ApiModelProperty(value = "业务员名")
    private String colonelName;

    /** 业务员级别（职务） */
    @Excel(name = "业务员级别", readConverterExp = "职=务")
    @ApiModelProperty(value = "业务员级别")
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    @ApiModelProperty(value = "父业务员id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long pcolonelId;

    /** 性别（数据字典） */
    @Excel(name = "性别", readConverterExp = "数=据字典")
    @ApiModelProperty(value = "性别")
    private Integer sex;

    /** 状态 1正常 0停用 */
    @Excel(name = "状态 1正常 0停用")
    @ApiModelProperty(value = "状态 1正常 0停用")
    private Integer status;

    /** 出生日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "出生日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    /** 籍贯 */
    @Excel(name = "籍贯")
    @ApiModelProperty(value = "籍贯")
    private String birthplace;

    /** 入职日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @Excel(name = "入职日期", width = 30, dateFormat = YYYY_MM_DD)
    @ApiModelProperty(value = "入职日期")
    private Date entryDate;

    /** 学历(数据字典) */
    @Excel(name = "学历(数据字典)")
    @ApiModelProperty(value = "学历(数据字典)")
    private String edu;

    /** 身份证号 */
    @Excel(name = "身份证号")
    @ApiModelProperty(value = "身份证号")
    private String idcard;

    /** 提成系数 */
    @Excel(name = "提成系数")
    @ApiModelProperty(value = "提成系数")
    private BigDecimal percentageRate;

    /** 联系地址 */
    @Excel(name = "联系地址")
    @ApiModelProperty(value = "联系地址")
    private String contactAddr;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 是否是业务管理员（1:是，2：否） */
    @Excel(name = "是否是业务管理员", readConverterExp = "Y:是，N:否")
    @ApiModelProperty(value = "是否是业务管理员")
    private String isColonelAdmin;

    /** 部门 */
    @Excel(name = "部门")
    @ApiModelProperty(value = "部门")
    private Long deptId;

    /** APP下单改价（1:是，2：否） */
    @Excel(name = "APP下单改价", readConverterExp = "Y:是，N:否")
    @ApiModelProperty(value = "APP下单改价")
    private String appOrderPriceAdjust;

    /** APP退货改价（1:是，2：否） */
    @Excel(name = "APP退货改价", readConverterExp = "Y:是，N:否")
    @ApiModelProperty(value = "APP退货改价")
    private String appAfterPriceAdjust;

    /** 下单自动审核（1:是，2：否） */
    @Excel(name = "下单自动审核", readConverterExp = "Y:是，N:否")
    @ApiModelProperty(value = "下单自动审核")
    private String orderAutoApprove;

    /** 父业务员id */
    @Excel(name = "父业务员名称")
    @ApiModelProperty(value = "父业务员名称")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long pcolonelName;

    /** 用户ID */
    @Excel(name = "用户ID")
    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long userId;


    /** 银行名称 */
    @ApiModelProperty(value = "银行名称", required = true, example = "浦发银行")
    private String bankName;

    /** 银行支行 */
    @ApiModelProperty(value = "银行支行", required = true, example = "浦发银行麓谷支行")
    private String bankBranch;

    /** 银行卡号 */
    @ApiModelProperty(value = "银行卡号", required = true, example = "***************")
    private String accountNo;

    /** 用户账号 */
    @Excel(name = "业务员登录名称")
    @ApiModelProperty(value = "业务员登录名称")
    private String userName;


    /** 密码 */
    @Excel(name = "业务员密码")
    @ApiModelProperty(value = "业务员密码")
    private String password;

    /** 销售金额 */
    @Excel(name = "销售金额")
    @ApiModelProperty(value = "销售金额")
    private BigDecimal salesAmt = BigDecimal.ZERO;

    /** 某段时间内的拜访次数 */
    @Excel(name = "某段时间内的拜访次数")
    @ApiModelProperty(value = "某段时间内的拜访次数")
    private Long visitNum;

    /** 某段时间内的理货次数 */
    @Excel(name = "某段时间内的理货次数")
    @ApiModelProperty(value = "某段时间内的理货次数")
    private Long colonelTidyNum;

    /** 来源 APP PC */
    @ApiModelProperty(value = "来源")
    private String source;

    /** 出生日期 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "出生日期")
    private Date updateTime;

    /** 创建人 */
    @ApiModelProperty(value = "创建人")
    private String updateBy;


    /** 审核状态 */
    @ApiModelProperty(value = "0待审核 1审核通过 2审核不通过")
    private Integer auditState = 0;

    /** 审核备注 */
    @ApiModelProperty(value = "审核备注")
    private String auditMemo;

    /** 审核人 */
    @ApiModelProperty(value = "审核人")
    private String auditBy;

    /** 审核时间 */
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 发展人id */
    @ApiModelProperty(value = "发展人id")
    private String developPeopleId;

    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "最近一次拜访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastVisitDate;
    /** 发展人名称 */
    @ApiModelProperty(value = "发展人名称")
    private String developPeopleName;

    @ApiModelProperty("一级区域城市ID, 省市区关联(省份)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long firstAreaCityId;

    @ApiModelProperty("二级区域城市ID, 省市区关联(城市)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long secondAreaCityId;

    @ApiModelProperty("三级区域城市ID, 省市区关联(区域)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long threeAreaCityId;

    //支付账号配置
    /** 商户绑定信息 */
    @ApiModelProperty(value = "商户绑定信息")
    private List<PlatformSimpleBindVO> platformSimpleBindList;
}
