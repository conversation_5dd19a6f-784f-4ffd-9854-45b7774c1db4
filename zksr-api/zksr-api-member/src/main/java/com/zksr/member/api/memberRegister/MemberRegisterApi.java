package com.zksr.member.api.memberRegister;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.memberRegister.dto.MemberRegisterDTO;
import com.zksr.member.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
*
 *
* <AUTHOR>
* @date 2024/4/24 10:36
*/
@FeignClient(
        contextId = "remoteMemberRegisterApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface MemberRegisterApi {

    String PREFIX = ApiConstants.PREFIX + "/memberRegister";


    /**
     * 根据ID查询数据
     * @param memberRegisterId
     * @return
     */
    @GetMapping(PREFIX + "/getMemberRegisterById")
    public CommonResult<MemberRegisterDTO> getMemberRegisterById(@RequestParam("memberRegisterId") Long memberRegisterId);

    /**
     * 小程序用户注册新增用户注册信息
     * @param memberRegisterDTO
     * @return
     */
    @PostMapping(PREFIX + "/insertUserMemberRegister")
    public CommonResult<Boolean> insertUserMemberRegister(@RequestBody MemberRegisterDTO memberRegisterDTO);

    /**
     * 根据用户账号查询数据
     * @param userName
     * @return
     */
    @GetMapping(PREFIX + "/getMemberRegisterByUserName")
    public CommonResult<MemberRegisterDTO> getMemberRegisterByUserName(@RequestParam("sysCode") Long sysCode, @RequestParam("userName") String userName);
}
