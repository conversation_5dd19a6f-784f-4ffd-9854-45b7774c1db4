package com.zksr.member.api.member.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberDTO implements Serializable {


    /** 用户id */
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 平台商id */
    private Long sysCode;

    /** 登录token */
    private String loginToken;

    /** 用户手机号 */
    private String memberPhone;

    /** 用户名 */
    private String memberName;

    /** 微信unionid */
    private String wxUnionid;

    /** 头像 */
    private String avatar;

    /** 状态：1正常  0禁用 */
    private Integer status;

    /** 小程序openid */
    private String xcxOpenid;

    /** 注册业务员id */
    private Long registerColonelId;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate; 		 // 过期时间

    /** 用户账号 */
    private String userName;

    /** 用户密码 */
    private String password;

    @ApiModelProperty(value = "用户Id集合")
    private List<Long> memberIds;

    /** 是否为店长用户 */
    @Excel(name = "是否为店长用户")
    private Integer isShopManager;

    /** 父ID */
    @Excel(name = "父ID")
    private Long pid;

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty("公众号openid")
    private String publishOpenid;

    @ApiModelProperty("是否是业务员 1：是 0：否")
    private Integer isColonel;

    @ApiModelProperty("关联业务员ID")
    private Long relateColonelId;

    @ApiModelProperty("设备ID ")
    private String deviceId;

    public MemberDTO(Long sysCode, Integer status, List<Long> memberIds) {
        this.sysCode = sysCode;
        this.status = status;
        this.memberIds = memberIds;
    }
}
