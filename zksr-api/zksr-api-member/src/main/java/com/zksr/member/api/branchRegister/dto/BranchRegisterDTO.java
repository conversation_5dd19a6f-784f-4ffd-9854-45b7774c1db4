package com.zksr.member.api.branchRegister.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 * 门店注册信息
* <AUTHOR>
* @date 2024/4/23 19:17
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchRegisterDTO {

    /** 门店注册信息ID */
    private Long branchRegisterId;

    /** 平台商id */
    private Long sysCode;

    /** 门店名称 */
    private String branchName;

    /** 联系人 */
    private String contactName;

    /** 联系电话 */
    private String contactPhone;

    /** 城市id */
    private Long areaId;

    /** 门店地址 */
    private String branchAddr;

    /** 经度 */
    private BigDecimal longitude;

    /** 纬度 */
    private BigDecimal latitude;

    /** 业务员id */
    private Long colonelId;

    /** 渠道id */
    private Long channelId;

    /** 门头照 */
    private String branchImages;

    /** 审核人 */
    private Long approveMan;

    /** 审核标识 */
    private Integer approveFlag;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date approveDate;

    /** 门店自动审核标识 */
    private Integer branchApproveFlag;

    /** 备注 */
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 用户类型 0 新用户 1老用户 */
    private Integer userType;

    /** 会员ID */
    private Long memberId;

    /** 电子围栏入驻商ID信息，以,逗号间隔 */
    private String supplierIds;

    @ApiModelProperty("三级区域城市ID, 省市区关联")
    private Long threeAreaCityId;


    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    private String districtName;
}
