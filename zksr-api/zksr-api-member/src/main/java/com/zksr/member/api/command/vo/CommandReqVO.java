package com.zksr.member.api.command.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@ApiModel("操作指令 -  CommandReqVO ")
public class CommandReqVO {
    /** 主键ID */
    @ApiModelProperty(value = "主键ID")
    private Long commandId;

    /** 平台商ID */
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    /** 0-指令锚点，1-普通指令 */
    @ApiModelProperty(value = "0-指令锚点，1-普通指令")
    private Integer commandLevel;

    /** 指令锚点id，仅普通指令 */
    @ApiModelProperty(value = "指令锚点id，仅普通指令")
    private Long pid;

    /** 指令有效期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "指令有效期")
    private Date commandDate;

    /** 指令类型（数据字典）(加单、拜访等) */
    @ApiModelProperty(value = "指令类型 1-加单；2-拜访")
    private Integer commandType;

    /** 0-作废 1-进行中  2-完成  */
    @ApiModelProperty(value = "0-作废 1-进行中  2-完成 ")
    private Integer status;

    /** 指令发布角色 colonel-业务员； branch-门店 */
    @ApiModelProperty(value = "指令发布角色 colonel-业务员； branch-门店")
    private String pubMerchantType;

    /** 指令发布人id */
    @ApiModelProperty(value = "指令发布人id")
    private Long pubId;

    /** 指令执行角色 branch-门店 */
    @ApiModelProperty(value = "指令执行角色 branch-门店")
    private String execMerchantType;

    /** 指令执行人id */
    @ApiModelProperty(value = "指令执行人id")
    private Long execId;

    /** 执行结果 */
    @ApiModelProperty(value = "执行结果")
    private String execRes;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String memo;
}
