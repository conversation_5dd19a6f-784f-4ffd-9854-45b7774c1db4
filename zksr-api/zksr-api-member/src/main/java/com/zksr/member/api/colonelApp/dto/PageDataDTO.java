package com.zksr.member.api.colonelApp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
*
 * 业务员APP 首页信息DTO
* <AUTHOR>
* @date 2024/5/11 15:47
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class PageDataDTO {

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 平台商ID */
    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    /** 下单数量 */
    @ApiModelProperty(value = "下单数量")
    private Long orderQty;

    /** 业务提成 */
    @ApiModelProperty(value = "业务提成")
    private BigDecimal percentageAmt;

    /** 门店下单金额 */
    @ApiModelProperty(value = "门店下单金额")
    private BigDecimal branchOrderAmt;

    /** 门店退货金额 */
    @ApiModelProperty(value = "门店退货金额")
    private BigDecimal branchRefundAmt;

    /** 拜访门店数量 */
    @ApiModelProperty(value = "拜访门店数量")
    private Long visitQty;

    /** 拜访门店ID列表 */
    @ApiModelProperty(value = "拜访门店ID列表")
    private Set<Long> visitBranchIds;

    /** 拓店数量 */
    @ApiModelProperty(value = "拓店数量")
    private Long addBranchQty;

    /** 动销门店数量 */
    @ApiModelProperty(value = "动销门店数量")
    private Long saleBranchQty;

    /** 动销门店ID列表 */
    @ApiModelProperty(value = "动销门店ID列表")
    private Set<Long> saleBranchIds;

    /** 下单数量（业务员代客下单） */
    @ApiModelProperty(value = "业务下单数量（业务员代客下单）")
    private Long businessOrderQty;

    /** 业务下单金额（业务员代客下单） */
    @ApiModelProperty(value = "业务下单金额（业务员代客下单）")
    private BigDecimal businessOrderAmt;

}
