package com.zksr.member.api.command.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ApiModel("业务员App-客户列表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CommandPageReqVO extends PageParam {

    @ApiModelProperty(value = "门店Id")
    private Long branchId;

    @ApiModelProperty(value = "状态： 0：待加单；1：加单中；2：加单完成")
    private Integer commandState;

    @ApiModelProperty(value = "查询年月日;字符串格式（yyyy-mm-dd）", hidden = true)
    private String searchDate;

    @ApiModelProperty(value = "门店ID集合，用于只查询当前业务员下管理的门店", hidden = true)
    private List<Long> branchIds;

    @ApiModelProperty(value = "指令发布角色 branch-门店；colonel—业务员")
    private String pubMerchantType;


}
