package com.zksr.member.api.member;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.member.dto.MemMemberSaveReqVO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.member.api.member.vo.MemberUpdatePwdVO;
import com.zksr.member.api.member.vo.MemberUpdateUserInfoVO;
import com.zksr.member.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(
        contextId = "remoteMemberApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface MemberApi {

    String PREFIX = ApiConstants.PREFIX + "/member";

    @PostMapping(PREFIX + "/register")
    public CommonResult<MemberDTO> memberRegister(@RequestBody MemMemberSaveReqVO createReqVO);

    @GetMapping(value = "/getInfoByMobileAndOpenid")
    public CommonResult<MemberDTO> getInfoByMobileAndOpenid(@RequestParam(name = "sysCode", required = false) Long sysCode,
                                                            @RequestParam(name = "mobile", required = false) String mobile,
                                                            @RequestParam(name = "openid", required = false) String openid,
                                                            @RequestParam(name = "userName", required = false) String userName);

    /**
     * 根据用户ID查询用户信息
     * @param memberId
     * @return
     */
    @GetMapping(value = "/getMemBerByMemberId")
    public CommonResult<MemberDTO> getMemBerByMemberId(@RequestParam("memberId")Long memberId);


    /**
     * 修改用户的默认门店
     * @param memberId
     * @param branchId
     * @param sysCode
     * @return
     */
    @PostMapping(PREFIX +  "/updateDefaultBranch")
    public CommonResult<Boolean> updateDefaultBranch(@RequestParam("memberId") Long memberId,@RequestParam("branchId")Long branchId,@RequestParam("sysCode")Long sysCode);

    /**
     * 获取半年内所有已过期的用户信息
     *
     * @param sysCode@return
     */
    @GetMapping(PREFIX + "/getAllExpirationDateMemberList")
    CommonResult<List<MemberDTO>> getAllExpirationDateMemberList(@RequestParam("sysCode")Long sysCode);

    /**
     * 批量修改用户的状态
     * @param memberDTO
     * @return
     */
    @PostMapping(PREFIX +  "/updateStatusByMemberIds")
    CommonResult<Boolean> updateStatusByMemberIds(@RequestBody MemberDTO memberDTO);

    /**
     * 新增用户的子用户
     * @param memberDTO
     * @return
     */
    @PostMapping(PREFIX +  "/insertChildUser")
    CommonResult<Boolean> insertChildUser(@RequestBody MemberDTO memberDTO);

    /**
     * 获取用户的子用户列表
     *
     * @param memberId
     * @return
     */
    @GetMapping(PREFIX + "/childUserList")
    CommonResult<List<MemberDTO>>  childUserList(@RequestParam("memberId")Long memberId);


    /**
     *  更新用户 login_token
     * @param memberDTO
     * @return
     */
    @PostMapping(PREFIX +  "/updateMemberToken")
    CommonResult<Boolean> updateMemberToken(@RequestBody MemberDTO memberDTO);

    /**
     * 更新用户公众号openid
     * @param memberId      用户ID
     * @param publishOpenid 公众号openid
     */
    @PutMapping(PREFIX +  "/updateMemberPublishOpenid")
    CommonResult<Boolean> updateMemberPublishOpenid(@RequestParam("memberId") Long memberId, @RequestParam("publishOpenid") String publishOpenid);

    /**
     *  更新用户 密码
     * @param memberUpdatePwdVO
     * @return
     */
    @PostMapping(PREFIX +  "/updateMemberPwd")
    CommonResult<Boolean> updateMemberPwd(@RequestBody MemberUpdatePwdVO memberUpdatePwdVO);

    /**
     *  更新用户 信息
     * @param memberUpdateUserInfoVO
     * @return
     */
    @PostMapping(PREFIX +  "/updateMemberUserInfoByMemberId")
    CommonResult<Boolean> updateMemberUserInfoByMemberId(@RequestBody MemberUpdateUserInfoVO memberUpdateUserInfoVO);


    /**
     * 根据业务员ID查询业务员用户信息
     * @param colonelId 业务员ID
     * @param sysCode 平台商ID
     * @return
     */
    @GetMapping(value = "/getMemBerByColonelId")
    public CommonResult<MemberDTO> getMemBerByColonelId(@RequestParam("colonelId")Long colonelId, @RequestParam("sysCode")Long sysCode);

    /**
     * 创建业务员商城用户账户
     * @param memberDTO
     * @return
     */
    @PostMapping(PREFIX +  "/createColonelMember")
    public CommonResult<MemberDTO> createColonelMember(@RequestBody MemberDTO memberDTO);

    /**
     *  更新用户设备ID
     * @param memberDTO
     * @return
     */
    @PostMapping(PREFIX +  "/updateMemberDeviceId")
    CommonResult<Boolean> updateMemberDeviceId(@RequestBody MemberDTO memberDTO);
}
