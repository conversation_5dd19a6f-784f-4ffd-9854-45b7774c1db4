package com.zksr.member.enums;

import com.zksr.common.core.exception.ErrorCode;

/**
 * Bpm 错误码枚举类
 * <p>
 * bpm 系统，使用 1-103-000-000 段
 */
public interface ErrorCodeConstants {

    // ==========  通用流程处理 模块 1-009-000-000 ==========
    ErrorCode HIGHLIGHT_IMG_ERROR = new ErrorCode(1_103_000_002, "获取高亮流程图异常");


    // ==========  member 模块通用 1-103-001-000 ==========
    ErrorCode MEM_COLONEL_RELATION_NOT_EXISTS = new ErrorCode(1_103_001_001, "业务员关系不存在");
    ErrorCode MEM_COLONEL_NOT_EXISTS = new ErrorCode(1_103_001_002, "业务员信息不存在");
    ErrorCode MEM_COLONEL_TARGET_NOT_EXISTS = new ErrorCode(1_103_001_003, "业务员目标设置不存在");
    ErrorCode MEM_COLONEL_TARGET_EXISTS = new ErrorCode(1_103_001_003, "业务员目标设置已存在");
    ErrorCode MEM_COLONEL_VISIT_LOG_NOT_EXISTS = new ErrorCode(1_103_001_004, "业务员拜访日志不存在");
    ErrorCode MEM_DISPLAY_TYPE_NOT_EXISTS = new ErrorCode(1_103_001_005, "陈列类型不存在");
    ErrorCode MEM_DISPLAY_PLAN_NOT_EXISTS = new ErrorCode(1_103_001_006, "陈列计划不存在");
    ErrorCode MEM_DISPLAY_PLAN_COUPON_NOT_EXISTS = new ErrorCode(1_103_001_007, "陈列计划优惠明细不存在");
    ErrorCode MEM_DISPLAY_DELIVERY_NOT_EXISTS = new ErrorCode(1_103_001_008, "陈列计划兑付不存在");
    ErrorCode MEM_DISPLAY_REGISTER_NOT_EXISTS = new ErrorCode(1_103_001_103, "陈列计划打卡记录不存在");
    ErrorCode MEM_BRANCH_NOT_EXISTS = new ErrorCode(1_103_001_010, "门店信息不存在");
    ErrorCode MEM_BRANCH_USER_NOT_EXISTS = new ErrorCode(1_103_001_011, "门店用户关联关系不存在");
    ErrorCode MEM_MEMBER_NOT_EXISTS = new ErrorCode(1_103_001_012, "用户信息不存在");
    ErrorCode MEM_COLONEL_DC_COLONEL_ROLE_IS_NULL = new ErrorCode(1_103_001_013, "运营商的业务员角色权限不存在");
    ErrorCode MEM_COLONEL_DC_COLONEL_IS_NULL = new ErrorCode(1_103_001_014, "该运营商的业务员账号信息不存在");
    ErrorCode MEM_COLONEL_DC_COLONEL_NOT_MATE = new ErrorCode(1_103_001_015, "该运营商的业务员账号与登陆账号不匹配");
    ErrorCode MEM_COLONEL_DC_COLONEL_IS_FAIL = new ErrorCode(1_103_001_015, "新增业务员账号失败");
    ErrorCode MEM_MEMBER_USERNAME_IS_EXISTS = new ErrorCode(1_103_001_016, "用户账号已存在");
    ErrorCode MEM_MEMBER_PASSWORD_NOT_LENGTH = new ErrorCode(1_103_001_017, "用户密码必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
    ErrorCode MEM_MEMBER_USERNAME_CHECK_ERR = new ErrorCode(1_103_001_016, "用户账号只能是数字且长度只能是11位！");
    ErrorCode MEM_BRANCH_REGISTER_NOT_EXISTS = new ErrorCode(1_103_001_018, "门店注册信息不存在");
    ErrorCode MEM_MEMBER_REGISTER_NOT_EXISTS = new ErrorCode(1_103_001_019, "用户注册信息不存在");
    ErrorCode MEM_BRANCH_IS_EXISTS = new ErrorCode(1_103_001_020, "门店信息已存在");
    ErrorCode MEM_COLONEL_TIDY_NOT_EXISTS = new ErrorCode(1_103_001_021, "理货信息不存在");
    ErrorCode MEM_MEMBER_BRANCH_REGISTER_NOT_EXISTS = new ErrorCode(1_103_001_22, "老用户扩店，手机号未获取到用户信息");
    ErrorCode NEW_BRANCH_AREA_NOT_DC = new ErrorCode(1_103_001_023, "新销售城市无绑定运营商");
    ErrorCode NEW_BRANCH_AREA_DC_CHANGE = new ErrorCode(1_103_001_024, "修改后的销售城市与目前的销售城市运营商不一致");

    ErrorCode MEM_MEMBER_BRANCH_CHECK_SYNC_SUPPLIER_OPENSOURCE = new ErrorCode(1_103_001_23, "该入驻商未对接第三方系统，无法门店信息初始化");

    ErrorCode MEM_MEMBER_BRANCH_CHECK_SYNC_SUPPLIER_TEMPLATE = new ErrorCode(1_103_001_24, "该入驻商未配置推送门店信息可视化接口，无法门店信息初始化");

    ErrorCode MEM_MEMBER_BRANCH_CHECK_SYNC_AREA_LOCK = new ErrorCode(1_103_001_25, "该入驻商近十分钟内已推送了该区域下的门店信息，无法重复推送");
    ErrorCode MEM_BRANCH_COLONEL_NOT_AREA = new ErrorCode(1_103_001_26, "门店与业务员不在同一区域无法修改");
    ErrorCode MEM_BRANCH_AREA_NOT_EXISTS = new ErrorCode(1_103_001_026, "区域信息不存在");
    ErrorCode MEM_BRANCH_COLONEL_NOT_EXISTS = new ErrorCode(1_103_001_027, "业务员信息不存在");
    ErrorCode MEM_BRANCH_CHANNEL_NOT_EXISTS = new ErrorCode(1_103_001_028, "渠道信息不存在");
    ErrorCode CATEGORY_NOT_DELETE = new ErrorCode(1_103_001_031, "类别下商品有参与过下单");
    ErrorCode CATEGORY_NOT_DELETE_HAVE_SON = new ErrorCode(1_103_001_033, "存在子类别无法操作");
    ErrorCode CATEGORY_NOT_DELETE_HAVE_SHELF = new ErrorCode(1_103_001_033, "类别存在上架商品");
    ErrorCode MEM_BRANCH_CHANNEL_NOT_STOP = new ErrorCode(1_103_001_029, "渠道有门店使用");

    ErrorCode MEM_BRANCH_CHANNEL_AREA__NOT_EXISTS = new ErrorCode(1_103_001_029, "区域【{}】下不存在业务员【{}】");

    // ==========  member 导入 1-103-002-000 ==========
    ErrorCode MEM_BRANCH_IMPORT_EMPTY = new ErrorCode(1_103_002_001, "导入门店信息为空");
    ErrorCode MEM_COLONEL_IMPORT_EMPTY = new ErrorCode(1_103_002_002, "导入业务员信息为空");

    // ==========  业务员信息 1-103-003-000 ==========
    ErrorCode MEM_COLONEL_BIND_PUBLISH_KEY_ERR = new ErrorCode(1_103_003_001, "业务员绑定key无效");

    // ==========  搜索词库 1-103-004-000 ==========
    ErrorCode MEM_SEARCH_WORD_NOT_EXISTS = new ErrorCode(1_103_004_001, "搜索词库不存在");

    // ==========  门店生命周期 1-103-005-000 ==========
    ErrorCode MEM_BRANCH_LIFECYCLE_ZIP_NOT_EXISTS = new ErrorCode(1_103_005_001, "门店生命周期不存在");

    ErrorCode MEM_BRANCH_USER_CREATE_ERR  = new ErrorCode(1_103_006_001, "门店创建成功，账号创建失败。账号创建失败原因:{msg}");

    ErrorCode MEM_MEMBER_PARAM_CONVERT = new ErrorCode(1_103_006_002, "参数转换错误");
    ErrorCode MEM_MEMBER_PARAM_REPEAT = new ErrorCode(1_103_006_003, "该门店已存在相同手机号用户");
    ErrorCode BALANCE_PAY_NOT_OPEN = new ErrorCode(1_103_005_002, "平台商关闭了余额管理，暂无法操作");

    ErrorCode BALANCE_AMT_NOT_ENOUGH = new ErrorCode(1_103_005_003, "退款金额不能大于剩余可用余额");

    ErrorCode TRADE_NO_NOT_EMPTY = new ErrorCode(1_103_005_004, "交易单号不能为空");

    ErrorCode OP_AMT_NOT_NULL = new ErrorCode(1_103_005_005, "输入金额必填，且值必须大于0");

    ErrorCode BRANCH_ID_NOT_EMPTY = new ErrorCode(1_103_005_006, "门店ID不能为空");

    ErrorCode BRANCH_DISABLE_NOT_ALLOW_OPER = new ErrorCode(1_103_005_007, "门店已停用，暂无法操作");

    ErrorCode OP_AMT_GT_MAX_PRICE = new ErrorCode(1_103_005_008, "输入金额不能大于999999.99");

}
