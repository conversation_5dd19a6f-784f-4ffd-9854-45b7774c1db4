package com.zksr.member.api.branchRegister;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.branchRegister.dto.BranchRegisterDTO;
import com.zksr.member.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
*
 *
* <AUTHOR>
* @date 2024/4/24 10:36
*/
@FeignClient(
        contextId = "remoteBranchRegisterApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface BranchRegisterApi {

    String PREFIX = ApiConstants.PREFIX + "/branchRegister";


    /**
     * 根据ID查询数据
     * @param branchRegisterId
     * @return
     */
    @GetMapping(PREFIX + "/getbranchRegisterById")
    public CommonResult<BranchRegisterDTO> getBranchRegisterById(@RequestParam("branchRegisterId") Long branchRegisterId);

    /**
    * @Description: 添加门店
    * @Author: liuxingyu
    * @Date: 2024/6/7 16:29
    */
    @PutMapping(PREFIX + "/addBranch")
    CommonResult<Long> addBranch(@RequestBody BranchRegisterDTO registerDTO);

    /**
     * 根据 用户名称 查询是否存在已提交的门店申请信息
     * @param userName
     * @return
     */
    @GetMapping(PREFIX + "/getBranchRegisterByUserName")
    public CommonResult<Boolean> getBranchRegisterByUserName(@RequestParam("userName") String userName);
}
