package com.zksr.member.api.command.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("操作指令 - 业务员加单 CommandAddOrderVO")
public class CommandAddOrderVO {
    @ApiModelProperty(value = "门店Id")
    private Long branchId;

    @ApiModelProperty(value = "类型 1：增加锚点指令；2：增加普通指令；3：更新普通执行执行结果（加入购物车）; 4：更新普通执行执行结果（下单更新）")
    private Integer type;

    @ApiModelProperty(value = "指令id")
    private Long commandId;

    @ApiModelProperty(value = "操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @ApiModelProperty(value = "订单下单金额")
    private BigDecimal orderAmt;

    @ApiModelProperty(value = "订单下单sku数量")
    private Long orderSkuQty;

    @ApiModelProperty(value = "推送加单金额")
    private BigDecimal pushAmt;

    @ApiModelProperty(value = "推送加单sku数量")
    private Long pushSkuQty;

    @ApiModelProperty(value = "加单备注")
    private String memo;

    @ApiModelProperty(value = "指令id集合；订单下单更新执行专用")
    private List<Long> commandIds;
}
