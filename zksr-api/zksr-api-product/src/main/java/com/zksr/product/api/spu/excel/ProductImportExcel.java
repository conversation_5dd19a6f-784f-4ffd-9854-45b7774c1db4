package com.zksr.product.api.spu.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 产品基础信息导入对象
 * @date 2024/4/25 14:20
 */
@Data
@ApiModel(description = "用于商品基本信息导入")
public class ProductImportExcel {

//    @Excel(name = "商品编号", headerColor = IndexedColors.RED, isExport = false)
//    private String spuNo;

    @Excel(name = "商品名称", headerColor = IndexedColors.RED)
    private String spuName;

    @Excel(name = "三级类别编号", headerColor = IndexedColors.RED)
    private String categoryId;

    @Excel(name = "品牌名称")
    private String brandName;

//    @Excel(name = "产地", headerColor = IndexedColors.RED)
//    private String originPlace;

    @Excel(name = "最旧生产日期")
    private Date oldestDate;

    @Excel(name = "最新生产日期")
    private Date latestDate;

    @Excel(name = "保质期")
    private Integer expirationDate;

//    @Excel(name = "备注")
//    private String memo;

    @Excel(name = "商品详情(描述)")
    private String details;

    /**===================================================  规格  ======================================== */
    @Excel(name = "小单位", prompt = "sys_prdt_unit字典", headerColor = IndexedColors.RED)
    private String unit;

    @Excel(name = "小单位条码", headerColor = IndexedColors.RED)
    private String barcode;

    @Excel(name = "小单位库存数量", headerColor = IndexedColors.RED)
    private Long stock;

    @Excel(name = "限购")
    private Long maxOq;

    @Excel(name = "订货组数")
    private Long jumpOq;

    @Excel(name = "起订量")
    private Long minOq;

    @Excel(name = "供货价", headerColor = IndexedColors.RED)
    private BigDecimal costPrice;

    @Excel(name = "标准价", headerColor = IndexedColors.RED)
    private BigDecimal markPrice;

    @Excel(name = "建议零售价")
    private BigDecimal suggestPrice;

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    private String auxiliarySpuNo;

//    @Excel(name = "参考进价")
//    private BigDecimal referencePrice;
//
//    @Excel(name = "参考售价")
//    private BigDecimal referenceSalePrice;
}
