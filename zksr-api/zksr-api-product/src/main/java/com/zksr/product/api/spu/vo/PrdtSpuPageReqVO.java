package com.zksr.product.api.spu.vo;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import lombok.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;

import static com.zksr.common.core.utils.DateUtils.*;

/**
 * 商品SPU对象 prdt_spu
 *
 * <AUTHOR>
 * @date 2024-02-29
 */
@ApiModel("商品SPU - prdt_spu分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtSpuPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 商品SPU_id */
    @ApiModelProperty(value = "商品SPU_id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 平台商管理分类id */
    @Excel(name = "平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long catgoryId;

    /** 平台商品牌id */
    @Excel(name = "平台商品牌id")
    @ApiModelProperty(value = "平台商品牌id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /** 封面视频（url） */
    @Excel(name = "封面视频", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面视频")
    private String thumbVideo;

    /** 详情页轮播（json） */
    @Excel(name = "详情页轮播", readConverterExp = "j=son")
    @ApiModelProperty(value = "详情页轮播")
    private String images;

    /** 详情信息(富文本) */
    @Excel(name = "详情信息(富文本)")
    @ApiModelProperty(value = "详情信息(富文本)")
    private String details;

    /** 库存数量 */
    @Excel(name = "库存数量")
    @ApiModelProperty(value = "库存数量")
    private Long stock;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Long isDelete = 0L;

    /** 最旧生产日期 */
    @Excel(name = "最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最旧生产日期")
    private Date oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @Excel(name = "最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最新生产日期")
    private Date latestDate; 		 // 最新生产日期

    /** 是否开启多规格 1-是 0-否 */
    @Excel(name = "是否开启多规格 1-是 0-否")
    @ApiModelProperty(value = "是否开启多规格 1-是 0-否")
    private Long isSpecs;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)")
    private Long status;

    /** 产地 */
    @Excel(name = "产地")
    @ApiModelProperty(value = "产地")
    private String originPlace;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** keyword */
    @Excel(name = "搜索关键字")
    @ApiModelProperty(value = "搜索关键字")
    private String keyword;

    /** 国际条码 */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码")
    private String barcode;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典")
    private Long unit;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    @ApiModelProperty(value = "属性数组，JSON 格式")
    private String properties;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价")
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    @ApiModelProperty(value = "建议零售价")
    private BigDecimal suggestPrice;

    /** 保质期 */
    @Excel(name = "保质期")
    @ApiModelProperty(value = "保质期")
    private Integer expirationDate;		 // 保质期

    /** 参考进价 */
    @Excel(name = "参考进价")
    @ApiModelProperty(value = "参考进价")
    private BigDecimal referencePrice;

    /** 参考售价 */
    @Excel(name = "参考售价")
    @ApiModelProperty(value = "参考售价")
    private BigDecimal referenceSalePrice;

    /** 商品SKU_id */
    @ApiModelProperty(value = "商品SKU_id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 上下架状态 */
    @Excel(name = "上下架状态")
    @ApiModelProperty(value = "上下架状态")
    private Integer shelfStatus;

    /** 上下架状态 */
    @Excel(name = "城市上下架状态",readConverterExp = "1=城市未上架,2=城市已上架")
    @ApiModelProperty(value = "城市上下架状态 0城市未上架 1城市已上架")
    private Integer areaShelfStatus;

    @Excel(name = "全国上下架状态",readConverterExp = "1=全国未上架,2=全国已上架")
    @ApiModelProperty(value = "全国上下架状态 0全国未上架 1全国已上架")
    private Integer supplierShelfStatus;

    /** 平台商管理分类名称 */
    @Excel(name = "平台商管理分类名称")
    @ApiModelProperty(value = "平台商管理分类名称")
    private String catgoryName;

    /** 平台商品牌名称 */
    @Excel(name = "平台商品牌名称")
    @ApiModelProperty(value = "平台商品牌名称")
    private String brandName;

    /** 上下架类型 */
    @Excel(name = "上下架类型")
    @ApiModelProperty(value = "上下架类型0 入驻商（全国）1是城市")
    private Integer ItemType;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典（sys_prdt_unit）", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    /** 中单位换算数量（换算成最小单位）） */
    @Excel(name = "中单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
    private Long midSize;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 大单位换算数量（换算成最小单位）） */
    @Excel(name = "大单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
    private Long largeSize;

    /** 是否开启联动换算 1-是 0-否 */
    @Excel(name = "是否开启联动换算 1-是 0-否")
    @ApiModelProperty(value = "是否开启联动换算 1-是 0-否")
    private Integer isLinkage;

    /** 国际条码 */
    @Excel(name = "国际条码（中单位）")
    @ApiModelProperty(value = "国际条码（中单位）")
    private String midBarcode;

    @Excel(name = "中单位-标准价")
    @ApiModelProperty(value = "中单位-标准价")
    private BigDecimal midMarkPrice;

    @Excel(name = "中单位-建议零售价")
    @ApiModelProperty(value = "中单位-建议零售价")
    private BigDecimal midSuggestPrice;

    @Excel(name = "中单位-成本价(供货价)")
    @ApiModelProperty(value = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    @Excel(name = "大单位-国际条码")
    @ApiModelProperty(value = "国际条码（大单位）")
    private String largeBarcode;

    @Excel(name = "大单位-标准价")
    @ApiModelProperty(value = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    @Excel(name = "大单位-建议零售价")
    @ApiModelProperty(value = "大单位-建议零售价")
    private BigDecimal largeSuggestPrice;

    @Excel(name = "大单位-成本价(供货价)")
    @ApiModelProperty(value = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    @ApiModelProperty("入驻商ID集合")
    private List<Long> supplierIdList;

    @ApiModelProperty("小于有效库存数的")
    private Integer validStockQty;

    @ApiModelProperty("是否有主图筛选条件，true 表示筛选有主图的，false 表示筛选无主图的")
    private Boolean hasMainImage;

    @ApiModelProperty("库存最小值")
    private Integer stockMin;

    @ApiModelProperty("库存最大值")
    private Integer stockMax;

    /** SPU辅助的商品编号 */
    @Excel(name = "SPU辅助的商品编号")
    @ApiModelProperty(value = "SPU辅助的商品编号")
    private String auxiliarySpuNo;

    /** 外部来源商品编号 */
    @Excel(name = "外部来源商品编号")
    @ApiModelProperty(value = "外部来源商品编号")
    private String sourceNo;

    /** 关联关键词 */
    @Excel(name = "关联关键词")
    @ApiModelProperty(value = "关联关键词")
    private String keywords;

    /** 只查询关联关键词的spu */
    @Excel(name = "只查询关联关键词的spu")
    private Boolean hasKeywords;

    @ApiModelProperty("SpuID集合")
    private List<Long> spuIdList;

    @Excel(name = "区域城市ID")
    @ApiModelProperty(value = "区域城市ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    @ApiModelProperty("商品计价方式类型(字典：spu_pricing_way) 默认为普通商品 1：普通商品 2：称重商品")
    private Integer pricingWay;

    /** 商品SPU编号 */
    @ApiModelProperty("商品SPU编号集合")
    @Excel(name = "商品SPU编号集合")
    private List<String> spuNoList;
}
