package com.zksr.product.api.saleClass.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;


/**
 * 城市展示分类对象 prdt_area_class
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("城市展示分类 - prdt_area_class Response VO")
public class SaleClassExportVo extends PageParam  {
    private static final long serialVersionUID = 1L;

    @Excel(name = "类别编号")
    @ApiModelProperty(value = "类别编号")
    private Long saleClassId;

    @Excel(name = "类别名称")
    @ApiModelProperty(value = "类别名称")
    private String name;

    @Excel(name = "上级类别")
    @ApiModelProperty(value = "上级类别")
    private String pidName;

    @Excel(name = "是否启用", readConverterExp = "1= 是,0= 否")
    @ApiModelProperty(value = "1-启用 0-未启用")
    private String status;

    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Long sort;

    @ApiModelProperty(value = "父id")
    private Long pid;

    @ApiModelProperty(value = "城市id")
    private Long areaId;

    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;


    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "分类图标")
    private String icon;

}
