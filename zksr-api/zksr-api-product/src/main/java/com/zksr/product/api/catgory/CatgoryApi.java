package com.zksr.product.api.catgory;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryIdDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.catgory.excel.PrdtCatgoryExcel;
import com.zksr.product.api.catgory.form.CategoryImportForm;
import com.zksr.product.api.catgory.vo.PrdtCatgoryPageVO;
import com.zksr.product.enums.ApiConstants;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(contextId = "catgoryApi", value = ApiConstants.NAME)
public interface CatgoryApi {

    String PREFIX = ApiConstants.PREFIX + "/catgory";

    /**
     * @Description: 根据管理分类Id获取管理分类
     * @Param: Long catgoryId
     * @return: CommonResult<CatgoryDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 10:24
     */
    @GetMapping(PREFIX + "/getCatgoryByCatgoryId")
    CommonResult<CatgoryDTO> getCatgoryByCatgoryId(@RequestParam("catgoryId") Long catgoryId);

    /**
     * 获取管理分类分润比例
     *
     * @param catgoryId 管理分类ID
     * @param areaId    区域城市ID
     * @return
     */
    @GetMapping(PREFIX + "/getCatgoryByCatgoryIdAndAreaId")
    CommonResult<CatgoryRateDTO> getCatgoryByIdAndAreaId(@RequestParam("catgoryId") Long catgoryId, @RequestParam("areaId") Long areaId);

    /**
     * @Description: 根据平台商获取平台管理分类
     * @Author: liuxingyu
     * @Date: 2024/5/7 11:11
     */
    @GetMapping("/getListBySysCode")
    CommonResult<List<CatgoryDTO>> getListBySysCode(@RequestParam("sysCode") Long sysCode);


    /**
     * 新增平台商管理分类
     */

    @PostMapping(PREFIX + "/addCategory")
    CommonResult<Long> add(@RequestBody CatgoryDTO catgoryDTO);

    /**
     * 修改平台商管理分类
     */

    @PutMapping(PREFIX + "/editCategory")
    CommonResult<Boolean> edit(@RequestBody CatgoryDTO catgoryDTO);

    /**
     * 删除平台商管理分类
     */

    @DeleteMapping(PREFIX + "/{classIds}")
    CommonResult<Boolean> remove(@PathVariable("classIds") Long[] classIds);

    /**
     *   根据入驻商ID获取绑定的管理类别
     * @param supplierId 入驻商ID
     * @return CatgoryDTO
     */
    @GetMapping(PREFIX + "/getCatgoryListBySupplierId")
    CommonResult<List<CatgoryDTO>> getCatgoryListBySupplierId(@RequestParam("supplierId") Long supplierId);


    /**
    * @Description: 获取平台管理类别一级Id
    * @Author: liuxingyu
    * @Date: 2024/5/16 17:01
    */
    @GetMapping("/getCatgoryFirstId")
    CommonResult<List<CatgoryIdDTO>> getCatgoryFirstId();

    /**
     * 获取平台商管理分类列表
     * @return
     */
    @GetMapping(PREFIX + "/getCatgoryList")
    CommonResult<List<CatgoryDTO>> getCatgoryList(@ApiParam(name = "catgoryId", value = "平台商管理分类id(非必传,返回数据剔除数据ID为传入的ID)")
                                                  @RequestParam(value = "catgoryId", required = false) Long catgoryId,
                                                  @RequestParam(value = "supplierId", required = false) Long supplierId);

    @PostMapping(PREFIX + "/importBaseCategoryEvent")
    CommonResult<String> importBaseCategoryEvent(@RequestBody CategoryImportForm categoryImportForm);

    @PostMapping(PREFIX + "/getPrdtCatgoryExcel")
    CommonResult<List<PrdtCatgoryExcel>> getPrdtCatgoryExcel(@RequestBody PrdtCatgoryPageVO pageVO);
}
