package com.zksr.product.api.yhdata.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货批次详情数据
 * @date 2024/12/12 14:06
 */
@Data
@ApiModel(description = "要货批次详情数据")
public class YhBatchItemVO {
    @ApiModelProperty("要货ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long yhId;

    @ApiModelProperty("本地上架商品")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaItemId;

    @ApiModelProperty("spuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty("skuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitType}
     */
    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位")
    private Integer mallUnitType;

    @ApiModelProperty("POS国条码")
    private String posBarcode;

    @ApiModelProperty("POS单位名称")
    private String posUnitName;

    @ApiModelProperty("要货数量")
    private Integer posSuggestQty;

    @ApiModelProperty("安全库存天数")
    private Integer posSafetyDays;

    @ApiModelProperty("安全库存")
    private Integer posSafetyStock;

    @ApiModelProperty("已下单, 未完成数量, 在途数量")
    private Long transitQty;

    @ApiModelProperty("昨日销量")
    private Integer posSalesQty;

    @ApiModelProperty("30天人均销量")
    private Integer pos30dayAvgSales;

    /** 上次补货时间 */
    @ApiModelProperty("上次补货时间")
    private String lastTime;

    /** 上次补货数量 */
    @ApiModelProperty("上次补货数量")
    private Integer lastSubmitQty;

    @ApiModelProperty("是否选中, 1-选中, 2-未选中")
    private Integer checked;

    /** 原始要货数量 */
    @ApiModelProperty("原始要货数量")
    private Integer sourceYhQty;

}

