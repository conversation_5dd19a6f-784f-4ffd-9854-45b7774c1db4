package com.zksr.product.api.material.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 活动素材
 * @date 2025/1/10 14:32
 */
@Data
@ApiModel(description = "活动素材")
public class MaterialDTO {

    /** 素材id */
    @ApiModelProperty("素材ID")
    private Integer materialId;

    /** 平台商id */
    @ApiModelProperty("平台商id")
    private Integer sysCode;

    /** 素材名称 */
    @ApiModelProperty("素材名称")
    private String name;

    /** 素材图片地址 */
    @ApiModelProperty("素材图片地址")
    private String img;

    /** 状态 1-启用 0-停用 */
    @ApiModelProperty("状态 1-启用 0-停用")
    private Integer status;
}
