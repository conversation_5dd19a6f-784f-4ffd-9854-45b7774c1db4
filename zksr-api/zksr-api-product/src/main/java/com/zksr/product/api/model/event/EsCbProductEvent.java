

package com.zksr.product.api.model.event;

import cn.hutool.core.collection.ListUtil;
import com.zksr.product.api.model.AbstractProductEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 上架事件
 * @date 2024/2/29 19:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EsCbProductEvent extends AbstractProductEvent {

    /**
     * 组合SPU集合
     */
    @ApiModelProperty("spuCombineId")
    private List<Long> spuCombineId;

    /**
     * 上架发布商品ID, 非商品ID
     */
    @ApiModelProperty("上架发布商品ID, 非商品ID")
    private List<Long> itemIds;

    public EsCbProductEvent(Long spuCombineId) {
        this.spuCombineId = ListUtil.toList(spuCombineId);
    }

    public EsCbProductEvent(List<Long> spuCombineId) {
        this.spuCombineId = spuCombineId;
    }
}
