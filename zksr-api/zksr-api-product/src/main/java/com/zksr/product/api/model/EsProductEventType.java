package com.zksr.product.api.model;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: es商品刷新事件类型
 * @date 2024/2/29 19:13
 */
public class EsProductEventType {
    // 本地商品上架发布事件
    public static final String LOCAL_RELEASE = "LOCAL_RELEASE";
    // 全国商品上架发布事件
    public static final String GLOBAL_RELEASE = "GLOBAL_RELEASE";
    // SPU 更新事件
    public static final String SPU_UPDATE = "SPU_UPDATE";
    // SKU 更新事件
    public static final String SKU_UPDATE = "SKU_UPDATE";
    // 删除数据
    public static final String REMOVE = "ES_PRODUCT_REMOVE";
    // 组合商品刷新
    public static final String CB_PRODUCT_UPDATE = "CB_PRODUCT_UPDATE";

    /**
     * 需要实现 @com.zksr.product.cache.handler.IProductEventHandler 对事件进行处理
     * 参考    @com.zksr.product.cache.handler.impl.LocalReleaseProductHandler 实现
     */
}
