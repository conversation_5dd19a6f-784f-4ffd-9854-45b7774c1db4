package com.zksr.product.api.sku.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
@NoArgsConstructor
public class SkuDTO {

    /** 商品sku_id */
    private Long skuId;

    /** 平台商id */
    private Long sysCode;

    /** 商品SPU_id */
    private Long spuId;

    /** 单位-数据字典（sys_prdt_unit） */
    private Long unit;

    /** 国际条码 */
    private String barcode;

    /** 属性数组，JSON 格式 */
    private String properties;

    /** 图片地址 */
    private String thumb;

    /** 库存数量 */
    private BigDecimal stock;

    /** 标准价 */
    private BigDecimal markPrice;

    /** 建议零售价 */
    private BigDecimal suggestPrice;

    /** 成本价 */
    private BigDecimal costPrice;

    /** 是否删除 1-是 0-否 */
    private Long isDelete;

    /** 状态(数据字典 sys_common_status) */
    private Long status;

    /** 保质期 */
    private Integer expirationDate;		 // 保质期

    /** 参考进价 */
    private BigDecimal referencePrice;

    /** 参考售价 */
    private BigDecimal referenceSalePrice;

    /** 起订 */
    private Long minOq;


    /** 订货组数 */
    private Long jumpOq;


    /** 限购 */
    private Long maxOq;

    /** 中单位-国际条码 */
    private String midBarcode;

    /** 中单位-标准价 */
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    private BigDecimal midCostPrice;

    /** 中单位-建议零售价 */
    private BigDecimal midSuggestPrice;

    /** 中单位-起订 */
    private Long midMinOq;

    /** 中单位-订货组数 */
    private Long midJumpOq;

    /** 中单位-限购 */
    private Long midMaxOq;

    /** 大单位-国际条码 */
    private String largeBarcode;

    /** 大单位-标准价 */
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    private BigDecimal largeCostPrice;

    /** 大单位-建议零售价 */
    private BigDecimal largeSuggestPrice;

    /** 大单位-起订 */
    private Long largeMinOq;

    /** 大单位-订货组数 */
    private Long largeJumpOq;

    /** 大单位-限购 */
    private Long largeMaxOq;

    /** 外部来源商品编号 */
    private String sourceNo;

    /** 外部来源商品编号 */
    private String source;

    @ApiModelProperty("销售分润占比, 最大0.29 = 29%")
    private BigDecimal saleTotalRate;



    /**
     * 已售数量, 库存 - 已售 = 剩余
     */
    private BigDecimal saleQty;

    /**
     * 已同步库存
     */
    private BigDecimal syncedQty;

    /** 最后库存更新时间 */
    @Excel(name = "最后库存更新时间")
    private Date lastUpdateTime;

    @ApiModelProperty("上架城市id")
    private Long areaId;

    @ApiModelProperty("城市小单位上架状态,0-未上架,1-已上架")
    private Integer areaMinShelfStatus;

    @ApiModelProperty("城市中单位上架状态,0-未上架,1-已上架")
    private Integer areaMidShelfStatus;

    @ApiModelProperty("城市大单位上架状态,0-未上架,1-已上架")
    private Integer areaLargeShelfStatus;

    @ApiModelProperty("全国小单位上架状态,0-未上架,1-已上架")
    private Integer supplierMinShelfStatus;

    @ApiModelProperty("全国中单位上架状态,0-未上架,1-已上架")
    private Integer supplierMidShelfStatus;

    @ApiModelProperty("全国大单位上架状态,0-未上架,1-已上架")
    private Integer supplierLargeShelfStatus;

    @ApiModelProperty("中单位状态")
    private Long midStatus;

    @ApiModelProperty("大单位状态")
    private Long largeStatus;

    @ApiModelProperty("小上架城市名称")
    private String minAreaName;

    @ApiModelProperty("中上架城市名称")
    private String midAreaName;

    @ApiModelProperty("大上架城市名称")
    private String largeAreaName;

    @ApiModelProperty("占用库存")
    private BigDecimal occupiedQty;

    /** 销售类型(0-零售、1-批发) */
    private Integer saleType;

    /** 零售价 */
    private java.math.BigDecimal retailPrice;

    /** 分润比例 百分比的小数表现形式，1%表示为0.01 */
    private java.math.BigDecimal profitRate;

    /** 分润金额 */
    private java.math.BigDecimal profitAmount;

    /** 中单位零售价 */
    private java.math.BigDecimal midRetailPrice;

    /** 中单位分润比例 百分比的小数表现形式，1%表示为0.01 */
    private java.math.BigDecimal midProfitRate;

    /** 中单位分润金额 */
    private java.math.BigDecimal midProfitAmount;

    /** 大单位零售价 */
    private java.math.BigDecimal largeRetailPrice;

    /** 大单位分润比例 百分比的小数表现形式，1%表示为0.01 */
    private java.math.BigDecimal largeProfitRate;

    /** 大单位分润金额 */
    private java.math.BigDecimal largeProfitAmount;

    public BigDecimal getMarkPrice(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return null;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midMinOq)) return midMarkPrice;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeMinOq)) return largeMarkPrice;
        return markPrice;
    }

    public BigDecimal getSuggestPrice(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return null;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midMinOq)) return midSuggestPrice;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeMinOq)) return largeSuggestPrice;
        return suggestPrice;
    }

    public Long getMinOq(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return null;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midMinOq)) return midMinOq;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeMinOq)) return largeMinOq;
        return Objects.isNull(minOq) ? null : minOq;
    }

    public Long getJumpOq(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return null;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midJumpOq)) return midJumpOq;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeJumpOq)) return largeJumpOq;
        return Objects.isNull(jumpOq) ? null : jumpOq;
    }

    public Long getMaxOq(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return null;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midMaxOq)) return midMaxOq;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeMaxOq)) return largeMaxOq;
        return Objects.isNull(maxOq) ? null : maxOq;
    }

    // 获得当前单位条码
    public String getUnitBarcode(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return null;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() ) return midBarcode;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue()) return largeBarcode;
        return Objects.isNull(barcode) ? null : barcode;
    }
}
