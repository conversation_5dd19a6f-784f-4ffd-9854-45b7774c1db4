package com.zksr.product.api.areaClass.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用于运营商展示类别信息导入
 * @date 2024/11/08 09:18
 */
@Data
@ApiModel(description = "用于运营商展示类别信息导入")
public class ProductAreaClassImportExcel {
    @Excel(name = "区域编号")
    private Long regionCode;  // 区域编号

    @Excel(name = "一级类别名称")
    private String primaryCategoryName;  // 一级类别名称

    @Excel(name = "二级类别名称")
    private String secondaryCategoryName;  // 二级类别名称

    @Excel(name = "三级类别名称")
    private String tertiaryCategoryName;  // 三级类别名称
}
