
package com.zksr.product.api.model.event;

import com.zksr.product.api.model.AbstractProductEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 上架事件
 * @date 2024/2/29 19:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EsRemoveProductEvent extends AbstractProductEvent {

    /**
     * SPU集合
     */
    @ApiModelProperty("spuIds")
    private List<Long> spuId;

    /**
     * skuId 集合
     */
    @ApiModelProperty("skuIds")
    private List<Long> skuId;

    /**
     * 上架商品集合
     */
    @ApiModelProperty("itemIds")
    private List<Long> itemIds;
}
