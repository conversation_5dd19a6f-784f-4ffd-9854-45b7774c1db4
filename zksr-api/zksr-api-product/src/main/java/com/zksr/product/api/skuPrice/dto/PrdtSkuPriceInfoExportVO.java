package com.zksr.product.api.skuPrice.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel("城市价格导出实体")
public class PrdtSkuPriceInfoExportVO  extends BaseEntity {

    @Excel(name = "价格编号")
    @ApiModelProperty(value = "价格编号")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuPriceId;

    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @Excel(name = "条码",headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "条码", example = "示例值")
    private String barcode;

    @Excel(name = "skuId",headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @Excel(name = "城市编号",headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "城市编号")
    private Long areaId;

    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    @Excel(name = "产品规格")
    @ApiModelProperty(value = "产品规格")
    private String specName;

    @Excel(name = "小单位")
    @ApiModelProperty(value = "小单位")
    private String minUnitName;

    @Excel(name = "中单位")
    @ApiModelProperty(value = "中单位")
    private String midUnitName;

    @Excel(name = "大单位")
    @ApiModelProperty(value = "大单位")
    private String largeUnitName;

    @Excel(name = "产品品牌")
    @ApiModelProperty(value = "产品品牌")
    private String brandName;

    @Excel(name = "管理类别")
    @ApiModelProperty(value = "管理类别")
    private String catgoryName;

    @Excel(name = "小单位-成本价(供货价)")
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    @Excel(name = "中单位-成本价(供货价)")
    @ApiModelProperty(value = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    @Excel(name = "大单位-成本价(供货价)")
    @ApiModelProperty(value = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价", example = "示例值")
    private BigDecimal markPrice;

    @Excel(name = "中单位-标准价")
    @ApiModelProperty(value = "中单位-标准价")
    private BigDecimal midMarkPrice;

    @Excel(name = "大单位-标准价")
    @ApiModelProperty(value = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    @Excel(name = "起订")
    @ApiModelProperty(value = "起订")
    private Long minOq;

    @Excel(name = "中单位-起订")
    @ApiModelProperty(value = "中单位-起订")
    private Long midMinOq;

    @Excel(name = "大单位-起订")
    @ApiModelProperty(value = "大单位-起订")
    private Long largeMinOq;

    @Excel(name = "订货组数")
    @ApiModelProperty(value = "订货组数")
    private Long jumpOq;

    @Excel(name = "中单位-订货组数")
    @ApiModelProperty(value = "中单位-订货组数")
    private Long midJumpOq;

    @Excel(name = "大单位-订货组数")
    @ApiModelProperty(value = "大单位-订货组数")
    private Long largeJumpOq;

    @Excel(name = "配送价1",headerColor = IndexedColors.RED)
    @ApiModelProperty(value = "配送价1")
    private BigDecimal salePrice1;

    /** 配送价2 */
    @Excel(name = "配送价2")
    @ApiModelProperty(value = "配送价2")
    private BigDecimal salePrice2;

    /** 配送价3 */
    @Excel(name = "配送价3")
    @ApiModelProperty(value = "配送价3")
    private BigDecimal salePrice3;

    /** 配送价4 */
    @Excel(name = "配送价4")
    @ApiModelProperty(value = "配送价4")
    private BigDecimal salePrice4;

    /** 配送价5 */
    @Excel(name = "配送价5")
    @ApiModelProperty(value = "配送价5")
    private BigDecimal salePrice5;

    /** 配送价6 */
    @Excel(name = "配送价6")
    @ApiModelProperty(value = "配送价6")
    private BigDecimal salePrice6;

    /** 中单位-配送价1 */
    @Excel(name = "中单位-配送价1")
    @ApiModelProperty(value = "中单位-配送价1")
    private BigDecimal midSalePrice1;

    /** 中单位-配送价2 */
    @Excel(name = "中单位-配送价2")
    @ApiModelProperty(value = "中单位-配送价2")
    private BigDecimal midSalePrice2;

    /** 中单位-配送价3 */
    @Excel(name = "中单位-配送价3")
    @ApiModelProperty(value = "中单位-配送价3")
    private BigDecimal midSalePrice3;

    /** 中单位-配送价4 */
    @Excel(name = "中单位-配送价4")
    @ApiModelProperty(value = "中单位-配送价4")
    private BigDecimal midSalePrice4;

    /** 中单位-配送价5 */
    @Excel(name = "中单位-配送价5")
    @ApiModelProperty(value = "中单位-配送价5")
    private BigDecimal midSalePrice5;

    /** 中单位-配送价6 */
    @Excel(name = "中单位-配送价6")
    @ApiModelProperty(value = "中单位-配送价6")
    private BigDecimal midSalePrice6;


    /** 大单位-配送价1 */
    @Excel(name = "大单位-配送价1")
    @ApiModelProperty(value = "大单位-配送价1")
    private BigDecimal largeSalePrice1;

    /** 大单位-配送价2 */
    @Excel(name = "大单位-配送价2")
    @ApiModelProperty(value = "大单位-配送价2")
    private BigDecimal largeSalePrice2;

    /** 大单位-配送价3 */
    @Excel(name = "大单位-配送价3")
    @ApiModelProperty(value = "大单位-配送价3")
    private BigDecimal largeSalePrice3;

    /** 大单位-配送价4 */
    @Excel(name = "大单位-配送价4")
    @ApiModelProperty(value = "大单位-配送价4")
    private BigDecimal largeSalePrice4;

    /** 大单位-配送价5 */
    @Excel(name = "大单位-配送价5")
    @ApiModelProperty(value = "大单位-配送价5")
    private BigDecimal largeSalePrice5;

    /** 大单位-配送价6 */
    @Excel(name = "大单位-配送价6")
    @ApiModelProperty(value = "大单位-配送价6")
    private BigDecimal largeSalePrice6;


    private Long sysCode;

    private Long minUnit;

    private Long midUnit;

    private Long largeUnit;
}
