package com.zksr.product.api.keywords;

import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/1/15 15:53
 * @关键词词库rpc服务
 */
@FeignClient(
        contextId = "remoteKeywordsApi",
        value = ApiConstants.NAME
)
public interface KeywordsApi {

    String PREFIX = ApiConstants.PREFIX + "/Keywords";

    /**
     * 批量插入关键词词库
     * @param keywords 关键词集合
     * @param sysCode 平台商ID
     * @return
     */
    @PostMapping(PREFIX + "/batchInsertKeywords")
    CommonResult<Boolean> batchInsertKeywords(@RequestHeader(name = SecurityConstants.SYS_CODE)Long sysCode, @RequestBody List<String> keywords);

    /**
     * 根据关键词搜索关键词词库
     *
     * @param keyword 关键词,sysCode 平台商ID
     */
    @PostMapping(PREFIX + "/searchKeywords")
    CommonResult<Boolean> searchKeywords(@RequestHeader(name = SecurityConstants.SYS_CODE)Long sysCode, @RequestBody String keyword);

}
