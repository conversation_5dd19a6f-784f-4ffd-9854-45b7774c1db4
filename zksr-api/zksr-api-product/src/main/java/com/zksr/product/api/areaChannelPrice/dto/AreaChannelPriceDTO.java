package com.zksr.product.api.areaChannelPrice.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaChannelPriceDTO {

    /** 平台商id */
    private Long sysCode;

    /** 城市id */
    private Long areaId;

    /** 渠道id */
    private Long channelId;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    private Long salePriceCode;

}
