package com.zksr.product.api.spu;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.vo.openapi.receive.SpuReceiveVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.spu.dto.*;
import com.zksr.product.api.spu.form.PrdSpuImportForm;
import com.zksr.product.api.spu.vo.PrdtSpuGroupInVO;
import com.zksr.product.api.spu.vo.PrdtSpuGroupSaveInVO;
import com.zksr.product.api.spu.vo.PrdtSpuPageReqVO;
import com.zksr.product.api.spu.dto.ProductDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.spu.dto.SpuductOpenDTO;
import com.zksr.product.api.spu.vo.PrdtSpuNotItemPageReqVo;
import com.zksr.product.enums.ApiConstants;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;
import java.util.Map;

@FeignClient(
        contextId = "remoteSpuApi",
        value = ApiConstants.NAME
)
/**
*
 *  入驻商服务
* <AUTHOR>
* @date 2024/3/16 11:29
*/
public interface SpuApi {

    String PREFIX = ApiConstants.PREFIX + "/spu";

    @GetMapping(PREFIX + "/getBySpuId")
    public CommonResult<SpuDTO> getBySpuId(@RequestParam("spuId") Long spuId);

    /**
    * @Description: 获取商品信息
    * @Author: liuxingyu
    * @Date: 2024/4/11 16:47
    */
    @GetMapping(PREFIX + "/getByProduct")
    CommonResult<ProductDTO> getByProduct(@RequestParam("param") String param);

    /**
     * @Description: 获取商品信息
     * @Author: liuxingyu
     * @Date: 2024/4/11 16:47
     * @param spuDTO
     * @return
     */
    @PostMapping(PREFIX + "/getSpuPageNotItemByApi")
    CommonResult<PageResult<PrdtSpuNotItemPageReqVo>> getSpuPageNotItemByApi(@RequestBody PrdtSpuNotItemPageReqVo spuDTO);


    @GetMapping(PREFIX + "/getPrdtList")
    @ApiOperation(value = "获得商品SPU分页列表", httpMethod = "GET")
    CommonResult<PageResult<PrdtSpuPageReqVO>> getPrdtList(@Valid PrdtSpuPageReqVO vo);

    /**
     *  获取商品
     * @param spuId ID
     */
    @GetMapping(PREFIX + "/getPrdtSpuInfo")
    CommonResult<PrdtSpuGroupInVO> getPrdtSpuInfo(@RequestParam("spuId") Long spuId);


    /**
     * 新增商品SPU
     */
    @PostMapping(PREFIX + "/addPrdtSpu")
    CommonResult<Long> addPrdtSpu(@Valid @RequestBody PrdtSpuGroupSaveInVO createReqVO);

    /**
     * 新增商品SPU
     */
    @PostMapping(PREFIX + "/addPrdtSpuOpen")
    CommonResult<Long> addPrdtSpuOpen(@Valid @RequestBody PrdtSpuGroupSaveInVO createReqVO);

    /**
     * 修改商品SPU
     */
    @PostMapping(PREFIX + "/editPrdtSpu")
    CommonResult<Boolean> editPrdtSpu(@Valid @RequestBody PrdtSpuGroupSaveInVO updateReqVO);

    /**
     * 修改商品SPU open
     */
    @PostMapping(PREFIX + "/editPrdtSpuOpen")
    CommonResult<Boolean> editPrdtSpuOpen(@Valid @RequestBody PrdtSpuGroupSaveInVO updateReqVO);

    /**
     * 获取sku上架商品集合
     */
    @PostMapping(PREFIX + "/getSkuUnitGroupList")
    CommonResult<List<SkuUnitGroupDTO>> getSkuUnitGroupList(
            @RequestParam("spuId") Long spuId,
            @RequestParam("areaId") Long areaId,
            @RequestParam("classId") Long classId,
            @RequestParam("productType") String productType
    );
    /**
     * 更据商品编号查询商品
     * @param spuNo
     * @return
     */
    @GetMapping(PREFIX + "/getBySpuNo")
    CommonResult<SpuDTO> getBySpuNo(@RequestParam("spuNo") Long spuNo);

    /**
     * 修改商品的生产日期
     *
     * @param sysCode
     * @param opensourceId
     * @param spuductOpenDTO
     */
    @PostMapping(PREFIX + "/updateBySpuNo")
    CommonResult<Boolean> updateProduct(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                        @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                        @RequestBody SpuductOpenDTO spuductOpenDTO);


    /**
     * 新增商品或者修改商品
     *
     * @param spuReceiveVO
     */
    @PostMapping(PREFIX + "/addOrUpdateSpu")
    CommonResult<Boolean> addOrUpdateSpu(
            @RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
            @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                         @RequestBody SpuReceiveVO spuReceiveVO);

    @PostMapping(PREFIX + "/getSpuExportList")
    CommonResult<List<SpuExportDTO>> getSpuExportList(@RequestBody PrdtSpuPageReqVO vo);

    @PostMapping(PREFIX + "/listBySpuIds")
    CommonResult<Map<Long, SpuDTO>> listBySpuIds(@RequestBody List<Long> spuIds);


    /**
     * spu 批量关联或者取消关联词条
     *
     * @param keyword 关联词条
     * @param spuIds spu集合
     * @param associationType 关联类型 0 关联 1 取消关联
     */
    @PostMapping(PREFIX + "/associationKeyword")
    CommonResult<Boolean> associationKeyword(@RequestParam("keyword") String keyword,
                                             @RequestParam("spuIds") List<Long> spuIds,
                                             @RequestParam("associationType") Integer associationType);

    @PostMapping(PREFIX + "/importDataEvent")
    CommonResult<String> importDataEvent(PrdSpuImportForm form);

}
