package com.zksr.product.api.material.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/1/10 14:34
 */
@Data
@SuppressWarnings("all")
@ApiModel(description = "素材缓存对象")
public class MaterialCacheVO {

    @ApiModelProperty("素材列表")
    private List<MaterialInfoVO> materialList;

    /**
     * 缓存key值
     * @param applyType 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品
     * @param applyId   促销活动ID, 全国上架ID, 本地上架ID
     * @return 缓存key值
     */
    public static String getCacheKey(Integer applyType, Long applyId) {
        return StringUtils.format("{}:{}", applyType, applyId);
    }

    /**
     * 获取全国商品素材缓存key
     */
    public static String getGlobalCacheKey(Long supplierItemId) {
        return getCacheKey(NumberPool.INT_TWO, supplierItemId);
    }

    /**
     * 获取本地商品素材缓存key
     */
    public static String getLocalCacheKey(Long areaItemId) {
        return getCacheKey(NumberPool.INT_THREE, areaItemId);
    }

    /**
     * 获取商品素材缓存key
     */
    public static String getProductCacheKey(ProductType productType, Long itemId) {
        return productType == ProductType.GLOBAL ? getGlobalCacheKey(itemId) : getLocalCacheKey(itemId);
    }

    /**
     * 获取促销素材缓存key
     */
    public static String getActivityCacheKey(Long activityId) {
        return getCacheKey(NumberPool.INT_ONE, activityId);
    }

    public static CacheKey parseKey(String keyInfo) {
        String[] infos = keyInfo.split(":");
        return new CacheKey(Integer.parseInt(infos[0]), Long.parseLong(infos[1]));
    }

    /**
     * 获取有效的素材
     */
    @JsonIgnore
    public String getValidateMaterial() {
        if (Objects.nonNull(materialList)) {
            for (MaterialInfoVO infoVO : materialList) {
                if (infoVO.getStartTime().getTime() < System.currentTimeMillis() && infoVO.getEndTime().getTime() > System.currentTimeMillis()) {
                    return infoVO.getImg();
                }
            }
        }
        return null;
    }

    @Data
    public static class CacheKey {
        private Integer applyType;
        private Long applyId;
        public CacheKey(Integer applyType, Long applyId) {
            this.applyType = applyType;
            this.applyId = applyId;
        }
    }
}
