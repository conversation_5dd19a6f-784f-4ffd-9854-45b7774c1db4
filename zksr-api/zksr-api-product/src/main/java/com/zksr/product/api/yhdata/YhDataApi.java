package com.zksr.product.api.yhdata;

import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchReqVO;
import com.zksr.common.core.domain.vo.openapi.CreateYhDataMatchRespVO;
import com.zksr.common.core.domain.vo.openapi.receive.CreateYhDataReqVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.yhdata.vo.*;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2024/12/10
 * @desc
 */
@FeignClient(
        contextId = "remoteYhDataApi",
        value = ApiConstants.NAME
)
public interface YhDataApi {

    String PREFIX = ApiConstants.PREFIX + "/yhData";

    /**
     * 保存补货数据到数据库
     * @param reqVO 补货数据
     * @return  保存数据库结果
     */
    @PostMapping(PREFIX + "/saveYhData")
    CommonResult<Boolean> saveYhData(@RequestBody CreateYhDataReqVO reqVO);

    /**
     * 获取补货单匹配状态
     * @param reqVO 查询请求
     * @return  匹配结果
     */
    @PostMapping(PREFIX + "/getBatchYhRes")
    CommonResult<CreateYhDataMatchRespVO> getBatchYhRes(@RequestBody CreateYhDataMatchReqVO reqVO);

    /**
     * 获取批次补货单入驻商一级展示分类集合
     * @param reqVO 查询请求
     * @return  匹配结果
     */
    @PostMapping(PREFIX + "/getBatchSupplierSaleClass")
    CommonResult<Map<Long, List<Long>>> getBatchSupplierSaleClass(@RequestBody YhBatchSupplierSaleClassReqVO reqVO);

    /**
     * 获取要货批次详情列表数据数据
     * @param reqVO  查询请求
     * @return  分页数据
     */
    @PostMapping(PREFIX + "/getBatchItemList")
    CommonResult<PageResult<YhBatchItemVO>> getBatchItemList(@RequestBody YhBatchListReqVO reqVO);

    /**
     * 门店操作, 添加要货数据
     * @param reqVO    要货请求
     * @return  结果
     */
    @PostMapping(PREFIX + "/branchBatchYhSave")
    CommonResult<Boolean> branchBatchYhSave(@RequestBody YhBatchSaveReqVO reqVO);

    /**
     * 批量删除
     * @param reqVO    删除请求
     * @return  结果
     */
    @PostMapping(PREFIX + "/removeBatchYh")
    CommonResult<Boolean> removeBatchYh(@RequestBody YhBatchRemoveReqVO reqVO);

}
