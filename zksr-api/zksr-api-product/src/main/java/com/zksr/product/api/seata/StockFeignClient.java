/*
 *  Copyright 1999-2021 Seata.io Group.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
package com.zksr.product.api.seata;

import com.zksr.common.core.constant.ServiceNameConstants;
import com.zksr.common.core.web.pojo.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Program Name: springcloud-nacos-seata
 * <p>
 * Description:
 * <p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/8/28 4:05 PM
 */
@FeignClient(contextId = "stockFeignClient", value = ServiceNameConstants.PRODUCT_SERVICE)
public interface StockFeignClient {

    @GetMapping("stock/deduct")
    CommonResult<Boolean> deduct(@RequestParam("commodityCode") String commodityCode, @RequestParam("count") Integer count);
}
