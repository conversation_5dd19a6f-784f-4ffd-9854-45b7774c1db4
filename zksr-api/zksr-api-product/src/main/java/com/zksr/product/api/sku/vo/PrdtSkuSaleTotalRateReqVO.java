package com.zksr.product.api.sku.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: sku 销售占比数据 request
 * @date 2024/9/14 9:40
 */
@Data
@ApiModel(description = "sku 销售占比数据 request")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PrdtSkuSaleTotalRateReqVO {

    @ApiModelProperty(value = "大区ID", required = true)
    private Long sysCode;

    @ApiModelProperty("skuId集合")
    private List<Long> skuIdList;
}
