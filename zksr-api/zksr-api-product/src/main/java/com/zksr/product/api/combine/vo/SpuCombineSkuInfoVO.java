package com.zksr.product.api.combine.vo;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/20 10:54
 */
@Data
public class SpuCombineSkuInfoVO {

    @ApiModelProperty("最小库存数量")
    private Long minStock = NumberPool.LONG_ZERO;

    @ApiModelProperty("促销套餐总价格")
    private BigDecimal totalMarkPrice = BigDecimal.ZERO;

}
