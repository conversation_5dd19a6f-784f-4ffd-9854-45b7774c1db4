package com.zksr.product.api.spu.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
* SPU商品导出 - 实体
* @date 2024/11/11 10:58
* <AUTHOR>
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("SPU商品导出 - 实体")
public class SpuExportDTO extends BaseEntity {

    /** 入驻商ID */
    @Excel(name = "入驻商ID", cellType = Excel.ColumnType.STRING)
    private Long supplierId;

    /** 商品sku_id */
    @Excel(name = "SKUID", cellType = Excel.ColumnType.STRING)
    private Long skuId;

    @Excel(name = "产品编号", cellType = Excel.ColumnType.STRING)
    private String spuNo;

    @Excel(name = "产品名称", cellType = Excel.ColumnType.STRING)
    private String spuName;

    @Excel(name = "入驻商名称", cellType = Excel.ColumnType.STRING)
    private String supplierName;

    @Excel(name = "三级类别编号", cellType = Excel.ColumnType.STRING)
    private Long thirdCatgoryId;

    @Excel(name = "一级类别名称", cellType = Excel.ColumnType.STRING)
    private String firstCatgoryName;

    @Excel(name = "二级类别名称", cellType = Excel.ColumnType.STRING)
    private String secondCatgoryName;

    @Excel(name = "三级类别名称", cellType = Excel.ColumnType.STRING)
    private String thirdCatgoryName;

    @Excel(name = "品牌名称", cellType = Excel.ColumnType.STRING)
    private String brandName;

    /** 最小单位-数据字典（sys_prdt_unit） */
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    private Long midUnit;

    /** 大单位-数据字典（sys_prdt_unit） */
    private Long largeUnit;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "小单位", cellType = Excel.ColumnType.STRING)
    private String minUnitName;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位", cellType = Excel.ColumnType.STRING)
    private String midUnitName;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位", cellType = Excel.ColumnType.STRING)
    private String largeUnitName;

    @Excel(name = "单位关系", cellType = Excel.ColumnType.STRING)
    private String unitRelation;

    /** 中单位换算数量（换算成最小单位）） */
    @Excel(name = "中单位换算数量", cellType = Excel.ColumnType.STRING)
    private BigDecimal midSize;

    /** 大单位换算数量（换算成最小单位）） */
    @Excel(name = "大单位换算数量", cellType = Excel.ColumnType.STRING)
    private BigDecimal largeSize;

    @Excel(name = "属性", cellType = Excel.ColumnType.STRING)
    private String properties;

    /** 标准价 */
    @Excel(name = "小单位标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal markPrice;

    /** 成本价 */
    @Excel(name = "小单位供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal costPrice;

    /** 中单位-标准价 */
    @Excel(name = "中单位标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal midCostPrice;

    /** 大单位-标准价 */
    @Excel(name = "大单位标准价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位供货价", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal largeCostPrice;

    @Excel(name = "库存", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal stock;

    @Excel(name = "中单位库存", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal midStock;

    @Excel(name = "大单位库存", cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal largeStock;


    @Excel(name = "条码", cellType = Excel.ColumnType.STRING)
    private String barcode;

    /** 中单位-国际条码 */
    @Excel(name = "中单位条码", cellType = Excel.ColumnType.STRING)
    private String midBarcode;

    /** 大单位-国际条码 */
    @Excel(name = "大单位条码", cellType = Excel.ColumnType.STRING)
    private String largeBarcode;

    @Excel(name = "状态", cellType = Excel.ColumnType.STRING)
    private String skuStatus;

    @Excel(name = "商品SPU状态", cellType = Excel.ColumnType.STRING)
    private String status;

    @Excel(name = "外部来源商品编号", cellType = Excel.ColumnType.STRING)
    private String sourceNo;

    @Excel(name = "SPU辅助的商品编号", cellType = Excel.ColumnType.STRING)
    private String auxiliarySpuNo;

    @Excel(name = "起订", cellType = Excel.ColumnType.STRING)
    private Long minOq;

    @Excel(name = "中单位起订", cellType = Excel.ColumnType.STRING)
    private Long midMinOq;

    @Excel(name = "大单位起订", cellType = Excel.ColumnType.STRING)
    private Long largeMinOq;

    @Excel(name = "起订倍数", cellType = Excel.ColumnType.STRING)
    private Long jumpOq;

    @Excel(name = "中单位起订倍数", cellType = Excel.ColumnType.STRING)
    private Long midJumpOq;

    @Excel(name = "大单位起订倍数", cellType = Excel.ColumnType.STRING)
    private Long largeJumpOq;

    @Excel(name = "限购", cellType = Excel.ColumnType.STRING)
    private Long maxOq;

    @Excel(name = "中单位限购", cellType = Excel.ColumnType.STRING)
    private Long midMaxOq;

    @Excel(name = "大单位限购", cellType = Excel.ColumnType.STRING)
    private Long largeMaxOq;


}
