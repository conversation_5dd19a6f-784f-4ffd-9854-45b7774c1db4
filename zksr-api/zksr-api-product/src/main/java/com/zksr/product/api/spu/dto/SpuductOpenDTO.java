package com.zksr.product.api.spu.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/31:15:18
 */
@Data
@ApiModel("接收商品生产日期实体")
public class SpuductOpenDTO {
    //入驻商编号
    private Long supplierId;

    // 商品编码
    @ApiModelProperty(value = "商品编码", required = true)
    private String spuNo;

    /** 最旧生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最旧生产日期", example = "2024-01-01", required = true)
    private Date oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "最新生产日期", example = "2024-09-01", required = true)
    private Date latestDate; 		 // 最新生产日期

}
