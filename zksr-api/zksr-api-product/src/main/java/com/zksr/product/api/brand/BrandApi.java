package com.zksr.product.api.brand;


import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.brand.form.BrandImportForm;
import com.zksr.product.api.brand.vo.BindBrandMerchantReqVO;
import com.zksr.product.enums.ApiConstants;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;



import java.util.List;
import java.util.Map;

@FeignClient(contextId = "brandApi", value = ApiConstants.NAME)
public interface BrandApi {

    String PREFIX = ApiConstants.PREFIX + "/brand";

    /**
     * @Description: 根据平台商品牌id获取平台商品牌
     * @Param: Long brandId
     * @return: CommonResult<BrandDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 10:24
     */
    @GetMapping(PREFIX + "/getBrandByBrandId")
    CommonResult<BrandDTO> getBrandByBrandId(@RequestParam("brandId") Long brandId);


    /**
     * 新增平台品牌
     * @param brandDTO
     * @return
     */
    @PostMapping(PREFIX + "/addBrand")
     CommonResult<Long> add( @RequestBody BrandDTO brandDTO);

    /**
     * 修改平台品牌
     */

    @PutMapping(PREFIX + "/editBrand")
     CommonResult<Boolean> edit( @RequestBody BrandDTO brandDTO);

    /**
     * 删除平台品牌
     */

    @DeleteMapping(PREFIX + "/{brandIds}")
     CommonResult<Boolean> remove(@PathVariable("brandIds") Long[] brandDTO);

    /**
     *  获取所有品牌
     */
    @GetMapping(PREFIX + "/getBrandList")
    CommonResult<List<BrandDTO>> getBrandList();

    /**
     * 品牌绑定品牌商
     * @param bindBrandMerchantReqVO    请求数据
     * @return
     */
    @PostMapping(PREFIX + "/bindBrandMerchant")
    CommonResult<Boolean> bindBrandMerchant(@RequestBody BindBrandMerchantReqVO bindBrandMerchantReqVO);

    @PostMapping(PREFIX + "/listByBrandIds")
    CommonResult<Map<Long, BrandDTO>> listByBrandIds(@RequestBody List<Long> brandIds);

    /**
     * 品牌信息导入
     * @param brandImportForm
     * @return
     */
    @PostMapping(PREFIX + "/importBrandData")
    CommonResult<String> importBrandData(@RequestBody BrandImportForm brandImportForm);
}
