package com.zksr.product.api.model.event;

import com.zksr.product.api.model.AbstractProductEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 上架事件
 * @date 2024/2/29 19:56
 */
@Data
public class EsSpuUpdateProductEvent  extends AbstractProductEvent {

    @ApiModelProperty("spuIds")
    private List<Long> spuIds;

    public EsSpuUpdateProductEvent() {
    }

    public EsSpuUpdateProductEvent(Long spuId) {
        this.spuIds = Collections.singletonList(spuId);
    }

    public EsSpuUpdateProductEvent(List<Long> spuIds) {
        this.spuIds = spuIds;
    }
}
