package com.zksr.product.api.skuPrice;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.skuPrice.dto.PrdtSkPageReqApiVO;
import com.zksr.product.api.skuPrice.dto.PrdtSkuPriceInfoExportVO;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

@FeignClient(
        contextId = "remoteSkuPriceApi",
        value = ApiConstants.NAME
)
/**
*
 *  sku销售价服务
* <AUTHOR>
* @date 2024/3/16 11:29
*/
public interface SkuPriceApi {

    String PREFIX = ApiConstants.PREFIX + "/skulPrice";

    @GetMapping(PREFIX + "/getSkuPriceByAreaIdAndSkuIdAndType")
    public CommonResult<SkuPriceDTO> getSkuPriceByAreaIdAndSkuIdAndType(@RequestParam("areaId") Long areaId, @RequestParam("skuId") Long skuId,@RequestParam("type") Integer type);

    @GetMapping(PREFIX + "/getSkuPriceByKey")
    public CommonResult<SkuPriceDTO> getSkuPriceByKey(@RequestParam("key") String key);

    @PostMapping(PREFIX + "/getSkuPriceExportList")
    CommonResult<List<PrdtSkuPriceInfoExportVO>> getSkuPriceExportList(@RequestBody PrdtSkPageReqApiVO pageVO);
}
