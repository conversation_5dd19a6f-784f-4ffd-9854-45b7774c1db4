package com.zksr.product.api.seata;

import com.zksr.common.core.web.pojo.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import static com.zksr.common.core.web.pojo.CommonResult.error;

@Component
@Slf4j
public class StockFeignClientFallbackFactory implements FallbackFactory<StockFeignClient> {

    @Override
    public StockFeignClient create(Throwable throwable) {
        log.error("StockFeignClient服务调用失败:{}", throwable.getMessage());
        return new StockFeignClient() {
            @Override
            public CommonResult<Boolean> deduct(String commodityCode, Integer count) {
                return error(500,"deduct接口调用失败");
            }
        };
    }
}
