package com.zksr.product.api.supplierGroupPrice;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.supplierGroupPrice.dto.SupplierGrouplPriceDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        contextId = "remoteSupplierGrouplPriceApi",
        value = ApiConstants.NAME
)
/**
*
 *  全国价格方案服务
* <AUTHOR>
* @date 2024/3/16 11:29
*/
public interface SupplierGrouplPriceApi {

    String PREFIX = ApiConstants.PREFIX + "/supplierGrouplPrice";

    @GetMapping(PREFIX + "/getPriceByAreaIdAndGroupId")
    public CommonResult<SupplierGrouplPriceDTO> getPriceByAreaIdAndGroupId(@RequestParam("areaId") Long areaId, @RequestParam("groupId") Long groupId);

    @GetMapping(PREFIX + "/getPriceByKey")
    public CommonResult<Integer> getPriceByKey(@RequestParam("key") String key);
}
