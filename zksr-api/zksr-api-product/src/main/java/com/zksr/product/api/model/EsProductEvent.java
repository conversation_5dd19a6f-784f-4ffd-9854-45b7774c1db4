package com.zksr.product.api.model;

import com.zksr.product.api.model.event.EsProductEventBuild;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 商品变动事件
 * 可以使用 {@link EsProductEventBuild} 快速构建
 */
@Data
public class EsProductEvent<T> {

    /**
     * @type =  com.zksr.product.api.model.EsProductEventType#RELEASE
     */
    @ApiModelProperty("事件类型, 参考com.zksr.product.api.model.EsProductEventType")
    private String type;

    /**
     * 事件发生时间
     */
    @ApiModelProperty("事件发生时间")
    private Date date;

    /**
     * 事件传递信息
     */
    @ApiModelProperty("事件消息")
    private T data;

    public EsProductEvent(String type, T data) {
        this.date = new Date();
        this.type = type;
        this.data = data;
    }

    public EsProductEvent() {
    }
}
