package com.zksr.product.api.property.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 规格属性KV
 * @date 2024/5/30 19:27
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(description = "商品属性名称")
public class PropertyKeyDTO {

    @ApiModelProperty("属性名ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long propertyId;

    @ApiModelProperty("属性名值")
    private String propertyName;

    @ApiModelProperty("属性值列表")
    private List<PropertyValueDTO> propertyValueDTOS;
}
