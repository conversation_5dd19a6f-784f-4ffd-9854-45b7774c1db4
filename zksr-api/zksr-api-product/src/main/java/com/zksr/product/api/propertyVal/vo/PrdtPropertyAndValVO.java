package com.zksr.product.api.propertyVal.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*
 * 规格名称 - 规格值对应 实体
* <AUTHOR>
* @date 2024/3/5 10:59
*/
@Data
@ApiModel("规格名称 - 规格值对应实体")
public class PrdtPropertyAndValVO {

    /** 规格名称id */
    @ApiModelProperty(value = "规格名称id", example = "示例值" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long propertyId;

    /** 规格名称 */
    @Excel(name = "规格名称")
    @ApiModelProperty(value = "规格名称", example = "示例值" ,required = true)
    private String propertyName;

    /** 规格值id */
    @ApiModelProperty(value = "规格值id", example = "示例值" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long propertyValId;

    /** 规格值名称 */
    @Excel(name = "规格值名称")
    @ApiModelProperty(value = "规格值名称", example = "示例值" ,required = true)
    private String valName;
}
