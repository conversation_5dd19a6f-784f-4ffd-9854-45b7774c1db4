package com.zksr.product.api.supplierClass.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 入驻商-平台商管理分类 关联关系对象 prdt_supplier_class
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Data
@ApiModel("入驻商-平台商管理分类 关联关系 - prdt_supplier_class Response VO")
@NoArgsConstructor
@AllArgsConstructor
public class PrdtSupplierClassRespDTO {
    private static final long serialVersionUID = 1L;

    /** 平台商管理分类id;平台商管理分类id */
    @Excel(name = "平台商管理分类id;平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id;平台商管理分类id", example = "示例值")
    private List<Long> catgoryIds;

    /** 入驻商id;入驻商id */
    @Excel(name = "入驻商id;入驻商id")
    @ApiModelProperty(value = "入驻商id;入驻商id", example = "示例值")
    private Long supplierId;

    /** 平台商id;平台商id */
    @Excel(name = "平台商id;平台商id")
    @ApiModelProperty(value = "平台商id;平台商id", example = "示例值")
    private Long sysCode;

    /** 一级管理分润, 销售占比 */
    @Excel(name = "一级管理分润, 销售占比")
    @ApiModelProperty(value = "一级管理分润, 销售占比")
    private List<PrdtSupplierClassRateDTO> supplierClassRateDTOS;

}
