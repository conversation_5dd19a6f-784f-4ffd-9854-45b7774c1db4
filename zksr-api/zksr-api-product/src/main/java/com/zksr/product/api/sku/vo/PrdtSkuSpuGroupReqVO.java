package com.zksr.product.api.sku.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.product.api.propertyVal.vo.PrdtPropertyAndValVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("商品基本信息 --sku 请求实体")
public class PrdtSkuSpuGroupReqVO {

    private static final long serialVersionUID = 1L;

    /** 商品sku_id */
    @ApiModelProperty(value = "是否删除 1-是 0-否" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 商品SPU_id */
    @Excel(name = "商品SPU_id")
    @ApiModelProperty(value = "商品SPU_id" ,required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典" ,required = true)
    private Long unit;

    /** 国际条码 */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码" ,required = true)
    private String barcode;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    @ApiModelProperty(value = "属性数组，JSON 格式" )
    private String properties;

    /** 图片地址 */
    @Excel(name = "图片地址")
    @ApiModelProperty(value = "图片地址" ,required = true)
    private String thumb;

    /** 库存数量 */
    @Excel(name = "库存数量")
    @ApiModelProperty(value = "库存数量" ,required = true)
    private BigDecimal stock;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价" ,required = true)
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    @ApiModelProperty(value = "建议零售价" ,required = true)
    private BigDecimal suggestPrice;

    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价" ,required = true)
    private BigDecimal costPrice;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否",required = true)
    private Long isDelete;

    /** 规格属性参数实体 */
    @Excel(name = "规格属性参数实体")
    @ApiModelProperty(value = "规格属性参数实体" ,required = true)
    private List<PrdtPropertyAndValVO> propertyAndValList;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)", example = "示例值" ,required = true)
    private Long status;

    /** 参考进价 */
    @Excel(name = "参考进价")
    @ApiModelProperty(value = "参考进价",required = true)
    private BigDecimal referencePrice;

    /** 参考售价 */
    @Excel(name = "参考售价")
    @ApiModelProperty(value = "参考售价" ,required = true)
    private BigDecimal referenceSalePrice;

    /** 起订 */
    @Excel(name = "起订")
    @ApiModelProperty(value = "起订")
    private Long minOq;


    /** 订货组数 */
    @Excel(name = "订货组数")
    @ApiModelProperty(value = "订货组数")
    private Long jumpOq;


    /** 限购 */
    @Excel(name = "限购")
    @ApiModelProperty(value = "限购")
    private Long maxOq;

    /** 中单位-国际条码 */
    @Excel(name = "中单位-国际条码")
    @ApiModelProperty(value = "中单位-国际条码")
    private String midBarcode;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    @ApiModelProperty(value = "中单位-标准价")
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位-成本价(供货价)")
    @ApiModelProperty(value = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    /** 中单位-建议零售价 */
    @Excel(name = "中单位-建议零售价")
    @ApiModelProperty(value = "中单位-建议零售价")
    private BigDecimal midSuggestPrice;

    /** 中单位-起订 */
    @Excel(name = "中单位-起订")
    @ApiModelProperty(value = "中单位-起订")
    private Long midMinOq;

    /** 中单位-订货组数 */
    @Excel(name = "中单位-订货组数")
    @ApiModelProperty(value = "中单位-订货组数")
    private Long midJumpOq;

    /** 中单位-限购 */
    @Excel(name = "中单位-限购")
    @ApiModelProperty(value = "中单位-限购")
    private Long midMaxOq;

    /** 大单位-国际条码 */
    @Excel(name = "大单位-国际条码")
    @ApiModelProperty(value = "大单位-国际条码")
    private String largeBarcode;

    /** 大单位-标准价 */
    @Excel(name = "大单位-标准价")
    @ApiModelProperty(value = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位-成本价(供货价)")
    @ApiModelProperty(value = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    /** 大单位-建议零售价 */
    @Excel(name = "大单位-建议零售价")
    @ApiModelProperty(value = "大单位-建议零售价")
    private BigDecimal largeSuggestPrice;

    /** 大单位-起订 */
    @Excel(name = "大单位-起订")
    @ApiModelProperty(value = "大单位-起订")
    private Long largeMinOq;

    /** 大单位-订货组数 */
    @Excel(name = "大单位-订货组数")
    @ApiModelProperty(value = "大单位-订货组数")
    private Long largeJumpOq;

    /** 大单位-限购 */
    @Excel(name = "大单位-限购")
    @ApiModelProperty(value = "大单位-限购")
    private Long largeMaxOq;

    /** 外部来源商品编号 */
    @Excel(name = "外部来源商品编号")
    @ApiModelProperty(value = "外部来源商品编号")
    private String sourceNo;

    /** 外部来源商品编号 */
    @Excel(name = "来源（B2B、ERP）")
    @ApiModelProperty(value = "来源（B2B、ERP）")
    private String source;

    /**
     * 销售分润占比, 最大0.29 = 29%
     */
    @Excel(name = "销售分润占比, 最大0.29 = 29%")
    @ApiModelProperty("销售分润占比, 最大0.29 = 29%")
    private BigDecimal saleTotalRate;

    /** 已售数量, 库存 - 已售 = 剩余 */
    @Excel(name = "已售数量, 库存 - 已售 = 剩余")
    private BigDecimal saleQty;

    /** 已同步库存 */
    @Excel(name = "已同步库存")
    @ApiModelProperty(value = "已同步库存", notes = "可售库存 = 总库存 - (已售 - 已同步)")
    private BigDecimal syncedQty;

    /** 中单位状态 */
    @Excel(name = "中单位状态")
    @ApiModelProperty(value = "中单位状态", notes = "中单位状态 1-启用 0-停用")
    private Long midStatus;

    /** 大单位状态 */
    @Excel(name = "大单位状态")
    @ApiModelProperty(value = "大单位状态", notes = "大单位状态 1-启用 0-停用")
    private Long largeStatus;

    @ApiModelProperty("上架城市id")
    private Long areaId;

    @ApiModelProperty("小上架城市名称")
    private String minAreaName;

    @ApiModelProperty("中上架城市名称")
    private String midAreaName;

    @ApiModelProperty("大上架城市名称")
    private String largeAreaName;

    @ApiModelProperty("城市小单位上架状态,0-未上架,1-已上架")
    private Integer areaMinShelfStatus;

    @ApiModelProperty("城市中单位上架状态,0-未上架,1-已上架")
    private Integer areaMidShelfStatus;

    @ApiModelProperty("城市大单位上架状态,0-未上架,1-已上架")
    private Integer areaLargeShelfStatus;

    @ApiModelProperty("全国小单位上架状态,0-未上架,1-已上架")
    private Integer supplierMinShelfStatus;

    @ApiModelProperty("全国中单位上架状态,0-未上架,1-已上架")
    private Integer supplierMidShelfStatus;

    @ApiModelProperty("全国大单位上架状态,0-未上架,1-已上架")
    private Integer supplierLargeShelfStatus;

    @ApiModelProperty("占用库存")
    private BigDecimal occupiedQty;
    
    /** 销售类型(0-零售、1-批发) */
    @ApiModelProperty("销售类型(0-零售、1-批发) 字典：sale_type")
    private Integer saleType;
    
    /** 零售价 */
    @ApiModelProperty("零售价")
    private java.math.BigDecimal retailPrice;
    
    /** 分润比例 */
    @ApiModelProperty("分润比例 百分比的小数表现形式，1%表示为0.01")
    private java.math.BigDecimal profitRate;
    
    /** 分润金额 */
    @ApiModelProperty("分润金额")
    private java.math.BigDecimal profitAmount;
    
    
    /** 中单位零售价 */
    @Excel(name = "中单位零售价")
    private java.math.BigDecimal midRetailPrice;
    /** 中单位分润比例 */
    @Excel(name = "中单位分润比例 百分比的小数表现形式，1%表示为0.01")
    private java.math.BigDecimal midProfitRate;
    /** 中单位分润金额 */
    @Excel(name = "中单位分润金额")
    private java.math.BigDecimal midProfitAmount;
    
    /** 大单位零售价 */
    @Excel(name = "大单位零售价")
    private java.math.BigDecimal largeRetailPrice;
    /** 大单位分润比例 */
    @Excel(name = "大单位分润比例 百分比的小数表现形式，1%表示为0.01")
    private java.math.BigDecimal largeProfitRate;
    /** 大单位分润金额 */
    @Excel(name = "大单位分润金额")
    private java.math.BigDecimal largeProfitAmount;
    
}
