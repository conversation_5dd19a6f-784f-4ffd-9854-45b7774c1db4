package com.zksr.product.api.saleClass;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.saleClass.dto.SaleClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "saleClassApi", value = ApiConstants.NAME)
public interface SaleClassApi {

    String PREFIX = ApiConstants.PREFIX + "/saleClass";

    /**
     * @Description: 根据平台商展示分类Id获取平台商展示分类
     * @Param: Long saleClassId
     * @return: CommonResult<SaleClassDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 10:24
     */
    @GetMapping(PREFIX + "/getSaleClassBySaleClassId")
    CommonResult<SaleClassDTO> getSaleClassBySaleClassId(@RequestParam("saleClassId") Long saleClassId);

    /**
     * @Description: 获取平台展示分类列表
     * @Param: Long sysCode
     * @return: List<SaleClassDTO>
     * @Author: liuxingyu
     * @Date: 2024/3/26 20:12
     */
    @GetMapping(PREFIX + "/getSaleClassListBySysCode")
    CommonResult<List<SaleClassDTO>> getSaleClassListBySysCode(@RequestParam("sysCode") Long sysCode);
    @PostMapping(PREFIX + "/getSaleClassExportList")
    CommonResult<List<SaleClassExportVo>>  getSaleClassExportList(@RequestBody SaleClassExportVo pageVo);


}
