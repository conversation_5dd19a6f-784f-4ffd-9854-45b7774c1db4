package com.zksr.product.api.supplierClass.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.domain.BaseEntity;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 城市展示分类对象 prdt_area_class
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("城市展示分类 - prdt_area_class Response VO")
public class PrdtAreaClassExportVo extends PageParam  {
    private static final long serialVersionUID = 1L;


    /** 城市展示分类名 */
    @Excel(name = "类别编号")
    @ApiModelProperty(value = "类别编号")
    private Long areaClassId;

    /** 城市展示分类名 */
    @Excel(name = "类别名称")
    @ApiModelProperty(value = "类别名称")
    private String areaClassName;

    @Excel(name = "城市名称")
    @ApiModelProperty(value = "城市名称")
    private String areaName;

    /** 是否是电子围栏分类 */
    @Excel(name = "电子围栏是否启用", readConverterExp = "1= 是,0= 否")
    @ApiModelProperty(value = "电子围栏是否启用")
    private String dzwlFlag;

    @Excel(name = "上级类别")
    @ApiModelProperty(value = "上级类别")
    private String pidName;

    @Excel(name = "是否启用", readConverterExp = "1= 是,0= 否")
    @ApiModelProperty(value = "1-启用 0-未启用")
    private String status;

    @Excel(name = "渠道类型")
    @ApiModelProperty(value = "渠道类型")
    private String channelIdName;

    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    private Long sort;

    @ApiModelProperty(value = "父id")
    private Long pid;

    @ApiModelProperty(value = "城市id")
    private Long areaId;

    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;


    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "分类图标")
    private String icon;

}
