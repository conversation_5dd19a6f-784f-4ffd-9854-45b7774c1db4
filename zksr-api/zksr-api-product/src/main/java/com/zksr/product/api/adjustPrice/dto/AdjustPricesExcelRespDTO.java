package com.zksr.product.api.adjustPrice.dto;

import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@ApiModel("商品调价单导出 - 返回实体")
@Accessors(chain = true)
public class AdjustPricesExcelRespDTO extends BaseEntity{
//    @Excel(name = "产品编号", cellType = Excel.ColumnType.STRING)
//    @ApiModelProperty(value = "产品编号")
//    private String spuNo;
//
//    @Excel(name = "skuId", cellType = Excel.ColumnType.STRING)
//    @ApiModelProperty(value = "skuId")
//    private Long skuId;
//
//    @Excel(name = "产品名称", cellType = Excel.ColumnType.STRING)
//    @ApiModelProperty(value = "产品名称")
//    private String spuName;
//
//    @Excel(name = "入驻商名称", cellType = Excel.ColumnType.STRING)
//    @ApiModelProperty(value = "入驻商名称")
//    private String supplierName;
//
//    @Excel(name = "三级管理类别名称", cellType = Excel.ColumnType.STRING)
//    @ApiModelProperty(value = "三级管理类别名称")
//    private String categoryName;
//
//    @Excel(name = "品牌名称", cellType = Excel.ColumnType.STRING)
//    @ApiModelProperty(value = "品牌名称")
//    private String brandName;
//
//    @Excel(name = "属性", cellType = Excel.ColumnType.STRING)
//    @ApiModelProperty(value = "属性")
//    private String properties;
//
//    @Excel(name = "单位关系", cellType = Excel.ColumnType.STRING)
//    @ApiModelProperty(value = "单位关系")
//    private String unitRelation;
//
//    @Excel(name = "小单位标准价", cellType = Excel.ColumnType.NUMERIC)
//    @ApiModelProperty(value = "小单位标准价")
//    private BigDecimal minMarkPrice;
//
//    @Excel(name = "小单位供货价", cellType = Excel.ColumnType.NUMERIC)
//    @ApiModelProperty(value = "小单位供货价")
//    private BigDecimal minCostPrice;
//
//    @Excel(name = "中单位标准价", cellType = Excel.ColumnType.NUMERIC)
//    @ApiModelProperty(value = "中单位标准价")
//    private BigDecimal midMarkPrice;
//
//    @Excel(name = "中单位供货价", cellType = Excel.ColumnType.NUMERIC)
//    @ApiModelProperty(value = "中单位供货价")
//    private BigDecimal midCostPrice;
//
//    @Excel(name = "大单位标准价", cellType = Excel.ColumnType.NUMERIC)
//    @ApiModelProperty(value = "大单位标准价")
//    private BigDecimal largeMarkPrice;
//
//    @Excel(name = "大单位供货价", cellType = Excel.ColumnType.NUMERIC)
//    @ApiModelProperty(value = "大单位供货价")
//    private BigDecimal largeCostPrice;
//
//
//    @ApiModelProperty(value = "入驻商编号")
//    private Long supplierId;
//
//    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
//    private Long midSize;
//
//    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
//    private Long largeSize;
//
//    @ApiModelProperty(value = "大单位编码(数据字典 sys_prdt_unit)")
//    private Long largeUnit;
//
//    @ApiModelProperty(value = "中单位编码(数据字典 sys_prdt_unit)")
//    private Long midUnit;
//
//    @ApiModelProperty(value = "小单位编码(数据字典 sys_prdt_unit)")
//    private Long minUnit;
}
