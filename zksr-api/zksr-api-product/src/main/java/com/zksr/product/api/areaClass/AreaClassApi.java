package com.zksr.product.api.areaClass;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaClass.form.AreaClassImportForm;
import com.zksr.product.api.supplierClass.dto.PrdtAreaClassExportVo;
import com.zksr.product.api.supplierClass.dto.PrdtSupplierClassRespDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "areaClassApi", value = ApiConstants.NAME)
public interface AreaClassApi {

    String PREFIX = ApiConstants.PREFIX + "/areaClass";

    /**
     * @Description: 根据Id获取城市展示分类
     * @Param: Long areaClassId
     * @return: CommonResult<AreaClassDto>
     * @Author: liuxingyu
     * @Date: 2024/3/25 10:24
     */
    @GetMapping(PREFIX + "/getAreaClassByAreaClassId")
    CommonResult<AreaClassDTO> getAreaClassByAreaClassId(@RequestParam("areaClassId") Long areaClassId);

    /**
    * @Description: 根据门店绑定的入驻商获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:41
    */
    @GetMapping(PREFIX + "/getAreaClassBranchList")
    CommonResult<List<AreaClassDTO>> getAreaClassBranchList(@RequestParam("supplierIds") List<Long> supplierIds);

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:47
    */
    @GetMapping(PREFIX + "/getAreaClassAreaChannelList")
    CommonResult<List<AreaClassDTO>> getAreaClassAreaChannelList(@RequestParam("key") String key);

    /**
     * 城市展示分类导出
     */
    @PostMapping(PREFIX + "/getPrdtAreaClassExportList")
    CommonResult<List<PrdtAreaClassExportVo>>  getPrdtAreaClassExportList(@RequestBody PrdtAreaClassExportVo pageVo);

    /**
     * 城市展示分类导入
     * @param form
     * @return
     */
    @PostMapping(PREFIX + "/importAreaClassDataEvent")
    CommonResult<String> importAreaClassDataEvent(@RequestBody AreaClassImportForm form);

}
