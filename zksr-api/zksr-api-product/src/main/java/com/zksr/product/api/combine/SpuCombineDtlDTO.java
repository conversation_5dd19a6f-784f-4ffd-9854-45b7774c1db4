package com.zksr.product.api.combine;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/28 17:09
 */
@Data
@ApiModel(description = "组合促销商品明细")
public class SpuCombineDtlDTO {
    /**
     * 平台商id
     */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 组合商品ID
     */
    @ApiModelProperty(value = "组合商品ID")
    private Long spuCombineId;

    /**
     * SKU ID
     */
    @ApiModelProperty(value = "SKU ID")
    private Long skuId;

    /**
     * 商品单位大小
     */
    @ApiModelProperty(value = "商品单位大小")
    private Integer skuUnitType;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Integer qty;

    /**
     * 是否为赠品
     */
    @ApiModelProperty(value = "是否为赠品")
    private Boolean giftFlag;

    @ApiModelProperty(value = "全国上架商品ID")
    private Long supplierItemId;

    @ApiModelProperty(value = "本地上架商品ID")
    private Long areaItemId;
}
