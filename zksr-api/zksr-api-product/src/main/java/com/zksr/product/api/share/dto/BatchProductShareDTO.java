package com.zksr.product.api.share.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/6 11:06
 * @注释
 */
@Data
@ApiModel("商品分享接收实体")
public class BatchProductShareDTO {

    @Excel(name = "来源平台")
    @ApiModelProperty(value = "来源平台", required = true)
    private String sysSource;

    @Excel(name = "来源平台")
    @ApiModelProperty(value = "来源平台", required = true)
    private List<BatchProductShareItemDTO> itemList;


    @ApiModel(description = "分享明细")
    @Data
    public static class BatchProductShareItemDTO {

        @Excel(name = "上架ID")
        @ApiModelProperty(value = "上架ID")
        private String itemId;

        @Excel(name = "单位")
        @ApiModelProperty(value = "单位")
        private Long unitSize;
    }

}
