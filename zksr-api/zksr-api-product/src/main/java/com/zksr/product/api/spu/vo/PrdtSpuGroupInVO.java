package com.zksr.product.api.spu.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.product.api.property.vo.PrdtPropertySpuGroupReqVO;
import com.zksr.product.api.property.vo.PrdtPropertySpuGroupSaveInVO;
import com.zksr.product.api.sku.vo.PrdtSkuSpuGroupReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSpuGroupSaveInVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
*
 * 商品基本信息 -- 查询明细请求实体
* <AUTHOR>
* @date 2024/3/5 18:08
*/
@Data
@ApiModel("商品基本信息 -- 查询明细请求实体")
public class PrdtSpuGroupInVO {

    /** Spu对象 */
    @Excel(name = "Spu保存对象")
    @ApiModelProperty(value = "Spu保存对象")
    private PrdtSpuSaveReqVO spu;

    /** Sku集合 */
    @Excel(name = "Sku集合")
    @ApiModelProperty(value = "Sku集合")
    private List<PrdtSkuSpuGroupReqVO> skuList;

    /** 规格名称集合 */
    @Excel(name = "规格名称集合")
    @ApiModelProperty(value = "规格名称集合")
    private List<PrdtPropertySpuGroupReqVO> propertyList;

    @ApiModelProperty("平台共享商品ID")
    private Long platformSpuId;

    /**
     * 是否同步标准价格 0-不同步 1-同步（默认同步）
     */
    private Integer syncMarkPrice;

    /**
     * 是否同步供货价格 0-不同步 1-同步（默认同步）
     */
    private Integer syncCostPrice;
}
