package com.zksr.product.api.adjustPrice;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.product.api.adjustPrice.dto.AdjustPricesDTO;
import com.zksr.product.api.areaChannelPrice.dto.AreaChannelPriceDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

@FeignClient(
        contextId = "remoteAdjustPriceApi",
        value = ApiConstants.NAME
)

/**
*  商品调价单服务
* @date 2024/11/8 9:32
* <AUTHOR>
*/
public interface AdjustPriceApi {

    String PREFIX = ApiConstants.PREFIX + "/adjustPrice";

    /**
     * 定时执行 定时生效-商品调价单
     * @param sysCode
     * @param validTime
     * @return
     */
    @GetMapping(PREFIX + "/operateValidJob")
    public CommonResult<Boolean> operateValidJob(@RequestParam("sysCode") Long sysCode,@RequestParam("validTime") Date validTime);

}
