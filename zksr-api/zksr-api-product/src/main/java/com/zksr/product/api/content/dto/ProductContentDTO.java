package com.zksr.product.api.content.dto;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductContentDTO {

    /** 上架商品ID */
    @ApiModelProperty("上架ID")
    private Long itemId;

    /** 商品类型, local本地, global 全国 */
    @ApiModelProperty("商品类型, local本地, global 全国 ")
    private String type;

    /** 商品名称 */
    @ApiModelProperty("商品名称")
    private String spuName;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

    /** 单位尺寸 */
    @ApiModelProperty("单位尺寸: 1-小单位,2-中单位,3-大单位")
    private Integer unitSize;

    /** 规格属性, 可能涉及搜索 */
    private String properties;

    /** 封面图片 */
    private String thumb;

    /** 封面视频 */
    private String thumbVideo;

    /** SPU ID */
    private Long spuId;

    /** 默认-1, 兼容全国和本地商品售后 */
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long areaId;

    /** 默认-1, 本地渠道 */
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long channelId;

    /** 默认-1, 兼容全国和本地商品 平台商城市分组id */
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long groupId;

    /** SKU ID */
    private Long skuId;

    /** 平台展示分类ID */
    private Long classId;

    /** 平台展示分类名称 */
    private String className;

    /** 入驻商ID */
    private Long supplierId;

    /** 品牌ID */
    private Long brandId;

    /** 管理分类ID*/
    private Long catgoryId;

    /** 品牌名称 */
    private String brandName;

    /** 建议零售价, 原价 */
    private BigDecimal suggestPrice;

    /** 销售最低价 */
    private BigDecimal minPrice;

    /** 最低价规格 */
    private BigDecimal minSkuName;

    /** 销售价1 */
    private BigDecimal salePrice;

    /** 销量 */
    @ApiModelProperty("销量")
    private Long saleQty;

    /** 库存 */
    @ApiModelProperty("库存")
    private Long stock;

    /** 商品规格 */
    @ApiModelProperty("商品规格")
    private String specName;

    /** 是否开启多规格 1-是 0-否 */
    @ApiModelProperty(value = "是否开启多规格 1-是 0-否",  example = "1-是 0-否")
    private Long isSpecs;

    /** 排序序号 */
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer sortNum;

    /** 起订 */
    @ApiModelProperty(value = "起订", example = "1")
    private Long minOq;

    /** 订货组数 */
    @ApiModelProperty(value = "订货组数", example = "1")
    private Long jumpOq;

    /** 限购 */
    @ApiModelProperty("限购数量")
    private Long maxOq;

    /** 小单位上架状态 */
    @ApiModelProperty("小单位上架状态")
    private Integer minShelfStatus;

    /** 中单位上架状态 */
    @ApiModelProperty("中单位架状态")
    private Integer midShelfStatus;

    /** 大单位上架状态 */
    @ApiModelProperty("大单位上架状态")
    private Integer largeShelfStatus;

    @ApiModelProperty("最新生产日期")
    private Date latestDate;

    @ApiModelProperty("最后生产日期")
    private Date oldestDate;

    @ApiModelProperty("0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    @ApiModelProperty("组合促销商品ID")
    private Long spuCombineId;
}
