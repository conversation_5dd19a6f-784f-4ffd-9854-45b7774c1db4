package com.zksr.product.api.areaItem.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 城市上架商品对象 prdt_area_item
 *
 * <AUTHOR>
 * @date 2024-03-07
 */
@ApiModel("城市上架商品 - prdt_area_item分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PrdtAreaItemPageRespVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 城市上架商品id */
    @ApiModelProperty(value = "城市上架商品id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaItemId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 城市展示分类id */
    @Excel(name = "城市展示分类id")
    @ApiModelProperty(value = "城市展示分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaClassId;

    /** 上下架状态 */
    @Excel(name = "上下架状态")
    @ApiModelProperty(value = "上下架状态")
    private Integer shelfStatus;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    @ApiModelProperty(value = "商品sku id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 平台商管理分类id */
    @Excel(name = "平台商管理分类id")
    @ApiModelProperty(value = "平台商管理分类id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long catgoryId;

    /** 平台商品牌id */
    @Excel(name = "平台商品牌id")
    @ApiModelProperty(value = "平台商品牌id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 成本价 */
    @Excel(name = "成本价")
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /** 库存数量 */
    @Excel(name = "库存数量")
    @ApiModelProperty(value = "库存数量")
    private Long stock;

    /** 是否删除 1-是 0-否 */
    @Excel(name = "是否删除 1-是 0-否")
    @ApiModelProperty(value = "是否删除 1-是 0-否")
    private Long isDelete;

    /** keyword */
    @Excel(name = "搜索关键字")
    @ApiModelProperty(value = "搜索关键字", example = "示例值")
    private String keyword;

    /** 国际条码 */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码", example = "示例值")
    private String barcode;

    /** 单位-数据字典（sys_prdt_unit） */
    @Excel(name = "单位-数据字典", readConverterExp = "s=ys_prdt_unit")
    @ApiModelProperty(value = "单位-数据字典", example = "示例值")
    private Long unit;

    /** 属性数组，JSON 格式 */
    @Excel(name = "属性数组，JSON 格式")
    @ApiModelProperty(value = "属性数组，JSON 格式", example = "示例值")
    private String properties;

    /** 标准价 */
    @Excel(name = "标准价")
    @ApiModelProperty(value = "标准价", example = "示例值")
    private BigDecimal markPrice;

    /** 建议零售价 */
    @Excel(name = "建议零售价")
    @ApiModelProperty(value = "建议零售价", example = "示例值")
    private BigDecimal suggestPrice;

    /** 保质期 */
    @Excel(name = "保质期")
    @ApiModelProperty(value = "保质期")
    private Integer expirationDate;		 // 保质期

    /** 参考进价 */
    @Excel(name = "参考进价")
    @ApiModelProperty(value = "参考进价")
    private BigDecimal referencePrice;

    /** 参考售价 */
    @Excel(name = "参考售价")
    @ApiModelProperty(value = "参考售价")
    private BigDecimal referenceSalePrice;

    /** 平台商管理分类名称 */
    @Excel(name = "平台商管理分类名称")
    @ApiModelProperty(value = "平台商管理分类名称")
    private String catgoryName;

    /** 平台商品牌名称 */
    @Excel(name = "平台商品牌名称")
    @ApiModelProperty(value = "平台商品牌名称")
    private String brandName;

    /** 排序序号 */
    @Excel(name = "排序序号")
    @ApiModelProperty(value = "排序序号", example = "示例值")
    private Integer sortNum;

    /** 上架时间 */
    @Excel(name = "上架时间")
    @ApiModelProperty(value = "上架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfDate; 		 // 上架时间

    /** 列表排序方式 */
    @Excel(name = "列表排序方式")
    @ApiModelProperty(value = "列表排序方式", example = "示例值")
    private String sortord;

    /** 列表排序顺序 */
    @Excel(name = "列表排序顺序")
    @ApiModelProperty(value = "列表排序顺序 0 正序 1 倒叙", example = "示例值")
    private Long sortOrder;

    /** 状态(数据字典 sys_common_status) */
    @Excel(name = "状态(数据字典 sys_common_status)")
    @ApiModelProperty(value = "状态(数据字典 sys_common_status)", example = "示例值")
    private Long status;

    /** 城市展示分类名称 */
    @Excel(name = "城市展示分类名称")
    @ApiModelProperty(value = "城市展示分类名称")
    private String areaClassName;

    /** 二级城市展示分类名称 */
    @Excel(name = "二级城市展示分类名称")
    @ApiModelProperty(value = "二级城市展示分类名称")
    private String secondAreaClassName;

    /** 一级城市展示分类名称 */
    @Excel(name = "一级城市展示分类名称")
    @ApiModelProperty(value = "一级城市展示分类名称")
    private String firstAreaClassName;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;


    /** 封面图（url） */
    @Excel(name = "封面图", readConverterExp = "u=rl")
    @ApiModelProperty(value = "封面图")
    private String thumb;

    /** 平台商管理分类级别 */
    @Excel(name = "平台商管理分类级别")
    @ApiModelProperty(value = "平台商管理分类级别")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Integer classLevel;

    /** 商品规格 */
    @Excel(name = "商品规格")
    @ApiModelProperty(value = "商品规格")
    private String specName;

    /** 小单位-上下架状态 */
    @Excel(name = "小单位-上下架状态")
    @ApiModelProperty(value = "小单位-上下架状态", example = "示例值")
    private Integer minShelfStatus;

    /** 中单位-上下架状态 */
    @Excel(name = "中单位-上下架状态")
    @ApiModelProperty(value = "中单位-上下架状态", example = "示例值")
    private Integer midShelfStatus;

    /** 大单位-上下架状态 */
    @Excel(name = "大单位-上下架状态")
    @ApiModelProperty(value = "大单位-上下架状态", example = "示例值")
    private Integer largeShelfStatus;

    /** 中单位-国际条码 */
    @Excel(name = "中单位-国际条码")
    private String midBarcode;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    /** 中单位-建议零售价 */
    @Excel(name = "中单位-建议零售价")
    private BigDecimal midSuggestPrice;

    /** 中单位-起订 */
    @Excel(name = "中单位-起订")
    private Long midMinOq;

    /** 中单位-订货组数 */
    @Excel(name = "中单位-订货组数")
    private Long midJumpOq;

    /** 中单位-限购 */
    @Excel(name = "中单位-限购")
    private Long midMaxOq;

    /** 大单位-国际条码 */
    @Excel(name = "大单位-国际条码")
    private String largeBarcode;

    /** 大单位-标准价 */
    @Excel(name = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    /** 大单位-建议零售价 */
    @Excel(name = "大单位-建议零售价")
    private BigDecimal largeSuggestPrice;

    /** 大单位-起订 */
    @Excel(name = "大单位-起订")
    private Long largeMinOq;

    /** 大单位-订货组数 */
    @Excel(name = "大单位-订货组数")
    private Long largeJumpOq;

    /** 大单位-限购 */
    @Excel(name = "大单位-限购")
    private Long largeMaxOq;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典（sys_prdt_unit）", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    /** 中单位换算数量（换算成最小单位）） */
    @Excel(name = "中单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
    private Long midSize;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 大单位换算数量（换算成最小单位）） */
    @Excel(name = "大单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
    private Long largeSize;

    @Excel(name = "区域ID集合")
    @ApiModelProperty(value = "区域ID集合")
    private List<Long> areaIdList;

    /**
     * 促销活动文案类型
     * 参加 {@link com.zksr.common.core.enums.PrmNoEnum}
     * */
    @ApiModelProperty(value = "促销活动文案类型")
    private String prmType;

    private List<Long> spuIdList;

    /** SKU状态, 0-停用, 1-启用 */
    @ApiModelProperty(value = "SKU状态, 0-停用, 1-启用")
    private Long skuStatus;

    @Excel(name = "区域名称")
    @ApiModelProperty(value = "区域名称")
    private String areaName;

    /** 0-普通商品, 1-组合商品 */
    @Excel(name = "0-普通商品, 1-组合商品")
    @ApiModelProperty(value = "0-普通商品, 1-组合商品")
    private Integer itemType;

    /** 活动开始时间, 活动商品 */
    @Excel(name = "活动开始时间, 活动商品")
    @ApiModelProperty(value = "活动开始时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityStartTime;

    /** 活动结束时间, 活动商品 */
    @Excel(name = "活动结束时间, 活动商品")
    @ApiModelProperty(value = "活动结束时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndTime;

    /** 组合商品ID */
    @Excel(name = "组合商品ID")
    @ApiModelProperty(value = "组合商品ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuCombineId;

    /** 活动ID */
    @Excel(name = "活动ID")
    @ApiModelProperty(value = "活动ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long activityId;

    /** 素材应用id */
    @ApiModelProperty(value = "素材应用ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialApplyId;

    /** 素材id */
    @Excel(name = "素材id")
    @ApiModelProperty(value = "素材id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialId;

    /** 素材图片地址 */
    @Excel(name = "素材图片地址")
    @ApiModelProperty(value = "素材图片地址")
    private String img;

    /** 二级管理分类名称 */
    @Excel(name = "二级管理分类名称")
    @ApiModelProperty(value = "二级管理分类名称")
    private String secondCatgoryName;

    /** 一级管理分类名称 */
    @Excel(name = "一级管理分类名称")
    @ApiModelProperty(value = "一级管理分类名称")
    private String firstCatgoryName;
}
