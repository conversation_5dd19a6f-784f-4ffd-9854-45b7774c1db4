package com.zksr.product.api.areaItem.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.poi.ss.usermodel.IndexedColors;


@Data
@ApiModel(description = "用于商品排序导入")
public class PrdtAreaItemSortImportExcel {

    @Excel(name = "入驻商编码", headerColor = IndexedColors.RED)
    private String supplierId;

    @Excel(name = "skuId", headerColor = IndexedColors.RED)
    private String skuId;

    @Excel(name = "三级分类编码", headerColor = IndexedColors.RED)
    private String areaClassId;

    @Excel(name = "排序号", headerColor = IndexedColors.RED)
    private String sort;
}