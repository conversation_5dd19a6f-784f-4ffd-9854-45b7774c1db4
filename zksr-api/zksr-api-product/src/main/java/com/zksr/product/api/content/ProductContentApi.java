package com.zksr.product.api.content;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.content.dto.ProductContentDTO;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.product.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "productContentApi", value = ApiConstants.NAME)
public interface ProductContentApi {

    String PREFIX = ApiConstants.PREFIX + "/productContent";

    /**
     * 搜索ES中的商品信息
     *
     * @param pageReqVo
     * @return
     */
    @PostMapping(PREFIX + "/getElasticSearchListByApi")
    CommonResult<List<ProductContentDTO>> getElasticSearchListByApi(@RequestBody ProductContentPageReqDTO pageReqVo);

    /**
     * 搜索ES中的城市上下架商品信息
     *
     * @param pageReqVo
     * @return
     */
    @PostMapping(PREFIX + "/getElasticSearchAreaItemListByApi")
    CommonResult<List<ProductContentDTO>> getElasticSearchAreaItemList(@RequestBody ProductContentPageReqDTO pageReqVo);

    /**
     * 检索本地 和 全国 全量数据
     * @param pageReqVo
     * @return
     */
    @PostMapping(PREFIX + "/getElasticSearchFullItemList")
    CommonResult<List<ProductContentDTO>> getElasticSearchFullItemList(@RequestBody ProductContentPageReqDTO pageReqVo);

    /**
     * 分页搜索商品数据
     *
     * @param pageReqVo
     * @return
     */
    @PostMapping(PREFIX + "/getPageElasticSearchListByApi")
    CommonResult<PageResult<ProductContentDTO>> getPageElasticSearchListByApi(@RequestBody ProductContentPageReqDTO pageReqVo);

    /**
     * 搜索ES中的商品信息
     *
     * @param pageReqVo
     * @return
     */
    @PostMapping(PREFIX + "/getElasticSearchItemList")
    CommonResult<PageResult<ProductContentDTO>> getElasticSearchItemList(@RequestBody ProductContentPageReqDTO pageReqVo);

    /**
     * 更新ES销量信息
     *
     * @param skuIdList sku id 集合
     */
    @PostMapping(PREFIX + "/sendSkuEvent")
    CommonResult<Boolean> sendSkuEvent(@RequestBody List<Long> skuIdList);

    /**
     * 更新ES销量信息
     * @param spuIdList spu id 集合
     */
    @PostMapping(PREFIX + "/sendSpuEvent")
    CommonResult<Boolean> sendSpuEvent(@RequestBody List<Long> spuIdList);

    /**
     * 更新组合商品信息
     * @param spuCombineList    组合商品ID集合
     */
    @PostMapping(PREFIX + "/sendSpuCombineEvent")
    CommonResult<Boolean> sendSpuCombineEvent(@RequestBody List<Long> spuCombineList);

    /**
     * 统计更新城市上架数据
     * @param areaId    城市ID
     */
    @PutMapping(PREFIX + "/updateAreaReleaseData")
    CommonResult<Boolean> updateAreaReleaseData(@RequestParam("areaId") Long areaId);

    /**
     * 统计更新全国上架数据
     */
    @PutMapping(PREFIX + "/updateGlobalReleaseData")
    CommonResult<Boolean> updateGlobalReleaseData(@RequestParam("sysCode") Long sysCode);
}
