package com.zksr.product.api.brand.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 品牌绑定品牌商
 * @date 2024/8/5 15:20
 */
@Data
@ApiModel(description = "品牌绑定品牌商")
public class BindBrandMerchantReqVO {

    @ApiModelProperty("品牌商ID")
    private Long brandMerchantId;

    @ApiModelProperty("品牌商ID")
    private List<Long> brandList;
}
