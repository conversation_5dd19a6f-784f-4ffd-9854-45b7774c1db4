package com.zksr.product.api.supplierItem.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
@NoArgsConstructor
public class SupplierItemDTO {

    @JsonIgnore
    private ProductType productType = ProductType.GLOBAL;

    /** 入驻商上架商品id */
    private Long supplierItemId;

    /** 平台商id */
    private Long sysCode;

    /** 平台商展示分类id */
    private Long saleClassId;

    /** 入驻商id */
    private Long supplierId;

    /** 商品SPU id */
    private Long spuId;

    /** 商品sku id */
    private Long skuId;

    /** 上架状态-数据字典 */
    private Integer shelfStatus;

    /** 平台商管理分类id */
    private Long catgoryId;

    /** 平台商品牌id */
    private Long brandId;

    /** 成本价 */
    private BigDecimal costPrice;

    /** 商品SPU编号 */
    private String spuNo;

    /** 商品SPU名称 */
    private String spuName;

    /** 库存数量 */
    private Long stock;

    /** 是否删除 1-是 0-否 */
    private Long isDelete;

    /** keyword */
    private String keyword;

    /** 国际条码 */
    private String barcode;

    /** 单位-数据字典（sys_prdt_unit） */
    private Long unit;

    /** 属性数组，JSON 格式 */
    private String properties;

    /** 标准价 */
    private BigDecimal markPrice;

    /** 建议零售价 */
    private BigDecimal suggestPrice;

    /** 保质期 */
    private Integer expirationDate;		 // 保质期

    /** 参考进价 */
    private BigDecimal referencePrice;

    /** 参考售价 */
    private BigDecimal referenceSalePrice;

    /** 平台商管理分类名称 */
    private String catgoryName;

    /** 平台商品牌名称 */
    private String brandName;

    /** 排序序号 */
    private Integer sortNum;

    /** 上架时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shelfDate; 		 // 上架时间

    /** 列表排序方式 */
    private String sortord;

    /** 列表排序顺序 */
    private Long sortOrder;

    /** 状态(数据字典 sys_common_status) */
    private Long status;

    /** 平台商展示分类名称 */
    private String saleClassName;


    /** 入驻商名称 */
    private String supplierName;

    /** 起订 */
    private Long minOq;

    /** 订货组数 */
    private Long jumpOq;

    /** 限购 */
    private Long maxOq;

    /** 商品规格 */
    private String specName;

    /** 小单位-上下架状态 */
    @Excel(name = "小单位-上下架状态")
    @ApiModelProperty(value = "小单位-上下架状态", example = "示例值")
    private Integer minShelfStatus;

    /** 中单位-上下架状态 */
    @Excel(name = "中单位-上下架状态")
    @ApiModelProperty(value = "中单位-上下架状态", example = "示例值")
    private Integer midShelfStatus;

    /** 大单位-上下架状态 */
    @Excel(name = "大单位-上下架状态")
    @ApiModelProperty(value = "大单位-上下架状态", example = "示例值")
    private Integer largeShelfStatus;

    /** 中单位-国际条码 */
    @Excel(name = "中单位-国际条码")
    private String midBarcode;

    /** 中单位-标准价 */
    @Excel(name = "中单位-标准价")
    private BigDecimal midMarkPrice;

    /** 中单位-成本价(供货价) */
    @Excel(name = "中单位-成本价(供货价)")
    private BigDecimal midCostPrice;

    /** 中单位-建议零售价 */
    @Excel(name = "中单位-建议零售价")
    private BigDecimal midSuggestPrice;

    /** 中单位-起订 */
    @Excel(name = "中单位-起订")
    private Long midMinOq;

    /** 中单位-订货组数 */
    @Excel(name = "中单位-订货组数")
    private Long midJumpOq;

    /** 中单位-限购 */
    @Excel(name = "中单位-限购")
    private Long midMaxOq;

    /** 大单位-国际条码 */
    @Excel(name = "大单位-国际条码")
    private String largeBarcode;

    /** 大单位-标准价 */
    @Excel(name = "大单位-标准价")
    private BigDecimal largeMarkPrice;

    /** 大单位-成本价(供货价) */
    @Excel(name = "大单位-成本价(供货价)")
    private BigDecimal largeCostPrice;

    /** 大单位-建议零售价 */
    @Excel(name = "大单位-建议零售价")
    private BigDecimal largeSuggestPrice;

    /** 大单位-起订 */
    @Excel(name = "大单位-起订")
    private Long largeMinOq;

    /** 大单位-订货组数 */
    @Excel(name = "大单位-订货组数")
    private Long largeJumpOq;

    /** 大单位-限购 */
    @Excel(name = "大单位-限购")
    private Long largeMaxOq;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @Excel(name = "最小单位-数据字典（sys_prdt_unit）", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "最小单位-数据字典（sys_prdt_unit）")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @Excel(name = "中单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "中单位-数据字典（sys_prdt_unit）")
    private Long midUnit;

    /** 中单位换算数量（换算成最小单位）） */
    @Excel(name = "中单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "中单位换算数量（换算成最小单位）")
    private Long midSize;

    /** 大单位-数据字典（sys_prdt_unit） */
    @Excel(name = "大单位-数据字典", readConverterExp = "sys_prdt_unit")
    @ApiModelProperty(value = "大单位-数据字典（sys_prdt_unit）")
    private Long largeUnit;

    /** 大单位换算数量（换算成最小单位）） */
    @Excel(name = "大单位换算数量（换算成最小单位）")
    @ApiModelProperty(value = "大单位换算数量（换算成最小单位）")
    private Long largeSize;

    /** 0-普通商品, 1-组合商品 */
    @ApiModelProperty(value = "0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    /** 活动开始时间, 活动商品 */
    @ApiModelProperty(value = "活动开始时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityStartTime;

    /** 活动结束时间, 活动商品 */
    @ApiModelProperty(value = "活动结束时间, 活动商品")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activityEndTime;

    /** 组合商品ID */
    @ApiModelProperty(value = "组合商品ID")
    private Long spuCombineId;

    /** 活动ID */
    @ApiModelProperty(value = "活动ID")
    private Long activityId;

    public SupplierItemDTO(Long spuId, Integer shelfStatus,Long supplierId) {
        this.supplierId = supplierId;
        this.spuId = spuId;
        this.shelfStatus = shelfStatus;
    }

    @JsonFormat
    public boolean isSpuCombine() {
        return Objects.nonNull(spuCombineId);
    }

    public Integer getShelfStatus(Integer unitSize) {
        if (Objects.isNull(unitSize)) {
            return NumberPool.INT_ZERO;
        }
        if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType().intValue() && Objects.nonNull(midJumpOq)) return midShelfStatus;
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType().intValue() && Objects.nonNull(largeJumpOq)) return largeShelfStatus;
        return minShelfStatus;
    }
}
