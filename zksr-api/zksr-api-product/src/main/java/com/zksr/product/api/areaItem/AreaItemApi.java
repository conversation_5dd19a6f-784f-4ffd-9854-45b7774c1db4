package com.zksr.product.api.areaItem;

import com.zksr.common.core.domain.vo.openapi.receive.AreaItemOpenDTO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.areaItem.form.PrdAreaItemImportForm;
import com.zksr.product.api.areaItem.vo.ApiAreaItemPageReqVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.enums.ApiConstants;
import com.zksr.report.api.homePages.dto.HomePagesSkuDataRespDTO;
import com.zksr.report.api.homePages.vo.HomePagesReqVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(
        contextId = "remoteAreaItemApi",
        value = ApiConstants.NAME
)
/**
*
 *  入驻商服务
* <AUTHOR>
* @date 2024/3/16 11:29
*/
public interface AreaItemApi {

    String PREFIX = ApiConstants.PREFIX + "/areaItem";

    @GetMapping(PREFIX + "/getAreaItemId")
    public CommonResult<AreaItemDTO> getAreaItemId(@RequestParam("areaItemId") Long areaItemId);

    @PostMapping(PREFIX + "/getAreaItemListByApi")
    public CommonResult<List<AreaItemDTO>> getAreaItemListByApi(@RequestBody AreaItemDTO itemDTO);

    /**
     * @param areaItemPageReqVO 请求参数
     * @return  本地上架商品分页数据
     */
    @PostMapping(PREFIX + "/getAreaItemPageByApi")
    public CommonResult<PageResult<PrdtAreaItemPageRespVO>> getAreaItemPageByApi(@RequestBody ApiAreaItemPageReqVO areaItemPageReqVO);

    /**
     * 统计已售数据
     * @param minAreaItemId 最小上架ID
     * @return  返回处理批次的最小上架ID
     */
    @GetMapping(PREFIX + "/updateSaleQtyTotal")
    CommonResult<Long> updateSaleQtyTotal(@RequestParam("minAreaItemId") Long minAreaItemId);

    /**
     * 根据组合商品ID 查询对应入驻商商品信息
     * @param spuCombineId 组合商品ID
     * @return  SupplierItemDTO
     */
    @PostMapping(PREFIX + "/getAreaItemByBySpuCombineId")
    public CommonResult<AreaItemDTO> getAreaItemBySpuCombineId(@RequestParam("spuCombineId") Long spuCombineId);

    /**
     * 修改全国上架商品
     * @param itemDTO
     * @return
     */
    @PostMapping(PREFIX + "/updateAreaItem")
    CommonResult<Boolean> updateAreaItem(@RequestBody AreaItemDTO itemDTO);

    /**
     * 根据活动ID 查询对应活动商品信息
     * @param activityId 活动ID
     * @return  SupplierItemDTO
     */
    @PostMapping(PREFIX + "/getAreaItemByActivityId")
    public CommonResult<AreaItemDTO>getAreaItemByActivityId(@RequestParam("activityId") Long activityId);

    /**
     * 获取PC首页SKU数据
     * @param reqVO
     * @return
     */
    @PostMapping(PREFIX + "/getHomePagesSkuData")
    CommonResult<List<HomePagesSkuDataRespDTO>> getHomePagesSkuData(@RequestBody HomePagesReqVO reqVO);

    /**
     * 城市上架商品信息导入
     * @param form
     * @return
     */
    @PostMapping(PREFIX + "/importDataDcEvent")
    CommonResult<String> importDataDcEvent(@RequestBody PrdAreaItemImportForm form);

    /**
     * 商品自动上下架
     * @param areaItemOpenDTO
     * @return
     */
    @PostMapping(PREFIX + "/addAreaItem")
    CommonResult<Boolean> addAreaItem(@RequestBody AreaItemOpenDTO areaItemOpenDTO);

}
