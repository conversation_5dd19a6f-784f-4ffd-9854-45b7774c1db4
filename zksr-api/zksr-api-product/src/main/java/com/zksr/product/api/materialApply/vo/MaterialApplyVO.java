package com.zksr.product.api.materialApply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * 素材应用与素材信息 VO
 *
 * <AUTHOR>
 * @date 2025-01-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialApplyVO {
    private static final long serialVersionUID=1L;

    /** 素材应用id */
    @ApiModelProperty(value = "素材应用id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialApplyId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 素材id */
    @ApiModelProperty(value = "素材id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long materialId;

    /** 素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品 */
    @ApiModelProperty(value = "素材应用类型;1-促销活动 2-全国上架商品 3-本地上架商品")
    private Integer applyType;

    /** 素材应用类型id */
    @ApiModelProperty(value = "素材应用类型id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long applyId;

    /** 生效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效时间")
    private Date startTime;

    /** 失效时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "失效时间")
    private Date endTime;

    /** 操作人 */
    @ApiModelProperty(value = "操作人")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long applyUserId;

    /** 素材图片地址 */
    @ApiModelProperty(value = "素材图片地址")
    private String img;

    /** 素材名称 */
    @Excel(name = "素材名称")
    @ApiModelProperty(value = "素材名称")
    private String name;

    /** 素材图片大小 */
    @Excel(name = "素材图片大小")
    @ApiModelProperty(value = "素材图片大小")
    private String imgSize;

}
