package com.zksr.product.utils;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 上架商品工具类
 * @date 2025/3/12 9:16
 */
public class ReleaseItemUtil {

    /**
     * 验证单位是否上架
     * @param areaItem   本地商品
     * @param unitSize   单位  {@link com.zksr.common.core.enums.UnitTypeEnum}
     * @return  true-上架, false-下架
     */
    public static boolean validateRelease(AreaItemDTO areaItem, Integer unitSize) {
        return Objects.nonNull(areaItem) && areaItem.getShelfStatus(unitSize) == NumberPool.INT_ONE;
    }

    /**
     * 验证单位是否上架
     * @param supplierItem  全国商品
     * @param unitSize      单位  {@link com.zksr.common.core.enums.UnitTypeEnum}
     * @return  true-上架, false-下架
     */
    public static boolean validateRelease(SupplierItemDTO supplierItem, Integer unitSize) {
        return Objects.nonNull(supplierItem) && supplierItem.getShelfStatus(unitSize) == NumberPool.INT_ONE;
    }
}
