package com.zksr.product.api.combine.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/1/4 14:35
 * @注释
 */
@Data
@NoArgsConstructor
@ApiModel("组合商品详情实体")
public class SpuCombineDtlRespDTO {

    /** 组合商品ID */
    @ApiModelProperty(value = "组合商品ID")
    private Long spuCombineId;

    /** skuID */
    @ApiModelProperty(value = "skuID")
    private Long skuId;

    /** 城市上架商品id */
    @ApiModelProperty(value = "城市上架商品id")
    private Long areaItemId;

    /** 全国上架商品id */
    @Excel(name = "全国上架商品id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierItemId;

    /** 封面图（url） */
    @Excel(name = "封面图（url）")
    @ApiModelProperty(value = "封面图（url）")
    private String thumb;

    /** 商品单位大小 */
    @ApiModelProperty(value = "商品单位大小")
    private Long skuUnitType;

    /** 最小单位-数据字典（sys_prdt_unit） */
    @ApiModelProperty(name = "最小单位-数据字典")
    private Long minUnit;

    /** 中单位-数据字典（sys_prdt_unit） */
    @ApiModelProperty(name = "中单位-数据字典")
    private Long midUnit;

    /** 大单位-数据字典（sys_prdt_unit） */
    @ApiModelProperty(name = "大单位-数据字典")
    private Long largeUnit;

    @Excel(name = "显示单位", readConverterExp = "显示单位")
    @ApiModelProperty(value = "显示单位")
    private String unitName;

    /** 数量 */
    @ApiModelProperty(value = "数量")
    private Long qty;

    /** 是否为赠品 */
    @ApiModelProperty(value = "是否为赠品")
    private Integer giftFlag;

    /** 商品SPU编号 */
    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    /** 商品SPU名称 */
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /** 平台商品牌id */
    @ApiModelProperty(value = "平台商品牌id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 平台商品牌名称 */
    @ApiModelProperty(value = "平台商品牌名称")
    private String brandName;

    /** 分类名称 */
    @ApiModelProperty(value = "分类名称")
    private String className;

    /** 国际条码 */
    @ApiModelProperty(value = "国际条码", example = "示例值")
    private String barcode;

    /** 标准价 */
    @ApiModelProperty(value = "标准价", example = "示例值")
    private BigDecimal markPrice;

    /** 供货价 */
    @ApiModelProperty(value = "供货价")
    private BigDecimal costPrice;

    /** 入驻商名称 */
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;
}
