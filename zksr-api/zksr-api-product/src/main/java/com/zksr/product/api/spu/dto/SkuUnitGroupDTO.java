package com.zksr.product.api.spu.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品发布spusku组, 上架的数据才会在这里里面, 未上架的数据不会有
 * @date 2024/5/30 19:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkuUnitGroupDTO {

    @ApiModelProperty(value = "itemId", notes = "本地或者全国上架ID")
    private Long itemId;

    @ApiModelProperty("skuId")
    private Long skuId;

    @ApiModelProperty("图片")
    private String thumb;

    @ApiModelProperty("库存")
    private Long stock;

    @ApiModelProperty("属性规格")
    private String properties;

    /** 保质期 */
    @ApiModelProperty(value = "保质期")
    private Integer expirationDate;

    @ApiModelProperty("单位集合")
    private List<SkuUnitDTO> unitList;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SkuUnitGroupDTO that = (SkuUnitGroupDTO) o;
        return Objects.equals(itemId, that.itemId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(itemId);
    }
}
