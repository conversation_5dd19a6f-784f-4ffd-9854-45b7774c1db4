package com.zksr.product.api.areaClass.dto;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * 城市展示分类对象 prdt_area_class
 *
 * <AUTHOR>
 * @date 2024-03-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AreaClassDTO implements Serializable {

    private static final long serialVersionUID = -1731576249768028371L;

    /** 城市展示分类id */
    private Long areaClassId;

    /** 平台商id */
    private Long sysCode;

    /** 城市id */
    private Long areaId;

    /** 父id */
    private Long pid;

    /** 城市展示分类名 */
    private String areaClassName;

    /** 分类图标 */
    private String icon;

    /** 排序 */
    private Long sort;

    /** 状态 */
    private String status;

    /** 是否是电子围栏分类 */
    private Long dzwlFlag;

    /** 入驻商id */
    private Long supplierId;

    /** 级别 */
    @Excel(name = "级别")
    private Integer level;

    /** 是否展示生产日期 0-关闭, 1-开启 */
    @Excel(name = "是否展示生产日期 0-关闭, 1-开启")
    @ApiModelProperty(value = "是否展示生产日期 0-关闭, 1-开启")
    private Integer showProduceDate = NumberPool.INT_ONE;

    /** 生产日期格式 yy/MM/dd 年月日 yy/MM 年月 */
    @Excel(name = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    @ApiModelProperty(value = "生产日期格式 yy/MM/dd 年月日 yy/MM 年月")
    private String produceDateFormat;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AreaClassDTO that = (AreaClassDTO) o;
        return Objects.equals(areaClassId, that.areaClassId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(areaClassId);
    }
}
