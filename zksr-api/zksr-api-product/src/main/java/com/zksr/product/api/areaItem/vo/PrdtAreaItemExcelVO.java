package com.zksr.product.api.areaItem.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.DateUtils;
import lombok.Data;

import java.util.Date;

/**
 * 城市上架商品Excel对象
 */
@Data
public class PrdtAreaItemExcelVO {
    @Excel(name = "入驻商名称")
    private String supplierName;
    @Excel(name = "产品编码")
    private String spuNo;
    @Excel(name = "产品名称")
    private String spuName;
    @Excel(name = "条形码")
    private String barcode;
    @Excel(name = "上架状态")
    private String shelfStatus;
    @Excel(name = "品牌名称")
    private String brandName;
    @Excel(name = "平台管理类别")
    private String catgoryName;
    @Excel(name = "上架城市")
    private String areaName;
    @Excel(name = "展示类别")
    private String className;
    @Excel(name = "小单位是否上架")
    private String minShelfStatus;
    @Excel(name = "中单位是否上架")
    private String midShelfStatus;
    @Excel(name = "大单位是否上架")
    private String largeShelfStatus;
    @Excel(name = "上架时间", dateFormat = DateUtils.YYYY_MM_DD_HH_MM_SS)
    private Date shelfDate;
    @Excel(name = "小单位名称")
    private String unit;
    @Excel(name = "中单位名称")
    private String midUnit;
    @Excel(name = "大单位名称")
    private String largeUnit;
    @Excel(name = "标准价小单位")
    private String markPrice;
    @Excel(name = "标准价中单位")
    private String midMarkPrice;
    @Excel(name = "标准价大单位")
    private String largeMarkPrice;
    @Excel(name = "零售价小单位")
    private String suggestPrice;
    @Excel(name = "零售价中单位")
    private String midSuggestPrice;
    @Excel(name = "零售价大单位")
    private String largeSuggestPrice;
    @Excel(name = "保质期")
    private Integer expirationDate;
}
