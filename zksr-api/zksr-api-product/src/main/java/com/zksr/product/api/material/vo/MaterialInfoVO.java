package com.zksr.product.api.material.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/1/11 9:13
 */
@Data
public class MaterialInfoVO {

    /** 素材id */
    @ApiModelProperty("素材ID")
    private Long materialId;

    /** 素材图片地址 */
    @ApiModelProperty("素材图片地址")
    private String img;

    /** 活动开始时间 */
    @ApiModelProperty("活动开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date startTime;

    /** 失效时间 */
    @ApiModelProperty("失效时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date endTime;
}
