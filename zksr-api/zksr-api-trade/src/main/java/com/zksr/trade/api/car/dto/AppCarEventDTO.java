package com.zksr.trade.api.car.dto;

import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车操作事件, 用于异步同步redis 到数据库
 * @date 2024/3/27 15:15
 */
@Data
@ApiModel(description = "购物车操作事件")
@ToString
public class AppCarEventDTO {

    @ApiModelProperty("影响ID")
    private List<AppCarIdDTO> carIds;

    @ApiModelProperty("事件类型")
    private String type;

    @ApiModelProperty("门店ID")
    private Long branchId;

    public AppCarEventDTO() {

    }

    public AppCarEventDTO(List<AppCarIdDTO> carIds, String type, Long branchId) {
        this.carIds = carIds;
        this.type = type;
        this.branchId = branchId;
    }

    /**
     * 标准事件
     */
    public static final String TYPE_STANDARD = "standard";

    /**
     * 全部删除
     */
    public static final String TYPE_CLEAN_ALL= "cleanAll";

    /**
     * 全部选中
     */
    public static final String TYPE_SELECTED_ALL= "selectedAll";

    /**
     * 全不选
     */
    public static final String TYPE_UNSELECTED_ALL= "unselectedAll";
}
