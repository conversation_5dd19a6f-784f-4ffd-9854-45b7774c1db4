package com.zksr.trade.api.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2024年04月23日 17:57
 * @description: AfterOrderPageRespDTO
 */
@Data
@ApiModel("订单售后实体 返回")
@Accessors(chain = true)
public class AfterOrderPageRespDTO {

    @ApiModelProperty(value = "售后订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long afterId;

    @ApiModelProperty(value = "售后订单编号")
    private String afterNo;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店地址")
    private String branchAddress;

    @ApiModelProperty(value = "处理状态（取数据字典{sys_after_handle_state}）")
    private Long handleState;

    @ApiModelProperty(value = "售后订单总数量")
    private Long afterOrderQty;

    @ApiModelProperty(value = "售后订单总金额(实际退款金额)")
    private BigDecimal afterOrderAmt;

    @ApiModelProperty(value = "退款原因")
    private String reason;

    @ApiModelProperty(value = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    @ApiModelProperty(value = "售后类型(取数据字典{sys_after_type});待定：1仅退款 2退货退款 ")
    private Long afterType;


    @ApiModelProperty("门店联系人")
    private String contactName;

    @ApiModelProperty("门店联系电话")
    private String contactPhone;

    @ApiModelProperty(value = "退款完成时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date refundTime;

    @ApiModelProperty(value = "售后订单总金额（优惠前金额）")
    private BigDecimal returnOrderAmt;

    @ApiModelProperty(value = "售后订单总优惠金额")
    private BigDecimal returnDiscountAmt;

    @ApiModelProperty(value = "入驻商订单集合")
    List<AfterSupplierRespDTO> afterSupplierRespDTOS;
}
