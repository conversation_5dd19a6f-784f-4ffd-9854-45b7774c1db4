package com.zksr.trade.api.order.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年03月28日 14:14
 * @description: 入驻商商品明细数据VO
 */
@Data
@ApiModel("订单信息 - 入驻商订单明细信息Request VO")
@Accessors(chain = true)
public class TrdSupplierOrderDtlSaveVO {

    /** 入驻商订单id */
    private Long supplierOrderId;

    /** 入驻商订单编号 */
    private String supplierOrderNo;

    /** 入驻商订单明细编号 */
    private String supplierOrderDtlNo;

    /** 入驻商id */
    private Long supplierId;

    /** 入驻商上架商品id 全国*/
    private Long supplierItemId;

    /** 城市上架商品id */
    private String areaItemId;

    /** 商品SPU id */
    private Long spuId;

    /** 商品sku id */
    private Long skuId;

    /** 商品spu 名称 */
    private String spuName;

    /** 封面图（url） */
    private String thumb;

    /** 封面视频（url） */
    private String thumbVideo;

    /** 详情页轮播（json） */
    private String images;

    /** 详情信息(富文本) */
    private String details;

    /** 商品数量 */
    private BigDecimal totalNum;

    /** 合计金额（price*total_num） */
    private BigDecimal totalAmt;

    /** 成交价 */
    private BigDecimal price;

    /** 原销售价 */
    private BigDecimal salePrice;

    /** 是否是赠品 1-是  0-否 */
    private Integer giftFlag;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 优惠劵优惠金额(分摊的) */
    private BigDecimal couponDiscountAmt;

    /** 优惠劵优惠金额(不分摊的) */
    private BigDecimal couponDiscountAmt2;

    /** 活动优惠金额(分摊的) */
    private BigDecimal activityDiscountAmt;

    /**
     * 分类编号
     */
    private Long categoryId;

    /**
     * spu规格
     */
    private String specName;

    /**
     *  主订单ID
     */
    private Long orderId;

    /**
     *  商品类型 0：全国商品 1：本地商品
     */
    private Integer itemType;

    /**
     *  商品配送状态
     */
    private Long deliveryState;

    /**
     *  是否同步  1：是 0：否
     */
    private Integer syncFlag;

    /** 精准成交价（6位小数） */
    private BigDecimal exactPrice;

    /** 精准商品金额（6位小数） */
    private BigDecimal exactTotalAmt;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 3-货到付款未收款 4-货到付款已收款 */
    private Integer payState;

    /** 商品订单原总金额（salePrice*total_num） */
    private BigDecimal subOrderAmt;

    /** 入驻商订单行号 */
    private Long lineNum;

    //=========================下单===========================
    /** 订单购买单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "订单购买单位")
    private String orderUnit;

    /** 单位大小 {@link com.zksr.common.core.enums.UnitTypeEnum}*/
    @Excel(name = "购买单位大小")
    private Integer orderUnitType;

    /** 订单购买单位数量;购买的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "订单购买单位数量")
    private Long orderUnitQty;

    /** 订单换算数量;小单位为1，中、大单位的换算数量  取SPU中对应单位的换算数量*/
    @Excel(name = "订单购买换算数量")
    private BigDecimal orderUnitSize;

    /** 订单购买单位价格 */
    @Excel(name = "订单购买原单位价格")
    private BigDecimal orderUnitPrice;

    /**
     * 平台商品牌id
     */
    private Long brandId;

    /** 订单购买单位实际销售单价（6位小数）  取价逻辑: 合计金额（totalAmt） / 下单单位数量*/
    private BigDecimal orderSalesUnitPrice;

    /** 最旧生产日期 */
    private Date oldestDate;

    /** 最新生产日期 */
    private Date latestDate;

    /**
     * 赠品优惠分摊单价（6位小数）
     */
    private BigDecimal exactGiftSharePrice = BigDecimal.ZERO;

    /**
     * 赠品分摊价小计
     */
    private BigDecimal giftShareSubtotalAmt = BigDecimal.ZERO;

    /**
     * 均摊赠品优惠后的最小单位成交价（6位小数）
     */
    private BigDecimal afterGiftSharePrice = BigDecimal.ZERO;

    /**
     * 均摊赠品优惠后的购买单位成交价（6位小数）
     */
    private BigDecimal afterGiftShareUnitPrice = BigDecimal.ZERO;
    /**
     * 均摊赠品优惠后的小计
     */
    private BigDecimal afterGiftShareSubtotalAmt = BigDecimal.ZERO;

    /**
     * SKU最小单位数量平均单价（6位小数）
     */
    private BigDecimal skuAvgPrice = BigDecimal.ZERO;

    /**
     * 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价）
     */
    private Integer giftPriceType;

    /**
     * 赠品取价算法- 对应最终最小单位单价 （6位小数）
     */
    private BigDecimal resPrice;

    /**
     * 赠品取价算法- 对应最终购买单位单价 （6位小数）
     */
    private BigDecimal resUnitPrice;

    /**
     * 赠品取价算法- 对应最终金额
     */
    private BigDecimal resAmt;

    /**
     * 加单指令ID
     */
    private Long commandId;

//=========================================不需要保存数据库字段===================================
    /**
     *  唯一值
     */
    private String uuIdNo;

    /**
     *  活动ID信息（目前只有赠品活动使用，如果这个商品同时参加了满赠和买赠  id使用；分隔）
     */
    private String activityIdInfo;

    @ApiModelProperty(name = "是否负库存下单 0-否 1-是")
    private Integer stockShortFlag;
}
