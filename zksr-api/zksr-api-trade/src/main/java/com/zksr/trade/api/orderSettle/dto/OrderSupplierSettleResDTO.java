package com.zksr.trade.api.orderSettle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月16日 10:23
 * @description: OrderSupplierSettleResDTO
 */
@Data
@ApiModel("入驻商订单结算信息 - 返回实体")
public class OrderSupplierSettleResDTO {
    @ApiModelProperty(value = "日期(外层使用)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createDate;

    @ApiModelProperty(value = "日期时间(内层使用)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "消费金额")
    private BigDecimal settleAmt;

    @ApiModelProperty(value = "订单数量")
    private Long orderCount;

    @ApiModelProperty(value = "入驻商订单单号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "单据金额")
    private BigDecimal subPayAmt;
}
