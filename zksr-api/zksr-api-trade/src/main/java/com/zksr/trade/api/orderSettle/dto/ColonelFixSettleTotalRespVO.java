package com.zksr.trade.api.orderSettle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员提成统计
 * @date 2024/9/27 10:48
 */
@ApiModel(description = "业务员提成统计")
@Data
public class ColonelFixSettleTotalRespVO {

    @ApiModelProperty("今日提成")
    private BigDecimal todaySettleAmt;

    @ApiModelProperty("本月提成")
    private BigDecimal monthSettleAmt;

    @ApiModelProperty("上月提成")
    private BigDecimal beforeMonthSettleAmt;
}
