package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/4/26 10:10
 * 业务员app-订单列表
 */
@Data
@ApiModel("业务员app-订单列表")
public class TrdColonelAppOrderListRespVO {
    private static final long serialVersionUID = 1L;

    /** 单据id（销售或退售后） */
    @ApiModelProperty(value = "单据id（销售或退售后）")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "单据编号（销售或退售后）")
    @ApiModelProperty(value = "单据编号（销售或退售后）")
    private String orderNo;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店地址")
    private String branchAddress;

    @ApiModelProperty(value = "是否是业务员下单 1：是 0 否")
    private Integer colonelFlag;

    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /** 下单时间 */
    @Excel(name = "下单时间")
    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 付款时间 */
    @Excel(name = "付款时间")
    @ApiModelProperty(value = "付款时间")
    private String payTime;

    /** 付款状态 */
    @Excel(name = "付款状态")
    @ApiModelProperty(value = "付款状态")
    private String payState;

    /** 付款方式 */
    @Excel(name = "付款方式")
    @ApiModelProperty(value = "付款方式")
    private String payWay;




    //===========================订单=========================

    /** 订单金额 */
    @ApiModelProperty(value = "订单金额")
    private BigDecimal orderAmt;

    /** 订单实际支付金额 */
    @ApiModelProperty(value = "订单实际支付金额")
    private BigDecimal orderPayAmt;

    /** 订单优惠金额 */
    @ApiModelProperty(value = "订单优惠金额")
    private BigDecimal orderDiscountAmt;


    //===========================售后=========================
    /** 实际退款金额 */
    @ApiModelProperty(value = "实际退款金额（优惠后）")
    private BigDecimal refundAmt;

    /** 售后金额（优惠前） */
    @ApiModelProperty(value = "售后金额（优惠前）")
    private BigDecimal returnAmt;

    /** 售后优惠金额 */
    @ApiModelProperty(value = "售后优惠金额")
    private BigDecimal returnDiscountAmt;

    /** 退款原因 */
    @ApiModelProperty(value = "退款原因")
    private String refundReason;







}
