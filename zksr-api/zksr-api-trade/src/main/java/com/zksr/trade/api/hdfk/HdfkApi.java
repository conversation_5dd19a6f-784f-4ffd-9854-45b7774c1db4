package com.zksr.trade.api.hdfk;

import com.zksr.common.core.domain.vo.openapi.receive.OrderHdfkSettleVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO;
import com.zksr.trade.api.hdfk.dto.HdfkPayDTO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkOrderRespVO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveReqVO;
import com.zksr.trade.api.hdfk.vo.HdfkPaySaveRespVO;
import com.zksr.trade.api.order.dto.TrdOrderResDto;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/5/28
 * @desc
 */
@FeignClient(
        contextId = "remoteHdfkApi",
        value = ApiConstants.NAME
)
public interface HdfkApi {

    String PREFIX = ApiConstants.PREFIX + "/hdfk";

    /**
     * 获取货到付款订单列表
     * @param branchHdfkReqVO
     * @return
     */
    @PostMapping(value = PREFIX + "/getBranchHdfkList")
    CommonResult<List<BranchHdfkOrderRespVO>> getBranchHdfkList(@RequestBody BranchHdfkReqVO branchHdfkReqVO);

    /**
     * 获取货到付款未支付统计
     * @param branchHdfkReqVO
     * @return
     */
    @PostMapping(value = PREFIX + "/getBranchHdfkTotal")
    CommonResult<HdfkNoPaymentTotalDTO> getBranchHdfkNoPaymentTotal(@RequestBody BranchHdfkReqVO branchHdfkReqVO);

    /**
     * 创建货到付款支付订单
     * @param paySaveReqVO
     * @return
     */
    @PostMapping(value = PREFIX + "/createPay")
    CommonResult<HdfkPaySaveRespVO> createPay(@RequestBody HdfkPaySaveReqVO paySaveReqVO);

    /**
     * 货到付款支付回调
     * @param orderNo
     * @param payPlatform
     * @param payWay
     * @return
     */
    @GetMapping(value = PREFIX + "/orderPaySuccessCallback")
    CommonResult<Boolean> orderPaySuccessCallback(@RequestParam("orderNo") String orderNo, @RequestParam("payPlatform") String payPlatform, @RequestParam("payWay") String payWay);

    /**
     * 根据货到付款ID 获取货到付款信息
     * @param hdfkPayId
     * @return
     */
    @GetMapping(value = PREFIX + "/getHdfkPayById")
    CommonResult<HdfkPayDTO> getHdfkPayById(@RequestParam("hdfkPayId") Long hdfkPayId);
    /**
     * 创建货到付款支付订单 并且无需付款
     * @param orderHdfkSettleVO
     * @return
     */
    @PostMapping(value = PREFIX + "/addHdfkSettle")
    CommonResult<Boolean> addHdfkSettle(@RequestParam("sysCode") Long sysCode,@RequestBody OrderHdfkSettleVO orderHdfkSettleVO);

    /**
     * 根据货到付款ID集合 获取货到付款集合信息
     * @param hdfkPayIdList
     * @return
     */
    @GetMapping(value = PREFIX + "/getHdfkPayListByIds")
    CommonResult<List<HdfkPayDTO>> getHdfkPayListByIds(@RequestParam("hdfkPayIdList") List<Long> hdfkPayIdList);
}
