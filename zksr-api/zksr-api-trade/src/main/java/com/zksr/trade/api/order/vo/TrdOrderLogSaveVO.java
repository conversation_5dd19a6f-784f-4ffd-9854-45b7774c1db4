package com.zksr.trade.api.order.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年04月09日 10:00
 * @description: TrdOrderLogSaveVO
 */
@Data
@ApiModel("订单信息 - 订单日志Request VO")
public class TrdOrderLogSaveVO {
    /** 入驻商订单明细id */
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    private String supplierOrderDtlNo;

    /** 操作前状态 */
    private Long beforeState;

    /** 操作后状态 */
    private Long afterState;

    /** 操作类型(数据字典) */
    private Long operateType;

    /** 订单日志信息 */
    private String content;
}
