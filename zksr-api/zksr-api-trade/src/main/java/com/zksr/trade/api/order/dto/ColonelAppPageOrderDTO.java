package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 *  客户使用优惠劵信息汇总
 */
@Data
@ApiModel("业务员APP -- 首页列表当天订单统计信息")
public class ColonelAppPageOrderDTO {

    /** 订单ID/售后订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 订单详情实际销售金额 （合计金额-发货前取消金额） / 门店退货金额 */
    @Excel(name = "订单实际销售金额 / 门店退货金额")
    private BigDecimal realSaleAmt;

    @ApiModelProperty("下单用户是否本身是业务员(0-否（默认）  1-是)")
    private Integer colonelFlag;

    /** 门店ID */
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

/*    *//** 业务提成 *//*
    @ApiModelProperty(value = "业务提成")
    private BigDecimal percentageAmt;*/
}
