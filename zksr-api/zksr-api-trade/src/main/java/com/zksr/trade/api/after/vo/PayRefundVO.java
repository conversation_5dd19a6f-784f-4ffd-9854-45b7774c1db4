package com.zksr.trade.api.after.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024年04月24日 15:58
 * @description: 退款回调
 */
@Data
@Accessors(chain = true)
public class PayRefundVO {
    /**
     * 退款状态
     *
     * 枚举 {@link com.zksr.common.core.enums.PayRefundStatusEnum}
     */
    @ApiModelProperty("退款状态,0-未退款,1-发起退款成功,2-退款成功,3-退款失败")
    private Integer status;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 售后单号
     */
    @ApiModelProperty("售后退款单号")
    private String refundNo;

    /**
     * 外部退款号
     */
    @ApiModelProperty("商户退款编号")
    private String outRefundNo;

    /**
     * 调用渠道报错时，错误信息
     */
    @ApiModelProperty("错误信息")
    private String errorMsg;

    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型")
    private Integer orderType;
}
