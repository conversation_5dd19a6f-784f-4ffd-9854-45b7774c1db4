package com.zksr.trade.api.after;

import com.zksr.common.core.domain.vo.openapi.TrdSupAfterDtlRequest;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.after.dto.*;
import com.zksr.trade.api.after.vo.AfterOrderPageReqVO;
import com.zksr.trade.api.after.vo.OrderAfterRequest;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.express.TrdOrderExpress;
import com.zksr.trade.api.order.dto.ColonelAppOrderListTotalDTO;
import com.zksr.trade.api.order.dto.ColonelAppPageOrderDTO;
import com.zksr.trade.api.order.vo.*;
import com.zksr.trade.api.after.vo.*;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024年04月20日 10:42
 * @description: AfterApi
 */
@FeignClient(
        contextId = "remoteAfterApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface AfterApi {
    String PREFIX = ApiConstants.PREFIX + "/after";

    /**
     * 根据条件获取可售后的订单数据
     * @param request
     * @return
     */
    @PostMapping(value =PREFIX + "/orderAfterInitiate")
    public CommonResult<OrderAfterResDTO> orderAfterInitiate(@RequestBody OrderAfterRequest request);

    /**
     * 根据条件获取门店合单售后商品列表
     * @param reqVo
     * @return
     */
    @PostMapping(value =PREFIX + "/branchSkuMergeAfterList")
    public CommonResult<List<BranchSkuMergeAfterResDTO>> branchSkuMergeAfterList(@RequestBody BranchSkuMergeAfterReqVO reqVo);

    /**
     * 根据条件获取门店合单售后商品信息- 提交页
     * @param reqVo
     * @return
     */
    @PostMapping(value =PREFIX + "/orderMergeAfter")
    public CommonResult<OrderMergeAfterResDTO> orderMergeAfter(@RequestBody BranchSkuMergeAfterReqVO reqVo);

    /**
     * 保存售后订单
     * @param request
     * @return
     */
    @PostMapping(value =PREFIX + "/saveAfterOrder")
    public CommonResult<OrderAfterSaveResDTO> saveAfterOrder(@RequestBody OrderAfterSaveRequest request);

    /**
     * 保存售后订单 - 合单售后
     * @param request
     * @return
     */
    @PostMapping(value =PREFIX + "/saveMergeAfterOrder")
    public CommonResult<OrderAfterSaveResDTO> saveMergeAfterOrder(@RequestBody OrderMergeAfterRequest request);


    /**
     * 分页查询售后订单
     * @param reqVO
     * @return
     */
    @PostMapping(value =PREFIX + "/pageAfter")
    public PageResult<AfterOrderPageRespDTO> pageAfter(@RequestBody AfterOrderPageReqVO reqVO);

    /**
     * 分页查询售后订单
     * @param reqVO
     * @return
     */
    @PostMapping(value =PREFIX + "/getAfterOrderInfo")
    public CommonResult<AfterOrderPageRespDTO> getAfterOrderInfo(@RequestBody AfterOrderPageReqVO reqVO);


    /**
     * 售后订单上传物流单号提交
     * @param expressSaveVO
     * @return
     */
    @PostMapping(value =PREFIX + "/uploadAfterExpressDelivery")
    public CommonResult<Boolean> uploadAfterExpressDelivery(@RequestBody OrderAfterUploadExpressSaveVO expressSaveVO);

    /**
     * 取消售后订单退货
     * @param afterId
     * @return
     */
    @PutMapping(value =PREFIX + "/cancelAfterReturn")
    public CommonResult<Boolean> cancelAfterReturn(@RequestParam("afterId") Long afterId);



    /**
     * 售后订单退款成功回调
     * @param refundVO
     * @return
     */
    @PostMapping(value =PREFIX + "/afterRefundSuccessCallback")
    public CommonResult<Boolean> afterRefundSuccessCallback(@RequestBody PayRefundVO refundVO);

    /**
     * 售后订单退款成功失败回调
     * @param refundVO
     * @return
     */
    @PostMapping(value =PREFIX + "/afterRefundFailCallback")
    public CommonResult<Boolean> afterRefundFailCallback(@RequestBody PayRefundVO refundVO);

    /**
     * @Description: 业务员app获取售后订单列表
     * @Author: liyi
     * @Date: 2024/4/26 15:15
     */
    @PostMapping(value =PREFIX + "/coloneAppPageAfterOrderList")
    CommonResult<ColonelAppOrderListTotalDTO> selectMemColonelAppAfterOrder(@RequestBody TrdColonelAppOrderListPageReqVO orderPageReqVO);

    /**
     * @Description: 业务员app获取订单列表详情
     * @Author: liyi
     * @Date: 2024/4/26 15:15
     */
    @PostMapping(value =PREFIX + "/getMemColoneAppAfterOrderDetail")
    public List<TrdColonelAppOrderDetailRespVO> getMemColonelAppAfterOrderDetail(@RequestParam("afterId") Long afterId);

    @PostMapping(value =PREFIX + "/getAfterDtlByDtlId")
    AfterApproveDtlEditVO getAfterDtlByDtlId(@RequestBody TrdSupAfterDtlRequest request);

    @PostMapping(value =PREFIX +"/approveAfterReturn")
    public CommonResult<Boolean> approveAfterReturn(@RequestBody AfterApproveDtlEditVO editVO);

    @PostMapping(value =PREFIX +"/approveAfterRefund")
    public CommonResult<Boolean> approveAfterRefund(@RequestBody AfterApproveDtlEditVO editVO);

    @PostMapping(value =PREFIX +"/getAfterByAfterNo")
    CommonResult<TrdAfter> getAfterByAfterNo(@RequestParam("afterNo") String afterNo);

    /**
     * 根据售后单明细id查询物流信息
     * @param supplierAfterDtlId
     * @return
     */
    @PostMapping(value =PREFIX +"/getOrderExpressBySupAfterDtlId")
    TrdOrderExpress getOrderExpressBySupAfterDtlId(@RequestParam("supplierAfterDtlId") Long supplierAfterDtlId);

    /**
     * 根据ID 查询售后订单
     *
     * @param afterId
     * @return
     */
    @GetMapping(value = PREFIX + "/getAfterById")
    CommonResult<TrdAfter> getAfterById(@RequestParam("afterId")Long afterId);


    /**
     * 根据订单ID校验是否存在未完成售后订单  true: 存在  false:不存在
     * @param orderId
     * @return
     */
    @GetMapping(value = PREFIX + "/checkNotFinishAfter")
    CommonResult<Boolean> checkNotFinishAfterByOrderId(@RequestParam("orderId")Long orderId);

    /**
     * 根据门店查询待退单金额
     * @param currentMonthId
     * @param branchIds
     * @return
     */
    @PostMapping(value = PREFIX + "/getPendingRefundAmount")
    CommonResult<List<Map<String, Object>>> getPendingRefundAmount(@RequestParam("currentMonthId") String currentMonthId,@RequestBody List<String> branchIds);

    /**
     * 根据业务员查询待退单金额
     * @param currentMonthId
     * @param colonelIds
     * @return
     */
    @PostMapping(value = PREFIX + "/getColonelPendingRefundAmount")
    CommonResult<List<Map<String, Object>>> getColonelPendingRefundAmount(@RequestParam("currentMonthId") String currentMonthId,@RequestBody List<String> colonelIds);

    /**
     * 获取入驻商售后详情
     * @param supplierAfterDtlIdList    入驻商详情ID集合
     * @return  入驻商售后详情集合
     */
    @PostMapping(value = PREFIX + "/getAfterDtlListById")
    CommonResult<List<SupplierAfterDtlDTO>> getAfterDtlListById(@RequestBody List<Long> supplierAfterDtlIdList);

    /**
     * 获取入驻商售后详情
     * @param supplierAfterId    入驻商售后订单
     * @return  入驻商售后详情集合
     */
    @PostMapping(value = PREFIX + "/getAfterDtlListBySupplierAfterId")
    CommonResult<List<SupplierAfterDtlDTO>> getAfterDtlListBySupplierAfterId(@RequestParam("supplierAfterId") Long supplierAfterId);

    /**
     * 获取入驻商售后主单
     * @param supplierAfterId   入驻商售后主单ID
     * @return
     */
    @GetMapping(value = PREFIX + "/getSupplierAfter")
    CommonResult<TrdSupplierAfterDTO> getSupplierAfter(@RequestParam("supplierAfterId") Long supplierAfterId);

    /**
     * 根据业务员ID获取业务员APP首页的售后金额（当天）
     *
     * @param colonelId
     * @return
     */
    @GetMapping(value =PREFIX + "/getColonelAppPageAfterAmt")
    CommonResult<BigDecimal> getColonelAppPageAfterAmt(@RequestParam("colonelId") Long colonelId);

    @PostMapping(value =PREFIX + "/getAfterExportPage")
    CommonResult<List<AfterOrderExportVO>> getAfterExportPage(@RequestBody AfterOrderPageReqVO pageVO);
}


