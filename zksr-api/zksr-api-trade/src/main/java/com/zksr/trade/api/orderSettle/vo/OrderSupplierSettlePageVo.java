package com.zksr.trade.api.orderSettle.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年04月16日 10:19
 * @description: OrderSupplierSettlePageVo
 */
@Data
@ApiModel("入驻商订单结算信息 - 请求实体")
public class OrderSupplierSettlePageVo{
    @ApiModelProperty(value = "入驻商ID", hidden = true)
    private Long supplierId;

    @ApiModelProperty(value = "选择日期 外层传 年月（yyyy-mm） 明细（yyyy-mm-dd） ")
    private String startDate;
}
