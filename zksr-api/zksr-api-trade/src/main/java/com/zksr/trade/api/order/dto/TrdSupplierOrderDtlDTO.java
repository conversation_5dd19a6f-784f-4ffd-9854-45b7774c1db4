package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.trade.api.express.dto.OrderExpressResDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("入驻商订单对象")
@Accessors(chain = true)
public class TrdSupplierOrderDtlDTO {

    @ApiModelProperty("商品SPU")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty("商品sku")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty("封面图")
    private String thumb;

    @ApiModelProperty("封面视频")
    private String thumbVideo;

    @ApiModelProperty("商品数量")
    private Long totalNum;

    @ApiModelProperty(value = "商品最小单位数量")
    private Long minTotalNum;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    private Long supplierOrderId;

    @ApiModelProperty("合计金额")
    private BigDecimal totalAmt;

    @ApiModelProperty("成交价")
    private BigDecimal price;

    @ApiModelProperty("销售原价")
    private BigDecimal originalPrice;

    @ApiModelProperty("订单状态")
    private Long deliveryState;

    @ApiModelProperty("优惠金额(包含优惠劵、活动及其他)")
    private BigDecimal discountAmt;

    @ApiModelProperty("是否是赠品")
    private Integer giftFlag;

    @ApiModelProperty("商品名称")
    private String spuName;

    @ApiModelProperty("规格信息")
    private String properties;

    @ApiModelProperty("商品条码")
    private String itemBarcode;

    @ApiModelProperty("商品编号（SPU）")
    private String spuNo;

    @ApiModelProperty("商品类型（0：全国商品，1：本地商品）")
    private Integer itemType;

    @ApiModelProperty("全国商品上架ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierItemId;

    @ApiModelProperty("本地商品上架ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaItemId;

    @ApiModelProperty("订单明细ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierOrderDtlId;

    @ApiModelProperty("快递信息集合")
    private List<OrderExpressResDTO> orderExpressResDTOList;

    /**
     * 字典值 {@link com.zksr.common.core.enums.ProductType}
     */
    @ApiModelProperty("商品类型")
    private String productType;

    @ApiModelProperty(value = "商品最小单位")
    private Long minUnit;

    @ApiModelProperty("下单单位")
    private String orderUnit;

    @ApiModelProperty("单位大小")
    private Long orderUnitType;

    @ApiModelProperty("原销售金额")
    private BigDecimal saleAmt;

    @ApiModelProperty("售后提示信息")
    private String afterPromptInfo;

    /** 最旧生产日期 */
    @ApiModelProperty("最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date oldestDate;

    /** 最新生产日期 */
    @ApiModelProperty("最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestDate;

    /** 最旧生产日期 */
    @ApiModelProperty("最旧生产日期 (展示分类格式化)")
    private String oldestDateFormat;

    /** 最新生产日期 */
    @ApiModelProperty("最新生产日期 (展示分类格式化)")
    private String latestDateFormat;

    /** 收货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "收货时间")
    private Date receiveTime;

    /** 商品结算（分佣）金额 */
    @ApiModelProperty(value = "商品结算（分佣）金额")
    private BigDecimal settleAmt;

    @ApiModelProperty(value = "是否可售后 (数据字典 sys_status_type)", hidden = true)
    private Integer isAfterSales = NumberPool.INT_ZERO;

    @ApiModelProperty(value = "可售后时间（存储类型为分钟）", hidden = true)
    private Long afterSalesTime;
}
