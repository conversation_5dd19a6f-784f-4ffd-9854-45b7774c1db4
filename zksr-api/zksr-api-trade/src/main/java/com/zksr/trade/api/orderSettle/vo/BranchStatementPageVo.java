package com.zksr.trade.api.orderSettle.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/12/17
 * @门店信息导出
 */
@Data
@ApiModel("门店信息 - 门店信息导出 req VO")
public class BranchStatementPageVo extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    private Long branchId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 城市id */
    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    private Long areaId;

    /** 业务员id */
    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    private Long colonelId;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 渠道id */
    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    private Long channelId;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id")
    private Long groupId;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "1正常 0停用")
    private Integer status;

    /** 审核状态 */
    @Excel(name = "审核状态")
    @ApiModelProperty(value = "1已审核 0未审核")
    private Integer auditState;

    @ApiModelProperty(value = "是否分页  1:分页, 0：不分页")
    private Integer isPage = 1;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @Excel(name = "价格码-数据字典", readConverterExp = "1=，2，3，4，5，6")
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;

    /** 最后一次登陆时间 */
    @Excel(name = "最后一次登陆时间")
    @ApiModelProperty(value = "最后一次登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;
    /** 搜索关键字 */
    @Excel(name = "搜索关键字")
    @ApiModelProperty(value = "搜索关键字")
    private String keywords;

    /** 城市列表id */
    @Excel(name = "城市列表id")
    @ApiModelProperty(value = "城市列表id")
    private List<Long> areaIds;

    @ApiModelProperty("门店编号")
    private String branchNo;

    @ApiModelProperty(value = "业务员id集合")
    private List<Long> colonelIds;

    /** 登录运营商id */
    @Excel(name = "登录运营商id")
    @ApiModelProperty(value = "登录运营商id")
    private Long loginDcId;

    /** 是否导出 */
    @Excel(name = "是否导出")
    @ApiModelProperty(value = "是否导出")
    private Integer isExport;

    @ApiModelProperty(value = "门店列表id")
    private List<Long> branchIds;

    @Excel(name = "注册开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private String registerStartTime;

    @Excel(name = "注册结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss" , timezone = "GMT+8")
    private String registerEndTime;

    @ApiModelProperty("注册类型查询 0 自主注册 1业务员拓店 2后台导出")
    private Integer registerType;

    @ApiModelProperty("客户状态")
    private String lifecycleStage;

}
