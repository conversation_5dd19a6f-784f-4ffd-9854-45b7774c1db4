package com.zksr.trade.api.driver.excel;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/13 14:45
 * 司机导入excel
 */
@Data
@ApiModel(description = "用于司机导入")
public class TrdDriverImportExcel {

    /**
     * 司机名
     */
    @Excel(name = "司机名")
    private String driverName;

    /**
     * 司机手机号
     */
    @Excel(name = "司机手机号")
    private String driverPhone;
}
