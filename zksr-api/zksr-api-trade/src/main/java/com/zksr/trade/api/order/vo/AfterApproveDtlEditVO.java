package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月23日 09:18
 * @description: AfterApproveDtlEditVO
 */
@ApiModel("售后单 - 审核售后单明细 Request VO")
@Data
public class AfterApproveDtlEditVO  extends AfterDtlReqVO{

    @ApiModelProperty(value = "售后收货手机号")
    private String returnPhone;

    @ApiModelProperty(value = "售后收货地址")
    private String returnAddr;

    /**
     *  {@link com.zksr.common.core.enums.AfterTypeEnum}
     */
    @ApiModelProperty(value = "售后类型: 字典值")
    private Long afterType;

    @ApiModelProperty(value = "售后订单明细数据")
    List<AfterDtl> afterDtlList;

//    @Data
//    public static class AfterDtl {
//        @ApiModelProperty(value = "售后单明细ID")
//        private Long supplierAfterDtlId;
//
//        @ApiModelProperty(value = "售后单明细编号")
//        private  String supplierAfterDtlNo;
//
//
//        @ApiModelProperty(value = "申请退货数量")
//        private Long returnQty;
//
//        @ApiModelProperty(value = "申请退货金额")
//        private BigDecimal returnAmt;
//
//        @ApiModelProperty(value = "实际退货数量")
//        private Long realityReturnQty;
//
//        @ApiModelProperty(value = "实际退货金额")
//        private BigDecimal realityReturnAmt;
//    }


}
