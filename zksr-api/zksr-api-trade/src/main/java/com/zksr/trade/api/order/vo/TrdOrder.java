package com.zksr.trade.api.order.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单对象 trd_order
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@TableName(value = "trd_order")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdOrder extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 订单id */
    @TableId(type= IdType.ASSIGN_ID)
    private Long orderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 运营商id */
    @Excel(name = "运营商id")
    private Long dcId;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long memberId;

    /** 业务员id */
    @Excel(name = "业务员id")
    private Long colonelId;

    /** 业务员级别 */
    @Excel(name = "业务员级别")
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    private Long pcolonelId;

    /** 父业务员级别 */
    @Excel(name = "父业务员级别")
    private Long pcolonelLevel;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String branchAddr;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 3-货到付款未收款 4-货到付款已收款*/
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    private Integer payState;

    /** 支付平台(数据字典);从sys_partner表字段jy_platform获取 */
    @Excel(name = "支付平台(数据字典);从sys_partner表字段jy_platform获取")
    private String platform;

    /** 支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款")
    private String payWay;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal discountAmt;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payAmt;

    /** 单据金额 */
    @Excel(name = "单据金额")
    private BigDecimal orderAmt;

    /** 支付公司收取的支付费率 */
    @Excel(name = "支付公司收取的支付费率")
    private BigDecimal payRate;

    /** 支付公司手续费金额 */
    @Excel(name = "支付公司手续费金额")
    private BigDecimal payFee;

    /** 订单类型：0 全国订单 1：本地订单 */
    @Excel(name = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    /** 下单用户是否本身是业务员(0-否（默认）  1-是) */
    @Excel(name = "下单用户是否本身是业务员(0-否（默认）  1-是)")
    private Integer colonelFlag;

    /** 订单来源(mini-小程序(默认) ywyApp-业务员APP) */
    @Excel(name = "订单来源(mini-小程序(默认) ywyApp-业务员APP)")
    private String source;

    /**
     * 已退款支付金额（售后总金额）
     */
    @Excel(name = "已退款支付金额(售后总金额)")
    private BigDecimal refundPayAmt;

    /**
     * 支付公司已退款手续费金额
     */
    @Excel(name = "支付公司已退款手续费金额")
    private BigDecimal refundPayFee;

    /**
     * 储值本金比率
     */
    @Excel(name = "储值本金比率")
    private BigDecimal czPrincipalRate = BigDecimal.ZERO;

    /**
     * 储值本金支付金额
     */
    @Excel(name = "储值本金支付金额")
    private BigDecimal czPrincipalPayAmt = BigDecimal.ZERO;

    /**
     * 储值赠金支付金额
     */
    @Excel(name = "储值赠金支付金额")
    private BigDecimal czGivePayAmt = BigDecimal.ZERO;


    /**
     * 分销模式
     */
    @ApiModelProperty(value = "分销模式")
    private String distributionMode;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    private String deliveryAddress;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 用户地址ID
     */
    @ApiModelProperty(value = "用户地址ID")
    private String memberAddressId;

    /** 用户发票id */
    @Excel(name = "用户发票id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberInvoiceId;

    /**
     * 是否余额支付 0：否，1：是
     */
    @Excel(name = "是否余额支付 0：否，1：是")
    private Integer isPayBalance;

    /**
     * 余额支付金额
     */
    @Excel(name = "余额支付金额")
    private BigDecimal payBalanceAmt = BigDecimal.ZERO;

    /**
     * 总支付金额
     */
    @Excel(name = "总支付金额")
    private BigDecimal totalPayAmt = BigDecimal.ZERO;
}
