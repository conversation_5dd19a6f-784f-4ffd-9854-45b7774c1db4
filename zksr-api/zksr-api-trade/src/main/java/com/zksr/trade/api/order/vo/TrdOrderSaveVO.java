package com.zksr.trade.api.order.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("订单信息 - 保存订单Request VO")
@Accessors(chain = true)
public class TrdOrderSaveVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "运营商ID")
    private Long dcId;

    @ApiModelProperty(value = "城市ID")
    private Long areaId;

    @ApiModelProperty(value = "用户ID")
    private Long memberId;

    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    @ApiModelProperty(value = "业务员级别")
    private Long colonelLevel;

    @ApiModelProperty(value = "上级业务员ID")
    private Long pcolonelId;

    @ApiModelProperty(value = "上级业务员级别")
    private Long pcolonelLevel;

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    @ApiModelProperty(value = "支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消")
    private Integer payState;

    @ApiModelProperty(value = "支付平台(数据字典);从sys_partner表字段jy_platform获取")
    private String platform;

    @ApiModelProperty(value = "支付方式(数据字典sys_pay_way));0-在线支付 1-储值支付 2-货到付款")
    private String payWay;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmt;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmt;

    @ApiModelProperty(value = "单据金额")
    private BigDecimal orderAmt;

    @ApiModelProperty(value = "支付公司收取的支付费率")
    private BigDecimal payRate;

    @ApiModelProperty(value = "支付公司手续费金额")
    private BigDecimal payFee;

    @ApiModelProperty(value = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    @ApiModelProperty(value = "平台商编号")
    private Long sysCode;

    @ApiModelProperty(value = "下单用户是否本身是业务员(0-否（默认）  1-是)")
    private Integer colonelFlag;

    @ApiModelProperty(value = "订单来源(mini-小程序(默认) ywyApp-业务员APP)")
    private String source;

}
