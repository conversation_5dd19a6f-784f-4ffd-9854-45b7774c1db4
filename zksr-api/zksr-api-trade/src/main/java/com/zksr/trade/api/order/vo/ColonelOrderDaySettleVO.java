package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取业务员结算信息 接口请求VO")
@Accessors(chain = true)
public class ColonelOrderDaySettleVO {

    @ApiModelProperty("平台商id")
    private Long sysCode;

    @ApiModelProperty("结算创建日期")
    private List<Long> colonelIds;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

}
