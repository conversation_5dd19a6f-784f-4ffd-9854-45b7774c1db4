package com.zksr.trade.api.order.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/26 9:47
 * @注释
 */
@ApiModel("业务员App-订单列表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TrdColonelAppOrderListPageReqVO extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 业务员id */
    @Excel(name = "业务员id")
    @TableField(fill = FieldFill.UPDATE)
    private Long colonelId;

    /** 开始时间 */
    @Excel(name = "开始时间")
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private String startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD)
    private String endTime;

    /** 订单类型 1：销售  2：退货 */
    @Excel(name = "订单类型 1：销售  2：退货")
    @ApiModelProperty(value = "订单类型 1：销售  2：退货")
    private Integer orderType;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 业务员Id集合 */
    @Excel(name = "业务员Id集合")
    @ApiModelProperty(value = "业务员Id集合")
    private List<Long> colonelIds;

    /** 门店Id集合 */
    @Excel(name = "门店Id集合")
    @ApiModelProperty(value = "门店Id集合")
    private List<Long> branchIds;

    /** 门店Id */
    @Excel(name = "门店Id")
    @ApiModelProperty(value = "门店Id")
    private Long branchId;

    /** 订单商品类型 0：全国  1：本地 */
    @ApiModelProperty(value = "订单商品类型 0：全国  1：本地")
    private Integer orderProductType;


    @ApiModelProperty(value = "订单使用，订单状态(100:全部/99:异常订单/0:待付款/3:代发货/4:待收货/5:已收货/6:已完成/2:已关闭)")
    private Integer orderStatus;

    @ApiModelProperty(value = "入驻商订单ID，查看详情时需要")
    private Long supplierOrderId;

    @ApiModelProperty(value = "订单ID，查看详情时需要")
    private Long orderId;


}
