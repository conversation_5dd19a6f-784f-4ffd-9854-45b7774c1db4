package com.zksr.trade.api.orderSettle.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2024年04月15日 15:58
 * @description: OrderSettlePageVO
 */
@Data
@ApiModel("订单结算信息 - 请求实体")
public class OrderSettlePageVO extends PageParam {
    @ApiModelProperty(value = "订单ID或售后订单ID")
    private Long orderId;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "商户ID")
    private Long merchantId;

    @ApiModelProperty(value = "结算状态 （数据字典） 0：未结算  1：已结算")
    private Integer state;

    @ApiModelProperty(value = "账户类型 (平台商：partner ， 运营商：dc ， 业务员：colonel)")
    private String merchantType;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "订单编号或售后订单编号")
    private String orderNo;

    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "门店ID（客户）")
    private Long branchId;

    @ApiModelProperty(value = "结算ID集合")
    private List<Long> settleIdList;
    @ApiModelProperty(value = "实际结算日期开始时间")
    @JsonFormat(pattern = YYYY_MM_DD)
    private Date actualSettleStartTime;

    @ApiModelProperty(value = "实际结算日期结束时间")
    @JsonFormat(pattern = YYYY_MM_DD)
    private Date actualSettleEndTime;

    @ApiModelProperty(value = "系统结算日期开始时间")
    @JsonFormat(pattern = YYYY_MM_DD)
    private Date systemSettleStartTime;

    @ApiModelProperty(value = "系统结算日期结束时间")
    @JsonFormat(pattern = YYYY_MM_DD)
    private Date systemSettleEndTime;

}
