package com.zksr.trade.api.orderSettle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月15日 16:02
 * @description: OrderSettleResDTO
 */
@Data
@ApiModel("订单结算信息 - 返回实体")
public class OrderSettleResDTO extends BaseEntity {

    @Excel(name = "订单创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.EXPORT)
    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Excel(name = "分润金额(商品总利润)", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty(value = "分润金额(商品总利润)")
    private BigDecimal profit;

    @ApiModelProperty(value = "订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;

    @Excel(name = "订单号", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "售后订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long afterId;

    @Excel(name = "售后订单号", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "售后订单号")
    private String afterNo;

    @ApiModelProperty(value = "商品skuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty(value = "商品spuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    @Excel(name = "商品金额", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty(value = "商品金额")
    private BigDecimal itemAmt;

    @Excel(name = "结算金额", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty(value = "结算金额")
    private BigDecimal settleAmt;

    /**
     * 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * */
    @ApiModelProperty(value = "商户类型（数据字典）")
    private String merchantType;

    @ApiModelProperty(value = "商户Id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long merchantId;

    @Excel(name = "商户分润比例", cellType = Excel.ColumnType.NUMERIC)
    @ApiModelProperty(value = "商户分润比例")
    private BigDecimal merchantRate;

    @Excel(name = "结算状态", cellType = Excel.ColumnType.STRING, readConverterExp = "0=未结算,1=已结算,2=结算中,3=待处理")
    @ApiModelProperty(value = "结算状态 （数据字典） 0：未结算  1：已结算")
    private Integer state;

    @Excel(name = "商品编号", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "商品编号")
    private String itemNo;

    @Excel(name = "商品名称", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "商品名称")
    private String itemName;

    @ApiModelProperty(value = "入驻商Id")
    private Long supplierId;

    @Excel(name = "入驻商名称", cellType = Excel.ColumnType.STRING)
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "结算ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long settleId;
}
