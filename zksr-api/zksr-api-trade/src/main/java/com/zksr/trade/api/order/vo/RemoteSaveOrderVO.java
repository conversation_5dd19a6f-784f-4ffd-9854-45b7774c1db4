package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年03月28日 19:12
 * @description: RemoteSaveOrderVO
 */
@Data
@ApiModel("订单信息 - 保存订单Request VO")
public class RemoteSaveOrderVO {
    /**
     * 主订单
     */
    TrdOrderSaveVO orderSaveVo;
    /**
     * 入驻商订单集合
     */
    List<TrdSupplierOrderSaveVO> supplierOrderSaveVOs;
    /**
     * 入驻商订单明细集合
     */
    List<TrdSupplierOrderDtlSaveVO> supplierOrderDtlSaveVOS;
    /**
     * 入驻商订单明细结算信息集合
     */
    List<TrdSupplierOrderSettleSaveVO> supplierOrderSettleSaveVOS;
    /**
     * 订单明细操作日志信息集合
     */
    List<TrdOrderLogSaveVO> orderLogSaveVOS;
    /**
     *  订单优惠信息
     */
    List<TrdOrderDiscountDtlSaveVO> orderDiscountDtlSaveVOS;

}
