package com.zksr.trade.api.status.dto;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 物流状态（ERP->B2B）对象 trd_express_status
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Data
@ApiModel("物流状态（ERP->B2B） - trd_express_status分页 Request VO")
public class TrdExpressStatusDTO {
    private static final long serialVersionUID = 1L;

    /** 单据类型BC */
    @Excel(name = "单据类型BC")
    @ApiModelProperty(value = "单据类型BC")
    private String transNo;

    /** 系统单号（物流单号） */
    @Excel(name = "系统单号", readConverterExp = "物=流单号")
    @ApiModelProperty(value = "系统单号")
    private String orderKey;

    /** 物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货）  */
    @Excel(name = "物流状态", readConverterExp = "物流状态（1待出库、2待配送、3配送中、4已配送（暂弃用）、5已收货） ")
    @ApiModelProperty(value = "物流状态")
    private String logisticsStatus;

    /** 顺序标识字段 */
    @Excel(name = "顺序标识字段")
    @ApiModelProperty(value = "顺序标识字段")
    private Long sort;

}
