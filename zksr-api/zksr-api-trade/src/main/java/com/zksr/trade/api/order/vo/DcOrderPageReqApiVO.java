package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.*;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DcOrderPageReqApiVO extends PageParam {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "子订单号")
    private String subOrderNo;

    @ApiModelProperty(value = "产品")
    private String product;

    @ApiModelProperty(value = "入驻商")
    private String supplier;

    @ApiModelProperty(value = "订单开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date startTime;

    @ApiModelProperty(value = "订单结束时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date endTime;

    @ApiModelProperty(value = "支付开始时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date payStartTime;

    @ApiModelProperty(value = "支付结束时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @DateTimeFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date payEndTime;

    @ApiModelProperty(value = "1,订单日期 2,支付日期")
    private Integer dateType;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "区域ID")
    private List<Long> areaIds;

    @ApiModelProperty(value = "业务员ID")
    private List<Long> colonelIds;

    @ApiModelProperty(value = "订单类型(100:全部/99:异常订单/0:待付款/3:代发货/4:待收货/5:已收货/6:已完成/2:已关闭)")
    private Integer orderType;

    @ApiModelProperty(value = "入驻商ID集合")
    private List<Long> supplierIdList;

    @ApiModelProperty(value = "是否根据入驻商查询 0,否 1,是")
    private Integer isSupplier;

    @ApiModelProperty(value = "产品集合集合")
    private List<DcOrderProductReqApiVO> productList;

    @ApiModelProperty(value = "门店Id集合")
    private List<Long> branchIds;

    @ApiModelProperty(value = "商品类型 0：全国商品 1：本地商品")
    private Integer itemType;

    @ApiModelProperty(value = "子订单ID,用于查询订单明细", hidden = true)
    private Long subOrderId;

    @ApiModelProperty(value = "入驻商订单外部订单号")
    private String sourceOrderNo;

    @ApiModelProperty(value = "订单列表")
    private List<String> orderNoList;

    @ApiModelProperty(value = "订单发货超时时间(天)")
    private String orderDeliveryOvertimeDay;

    private Long dcId;

    @ApiModelProperty("入驻商id")
    private Long supplierId;

    @ApiModelProperty("是否查询欠款订单 1 查询欠款订单，其余正常查询")
    private Integer isDebtOrder;

    /**
     * 入驻商订单状态
     */
    @ApiModelProperty("入驻商订单状态")
    private List<Integer> deliveryStates;

    private Integer payState;

    private Integer payWay;

}
