package com.zksr.trade.api.order.dto;

import com.zksr.common.core.domain.vo.openapi.ReceiptPayOpenDTO;
import com.zksr.common.core.enums.PayWayEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("订单（收款/售后）数据 -- 用于返回第三方系统（收款/退款）单据")
public class OrderReceiptRespDTO {

    @ApiModelProperty("入驻商订单号")
    private String supplierSheetNo;

    @ApiModelProperty("（收款/退款）金额")
    private BigDecimal receiptAmt;

    @ApiModelProperty("（收款/退款）单据精确金额（6位小数）")
    private BigDecimal receiptExactAmt;

    @ApiModelProperty("订单类型（销售：XS，售后：SH）")
    private String sheetType;

    @ApiModelProperty("单据类型（销售：XSS 售后：（正常售后单：SHS，拒收售后单：SHJ， 差异出库售后单：SHC））")
    private String supplierSheetType;

    /**
     * {@link   com.zksr.common.core.enums.OrderPayWayEnum}
     */
    @ApiModelProperty("支付方式 0：在线支付，2：货到付款")
    private String sheetPayWay;

    @ApiModelProperty("订单支付信息")
    private List<ReceiptPayOpenDTO> payList;

}
