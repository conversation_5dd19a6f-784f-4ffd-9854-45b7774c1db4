package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 *  客户使用优惠劵信息汇总
 */
@Data
@ApiModel("优惠劵模板 -- 客户使用优惠劵信息汇总")
public class CouponCustomerOrderUseTotalDTO {
    @ApiModelProperty(value = "优惠劵模板ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long couponTemplateId;

    @ApiModelProperty(value = "客户ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "该优惠劵模板 参与优惠商品总数量（订单下单的规格数量）")
    private Long orderNumSum;

    @ApiModelProperty(value = "该优惠劵模板 参与优惠商品总支付（订单下单的规格数量金额）")
    private BigDecimal orderAmtSum;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单下单时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date orderCreateTime;

    @ApiModelProperty(value = "领取优惠券时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date couponCreateTime;

}
