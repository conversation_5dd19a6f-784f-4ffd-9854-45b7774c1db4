package com.zksr.trade.api.car.vo;

import com.zksr.common.core.enums.ProductType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新增购物车商品
 * @date 2024/3/26 16:51
 */
@Data
@ApiModel(description = "选中购物车商品VO")
public class CarSelectedReqVO {

    @ApiModelProperty(value = "购物车ID",required = true)
    private List<String> carId;

    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;

    @ApiModelProperty(value = "商品类型 local-本地商品, global-全国商品", required = true, example = "local")
    private String productType = ProductType.LOCAL.getType();

    @ApiModelProperty(value = "操作类型, 0-选中, 1-取消选中, 2-全选, 3-全不选", required = true, example = "0")
    private Integer opType;

    public CarSelectedReqVO() {

    }

    public CarSelectedReqVO(List<String> carId, Long branchId, String productType, Integer opType) {
        this.carId = carId;
        this.branchId = branchId;
        this.opType = opType;
        this.productType = productType;
    }
}
