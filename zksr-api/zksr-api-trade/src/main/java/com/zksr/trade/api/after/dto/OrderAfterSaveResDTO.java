package com.zksr.trade.api.after.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月21日 17:28
 * @description: OrderAfterSaveResDTO
 */
@Data
@ApiModel("售后订单实体 响应")
public class OrderAfterSaveResDTO {

    @ApiModelProperty(value = "售后单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long afterId;

    @ApiModelProperty(value = "售后单编号")
    private String afterNo;



}
