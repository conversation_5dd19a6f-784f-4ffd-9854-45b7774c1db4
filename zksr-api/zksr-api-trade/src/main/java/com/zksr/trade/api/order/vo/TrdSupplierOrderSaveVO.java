package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("订单信息 - 入驻商订单信息Request VO")
public class TrdSupplierOrderSaveVO {
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "入驻商订单金额")
    private BigDecimal subAmt;

    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal subDiscountAmt;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal subPayAmt;

    @ApiModelProperty(value = "订单金额 未减去优惠的订单金额")
    private BigDecimal subOrderAmt;

    @ApiModelProperty(value = "订单商品总数量")
    private BigDecimal subOrderNum;

    @ApiModelProperty(value = "支付公司收取的支付费率")
    private BigDecimal payRate;

    @ApiModelProperty(value = "支付公司手续费金额")
    private BigDecimal subPayFee;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 */
    @ApiModelProperty(name = "支付状态")
    private Integer payState;

    @ApiModelProperty(name = "是否负库存下单 0-否 1-是")
    private Integer stockShortFlag;

    /**
     * 用户发票id
     */
    @ApiModelProperty(name = "用户发票id")
    private Long memberInvoiceId;

    @ApiModelProperty(value = "余额支付金额")
    private BigDecimal subPayBalanceAmt;

    @ApiModelProperty(value = "总支付金额")
    private BigDecimal totalSubPayAmt;
}
