package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年011月30日 09:13
 * @description: TrdOrderDiscountDTO
 */
@ApiModel("订单优惠劵信息返回对象")
@Data
@Accessors(chain = true)
public class TrdOrderDiscountDTO {

    /** 优惠券模板名称 */
    private String couponName;

    /** 优惠类型(数据字典);0-满减券  1-折扣券 */
    private Integer discountType;

    /** 优惠折扣;折，如99折记9.9折(折扣券设置) */
    private BigDecimal discountPercent;

    /** 优惠券面额;满多少元可以减多少元(满减券设置) */
    private BigDecimal discountAmt;

    /** 优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用 */
    private BigDecimal triggerAmt;

    /** 模板开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date templateStartDate;

    /** 模板结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date templateEndDate;

    /** 赠品数量 */
    private Long discountGiftCount;

}
