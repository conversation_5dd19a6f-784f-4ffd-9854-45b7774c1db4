package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券拓展表统计数据
 * @date 2024/5/27 16:55
 */
@Data
@ApiModel(description = "优惠券拓展表统计数据")
public class CouponExtendTotalVO {

    @ApiModelProperty(value = "优惠券模版ID")
    private Long couponTemplateId;

    @ApiModelProperty(value = "使用优惠券的订单的支付金额")
    private BigDecimal totalSaleAmt;

    @ApiModelProperty(value = "优惠券优惠金额")
    private BigDecimal totalCouponAmt;
}
