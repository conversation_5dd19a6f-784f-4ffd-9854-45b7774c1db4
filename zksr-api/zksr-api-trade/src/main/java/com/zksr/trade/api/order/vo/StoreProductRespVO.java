package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("常购商品返回实体类")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StoreProductRespVO {

    @ApiModelProperty("唯一ID 规则: CONCAT(branch_id,'_',sku_id,'_',itemType)")
    private String id;

    @ApiModelProperty("平台商编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long sysCode;

    @ApiModelProperty("门店ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long branchId;

    @ApiModelProperty("skuId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long skuId;

    @ApiModelProperty("spuId")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long spuId;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("最近购买数量")
    private Long recentlyPurchasedNumber;

    @ApiModelProperty("最近90天购买次数")
    private Long withinPurchasedFrequencyTotal;

    @ApiModelProperty("最近90天购买数量")
    private Long withinPurchasedNumberTotal;

    @ApiModelProperty("最近90天平均购买的规格数量")
    private BigDecimal withinPurchasedNumberAvg;

    @ApiModelProperty("最近90天购买频次")
    private BigDecimal withinPurchasedFrequencyAvg;

    @ApiModelProperty("首次下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date firstPurchasedDate;

    @ApiModelProperty("最近下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date recentlyPurchasedDate;

    @ApiModelProperty("历史累计下单次数")
    private Long purchasedFrequencyTotal;

    @ApiModelProperty("历史累计购买数量")
    private Long purchasedNumberTotal;

    @ApiModelProperty("管理类别编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long catgoryId;

    @ApiModelProperty("平台管理分类一级类别编号")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long catgoryFirstId;

    @ApiModelProperty("商品品牌")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long brandId;

    @ApiModelProperty("点击次数")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long clickNumberTotal;

    @ApiModelProperty("商品类型 0：全国商品 1：本地商品")
    private Integer itemType;

    @ApiModelProperty("入驻商ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    @ApiModelProperty("城市上架商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long areaItemId;

    @ApiModelProperty("入驻商上架商品id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierItemId;

    @ApiModelProperty("图片")
    private String thumb;

    @ApiModelProperty("标准价")
    private BigDecimal markPrice;

    @ApiModelProperty("库存")
    private Long surplusSaleQty;

    /**
     * 单位
     */
    @ApiModelProperty("单位 sys_prdt_unit")
    private String unit;

    @ApiModelProperty("属性")
    private String properties;

    @ApiModelProperty("起订")
    private Long minOq;

    @ApiModelProperty("订货组数")
    private Long jumpOq;

    @ApiModelProperty("限购")
    private Long maxOq;

    /**
     * 单位, 1-小单位, 2-中单位, 3-大单位
     * 参见 {@link com.zksr.common.core.enums.UnitTypeEnum}
     */
    private Integer unitSize;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;
}
