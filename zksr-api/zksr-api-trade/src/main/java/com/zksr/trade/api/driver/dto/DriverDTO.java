package com.zksr.trade.api.driver.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/13 16:30
 * @注释
 */
@Data
@ApiModel("司机信息")
public class DriverDTO {

    /**
     * 司机id
     */
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty(value = "司机手机号")
    private Long driverId;


    /**
     * 司机名
     */
    @ApiModelProperty(value = "司机名")
    private String driverName;

    /**
     * 司机手机号
     */
    @ApiModelProperty(value = "司机手机号")
    private String driverPhone;
}
