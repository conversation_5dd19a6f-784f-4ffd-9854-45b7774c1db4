package com.zksr.trade.api.after.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@ApiModel("售后管理")
public class AfterOrderExportVO extends BaseEntity {


    @ApiModelProperty(value = "售后单ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private  Long afterId;

    @Excel(name = "退货单号")
    @ApiModelProperty(value = "退货单号")
    private  String afterNo;

    @Excel(name = "子退货订单号")
    @ApiModelProperty(value = "子退货订单号")
    private  String supplierAfterNo;

    @Excel(name = "外部售后单号")
    @ApiModelProperty(value = "外部售后单号")
    private String sourceOrderNo;

    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private  String orderNo;

    @Excel(name = "申请售后时间")
    @ApiModelProperty(value = "申请售后时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date afterCreateTime;

    @Excel(name = "状态", readConverterExp = "1=待处理,2=未退货,3=退货中,4=已退货,5=待退款,6=退款中,7=退款失败,8=已处理,9=已拒绝,10=已取消,11=已撤销")
    @ApiModelProperty(value = "处理状态处理中状态{0：(处理中状态全部)，1：待处理， 2：未退货，3：退货中，4：已退货，5：待退款，6：退款中，7：退款失败, 8:已处理（完成），9：已拒绝}")
    private  Long handleState;

    @Excel(name = "支付时间")
    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private  Date orderCreateTime;

    @Excel(name = "付款方式", readConverterExp = "0=在线支付,1=储值支付,2=货到付款")
    @ApiModelProperty(value = "付款方式（数据字典KEY：sys_pay_way）")
    private Long payWay;

    @Excel(name = "业务员")
    @ApiModelProperty(value = "业务员")
    private String colonelName;

    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @Excel(name = "门店电话")
    @ApiModelProperty(value = "门店电话")
    private String branchPhone;

    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddress;

    @Excel(name = "单据类型", readConverterExp = "0=全国商品,1=本地商品")
    @ApiModelProperty(value = "单据类型")
    private  Long productType;

    @Excel(name = "申请退货数量")
    @ApiModelProperty(value = "申请退货数量")
    private  Long returnQty;

    @Excel(name = "申请退款金额")
    @ApiModelProperty(value = "申请退款金额")
    private  BigDecimal returnAmt;

    @Excel(name = "售后发起人")
    @ApiModelProperty(value = "售后发起人")
    private String createBy;

    @Excel(name = "售后类型", readConverterExp = "1=仅退款,2=退货退款,3=换货,1=补发")
    @ApiModelProperty(value = "售后类型")
    private  Long afterType;


    @Excel(name = "售后阶段", readConverterExp = "1=发货前退款,2=发货后退款")
    @ApiModelProperty(value = "售后阶段")
    private  Long afterPhase;

    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=同意,2=拒绝")
    @ApiModelProperty(value = "审核状态")
    private  Long approveState;

    @Excel(name = "退货状态", readConverterExp = "0=未退货,1=退货中,2=退货完成,3=无需退货")
    @ApiModelProperty(value = "退货状态")
    private  Long returnState;

    @Excel(name = "退款状态", readConverterExp = "0=未退款,1=退款中,2=退款完成,3=退款失败")
    @ApiModelProperty(value = "退款状态")
    private  Long refundState;

    @Excel(name = "订单支付时间")
    @ApiModelProperty(value = "订单支付时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date orderPayTime;

    @ApiModelProperty(value = "门店Id")
    private Long branchId;

    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;



}
