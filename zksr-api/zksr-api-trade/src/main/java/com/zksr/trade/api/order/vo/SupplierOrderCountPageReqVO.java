package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
 * 统计订单数量入参
 *
 * <AUTHOR>
 *
 */
@ApiModel("入驻商信息 - sys_supplier分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SupplierOrderCountPageReqVO extends PageParam{
    private static final long serialVersionUID = 1L;

    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /** 入驻商名称 */
    @ApiModelProperty(value = "入驻商名称")
    @JsonSerialize(using = CustomLongSerialize.class)
    private String supplierName;

    /** 入驻商编号 */
    @ApiModelProperty(value = "入驻商编号")
    private String supplierCode;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 平台商名称 */
    @Excel(name = "平台商名称")
    @ApiModelProperty(value = "平台商名称")
    @JsonSerialize(using = CustomLongSerialize.class)
    private String sysName;

    @ApiModelProperty(value = "开始时间")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate endDate;

    /** saas租户编码 */
    @ApiModelProperty(value = "saas租户编码")
    private String saasTenantCode;

}
