package com.zksr.trade.api.driver.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/12/13 16:47
 * @注释
 */
@Data
@ApiModel("司机评价信息")
public class DriverRatingDTO {

    /**
     * 司机评分
     */
    @ApiModelProperty(value = "反馈图片;例：")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long driverRatingId;

    /**
     * 平台商id
     */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /**
     * 门店ID
     */
    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /**
     * 司机名称
     */
    @ApiModelProperty(value = "司机名称")
    private String driverName;

    /**
     * 司机电话
     */
    @ApiModelProperty(value = "司机电话")
    private String driverPhone;

    /**
     * 综合评分
     */
    @ApiModelProperty(value = "综合评分")
    private Integer totalScore;

    /**
     * 入驻商订单id
     */
    @ApiModelProperty(value = "入驻商订单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderId;

    /**
     * 评价维度1字典code;例：0
     */
    @ApiModelProperty(value = "评价维度1字典code;例：0")
    private String slot1Code;

    /**
     * 评价维度1字典值;例：司机态度
     */
    @ApiModelProperty(value = "评价维度1字典值;例：司机态度")
    private String slot1Val;

    /**
     * 评价维度1-得分字典;例：4
     */
    @ApiModelProperty(value = "评价维度1-得分字典;例：4")
    private Integer slot1ScoreCode;

    /**
     * 评价维度1-得分字典值;例：满意
     */
    @ApiModelProperty(value = "评价维度1-得分字典值;例：满意")
    private String slot1ScoreCodeVal;

    /**
     * 评价维度2字典code;例：1
     */
    @ApiModelProperty(value = "评价维度2字典code;例：1")
    private String slot2Code;

    /**
     * 评价维度2字典值;例：配送时效
     */
    @ApiModelProperty(value = "评价维度2字典值;例：配送时效")
    private String slot2Val;

    /**
     * 评价维度2-得分字典;例：5
     */
    @ApiModelProperty(value = "评价维度2-得分字典;例：5")
    private Integer slot2Score;

    /**
     * 评价维度2-得分字典值;例：非常满意
     */
    @ApiModelProperty(value = "评价维度2-得分字典值;例：非常满意")
    private String slot2ScoreCodeVal;

    /**
     * 评价维度3字典code;例：2
     */
    @ApiModelProperty(value = "评价维度3字典code;例：2")
    private String slot3Code;

    /**
     * 评价维度3字典值;例：商品完好
     */
    @ApiModelProperty(value = "评价维度3字典值;例：商品完好")
    private String slot3Val;

    /**
     * 评价维度3-得分;例：3
     */
    @ApiModelProperty(value = "评价维度3-得分;例：3")
    private Integer slot3Score;

    /**
     * 评价维度1-得分字典值;例：一般满意
     */
    @ApiModelProperty(value = "评价维度1-得分字典值;例：一般满意")
    private String scoreCodeVal;

    /**
     * 原因code;例：0,1
     */
    @ApiModelProperty(value = "原因code;例：0,1")
    private String reasonCode;

    /**
     * 低分原因code字典值;例：态度不好,送货不及时
     */
    @ApiModelProperty(value = "低分原因code字典值;例：态度不好,送货不及时")
    private String reasonVal;

    /**
     * 反馈信息;例：加油！努力！
     */
    @ApiModelProperty(value = "反馈信息;例：加油！努力！")
    private String fedbackMsg;

    /**
     * 反馈图片;例：
     */
    @ApiModelProperty(value = "反馈图片;例：")
    private String fedbackPics;


    @ApiModelProperty("司机评论时间")
    private String createTime;
}
