package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AfterDtl {
    @ApiModelProperty(value = "售后单明细ID")
    private Long supplierAfterDtlId;

    @ApiModelProperty(value = "售后单明细编号")
    private  String supplierAfterDtlNo;


    @ApiModelProperty(value = "申请退货数量（当前是最小单位数量）")
    private BigDecimal returnQty;

    @ApiModelProperty(value = "申请退货金额")
    private BigDecimal returnAmt;

    /**
     * SAASB-507 【成都佰润】散装称重商品需求
     */
    @ApiModelProperty(value = "实际退货数量（当前是最小单位数量）")
    private BigDecimal realityReturnQty;

    @ApiModelProperty(value = "实际退货金额")
    private BigDecimal realityReturnAmt;
}
