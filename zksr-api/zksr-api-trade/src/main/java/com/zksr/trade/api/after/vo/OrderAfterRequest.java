package com.zksr.trade.api.after.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.constant.SheetTypeConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月20日 10:07
 * @description: OrderAfterRequest
 */
@Data
@ApiModel("订单售后实体 请求")
public class OrderAfterRequest {
    @ApiModelProperty(value = "订单ID", required = true)
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "状态", required = true)
    private Long deliveryState;

    @ApiModelProperty(value = "入驻商订单集合")
    private List<SupplierOrder> supplierOrders;

    @ApiModel(value = "入驻商订单项")
    @Data
    public static class SupplierOrder {

        @ApiModelProperty(value = "入驻商id", required = true)
        private Long supplierId;

        @ApiModelProperty(value = "入驻商订单id", required = true)
        private Long supplierOrderId;

        @ApiModelProperty(value = "入驻商订单明细项数组")
        private List<SupplierOrderDtl> supplierOrderDtls;

        @ApiModelProperty(value = "外部来源单号, 第三方生成售后单时使用")
        private String sourceOrderNo;

        /** 订单类型 */
        @Excel(name = "订单类型 SheetTypeConstants")
        private String transNo = SheetTypeConstants.SHS;

        @ApiModel(value = "入驻商订单明细项")
        @Data
        public static class SupplierOrderDtl {

            @ApiModelProperty(value = "入驻商订单明细ID" , required = true)
            private Long supplierOrderDtlId;

            @ApiModelProperty(value = "商品可退数量" , required = true)
            private BigDecimal totalNum;

            @ApiModelProperty(value = "退货商品单价" , required = true)
            private BigDecimal refundPrice;

            @ApiModelProperty(value = "退货商品数量", required = true)
            private BigDecimal refundQty;

            @ApiModelProperty(value = "退货商品金额", required = true)
            private BigDecimal refundAmt;

            @ApiModelProperty(value = "退货单位大小 1：小单位，2：中单位，3：大单位", required = true)
            private Integer unitType;

            @ApiModelProperty(value = "售后阶段(数据字典);1发货前退款 2发货后退款")
            private Long afterPhase;
        }

    }
}
