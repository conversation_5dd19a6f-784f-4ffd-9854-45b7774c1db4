package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@Accessors(chain = true)
@ApiModel("入驻商订单商品明细导出实体")
public class SupplierOrderDtlInfoExportVO extends BaseEntity {

    @Excel(name = "入驻商订单编号", needMerge = true)
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    @Excel(name = "商品名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    @Excel(name = "外部订单号")
    @ApiModelProperty(value = "外部订单号")
    private String sourceOrderNo;

    @Excel(name = "异常状态")
    @ApiModelProperty(value = "异常状态")
    private String errorState = "正常";

    @Excel(name = "异常状态")
    @ApiModelProperty(value = "异常状态描述")
    private String errorMemo;

    @Excel(name = "单位")
    @ApiModelProperty(value = "单位")
    private String skuUnit;


    @Excel(name = "规格名称")
    @ApiModelProperty(value = "商品SPU规格名称")
    private String specName;

    @ApiModelProperty(value = "订单状态")
    @Excel(name = "订单状态")
    private String deliveryState;

    @Excel(name = "实际金额")
    @ApiModelProperty(value = "实际金额")
    private BigDecimal payTotalAmt;


    @Excel(name = "金额")
    @ApiModelProperty(value = "金额")
    private BigDecimal demandAmt;

    @Excel(name = "合计金额")
    @ApiModelProperty(value = "合计金额")
    private BigDecimal saleTotalAmt;

    @Excel(name = "优惠金额")
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmt;



    @Excel(name = "商品条码")
    @ApiModelProperty(value = "商品条码")
    private String barcode;

    @Excel(name = "单位")
    @ApiModelProperty(value = "下单单位")
    private String saleUnit;

    @Excel(name = "是否赠品", readConverterExp = "0=否,1=是")
    @ApiModelProperty(value = "是否赠品（1：是 ，0：否）")
    private Integer giftFlag;


    @Excel(name = "数量")
    @ApiModelProperty(value = "商品数量（下单单位数量）")
    private Long totalNum;

    @Excel(name = "单价")
    @ApiModelProperty(value = "商品销售价（优惠后平均单价）")
    private BigDecimal price;

    @Excel(name = "打印状态")
    @ApiModelProperty(value = "打印状态")
    private String printState;

    @Excel(name = "支付状态")
    @ApiModelProperty(value = "支付状态")
    private String payState;

    @Excel(name = "同步标识")
    @ApiModelProperty(value = "同步标识")
    private String pushStatus;



    @Excel(name = "总价")
    @ApiModelProperty(value = "商品销售总价（优惠后价格）")
    private BigDecimal totalAmt;



    @Excel(name = "订单编号", needMerge = true)
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @Excel(name = "订单日期",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单日期")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date createDate;

    @Excel(name = "订单年")
    @ApiModelProperty(value = "订单年")
    private String createTimeYear;

    @Excel(name = "订单月")
    @ApiModelProperty(value = "订单月")
    private String createTimeMonth;

    @Excel(name = "订单日")
    @ApiModelProperty(value = "订单日")
    private String createTimeDay;

    @ApiModelProperty(value = "订单商品状态")
    @Excel(name = "订单商品状态")
    private String delivery;

    @Excel(name = "订单所属的下单门店编号")
    @ApiModelProperty("订单所属的下单门店编号")
    private String branchNo;

    @Excel(name = "订单所属的下单门店名称")
    @ApiModelProperty(value = "订单所属的下单门店名称")
    private String branchName;

    @Excel(name = "订单所属的入驻商编号")
    @ApiModelProperty(value = "订单所属的入驻商编号")
    private String supplierCode;

    @Excel(name = "订单所属的入驻商名称")
    @ApiModelProperty(value = "订单所属的入驻商名称")
    private String supplierName;

    @Excel(name = "订单所属的运营商编号")
    @ApiModelProperty(value = "订单所属的运营商编号")
    private String dcCode;

    @Excel(name = "订单所属的运营商名称")
    @ApiModelProperty(value = "订单所属的运营商名称")
    private String dcName;

    @Excel(name = "订单所属的下单门店渠道")
    @ApiModelProperty(value = "订单所属的下单门店渠道")
    private String channelName;

    @Excel(name = "订单所属的销售经理")
    @ApiModelProperty(value = "订单所属的销售经理")
    private String pcolonelName;

    @ApiModelProperty("订单所属的业务员")
    @Excel(name = "订单所属的业务员")
    private String colonelName;

    @ApiModelProperty("订单所属的下单门店地址")
    @Excel(name = "订单所属的下单门店地址")
    private String branchAddr;

    @ApiModelProperty("订单所属的下单门店经度")
    @Excel(name = "订单所属的下单门店经度")
    private BigDecimal longitude;

    @ApiModelProperty("订单所属的下单门店纬度")
    @Excel(name = "订单所属的下单门店纬度")
    private BigDecimal latitude;

    @ApiModelProperty("订单的付款方式")
    @Excel(name = "订单的付款方式")
    private String payWay;

    @ApiModelProperty("订单的订单类型")
    @Excel(name = "订单的订单类型")
    private String orderType;

    @ApiModelProperty(value = "订单所属的一级区域")
    @Excel(name = "订单所属的一级区域")
    private String oneArea;

    @ApiModelProperty(value = "订单所属的二级区域")
    @Excel(name = "订单所属的二级区域")
    private String twoArea;

    @ApiModelProperty(value = "订单所属的三级区域")
    @Excel(name = "订单所属的三级区域")
    private String threeArea;



    @Excel(name = "订单所属的省份")
    @ApiModelProperty(value = "订单所属的省份")
    private String province;

    @Excel(name = "订单所属的城市")
    @ApiModelProperty(value = "订单所属的城市")
    private String city;

    @Excel(name = "订单所属的区县")
    @ApiModelProperty(value = "订单所属的区县")
    private String county;

    @ApiModelProperty("订单商品的商品编号")
    @Excel(name = "订单商品的商品编号")
    private String spuNo;

    @Excel(name = "订单商品的辅助商品编号")
    private String auxiliarySpuNo;



    @ApiModelProperty(value = "订单商品的品牌")
    @Excel(name = "订单商品的品牌")
    private String brandName;






    @ApiModelProperty("订单商品的要货数量")
    @Excel(name = "订单商品的要货数量")
    private Long demandNum;

    @ApiModelProperty(value = "订单商品的金额")
    @Excel(name = "订单商品的金额")
    private BigDecimal saleOrderAmt;


    @ApiModelProperty(value = "订单商品的佣金总扣点")
    @Excel(name = "订单商品的佣金总扣点")
    private BigDecimal orderAmtRate;


    @ApiModelProperty(value = "订单商品的平台管理分润占比")
    @Excel(name = "订单商品的平台管理分润占比")
    private BigDecimal partnerRate;

    @ApiModelProperty(value = "订单商品的运营商分润占比")
    @Excel(name = "订单商品的运营商分润占比")
    private BigDecimal dcRate;

    @ApiModelProperty(value = "订单商品的责任人分润占比")
    @Excel(name = "订单商品的责任人分润占比")
    private BigDecimal colonel1Rate;

    @ApiModelProperty(value = "订单商品的业务员分润占比")
    @Excel(name = "订单商品的业务员/销售经分润占比")
    private BigDecimal colonel2Rate;

    @ApiModelProperty(value = "订单商品的平台佣金")
    @Excel(name = "订单商品的平台佣金")
    private BigDecimal partnerAmt;

    @ApiModelProperty(value = "订单商品的运营商分润佣金")
    @Excel(name = "订单商品的运营商分润佣金")
    private BigDecimal dcAmt;

    @ApiModelProperty(value = "订单商品的销售经理分润佣金")
    @Excel(name = "订单商品的销售经理分润佣金")
    private BigDecimal colonel1Amt;

    @ApiModelProperty(value = "订单商品的业务员分润佣金")
    @Excel(name = "订单商品的业务员分润佣金")
    private BigDecimal colonel2Amt;

    @ApiModelProperty(value = "订单商品所属的平台商展示城市类别的一级类目")
    @Excel(name = "订单商品所属的平台商展示城市类别的一级类目")
    private String oneAreaClass;

    @ApiModelProperty(value = "订单商品所属的平台商展示城市类别的二级类目")
    @Excel(name = "订单商品所属的平台商展示城市类别的二级类目")
    private String twoAreaClass;

    @ApiModelProperty(value = "订单商品所属的平台商展示城市类别的三级类目")
    @Excel(name = "订单商品所属的平台商展示城市类别的三级类目")
    private String threeAreaClass;


    @ApiModelProperty(value = "订单商品所属的一级类目")
    @Excel(name = "订单商品所属的一级类目")
    private String oneCategory;

    @ApiModelProperty(value = "订单商品所属的二级类目")
    @Excel(name = "订单商品所属的二级类目")
    private String twoCategory;

    @ApiModelProperty(value = "订单商品所属的三级类目")
    @Excel(name = "订单商品所属的三级类目")
    private String threeCategory;



    @ApiModelProperty(value = "订单商品促销类型")
    @Excel(name = "订单商品促销类型")
    private String discountType;

    @ApiModelProperty(value = "订单商品使用的优惠劵")
    @Excel(name = "订单商品使用的优惠劵")
    private String discountRoll;

    @ApiModelProperty(value = "订单商品优惠劵领取方式")
    @Excel(name = "订单商品优惠劵领取方式")
    private String getDiscountRollWay;




    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    @Excel(name = "支付平台商户订单号")
    @ApiModelProperty(value = "支付平台商户订单号")
    private String outTradeNo;



    //==================== 不导出显示数据=============================
    @ApiModelProperty(value = "skuid")
    private Long skuId;
    @ApiModelProperty(value = "spuid")
    private Long spuId;
    @ApiModelProperty(value = "下单单位大小")
    private Integer orderUnitType;
    @ApiModelProperty(value = "下单单位")
    private Integer orderUnit;

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "运营商id")
    private Long dcId;

    @ApiModelProperty(value = "城市ID")
    private Long areaId;

    @ApiModelProperty("业务员id")
    private Long colonelId;

    private Long orderId;

    @ApiModelProperty("入驻商id")
    private Long supplierId;

    @ApiModelProperty("入驻商id")
    private Long pcolonelId;

    @ApiModelProperty("入驻商订单id")
    private Long supplierOrderId;

    @ApiModelProperty("城市上架商品id")
    private Long areaItemId;

    @ApiModelProperty("入驻商上架商品id")
    private Long supplierItemId;

    @ApiModelProperty("最后一次下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = TIMEZONE)
    private Date lasterOrderTime;
    @ApiModelProperty("订单支付时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date payTime;




}
