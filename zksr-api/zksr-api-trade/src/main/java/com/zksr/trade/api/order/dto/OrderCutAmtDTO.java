package com.zksr.trade.api.order.dto;

import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/3/18 11:01
 */
@Data
public class OrderCutAmtDTO {

    public static final String CUT_FORMAT = "yyyy-MM-dd HH:mm";

    public static final String CUT_TIME = "HH:mm";

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmt = BigDecimal.ZERO;

    @Data
    @ApiModel(description = "缓存key")
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CacheKey {

        @ApiModelProperty(value = "门店ID", required = true)
        private Long branchId;

        @ApiModelProperty(value = "商品类型", required = true)
        private String productType;

        @ApiModelProperty(value = "订单日期", required = true)
        private String date;

        @ApiModelProperty(value = "截单时间", required = true)
        private String cutTime;

        @ApiModelProperty(value = "入驻商ID", required = true)
        private Long supplierId;

        @Override
        public String toString() {
            return StringUtils.format("{}_{}_{}_{}_{}", branchId, productType, date, cutTime, supplierId);
        }

        public static CacheKey init(String toString) {
            String[] infos = toString.split(StringPool.UNDERSCORE);
            CacheKey cacheKey = new CacheKey();
            cacheKey.setBranchId(Long.valueOf(infos[0]));
            cacheKey.setProductType(infos[1]);
            cacheKey.setDate(infos[2]);
            cacheKey.setCutTime(infos[3]);
            cacheKey.setSupplierId(Long.valueOf(infos[4]));
            return cacheKey;
        }
    }
}
