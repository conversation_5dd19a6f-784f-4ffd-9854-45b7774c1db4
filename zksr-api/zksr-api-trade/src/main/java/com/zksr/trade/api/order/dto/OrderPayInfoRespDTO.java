package com.zksr.trade.api.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@ApiModel("订单支付信息返回Dto")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class OrderPayInfoRespDTO {
    @ApiModelProperty(value = "订单支付平台")
    private String platform;

    @ApiModelProperty(value = "订单Id")
    private Long orderId;

    @ApiModelProperty(value = "订单应支付金额总金额")
    private BigDecimal payAmt = BigDecimal.ZERO;

    @ApiModelProperty(value = "门店储值赠送余额承担金额", notes = "仅在储值支付时有效")
    private BigDecimal walletGiveAmt;

    @ApiModelProperty(value = "入驻商订单信息&分账商户信息")
    private List<TrdSupplierResDto> supplierList;
}
