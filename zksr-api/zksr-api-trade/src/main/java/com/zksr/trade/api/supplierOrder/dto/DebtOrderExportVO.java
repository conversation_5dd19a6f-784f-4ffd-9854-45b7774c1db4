package com.zksr.trade.api.supplierOrder.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("欠款订单导出vo对象")
public class DebtOrderExportVO extends PageParam {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @Excel(name = "订单编号")
    @ApiModelProperty("订单编号")
    private String orderNo;

    @Excel(name = "下单时间")
    @ApiModelProperty("订单创建时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date createTime;

    @ApiModelProperty("入驻商订单Id（子订单Id）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierOrderId;

    @Excel(name = "子订单号")
    @ApiModelProperty("入驻商订单号（子订单号）")
    private String supplierOrderNo;


    @Excel(name = "入驻商订单外部订单号")
    @ApiModelProperty(value = "入驻商订单外部订单号")
    private String sourceOrderNo;

    @ApiModelProperty("门店id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long branchId;

    @Excel(name = "门店名称")
    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("业务员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long colonelId;

    @Excel(name = "业务员名称")
    @ApiModelProperty("业务员名称")
    private String colonelName;

    @ApiModelProperty("入驻商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    @Excel(name = "入驻商名称")
    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty("支付状态 数据字典：sys_pay_status")
    private String payState;

    @Excel(name = "订单类型",readConverterExp = "0= 全国商品,1= 本地商品")
    @ApiModelProperty("订单类型 0：全国商品 1：本地商品")
    private Integer orderType;

    @Excel(name = "实际支付金额")
    @ApiModelProperty("实际支付金额")
    private BigDecimal payTotalAmt;


    @Excel(name = "订单明细")
    @ApiModelProperty("订单明细")
    private List<DebtSupplierOrderDtlRespVO> supplierOrderDtlRespVOList;
}

