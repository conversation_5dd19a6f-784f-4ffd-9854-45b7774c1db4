package com.zksr.trade.api.hdfk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店货到付款订单返回数据请求 - Resp VO
 * @date 2024/5/28 15:58
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "门店货到付款订单返回数据请求")
public class BranchHdfkOrderRespVO {
    @ApiModelProperty("订单编号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("入驻商订单编号")
    private String supplierOrderNo;

    @ApiModelProperty("入驻商订单ID")
    private Long supplierOrderId;

    @ApiModelProperty("入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty("入驻商头像")
    private String avatar;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("订单创建时间")
    private Date createTime;

    @ApiModelProperty("应付款金额")
    private BigDecimal payAmt;

    @ApiModelProperty("订单状态, 0-待配货,3-待发货,4-待收货,5-已收货,6-已完成,7-备货中,40-待装车")
    private Long deliveryState;

    @ApiModelProperty("商品信息")
    private List<BranchHdfkOrderItem> itemList;

    @Data
    @ApiModel(description = "门店待付款订单列表商品信息")
    public static class BranchHdfkOrderItem {

        @ApiModelProperty("入驻商商品详情ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long supplierOrderDtlId;

        @ApiModelProperty("spu名称")
        private String spuName;

        @ApiModelProperty("skuId")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long skuId;

        @ApiModelProperty("销售数量")
        private Integer totalNum;

        @ApiModelProperty("商品图片")
        private String thumb;
    }
}
