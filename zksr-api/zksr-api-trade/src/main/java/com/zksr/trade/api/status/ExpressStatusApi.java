package com.zksr.trade.api.status;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.vo.openapi.receive.OrderStateReturnVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import javax.validation.Valid;

/**
 *
 * <AUTHOR>
 * @date 2024/6/4 16:19
 */

@FeignClient(
        contextId = "remoteExpressStatusApi",
        value = ApiConstants.NAME
)
public interface ExpressStatusApi {

    String PREFIX = ApiConstants.PREFIX + "/expressStatus";

    @PostMapping(value =PREFIX + "/saveExpressStatus")
    CommonResult<Boolean> saveExpressStatus(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                            @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                            @Valid @RequestBody Object vo);

}
