package com.zksr.trade.api.order.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年03月28日 17:07
 * @description: 入驻商订单结算信息VO
 */
@Data
@ApiModel("订单信息 - 入驻商订单结算信息Request VO")
@Accessors(chain = true)
public class TrdSupplierOrderSettleSaveVO {

    /** 订单编号 */
    private String orderNo;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 入驻商订单编号 */
    private String supplierOrderNo;

    /** 入驻商订单id */
    private Long supplierOrderId;

    /** 入驻商订单明细编号 */
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id */
    private Long supplierOrderDtlId;

    /** 商品数量 */
    private BigDecimal itemQty;

    /** 商品销售价 */
    private BigDecimal itemPrice;

    /** 商品金额 */
    private BigDecimal itemAmt;

    /** 运费 */
    private BigDecimal transAmt;

    /** 入驻商结算金额 */
    private BigDecimal supplierAmt;

    /** 利润 */
    private BigDecimal profit;

    /** 入驻商成本价 */
    private BigDecimal costPrice;

    /**
     * 商品销售利润比例 占比, 最大0.29 = 29%
     */
    private BigDecimal saleTotalRate;

    /**
     * 利润模式 0=(售价*比例=利润), 1=(售价-进货价=利润)
     */
    private String profitModel;

    /** 软件商分润比例 */
    private BigDecimal softwareRate;

    /** 软件商结算金额 */
    private BigDecimal softwareAmt;

    /** 平台商分润比例 */
    private BigDecimal partnerRate;

    /** 平台商结算金额 */
    private BigDecimal partnerAmt;

    /** 运营商分润比例 */
    private BigDecimal dcRate;

    /** 运营商结算金额 */
    private BigDecimal dcAmt;

    /** 业务员负责人分润比例 */
    private BigDecimal colonel1Rate;

    /** 业务员结算金额 */
    private BigDecimal colonel1Amt;

    /** 业务员分润比例 */
    private BigDecimal colonel2Rate;

    /** 业务员结算金额 */
    private BigDecimal colonel2Amt;
    
    
    /** 门店分润比例 */
    private BigDecimal branchRate;
    
    /** 门店计算金额 */
    private BigDecimal branchAmt;

    /** 支付金额 */
    private BigDecimal payAmt;

    /** 支付公司收取的支付费率 */
    private BigDecimal payRate;

    /** 支付平台手续费;(pay_amt*pay_rate) 四舍五入 */
    private BigDecimal payFee;

    /** 入驻商分钱;pay_amt - pay_fee */
    private BigDecimal supplierDivideAmt;

    /** 入驻商ID  用于保存订单时校验入驻商信息*/
    private Long supplierId;

    /**
     * 总运营商分润比例
     */
    private BigDecimal allDcRate;

    /**
     * 运营商设置的运营商分润比例
     */
    private BigDecimal settingDcRate;

    /**
     * 运营商设置的业务员管理员分润比例
     */
    private BigDecimal settingColonel1Rate;

    /**
     * 业务员管理员提成系数
     */
    private BigDecimal colonel1Percentage;

    /**
     * 满额业务员管理员分润金额
     */
    private BigDecimal allColonel1Amt;


    /**
     * 运营商设置的业务员分润比例
     */
    private BigDecimal settingColonel2Rate;

    /**
     * 业务员提成系数
     */
    private BigDecimal colonel2Percentage;

    /**
     * 满额业务员分润金额
     */
    private BigDecimal allColonel2Amt;



    public  BigDecimal sumProfitAmt (){
        return dcAmt.add(partnerAmt).add(colonel1Amt).add(colonel2Amt);
    }
}
