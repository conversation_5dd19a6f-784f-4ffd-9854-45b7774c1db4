package com.zksr.trade.api.supplierOrder.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 用户发票对象 trd_supplier_order_invoice
 *
 * <AUTHOR>
 * @date 2025-07-04
 */
@Data
@ApiModel("用户发票 - trd_supplier_order_invoice Response VO")
public class TrdSupplierOrderInvoiceRespDTO {
    private static final long serialVersionUID = 1L;

    /** ID主键 */
    @ApiModelProperty(value = "版本号")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long id;

    /** 用户发票id */
    @Excel(name = "用户发票id")
    @ApiModelProperty(value = "用户发票id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberInvoiceId;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /** 入驻商订单ID */
    @Excel(name = "入驻商订单ID")
    @ApiModelProperty(value = "入驻商订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderId;

    /** 入驻商订单号 */
    @Excel(name = "入驻商订单号")
    @ApiModelProperty(value = "入驻商订单号")
    private String supplierOrderNo;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 发票类型,10电子普通发票20专用发票 */
    @Excel(name = "发票类型,10电子普通发票20专用发票")
    @ApiModelProperty(value = "发票类型,10电子普通发票20专用发票")
    private Long invoiceType;

    /** 发票抬头类型,10个人20单位 */
    @Excel(name = "发票抬头类型,10个人20单位")
    @ApiModelProperty(value = "发票抬头类型,10个人20单位")
    private Long titleType;

    /** 发票抬头 */
    @Excel(name = "发票抬头")
    @ApiModelProperty(value = "发票抬头")
    private String invoiceTitle;

    /** 纳税人识别码 */
    @Excel(name = "纳税人识别码")
    @ApiModelProperty(value = "纳税人识别码")
    private String taxpayerCode;

    /** 单位地址 */
    @Excel(name = "单位地址")
    @ApiModelProperty(value = "单位地址")
    private String companyAddress;

    /** 单位电话 */
    @Excel(name = "单位电话")
    @ApiModelProperty(value = "单位电话")
    private String companyPhone;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 开户银行 */
    @Excel(name = "开户银行")
    @ApiModelProperty(value = "开户银行")
    private String bankName;

    /** 银行账户 */
    @Excel(name = "银行账户")
    @ApiModelProperty(value = "银行账户")
    private String bankAccount;

    /** 发票PDF文件下载/访问URL  */
    @Excel(name = "发票PDF文件下载/访问URL ")
    @ApiModelProperty(value = "发票PDF文件下载/访问URL ")
    private String pdfUrl;

    /** 是否删除：0-否，1-是 */
    @ApiModelProperty(value = "发票PDF文件下载/访问URL ")
    private Long delFlag;

    /** 版本号 */
    @Excel(name = "版本号")
    @ApiModelProperty(value = "版本号")
    private Long version;

}
