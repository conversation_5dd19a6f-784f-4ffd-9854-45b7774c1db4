package com.zksr.trade.api.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2024年04月23日 17:57
 * @description: AfterSupplierRespDTO
 */
@Data
@ApiModel("订单售后实体 返回")
@Accessors(chain = true)
public class AfterSupplierRespDTO {
    @ApiModelProperty(value = "售后订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long afterId;

    @ApiModelProperty(value = "售后入驻商订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierAfterId;

    @ApiModelProperty(value = "售后入驻商订单编号")
    private String supplierAfterNo;

    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "售后入驻商订单总数量")
    private Long supplierAfterQty;

    @ApiModelProperty(value = "售后入驻商订单总金额")
    private BigDecimal supplierAfterAmt;

    @ApiModelProperty(value = "入驻商寄回地址")
    private String supplierAddress;

    @ApiModelProperty(value = "入驻商收货手机号")
    private String supplierPhone;

    @ApiModelProperty(value = "入驻商订单明细集合")
    List<AfterSupplierDtlRespDTO> afterSupplierDtlRespDTOList;
}
