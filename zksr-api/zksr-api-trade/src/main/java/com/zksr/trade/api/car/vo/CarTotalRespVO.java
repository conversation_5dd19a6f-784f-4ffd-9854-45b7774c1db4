package com.zksr.trade.api.car.vo;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车角标统计数据
 * @date 2024/3/26 19:16
 */
@Data
@ApiModel(description = "购物车门店统计数据")
public class CarTotalRespVO {

    @ApiModelProperty("本地商品种类数量")
    private Long localProductTypeNum;

    @ApiModelProperty("全国商品种类数量")
    private Long globalProductTypeNum;

    @ApiModelProperty("合计种类商品数量")
    private Long productTypeNum;

    public CarTotalRespVO() {

    }


    public CarTotalRespVO(Long localProductTypeNum, Long globalProductTypeNum) {
        this.localProductTypeNum = Objects.nonNull(localProductTypeNum) ? localProductTypeNum : NumberPool.LONG_ZERO;
        this.globalProductTypeNum = Objects.nonNull(globalProductTypeNum) ? globalProductTypeNum : NumberPool.LONG_ZERO;
        this.productTypeNum = this.localProductTypeNum + this.globalProductTypeNum;
    }
}
