package com.zksr.trade.api.orderSettle.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.branch.BranchTagEnum;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("门店信息 - mem_branch Response VO")
public class BranchStatementExportVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 门店id */
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    @Excel(name = "门店id")
    private Long branchId;

    /** 门店编号 */
    @ApiModelProperty(value = "门店编号")
    @Excel(name = "门店编号")
    private String branchNo;

    /** 平台商id */
//    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    /** 平台商名称 */
    @Excel(name = "平台商名称")
    @ApiModelProperty(value = "平台商名称")
    private String sysName;

    /** 门店名称 */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 城市id */
//    @Excel(name = "城市id")
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 城市名称 */
    @Excel(name = "城市名称")
    @ApiModelProperty(value = "城市名称")
    private String areaName;

    /** 业务员id */
//    @Excel(name = "业务员id")
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    /** 客户状态 */
    @Excel(name = "客户状态")
    private String lifecycleStage;

    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /** 门店地址 */
    @Excel(name = "门店地址")
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 渠道id */
//    @Excel(name = "渠道id")
    @ApiModelProperty(value = "渠道id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long channelId;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    /** 平台商城市分组id */
    @Excel(name = "平台商城市分组id")
    @ApiModelProperty(value = "平台商城市分组id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long groupId;

    /** 联系人 */
    @Excel(name = "联系人")
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private String status;

    /** 状态显示 */
//    @Excel(name = "状态")
//    @ApiModelProperty(value = "状态显示")
//    private String statusName;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date auditTime;

    /** 审核状态 */
    @Excel(name = "审核状态", readConverterExp = "1=已审核,0=未审核")
//    @Excel(name = "审核状态")
    @ApiModelProperty(value = "1已审核 0未审核")
    private Integer auditState;

    @Excel(name = "审核状态")
    @ApiModelProperty(value = "1已审核 0未审核")
    private String auditStateName;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @Excel(name = "价格码")
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    /** 门头照 */
    @Excel(name = "门头照")
    @ApiModelProperty(value = "门头照")
    private String branchImages;


    @Excel(name = "是否支持货到付款", readConverterExp = "0=不支持,1=支持")
    @ApiModelProperty(value = "是否支持货到付款(0,否 1,是)")
    private Integer hdfkSupport;

    /** 最后一次登陆时间 */
    @Excel(name = "最后一次登陆时间")
    @ApiModelProperty(value = "最后一次登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    /** 签到ID */
    @Excel(name = "签到ID")
    @ApiModelProperty(value = "签到ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelVisitLogId;

    /** 最后一次登陆时间 */
    @Excel(name = "注册时间")
    @ApiModelProperty(value = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;

    @Excel(name = "注册类型", readConverterExp = "0=自主注册,1=业务员拓店,2=后台导入")
    @ApiModelProperty(value = "注册类型 0-自主注册,1-业务员拓店,2-后台导入")
    private String registerTypeName;

    /** 未签退的门店ID */
    @Excel(name = "未签退的门店ID")
    @ApiModelProperty(value = "未签退的门店ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long UnSignBranchId;

    @Excel(name = "货到付款最大可欠款金额")
    @ApiModelProperty("货到付款最大可欠款金额")
    private BigDecimal hdfkMaxAmt;

    @ApiModelProperty("一级区域城市ID, 省市区关联(省份)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long firstAreaCityId;

    @ApiModelProperty("二级区域城市ID, 省市区关联(城市)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long secondAreaCityId;

    @ApiModelProperty("三级区域城市ID, 省市区关联(区域)")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long threeAreaCityId;

    @ApiModelProperty("门店标签信息")
    private List<BranchTagEnum> branchTags;
}
