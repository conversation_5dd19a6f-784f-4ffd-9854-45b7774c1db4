package com.zksr.trade.api.after.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月23日 17:58
 * @description: AfterSupplierDtlRespDTO
 */
@Data
@ApiModel("订单售后实体 返回")
@Accessors(chain = true)
public class AfterSupplierDtlRespDTO {

    @ApiModelProperty(value = "售后入驻商订单明细ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierAfterDtlId;

    @ApiModelProperty(value = "售后入驻商订单明细编号")
    private String supplierAfterDtlNo;

    @ApiModelProperty(value = "售后入驻商订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierAfterId;

    @ApiModelProperty(value = "skuID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty(value = "spuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty(value = "退货数量")
    private Long returnQty;

    @ApiModelProperty(value = "退货单价")
    private BigDecimal returnPrice;

    @ApiModelProperty(value = "退货金额")
    private BigDecimal returnAmt;

    @ApiModelProperty(value = "规格信息")
    private String properties;

    @ApiModelProperty(value = "商品名称")
    private String spuName;

    @ApiModelProperty(value = "图片地址")
    private String thumb;

    @ApiModelProperty(value = "商品类型 {0：全国 1：本地}")
    private Long itemType;

    @ApiModelProperty(value = "商品编号")
    private String spuNo;

    @ApiModelProperty(value = "售后单位编号（数据字典：sys_prdt_unit）")
    private String returnUnit;

    @ApiModelProperty(value = "售后单位大小（数据字典：sys_prdt_unit）")
    private Integer returnUnitType;

    @ApiModelProperty("商品条码")
    private String itemBarcode;

    @ApiModelProperty(value = "退款单号")
    private String refundNo;


}
