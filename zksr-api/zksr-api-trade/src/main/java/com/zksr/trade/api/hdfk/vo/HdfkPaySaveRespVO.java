package com.zksr.trade.api.hdfk.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 创建货到付款单
 * @date 2024/5/29 11:40
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HdfkPaySaveRespVO {

    @ApiModelProperty("支付单号")
    private String orderNo;

    @ApiModelProperty("订单信息, 最大100个字符")
    private String body;

    @ApiModelProperty("付款单ID")
    private Long busiId;

    @ApiModelProperty("支付金额")
    private BigDecimal payAmt;

    @ApiModelProperty("结算信息")
    private List<OrderSettlementDTO> settlementDTOS;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderSettlementDTO {

        @ApiModelProperty("进件方账户")
        private String accountNo;

        @ApiModelProperty("分账金额")
        private BigDecimal amt;

        @ApiModelProperty("商户ID")
        private Long merchantId;

        @ApiModelProperty("商户类型")
        private String merchantType;
    }
}
