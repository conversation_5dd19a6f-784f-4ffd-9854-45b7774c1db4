package com.zksr.trade.api.orderSettle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年05月07日 11:21
 * @description: OrderDtlSettleResDTO
 */
@Data
@ApiModel("订单结算信息 - 返回实体（商品）")
public class OrderDtlSettleResDTO {
    @ApiModelProperty(value = "订单ID或售后订单ID")
    private Long orderId;

    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "spuId")
    private Long spuId;

    @ApiModelProperty(value = "spu名称")
    private String spuName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal itemQty;

    @ApiModelProperty(value = "商品金额")
    private BigDecimal itemAmt;

    @ApiModelProperty(value = "商品利润")
    private BigDecimal profit;

    @ApiModelProperty(value = "结算金额(提成金额)")
    private BigDecimal settleAmt;

    @ApiModelProperty(value = "结算比例(实际提成比例)")
    private BigDecimal settleRate;

    @ApiModelProperty(value = "商品下单单位 字典Key:（sys_prdt_unit）")
    private Long orderUnit;
}
