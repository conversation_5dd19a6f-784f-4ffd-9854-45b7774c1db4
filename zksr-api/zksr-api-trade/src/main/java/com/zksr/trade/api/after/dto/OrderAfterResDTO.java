package com.zksr.trade.api.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.trade.api.order.dto.TrdOrderDiscountDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月20日 14:08
 * @description: OrderAfterResDTO
 */
@Data
@ApiModel("订单售后实体 返回")
@Accessors(chain = true)
public class OrderAfterResDTO {
    @ApiModelProperty(value = "订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;
    @ApiModelProperty(value = "订单编号")
    private String orderNo;
    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /**
     * 售后类型  {@link com.zksr.common.core.enums.AfterTypeEnum}
     */
    @ApiModelProperty(value = "售后类型")
    private Long afterType;

    /**
     * 状态  {@link com.zksr.common.core.enums.DeliveryStatusEnum}
     */
    @ApiModelProperty(value = "状态")
    private Long deliveryState;

    @ApiModelProperty(value = "订单退款总金额")
    private BigDecimal afterReturnAmt;

    @ApiModelProperty(value = "入驻商集合")
    private List<OrderSupplier> orderSuppliers;

    @Data
    public static class OrderSupplier {
        @ApiModelProperty(value = "入驻商ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long supplierId;
        @ApiModelProperty(value = "入驻商名称")
        private String supplierName;
        @ApiModelProperty(value = "入驻商订单ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long supplierOrderId;
        @ApiModelProperty(value = "入驻商订单编号")
        private String supplierOrderNo;

        @ApiModelProperty(value = "入驻商订单退款总金额")
        private BigDecimal subReturnAmt;

        @ApiModelProperty(value = "入驻商优惠劵赠品集合")
        private List<TrdOrderDiscountDTO> couponTemplateList;

        @ApiModelProperty(value = "入驻商明细集合")
        private List<OrderSupplierDtl> OrderSupplierDtls;

        @Data
        public static class OrderSupplierDtl {
            @ApiModelProperty(value = "入驻商订单明细ID")
            @JsonSerialize(using = CustomLongSerialize.class)
            private Long supplierOrderDtlId;

            @ApiModelProperty(value = "规格信息")
            private String properties;

            @ApiModelProperty(value = "商品名称")
            private String spuName;

            @ApiModelProperty(value = "可售后数量（下单单位）")
            private BigDecimal totalNum;

            @ApiModelProperty(value = "可退金额")
            private BigDecimal totalAmt;

            @ApiModelProperty(value = "单价（下单单位）")
            private BigDecimal price;

            @ApiModelProperty(value = "图片地址")
            private String thumb;

            @ApiModelProperty(value = "是否是赠品 1-是  0-否")
            private Integer giftFlag;

            @ApiModelProperty(value = "退货单位大小（下单单位） 1：小单位，2：中单位，3：大单位")
            private Integer unitType;

            @ApiModelProperty(value = "退货单位编号（下单单位），取数据字典：sys_prdt_unit")
            private Long orderUnit;

            @ApiModelProperty(value = "退货数量（最小单位）")
            private BigDecimal minTotalNum;

            @ApiModelProperty(value = "单价（最小单位）")
            private BigDecimal minPrice;

            @ApiModelProperty(value = "退货单位编号（最小单位），取数据字典：sys_prdt_unit")
            private Long minUnit;

            @ApiModelProperty(value = "商品SPU编号")
            private String spuNo;

            @ApiModelProperty(value = "退货单位条码")
            private String unitBarcode;

            /** 最旧生产日期 */
            @ApiModelProperty("最旧生产日期")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private Date oldestDate;

            /** 最新生产日期 */
            @ApiModelProperty("最新生产日期")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private Date latestDate;

            @ApiModelProperty(value = "订单销售数量（下单单位）")
            private Long orderTotalNum;

            @ApiModelProperty("商品计价方式类型(字典：spu_pricing_way) 默认为普通商品 1：普通商品（只能是小数） 2：称重商品(允许为小数)")
            private Integer pricingWay = NumberPool.INT_ONE;
        }

    }
}
