package com.zksr.trade.enums;

import com.zksr.common.core.exception.ErrorCode;

/**
 * Bpm 错误码枚举类
 * <p>
 * bpm 系统，使用 1-009-000-000 段
 */
public interface ErrorCodeConstants {

    // ==========  通用流程处理 模块 1-009-000-000 ==========
    ErrorCode HIGHLIGHT_IMG_ERROR = new ErrorCode(1_009_000_002, "获取高亮流程图异常");
    ErrorCode TRD_SUPPLIER_ORDER_NOT_EXISTS = new ErrorCode(1_009_001_001, "入驻商订单不存在");
    ErrorCode TRD_SUPPLIER_ORDER_NOT_ROLE = new ErrorCode(1_009_001_002, "登录账号不是入驻商，操作失败");
    ErrorCode TRD_SUPPLIER_ORDER_NOT_TEMPLATTE = new ErrorCode(1_009_001_003, "该入驻商未对接第三方系统订单功能，请确认配置信息");

    ErrorCode TRD_SUPPLIER_ORDER_CHECK_DELIVERY_STATE = new ErrorCode(1_009_001_004, "订单{}当前状态不能确认收货");
    ErrorCode TRD_SUPPLIER_ORDER_DTL_NOT_EXISTS = new ErrorCode(1_009_002_001, "入驻商订单明细不存在");
    ErrorCode TRD_SUPPLIER_LOCAL_ORDER_DTL_NOT_EXISTS = new ErrorCode(1_009_002_002, "订单{},入驻商本地商品明细不存在！");
    ErrorCode TRD_SUPPLIER_ORDER_SETTLE_NOT_EXISTS = new ErrorCode(1_009_003_001, "入驻商订单结算信息不存在");
    ErrorCode TRD_SUPPLIER_ORDER_DTL_SETTLE_NOT_EXISTS = new ErrorCode(1_009_003_001, "销售订单明细ID：{}结算信息不存在");
    ErrorCode TRD_ORDER_NOT_EXISTS = new ErrorCode(1_009_004_001, "订单不存在");
    ErrorCode TRD_PRICE_CALCULATE_PAY_PRICE_ILLEGAL = new ErrorCode(1_009_004_002, "订单支付价格小于等于0！");
    ErrorCode TRD_ORDER_SAVE_FAIL = new ErrorCode(1_009_004_003, "订单保存失败！");
    ErrorCode TRD_ORDER_NOT_PAY = new ErrorCode(1_009_004_004, "订单支付状态不是已支付！");
    ErrorCode TRD_ORDER_ALREADY_CANCEL = new ErrorCode(1_009_004_005, "订单已经取消了！");
    ErrorCode TRD_ORDER_NOT_CANCEL = new ErrorCode(1_009_004_006, "订单不可取消！");
    ErrorCode TRD_ORDER_NOT_AFTER = new ErrorCode(1_009_004_007, "订单{}不可售后，已推送第三方！");
    ErrorCode TRD_ORDER_NOT_OPERATE = new ErrorCode(1_009_004_009, "入驻商：{}不可操作订单，已对接第三方！");
    ErrorCode TRD_PRICE_CALCULATE_PROFIT_ERROR = new ErrorCode(1_009_004_008, "商品{}利润比例超出或等于100%！");
    ErrorCode TRD_ORDER_CANCEL_FAIL = new ErrorCode(1_009_004_010, "订单取消失败，请稍后再试");
    ErrorCode TRD_ORDER_CANNOT_CANCEL = new ErrorCode(1_009_004_011, "取消失败。仓库已开始作业，暂无法取消，请联系业务员处理。");
    ErrorCode TRD_ORDER_CANCELLING = new ErrorCode(1_009_004_012, "订单取消中，请稍后再试！");
    ErrorCode WX_NOT_SETTLE = new ErrorCode(1_009_004_013, "微信支付还未结账，不能取消订单！");
    ErrorCode OCCUPY_ERP_STOCK_ERROR = new ErrorCode(1_009_004_014, "预占ERP库存失败！");
    ErrorCode TRD_CAR_NOT_EXISTS = new ErrorCode(1_009_005_001, "购物车不存在");
    ErrorCode TRD_SETTLE_NOT_EXISTS = new ErrorCode(1_009_006_001, "订单结算流水不存在！");
    ErrorCode TRD_ORDER_LOG_NOT_EXISTS = new ErrorCode(1_009_007_001, "订单日志不存在！");
    ErrorCode TRD_EXPRESS_IMPORT_NOT_EXISTS = new ErrorCode(1_009_008_001, "快递导入记录不存在");
    ErrorCode TRD_EXPRESS_IMPORT_NULL = new ErrorCode(1_009_008_002, "导入数据为空！");
    ErrorCode TRD_EXPRESS_IMPORT_DTL_NOT_EXISTS = new ErrorCode(1_009_008_003, "快递导入明细不存在");
    ErrorCode TRD_ORDER_EXPRESS_NOT_EXISTS = new ErrorCode(1_009_008_004, "订单快递不存在");

    //==========================  售后 ===================================
    ErrorCode TRD_AFTER_NOT_EXISTS = new ErrorCode(1_009_009_001, "售后单不存在");
    ErrorCode TRD_SUPPLIER_AFTER_NOT_EXISTS = new ErrorCode(1_009_009_002, "入驻商售后单不存在");
    ErrorCode TRD_SUPPLIER_AFTER_DTL_NOT_EXISTS = new ErrorCode(1_009_009_003, "售后单明细不存在");
    ErrorCode TRD_SUPPLIER_AFTER_SETTLE_NOT_EXISTS = new ErrorCode(1_009_009_004, "售后结算信息不存在");
    ErrorCode TRD_SUPPLIER_AFTER_DTL_SETTLE_NOT_EXISTS = new ErrorCode(1_009_009_004, "售后订单明细ID：{}结算信息不存在");
    ErrorCode TRD_AFTER_LOG_NOT_EXISTS = new ErrorCode(1_009_009_005, "售后日志不存在");

    ErrorCode TRD_AFTER_ALREADY_CANCEL = new ErrorCode(1_009_009_006, "售后单{}已取消！");

    ErrorCode TRD_AFTER_ALREADY_APPROVE = new ErrorCode(1_009_009_007, "售后单{}已审核，不可取消！");

    ErrorCode TRD_SUPPLIER_AFTER_DTL_ALREADY_CANCEL = new ErrorCode(1_009_009_008, "售后单明细已取消！");

    ErrorCode TRD_SUPPLIER_AFTER_DTL_CHECK_OTHER_AFTER_APPROVE_AFTER_REFUND = new ErrorCode(1_009_009_009, "已推送给第三方的售后订单存在商品未退货完成！");

    ErrorCode TRD_SUPPLIER_AFTER_DTL_CHECK_NOT_PUSHED_APPROVAL = new ErrorCode(1_009_009_010, "已推送给第三方的售后订单是未推送状态，无法退款！");

    ErrorCode TRD_SUPPLIER_AFTER_ALREADY_CANCEL = new ErrorCode(1_009_009_011, "售后单{}状态异常，撤销售后失败！");

    ErrorCode TRD_SUPPLIER_AFTER_PUSHED = new ErrorCode(1_009_009_012, "入驻商{}已对接第三方系统，请等待第三方处理！");

    //========================== 购物车 ======================================
    ErrorCode TRD_CAR_SUPPLIER_DEBT = new ErrorCode(1_009_010_001, "当前入驻商预留金不足~");
    ErrorCode TRD_CAR_PRODUCT_NOT_RELEASE = new ErrorCode(1_009_010_002, "商品已下架~");
    ErrorCode TRD_CAR_SKU_NONE = new ErrorCode(1_009_010_003, "商品不存在");
    ErrorCode TRD_CAR_UNIT_SIZE_NONE = new ErrorCode(1_009_010_004, "加入购物车商品规格单位不存在");
    ErrorCode TRD_CAR_MAX_NUMBER = new ErrorCode(1_009_010_005, "购物车最大可加入 {} 个商品");
    ErrorCode TRD_CAR_AREA_OUTSIDE = new ErrorCode(1_009_010_006, "您当前所在的门店区域不可参加此活动: {}");
    ErrorCode TRD_CAR_SALE_OUTSIDE = new ErrorCode(1_009_010_007, "您当前所在的门店不可参加此活动: {}");
    ErrorCode TRD_CAR_BRANCH_NOT_EXIST = new ErrorCode(1_009_010_008, "未查询到门店信息");

    //========================== OpenAPI 订单错误码 ======================================
    ErrorCode OPEN_TRD_SUPPLIER_ORDER_DEL_SIZE_NONE = new ErrorCode(1_009_011_001, "出库回传订单详情列表条数与当前订单条数不对等");
    ErrorCode OPEN_TRD_SUPPLIER_AFTER_DEL_SIZE_NONE = new ErrorCode(1_009_011_002, "销售退单回传订单详情列表条数与当前订单条数不对等");

    ErrorCode OPEN_TRD_SUPPLIER_AFTER_JS_DEL_SIZE_NONE = new ErrorCode(1_009_011_003, "拒收退货单生成失败，拒收详情数量为空或0");

    ErrorCode OPEN_TRD_SUPPLIER_AFTER_JS_DEL_QTY_SUM_NONE = new ErrorCode(1_009_011_004, "拒收退货单生成失败，该商品{}的拒收数量加收货数量不等于订单总数量");

    ErrorCode OPEN_TRD_SUPPLIER_ORDER_DEL_JS_SIZE_NONE = new ErrorCode(1_009_011_005, "订单收货详情列表条数与当前订单条数不对等");

    ErrorCode OPEN_TRD_SUPPLIER_ORDER_DEL_ITEM_NONE = new ErrorCode(1_009_011_005, "订单收货详情商品匹配失败");

    ErrorCode OPEN_TRD_SUPPLIER_ORDER_CHECK_JS_AFTER= new ErrorCode(1_009_011_006, "拒收退货单生成失败,该第三方单据：{}已生成拒收单");

    ErrorCode OPEN_TRD_SUPPLIER_ORDER_DEL_LINE_NONE = new ErrorCode(1_009_011_007, "第三方推送的行号商品与订单详情匹配失败,未匹配的商品编号为：{}");

    ErrorCode OPEN_TRD_SUPPLIER_ORDER_SYNC_STOCK = new ErrorCode(1_009_011_008, "同步订单：更新同步库存缓存失败，未获取到对应的订单详情信息，data:{}");

    ErrorCode OPEN_TRD_SUPPLIER_AFTER_SHC_SYNC_STOCK = new ErrorCode(1_009_011_009, "差异售后订单：更新同步库存缓存失败，未获取到对应的差异订单信息/详情信息，data:{}");

    ErrorCode OPEN_TRD_SUPPLIER_AFTER_DEL_CHECK_RETURN_QTY = new ErrorCode(1_009_011_010, "退货确认时实际退货数量不能大于应退数量！异常商品：{}");

    ErrorCode OPEN_TRD_SUPPLIER_ORDER_NO_IS_NULL = new ErrorCode(1_009_011_011, "B2B入驻商销售订单号不能为空！请检查推送数据的准确性");

    ErrorCode OPEN_TRD_SUPPLIER_ORDER_IS_NULL = new ErrorCode(1_009_011_012, "B2B入驻商销售订单未查询到！请检查推送数据的准确性");

    ErrorCode OPEN_TRD_SUPPLIER_AFTER_NO_IS_NULL = new ErrorCode(1_009_011_013, "B2B入驻商销售订单号不能为空！请检查推送数据的准确性");

    ErrorCode OPEN_TRD_SUPPLIER_AFTER_IS_NULL = new ErrorCode(1_009_011_014, "B2B入驻商销售订单未查询到！请检查推送数据的准确性");

    ErrorCode OPEN_TRD_SUPPLIER_ORDER_DEL_CHECK_DELIVER_QTY = new ErrorCode(1_009_011_015, "订单发货失败，该商品{}的发货数量不能大于订单总数量");


    //========================== OpenAPI 商品错误码 ======================================
    ErrorCode THE_PRODUCT_LIST_PARAMETER_CANNOT_BE_EMPTY = new ErrorCode(1_009_012_001, "商品列表参数不能为空");
    ErrorCode THE_PRODUCT_ID_PARAMETER_CANNOT_BE_EMPTY = new ErrorCode(1_009_012_002, "商品编号参数不能为空");
    ErrorCode NO_CORRESPONDING_SPU_FOUND = new ErrorCode(1_009_012_004, "没有找到对应的SPU");
    ErrorCode NO_SKUS_FOUND = new ErrorCode(1_009_012_005, "没有找到对应的SKU");
    ErrorCode SMALL_UNITS_MUST_BE_PASSED = new ErrorCode(1_009_012_006, "小单位必须传");
    ErrorCode CATEGORY_LEVELS_EXCEED_LIMIT   = new ErrorCode(1_009_012_007, "分类层级不能超过三级");
    ErrorCode CATEGORY_VALIDATION_FAILED   = new ErrorCode(1_009_012_008, "分类校验失败，未能匹配到有效的三级分类");

    //========================== 货到付款清账 ======================================
    ErrorCode TRD_HDFK_CLEAR_SETTLES_NULL = new ErrorCode(1_009_013_001, "请刷新数据后重试");
    ErrorCode TRD_HDFK_NOT_SUPPORT_B2B_PAY = new ErrorCode(1_009_013_002, "线下清账不支持微信B2B支付方式, 微信B2B支付请使用在线收款");
    ErrorCode TRD_HDFK_NOT_SETTLE = new ErrorCode(1_009_013_003, "无可清账数据");
    ErrorCode TRD_HDFK_SUPPLIER_NOT_B2B_SETTLE = new ErrorCode(1_009_013_004, "入驻商支持货到付款清账,不允许B2B系统再对入驻商订单做清账");

    ErrorCode TRD_HDFK_PAY_NOT_FOUND = new ErrorCode(1_009_013_005, "找不到货到付款付款单");

    //========================== 订单分享 ======================================
    ErrorCode TRD_ORDER_SHARE_ROLE = new ErrorCode(1_009_014_001, "当前订单不属于操作门店");
    ErrorCode TRD_ORDER_SHARE_MAX = new ErrorCode(1_009_014_002, "订单分享达到上限");
    ErrorCode TRD_ORDER_SHARE_NONE = new ErrorCode(1_009_014_003, "订单不存在");
    ErrorCode TRD_ORDER_SHARE_EXPIRATION = new ErrorCode(1_009_014_004, "分享链接已过期");

    //========================== 司机模块 ======================================
    ErrorCode TRD_DRIVER_PHONE_EXIST = new ErrorCode(1_009_015_001, "司机手机号已存在");
    ErrorCode TRD_DRIVER_IMPORT_NULL = new ErrorCode(1_009_015_002, "导入的司机信息不允许为空");

}
