package com.zksr.trade.api.after.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月24日 10:57
 * @description: OrderAfterUploadExpressSaveVO
 */
@Data
@ApiModel("订单售后实体 保存物流信息")
public class OrderAfterUploadExpressSaveVO {
    @ApiModelProperty(value = "售后订单ID", required = true)
    private Long afterId;

    @ApiModelProperty(value = "售后订单编号")
    private String afterNo;

//    @ApiModelProperty(value = "状态", required = true)
//    private Long deliveryState;

    @ApiModelProperty(value = "入驻商订单集合")
    private List<SupplierAfter> supplierAfters;

    @ApiModel(value = "入驻商售后订单项")
    @Data
    public static class SupplierAfter {
        @ApiModelProperty(value = "入驻商id")
        private Long supplierId;

        @ApiModelProperty(value = "入驻商售后订单id")
        private Long supplierAfterId;

        @ApiModelProperty(value = "入驻商订单明细项数组")
        private List<SupplierAfterDtl> supplierAfterDtls;

        @ApiModel(value = "入驻商售后订单明细项")
        @Data
        public static class SupplierAfterDtl {

            @ApiModelProperty(value = "入驻商订单明细ID")
            private Long supplierAfterDtlId;


            @ApiModelProperty(value = "快递单号")
            private String expressNo;

            @ApiModelProperty(value = "快递公司编码")
            private String expressCom;
        }

    }
}
