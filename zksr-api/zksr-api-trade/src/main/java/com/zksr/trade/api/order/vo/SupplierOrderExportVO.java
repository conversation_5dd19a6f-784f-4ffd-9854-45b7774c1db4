package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@Accessors(chain = true)
@ApiModel("入驻商订单导出实体")
public class SupplierOrderExportVO extends BaseEntity {

    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号")
    private String orderNo;

    @ApiModelProperty("入驻商订单编号")
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "外部订单号")
    @Excel(name = "外部订单号")
    private String sourceOrderNo;

    @ApiModelProperty("订单类型 0：全国商品 1：本地商品")
    @Excel(name = "单据类型", readConverterExp = "0=全国商品, 1=本地商品")
    private String orderType;

    @Excel(name = "状态")
    @ApiModelProperty(value = "异常状态")
    private String errorState = "正常";

    @ApiModelProperty(value = "异常说明")
    @Excel(name = "异常说明")
    private String errorMemo;

    @ApiModelProperty("订单支付时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date payTime;


    @ApiModelProperty("合计金额")
    @Excel(name = "合计金额")
    private BigDecimal subOrderAmt;

    @ApiModelProperty("优惠金额")
    @Excel(name = "优惠金额")
    private BigDecimal subDiscountAmt;

    @ApiModelProperty("实际金额")
    @Excel(name = "实际金额")
    private BigDecimal subPayAmt;

    //订单状态

    @ApiModelProperty(name = "打印状态（0：未打印，1：已打印）")
    @Excel(name = "打印状态", readConverterExp = "0=未打印,1=已打印")
    private String printState;

    @ApiModelProperty("付款方式")
    @Excel(name = "付款方式")
    private String payWay;

    @ApiModelProperty("支付状态 数据字典：sys_pay_status")
    @Excel(name = "支付状态", readConverterExp = " 0=未支付, 1=已支付,2=未付款取消")
    private String payState;

    @ApiModelProperty("订单所属业务员")
    @Excel(name = "订单所属业务员")
    private String colonelName;

    @ApiModelProperty("门店id")
    private Long branchId;


    @ApiModelProperty("门店名称")
    @Excel(name = "门店名称")
    private String branchName;

    @ApiModelProperty("门店地址")
    @Excel(name = "门店地址")
    private String branchAddr;

    @Excel(name = "同步标识", readConverterExp = "0=未推送, 1=已推送, 2=已接收")
    @ApiModelProperty("推送状态 0未推送 1已推送 2已接收")
    private String pushStatus;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String memo;


    @ApiModelProperty("订单创建时间")
    @Excel(name = "订单日期",dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    private Date createDate;

    @Excel(name = "订单年")
    @ApiModelProperty(value = "订单年")
    private String createTimeYear;

    @Excel(name = "订单月")
    @ApiModelProperty(value = "订单月")
    private String createTimeMonth;

    @Excel(name = "订单日")
    @ApiModelProperty(value = "订单日")
    private String createTimeDay;

    @Excel(name = "订单所属的下单门店编号")
    @ApiModelProperty("订单所属的下单门店编号")
    private String branchNo;

    @ApiModelProperty("入驻商id")
    private Long supplierId;


    @Excel(name = "订单所属的入驻商编号")
    @ApiModelProperty(value = "订单所属的入驻商编号")
    private String supplierCode;

    @Excel(name = "订单所属的入驻商名称")
    @ApiModelProperty(value = "订单所属的入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "运营商id")
    private Long dcId;


    @Excel(name = "订单所属的运营商编号")
    @ApiModelProperty(value = "订单所属的运营商编号")
    private String dcCode;

    @Excel(name = "订单所属的运营商名称")
    @ApiModelProperty(value = "订单所属的运营商名称")
    private String dcName;

    @Excel(name = "订单所属的下单门店渠道")
    @ApiModelProperty(value = "订单所属的下单门店渠道")
    private String channelName;


    @ApiModelProperty("业务员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long colonelId;

    @ApiModelProperty("上级业务员id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pcolonelId;

    @ApiModelProperty(value = "城市ID")
    private Long areaId;


    @ApiModelProperty("业务员经理")
    @Excel(name = "订单所属的销售经理")
    private String pcolonelName;

    @ApiModelProperty(value = "订单所属的一级区域")
    @Excel(name = "订单所属的一级区域")
    private String oneArea;

    @ApiModelProperty(value = "订单所属的二级区域")
    @Excel(name = "订单所属的二级区域")
    private String twoArea;

    @ApiModelProperty(value = "订单所属的三级区域")
//    @Excel(name = "订单所属的三级区域")
    private String threeArea;

    @Excel(name = "订单所属的省份")
    @ApiModelProperty(value = "订单所属的省份")
    private String province;

    @Excel(name = "订单所属的城市")
    @ApiModelProperty(value = "订单所属的城市")
    private String city;

    @Excel(name = "订单所属的区县")
    @ApiModelProperty(value = "订单所属的区县")
    private String county;

    @Excel(name = "入驻商订单状态", readConverterExp = "0=待付款,10=待发货,20=待收货,21=已收货,30=售后中,40=已完成,50=已取消")
    @ApiModelProperty(value = "入驻商订单状态")
    private String deliveryState;

}
