package com.zksr.trade.api.car.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 购物车对象 trd_car
 *
 * <AUTHOR>
 * @date 2024-05-06
 */
@Data
@ApiModel("购物车 - trd_car Response VO")
public class TrdCarApiRespVO {
    private static final long serialVersionUID = 1L;

    /** 购物车id */
    @ApiModelProperty(value = "是否业务员推荐")
    private Long carId;

    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "创建人")
    private String createMemberId;

    @ApiModelProperty(value = "更新人")
    private String updateMemberId;

    @ApiModelProperty(value = "门店id")
    private Long branchId;

    @ApiModelProperty(value = "入驻商上架商品id")
    private Long supplierItemId;

    @ApiModelProperty(value = "城市上架商品id")
    private Long areaItemId;

    @ApiModelProperty(value = "商品SPU id")
    private Long spuId;

    @ApiModelProperty(value = "商品sku id")
    private Long skuId;

    @ApiModelProperty(value = "数量")
    private Long qty;

    @ApiModelProperty(value = "是否选中 1-是 0-否")
    private Integer selected;

    @ApiModelProperty(value = "是否业务员推荐")
    private Integer recommendFlag;

    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位", notes = "7")
    private Integer unitSize;

}
