package com.zksr.trade.api.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("前置仓小程序订单操作实体类")
public class TrdOrderOperDTO {

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "订单集合")
    private List<TrdOrderVO> trdOrderVOS;

    @ApiModel(value = "订单项")
    @Data
    public static class TrdOrderVO {
        @ApiModelProperty(value = "订单id")
        private Long orderId;

        @ApiModelProperty(value = "订单编号")
        private String orderNo;
    }
}
