package com.zksr.trade.api.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.trade.api.order.dto.TrdOrderDiscountDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月20日 14:08
 * @description: OrderMergeAfterResDTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("订单合单售后实体 返回")
@Accessors(chain = true)
@Builder
public class OrderMergeAfterResDTO {

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "spuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty(value = "商品订单集合")
    private List<BranchSkuMergeAfterResDTO> branchSkuMergeAfterResDTOList;

    //==================================非数据库表存储字段=====================================
    @ApiModelProperty(value = "图片地址")
    private String thumb;

    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    @ApiModelProperty(value = "商品条码")
    private String barcode;



}
