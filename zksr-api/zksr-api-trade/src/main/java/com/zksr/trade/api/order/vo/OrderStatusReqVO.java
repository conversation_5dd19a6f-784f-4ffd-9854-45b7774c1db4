package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("我的订单状态角标数字 请求实体")
public class OrderStatusReqVO {
    @ApiModelProperty("请求查询类型（1： 门店 2：业务员）")
    private Integer reqType;

    @ApiModelProperty("请求查询ID（ 门店或业务员ID）")
    private Long reqId;
}
