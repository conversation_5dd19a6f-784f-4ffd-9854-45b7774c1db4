package com.zksr.trade.api.order.dto;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TrdPackageDto {
    @ApiModelProperty(value = "包裹ID")
    private Long packageId;

    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "包裹编号")
    private String packageNo;

    @ApiModelProperty(value = "供应商ID")
    private Long supplierId;

    @ApiModelProperty(value = "供应商订单ID")
    private Long supplierOrderId;

    @ApiModelProperty(value = "供应商订单编号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "发货状态(0-待发货 1-已发货 2-已签收)")
    private Integer deliveryState;

    @ApiModelProperty(value = "快递公司")
    private String expressCom;

    @ApiModelProperty(value = "快递公司编号")
    private String expressComNo;

    @ApiModelProperty(value = "发货时间")
    private Date deliveryTime;

    @ApiModelProperty(value = "预计送达时间")
    private Date planReceiveTime;

    @ApiModelProperty(value = "实际送达时间")
    private Date actReceiveTime;

    @ApiModelProperty(value = "包裹商品总数量")
    private Integer totalNum;

    @ApiModelProperty(value = "包裹商品总金额")
    private BigDecimal totalAmt;

    @ApiModelProperty(value = "包裹重量(kg)")
    private BigDecimal weight;

    @ApiModelProperty(value = "包裹体积(m³)")
    private BigDecimal volume;

    @ApiModelProperty(value = "包裹长度(cm)")
    private BigDecimal length;

    @ApiModelProperty(value = "包裹宽度(cm)")
    private BigDecimal width;

    @ApiModelProperty(value = "包裹高度(cm)")
    private BigDecimal height;

    @ApiModelProperty(value = "幂等键(防止重复提交)")
    private String idempotentKey;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}