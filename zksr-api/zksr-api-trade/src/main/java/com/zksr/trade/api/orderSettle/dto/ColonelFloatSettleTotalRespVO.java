package com.zksr.trade.api.orderSettle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员提成统计
 * @date 2024/9/27 10:48
 */
@ApiModel(description = "业务员浮动提成统计")
@Data
public class ColonelFloatSettleTotalRespVO {

    @ApiModelProperty("已到账金额")
    private BigDecimal settleAmt;

    @ApiModelProperty("未到账金额")
    private BigDecimal noSettleAmt;

    @ApiModelProperty("月销售分润明细（按天）")
    List<ColonelCommissionDayTotalRespDTO> dayTotalList;
}
