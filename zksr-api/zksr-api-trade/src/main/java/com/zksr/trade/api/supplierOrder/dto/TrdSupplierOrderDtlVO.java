package com.zksr.trade.api.supplierOrder.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

import com.zksr.common.core.annotation.Excel;

@Data
public class TrdSupplierOrderDtlVO {
    private static final long serialVersionUID=1L;

    private String orderNo;
    /** 入驻商订单明细id */
    private Long supplierOrderDtlId;
    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    private Long supplierOrderId;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    private Long spuId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    private Long skuId;

    /** 商品spu 名称 */
    @Excel(name = "商品spu 名称")
    private String spuName;

    /** 商品数量 */
    @Excel(name = "商品数量")
    private Long totalNum;

    /** 合计金额（price*total_num） */
    @Excel(name = "合计金额", readConverterExp = "p=rice*total_num")
    private BigDecimal totalAmt;

    /** 成交价 */
    @Excel(name = "成交价")
    private BigDecimal price;

    /** 原销售价 */
    @Excel(name = "原销售价")
    private BigDecimal salePrice;

    /** *订单状态（数据字典） */
    @Excel(name = "*订单状态", readConverterExp = "数=据字典")
    private Long deliveryState;

    /** 是否是赠品 1-是  0-否 */
    @Excel(name = "是否是赠品 1-是  0-否")
    private Integer giftFlag;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 优惠劵优惠金额(分摊的) */
    @Excel(name = "优惠劵优惠金额(分摊的)")
    private BigDecimal couponDiscountAmt;

    /** 优惠劵优惠金额(不分摊的) */
    @Excel(name = "优惠劵优惠金额(不分摊的)")
    private BigDecimal couponDiscountAmt2;

    /** 活动优惠金额(不分摊的) */
    @Excel(name = " 活动优惠金额(分摊的)")
    private BigDecimal activityDiscountAmt;

    /** 是否已经同步 1-是 0-否 */
    @Excel(name = "是否已经同步 1-是 0-否")
    private Integer syncFlag;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** spu规格 **/
    @Excel(name = "spu规格")
    private String specName;

    /** 主订单ID **/
    @Excel(name = "主订单ID")
    private Long orderId;

    /** 商品类型 0：全国商品 1：本地商品 **/
    @Excel(name = "商品类型 0：全国商品 1：本地商品")
    private Integer itemType;

    /** 发货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发货时间")
    private Date deliveryTime;

    /** 收货时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "收货时间")
    private Date receiveTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间")
    private Date completeTime;

    /** 发货数量 */
    @Excel(name = "发货数量")
    private Long sendQty;

    /** 发货前取消数量 */
    @Excel(name = "发货前取消数量")
    private Long cancelQty;

    /** 发货前取消金额 */
    @Excel(name = "发货前取消金额")
    private BigDecimal cancelAmt;

    /** 精准成交价（6位小数） */
    @Excel(name = "精准成交价")
    private BigDecimal exactPrice;

    /** 精准商品金额（6位小数） */
    @Excel(name = "精准商品金额")
    private BigDecimal exactTotalAmt;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 */
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    private Integer payState;

    /** 支付平台 */
    @Excel(name = "支付平台")
    private String platform;

    /** 商品订单原总金额（salePrice*total_num） */
    @Excel(name = "商品订单原总金额")
    private BigDecimal subOrderAmt;

    /** 入驻商订单行号 */
    @Excel(name = "入驻商订单行号")
    private Long lineNum;

    /**
     * 平台商品牌id
     */
    @Excel(name = "平台商品牌id")
    private Long brandId;

    //=========================下单===========================
    /** 订单购买单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "订单购买单位")
    private String orderUnit;

    /** 单位大小 {@link com.zksr.common.core.enums.UnitTypeEnum}*/
    @Excel(name = "购买单位大小")
    private Integer orderUnitType;

    /** 订单购买单位数量;购买的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "订单购买单位数量")
    private Long orderUnitQty;

    /** 订单换算数量;小单位为1，中、大单位的换算数量  取SPU中对应单位的换算数量*/
    @Excel(name = "订单购买换算数量")
    private Long orderUnitSize;

    /** 订单购买单位原价格  取价逻辑:原销售价 /当前单位数量 */
    @Excel(name = "订单购买原单位价格")
    private BigDecimal orderUnitPrice;

    /** 订单购买单位销售价格（6位小数）  取价逻辑: 合计金额（totalAmt） / 下单单位数量*/
    @Excel(name = "订单购买单位实际销售单价")
    private BigDecimal orderSalesUnitPrice;

    //===========================发货=================================
    /** 发货单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "发货单位")
    private String sendUnit;

    /** 发货单位大小 {@link com.zksr.common.core.enums.UnitTypeEnum}*/
    @Excel(name = "发货单位大小")
    private Integer sendUnitType;

    /** 发货单位数量;发货的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "发货单位数量")
    private Long sendUnitQty;

    /** 发货单位换算数量 */
    @Excel(name = "发货单位换算数量")
    private Long sendUnitSize;

    //=========================取消========================
    /** 发货前取消单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "发货前取消单位")
    private String cancelUnit;

    /** 单位大小 */
    @Excel(name = "单位大小")
    private Integer cancelUnitType;

    /** 发货前取消数量;发货前取消的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "发货前取消数量")
    private Long cancelUnitQty;

    /** 发货前取消单位换算数量 */
    @Excel(name = "发货前取消单位换算数量")
    private Long cancelUnitSize;

    //=========================拒收========================
    /** 拒收数量;最小单位 */
    @Excel(name = "拒收数量")
    private Long rejectQty;

    /** 拒收单位;最小单位的单位，中单位的单位，大单位的单位 */
    @Excel(name = "拒收单位")
    private Integer rejectUnit;

    /** 单位大小 */
    @Excel(name = "单位大小")
    private Long rejectUnitType;

    /** 拒收单位数量;拒收的是中单位，即为中单位数量。小单位、大单位同理 */
    @Excel(name = "拒收单位数量")
    private Long rejectUnitQty;

    /** 拒收单位换算数量 */
    @Excel(name = "拒收单位换算数量")
    private Long rejectUnitSize;

    /** 收货是否收款 0否 1是*/
    @Excel(name = "收货是否收款 0否 1是")
    private Integer isProceeds;

    /** 货到付款单ID */
    @Excel(name = "货到付款单ID")
    private Long hdfkPayId;

    /** 同步库存标识 0否 1是*/
    @Excel(name = "同步库存标识 0否 1是")
    private Integer syncStock;

    /**
     * 管理分类Id
     */
    @Excel(name = "管理分类Id")
    private Long categoryId;

    /** 最旧生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最旧生产日期")
    private Date oldestDate;

    /** 最新生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最新生产日期")
    private Date latestDate;


    /**
     * 赠品优惠分摊单价（6位小数）
     */
    @Excel(name = "赠品优惠分摊单价（6位小数）")
    private BigDecimal exactGiftSharePrice;

    /**
     * 赠品分摊价小计
     */
    @Excel(name = "赠品分摊价小计")
    private BigDecimal giftShareSubtotalAmt;

    /**
     * 均摊赠品优惠后的成交价（6位小数）
     */
    @Excel(name = "均摊赠品优惠后的成交价（6位小数）")
    private BigDecimal afterGiftSharePrice;

    /**
     * 均摊赠品优惠后的购买单位成交价（6位小数）
     */
    @Excel(name = "均摊赠品优惠后的购买单位成交价（6位小数）")
    private BigDecimal afterGiftShareUnitPrice;

    /**
     * 均摊赠品优惠后的小计
     */
    @Excel(name = "均摊赠品优惠后的小计")
    private BigDecimal afterGiftShareSubtotalAmt;

    /**
     * SKU最小单位数量平均单价（6位小数）
     */
    @Excel(name = "SKU最小单位数量平均单价（6位小数）")
    private BigDecimal skuAvgPrice;

    /**
     * 赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价）
     */
    @Excel(name = "赠品取价算法-数据字典（0-零价(默认) 1-均价 2-分摊价）")
    private Integer giftPriceType;

    /**
     * 赠品取价算法- 对应最终单价 （6位小数）
     */
    @Excel(name = "取价算法- 对应最终单价 （6位小数）")
    private BigDecimal resPrice;

    /**
     * 赠品取价算法- 对应最终购买单位单价 （6位小数）
     */
    @Excel(name = "取价算法- 对应最终购买单位单价 （6位小数）")
    private BigDecimal resUnitPrice;

    /**
     * 赠品取价算法- 对应最终金额
     */
    @Excel(name = "取价算法- 对应最终金额（2位小数）")
    private BigDecimal resAmt;

    /**
     * 加单指令ID
     */
    private Long commandId;

    /**
     * 订单总金额
     */
    private BigDecimal saleTotalAmt;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmt;

    /**
     * 优惠卷优惠金额
     */
    private BigDecimal couponAmt;

    /**
     * 用卷下单金额
     */
    private BigDecimal useCouponOrderMoney;
}
