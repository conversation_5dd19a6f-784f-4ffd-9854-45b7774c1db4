package com.zksr.trade.api.express.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @date 2024年04月18日 19:55
 * @description: OrderExpressResDTO
 */
@Data
@ApiModel("订单快递信息实体")
public class OrderExpressResDTO extends BaseEntity {

    @ApiModelProperty(value = "订单快递id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderExpressId;

    @ApiModelProperty(value = "订单id;订单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;

    @ApiModelProperty(value = "订单编号;订单编号")
    private String orderNo;

    @ApiModelProperty(value = "入驻商订单明细id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierOrderDtlId;

    @ApiModelProperty(value = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    @ApiModelProperty(value = "快递单号")
    private String expressNo;

    @ApiModelProperty(value = "快递公司")
    private String expressCom;

    @ApiModelProperty(value = "快递公司编码")
    private String expressComNo;

    @ApiModelProperty(value = "收货人")
    private String receiveMan;

    @ApiModelProperty(value = "收货人电话")
    private String receivePhone;

    @ApiModelProperty(value = "收货地址")
    private String address;

    @ApiModelProperty(value = "最新物流轨迹")
    private String latestInfo;

    @ApiModelProperty(value = "快递导入明细id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long expressImportDtlId;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    @ApiModelProperty(value = "真实发货时间(快递开始运输时间")
    private Date realInTransitTime;

    @ApiModelProperty(value = "物流状态(数据字典);0-暂无轨迹信息 1-已揽收 2-在途中 3-签收 4-问题件")
    private Long state;
}
