package com.zksr.trade.api.after.vo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 售后单对象 trd_after
 *
 * <AUTHOR>
 * @date 2024-04-20
 */
@TableName(value = "trd_after")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdAfter extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 售后单id */
    @TableId(type= IdType.ASSIGN_ID)
    private Long afterId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 售后单编号 */
    @Excel(name = "售后单编号")
    private String afterNo;

    /** 运营商id */
    @Excel(name = "运营商id")
    private Long dcId;

    /** 城市id */
    @Excel(name = "城市id")
    private Long areaId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long memberId;

    /** 业务员id */
    @Excel(name = "业务员id")
    private Long colonelId;

    /** 业务员级别 */
    @Excel(name = "业务员级别")
    private Long colonelLevel;

    /** 父业务员id */
    @Excel(name = "父业务员id")
    private Long pcolonelId;

    /** 父业务员级别 */
    @Excel(name = "父业务员级别")
    private Long pcolonelLevel;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String branchAddr;

    /** 退货原因 */
    @Excel(name = "退货原因")
    private String reason;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmt;

    /** 订单本次售后退款金额（不包括优惠） */
    @Excel(name = "订单本次售后退款金额（不包括优惠）")
    private BigDecimal returnOrderAmt;

    /** 本次售后订单优惠金额 */
    @Excel(name = "本次售后订单优惠金额")
    private BigDecimal returnDiscountAmt;

    /** 退货说明 */
    @Excel(name = "退货说明")
    private String descr;

    /** 订单退款金额;包含本次售后单和之前未取消的售后单的退款金额之和 */
    @Excel(name = "订单退款金额;包含本次售后单和之前未取消的售后单的退款金额之和")
    private BigDecimal orderRefundAmt;

    /** 订单支付金额;从订单表 */
    @Excel(name = "订单支付金额;从订单表")
    private BigDecimal payAmt;

    /** 退款方式（数据字典）;从订单表 0-在线支付 1-储值支付 2-货到付款 */
    @Excel(name = "退款方式", readConverterExp = "数=据字典")
    private String payWay;

    /** 支付平台(数据字典);从订单表 */
    @Excel(name = "支付平台(数据字典);从订单表")
    private String platform;

    /** 订单支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单支付时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payTime;

    /** 退货完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退货完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date returnTime;

    /** 退款完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refundTime;

    /** 是否已取消 0未取消 1已取消 */
    @Excel(name = "是否已取消 0未取消 1已取消")
    private Long isCancel;

    /** 来源(数据字典);1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序 */
    @Excel(name = "来源(数据字典);1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序")
    private Long source;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 申请时上传的凭证照片 */
    @Excel(name = "申请时上传的凭证照片")
    private String applyImgs;

    /** 回填快递单时上传的凭证照片 */
    @Excel(name = "回填快递单时上传的凭证照片")
    private String expressImgs;

    /** 支付公司收取的支付费率;从订单表 */
    @Excel(name = "支付公司收取的支付费率;从订单表")
    private BigDecimal payRate;

    /** 支付平台手续费;从订单表 (pay_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费;从订单表 (pay_amt*pay_rate) 四舍五入")
    private BigDecimal payFee;

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 订单类型：0 全国订单 1：本地订单 */
    @Excel(name = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    /**
     * 售后储值本金比率
     */
    @Excel(name = "售后储值本金比率")
    private BigDecimal returnCzPrincipalRate = BigDecimal.ZERO;

    /**
     * 售后储值本金支付金额
     */
    @Excel(name = "售后储值本金支付金额")
    private BigDecimal returnCzPrincipalPayAmt = BigDecimal.ZERO;

    /**
     * 售后储值赠金支付金额
     */
    @Excel(name = "售后储值赠金支付金额")
    private BigDecimal returnCzGivePayAmt = BigDecimal.ZERO;
}
