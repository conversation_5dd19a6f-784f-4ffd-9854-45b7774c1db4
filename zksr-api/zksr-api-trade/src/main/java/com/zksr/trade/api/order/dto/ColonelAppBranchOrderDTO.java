package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 *  客户使用优惠劵信息汇总
 */
@Data
@ApiModel("业务员APP -- 客户列表本月订单统计信息")
public class ColonelAppBranchOrderDTO {

    /** 订单ID */
    @Excel(name = "订单ID")
    private Long orderId;

    /** 订单详情实际销售金额 （合计金额-发货前取消金额） */
    @Excel(name = "订单实际销售金额")
    private BigDecimal realSaleAmt;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmt;

    /** 订单创建时间 */
    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
