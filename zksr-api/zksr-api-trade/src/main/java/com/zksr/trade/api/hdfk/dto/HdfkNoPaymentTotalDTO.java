package com.zksr.trade.api.hdfk.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 货到付款未付款统计
 * @date 2024/5/29 9:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "货到付款未付款统计")
public class HdfkNoPaymentTotalDTO {

    @ApiModelProperty("应付款金额")
    public BigDecimal totalAmt = BigDecimal.ZERO;

}
