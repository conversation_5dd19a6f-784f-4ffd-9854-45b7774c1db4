package com.zksr.trade.api.orderSettle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/10/30 14:32
 * @订单分佣对账单
 */
@Data
@ApiModel("订单结算 - 订单分佣对账单 Response VO")
public class OrderCommissionStatementResDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 入驻商id
     */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /**
     * 入驻商名称
     */
    @Excel(name = "入驻商名称")
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /**
     * 门店名称
     */
    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /**
     * 下单时间
     */
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 订单类型
     */
    @Excel(name = "订单类型")
    @ApiModelProperty(value = "订单类型")
    private String orderType;

    /**
     * 订单金额
     */
    @Excel(name = "订单金额")
    @ApiModelProperty(value = "订单金额")
    private BigDecimal subPayAmt;

    /**
     * 订单状态名称
     */
    @Excel(name = "订单状态名称")
    @ApiModelProperty(value = "订单状态名称")
    private String deliveryStateName;

    /**
     * 结算金额
     */
    @Excel(name = "结算金额")
    @ApiModelProperty(value = "结算金额")
    private BigDecimal subSettleAmt;

    /**
     * 优惠券金额
     */
    @Excel(name = "优惠券金额")
    @ApiModelProperty(value = "优惠券金额")
    private BigDecimal subDiscountAmt;

    /**
     * 订单编号
     */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 子订单号(入驻商订单号)
     */
    @Excel(name = "子订单号(入驻商订单号)")
    @ApiModelProperty(value = "子订单号(入驻商订单号)")
    private String supplierOrderNo;

    /**
     * 分账状态
     */
    @Excel(name = "分账状态")
    @ApiModelProperty(value = "分账状态")
    private String settleStatus;

    /**
     * 分润金额
     */
    @Excel(name = "分润金额")
    @ApiModelProperty(value = "分润金额")
    private BigDecimal settleAmt;

    /**
     * 已分润金额
     */
    @Excel(name = "已分润金额")
    @ApiModelProperty(value = "已分润金额")
    private BigDecimal settledAmt;

    /**
     * 软件商名称
     */
    @Excel(name = "软件商名称")
    @ApiModelProperty(value = "软件商名称")
    private String softwareName;

    /**
     * 软件商应分账金额
     */
    @Excel(name = "软件商应分账金额")
    @ApiModelProperty(value = "软件商应分账金额")
    private BigDecimal softwareSettleAmt;

    /**
     * 软件商已分账金额
     */
    @Excel(name = "软件商已分账金额")
    @ApiModelProperty(value = "软件商已分账金额")
    private BigDecimal softwareSettledAmt;

    /**
     * 软件商应分账比例
     */
    @Excel(name = "软件商应分账比例")
    @ApiModelProperty(value = "软件商应分账比例")
    private BigDecimal softwareSettleRatio;

    /**
     * 软件商分账状态
     */
    @Excel(name = "软件商分账状态")
    @ApiModelProperty(value = "软件商分账状态")
    private String softwareSettleStatus;

    /**
     * 软件商分账完成时间
     */
    @Excel(name = "软件商分账完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "软件商分账完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date softwareSettleTime;

    /**
     * 平台商名称
     */
    @Excel(name = "平台商名称")
    @ApiModelProperty(value = "平台商名称")
    private String partnerName;

    /**
     * 平台商应分账
     */
    @Excel(name = "平台商应分账")
    @ApiModelProperty(value = "平台商应分账")
    private BigDecimal partnerSettleAmt;

    /**
     * 平台商已分账
     */
    @Excel(name = "平台商已分账")
    @ApiModelProperty(value = "平台商已分账")
    private BigDecimal partnerSettledAmt;


    /**
     * 平台商分账比例
     */
    @Excel(name = "平台商分账比例")
    @ApiModelProperty(value = "平台商分账比例")
    private BigDecimal partnerSettleRatio;

    /**
     * 平台商分账状态
     */
    @Excel(name = "平台商分账状态")
    @ApiModelProperty(value = "平台商分账状态")
    private String partnerSettleStatus;

    /**
     * 平台商分账完成时间
     */
    @Excel(name = "平台商分账完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "平台商分账完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date partnerSettleTime;


    /**
     * 运营商名称
     */
    @Excel(name = "运营商名称")
    @ApiModelProperty(value = "运营商名称")
    private String dcName;

    /**
     * 运营商应分账
     */
    @Excel(name = "运营商应分账")
    @ApiModelProperty(value = "运营商应分账")
    private BigDecimal dcSettleAmt;

    /**
     * 运营商已分账
     */
    @Excel(name = "运营商已分账")
    @ApiModelProperty(value = "运营商已分账")
    private BigDecimal dcSettledAmt;

    /**
     * 运营商分账比例
     */
    @Excel(name = "运营商分账比例")
    @ApiModelProperty(value = "运营商分账比例")
    private BigDecimal dcSettleRatio;

    /**
     * 运营商分账状态
     */
    @Excel(name = "运营商分账状态")
    @ApiModelProperty(value = "运营商分账状态")
    private String dcSettleStatus;

    /**
     * 运营商分账完成时间
     */
    @Excel(name = "运营商分账完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "运营商分账完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dcSettleTime;

    /**
     * 业务员名称
     */
    @Excel(name = "业务员名称")
    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    /**
     * 业务员应分账
     */
    @Excel(name = "业务员应分账")
    @ApiModelProperty(value = "业务员应分账")
    private BigDecimal colonelSettleAmt;

    /**
     * 业务员已分账
     */
    @Excel(name = "业务员已分账")
    @ApiModelProperty(value = "业务员已分账")
    private BigDecimal colonelSettledAmt;

    /**
     * 业务员分账比例
     */
    @Excel(name = "业务员分账比例")
    @ApiModelProperty(value = "业务员分账比例")
    private BigDecimal colonelSettleRatio;

    /**
     * 业务员分账状态
     */
    @Excel(name = "业务员分账状态")
    @ApiModelProperty(value = "业务员分账状态")
    private String colonelSettleStatus;

    /**
     * 业务员分账完成时间
     */
    @Excel(name = "业务员分账完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "业务员分账完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date colonelSettleTime;

    /**
     * 支付平台分账完成时间
     */
    @Excel(name = "支付平台分账完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "支付平台分账完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date platformSettleTime;

}
