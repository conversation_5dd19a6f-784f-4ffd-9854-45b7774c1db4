package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.Date;

@ApiModel("订单信息 分页请求对象")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TrdOrderPageReqDTO extends PageParam {
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long memberId;

    @ApiModelProperty(value = "业务员ID", hidden = true)
    private Long colonelId;

    @ApiModelProperty(value = "订单状态 数据字典：sys_delivery_status")
    private Integer deliveryState;

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "主订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = DateUtils.YYYY_MM_DD_HH_MM_SS_SSS)
    private Date endTime;

    @ApiModelProperty(value = "入驻商订单ID")
    private Long supplierOrderId;

    @ApiModelProperty(value = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "超时未发货订单标志")
    private Integer orderDeliveryOvertimeFlag;


}
