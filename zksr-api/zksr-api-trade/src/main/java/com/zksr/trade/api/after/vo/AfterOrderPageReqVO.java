package com.zksr.trade.api.after.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月23日 17:08
 * @description: AfterOrderPageReqVO
 */
@ApiModel("售后订单信息 分页请求对象")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AfterOrderPageReqVO extends PageParam {
    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long memberId;

    @ApiModelProperty(value = "业务员ID", hidden = true)
    private Long colonelId;

    @ApiModelProperty(value = "关键字")
    private String keyWords;

    @ApiModelProperty(value = "开始时间")
    private String startDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "售后订单ID")
    private Long afterId;

    @ApiModelProperty(value = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    @ApiModelProperty(value = "退款单号")
    private String refundNo;

    @ApiModelProperty(value = "售后单号")
    private String afterNo;

    @ApiModelProperty(value = "入驻商售后单号")
    private String supplierAfterNo;

    @ApiModelProperty(value = "订单单号")
    private String orderNo;

    @ApiModelProperty(value = "单据类型")
    private Long productType;

    @ApiModelProperty(value = "单据状态{0：全部，1：待处理，2：处理中，3：已处理（完成）}")
    private Long approveState;

    @ApiModelProperty(value = "处理中状态{0：(处理中状态全部)，1：待处理， 2：未退货，3：退货中，4：已退货，5：待退款，6：退款中，7：退款失败, 8:已处理（完成），99：可确认退款， 100：发货前售后单据}")
    private Long handleState;

    @ApiModelProperty(value = "入驻商ID", hidden = true)
    private Long supplierId;

    @ApiModelProperty(value = "入驻商售后订单外部订单号")
    private String sourceOrderNo;

}
