package com.zksr.trade.api.orderSettle;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.orderSettle.dto.*;
import com.zksr.trade.api.orderSettle.vo.OrderCommissionStatementPageVo;
import com.zksr.trade.api.orderSettle.vo.OrderSettlePageVO;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

@FeignClient(
        contextId = "remoteOrderSettleApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface OrderSettleApi {
    String PREFIX = ApiConstants.PREFIX + "/orderSettle";

    /**
     * 查询订单流水数据 (业务员APP)
     * @param pageVO
     * @return
     */
    @PostMapping(value =PREFIX + "/getColonelOrderSettleInfoPage")
    PageResult<OrderSettleColonelResDTO> getColonelOrderSettleInfoPage(@RequestBody OrderSettlePageVO pageVO);

    /**
     * 获取业务员APP提成统计
     * @param colonelId 业务员ID
     * @return  统计数据
     */
    @GetMapping(value =PREFIX + "/getColonelSettleTotal")
    CommonResult<ColonelFixSettleTotalRespVO> getColonelSettleTotal(@RequestParam("colonelId") Long colonelId);

    /**
     * 获取业务员APP提成统计, 区间结算查询
     * @param reqVO req
     * @return  统计数据
     */
    @PostMapping(value =PREFIX + "/getColonelSettleTotalRange")
    CommonResult<ColonelFloatSettleTotalRespVO> getColonelSettleTotalRange(@RequestBody ColonelFloatSettleTotalReqVO reqVO);

    /**
     * 获取商户结算流水明细
     * @param pageVO
     * @return
     */
    @PostMapping(value =PREFIX + "/getAccountOrderSettleFlowPageList")
    CommonResult<List<OrderSettleResDTO>> getAccountOrderSettleFlowPageList(@RequestBody OrderSettlePageVO pageVO);


    /**
     * 获取订单分佣对账单数据
     * @param pageVO
     * @return
     */
    @PostMapping(value =PREFIX + "/getStatementOfAccountPage")
    CommonResult<List<OrderCommissionStatementResDTO>> getStatementOfAccountPage(@RequestBody OrderCommissionStatementPageVo pageVO);

    /**
     * 根据业务员ID获取业务员APP首页的提成金额（当天）
     *
     * @param colonelId
     * @return
     */
    @GetMapping(value =PREFIX + "/getColonelAppPercentageAmt")
    CommonResult<BigDecimal> getColonelAppPercentageAmt(@RequestParam("colonelId") Long colonelId);

}
