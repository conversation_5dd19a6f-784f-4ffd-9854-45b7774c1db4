package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2024/4/25 17:50
 */
@Data
@ApiModel("单月订单数据统计 VO")
public class OrderAmountStatisticsVO implements Serializable {

    @ApiModelProperty("在途金额")
    private BigDecimal outstandingAmount;
    @ApiModelProperty("到货金额")
    private BigDecimal arrivalAmount;
    @ApiModelProperty("退货金额")
    private BigDecimal returnAmount;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("当月开始时间")
    private Date startDate;
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("当月结束时间")
    private Date endDate;

}
