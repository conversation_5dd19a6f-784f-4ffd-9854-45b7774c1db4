package com.zksr.trade.api.order.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("包裹信息响应对象")
public class TrdPackageRepDto {

    @ApiModelProperty(value = "包裹信息列表")
    private TrdPackageDto trdPackageList;
    
    @ApiModelProperty(value = "包裹明细列表")
    private List<TrdPackageDtlDto> trdPackageDtlList;

}

