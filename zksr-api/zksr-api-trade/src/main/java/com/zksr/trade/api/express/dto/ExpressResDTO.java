package com.zksr.trade.api.express.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月18日 09:54
 * @description: ExpressResDTO
 */
@Data
@ApiModel("快递物流信息实体")
public class ExpressResDTO {
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "用户 ID")
    private String EBusinessID;
    @ApiModelProperty(value = "订单编号")
    private String OrderCode;
    @ApiModelProperty(value = "快递公司编码")
    private String ShipperCode;
    @ApiModelProperty(value = "快递单号")
    private String LogisticCode;
    @ApiModelProperty(value = "用户自定义回传字段")
    private String Callback;
    @ApiModelProperty(value = "成功与否(true/false)")
    private String Success;
    @ApiModelProperty(value = "失败原因")
    private String Reason;
    @ApiModelProperty(value = " 物 流 状 态 ： 0-暂无轨迹信息 1-已揽收 2-在途中 3-签收 4-问题件")
    private String State;
    @ApiModelProperty(value = "增值物流状态：1-已揽收 （2-在途中 201-到达派件城市 202-派件中 211-已放入快递柜或驿站） （3-已签收 301-正常签收 302-派件异常后最终签收 304-代收签收 311-快递柜或驿站签收） （4-问题件 401-发货无信息 402-超时未签收 403-超时未更新 404-拒收(退件) 405-派件异常 406-退货签收 407-退货未签收 412-快递柜或驿站超时未取）")
    private String StateEx;
    @ApiModelProperty(value = "所在城市")
    private String Location;
    @ApiModelProperty(value = "轨迹")
    private List<Traces> Traces;
    @ApiModelProperty(value = "快递公司 字典值express_company")
    private String courierCompany;

    @Data
    private static class Traces{
        @ApiModelProperty(value = "轨迹发生时间 YYYY-MM-DD HH24:MM:SS")
        private String AcceptTime;
        @ApiModelProperty(value = "轨迹描述")
        private String AcceptStation;
        @ApiModelProperty(value = "当前城市")
        private String Location;
        @ApiModelProperty(value = "当前状态(同 StateEx)")
        private String Action;
        @ApiModelProperty(value = "备注")
        private String Remark;
    }
}
