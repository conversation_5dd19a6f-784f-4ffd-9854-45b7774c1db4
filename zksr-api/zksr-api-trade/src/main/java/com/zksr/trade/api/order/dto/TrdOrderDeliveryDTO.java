package com.zksr.trade.api.order.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年05月29日 09:13
 * @description: TrdOrderDeliveryDTO
 */
@ApiModel("入驻商订单操作信息返回对象")
@Data
@Accessors(chain = true)
public class TrdOrderDeliveryDTO {

    /** 操作时间 */
    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 当前状态 */
    @ApiModelProperty("当前状态")
    private Long state;

    /** 状态说明 */
    @ApiModelProperty("状态说明")
    private String status;

    /** 具体描述 */
    @ApiModelProperty("具体描述")
    private String describe;

    /** 排序号 */
    @ApiModelProperty("排序号")
    private Integer sort;





}
