package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
 * 统计订单总量出参
 *
 * <AUTHOR>
 *
 */
@Data
public class SupplierOrderCountAllRespVO {

    @ApiModelProperty("平台编号")
    private Long sysCode;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty("入驻商id")
    private String supplierId;

    @ApiModelProperty("订单创建时间")
    @JsonFormat(pattern = YYYY_MM_DD)
    private Date createDate;

    @ApiModelProperty("单据数量")
    private Long qty;

    /** saas租户编码 */
    @ApiModelProperty(value = "saas租户编码")
    private String saasTenantCode;

    /** 类型 */
    @ApiModelProperty(value = "类型")
    private int orderType;

    @Excel(name = "订单金额")
    private BigDecimal subOrderAmt;

}
