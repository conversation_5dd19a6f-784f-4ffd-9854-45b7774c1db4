package com.zksr.trade.api.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月20日 14:45
 * @description: OrderAfterDtlDTO
 */
@Data
public class OrderAfterDtlDTO {
    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "入驻商订单ID")
    private Long supplierOrderId;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "入驻商订单明细ID")
    private Long supplierOrderDtlId;

    @ApiModelProperty(value = "skuID")
    private Long skuId;

    @ApiModelProperty(value = "spuId")
    private Long spuId;

    @ApiModelProperty(value = "商品数量（下单单位）")
    private BigDecimal totalNum;

    @ApiModelProperty(value = "商品单价（下单单位）")
    private BigDecimal price;

    @ApiModelProperty(value = "商品金额")
    private BigDecimal totalAmt;

    @ApiModelProperty(value = "是否是赠品 1-是  0-否")
    private Integer giftFlag;

    @ApiModelProperty(value = "退货单位大小（下单单位） 1：小单位，2：中单位，3：大单位")
    private Integer unitType;

    @ApiModelProperty(value = "是否已推送ERP 0未推送 1已推送 2已接收")
    private Integer pushStatus;

    @ApiModelProperty(value = "退货单位编号（下单单位），取数据字典：sys_prdt_unit")
    private Long orderUnit;

    @ApiModelProperty(value = "退货数量（最小单位）")
    private BigDecimal minTotalNum;

    @ApiModelProperty(value = "单价（最小单位）")
    private BigDecimal minPrice;

    /** 最旧生产日期 */
    @ApiModelProperty("最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date oldestDate;

    /** 最新生产日期 */
    @ApiModelProperty("最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestDate;

    @ApiModelProperty(value = "订单销售数量（下单单位）")
    private Long orderTotalNum;


//    @ApiModelProperty(value = "退货单位编号（最小单位），取数据字典：sys_prdt_unit(下单单位)")
//    private Long minUnit;
}
