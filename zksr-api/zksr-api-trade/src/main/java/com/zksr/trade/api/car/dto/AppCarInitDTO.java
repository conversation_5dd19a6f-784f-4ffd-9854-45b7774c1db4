package com.zksr.trade.api.car.dto;

import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/3/27 16:16
 */
@Data
@ApiModel(description = "购物车初始化数据")
public class AppCarInitDTO extends AppCarIdDTO {

    @ApiModelProperty("购物车最小键")
    private Long trdCarId;

    @ApiModelProperty(value = "商品数量")
    private Integer productNum = 0;

    @ApiModelProperty(value = "是否选中, 0 否, 1 是")
    private Integer selected = 0;

    @ApiModelProperty(value = "是否业务员推荐, 0 否, 1 是")
    private Integer recommendFlag = 0;
}
