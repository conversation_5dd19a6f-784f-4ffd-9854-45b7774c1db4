package com.zksr.trade.api.driver.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/12/11 15:34
 * @注释
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("司机评价 - 新增评价VO")
public class DriverRatingReqVO {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /**
     * 平台商id
     */
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /**
     * 入驻商订单id
     */
    @ApiModelProperty(value = "入驻商订单id", required = true)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderId;

    /**
     * 评价维度1字典code;例：0
     */
    @ApiModelProperty(value = "评价维度1字典code;例：0")
    private String slot1Code;

    /**
     * 评价维度1字典值;例：司机态度
     */
    @ApiModelProperty(value = "评价维度1字典值;例：司机态度")
    private String slot1Val;

    /**
     * 评价维度1-得分字典;例：4
     */
    @ApiModelProperty(value = "评价维度1-得分字典;例：4")
    private Integer slot1ScoreCode;

    /**
     * 评价维度1-得分字典值;例：满意
     */
    @ApiModelProperty(value = "评价维度1-得分字典值;例：满意")
    private String slot1ScoreCodeVal;

    /**
     * 评价维度2字典code;例：1
     */
    @ApiModelProperty(value = "评价维度2字典code;例：1")
    private String slot2Code;

    /**
     * 评价维度2字典值;例：配送时效
     */
    @ApiModelProperty(value = "评价维度2字典值;例：配送时效")
    private String slot2Val;

    /**
     * 评价维度2-得分字典;例：5
     */
    @ApiModelProperty(value = "评价维度2-得分字典;例：5")
    private Integer slot2Score;

    /**
     * 评价维度2-得分字典值;例：非常满意
     */
    @ApiModelProperty(value = "评价维度2-得分字典值;例：非常满意")
    private String slot2ScoreCodeVal;

    /**
     * 评价维度3字典code;例：2
     */
    @ApiModelProperty(value = "评价维度3字典code;例：2")
    private String slot3Code;

    /**
     * 评价维度3字典值;例：商品完好
     */
    @ApiModelProperty(value = "评价维度3字典值;例：商品完好")
    private String slot3Val;

    /**
     * 评价维度3-得分;例：3
     */
    @ApiModelProperty(value = "评价维度3-得分;例：3")
    private Integer slot3Score;

    /**
     * 评价维度1-得分字典值;例：一般满意
     */
    @ApiModelProperty(value = "评价维度1-得分字典值;例：一般满意")
    private String scoreCodeVal;

    /**
     * 原因code;例：0,1
     */
    @ApiModelProperty(value = "原因code;例：0,1")
    private String reasonCode;

    /**
     * 低分原因code字典值;例：态度不好,送货不及时
     */
    @ApiModelProperty(value = "低分原因code字典值;例：态度不好,送货不及时")
    private String reasonVal;

    /**
     * 反馈信息;例：加油！努力！
     */
    @ApiModelProperty(value = "反馈信息;例：加油！努力！")
    private String fedbackMsg;

    /**
     * 反馈图片;例：
     */
    @ApiModelProperty(value = "反馈图片;例：")
    private String fedbackPics;

    /**
     * 司机ID
     */
    @ApiModelProperty(value = "司机ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long driverId;

}
