package com.zksr.trade.api.order.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;


/**
*
 * 订单结算对象DTO
* <AUTHOR>
* @date 2024/5/15 15:42
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TrdSettleDTO {
    /** 订单结算id */
    private Long settleId;

    /** 平台商id */
    private Long sysCode;

    /** 订单编号 */
    private String orderNo;

    /** 订单id */
    private Long orderId;

    /** 全国商品订单编号 */
    private String supplierOrderNo;

    /** 入驻商订单id */
    private Long supplierOrderId;

    /** 入驻商订单明细编号 */
    private String supplierOrderDtlNo;

    /** 入驻商订单明细id */
    private Long supplierOrderDtlId;

    /** 售后编号 */
    private String afterNo;

    /** 售后单id */
    private Long afterId;

    /** 结算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date settleTime;

    /** 结算状态 0=未结算，1=已结算 */
    private Integer state;

    /** 结算金额 */
    private BigDecimal settleAmt;

    /** 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店 */
    private String merchantType;

    /** 商户id */
    private Long merchantId;

    /** 入驻商id */
    private Long supplierId;

    /** 支付平台(数据字典);从订单表来 */
    private String platform;

    /** 结算比例（实际） */
    private BigDecimal settleRate;
}
