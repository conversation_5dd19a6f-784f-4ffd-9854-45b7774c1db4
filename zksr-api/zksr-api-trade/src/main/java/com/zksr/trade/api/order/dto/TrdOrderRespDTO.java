package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 订单查询返回对象
 * @Author: liuxingyu
 * @Date: 2024/3/30 10:26
 */
@ApiModel("订单返回对象")
@Data
@Accessors(chain = true)
public class TrdOrderRespDTO {
    /**
     * 订单id
     */
    @ApiModelProperty("订单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;

    /**
     * 运营商id
     */
    @ApiModelProperty("运营商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    /**
     * 门店id
     */
    @ApiModelProperty("门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    /**
     * 门店地址
     */
    @ApiModelProperty("门店地址")
    private String branchAddr;

    /**
     * 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消
     */
    @ApiModelProperty("支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消")
    private Integer payState;

    /**
     * 支付时间
     */
    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 优惠金额
     */
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmt;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String memo;

    /**
     * 支付金额
     */
    @ApiModelProperty("支付金额")
    private BigDecimal payAmt;

    /**
     * 单据金额
     */
    @ApiModelProperty("单据金额")
    private BigDecimal orderAmt;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 入驻商订单列表
     */
    @ApiModelProperty("入驻商订单列表")
    private List<TrdSupplierOrderDTO> supplierOrderList;

    /**
     * 订单状态
     */
    @ApiModelProperty("订单状态")
    private Integer deliveryState;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    private String branchName;

    /**
     * 门店联系人
     */
    @ApiModelProperty("门店联系人")
    private String contactName;

    /**
     * 门店联系电话
     */
    @ApiModelProperty("门店联系电话")
    private String contactPhone;

    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private String payWay;

    /**
     * 门店头像
     */
    @ApiModelProperty("门店头像")
    private String branchUrl;

    /** 生命周期阶段 */
    @ApiModelProperty("生命周期阶段")
    private Integer lifecycleStage;

    /**
     * 到期支付时间
     */
    @ApiModelProperty("到期支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirePayTime;

    @ApiModelProperty(value = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    @ApiModelProperty(value = "是否是业务员下单 1：是 0 否")
    private Integer colonelFlag;

    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "业务员头像")
    private String colonelAvatarImages;

    @ApiModelProperty(value = "司机ID")
    private Long driverId;

    /**
     * 司机评价状态 (0-未评价, 1-已评价)
     */
    @ApiModelProperty(value = "司机评价状态 (0-未评价, 1-已评价)")
    private Integer driverRatingFlag;

    /**
     * 司机评价ID
     */
    @ApiModelProperty(value = "司机评价ID")
    private Long driverRatingId;

    /**
     * 是否展示订单售后时间
     */
    @ApiModelProperty("是否展示订单售后时间(默认不显示). 0-不显示, 1-显示")
    private String showAfterHour;
    
    /**
     * 分销模式
     */
    @ApiModelProperty(value = "分销模式")
    private String distributionMode;

    @ApiModelProperty(value = "门店余额支付金额")
    private BigDecimal payBalanceAmt;
}
