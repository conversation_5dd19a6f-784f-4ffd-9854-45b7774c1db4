package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/4/28 10:11
 * 销售订单或者退货订单详情
 */
@Data
@ApiModel("业务员App - 销售订单或者退货订单详情")
public class TrdColonelAppOrderDetailRespVO {

    /** 商品SPU id */
    @Excel(name = "商品SPU id")
    @ApiModelProperty(value = "商品SPU id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    @Excel(name = "商品skuId")
    @ApiModelProperty(value = "商品skuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    /** 商品SPU名称 */
    @Excel(name = "商品SPU名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    /** 商品SPU编号 */
    @Excel(name = "商品SPU编号")
    @ApiModelProperty(value = "商品SPU编号", example = "示例值")
    private String spuNo;

    /** 国际条码 */
    @Excel(name = "国际条码")
    @ApiModelProperty(value = "国际条码")
    private String barcode;

    @Excel(name = "图片地址")
    @ApiModelProperty(value = "图片地址", example = "示例值")
    private String thumb;

    /** 商品数量 */
    @Excel(name = "商品数量")
    @ApiModelProperty(value = "商品数量")
    private BigDecimal totalNum;

    /** 合计金额（price*total_num） */
    @Excel(name = "合计金额", readConverterExp = "p=rice*total_num")
    @ApiModelProperty(value = "合计金额")
    private BigDecimal totalAmt;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmt;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "商品单价")
    private BigDecimal price;

    @ApiModelProperty(value = "商品结算（分佣）金额")
    private BigDecimal settleAmt;

    @ApiModelProperty(value = "入驻商订单Id")
    private Long supplierOrderDtlId;
}
