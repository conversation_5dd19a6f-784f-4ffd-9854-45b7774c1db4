package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@ApiModel("入驻商进件信息返回Dto")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class TrdSupplierResDto {

    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty(value = "入驻商商户编号")
    private String supplierAltMchNo;

    @ApiModelProperty(value = "入驻商订单编号")
    private String subOrderNo;

    @ApiModelProperty(value = "入驻商订单金额/商户金额")
    private BigDecimal subOrderAmt;

    @ApiModelProperty(value = "储值本金支付金额")
    private BigDecimal czPrincipalPayAmt;

    @ApiModelProperty(value = "储值赠金支付金额")
    private BigDecimal czGivePayAmt;

    @ApiModelProperty(value = "总订单支付金额")
    private BigDecimal payAmt;

    @ApiModelProperty(value = "入驻商订单手续费金额")
    private BigDecimal subPayFee;

    @ApiModelProperty(value = "入驻商订单ID")
    private Long supplierOrderId;

    @ApiModelProperty(value = "入驻商商品详情")
    private String itemInfo;

    @ApiModelProperty(value = "订单支付平台")
    private String platform;

    @ApiModelProperty(value = "商户ID")
    private Long merchantId;

    /**
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(value = "商户类型")
    private String merchantType;

    @ApiModelProperty(value = "订单ID")
    private Long orderId;
    
    
    /**
     * 分销模式
     */
    @ApiModelProperty(value = "分销模式")
    private String distributionMode;
    
}
