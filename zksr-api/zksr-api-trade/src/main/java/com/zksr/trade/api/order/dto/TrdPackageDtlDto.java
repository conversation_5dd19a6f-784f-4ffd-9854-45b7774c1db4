package com.zksr.trade.api.order.dto;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public  class TrdPackageDtlDto {
    @ApiModelProperty(value = "包裹明细ID")
    private Long packageDtlId;

    @ApiModelProperty(value = "平台商ID")
    private Long sysCode;

    @ApiModelProperty(value = "版本号")
    private Long version;

    @ApiModelProperty(value = "包裹ID")
    private Long packageId;

    @ApiModelProperty(value = "运单号")
    private String expressNo;

    @ApiModelProperty(value = "商品编码")
    private String productCode;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "商品数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "商品订单行号")
    private String orderLineNo;

    @ApiModelProperty(value = "套件标识 0散件 1套件")
    private Integer bomFlag;

    @ApiModelProperty(value = "子件商品名称")
    private String subItemName;

    @ApiModelProperty(value = "子件商品编码")
    private String subItemNo;

    @ApiModelProperty(value = "单位用量")
    private BigDecimal subQtyBom;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
} 