package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@ApiModel("活动报表 -- 客户使用活动订单信息汇总")
public class ActivityCustomerOrderUseTotalDTO {

    @ApiModelProperty(value = "活动ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long activityId;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "客户ID（门店）")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long customerId;

    @ApiModelProperty(value = "客户名称（门店）")
    private String customerName;

    @ApiModelProperty(value = "订单下单时间")
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS)
    private Date orderCreateTime;

    @ApiModelProperty(value = "客户参与活动总次数")
    private Long activityOrderCount;

    @ApiModelProperty(value = "客户活动参与订单下单总金额")
    private BigDecimal activityOrderAmtSum;


}
