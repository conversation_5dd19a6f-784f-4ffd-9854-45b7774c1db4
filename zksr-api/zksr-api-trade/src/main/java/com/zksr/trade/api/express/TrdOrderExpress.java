package com.zksr.trade.api.express;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 订单快递对象 trd_order_express
 *
 * <AUTHOR>
 * @date 2024-04-17
 */
@TableName(value = "trd_order_express")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdOrderExpress extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 订单快递id */
    @TableId(type= IdType.ASSIGN_ID)
    private Long orderExpressId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 订单id;订单id */
    @Excel(name = "订单id;订单id")
    private Long orderId;

    /** 订单编号;订单编号 */
    @Excel(name = "订单编号;订单编号")
    private String orderNo;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 快递单号 */
    @Excel(name = "快递单号")
    private String expressNo;

    /** 快递公司 */
    @Excel(name = "快递公司")
    private String expressCom;

    /** 快递公司编码 */
    @Excel(name = "快递公司编码")
    private String expressComNo;

    /** 收货人 */
    @Excel(name = "收货人")
    private String receiveMan;

    /** 收货人电话 */
    @Excel(name = "收货人电话")
    private String receivePhone;

    /** 收货地址 */
    @Excel(name = "收货地址")
    private String address;

    /** 最新物流轨迹 */
    @Excel(name = "最新物流轨迹")
    private String latestInfo;

    /** 快递导入明细id */
    @Excel(name = "快递导入明细id")
    private Long expressImportDtlId;

    /** 真实发货时间(快递开始运输时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "真实发货时间(快递开始运输时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date realInTransitTime;

    /** 物流状态(数据字典);0-暂无轨迹信息 1-已揽收 2-在途中 3-签收 4-问题件 */
    @Excel(name = "物流状态(数据字典);0-暂无轨迹信息 1-已揽收 2-在途中 3-签收 4-问题件")
    private Long state;

}
