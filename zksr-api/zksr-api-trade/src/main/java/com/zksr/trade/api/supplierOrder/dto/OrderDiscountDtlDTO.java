package com.zksr.trade.api.supplierOrder.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;

/**
 * 订单优惠明细对象 trd_order_discount_dtl
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Data
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class OrderDiscountDtlDTO{
    private static final long serialVersionUID=1L;

    /** 主键id */
    @Excel(name = "主键id")
    private Long orderDiscountDtlId;

    /** 平台商id */
    @Excel(name = "平台商id")
    private Long sysCode;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 门店id */
    @Excel(name = "门店id")
    private Long branchId;

    /** 商品sku id */
    @Excel(name = "商品sku id")
    private Long skuId;

    /** 入驻商订单明细id */
    @Excel(name = "入驻商订单明细id")
    private Long supplierOrderDtlId;

    /** 入驻商订单明细编号 */
    @Excel(name = "入驻商订单明细编号")
    private String supplierOrderDtlNo;

    /** 优惠类型（数据字典） */
    @Excel(name = "优惠类型", readConverterExp = "数=据字典")
    private String discountType;

    /** 优惠id */
    @Excel(name = "优惠id")
    private Long discountId;

    /** 活动优惠金额(分摊的)(new) */
    @Excel(name = "活动优惠金额(分摊的)(new)")
    private BigDecimal activityDiscountAmt;

    /** 优惠金额(分摊的)(new) */
    @Excel(name = "优惠金额(分摊的)(new)")
    private BigDecimal couponDiscountAmt;

    /** 优惠金额(不分摊的)(new) */
    @Excel(name = "优惠金额(不分摊的)(new)")
    private BigDecimal couponDiscountAmt2;

    /** 赠品类型;0-商品 1-优惠券 */
    @Excel(name = "赠品类型;0-商品 1-优惠券")
    private Long giftType;

    /** 赠品sku;gift_type=0 则记录;gift_type=1 则记录 */
    @Excel(name = "赠品sku;gift_type=0 则记录;gift_type=1 则记录")
    private Long giftSkuId;

    /** 赠品sku优惠券模板 */
    @Excel(name = "赠品sku优惠券模板")
    private Long giftCouponTemplateId;

    /** 赠品数量 */
    @Excel(name = "赠品数量")
    private Long giftQty;

    /** 入驻商订单id */
    @Excel(name = "入驻商订单id")
    private Long supplierOrderId;

    /**
     * 活动优惠规则ID
     */
    private Long discountRuleId;

    @Excel(name = "优惠券模板ID ,用于优惠劵报表")
    private Long couponTemplateId;

}
