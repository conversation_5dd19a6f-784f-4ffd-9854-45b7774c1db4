package com.zksr.trade.api.supplierAfter;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.vo.openapi.AfterDetailPushDTO;
import com.zksr.common.core.domain.vo.openapi.AfterPushDTO;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 交易模块订单跨服务调用
 */
@FeignClient(
        contextId = "remoteSupplierAfterApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface SupplierAfterApi {
    String PREFIX = ApiConstants.PREFIX + "/supplierAfter";

    /**
     * OpenApi 销售/拒收退货回传回传
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/receiveSalesReturn")
    CommonResult<Boolean> receiveSalesReturn (@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                              @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                              @RequestBody AfterSalesReturnVO vo);

    /**
     * OpenApi 订单收货回传（接收拒收退货单）
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/receiveOrderTakeDelivery")
    CommonResult<Boolean> receiveOrderTakeDelivery(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                                   @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                                   @RequestBody ReceiveOrderTakeDeliveryVO vo);

    @GetMapping(value =PREFIX + "/getSupplierAfterListByAfterNo")
    CommonResult<List<AfterPushDTO>> getSupplierAfterListByAfterNo(@RequestParam("afterNo") String afterNo);

    @GetMapping(value =PREFIX + "/getSupAfterBySupAfterNo")
    CommonResult<AfterPushDTO> getSupAfterBySupAfterNo(@RequestParam("supplierAfterNo") String supplierAfterNo);

    @GetMapping(value =PREFIX + "/getSupplierAfterListBySupplierAfterNo")
    CommonResult<List<AfterDetailPushDTO>> getSupplierAfterListBySupplierAfterNo(@RequestParam("supplierAfterNo") String supplierAfterNo);

    @PostMapping(value =PREFIX + "/getSupplierAfterDtlListByIds")
    CommonResult<List<AfterDetailPushDTO>> getSupplierAfterDtlListByIds(@RequestBody List<Long> ids);

    /**
     * 退单接收成功通知
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/afterOrderReceiveCallback")
    CommonResult<Boolean> afterOrderReceiveCallback(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                                    @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                                    @RequestBody AfterOrderReceiveCallbackVO vo);

    /**
     * 售后订单推送成功后处理
     * @param data
     * @return
     */
    @PostMapping(value =PREFIX + "/syncAfterResponseSuccess")
    CommonResult<Boolean> syncAfterResponseSuccess(@Valid @RequestBody SyncDataDTO data);

    /**
     * 退货确认前取消(售后取消)
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/afterCancel")
    CommonResult<Boolean> afterCancel(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                      @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                      @RequestBody AfterCancelVO vo);
}
