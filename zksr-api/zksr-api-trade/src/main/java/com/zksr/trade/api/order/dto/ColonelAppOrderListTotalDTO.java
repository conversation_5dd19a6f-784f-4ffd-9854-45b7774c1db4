package com.zksr.trade.api.order.dto;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.order.vo.TrdColonelAppOrderListRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("业务员app-订单列表汇总")
public class ColonelAppOrderListTotalDTO {

    @ApiModelProperty(value = "数据总数")
    private Long numTotal;

    @ApiModelProperty(value = "数据总金额")
    private BigDecimal amtTotal;

    @ApiModelProperty(value = "订单分页数据")
    private List<?> list;

    @ApiModelProperty(value = "总数")
    private Long total;
}
