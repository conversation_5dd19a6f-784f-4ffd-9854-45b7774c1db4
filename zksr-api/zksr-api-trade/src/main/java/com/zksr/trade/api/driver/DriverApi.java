package com.zksr.trade.api.driver;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.api.driver.dto.DriverDTO;
import com.zksr.trade.api.driver.dto.DriverRatingDTO;
import com.zksr.trade.api.driver.form.TrdDriverImportForm;
import com.zksr.trade.api.driver.vo.DriverRatingReqVO;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2024/12/11 15:29
 * @司机相关rpc调用
 */
@FeignClient(
        contextId = "remoteDriverApi",
        value = ApiConstants.NAME
)
public interface DriverApi {

    String PREFIX = ApiConstants.PREFIX + "/driver";

    /**
     * 保存司机评价
     *
     * @param DriverRatingReqVO
     * @return
     */
    @PostMapping(value = PREFIX + "/saveDriverRating")
    CommonResult<Boolean> saveDriverRating(@RequestBody DriverRatingReqVO DriverRatingReqVO);

    /**
     * 获取司机信息
     *
     * @param driverId
     * @return
     */
    @PostMapping(value = PREFIX + "/getDriverInfo")
    CommonResult<DriverDTO> getDriverInfo(@RequestParam("driverId") Long driverId);

    /**
     * 获取司机评价信息
     *
     * @param driverRatingId
     * @return
     */
    @PostMapping(value = PREFIX + "/getDriverRatingInfo")
    CommonResult<DriverRatingDTO> getDriverRatingInfo(@RequestParam("driverRatingId") Long driverRatingId);

    @PostMapping(value = PREFIX + "/importDataEvent")
    CommonResult<String> importDataEvent(@RequestBody TrdDriverImportForm form);

}
