package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("门店订单销售数据 -- ES门店月订单销售数据汇总")
public class EsBranchOrderSalesRespDTO {

    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("平台商id")
    private Long sysCode;

    @ApiModelProperty("数据创建年月（yyyy-MM）")
    private String createYearMonth;

    @ApiModelProperty("本月订单笔数")
    private Long orderQtyCount;

    @ApiModelProperty("本月订单金额")
    private BigDecimal orderAmtSum;


    @ApiModelProperty(value = "最近一次订货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastBuyTime;

    @ApiModelProperty("最近一次订货金额")
    private BigDecimal lastBuyAmt;


    @ApiModelProperty(value = "上次访问系统时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastAccessSystemTime;

    /** 用户最近登陆时间 */
    @ApiModelProperty(value = "最近登陆时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    /** ------业务员签到------*/

    /** 业务员最近一次拜访时间 */
    @ApiModelProperty(value = "最近一次拜访时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastVisitTime;
}
