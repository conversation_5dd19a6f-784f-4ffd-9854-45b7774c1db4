package com.zksr.trade.api.invoice;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.system.api.invoice.dto.InvoiceCallbackDTO;
import com.zksr.trade.api.invoice.dto.InvoiceProcessDTO;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
        contextId = "remoteInvoiceApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
@SuppressWarnings("all")
public interface InvoiceApi {

    final String PREFIX = ApiConstants.PREFIX + "/invoice";

    /**
     * 开票接口
     *
     * @param invoiceProcessDTO
     * @return
     */
    @PostMapping(value = PREFIX + "/blueInvoiceProcess")
    public CommonResult<Boolean> blueInvoiceProcess(@RequestBody InvoiceProcessDTO invoiceProcessDTO);


    /**
     * 开票回调
     *
     * @param invoiceCallbackDTO
     * @return
     */

    @PostMapping(value = PREFIX + "/invoiceCallback")
    public CommonResult<Boolean> invoiceCallback(@RequestBody InvoiceCallbackDTO invoiceCallbackDTO);


}
