package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
@Data
@NoArgsConstructor
@ApiModel("业务员日结信息")
public class ColonelOrderDaySettleDTO {

    /** 平台商id */
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /** 结算创建日期 */
    @ApiModelProperty("结算创建日期")
    private Date settleCreateDate;

    /** 业务员ID */
    @ApiModelProperty("业务员ID")
    private Long colonelId;

    /** 下单数量（客户下单） */
    @ApiModelProperty(" 下单数量（客户下单）")
    private Long orderQty;

    /** 门店下单金额 */
    @ApiModelProperty("门店下单金额")
    private BigDecimal branchOrderAmt;

    /** 下单数量（业务员代客下单） */
    @ApiModelProperty("下单数量（业务员代客下单）")
    private Long businessOrderQty;

    /** 业务下单金额（业务员代客下单） */
    @ApiModelProperty("业务下单金额（业务员代客下单）")
    private BigDecimal businessOrderAmt;

    /** 业务提成金额 */
    @ApiModelProperty("业务提成金额")
    private BigDecimal percentageAmt;

    /** 门店退货金额 */
    @ApiModelProperty("门店退货金额")
    private BigDecimal branchRefundAmt;

    /** 拜访门店数量 */
    @ApiModelProperty("拜访门店数量")
    private Long visitQty;

    /** 拓店数量 */
    @ApiModelProperty("拓店数量")
    private Long addBranchQty;

    /** 动销门店数量 */
    @ApiModelProperty("动销门店数量")
    private Long saleBranchQty;


    @JsonIgnore
    public String getMapKey() {
        return StringUtils.format("{}_{}", colonelId, DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, settleCreateDate));
    }
}
