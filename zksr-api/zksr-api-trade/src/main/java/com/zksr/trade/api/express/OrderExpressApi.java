package com.zksr.trade.api.express;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.api.order.dto.TrdOrderResDto;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2024年04月18日 10:52
 * @description: 订单快递信息Api
// */
@FeignClient(
        contextId = "remoteExpressApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface OrderExpressApi {
    String PREFIX = ApiConstants.PREFIX + "/express";

    /**
     * 保存订单
     * @param phone 收件人手机号
     * @param courierCompanyNo 快递公司编码
     * @param courierNumber 快递单号
     * @return
     */
    @GetMapping(value =PREFIX + "/getExpressInfoByCourier")
    public CommonResult<ExpressResDTO> getExpressInfoByCourier(@RequestParam(value = "phone") String phone,
                                                               @RequestParam(value = "courierCompanyNo") String courierCompanyNo,
                                                               @RequestParam(value = "courierNumber") String courierNumber);
}
