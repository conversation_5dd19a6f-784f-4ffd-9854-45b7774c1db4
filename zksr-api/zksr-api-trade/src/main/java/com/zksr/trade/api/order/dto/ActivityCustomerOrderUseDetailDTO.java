package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel("活动报表 -- 客户使用活动订单商品明细")
public class ActivityCustomerOrderUseDetailDTO {
    @ApiModelProperty(value = "活动ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long activityId;

    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "spuId")
    private Long spuId;

    @ApiModelProperty(value = "商品编号")
    private String spuNo;

    @ApiModelProperty(value = "商品图片")
    private String spuUrl;

    @ApiModelProperty(value = "商品名称")
    private String spuName;

    @ApiModelProperty(value = "品牌ID")
    private Long brandId;

    @ApiModelProperty(value = "品牌名称")
    private String brandName;

    @ApiModelProperty(value = "管理分类ID")
    private Long catgoryId;

    @ApiModelProperty(value = "管理分类名称")
    private String catgoryName;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "单位")
    private Long orderUnit;

    @ApiModelProperty(value = "单位购买数量")
    private Long orderUnitQty;

    @ApiModelProperty(value = "单位购买金额（商品实际应支付金额）")
    private BigDecimal orderUnitAmt;
}
