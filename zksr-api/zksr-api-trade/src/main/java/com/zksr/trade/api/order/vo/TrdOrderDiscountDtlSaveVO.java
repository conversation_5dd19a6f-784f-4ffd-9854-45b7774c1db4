package com.zksr.trade.api.order.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年05月14日 15:06
 * @description: TrdOrderDiscountDtlSaveVO
 */
@Data
@ApiModel("订单优惠信息 - 保存订单优惠Request VO")
public class TrdOrderDiscountDtlSaveVO {


    /** 门店id */
    private Long branchId;

    /** 商品sku id */
    private Long skuId;

    /** 入驻商订单明细编号 */
    private String supplierOrderDtlNo;

    /** 优惠类型（数据字典）trd_discount_type */
    private String discountType;

    /** 优惠id */
    private Long discountId;

    /** 活动优惠金额(分摊的)(new) */
    private BigDecimal activityDiscountAmt;

    /** 优惠金额(分摊的)(new) */
    private BigDecimal couponDiscountAmt;

    /** 优惠金额(不分摊的)(new) */
    private BigDecimal couponDiscountAmt2;

    /** 赠品类型;0-商品 1-优惠券 */
    private Long giftType;

    /** 赠品sku;gift_type=0 则记录;gift_type=1 则记录 */
    private Long giftSkuId;

    /** 赠品sku优惠券模板 */
    private Long giftCouponTemplateId;

    /** 赠品数量 */
    private Long giftQty;

    /** 赠品单位大小 */
    private Integer giftUnitType;

    /** 赠品单位 */
    private String giftUnit;

    /** 赠品单位换算数量 */
    private BigDecimal giftUnitSize ;


    /** 订单单号  用于优惠劵核销*/
    private String orderNo;

    /** 入驻商订单单号 用于绑定是那一条活动使用的促销 */
    private String supplierOrderNo;

    /**
     * 入驻商ID
     */
    private Long supplierId;
    /**
     * 优惠规则ID 用于活动加减库存使用
     */
    private Long discountRuleId;

    /**
     * 秒杀、特价数量 用于活动加减库存使用
     */
    private Integer spSkCount;

    /**
     * 秒杀、特价单位换算数量 用于活动加减库存使用
     */
    private BigDecimal spSkUnitSizeQty;

    /**
     * 优惠券模板ID ,用于优惠劵报表
     */
    private Long couponTemplateId;

    /**
     * 赠送优惠劵；赠品优惠时使用，存放参与此赠品优惠的订单明细编号对应的行号，多个以;分号分隔
     */
    private String orderDtlNumStr;

    /**
     * 活动满足条件
     * 满足多少金额或数量参与促销 赠送优惠劵（目前只有满赠、买赠使用）
     */
    private String discountCondition;

    /**
     * 优惠名称
     */
    private String discountName;

    /** 单位 1：小单位 2：中单位 3：大单位 */
    private Integer spSkUnitSize;
}
