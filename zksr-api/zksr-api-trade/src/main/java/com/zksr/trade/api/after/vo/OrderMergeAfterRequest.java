package com.zksr.trade.api.after.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.constant.SheetTypeConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月20日 10:07
 * @description: OrderMergeAfterRequest
 */
@Data
@ApiModel("订单合单售后实体 请求")
public class OrderMergeAfterRequest {
    @ApiModelProperty(value = "售后类型", required = true)
    private Long afterType;

    @ApiModelProperty(value = "退款原因", required = true)
    private String reason;

    @ApiModelProperty(value = "申请凭证图片")
    private String applyImgs;

    @ApiModelProperty(value = "数据来源（1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序）,5-第三方系统", hidden = true)
    private Long source;

    @ApiModelProperty(value = "入驻商收货手机号")
    private String returnPhone;

    @ApiModelProperty(value = "入驻商收货地址")
    private String returnAddr;

    @ApiModelProperty(value = "状态", required = true)
    private Long deliveryState;

    @ApiModelProperty(value = "入驻商id", required = true)
    private Long supplierId;

    @ApiModelProperty(value = "门店id", required = true)
    private Long branchId;


    @ApiModelProperty(value = "入驻商订单明细项数组")
    private List<SupplierOrderDtl> supplierOrderDtls;

    @ApiModel(value = "入驻商订单明细项")
    @Data
    public static class SupplierOrderDtl {

        @ApiModelProperty(value = "订单ID", required = true)
        private Long orderId;

        @ApiModelProperty(value = "入驻商订单ID" , required = true)
        private Long supplierOrderId;

        @ApiModelProperty(value = "入驻商订单明细ID" , required = true)
        private Long supplierOrderDtlId;

        @ApiModelProperty(value = "商品可退数量" , required = true)
        private Long totalNum;

        @ApiModelProperty(value = "退货商品单价" , required = true)
        private BigDecimal refundPrice;

        @ApiModelProperty(value = "退货商品数量", required = true)
        private Long refundQty;

        @ApiModelProperty(value = "退货商品金额", required = true)
        private BigDecimal refundAmt;

        @ApiModelProperty(value = "退货单位大小 1：小单位，2：中单位，3：大单位", required = true)
        private Integer unitType;
    }
}
