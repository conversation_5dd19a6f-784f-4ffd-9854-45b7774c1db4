package com.zksr.trade.api.orderSettle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员提成统计
 * @date 2024/9/27 10:48
 */
@ApiModel(description = "业务员浮动提成统计")
@Data
public class ColonelFloatSettleTotalReqVO {

    @ApiModelProperty(value = "统计开始时间(yyyy-MM-dd HH:mm:ss)", required = true)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date startTime;

    @ApiModelProperty(value = "统计结束时间(yyyy-MM-dd HH:mm:ss)", required = true)
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    private Date endTime;

    @ApiModelProperty(value = "业务员ID", hidden = true)
    private Long colonelId;
}
