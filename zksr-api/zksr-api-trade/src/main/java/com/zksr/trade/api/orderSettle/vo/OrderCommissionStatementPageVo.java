package com.zksr.trade.api.orderSettle.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.pojo.PageParam;
import com.zksr.system.api.model.LoginUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2024/10/30 14:32
 * @订单分佣对账单
 */
@Data
@ApiModel("订单结算 - 订单分佣对账单 req VO")
public class OrderCommissionStatementPageVo extends PageParam {
    private static final long serialVersionUID = 1L;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @ApiModelProperty(value = "入驻商id")
    private Long supplierId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 子订单号(入驻商订单号) */
    @Excel(name = "子订单号(入驻商订单号)")
    @ApiModelProperty(value = "子订单号(入驻商订单号)")
    private String supplierOrderNo;

    /** 售后单号 */
    @Excel(name = "售后单号")
    @ApiModelProperty(value = "售后单号")
    private String afterNo;

    /** 订单类型 */
    @Excel(name = "订单类型")
    @ApiModelProperty(value = "订单类型")
    private String orderType;

    /** 分账状态 */
    @Excel(name = "分账状态")
    @ApiModelProperty(value = "分账状态")
    private String settleStatus;

    /** 支付渠道 */
    @Excel(name = "支付渠道")
    @ApiModelProperty(value = "支付渠道")
    private String platform;

    /** 平台商id */
    @Excel(name = "平台商id")
    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "登录信息")
    private LoginUser loginUser;

        /** 登录入驻商id */
    @Excel(name = "登录入驻商id")
    @ApiModelProperty(value = "登录入驻商id")
    private Long loginSupplierId;

    /** 登录运营商id */
    @Excel(name = "登录运营商id")
    @ApiModelProperty(value = "登录运营商id")
    private Long loginDcId;

    /** 是否导出 */
    @Excel(name = "是否导出")
    @ApiModelProperty(value = "是否导出")
    private Integer isExport;

    /** 订单结算开始时间 */
    @Excel(name = "订单结算开始时间")
    @ApiModelProperty(value = "订单结算开始时间")
    private String orderSettleStartTime;

    /** 订单结算结束时间 */
    @Excel(name = "订单结算结束时间")
    @ApiModelProperty(value = "订单结算结束时间")
    private String orderSettleEndTime;

    /** 订单创建开始时间 */
    @Excel(name = "订单创建开始时间")
    @ApiModelProperty(value = "订单创建开始时间")
    private String orderCreateStartTime;

    /** 订单创建结束时间 */
    @Excel(name = "订单创建结束时间")
    @ApiModelProperty(value = "订单创建结束时间")
    private String orderCreateEndTime;

    /**
     * 订单状态
     */
    @Excel(name = "订单状态")
    @ApiModelProperty(value = "订单状态")
    private String deliveryState;

    /**
     * 门店ID
     */
    @Excel(name = "门店ID")
    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;



}
