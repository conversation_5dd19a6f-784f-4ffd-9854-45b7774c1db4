package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("我的订单状态 数字角标VO")
public class OrderStatusVO {

    @ApiModelProperty("待付款")
    private Integer pendingPayment;

    @ApiModelProperty("待发货")
    private Integer toBeDelivered;

    @ApiModelProperty("待收货")
    private Integer awaitingReceipt;

    @ApiModelProperty("已收货")
    private Integer received = 0;

    @ApiModelProperty("售后")
    private Integer afterSales;

}
