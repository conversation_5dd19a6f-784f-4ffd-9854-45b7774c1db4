package com.zksr.trade.api.hdfk.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店货到付款查询请求 - Req VO
 * @date 2024/5/28 15:58
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "门店货到付款查询请求")
public class BranchHdfkReqVO extends PageParam {

    @ApiModelProperty(value = "门店ID", hidden = true)
    private Long branchId;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long memberId;

    @ApiModelProperty(value = "搜索条件")
    private String condition;

    @ApiModelProperty(value = "订单号")
    private String orderNo;
}
