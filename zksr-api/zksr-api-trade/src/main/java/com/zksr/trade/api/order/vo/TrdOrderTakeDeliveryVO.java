package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月11日 09:39
 * @description: TrdOrderTakeDeliveryVO
 */
@ApiModel("订单操作定时任务实体")
@Data
public class TrdOrderTakeDeliveryVO {
    @ApiModelProperty("平台商ID")
    private Long sysCode;

    @ApiModelProperty("运营商ID")
    private Long dcId;

    @ApiModelProperty("定时操作时间")
    private Integer operDate;

    @ApiModelProperty("定时操作时间类型  0：小时 1：分钟")
    private String operDateType;

    @ApiModelProperty("商品状态 枚举：DeliveryStatusEnum")
    private Long deliveryState;

    @ApiModelProperty("当前时间")
    private Date nowDate;

    @ApiModelProperty("商品类型： 0.全国 1：本地")
    private Integer itemType;
}
