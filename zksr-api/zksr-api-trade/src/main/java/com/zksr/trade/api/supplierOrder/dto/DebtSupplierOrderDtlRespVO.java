package com.zksr.trade.api.supplierOrder.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *  @description: PC端订单明细查询页面返回实体
 */
@ApiModel("PC端订单明细查询页面返回实体")
@Data
public class DebtSupplierOrderDtlRespVO {

    @Excel(name = "产品名称")
    @ApiModelProperty("产品名称")
    private String spuName;

    @Excel(name = "金额")
    @ApiModelProperty("金额（订单发货金额）")
    private BigDecimal demandAmt;


    @Override
    public String toString() {
        return "{" +
                "商品名称='" + spuName + '\'' +
                ",金额=" + demandAmt +
                '}';
    }
}
