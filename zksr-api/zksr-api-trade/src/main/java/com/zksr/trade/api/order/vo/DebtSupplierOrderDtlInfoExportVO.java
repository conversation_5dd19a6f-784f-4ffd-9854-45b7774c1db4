package com.zksr.trade.api.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

@Data
@Accessors(chain = true)
@ApiModel("入驻商欠款订单商品明细导出实体")
public class DebtSupplierOrderDtlInfoExportVO extends BaseEntity {

    @Excel(name = "订单号", needMerge = true)
    @ApiModelProperty(value = "订单编号")
    private String orderNo;


    @Excel(name = "下单时间")
    @ApiModelProperty("下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;

    @Excel(name = "子订单号", needMerge = true)
    @ApiModelProperty(value = "入驻商订单编号")
    private String supplierOrderNo;

    @Excel(name = "外部订单号")
    @ApiModelProperty(value = "外部订单号")
    private String sourceOrderNo;

    @Excel(name = "入驻商名称")
    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @Excel(name = "单据类型")
    @ApiModelProperty("订单的订单类型")
    private String orderType;

    @Excel(name = "状态")
    @ApiModelProperty(value = "状态")
    private String errorState = "正常";

    @Excel(name = "门店名称")
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @Excel(name = "产品名称")
    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    @Excel(name = "单位")
    @ApiModelProperty(value = "单位")
    private String saleUnit;

    @Excel(name = "单价")
    @ApiModelProperty(value = "商品销售价（优惠后平均单价）")
    private BigDecimal price;

    @Excel(name = "要货数量")
    @ApiModelProperty("要货数量")
    private Long demandNum;

    @Excel(name = "金额")
    @ApiModelProperty(value = "金额")
    private BigDecimal demandAmt;

    @Excel(name = "合计金额")
    @ApiModelProperty("合计金额")
    private BigDecimal saleTotalAmt;

    @Excel(name = "优惠金额")
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmt;

    @Excel(name = "实际支付金额")
    @ApiModelProperty("实际支付金额")
    private BigDecimal payTotalAmt;

    @ApiModelProperty(value = "订单状态")
    @Excel(name = "订单状态")
    private String deliveryState;

    @Excel(name = "支付状态")
    @ApiModelProperty(value = "支付状态")
    private String payState;

    @ApiModelProperty("付款方式")
    @Excel(name = "付款方式")
    private String payWay;

    @ApiModelProperty("业务员")
    @Excel(name = "业务员")
    private String colonelName;

    @ApiModelProperty("业务经理")
    @Excel(name = "业务经理")
    private String pcolonelName;

    @ApiModelProperty("门店地址")
    @Excel(name = "门店地址")
    private String branchAddr;

}
