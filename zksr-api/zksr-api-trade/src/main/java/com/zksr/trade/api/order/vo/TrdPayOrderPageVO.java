package com.zksr.trade.api.order.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.PayOrderStatusRespEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel("订单支付信息 VO")
public class TrdPayOrderPageVO {
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 支付状态
     *
     * 枚举：{@link PayOrderStatusRespEnum}
     */
    @ApiModelProperty(value = "支付状态 0-未支付,1-支付发起成功,2-支付成功(针对储值和模拟支付方式),3-支付失败,4-已退款,5-支付关闭")
    private Integer status;

    @ApiModelProperty("返回处理信息, status=3 返回原因")
    private String message;

    @ApiModelProperty(value = "外部订单号, 第三方系统支付流水号")
    private String outTradeNo;

    @ApiModelProperty(value = "支付成功时间")
    private Date successTime;

    /**
     * 支付平台标识 com.zksr.account.client.PayClient#getPlatform()
     */
    @ApiModelProperty("支付平台标识 hlb-合利宝 wallet-钱包(余额) mock-模拟支付")
    private String payPlatform;

    @Excel(name = "支付方式, 0-在线支付 1-储值支付, 2-模拟支付")
    private String payWay;

    @ApiModelProperty(value = "有效返回的数据")
    private Object rawData;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payAmt;
}
