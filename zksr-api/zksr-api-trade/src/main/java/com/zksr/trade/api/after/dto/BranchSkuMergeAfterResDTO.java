package com.zksr.trade.api.after.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.trade.api.order.dto.TrdOrderDiscountDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年1月13日 14:08
 * @description: branchSkuMergeAfterResDTO
 */
@Data
@ApiModel("获取门店合单售后商品列表 返回")
@Accessors(chain = true)
public class BranchSkuMergeAfterResDTO {

    @ApiModelProperty(value = "订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;

    @ApiModelProperty("下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "门店ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty(value = "入驻商订单ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderId;

    @ApiModelProperty(value = "入驻商订单明细ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderDtlId;

    @ApiModelProperty(value = "spuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty(value = "skuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty("最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date oldestDate;

    @ApiModelProperty("最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestDate;

    /**
     * 状态  {@link com.zksr.common.core.enums.DeliveryStatusEnum}
     */
    @ApiModelProperty("商品状态")
    private Long deliveryState;

    @ApiModelProperty(value = "是否是赠品 1-是  0-否")
    private Integer giftFlag;

    @ApiModelProperty(value = "订单商品类型 0：全国  1：本地")
    private Long orderType;

    @ApiModelProperty(value = "售后单位大小（下单单位） 1：小单位，2：中单位，3：大单位")
    private Integer orderUnitType;

    @ApiModelProperty(value = "售后单位编号（下单单位），取数据字典：sys_prdt_unit")
    private Long orderUnit;

    @ApiModelProperty(value = "可售后数量（下单单位）")
    private Long totalNum;

    @ApiModelProperty(value = "售后单价（下单单位）")
    private BigDecimal price;

    @ApiModelProperty(value = "可售后数量（最小单位）")
    private Long minTotalNum;

    @ApiModelProperty(value = "售后单价（最小单位）")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "退货单位编号（最小单位），取数据字典：sys_prdt_unit")
    private Long minUnit;

    @ApiModelProperty(value = "可退金额")
    private BigDecimal totalAmt;

    //==================================非数据库表存储字段=====================================

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "商品SPU名称")
    private String spuName;

    @ApiModelProperty(value = "商品SPU编号")
    private String spuNo;

    @ApiModelProperty(value = "商品条码")
    private String barcode;

    @ApiModelProperty(value = "图片地址")
    private String thumb;



}
