package com.zksr.trade.api.order.vo;

import com.zksr.common.core.enums.SourceType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年04月23日 08:49
 * @description: AfterDtlReqVO
 */
@ApiModel("售后单 - trd_after明细 Request VO")
@Data
public class AfterDtlReqVO {
    @ApiModelProperty(value = "单据类型")
    private Long productType;

    @ApiModelProperty(value = "处理中状态{0：(处理中状态全部)，1：待处理， 2：未退货，3：退货中，4：已退货，5：待退款，6：退款中，7：退款失败, 8:已处理（完成）}")
    private Long handleState;

    @ApiModelProperty(value = "入驻商ID", hidden = true)
    private Long supplierId;

    @ApiModelProperty(value = "售后订单ID")
    private Long afterId;

    @ApiModelProperty(value = "数据来源（1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序）,5-第三方系统", hidden = true)
    private Long source = SourceType.SHOPPINGMALL.getType();
}
