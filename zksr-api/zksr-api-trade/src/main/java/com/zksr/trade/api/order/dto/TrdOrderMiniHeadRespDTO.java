package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 小程序订单查询返回对象
 * @Author: chenmingqing
 * @Date: 2025/3/25 10:26
 */
@ApiModel("小程序订单查询返回对象")
@Data
@Accessors(chain = true)
public class TrdOrderMiniHeadRespDTO {

    @ApiModelProperty("订单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("运营商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    @ApiModelProperty("用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    @ApiModelProperty("门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    /**
     * 支付状态 {@link com.zksr.common.core.enums.PayStateEnum}
     */
    @ApiModelProperty("支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消")
    private Integer payState;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmt;

    @ApiModelProperty("备注")
    private String memo;

    @ApiModelProperty("支付金额")
    private BigDecimal payAmt;

    @ApiModelProperty("单据金额")
    private BigDecimal orderAmt;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("订单状态")
    private Integer deliveryState;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("支付方式")
    private String payWay;

    @ApiModelProperty("门店头像")
    private String branchUrl;

    @ApiModelProperty("到期支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirePayTime;

    @ApiModelProperty(value = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    @ApiModelProperty(value = "是否是业务员下单 1：是 0 否")
    private Integer colonelFlag;

    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "业务员头像")
    private String colonelAvatarImages;

    @ApiModelProperty("是否展示订单售后时间(默认不显示). 0-不显示, 1-显示")
    private String showAfterHour;

    @ApiModelProperty("入驻商订单列表")
    private List<SupplierOrderDTO> supplierOrderList;

    @ApiModelProperty("是否支持在线收款 0：否，1：是")
    private Integer isPayOnline;

    @ApiModel(value = "入驻商订单")
    @Data
    public static class SupplierOrderDTO {

        @ApiModelProperty("入驻商id")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long supplierId;


        @ApiModelProperty("入驻商名称")
        private String supplierName;

        @ApiModelProperty("入驻商头像")
        private String avatar;

        @ApiModelProperty("入驻商订单id")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long supplierOrderId;

        @ApiModelProperty("入驻商订单编号")
        private String supplierOrderNo;

        @ApiModelProperty("入驻商订单金额")
        private BigDecimal subAmt;

        @ApiModelProperty("入驻商订单数量")
        private Long subNum;

        @ApiModelProperty("订单状态")
        private Integer deliveryState;

        @ApiModelProperty(value = "司机ID")
        private Long driverId;

        @ApiModelProperty(value = "司机评价状态 (0-未评价, 1-已评价)")
        private Integer driverRatingFlag;

        @ApiModelProperty(value = "司机评价ID")
        private Long driverRatingId;

        /**
         * 支付状态 {@link com.zksr.common.core.enums.PayStateEnum}
         */
        @ApiModelProperty("支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消")
        private Integer payState;

        @ApiModelProperty("入驻商订单明细集合")
        private List<SupplierOrderDtlDTO> supplierOrderDtlDTOList;
    }

    @ApiModel(value = "入驻商订单明细")
    @Data
    public static class SupplierOrderDtlDTO {
        @ApiModelProperty("订单明细ID")
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long supplierOrderDtlId;

        @ApiModelProperty("封面图")
        private String thumb;

        @ApiModelProperty("商品数量")
        private Long totalNum;

        @ApiModelProperty("合计金额")
        private BigDecimal totalAmt;

        @ApiModelProperty("优惠金额(包含优惠劵、活动及其他)")
        private BigDecimal discountAmt;

        @ApiModelProperty("合计金额")
        private BigDecimal saleAmt;



    }


}
