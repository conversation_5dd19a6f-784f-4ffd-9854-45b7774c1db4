package com.zksr.trade.api.order.vo;

import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class PayRefundReqVO {

    @ApiModelProperty(value = "订单编号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "子订单编号")
    private String supplierOrderNo;

    @ApiModelProperty(value = "退款单号", required = true)
    private String refundNo;

    @ApiModelProperty(value = "退款金额", required = true)
    private BigDecimal refundAmt;

    @ApiModelProperty(value = "平台商ID", hidden = true)
    private Long sysCode;

    @ApiModelProperty(value = "订单类型", hidden = true)
    private Integer orderType;
    
    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;
    
    /**
     * 商户类型（数据字典）;partner-平台商 dc-运营商 colonel-业务员 supplier-入驻商 branch-门店
     * {@link com.zksr.common.core.enums.MerchantTypeEnum}
     */
    @ApiModelProperty(required = true, value = "商户类型")
    private String merchantType;
    
    /**
     * 商户id 根据操作账户类型不同传入
     */
    @ApiModelProperty(required = true, value = "商户id")
    private Long merchantId;
    
    @ApiModelProperty(value = "platform", required = true)
    private String platform;

    /**
     * 结构参考
     * {
     *     {@linkplain PayOrderSubmitExtras#MERCHANT_ID }       : "门店ID",
     *     {@linkplain PayOrderSubmitExtras#SUPPLIER_ID }       : "入驻商ID",
     *     {@linkplain PayOrderSubmitExtras#SUPPLIER_ORDER_NO } : "入驻商订单号",
     * }
     */
    @ApiModelProperty(value = "额外参数", required = true)
    private Map<String, String> extras = new HashMap<>();
}
