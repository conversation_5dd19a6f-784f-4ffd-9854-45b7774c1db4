package com.zksr.trade.api.order.vo;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("常购商品请求实体类")
public class StoreProductRequest extends PageParam {

    @ApiModelProperty("唯一ID 规则: CONCAT(branch_id,'_',sku_id,'_',itemType)")
    private String id;

    @ApiModelProperty("查询类型 1,购买次数 2,最近购买 3,购买数量 4,类别编号")
    private Integer type;

    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("平台管理分类三级类别编号")
    private Long catgoryId;

    @ApiModelProperty("平台管理分类一级类别编号")
    private Long catgoryFirstId;

    @ApiModelProperty("排序 默认0 降序 1升序")
    private Integer orderBy;
}
