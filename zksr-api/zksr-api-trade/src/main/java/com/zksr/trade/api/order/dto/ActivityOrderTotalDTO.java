package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("促销活动 -- 活动订单使用汇总数据返回实体")
public class ActivityOrderTotalDTO {

    @ApiModelProperty(value = "活动ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long activityId;

    @ApiModelProperty(value = "活动参与客户总数量")
    private Long activityCustomerCount;

    @ApiModelProperty(value = "活动参与订单下单总金额")
    private BigDecimal activityOrderAmtSum;
}
