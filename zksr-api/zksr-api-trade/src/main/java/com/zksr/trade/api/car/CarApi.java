package com.zksr.trade.api.car;

import com.zksr.common.core.domain.vo.car.AppCarPageReqVO;
import com.zksr.common.core.domain.vo.car.AppCarPageRespVO;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import com.zksr.trade.api.car.dto.AppCarInitDTO;
import com.zksr.trade.api.car.vo.CarSaveReqVO;
import com.zksr.trade.api.car.vo.TrdCarApiRespVO;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/27
 * @desc
 */
@FeignClient(
        contextId = "remoteCarApi",
        value = ApiConstants.NAME
)
public interface CarApi {

    String PREFIX = ApiConstants.PREFIX + "/car";

    /**
     * 发送购物车事件
     * @param carEvent 购物车事件
     * @return
     */
    @PostMapping(value =PREFIX + "/carEvent")
    public CommonResult<Boolean> carEvent(@RequestBody AppCarEventDTO carEvent);


    /**
     * 获取初始化数据
     * @param branchId  门店ID
     * @param minId     最小键
     * @return  每次返回500条数据
     */
    @PostMapping(value =PREFIX + "/getInitData")
    public CommonResult<List<AppCarInitDTO>> getInitData(@RequestParam("branchId") Long branchId, @RequestParam("minId") Long minId);

    /**
     * 获得购物车分页
     *
     * @param branchId 分页查询
     * @return 购物车分页
     */
    @PostMapping(value =PREFIX + "/getTrdCarList")
    public List<TrdCarApiRespVO> getTrdCarList(@RequestParam("branchId") Long branchId);

    /**
     * 获得购物车分页, 从缓存
     * @param appCarPageReqVO
     * @return
     */
    @PostMapping(value =PREFIX + "/getTrdCacheCarItemPageList")
    public CommonResult<AppCarPageRespVO> getTrdCacheCarItemPageList(@RequestBody AppCarPageReqVO appCarPageReqVO);


    /**
     * 保存redis购物车
     * @param branchId
     * @return
     */
    @GetMapping(value =PREFIX + "/saveRedisCar")
    CommonResult<Boolean> saveRedisCar(@RequestParam("branchId") Long branchId);
}
