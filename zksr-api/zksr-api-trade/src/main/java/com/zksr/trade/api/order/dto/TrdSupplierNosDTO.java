package com.zksr.trade.api.order.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description: 订单查询返回对象
 * @Author: liuxingyu
 * @Date: 2024/3/30 10:26
 */
@ApiModel("订单返回对象")
@Data
@Accessors(chain = true)
public class TrdSupplierNosDTO {
    @ApiModelProperty("入驻商订单编号")
    private String supplierOrderNo;
}
