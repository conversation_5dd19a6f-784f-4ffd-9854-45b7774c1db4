package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@ApiModel("订单信息—— 订单保存返回Dto")
@Data
@NoArgsConstructor
public class TrdOrderResDto {

    @ApiModelProperty(value = "订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "运营商ID")
    private Long dcId;

    @ApiModelProperty(value = "支付到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirePayTime;

    @ApiModelProperty(value = "订单支付金额")
    private BigDecimal payAmt;
}
