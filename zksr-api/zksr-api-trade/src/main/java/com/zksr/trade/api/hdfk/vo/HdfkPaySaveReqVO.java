package com.zksr.trade.api.hdfk.vo;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 创建货到付款单
 * @date 2024/5/29 11:40
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HdfkPaySaveReqVO {

    @ApiModelProperty("入驻商订单集合")
    private List<Long> supplierOrderDtlIdList;

    @ApiModelProperty(value = "支付方式", hidden = true)
    private String payWay;

    @ApiModelProperty(value = "支付平台", hidden = true)
    private String platform;

    @ApiModelProperty(value = "大区编号", hidden = true)
    private Long sysCode;

    @ApiModelProperty("来源,0-app, 1-后台")
    private Integer paySource = NumberPool.INT_ZERO;

    @ApiModelProperty("备注")
    private String tips;

    @ApiModelProperty("凭证")
    private String voucher;
}
