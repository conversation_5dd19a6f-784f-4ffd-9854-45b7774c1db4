package com.zksr.trade.api.after.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月21日 08:43
 * @description: OrderAfterResp
 */
@Data
public class OrderAfterResp {
    @ApiModelProperty(value = "订单ID", required = true)
    private Long orderId;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "状态", required = true)
    private Long deliveryState;

    @ApiModelProperty(value = "售后类型", required = true)
    private Long afterType;

    @ApiModelProperty(value = "退款原因", required = true)
    private String reason;

    @ApiModelProperty(value = "申请凭证图片")
    private String applyImgs;

    @ApiModelProperty(value = "订单退款金额")
    private BigDecimal refundAmt;

    @ApiModelProperty(value = "数据来源（1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序）")
    private Long source;

    @ApiModelProperty(value = "申请人名称")
    private String createUserName;


    @ApiModelProperty(value = "入驻商订单集合")
    private List<SupplierOrder> supplierOrders;

    @ApiModel(value = "入驻商订单项")
    @Data
    public static class SupplierOrder {

        @ApiModelProperty(value = "入驻商id", required = true)
        private Long supplierId;

        @ApiModelProperty(value = "入驻商订单id")
        private Long supplierOrderId;

        @ApiModelProperty(value = "入驻商退货金额")
        private BigDecimal subRefundAmt;

        @ApiModelProperty(value = "入驻商订单明细项数组")
        private List<SupplierOrderDtl> supplierOrderDtls;

        @ApiModelProperty(value = "外部来源单号, 第三方生成售后单时使用")
        private String sourceOrderNo;

        /** 订单类型 */
        @ApiModelProperty(name = "订单类型 SheetTypeConstants")
        private String transNo;

        @ApiModel(value = "入驻商订单明细项")
        @Data
        public static class SupplierOrderDtl {

            @ApiModelProperty(value = "入驻商订单明细ID")
            private Long supplierOrderDtlId;

            @ApiModelProperty(value = "商品可退数量")
            private BigDecimal totalNum;

            @ApiModelProperty(value = "退货商品单价")
            private BigDecimal refundPrice;

            @ApiModelProperty(value = "退货商品数量")
            private BigDecimal refundQty;

            @ApiModelProperty(value = "退货商品金额")
            private BigDecimal refundAmt;

            @ApiModelProperty(value = "退货单位大小 1：小单位，2：中单位，3：大单位")
            private Integer unitType;

            @ApiModelProperty(value = "售后阶段(数据字典);1发货前退款 2发货后退款")
            private Long afterPhase;
        }

    }
}
