package com.zksr.trade.api.order.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 购物车传输对象DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("preSaveOrder 预下单接口请求对象")
public class CarDTO {

    /** 购物车id */
    private Long carId;

    /** 平台商id */
    private Long sysCode;

    /** 创建人 */
    private String createMemberId;

    /** 更新人 */
    private String updateMemberId;

    /** 门店id */
    private Long branchId;

    /** 入驻商上架商品id */
    private Long supplierItemId;

    /** 城市上架商品id */
    private Long areaItemId;

    /** 商品SPU id */
    private Long spuId;

    /** 商品sku id */
    private Long skuId;

    /** 数量 */
    private Long qty;

    /** 是否选中 1-是 0-否 */
    private Integer selected;

    /** 是否业务员推荐（1-是 0-否） */
    private Integer recommendFlag;
}
