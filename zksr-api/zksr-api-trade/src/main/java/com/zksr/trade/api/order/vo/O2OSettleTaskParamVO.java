package com.zksr.trade.api.order.vo;

import lombok.Data;

import java.util.List;

import com.zksr.common.core.enums.MerchantTypeEnum;

/**
 * O2O任务参数
 */
@Data
public class O2OSettleTaskParamVO {
    /**
     * 系统编码
     */
    private Long sysCode;
    
    /**
     * 订单号列表
     */
    private List<String> orderNos;
    
    /**
     * 入驻商订单号列表
     */
    private List<String> supplierOrderNos;
    
    /**
     * 默认入驻商 ， 可选 门户（MerchantTypeEnum.BRANCH_PROFIT）
     */
    private String merchantType  =  MerchantTypeEnum.SUPPLIER.getType();
    
    /**
     * 签收后多少分钟可以结算（默认7天 = 10080分钟）
     */
    private Integer signAfterMinutes = 10080;
    
    /**
     * 查询签收后多少天的结算记录（默认14天 = 20160分钟） - 有可能可能放假7天，所以查询范围要扩大成 7天 + 放假7天 = 14天
     */
    private Integer signSearchMinutes = 10080;
}