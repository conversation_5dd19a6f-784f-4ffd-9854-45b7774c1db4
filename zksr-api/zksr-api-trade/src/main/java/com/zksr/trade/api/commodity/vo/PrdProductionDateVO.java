package com.zksr.trade.api.commodity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * <AUTHOR>
 * @Date 2024/5/30 14:33
 * @接收商品生产日期实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("接收商品生产日期实体")
public class PrdProductionDateVO {

    /** 入驻商编号 */
    @Excel(name = "入驻商编号")
    @ApiModelProperty(value = "入驻商编号")
    private String supplierCode;

    /** 仓库编号 */
    @Excel(name = "仓库编号")
    @ApiModelProperty(value = "仓库编号")
    private String warehouseCode;

    /** 商品编号 */
    @Excel(name = "商品编号")
    @ApiModelProperty(value = "商品编号")
    private String itemNo;

    /** 最旧生产日期 */
    @Excel(name = "最旧生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最旧生产日期")
    private String minProductionDate;

    /** 最新生产日期 */
    @Excel(name = "最新生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最新生产日期")
    private String maxProductionDate;

    /** 外部编码 */
    @Excel(name = "外部编码")
    @ApiModelProperty(value = "外部编码")
    private String sourceOrderNo;
}
