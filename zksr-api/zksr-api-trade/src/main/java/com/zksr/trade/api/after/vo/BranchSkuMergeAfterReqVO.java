package com.zksr.trade.api.after.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年1月13日 14:08
 * @description: BranchSkuMergeAfterReqVO
 */
@Data
@ApiModel("获取门店合单售后商品列表 请求")
public class BranchSkuMergeAfterReqVO {

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "搜索KEY：（商品SPU名称）")
    private String searchKey;

    @ApiModelProperty(value = "skuId")
    private Long skuId;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "商品状态 ：(3:代发货/5:已收货/)")
    private Long deliveryState;
}
