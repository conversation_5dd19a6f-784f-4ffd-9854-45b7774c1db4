package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/13 17:40
 */
@Data
@NoArgsConstructor
public class BranchTransitQtyReqVO {

    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("本地上架ID")
    private Long areaItemId;

    public BranchTransitQtyReqVO(Long branchId, Long areaItemId) {
        this.branchId = branchId;
        this.areaItemId = areaItemId;
    }
}
