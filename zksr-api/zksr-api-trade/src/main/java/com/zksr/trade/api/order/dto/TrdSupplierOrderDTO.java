package com.zksr.trade.api.order.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("入驻商订单明细对象")
public class TrdSupplierOrderDTO {

    @ApiModelProperty("入驻商订单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierOrderId;

    @ApiModelProperty("入驻商订单编号")
    private String supplierOrderNo;

    @ApiModelProperty("配送费")
    private BigDecimal transAmt;

    @ApiModelProperty("入驻商订单金额")
    private BigDecimal subAmt;

    @ApiModelProperty("入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "入驻商配送标签, 来自平台商字典")
    private String productDistributionLabel;

    @ApiModelProperty("是否开启钱包支付, 0-关闭, 1-开启")
    private String switchWalletPay;

    @ApiModelProperty("入驻商头像")
    private String avatar;

    @ApiModelProperty("入驻商订单数量")
    private Long subNum;

    @ApiModelProperty("订单状态")
    private Integer deliveryState;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String memo;

    @ApiModelProperty("入驻商订单明细集合")
    private List<TrdSupplierOrderDtlDTO> supplierOrderDtlDTOList;

    @ApiModelProperty("入驻商订单操作信息集合")
    private List<TrdOrderDeliveryDTO> orderDeliveryDTOList;

    @ApiModelProperty(value = "入驻商优惠劵赠品集合")
    private List<TrdOrderDiscountDTO> couponTemplateList;

    @ApiModelProperty("入驻商订单门店余额支付金额")
    private BigDecimal subPayBalanceAmt;
}
