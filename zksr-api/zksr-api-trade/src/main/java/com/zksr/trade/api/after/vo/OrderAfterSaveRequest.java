package com.zksr.trade.api.after.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024年04月20日 15:49
 * @description: OrderAfterSaveRequest
 */
@Data
public class OrderAfterSaveRequest extends  OrderAfterRequest{

    @ApiModelProperty(value = "售后类型", required = true)
    private Long afterType;

    @ApiModelProperty(value = "退款原因", required = true)
    private String reason;

    @ApiModelProperty(value = "申请凭证图片")
    private String applyImgs;

    @ApiModelProperty(value = "数据来源（1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序）,5-第三方系统")
    private Long source;

    @ApiModelProperty(value = "申请人名称")
    private String createUserName;

}
