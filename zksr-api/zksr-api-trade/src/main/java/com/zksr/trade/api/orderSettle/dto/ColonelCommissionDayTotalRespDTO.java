package com.zksr.trade.api.orderSettle.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员提成统计
 * @date 2024/9/27 10:48
 */
@ApiModel(description = "业务员佣金统计（按天汇总）")
@Data
public class ColonelCommissionDayTotalRespDTO {

    @ApiModelProperty("操作日期")
    private String  operateDate;

    @ApiModelProperty("订单金额")
    private BigDecimal orderAmt;

    @ApiModelProperty("售后金额")
    private BigDecimal afterAmt;

    @ApiModelProperty("分润金额")
    private BigDecimal settleAmt;
}
