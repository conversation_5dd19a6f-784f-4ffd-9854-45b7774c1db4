package com.zksr.trade.api.invoice.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class InvoiceProcessDTO implements Serializable {

    /**
     * 业务流水号
     */
    private String bizId;

    //发票类型
//    private Integer invoiceType;

    // 销方纳税人识别号
    private String taxpayerCode;

    // 购方纳税人识别号
    private String payTaxpayerCode;

    // 购方纳税人名称
    private String payTaxpayerName;

    // 发票含税总金额，发票金额必须等于明细所有数据含税金额总和
//    private BigDecimal invoiceAmt;

    // 发票抬头（1个人/2企业） 开专票时，必须为企业
//    private Integer invoiceHead;

    // 拆合规则 1:不启用发票系统规则 - 不拆不合 2:使用发票系统规则 没有该字段 走不使用开票系统规则
//    private String invoiceRule = "1";

    // 开票渠道:1：入驻商开票,2：美的付开票
    private Integer channel;

    // 开票场景:1：佣金发票,2：订单发票
    private Integer businessType;

    // 商户ID（可空）
//    private String merchantId;

    // 购方银行账号
//    private String payBankAccount;
//
//    // 购方银行名称
//    private String payBankName;
//
//    // 购方固定电话
//    private String payFixedPhoneNumber;
//
//    // 购方地址
//    private String payUnitAddress;

    // 购方邮箱
    private String mail;

    // 票面备注
    private String remarks;

    // 开票人
    private String operator;


    private List<InvoiceBlueDetailDTO> detailList;

    @Data
    public static class InvoiceBlueDetailDTO {

        // 业务单号
        private String businessNo;

        // 开票金额
//        private BigDecimal amtContainTax;
//
//        // 商品数量
//        private Integer goodCount;
//
//        // 商品编码(可空)
//        private String code;
//
//        // 商品名称
//        private String goodsName;
//
//        // 商品单位
//        private String goodsUnit;
//
//        // 规格型号
//        private String standards;
//
//        // 税收分类编码 支持长、短税码，长税码固定19位，确保在主数据平台有维护
//        private String taxCode;
//
//        // 税率
//        private BigDecimal taxRate;

        // 含税单价, 必须为正数
        private BigDecimal taxUnitPrice;
    }

}
