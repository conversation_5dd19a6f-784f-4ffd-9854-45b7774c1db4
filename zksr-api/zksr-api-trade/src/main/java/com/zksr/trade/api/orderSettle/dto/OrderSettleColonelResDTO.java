package com.zksr.trade.api.orderSettle.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年05月07日 11:15
 * @description: OrderSettleColonelResDTO
 */
@Data
@ApiModel("订单结算信息 - 返回实体（订单）")
@Accessors(chain = true)
public class OrderSettleColonelResDTO {

    @ApiModelProperty(value = "订单ID或售后订单ID")
    private Long orderId;

    @ApiModelProperty(value = "订单编号或售后订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "订单状态类型（销售订单，售后订单）")
    private String orderStateType;

    @ApiModelProperty(value = "门店名称")
    private String branchName;

    @ApiModelProperty(value = "门店ID")
    private Long branchId;

    @ApiModelProperty(value = "Sku数量")
    private Integer skuNum;

    @ApiModelProperty(value = "订单实付金额")
    private BigDecimal payAmtTotal;

    @ApiModelProperty(value = "合计佣金")
    private BigDecimal settleTotal;

    @ApiModelProperty(value = "订单结算明细")
    private List<OrderDtlSettleResDTO> orderDtlSettleResDTOS;

}
