package com.zksr.trade.api.supplierOrder;

import com.zksr.common.core.constant.OpenapiSecurityConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.vo.openapi.OrderDetailOpenDTO;
import com.zksr.common.core.domain.vo.openapi.OrderOpenDTO;
import com.zksr.common.core.domain.vo.openapi.SyncDataDTO;
import com.zksr.common.core.domain.vo.openapi.receive.*;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.trade.api.supplierOrder.dto.OrderDiscountDtlDTO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderInvoicePageReqDTO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderInvoiceRespDTO;
import com.zksr.trade.enums.ApiConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 交易模块订单跨服务调用
 */
@FeignClient(
        contextId = "remoteSupplierOrderApi",
        value = ApiConstants.NAME
//        fallbackFactory = RemoteMerchantAccountFallbackFactory.class
)
public interface SupplierOrderApi {
    String PREFIX = ApiConstants.PREFIX + "/supplierOrder";

    /**
     * OpenApi销售订单出库回传
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/receiveOrderOutboundReturn")
    CommonResult<Boolean> receiveOrderOutboundReturn (@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                                      @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                                      @RequestBody OrderOutboundReturnVO vo);
    /**
     * OpenApi包裹接收
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/receivePackage")
    CommonResult<Boolean> receivePackage (@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                                      @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                                      @RequestBody OrderPackageReturnVO vo);

    /**
     * 根据订单编号查询销售订单
     * @param orderNo
     * @return
     */
    @GetMapping(value =PREFIX + "/getSupplierOrderListByOrderNo")
    CommonResult<List<OrderOpenDTO>> getSupplierOrderListByOrderNo(@RequestParam("orderNo") String orderNo);

    /**
     * 根据入驻商订单编号查询入驻商销售订单
     * @param supplierOrderNo
     * @return
     */
    @GetMapping(value =PREFIX + "/getSupplierOrderBySupplierOrderNo")
    CommonResult<OrderOpenDTO> getSupplierOrderBySupplierOrderNo(@RequestParam("supplierOrderNo") String supplierOrderNo);

    /**
     * 获取入驻商销售订单信息详情
     * @param supplierOrderNo
     * @return
     */
    @GetMapping(PREFIX + "/getSupplierOrderDtlBySupplierOrderNo")
    CommonResult<List<OrderDetailOpenDTO>> getSupplierOrderDtlBySupplierOrderNo(@RequestParam("supplierOrderNo") String supplierOrderNo);

    /**
     * 更新订单推送标志
     *
     * @return
     */
    @PostMapping(value = PREFIX + "/updateByPushStatus")
    CommonResult<Boolean> updateByPushStatus(@RequestParam("supplierOrderNo") String supplierOrderNo,@RequestParam("pushStatus") Integer pushStatus);


    /**
     * 订单推送成功后处理
     * @param data
     * @return
     */
    @PostMapping(value =PREFIX + "/syncOrderResponseSuccess")
    CommonResult<Boolean> syncOrderResponseSuccess(@Valid @RequestBody SyncDataDTO data);

    @PostMapping(value =PREFIX + "/getSupplierOrderDtlListByIds")
    CommonResult<List<OrderDetailOpenDTO>> getSupplierOrderDtlListByIds(@RequestBody List<Long> ids);

    /**
     * 订单接收成功通知
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/orderReceiveCallback")
    CommonResult<Boolean> orderReceiveCallback(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                               @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                               @RequestBody OrderReceiveCallbackVO vo);

    /**
     * 根据入驻商订单ID 获取入驻商订单的优惠信息
     * @param supplierOrderId
     * @return OrderDiscountDtlDTO
     */
    @GetMapping(PREFIX + "/getOrderDiscountDtlListBySupplierOrderId")
    CommonResult<List<OrderDiscountDtlDTO>> getOrderDiscountDtlListBySupplierOrderId(@RequestParam("supplierOrderId")Long supplierOrderId);

    /**
     * 订单发货前取消
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/orderCancel")
    CommonResult<Boolean> orderCancel(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                               @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                               @RequestBody OrderCancelVO vo);

    /**
     * 删除商品信息 前置校验 需要删除的商品是否下单
     * @param spuIds 需要校验的SPUID集合
     * @return 已经下过单的SPUID集合
     */
    @PostMapping(value =PREFIX + "/deleteSpuCheckSupplierOrderDtl")
    CommonResult<List<Long>> deleteSpuCheckSupplierOrderDtl(@RequestBody Long[] spuIds);


    /**
     * 查询类别是否参与过下单
     */
    @PostMapping(value =PREFIX + "/getCategoryExistOrder")
    CommonResult<List<OrderDetailOpenDTO> > getCategoryExistOrder(@RequestParam("categoryId") Long categoryId,@RequestParam("sysCode") Long sysCode);

    /**
     * 订单取消接收通知
     *
     * @param sysCode
     * @param opensourceId
     * @param vo
     * @return
     */
    @PostMapping(value =PREFIX + "/orderCancelReceiveCallback")
    CommonResult<Boolean> orderCancelReceiveCallback(@RequestHeader(name = SecurityConstants.SYS_CODE) Long sysCode,
                                                     @RequestHeader(name = OpenapiSecurityConstants.DETAILS_OPENSOURCE_ID) Long opensourceId,
                                                     @RequestBody OrderCancelReceiveCallbackVO vo);

    /**
     * 查询入驻商订单发票
     * @param reqDTO
     * @return
     */
    @PostMapping(value =PREFIX + "/getSupplierOrderInvoice")
    CommonResult<TrdSupplierOrderInvoiceRespDTO> getSupplierOrderInvoice(@RequestBody TrdSupplierOrderInvoicePageReqDTO reqDTO);

    /**
     * 订单发票接收
     * @param reqDTO
     * @return
     */
    @PostMapping(value =PREFIX + "/receiveOrderInvoice")
    CommonResult<Boolean> receiveOrderInvoice(@RequestBody SupplierOrderInvoiceOpenDTO reqDTO);
}
