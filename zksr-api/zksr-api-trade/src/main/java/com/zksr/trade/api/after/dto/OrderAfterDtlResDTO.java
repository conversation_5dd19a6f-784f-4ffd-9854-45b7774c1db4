package com.zksr.trade.api.after.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年05月16日 14:16
 * @description: OrderAfterDtlResDTO
 */
@Data
@ApiModel("订单售后总计实体 返回")
public class OrderAfterDtlResDTO {
    @ApiModelProperty(value = "售后总金额")
    private BigDecimal sumReturnAmt;

    @ApiModelProperty(value = "售后总数量")
    private BigDecimal sumReturnNum;

    @ApiModelProperty(value = "订单明细ID")
    private Long supplierOrderDtlId;
}
