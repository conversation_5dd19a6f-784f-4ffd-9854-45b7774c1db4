package com.zksr.trade.api.after.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年04月20日 17:29
 * @description: TrdAfterSaveVO
 */
@Data
@ApiModel("订单信息 - 保存售后主订单Request VO")
public class TrdAfterSaveVO {

    /** 售后单编号 */
    private String afterNo;
    /** 运营商id */
    private Long dcId;
    /** 城市id */
    private Long areaId;
    /** 用户id */
    private Long memberId;
    /** 业务员id */
    private Long colonelId;
    /** 业务员级别 */
    private Long colonelLevel;
    /** 父业务员id */
    private Long pcolonelId;
    /** 父业务员级别 */
    private Long pcolonelLevel;
    /** 门店id */
    private Long branchId;
    /** 经度 */
    private BigDecimal longitude;
    /** 纬度 */
    private BigDecimal latitude;
    /** 门店地址 */
    private String branchAddr;
    /** 退货原因 */
    private String reason;
    /** 退款金额 */
    private BigDecimal refundAmt;
    /** 退货说明 */
    private String descr;

    /** 订单退款金额;包含本次售后单和之前未取消的售后单的退款金额之和 */
    private BigDecimal orderRefundAmt;

    /** 订单支付金额;从订单表 */
    private BigDecimal payAmt;

    /** 退款方式（数据字典）;从订单表 0-在线支付 1-储值支付 2-货到付款 */
    private String payway;

    /** 支付平台(数据字典);从订单表 */
    private String platform;

    /** 订单支付时间 */
    private Date payTime;

    /** 退货完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date returnTime;

    /** 退款完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date refundTime;

    /** 是否已取消 0未取消 1已取消 */
    @Excel(name = "是否已取消 0未取消 1已取消")
    private Long isCancel;

    /** 来源(数据字典);1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序 */
    @Excel(name = "来源(数据字典);1用户自己(默认)，2-后台管理员，3-业务员app, 4-入驻商小程序")
    private Long source;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 申请时上传的凭证照片 */
    @Excel(name = "申请时上传的凭证照片")
    private String applyImgs;

    /** 回填快递单时上传的凭证照片 */
    @Excel(name = "回填快递单时上传的凭证照片")
    private String expressImgs;

    /** 支付公司收取的支付费率;从订单表 */
    @Excel(name = "支付公司收取的支付费率;从订单表")
    private BigDecimal payRate;

    /** 支付平台手续费;从订单表 (pay_amt*pay_rate) 四舍五入 */
    @Excel(name = "支付平台手续费;从订单表 (pay_amt*pay_rate) 四舍五入")
    private BigDecimal payFee;
}
