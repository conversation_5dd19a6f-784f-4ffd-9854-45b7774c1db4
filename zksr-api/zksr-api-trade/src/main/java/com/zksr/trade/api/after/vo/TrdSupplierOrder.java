package com.zksr.trade.api.after.vo;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.domain.BaseEntity;

/**
 * 入驻商订单对象 trd_supplier_order
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
@TableName(value = "trd_supplier_order")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrdSupplierOrder extends BaseEntity{
    private static final long serialVersionUID=1L;

    /** 入驻商订单id */
    @TableId(type= IdType.ASSIGN_ID)
    private Long supplierOrderId;

    /** 平台商id */
    @Excel(name = "平台商id")
    @TableField(updateStrategy = FieldStrategy.NEVER)
    private Long sysCode;

    /** 入驻商订单编号 */
    @Excel(name = "入驻商订单编号")
    private String supplierOrderNo;

    /** 订单id */
    @Excel(name = "订单id")
    private Long orderId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 配送费 */
    @Excel(name = "配送费")
    private BigDecimal transAmt;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    private Long supplierId;

    /** 入驻商名称 */
    @Excel(name = "入驻商名称")
    private String supplierName;

    /** 备注 */
    @Excel(name = "备注")
    private String memo;

    /** 优惠金额 **/
    @Excel(name = "优惠金额")
    private BigDecimal subDiscountAmt;

    /** 支付金额 **/
    @Excel(name = "支付金额")
    private BigDecimal  subPayAmt;

    /** 订单金额 未减去优惠的订单金额 **/
    @Excel(name = "订单金额")
    private BigDecimal subOrderAmt;

    /** 删除状态(0:正常，2：删除) */
    private Integer delFlag;

    /** 订单商品总数量 **/
    @Excel(name = "订单商品总数量")
    private BigDecimal subOrderNum;

    /** 支付公司收取的支付费率 */
    @Excel(name = "支付公司收取的支付费率")
    private BigDecimal payRate;

    /** 支付公司手续费金额 */
    @Excel(name = "支付公司手续费金额")
    private BigDecimal subPayFee;

    /** 已退款金额 */
    @Excel(name = "已退款金额")
    private BigDecimal subRefundAmt;

    /** 已退款手续费金额 */
    @Excel(name = "已退款手续费金额")
    private BigDecimal subRefundFee;

    /** 发货前取消数量 */
    @Excel(name = "发货前订单取消数量")
    private BigDecimal subCancelQty;

    /** 发货前取消金额 */
    @Excel(name = "发货前订单取消金额")
    private BigDecimal subCancelAmt;

    /** 外部订单号 */
    @Excel(name = "外部订单号")
    private String sourceOrderNo;

    /** 支付状态（数据字典sys_pay_state） 0-未支付 1-已支付 2-未付款取消 */
    @Excel(name = "支付状态", readConverterExp = "数=据字典sys_pay_state")
    private Integer payState;

    /** 推送状态 0未推送 1已推送 2已接收*/
    @Excel(name = "推送状态")
    private Integer pushStatus;

    /** 司机ID */
    @Excel(name = "司机ID")
    private Long driverId;

    /** 司机评价状态 (0-未评价, 1-已评价) */
    @Excel(name = "司机评价状态")
    private Integer driverRatingFlag;

    /** 司机评价ID */
    @Excel(name = "司机评价ID")
    private Long driverRatingId;

    @Excel(name = "打印次数")
    @ApiModelProperty("打印次数")
    private Long printQty;

    /** *订单状态（数据字典） */
    @Excel(name = "订单状态", readConverterExp = "数=据字典")
    private Integer deliveryState;
    @ApiModelProperty(name = "是否负库存下单 0-否 1-是")
    private Integer stockShortFlag;

    /** 余额支付金额 */
    @Excel(name = "余额支付金额")
    private BigDecimal subPayBalanceAmt;

    /** 总支付金额 */
    @Excel(name = "总支付金额")
    private BigDecimal totalSubPayAmt;
}
