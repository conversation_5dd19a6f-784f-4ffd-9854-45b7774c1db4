package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024年03月31日 16:46
 * @description: TrdOrderInfo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("订单信息 - 缓存订单")
public class TrdOrderInfo {
    /** 订单编号 */
    private String orderNo;
    /** 订单创建时间 */
    private long createTime;
    /** 平台商编号 */
    private long sysCode;
    /** 运营商编号 */
    private long dcId;
}
