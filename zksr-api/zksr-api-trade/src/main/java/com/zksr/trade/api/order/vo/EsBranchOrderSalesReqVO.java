package com.zksr.trade.api.order.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@ApiModel("查询ES门店月订单销售数据 --  请求")
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EsBranchOrderSalesReqVO {
    @ApiModelProperty("门店ID")
    private Long branchId;

    @ApiModelProperty("平台ID")
    private Long sysCode;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;
}
