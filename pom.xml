<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zksr</groupId>
    <artifactId>zksr</artifactId>
    <version>3.6.3</version>

    <name>zksr</name>
    <url>http://www.zksr.vip</url>
    <description>中科商软微服务系统</description>

    <properties>
        <zksr.version>3.6.3</zksr.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-surefire-plugin.version>3.0.0-M5</maven-surefire-plugin.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <lombok.version>1.18.30</lombok.version>

        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
        <spring-boot-admin.version>2.7.11</spring-boot-admin.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <knife4j.version>2.0.9</knife4j.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.0.0</pagehelper.boot.version>
        <druid.version>1.2.20</druid.version>
        <dynamic-ds.version>3.5.2</dynamic-ds.version>
        <commons.io.version>2.13.0</commons.io.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>2.0.43</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>4.1.2</poi.version>
        <transmittable-thread-local.version>2.14.4</transmittable-thread-local.version>
        <mybatis-plus.version>3.5.3.2</mybatis-plus.version>
        <mybatis-plus-join-boot-starter.version>1.4.6</mybatis-plus-join-boot-starter.version>
        <hutool.version>5.8.22</hutool.version>
        <guava.version>32.1.2-jre</guava.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <hutool.version>5.8.5</hutool.version>
        <jetcache.version>2.7.4</jetcache.version>
        <jedis.version>4.3.1</jedis.version>
        <xxl_job.version>2.4.0</xxl_job.version>
        <liteflow.version>2.12.4</liteflow.version>
        <jiba.version>1.0.2</jiba.version>
        <hanlp.version>portable-1.3.2</hanlp.version>
        <volcengine.version>1.0.99</volcengine.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-core</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-swagger</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-security</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-mp</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-datasource</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-seata</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-log</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-redis</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- Elasticsearch搜索引擎中间件服务 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-elasticsearch</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- rockermq消息中间件服务 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-rocketmq</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 第三方 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-common-third</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-system</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 报表接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-report</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-portal</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId> <!-- MyBatis 联表查询 -->
                <version>${mybatis-plus-join-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <!-- 文件|导出|接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-file</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-product</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-member</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-supplier</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-promotion</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-trade</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-account</artifactId>
                <version>${zksr.version}</version>
            </dependency>


            <!-- 系统接口 -->
            <dependency>
                <groupId>com.zksr</groupId>
                <artifactId>zksr-api-demo</artifactId>
                <version>${zksr.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alicp.jetcache</groupId>
                <artifactId>jetcache-starter-redis</artifactId>
                <version>${jetcache.version}</version>
            </dependency>

            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-spring-boot-starter</artifactId>
                <version>${liteflow.version}</version>
            </dependency>
            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>liteflow-script-javax</artifactId>
                <version>${liteflow.version}</version>
            </dependency>
            <!-- 结巴分词 -->
            <dependency>
                <groupId>com.huaban</groupId>
                <artifactId>jieba-analysis</artifactId>
                <version>${jiba.version}</version>
            </dependency>
            <!-- hanlp 分词 -->
            <dependency>
                <groupId>com.hankcs</groupId>
                <artifactId>hanlp</artifactId>
                <version>${hanlp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volc-sdk-java</artifactId>
                <version>${volcengine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.midea.mbf</groupId>
                <artifactId>mbf-mq-producer-spring-boot-starter</artifactId>
                <version>1.2.4</version>
            </dependency>
            <!--RocketMq依赖-->
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>2.2.1</version>
            </dependency>

            <dependency>
                <groupId>com.midea.apaas</groupId>
                <artifactId>consumer-spring-boot-starter</artifactId>
                <version>1.0.6-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.midea.apaas</groupId>
                <artifactId>consumer-service-spring-boot-starter</artifactId>
                <version>1.0.6-SNAPSHOT</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>zksr-auth</module>
        <module>zksr-gateway</module>
        <module>zksr-visual</module>
        <module>zksr-modules</module>
        <module>zksr-api</module>
        <module>zksr-common</module>
        <module>zksr-portal</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>


                <!-- maven-surefire-plugin 插件，用于运行单元测试。 -->
                <!-- 注意，需要使用 3.0.X+，因为要支持 Junit 5 版本 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <!-- maven-compiler-plugin 插件，解决 Lombok + MapStruct 组合 -->
                <!-- https://stackoverflow.com/questions/33483697/re-run-spring-boot-configuration-annotation-processor-to-update-generated-metada -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.springframework.boot</groupId>
                                <artifactId>spring-boot-configuration-processor</artifactId>
                                <version>${spring-boot.version}</version>
                            </path>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
            </plugins>

        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>

        <repository>
            <!--指定从midea私服仓库下载依赖-->
            <id>midea-public</id>
            <url>https://mvn.midea.com/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <!--必须是true-->
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>

    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>

        <pluginRepository>
            <id>midea-public</id>
            <url>https://mvn.midea.com/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <!--必须是true-->
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
