package com.zksr.portal;

import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.portal.controller.mall.vo.coupon.CouponOrderValidReqVO;
import com.zksr.portal.service.mall.IActivityService;
import com.zksr.portal.service.mall.ICarService;
import com.zksr.portal.service.mall.ICouponService;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.content.dto.ProductContentDTO;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.trade.api.car.vo.CarSaveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/6/4 16:34
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrPortalApplication.class)
@Slf4j
public class ZksrPortalApplicationTest {


    /*@Autowired
    private IActivityService activityService;

    @Test
    public void test() {
        activityService.getActivityDetail(585513293609893889L);
    }*/

    /*@Autowired
    private ICarService carService;

    @Resource
    private ProductContentApi productContentApi;

    @Resource
    private AreaApi areaApi;

    @Test
    public void test02() {

        List<AreaDTO> areaDTOList = areaApi.getListBySysCode(4L).getCheckedData();
        List<Long> areaList = areaDTOList.stream().map(AreaDTO::getAreaId).collect(Collectors.toList());


        {
            ProductContentPageReqDTO pageReqDTO = new ProductContentPageReqDTO();
            pageReqDTO.setSysCode(4L);
            pageReqDTO.setPageSize(1000);
            pageReqDTO.setAreaId(24L);
            //获取商品数据
            PageResult<ProductContentDTO> elasticSearchListResult = productContentApi.getPageElasticSearchListByApi(pageReqDTO).getCheckedData();

            for (ProductContentDTO contentDTO : elasticSearchListResult.getList()) {

                CarSaveReqVO req = new CarSaveReqVO();
                AppCarIdDTO carId = new AppCarIdDTO();
                carId.setType(contentDTO.getType());
                carId.setBranchId(466979106766028801L);
                carId.setSupplierId(contentDTO.getSupplierId());
                carId.setAreaItemId(contentDTO.getItemId());
                carId.setSupplierItemId(contentDTO.getItemId());
                carId.setSkuId(contentDTO.getSkuId());
                carId.setSpuId(contentDTO.getSpuId());
                carId.setUnitSize(contentDTO.getUnitSize());

                req.setCarId(carId);
                req.setOpQty(1);
                req.setOpType(1);
                try {
                    carService.add(req);
                } catch (Exception e) {

                }
            }
        }
        *//*{

            ProductContentPageReqDTO pageReqDTO = new ProductContentPageReqDTO();
            pageReqDTO.setPageSize(1000);
            pageReqDTO.setAreaId(139L);
            //获取商品数据
            PageResult<ProductContentDTO> elasticSearchListResult = productContentApi.getPageElasticSearchListByApi(pageReqDTO).getCheckedData();
            System.out.println(elasticSearchListResult);




        }*//*

    }*/
}
