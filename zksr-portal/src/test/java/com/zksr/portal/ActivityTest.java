package com.zksr.portal;

import com.github.pagehelper.Page;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.portal.base.BaseTest;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageReqVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageRespVO;
import com.zksr.portal.service.impl.PortalCacheServiceImpl;
import com.zksr.portal.service.impl.mall.ActivityServiceImpl;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;

@Slf4j
public class ActivityTest extends BaseTest {
    @InjectMocks
    @Autowired
    private ActivityServiceImpl activityService;
    @InjectMocks
    @Autowired
    private PortalCacheServiceImpl portalCacheService;

    @Mock
    private AreaItemApi areaItemApi;
    @Mock
    private ActivityApi activityApi;


    private static final Long skuId = 1111111111111111L;
    private static final Long spuId = 2222222222222222222L;
    private static final Long activityId = 10000000123456789L;

    @Test
    public void testGetItemsByActivityIds() throws Exception {
        ActivitySpuScopeDTO activitySpuScopeDTO = new ActivitySpuScopeDTO();
        activitySpuScopeDTO.setActivityId(activityId);
        activitySpuScopeDTO.setSpuId(spuId);
        activitySpuScopeDTO.setApplyType(4L);
        activitySpuScopeDTO.setApplyId(skuId);
        activitySpuScopeDTO.setWhiteOrBlack(1);
        Mockito.doReturn(CommonResult.success(Collections.singletonList(activitySpuScopeDTO))).when(activityApi).getActivitySpuScopeList(Mockito.any());

        AreaItemDTO areaItemDTO = new AreaItemDTO();
        areaItemDTO.setSkuId(skuId);
        areaItemDTO.setSpuId(spuId);
        Mockito.doReturn(CommonResult.success(PageResult.result(new Page<>(), Collections.singletonList(areaItemDTO)))).when(areaItemApi).getAreaItemPageByApi(Mockito.any());

        ActivityItemPageReqVO pageReqVO = new ActivityItemPageReqVO();
        pageReqVO.setActivityIds(Collections.singletonList(activityId));
        List<ActivityItemPageRespVO> activityItemPageRespVOS = activityService.getItemsByActivityIds(pageReqVO);
        log.info("testGetItemsByActivityIds result: {}", JsonUtils.toJsonString(activityItemPageRespVOS));
    }

}
