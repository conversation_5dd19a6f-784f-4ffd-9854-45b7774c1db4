package com.zksr.portal.service;

import com.alibaba.fastjson2.JSON;
import com.zksr.portal.ZksrPortalApplication;
import com.zksr.portal.controller.mall.vo.car.CarPageReqVO;
import com.zksr.portal.controller.mall.vo.car.CarPageRespVO;
import com.zksr.portal.service.mall.ICarService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrPortalApplication.class)
public class CarServiceTest {

    @Autowired
    private ICarService carService;

    @Test
    public void testGetPage() {
        String s = "{\"ignoreDebt\":false,\"branchId\":484063576146575362,\"pageNo\":1,\"count\":true,\"pageSize\":999,\"params\":{},\"productType\":\"local\"}";
        CarPageReqVO pageReqVO = JSON.parseObject(s, CarPageReqVO.class);
        CarPageRespVO resp = carService.getPage(pageReqVO);
    }
}
