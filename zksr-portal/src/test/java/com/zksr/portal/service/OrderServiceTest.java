package com.zksr.portal.service;


import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.zksr.account.model.pay.dto.OrderSettlementDTO;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.common.core.config.AnntoProxyConfig;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.ZksrPortalApplication;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.service.mall.ISkuService;
import com.zksr.portal.service.mall.OrderService;
import com.zksr.product.api.blockScheme.BlockSchemeApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.trade.api.order.vo.StoreProductRespVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Author: chenyj8
 * @Desciption: 测试类
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrPortalApplication.class)
public class OrderServiceTest {
    @Autowired
    private OrderService orderService;

    @MockBean
    private BlockSchemeApi blockSchemeApi;

//    @MockBean
//    ISkuService skuService;

    @Test
    public void testSubmitOrder() {

    }

    @Test
    public void testSettleOrder(){
        Long memberId = 484063576146575361L;
        Long branchId = 484063576146575362L;
        SettleOrderRequest request = new SettleOrderRequest();
        request.setBranchId(branchId);
        request.setMemberId(memberId);

        List<SettleOrderRequest.Item> items = new ArrayList<>();
        {
            SettleOrderRequest.Item item = new SettleOrderRequest.Item();
            item.setSupplierId(15580493948L);
            List<SettleOrderRequest.Item.SupplierItem> supplierItems = new ArrayList<>();

            SettleOrderRequest.Item.SupplierItem supplierItem = new SettleOrderRequest.Item.SupplierItem();
            supplierItem.setAreaItemId(629296967639793665L);
            supplierItem.setSupplierItemId(null);
            supplierItem.setCarId("local_484063576146575362_15580493948_-1_629296967639793665_629252793901154305_629252793901154304_1");
            supplierItem.setGoodsType(0);
            supplierItem.setCount(1L);
            supplierItem.setUnit("21");
            supplierItem.setUnitSize(1);
            supplierItem.setIsActivitySpSk(0);
            supplierItem.setSkuId(629252793901154305L);

            supplierItems.add(supplierItem);

            SettleOrderRequest.Item.SupplierItem supplierItem1 = new SettleOrderRequest.Item.SupplierItem();
            supplierItem1.setAreaItemId(629296967639793665L);
            supplierItem1.setSupplierItemId(null);
            supplierItem1.setCarId("local_484063576146575362_15580493948_-1_629296967639793665_629252793901154305_629252793901154304_1");
            supplierItem1.setGoodsType(0);
            supplierItem1.setCount(1L);
            supplierItem1.setUnit("21");
            supplierItem1.setUnitSize(1);
            supplierItem1.setIsActivitySpSk(1);
            supplierItem1.setSkuId(629252793901154305L);

            supplierItems.add(supplierItem1);


            item.setSupplierItems(supplierItems);
            items.add(item);
        }


        {
            SettleOrderRequest.Item item = new SettleOrderRequest.Item();
            item.setSupplierId(15580493955L);
            List<SettleOrderRequest.Item.SupplierItem> supplierItems = new ArrayList<>();

            SettleOrderRequest.Item.SupplierItem supplierItem = new SettleOrderRequest.Item.SupplierItem();
            supplierItem.setAreaItemId(628763494046662657L);
            supplierItem.setSupplierItemId(null);
            supplierItem.setCarId("local_484063576146575362_15580493955_-1_628763494046662657_628762609283399681_628762609283399680_1");
            supplierItem.setGoodsType(0);
            supplierItem.setCount(1L);
            supplierItem.setUnit("21");
            supplierItem.setUnitSize(1);
            supplierItem.setIsActivitySpSk(0);
            supplierItem.setSkuId(628762609283399681L);

            supplierItems.add(supplierItem);

            SettleOrderRequest.Item.SupplierItem supplierItem1 = new SettleOrderRequest.Item.SupplierItem();
            supplierItem1.setAreaItemId(628763494046662657L);
            supplierItem1.setSupplierItemId(null);
            supplierItem1.setCarId("local_484063576146575362_15580493955_-1_628763494046662657_628762609283399681_628762609283399680_1");
            supplierItem1.setGoodsType(0);
            supplierItem1.setCount(1L);
            supplierItem1.setUnit("21");
            supplierItem1.setUnitSize(1);
            supplierItem1.setIsActivitySpSk(1);
            supplierItem1.setSkuId(628762609283399681L);

            supplierItems.add(supplierItem1);


            item.setSupplierItems(supplierItems);
            items.add(item);
        }



        List<ActivityVO> activitys = new ArrayList<>();
        ActivityVO activityVO = new ActivityVO();
        activityVO.setActivityId(630655036258025472L);
        activityVO.setActivityType("SP");
        activityVO.setActivityRuleId(630655036258025473L);
        activitys.add(activityVO);

        ActivityVO activityVO1 = new ActivityVO();
        activityVO1.setActivityId(630655036258025472L);
        activityVO1.setActivityType("SP");
        activityVO1.setActivityRuleId(630655036258025474L);
        activitys.add(activityVO1);

        request.setItems(items);
        request.setActivitys(activitys);

        System.out.println(JsonUtils.toJsonString(request));
        SettleOrderResp settleOrderResp = orderService.settleOrder(memberId, branchId, request);
        System.out.println(JsonUtils.toJsonString(settleOrderResp));
    }

    @Test
    public void testJsonUtils() {
        List list = new ArrayList();
        for (int i = 0; i < 5; i++) {
            PayOrderSubmitReqVO vo = new PayOrderSubmitReqVO();
            vo.setOrderNo(i+"");
            OrderSettlementDTO dto = new OrderSettlementDTO();
            dto.setAccountNo(i+1+"");
            dto.setAmt(BigDecimal.ZERO);
            vo.setSettlements(Lists.newArrayList(dto));
            list.add(vo);
        }

        System.out.println(JsonUtils.toJsonString(list));
    }

    @Test
    public void testSplitSku(){
        PageResult<StoreProductRespVO> pageResult = new PageResult<>();
        List<StoreProductRespVO> data = new ArrayList<>();
        for(long i=0; i<5; i++){
            StoreProductRespVO vo = new StoreProductRespVO();
            vo.setSkuId(i);
            data.add(vo);
        }
        pageResult.setList(data);
        pageResult.setTotal(Long.valueOf(data.size()));
        BranchDTO branchDto = new BranchDTO();
        branchDto.setBranchId(1L);
        orderService.splitSku(pageResult, branchDto);
    }

    @Before
    public void mockApi(){
        PayOrderRespDTO payOrderRespDTO = new PayOrderRespDTO();
        List<Long> ids = new ArrayList<>();
        ids.add(1L);
        ids.add(2L);
        ids.add(8L);
        ids.add(9L);
        CommonResult<List<Long>> pageList = CommonResult.success(ids);

        Mockito.when(blockSchemeApi.getBlockSkusByBranchId(1L)).thenReturn(pageList);

        List<SkuPageRespVO> skuList = new ArrayList<>();
        SkuPageRespVO vo1 = new SkuPageRespVO();
        vo1.setSkuId(1L);
        skuList.add(vo1);

        SkuPageRespVO vo2 = new SkuPageRespVO();
        vo1.setSkuId(2L);
        skuList.add(vo2);

//        Mockito.when(skuService.blockSkus(Mockito.any(ArrayList.class),Mockito.any(Long.class))).thenReturn(skuList);


    }

    @Test
    public void testSaveOrder(){

        Long memberId = 484063576146575361L;
        Long branchId = 484063576146575362L;

        CreateOrderRequest request = new CreateOrderRequest();
        request.setOrderSource("mini");
        request.setPayWay("0");

        List<SettleOrderRequest.Item> items = new  ArrayList<SettleOrderRequest.Item>();
        SettleOrderRequest.Item item = new SettleOrderRequest.Item();
        item.setSupplierId(15580493955L);

        List<SettleOrderRequest.Item.SupplierItem> supplierItemList = new ArrayList<>();

        SettleOrderRequest.Item.SupplierItem supplierItem = new SettleOrderRequest.Item.SupplierItem();
        supplierItem.setSpuId(492237431495819264L);
        supplierItem.setSkuId(492237431495819265L);
        supplierItem.setAreaItemId(493325003116314624L);
        supplierItem.setCarId("local_484063576146575362_15580493955_-1_493325003116314624_492237431495819265_492237431495819264_1");
        supplierItem.setCount(1L);
        supplierItem.setUnit("41");
        supplierItem.setUnitSize(1);
        supplierItem.setGoodsType(0);
        supplierItemList.add(supplierItem);

        item.setSupplierItems(supplierItemList);
        items.add(item);
        request.setItems(items);

        orderService.saveOrder(memberId, branchId, request);
    }

    @Test
    public void testA(){
        DcDTO dcDTO = new DcDTO();
        dcDTO.setGlobalMinAmt(BigDecimal.valueOf(0.5));
        dcDTO.setMinAmt(BigDecimal.valueOf(0.3));
        boolean isCheckDcGlobalMinAmt = true;
        boolean isCheckDcMinAmt = true;
        AtomicReference<Integer> orderType = new AtomicReference<>();
        orderType.set(1);
        BigDecimal orderTotalAmt = BigDecimal.valueOf(0.52);
        //运营商起送价校验
        if (Objects.equals(orderType.get(), NumberPool.INT_ZERO) && isCheckDcGlobalMinAmt) { // 验证全国起送价
            Assert.isTrue(orderTotalAmt.compareTo(ToolUtil.isEmptyReturn(dcDTO.getGlobalMinAmt(), BigDecimal.ZERO)) >= 0,
                    "运营商({})全国最低配送金额({}),本次购买金额({}) ", dcDTO.getDcName(), dcDTO.getGlobalMinAmt(), orderTotalAmt);
        } else if (Objects.equals(orderType.get(), NumberPool.INT_ONE) && isCheckDcMinAmt) { // 验证本地起送价
            Assert.isTrue(orderTotalAmt.compareTo(ToolUtil.isEmptyReturn(dcDTO.getMinAmt(), BigDecimal.ZERO)) >= 0,
                    "运营商({})本地最低配送金额({}),本次购买金额({}) ", dcDTO.getDcName(), dcDTO.getMinAmt(), orderTotalAmt);
        }
    }
}