package com.zksr.portal.controller;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.zksr.member.api.member.dto.MemMemberAddressPageReqDTO;
import com.zksr.member.api.member.dto.MemMemberAddressSaveReqDTO;
import com.zksr.portal.ZksrPortalApplication;
import com.zksr.portal.controller.mall.MemberAddressController;
import com.zksr.trade.api.order.vo.HomePagesCurrentSalesDataReqVO;
import com.zksr.trade.api.order.vo.SupplierOrderCountAllPageReqVO;
import com.zksr.trade.api.order.vo.SupplierOrderCountPageReqVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * @Author: chenyj8
 * @Desciption: 测试类
 */
@Slf4j
@ActiveProfiles({"dev"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZksrPortalApplication.class)
public class MemberAddressControllerTest {
//    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private MemberAddressController memberAddressController;

    @Autowired
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setup() {
        mockMvc = MockMvcBuilders.standaloneSetup(memberAddressController)
                .setValidator(new LocalValidatorFactoryBean())
                .build();
    }

    @Test
    public void testAddMemberAddress() throws Exception {
        MemMemberAddressSaveReqDTO createReqDTO = new MemMemberAddressSaveReqDTO();
        createReqDTO.setMemberId(460325218875572224L);
        createReqDTO.setDeliveryAddress("cyj测试地址");
        createReqDTO.setThreeAreaCityId(27L);
        createReqDTO.setProvinceName("广东省");
        createReqDTO.setCityName("佛山市");
        createReqDTO.setDistrictName("顺德区");

        mockMvc.perform(post("/mall/address/addMemberAddress")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }



    @Test
    public void testUpdateMemberAddress() throws Exception {
        MemMemberAddressSaveReqDTO createReqDTO = new MemMemberAddressSaveReqDTO();
        createReqDTO.setId(1L);
        createReqDTO.setMemberId(460325218875572224L);
        createReqDTO.setDeliveryAddress("cyj测试地址");
        createReqDTO.setThreeAreaCityId(27L);
        createReqDTO.setProvinceName("广东省更新");
        createReqDTO.setCityName("佛山市");
        createReqDTO.setDistrictName("顺德区");

        mockMvc.perform(post("/mall/address/updateMemberAddress")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReqDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }

    @Test
    public void testGetMemberAddress() throws Exception {
        MemMemberAddressPageReqDTO pageReqVO = new MemMemberAddressPageReqDTO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(100);
        pageReqVO.setMemberId(460325218875572224L);
//        MemMemberAddressSaveReqDTO createReqDTO = new MemMemberAddressSaveReqDTO();
//        createReqDTO.setId(1L);
//        createReqDTO.setMemberId(460325218875572224L);
//        createReqDTO.setDeliveryAddress("cyj测试地址");
//        createReqDTO.setThreeAreaCityId(27L);
//        createReqDTO.setProvinceName("广东省更新");
//        createReqDTO.setCityName("佛山市");
//        createReqDTO.setDistrictName("顺德区");

        mockMvc.perform(post("/mall/address/getMemberAddress")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(pageReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0)) // 假设CommonResult的code为0表示成功
//                .andExpect(jsonPath("$.data").isNumber())
                .andDo(MockMvcResultHandlers.print());
    }

}