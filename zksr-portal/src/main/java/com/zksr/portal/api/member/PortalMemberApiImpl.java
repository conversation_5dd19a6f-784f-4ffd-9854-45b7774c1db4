package com.zksr.portal.api.member;

import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.InnerAuth;
import com.zksr.portal.api.dto.BranchRegisterReqDTO;
import com.zksr.portal.api.dto.BranchRegisterRespDTO;
import com.zksr.portal.controller.mall.vo.RegisterReq;
import com.zksr.portal.convert.member.MemberConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IIndexService;
import com.zksr.system.api.partner.dto.PartnerDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@ApiIgnore
@InnerAuth
@Slf4j
public class PortalMemberApiImpl implements PortalMemberApi {
    @Autowired
    private IIndexService indexService;
    @Resource
    private IPortalCacheService portalCacheService;
    @Override
    public CommonResult<BranchRegisterRespDTO> registerBranch(@RequestBody BranchRegisterReqDTO request) {
        log.info(" erp门店注册入参,{}", JsonUtils.toJsonString(request));
        BranchRegisterRespDTO resp = null;
        try {
            if(null == request){
                return CommonResult.error(new ServiceException("门店注册参数为空!"));
            }
            RegisterReq registerReq = MemberConvert.INSTANCE.convert2RegisterReq(request);
            //匹配省市区，找城市区域
            if(StringUtils.isNotEmpty(registerReq.getProvinceName()) &&
                    StringUtils.isNotEmpty(registerReq.getCityName()) &&
                    StringUtils.isNotEmpty(registerReq.getDistrictName())){

            }
            if(null != request.getSysCode()){
                PartnerDto partnerDto = portalCacheService.getPartnerDto(String.valueOf(request.getSysCode()));
                if(null != partnerDto){
                    registerReq.setSysSource(partnerDto.getSource());
                }
            }
            if(StringUtils.isEmpty(registerReq.getPassword())){
                registerReq.setPassword(UserConstants.DEFAULT_PASSWORD);
            }
            resp = MemberConvert.INSTANCE.convert2BranchRegisterRespDTO(indexService.register(registerReq));
        } catch (ServiceException e) {
            throw new ServiceException(e.getMessage());
        } catch (Exception e){
            log.error("erp门店注册失败,",e);
            throw new RuntimeException(e.getMessage());
        } finally {

        }

        return CommonResult.success(resp);
    }

}
