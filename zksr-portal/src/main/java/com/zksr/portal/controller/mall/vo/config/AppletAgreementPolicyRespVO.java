package com.zksr.portal.controller.mall.vo.config;

import com.zksr.common.core.pool.StringPool;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 小程序返回参数
 * @date 2024/6/14 14:33
 */
@Data
@ApiModel(description = "小程序系统配置")
public class AppletAgreementPolicyRespVO extends AppletAgreementPolicyDTO {

    @ApiModelProperty("小程序公众号appId")
    private String publishAppId;

    @ApiModelProperty("公众号名称")
    private String publishAccountName;

    @ApiModelProperty("appVersion使用这个来判断版本是否需要更新, 使用时间戳填充")
    private String appVersion;

    @ApiModelProperty("这个只是app显示更新版本")
    private String appVersionName;

    @ApiModelProperty("appVersion使用这个来判断版本是否需要更新, 使用时间戳填充(IOS)")
    private String iosVersion;

    @ApiModelProperty("这个只是app显示更新版本(IOS)")
    private String iosVersionName;

    @ApiModelProperty("app版本更新内容 (安卓)")
    private String appVersionContent;

    @ApiModelProperty("app版本更新内容 (IOS)")
    private String iosVersionContent;

    @ApiModelProperty("app更新包位置, 仅支持安卓")
    private String appUpdateUrl;

    @ApiModelProperty("是否强制更新,0-不强制, 1-强制更新 (安卓)")
    private String forceUpdate;

    @ApiModelProperty("是否强制更新,0-不强制, 1-强制更新 (IOS)")
    private String iosForceUpdate;

    /**
     * 当前平台是否允许销售全国本地,0-默认全部允许, 1-只允许本地, 2-只允许全国
     */
    @ApiModelProperty("当前平台是否允许销售全国本地, 0-默认全部允许, 1-只允许本地, 2-只允许全国")
    private String saleClassSwitch = "0";

    @ApiModelProperty("是否开启钱包支付, 0-关闭, 1-开启")
    private String switchWalletPay;

    @ApiModelProperty("是否开启在线支付, 0-关闭, 1-开启")
    private String switchStoreOrderPay = StringPool.ONE;

    @ApiModelProperty("是否展示中科标签 0 展示 1 不展示")
    private String zksrShowLabel;

}
