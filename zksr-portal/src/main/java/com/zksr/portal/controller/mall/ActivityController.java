package com.zksr.portal.controller.mall;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.portal.controller.mall.vo.activity.ActivityDetailRespVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityFgBgDetailVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageReqVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageRespVO;
import com.zksr.portal.service.mall.IActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动接口
 * @date 2024/5/24 8:40
 */
@RestController
@RequestMapping("/mall/activity")
@Api(tags = "商城 - 促销接口")
public class ActivityController {

    @Autowired
    private IActivityService activityService;

    @GetMapping("/getFgOrBgActivityDetail")
    @ApiOperation(value = "获取买赠或者满赠促销详情", httpMethod = HttpMethod.GET)
    @RequiresMallLogin(isMember = true)
    public CommonResult<ActivityFgBgDetailVO> getFgOrBgActivityDetail(
            @RequestParam("activityId") @ApiParam(name = "activityId", value = "促销活动ID", required = true) Long activityId) {
        return CommonResult.success(activityService.getFgOrBgActivityDetail(activityId));
    }

    @GetMapping("/getActivityDetail")
    @ApiOperation(value = "获取通用促销活动详情", httpMethod = HttpMethod.GET)
    @RequiresMallLogin(isMember = true)
    public CommonResult<ActivityDetailRespVO> getActivityDetail(
            @RequestParam("activityId") @ApiParam(name = "activityId", value = "促销活动ID", required = true) Long activityId) {
        return CommonResult.success(activityService.getActivityDetail(activityId));
    }

    @GetMapping("/getItemsByActivityIds")
    @ApiOperation(value = "根据活动id获取商品", httpMethod = HttpMethod.GET)
    //@RequiresMallLogin(isMember = true)
    public CommonResult<List<ActivityItemPageRespVO>> getItemsByActivityIds(@Valid ActivityItemPageReqVO pageReqVO) {
        return CommonResult.success(activityService.getItemsByActivityIds(pageReqVO));
    }
}
