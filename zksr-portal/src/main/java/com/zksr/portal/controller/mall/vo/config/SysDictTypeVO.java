package com.zksr.portal.controller.mall.vo.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "字典对象 请求")
public class SysDictTypeVO {

    @ApiModelProperty(value = "字典KEY", required = true)
    List<String> dictTypeList;

    @ApiModelProperty(value = "平台来源", required = true)
    private String sysSource;

}
