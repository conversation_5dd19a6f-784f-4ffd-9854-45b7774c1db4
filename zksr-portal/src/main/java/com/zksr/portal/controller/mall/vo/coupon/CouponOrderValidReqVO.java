package com.zksr.portal.controller.mall.vo.coupon;

import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 获取下单有效优惠券
 * @date 2024/4/3 9:30
 */
@Data
@ApiModel(description = "请求下单可用优惠券信息")
public class CouponOrderValidReqVO {

    @ApiModelProperty(value = "门店ID", hidden = true, notes = "前端不需要传递, 后台从token从解析")
    private Long branchId;

    @Size(min = 1, max = 1999, message = "验证商品数组不能为空")
    @ApiModelProperty(value = "订单商品信息", required = true)
    private List<OrderValidItemDTO> items;

 }
