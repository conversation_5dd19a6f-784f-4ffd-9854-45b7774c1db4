package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class TradePriceCalculateResp {

    /**
     * 订单价格
     */
    private Price price;

    /**
     * 订单项数组
     */
    private List<OrderItem> items;

    /**
     * 营销信息 数组
     */
    private List<Promotion> promotions;

    /**
     * 促销活动提示信息
     */
    private String activityTipsInfo;

    /**
     * 订单价格
     */
    @Data
    public static class Price {

        /**
         * 商品原价（总），单位：分
         *
         * 基于 {@link OrderItem.SupplierItem#getPrice()} * {@link OrderItem.SupplierItem#getCount()} 求和
         *
         * 对应 taobao 的 trade.total_fee 字段
         */
        private BigDecimal totalPrice;
        /**
         * 最终购买金额（总），单位：分
         */
        private BigDecimal payPrice;

        /**
         * 最终优惠金额（总），单位：分
         */
        private BigDecimal totalDiscountPrice;

        /**
         * 活动优惠金额（总），单位：分
         */
        private BigDecimal totalActivityDiscountPrice;

        /**
         * 优惠券优惠金额（总），单位：分
         */
        private BigDecimal totalCouponDiscountPrice;

    }

    /**
     * 订单商品 SKU
     */
    @Data
    public static class OrderItem {

        /**
         * 入驻商id
         */
        private Long supplierId;

        /**
         * 入驻商名
         */
        private String supplierName;

        //备注
        private String memo;

        /**
         * 入驻商配送标签, 来自平台商字典
         */
        private String productDistributionLabel;

        /**
         * 是否开启钱包支付, 0-关闭, 1-开启 默认为0
         */
        private String switchWalletPay = StringPool.ZERO;;

        /**
         * 商品原价（入驻商总计），单位：分
         */
        private BigDecimal totalAmt;
        /**
         * 最终购买金额（入驻商总计），单位：分
         */
        private BigDecimal payAmt;
        /**
         * 最终优惠金额（入驻商总计），单位：分
         */
        private BigDecimal discountAmt;

        /**
         * 入驻商商品总数量
         */
        private Integer totalNum;

        /**
         * 入驻商-购物项数组
         */
        private List<SupplierItem> supplierItems;

        /**
         * 购物项
         */
        @Data
        public static class SupplierItem {

            // ========== SPU 信息 ==========

            /**
             * 分类编号
             */
            private Long categoryId;

            /**
             * SPU 编号
             */
            private Long spuId;

            /**
             * 商品名
             */
            private String spuName;

            /**
             * SPU编码
             */
            private String spuNo;

            /**
             * 商品图片 图片地址
             * <p>
             * 优先级：SKU.picUrl > SPU.picUrl
             */
            private String picUrl;

            /**
             * 封面视频地址
             * <p>
             * 优先级：SKU.picUrl > SPU.picUrl
             */
            private String videoUrl;

            /**
             * 商品规格
             */
            private String specName;

            // ========== SKU 信息 ==========
            /**
             * SKU 编号
             */
            private Long skuId;

            /**
             * 商品SKU条码
             */
            private String barcode;

            /** 最旧生产日期 */
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private Date oldestDate; 		 // 最旧生产日期

            /** 最新生产日期 */
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private Date latestDate;

            /**
             * 商品现价价（单），单位：分
             * <p>
             * 对应 ProductSkuDO 的 price 字段
             * 对应 taobao 的 order.price 字段
             */
            private BigDecimal price;


            /**
             * 现下单单位原销售价
             */
            private BigDecimal salePrice;

            /**
             * 最小单位原销售价
             */
            private BigDecimal minSalePrice;

           //===============================商品单位信息===================================
            /** 订单购买单位 */
            private String unit;

            /** 购买单位大小 */
            private Integer unitSize;

            /** 订单购买换算(最小单位)数量 */
            private BigDecimal orderUnitSize ;



            /**
             * 应付金额（总），单位：分
             */
            private BigDecimal payPrice;

            /**
             * 商品原总金额（总），单位：分
             */
            private BigDecimal subOrderAmt;

            /**
             * 属性信息
             */
            private String properties;

            // ========== 购物车信息 ==========
            /**
             * 购物车项的编号
             */
            private String carId;

            /**
             * 购买数量
             */
            private Integer count;

            /**
             * 活动数量
             */
            private Integer activityCount;
            /**
             * 是否选中
             */
            private Boolean selected;

            /**
             * 加单指令ID
             */
            private Long commandId;

            // ========== 商品上架信息 ==========
            /**
             * 入驻商商品上架ID
             */
            private Long supplierItemId;
            /**
             * 城市商品上架ID
             */
            private Long areaItemId;

            /**
             * 商品类型 0：全国商品 1：本地商品
             */
            private Integer itemType;


            // ================= 优惠劵验证时需使用字段 =================
            /** 展示类目ID */
            private Long classId;
            /** 品牌ID*/
            private Long brandId;
            /**
             * 商品类型
             * {@link ProductType} type
             */
            private String productType;

            // ================= 优惠信息 =================
            /**
             * 优惠劵优惠金额（入驻商），单位：分 （分摊）
             */
            private BigDecimal couponDiscountAmt;

            /**
             * 优惠劵优惠金额（入驻商），单位：分（不分摊）
             */
            private BigDecimal couponDiscountAmt2;

            /**
             * 活动优惠金额（入驻商），单位：分
             */
            private BigDecimal activityDiscountAmt;

            /**
             * 是否赠品  1：是 0： 否
             */
            private Integer giftFlag;

            /**
             * 活动单价
             */
            private BigDecimal activityPrice;

            /**
             * 是否参与秒杀、特价活动  1：是 0： 否
             */
            private Integer isActivitySpSk;

            /**
             *  唯一值
             */
            private String uuIdNo;

            // ================= 赠品商品价格分摊使用 =================

            /**
             *  活动ID信息（目前只有赠品活动使用，如果这个商品同时参加了满赠和买赠  id使用；分隔）
             */
            private String activityIdInfo = StringPool.EMPTY;

            /**
             * 商品类型 0-普通商品, 1-组合商品
             */
            private Integer goodsType;

            /**
             * 组合商品ID （默认为 -1）
             */
            private Long spuCombineId = NumberPool.LOWER_GROUND_LONG;

            /**
             * 组合商品明细数据
             */
            private List<SpuCombineSkuVO> spuCombineSkuVOList;

            @ApiModelProperty("是否支持负库存下单 0：否，1：是")
            private Integer isNegativeStock;
        }
    }

    /**
     * 营销信息
     */
    @Data
    public static class Promotion {

        /**
         * 入驻商编号
         */
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long supplierId;

        /**
         * 营销编号
         * 例如说：营销活动的编号、优惠劵的编号
         */
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long id;

        /**
         * 营销名字
         */
        private String name;
        /**
         * 营销类型
         *
         * 枚举 @link {TrdDiscountTypeEnum}
         */
        private String type;

        /**
         * 营销单据优惠总金额
         */
        private BigDecimal discountTotalPrice;

        /**
         * 匹配的商品 SKU 数组
         */
        private List<PromotionItem> promotionItems;

    }


    /**
     * 营销匹配的商品 SKU 优惠信息
     */
    @Data
    public static class PromotionItem {

        /**
         * 优惠券模板ID ,用于优惠劵报表
         */
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long couponTemplateId;

        /**
         * 商品 SKU 编号
         */
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long skuId;
        /**
         * 秒杀、特价数量、组合商品，用于订单保存后更新缓存
         */
        private Integer count;

        // =============金额优惠================
        /** 活动优惠金额(分摊的)(new) */
        private BigDecimal activityDiscountAmt;

        /** 优惠金额(分摊的)(new) */
        private BigDecimal couponDiscountAmt;

        /** 优惠金额(不分摊的)(new) */
        private BigDecimal couponDiscountAmt2;

        // =============赠品优惠================

        @ApiModelProperty(value = "活动规则id")
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long activityRuleId;

        /** 赠品类型;0-商品 1-优惠券 */
        private Integer giftType;

        /** 赠品sku;gift_type=0 则记录;gift_type=1 则记录 */
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long giftSkuId;

        /** 赠品sku优惠券模板 */
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long giftCouponTemplateId;

        /** 赠品数量 */
        private Integer giftQty;

        /** 赠品分组   同一个活动下只能选择一组赠品 */
        private Integer giftGroupNum;

        /**
         *  唯一值 {@link com.zksr.portal.controller.mall.vo.TradePriceCalculateResp.OrderItem.SupplierItem} 字段【uuIdNo】对应
         */
        private String uuIdNo;

        /** 赠品商品的 大中小规格 赠送商品时使用 1:小 2：中 3：大 */
        private Integer giftSkuUnitType;

        /** 赠品商品的 大中小规格换算数量，默认值为1 目前只用于核销活动【特价、秒杀】使用数量时使用  */
        private BigDecimal unitSizeQty = BigDecimal.ONE;

        /** 单位 1：小单位 2：中单位 3：大单位 */
        private Integer unitSize;

        /** 赠品商品的 原价格 （买赠、满赠使用） */
        private BigDecimal giftSkuUnitPrice = BigDecimal.ZERO;

        /**
         * 活动满足条件
         * 满足多少金额或数量参与促销 （目前只有满赠、买赠使用）
         */
        private String discountCondition;

        /**
         * 营销编号
         * 例如说：营销活动的编号、优惠劵的编号
         */
        private Long id;

    }


}
