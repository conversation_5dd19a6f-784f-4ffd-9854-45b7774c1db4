package com.zksr.portal.controller.mall;

/**
 * <AUTHOR>
 * @date 2024年04月20日 10:01
 * @description: 订单售后Controller
 */

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.SourceType;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.dto.AfterOrderPageRespDTO;
import com.zksr.trade.api.after.dto.OrderAfterResDTO;
import com.zksr.trade.api.after.dto.OrderAfterSaveResDTO;
import com.zksr.trade.api.after.vo.AfterOrderPageReqVO;
import com.zksr.trade.api.after.vo.OrderAfterRequest;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.after.vo.OrderAfterUploadExpressSaveVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@Api(tags = "商城 - 订单售后接口")
@RestController
@RequestMapping("/mall/orderAfter")
@Slf4j
public class OrderAfterController {
    @Autowired
    private AfterApi afterApi;

    /**
     * 售后接口-订单列表页跳售后提交页
     */
    @ApiOperation("售后接口-订单列表页跳售后提交页")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/orderAfter")
    public CommonResult<OrderAfterResDTO> orderAfter(@RequestBody OrderAfterRequest request){
        return success( afterApi.orderAfterInitiate(request).getCheckedData());
    }

    /**
     * 售后接口-售后订单提交
     */
    @ApiOperation("售后接口-售后订单提交")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/saveOrderAfter")
    public CommonResult<OrderAfterSaveResDTO> saveOrderAfter(@RequestBody OrderAfterSaveRequest request){
        request.setSource(SourceType.SHOPPINGMALL.getType());
        request.setCreateUserName(MallSecurityUtils.getLoginMember().getMember().getUserName());
        return success(afterApi.saveAfterOrder(request).getCheckedData());
    }


    @ApiOperation("售后接口-售后订单分页查询")
    @RequiresMallLogin(isMember = true)
    @GetMapping(value = "/pageAfter")
    public CommonResult<PageResult<AfterOrderPageRespDTO>> pageAfter(@Valid AfterOrderPageReqVO reqVO){
        reqVO.setBranchId(MallSecurityUtils.getBranchId());
//        reqVO.setMemberId(MallSecurityUtils.getMemberId());
        return success(afterApi.pageAfter(reqVO));
    }



    @ApiOperation("售后接口-售后订单上传物流单号提交")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/uploadAfterExpressDelivery")
    public CommonResult<Boolean> uploadAfterExpressDelivery(@RequestBody OrderAfterUploadExpressSaveVO expressSaveVO){
        return success(afterApi.uploadAfterExpressDelivery(expressSaveVO).getCheckedData());
    }

    @ApiOperation(value = "取消售后单退货", httpMethod = HttpMethod.PUT)
    @RequiresMallLogin(isMember = true)
    @PutMapping("/cancelAfterReturn/{afterId}")
    public CommonResult<Boolean> cancelAfterReturn(@ApiParam(name = "afterId", value = "售后订单ID", required = true) @PathVariable("afterId") Long afterId) {
        afterApi.cancelAfterReturn(afterId).getCheckedData();
        return success(true);
    }
}
