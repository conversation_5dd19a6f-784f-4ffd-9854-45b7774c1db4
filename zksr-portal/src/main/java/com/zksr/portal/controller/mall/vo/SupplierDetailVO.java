package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @date 2024/5/15 10:08
 */
@Data
@ApiModel("商城-入驻商信息 VO对象")
public class SupplierDetailVO {
    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 入驻商编号 */
    @ApiModelProperty(value = "入驻商编号")
    private String supplierCode;

    /** 入驻商名称 */
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 关注人数 */
    @ApiModelProperty(value = "关注人数")
    private String numberOfFollowers;

    /** 供应商图片 */
    @ApiModelProperty(value = "供应商图片")
    private String supplierImgUrl;

    /** 店铺公告 */
    @ApiModelProperty(value = "店铺公告")
    private String announcement;

    /** 入驻商头像地址 */
    @ApiModelProperty(value = "入驻商头像地址")
    private String avatar;

    @ApiModelProperty("入驻商 - 暂停营业开始时间")
    private String shuttingStartTime;

    @ApiModelProperty("入驻商 - 暂停营业结束时间")
    private String shuttingEndTime;

    /** 入驻商配送标签, 来自平台商字典 */
    @ApiModelProperty(value = "入驻商配送标签, 来自平台商字典")
    private String productDistributionLabel;
}




