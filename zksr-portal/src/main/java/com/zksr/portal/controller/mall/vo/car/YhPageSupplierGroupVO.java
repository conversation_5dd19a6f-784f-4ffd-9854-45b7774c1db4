package com.zksr.portal.controller.mall.vo.car;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyResultDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class YhPageSupplierGroupVO {

    @ApiModelProperty("入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty("活动检验商品列表,处理逻辑使用")
    private List<ActivityVerifyItemDTO> activityVerifyItemDtoList = new ArrayList<>();

    @ApiModelProperty("当前入驻商所有活动列表，处理逻辑使用")
    private List<SupplierActivityDTO> serviceSupplierActivityList = new ArrayList<>();

    @ApiModelProperty("活动列表对象")
    private ActivityVerifyResultDTO verifyResultDTO;
}
