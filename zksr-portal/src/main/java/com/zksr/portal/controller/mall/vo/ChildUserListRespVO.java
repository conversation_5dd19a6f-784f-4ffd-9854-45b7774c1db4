package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
*
 *   子用户列表 响应实体
* <AUTHOR>
* @date 2024/4/25 10:32
*/
@Data
@ApiModel("子用户列表 响应实体")
public class ChildUserListRespVO {

    //用户名  memberName 必填
    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    private String memberName;

    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    private String userName;

    /** 状态：1启用  0禁用 */
    @ApiModelProperty(value = "状态：1启用  0禁用")
    private Integer status;


}
