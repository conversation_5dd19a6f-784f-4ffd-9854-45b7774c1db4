package com.zksr.portal.controller.mall.vo.order;

import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 支付请求
 * @date 2024/5/29 11:05
 */
@Data
@ApiModel("小程序支付请求")
public class PotalPayOrderSubmitReqVO extends PayOrderSubmitReqVO {

    @ApiModelProperty("平台来源")
    private String sysSource;

}
