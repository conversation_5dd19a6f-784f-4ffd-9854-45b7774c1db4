package com.zksr.portal.controller.mall;

import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.annotation.RequiresPermissions;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.portal.controller.mall.vo.order.ShareOrderInfoReqVO;
import com.zksr.portal.service.mall.IProductShareService;
import com.zksr.product.api.share.ProductShareApi;
import com.zksr.product.api.share.dto.BatchProductShareDTO;
import com.zksr.product.api.share.dto.PrdtProductSharePackageInfoRespDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import com.zksr.trade.api.order.vo.TrdOrderShareRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_ORDER_SHARE_EXPIRATION;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_ORDER_SHARE_NONE;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 白名单接口, 无需token, 注意此路径下的接口默认全部开放白名单
 * @date 2024/10/29 15:51
 */
@RestController
@RequestMapping("/mall/public")
@Api(tags = "商城 - 开放接口")
@Slf4j
public class PublicController {

    @Resource
    private OrderApi orderApi;

    @Autowired
    private IProductShareService productShareService;
    /**
     * @Description: 获取订单详情
     * @Param: Long orderId
     * @return: CommonResult<OrderVO>
     * @Author: chenmingqing
     * @Date: 2024/7/31 9:19
     */
    @GetMapping("/getShareOrderInfo")
    @ApiOperation(value = "获取用户分享订单详情", httpMethod = HttpMethod.GET)
    public CommonResult<TrdOrderRespDTO> getOrderInfo(@Valid ShareOrderInfoReqVO reqVO){
        TrdOrderShareRespVO shareRespVO = orderApi.getShareOrder(reqVO.getShareKey()).getCheckedData();
        if (Objects.isNull(shareRespVO)) {
            throw exception(TRD_ORDER_SHARE_NONE);
        }
        if (shareRespVO.getExpirationTime().before(DateUtil.date())) {
            throw exception(TRD_ORDER_SHARE_EXPIRATION);
        }

        TrdOrderPageReqDTO reqDTO = new TrdOrderPageReqDTO();
        reqDTO.setBranchId(shareRespVO.getBranchId());
        reqDTO.setOrderId(shareRespVO.getOrderId());
        reqDTO.setSupplierOrderId(shareRespVO.getSupplierOrderId());
        return orderApi.getOrderInfo(reqDTO);
    }


    @GetMapping("/getShareProductInfo")
    @ApiOperation(value = "根据分享Key获取分享商品信息", httpMethod = HttpMethod.GET)
    public CommonResult<List<PrdtProductSharePackageInfoRespDTO>> getShareProductInfo(ShareOrderInfoReqVO reqVO) {
        return CommonResult.success(productShareService.getShareProductInfo(reqVO.getShareKey()));
    }
}
