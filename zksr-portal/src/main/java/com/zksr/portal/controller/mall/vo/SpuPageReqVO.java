package com.zksr.portal.controller.mall.vo;


import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.core.enums.ProductSortType;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import java.util.List;

@Data
@ApiModel("商城-SPU详情 VO对象")
public class SpuPageReqVO extends PageParam {

    public static final String SORT_FIELD_PRICE = "price";
    public static final String SORT_FIELD_SALES_COUNT = "salesCount";


    /** 展示三级分类ID */
    @ApiModelProperty("展示三级分类ID")
    private List<Long> class3Id;

    /** 展示二级分类ID */
    @ApiModelProperty("展示二级分类ID")
    private List<Long> class2Id;

    /** 展示一级分类ID */
    @ApiModelProperty("展示一级分类ID")
    private List<Long> class1Id;

    /** 管理分类 */
    @ApiModelProperty("catgoryId")
    private List<Long> catgoryId;


    @ApiModelProperty(value = "入驻商id", example = "1")
    private List<Long> supplierId;

    @ApiModelProperty(value = "关键字", example = "好看")
    private String condition;

    /** 排序类型 */
    @ApiModelProperty("排序类型: none-无, sale-销量, price-价格")
    private String sortType = StringPool.NONE;

    /** 排序方式 @com.zksr.common.core.enums.ProductSortType */
    @ApiModelProperty("排序方式: none-无,des-降序, asc-升序")
    private String orderBy = StringPool.NONE;


    /** 默认-1, 兼容全国和本地商品售后 */
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long areaId;

    /** 默认-1, 本地渠道 */
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long channelId;

    /** 默认-1, 兼容全国和本地商品 平台商城市分组id */
    @ApiModelProperty("区域城市默认-1, 兼容全国和本地商品售后")
    private Long groupId;

    /** 是否启用经营屏蔽, 0-否, 1-是 */
    @ApiModelProperty(value = "是否启用经营屏蔽, 0-否, 1-是")
    private Integer enableBlock = 0;

    @AssertTrue(message = "排序字段不合法")
    @JsonIgnore
    public boolean isSortFieldValid() {
        if (StrUtil.isEmpty(sortType)) {
            return true;
        }
        return StrUtil.equalsAny(sortType, ProductSortType.NONE.getSortType(), ProductSortType.PRICE.getSortType()
                ,ProductSortType.SALE.getSortType(),ProductSortType.SORT_NUM.getSortType(),ProductSortType.SALE_PRICE.getSortType());
    }

//    @ApiModelProperty(value = "推荐类型", example = "hot") // 参见 AppProductSpuPageReqVO.RECOMMEND_TYPE_XXX 常量
//    private String recommendType;


}
