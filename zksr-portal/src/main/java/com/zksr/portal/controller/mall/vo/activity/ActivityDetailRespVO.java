package com.zksr.portal.controller.mall.vo.activity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销详情数据
 * @date 2025/2/22 8:59
 */
@Data
public class ActivityDetailRespVO {

    /** 活动id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("促销活动ID")
    private Long activityId;

    /** 全国或者本地(数据字典);1-全国商品可用（平台商设定）2-本地商品可用（运营商设定） */
    @Excel(name = "全国或者本地(数据字典);1-全国商品可用", readConverterExp = "平=台商设定")
    @ApiModelProperty("1-全国商品可用（平台商设定）2-本地商品可用（运营商设定）")
    private Integer funcScope;

    /** 入驻商id */
    @Excel(name = "入驻商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("入驻商id")
    private Long supplierId;

    /** 促销类型（数据字典）;SK-秒杀 BG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价 */
    @Excel(name = "促销类型", readConverterExp = "数=据字典")
    @ApiModelProperty("SK-秒杀 BG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价")
    private String prmNo;

    /** 活动名称 */
    @Excel(name = "活动名称")
    @ApiModelProperty("活动名称")
    private String activityName;

    /** 活动开始时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @Excel(name = "活动开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("活动开始时间")
    private Date startTime;

    /** 活动结束时间 */
    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = "Asia/Shanghai")
    @Excel(name = "活动结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("活动结束时间")
    private Date endTime;

    /** 商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品） */
    @Excel(name = "商品适用范围(数据字典);0-所有商品可用", readConverterExp = "全=场")
    @ApiModelProperty("商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品）")
    private Integer spuScope;

    /** 是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) 满赠:(1-满赠 0-每赠)*/
    @Excel(name = "是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) 满赠:(1-满赠 0-每赠)")
    @ApiModelProperty("是否阶梯 1-是 0-否;买赠:(1-满赠 0-每赠)  满减:(1-满减 0-每减) 满赠:(1-满赠 0-每赠)")
    private Integer ladderFlag;

    /** 参与活动次数限制规则（数据字典）;仅特价限购和满赠，0-每日一次 1-这个活动只能参加一次  2-仅活动期间内首单(作废) 3-系统首单*/
    @Excel(name = "参与活动次数限制规则", readConverterExp = "数=据字典")
    @ApiModelProperty("参与活动次数限制规则（数据字典）;仅特价限购和满赠，0-每日一次 1-这个活动只能参加一次  2-仅活动期间内首单(作废), 3-系统首单")
    private Integer timesRule;

    /** 活动优惠类型;仅满减，0-金额  1-数量 */
    @Excel(name = "活动优惠类型;仅满减，0-金额  1-数量")
    @ApiModelProperty("活动优惠类型;仅满减，0-金额  1-数量")
    private Integer amtOrQty;

    @ApiModelProperty("规则")
    private List<Object> rules;

    @ApiModelProperty("促销活动参与范围")
    private List<ActivitySpuScopeDTO> spuScopeList;
}
