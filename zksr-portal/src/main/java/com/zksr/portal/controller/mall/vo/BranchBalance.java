package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(description = "门店余额")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BranchBalance implements Serializable {

    private static final long serialVersionUID = -4704526293415228736L;

    @ApiModelProperty(value = "是否开启余额管理/支付(0:关闭，1:开启，默认为关闭)")
    private String openBalancePay;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "门店余额账户id")
    private Long balanceId;

    @ApiModelProperty(value = "门店账户余额金额")
    private BigDecimal balanceAmt = BigDecimal.ZERO;

}
