package com.zksr.portal.controller.mall.vo.car;

import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车分页对象
 * @date 2024/3/26 19:35
 */
@Data
@ApiModel(description = "购物车分页对象")
public class CarPageReqVO extends PageParam {
    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @ApiModelProperty(value = "商品类型 local-本地商品,retail-零售商品, global-全国商品", required = true, example = "local")
    private String productType = ProductType.LOCAL.getType();

    @ApiModelProperty("忽略欠款选中, 默认-false, 欠款入驻商商品不会下发选中")
    private Boolean ignoreDebt = false;
}
