package com.zksr.portal.controller.mall.vo.spu;

import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 店铺集合展示, 简单商品数据
 * @date 2024/9/10 15:13
 */
@Data
@ApiModel(description = "店铺集合展示, 简单商品数据")
public class SupplierListSpuReqVO {

    @ApiModelProperty("入驻商ID集合")
    @Size(min = 1, max = 100, message = "最大搜索100个入驻商")
    private List<Long> supplierIdList;

    @ApiModelProperty("每个入驻商展示多少个商品数量")
    @Max(value = 20, message = "最大展示20条数据")
    private Integer size;

    @ApiModelProperty(value = "关键字")
    private String condition;

    @ApiModelProperty(value = "平台编码", hidden = true)
    private String sysSource;
}
