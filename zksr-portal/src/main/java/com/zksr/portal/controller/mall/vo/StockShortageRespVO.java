package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 库存不足响应VO
 * @Date: 2025/07/15
 */
@Data
@Accessors(chain = true)
@ApiModel("库存不足响应VO")
public class StockShortageRespVO {

    @ApiModelProperty("库存不足商品列表")
    private List<StockShortageItemVO> shortageItems;

    /**
     * 库存不足商品项
     */
    @Data
    @Accessors(chain = true)
    @ApiModel("库存不足商品项")
    public static class StockShortageItemVO {

        @ApiModelProperty("商品编码")
        private String itemNo;

        @ApiModelProperty("skuId")
        private String skuId;

        @ApiModelProperty("spuId")
        private String spuId;

        @ApiModelProperty("商品名称")
        private String spuName;

        @ApiModelProperty("商品图片")
        private String thumb;

        @ApiModelProperty("需求数量")
        private BigDecimal requestQty;

        @ApiModelProperty("可用库存数量")
        private BigDecimal availableQty;
    }
}
