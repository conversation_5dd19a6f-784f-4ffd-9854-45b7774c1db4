package com.zksr.portal.controller.mall.vo.car;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新增购物车商品
 * @date 2024/3/26 16:51
 */
@Data
@ApiModel(description = "选择商品数据")
public class CarSelectedItemVO extends AppCarIdDTO {

    @ApiModelProperty("商品数量")
    private Integer productNum;

    @ApiModelProperty("单价")
    private BigDecimal salePrice;

    @ApiModelProperty(value = "unit, 单位")
    private String unit = StringPool.ZERO;

    @ApiModelProperty("是否特价商品,0-正常,1-特价商品")
    public Integer isActivityspsk = NumberPool.INT_ZERO;

    @ApiModelProperty(value = "组合商品ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuCombineId;

    @ApiModelProperty(value = "促销活动ID, 只有组合商品才有直接关联的促销ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    @ApiModelProperty(value = "指令ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long commandId;

    @ApiModelProperty(value = "商品类型,0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    @JsonIgnore
    @ApiModelProperty(value = "加入购物车时间,毫秒值")
    private Long addTime;

    @JsonIgnore
    public boolean isSpuCombineProduct() {
        return Objects.nonNull(spuCombineId);
    }

    public static CarSelectedItemVO build(AppCarIdDTO carId) {
        return BeanUtil.toBean(carId, CarSelectedItemVO.class);
    }

}
