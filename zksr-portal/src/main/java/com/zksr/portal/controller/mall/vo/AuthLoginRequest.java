package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("authLogin 接口请求")
public class AuthLoginRequest {

    @ApiModelProperty(value = "微信头像 选填")
    private String avatar;//微信头像

    @ApiModelProperty(value = "微信昵称 选填")
    private String memberName;//微信昵称

    @ApiModelProperty(value = "code  必填")
    @NotBlank(message = "code不能为空")
    private String code;

    @ApiModelProperty(value = "openid  必填")
    @NotBlank(message = "openid不能为空")
    private String openid;

    @ApiModelProperty(value = "unionid 选填")
    //@NotBlank(message = "unionid不能为空")
    private String unionid;

    @ApiModelProperty(value = "手机号  必填")
    @NotBlank(message = "mobile不能为空")
    private String mobile;//手机号

    @ApiModelProperty(value = "sysSource  必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;

    //@NotBlank(message = "isNew不能为空")
    @ApiModelProperty(value = "是否新用户：1是0否  必填")
    @NotNull(message = "isNew不能为空")
    private String isNew;//是否新用户：1是0否

}
