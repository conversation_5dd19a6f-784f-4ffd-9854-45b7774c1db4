package com.zksr.portal.controller.mall.vo.car;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车门店选中数据状态
 * @date 2024/3/26 19:16
 */
@Data
@ApiModel(description = "购物车门店选中数据状态")
public class CarCountStatusRespVO {

    @ApiModelProperty("选中的carId")
    private List<CarSelectedItemStatusVO> selectedCarId;

    public CarCountStatusRespVO() {
    }

    public CarCountStatusRespVO(List<CarSelectedItemStatusVO> selectedCarId) {
        this.selectedCarId = selectedCarId;
    }
}
