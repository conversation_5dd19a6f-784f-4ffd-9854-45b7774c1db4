package com.zksr.portal.controller.mall.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/7 9:23
 */
@ApiModel(description = "获取优惠券模版数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponTemplateFragmentReqVO {

    @ApiModelProperty(value = "优惠券ID集合", required = true)
    @Size(min = 1, max = 20, message = "最小1最大20个优惠券模版ID")
    private List<Long> couponTemplateIdList;

    @ApiModelProperty(value = "每个优惠券展示多少个片段商品信息")
    @Range(min = 1L, max = 30L, message = "最小1最大30个商品数据")
    private Integer size;

    @ApiModelProperty("平台商source")
    private String sysSource;
}
