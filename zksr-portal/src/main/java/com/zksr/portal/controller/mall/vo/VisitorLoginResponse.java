package com.zksr.portal.controller.mall.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("visitorLogin 接口响应")
public class VisitorLoginResponse {

    private String token;//生成的token码
    private Long expiresIn;//过期时间

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

}
