package com.zksr.portal.controller.mall.vo.yh;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/13 14:14
 */
@Data
@NoArgsConstructor
public class YhSupplierActivityVO {

    @ApiModelProperty("入驻商ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty("享受的促销活动")
    private List<ActivityLabelInfoVO> activityList = new ArrayList<>();

    public YhSupplierActivityVO(SupplierDTO supplierDTO, List<ActivityLabelInfoVO> activityList) {
        this.supplierId = supplierDTO.getSupplierId();
        this.supplierName = supplierDTO.getSupplierName();
        this.activityList = activityList;
    }
}
