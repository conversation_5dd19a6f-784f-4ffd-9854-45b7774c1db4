package com.zksr.portal.controller.mall.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 领取优惠券 - request
 * @date 2024/4/2 8:38
 */
@Data
@ApiModel("优惠券领取 - request VO 请求")
@Accessors(chain = true)
public class CouponReceiveReqVO {

    @ApiModelProperty(value = "优惠券模版ID列表", required = true)
    @Size(min = 1, max = 20, message = "优惠券模版ID长度必须在1-20之间")
    @NotNull
    private List<Long> couponTemplateIds;
}
