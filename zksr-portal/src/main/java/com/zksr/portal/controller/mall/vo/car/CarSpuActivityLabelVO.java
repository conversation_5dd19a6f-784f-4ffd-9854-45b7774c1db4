package com.zksr.portal.controller.mall.vo.car;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.activity.dto.BgRuleDTO;
import com.zksr.promotion.api.activity.dto.FdRuleDTO;
import com.zksr.promotion.api.activity.dto.FgRuleDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品关联促销活动标签
 * @date 2024/5/23 14:34
 */
@Data
@ApiModel(description = "商品关联促销活动标签")
public class CarSpuActivityLabelVO {

    /** 活动id */
    @TableId
    @JsonSerialize(using = CustomLongSerialize.class)
    @ApiModelProperty("促销活动ID")
    private Long activityId;

    /** 促销类型（数据字典）;SK-秒杀 BG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价 */
    @Excel(name = "促销类型", readConverterExp = "数=据字典")
    @ApiModelProperty("SK-秒杀 BG-买赠 FG-满赠 BL-限购 FD-满减 SP-特价")
    private String prmNo;

    /** 商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品） */
    @Excel(name = "商品适用范围(数据字典);0-所有商品可用", readConverterExp = "全=场")
    @ApiModelProperty("商品适用范围(数据字典);0-所有商品可用（全场） 2-指定品类可用（品类），3-指定品牌可用（品牌），4-指定商品可用（商品）")
    private Integer spuScope;

    /** 活动优惠类型;仅满减，0-金额  1-数量 */
    @Excel(name = "活动优惠类型;仅满减，0-金额  1-数量")
    @ApiModelProperty("活动优惠类型;仅满减，0-金额  1-数量")
    private Integer amtOrQty;

    @ApiModelProperty("规则")
    private List<Object> rules;
}
