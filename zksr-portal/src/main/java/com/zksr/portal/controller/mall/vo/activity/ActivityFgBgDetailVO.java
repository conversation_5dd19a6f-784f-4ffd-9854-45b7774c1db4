package com.zksr.portal.controller.mall.vo.activity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.activity.dto.ActivityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 买赠, 满赠促销规则详情VO
 * @date 2024/5/24 8:44
 */
@Data
@ApiModel(description = "买赠,满赠促销活动详情")
public class ActivityFgBgDetailVO extends ActivityDTO {

    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "活动范围别名, 如xxx类目, xxx品牌, xxx商品, 没有就是这个入驻商都可用")
    private List<String> scopeAsName;

    @ApiModelProperty(value = "促销规则详情")
    private List<Rule> ruleList = new ArrayList<>();

    @Data
    @ApiModel(description = "赠送条件")
    public static class Rule {

        @ApiModelProperty("满足条件, FG-是满赠是金额, BG-是买赠满赠条件类型为数量")
        private BigDecimal fullValue;

        /**
         * 购买品项数(sku种类数)
         */
        @ApiModelProperty("购买品项数(sku种类数)")
        private Integer buySkuNum;

        /**
         * 赠送方式(默认为全赠 0仅一种，1任选，2全赠)
         */
        @ApiModelProperty("赠送方式(默认为全赠 0仅一种，1任选，2全赠)")
        private Integer giftGroupType;

        /**
         * 赠送单位数量
         */
        @ApiModelProperty("赠送单位数量")
        private Integer giftSkuUnitQty;

        @ApiModelProperty("赠品集合")
        private List<Gift> giftList;
    }

    @Data
    @ApiModel(description = "赠品信息")
    public static class Gift {

        @JsonIgnore
        @ApiModelProperty(hidden = true)
        private BigDecimal fullValue;

        /**
         * 购买品项数(sku种类数)
         */
        @ApiModelProperty("购买品项数(sku种类数)")
        private Integer buySkuNum;

        /**
         * 赠送方式(默认为全赠 0仅一种，1任选，2全赠)
         */
        @ApiModelProperty("赠送方式(默认为全赠 0仅一种，1任选，2全赠)")
        private Integer giftGroupType;

        /**
         * 赠送单位数量
         */
        @ApiModelProperty("赠送单位数量")
        private Integer giftSkuUnitQty;

        @ApiModelProperty("规则ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long ruleId;

        @ApiModelProperty("skuId")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long skuId;

        @ApiModelProperty("couponTemplateId")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long couponTemplateId;

        @ApiModelProperty("sku or 优惠券")
        private Object giftObj;

        @ApiModelProperty("赠品类型;0-商品 1-优惠券")
        private Integer giftType;

        @ApiModelProperty("每次赠送多个")
        private Integer onceGiftQty;

        @ApiModelProperty("总共有多少份")
        private Integer totalGiftQty;

        /** 赠品商品单位大小 */
        @Excel(name = "赠品商品单位大小 1:小 2：中 3：大")
        private Integer giftSkuUnitType;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(description = "商品信息")
    public static class Item {

        @ApiModelProperty("商品名称")
        private String spuName;

        @ApiModelProperty("规格名称")
        private String skuName;

        @ApiModelProperty("销售价格")
        private BigDecimal markPrice;

        @ApiModelProperty("商品图片")
        private String thumb;
    }
}
