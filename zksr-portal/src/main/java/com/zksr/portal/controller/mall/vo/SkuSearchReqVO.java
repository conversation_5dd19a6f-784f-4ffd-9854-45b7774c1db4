package com.zksr.portal.controller.mall.vo;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/22 10:11
 */
@ApiModel(value = "商品SKU搜索商品请求VO")
@Data
public class SkuSearchReqVO extends PageParam {

    @ApiModelProperty(value = "关键字", example = "好看")
    private String condition;

    @ApiModelProperty(value = "入驻商ID")
    private List<Long> supplierId;

    @ApiModelProperty(value = "品牌商ID")
    private List<Long> brandId;

    @ApiModelProperty(value = "分类ID")
    private List<Long> classId;

    @ApiModelProperty(value = "最小价")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "最高价")
    private BigDecimal maxPrice;

    /**
     * 排序类型
     */
    @ApiModelProperty("排序类型: none-无, sale-销量, price-价格")
    private String sortType = StringPool.NONE;

    /**
     * 排序方式 @com.zksr.common.core.enums.ProductSortType
     */
    @ApiModelProperty("排序方式: none-无,des-降序, asc-升序")
    private String orderBy = StringPool.NONE;

    /**
     * 商品类型 com.zksr.common.core.enums.ProductType
     */
    @ApiModelProperty("商品类型 local-本地, global-全国, group-全部")
    private String productType;

    /**
     * 活动ID
     */
    @ApiModelProperty("活动ID")
    private Long activityId;
}
