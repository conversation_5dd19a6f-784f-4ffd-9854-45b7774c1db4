package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("registerSimple用户注册 接口请求")
public class RegisterSimpleReq {

    @ApiModelProperty(value = "sysSource  必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;

    //验证码  非必填
    /** 验证码 */
    @ApiModelProperty(value = "验证码  非必填")
    private String validateCode;

    //用户名  memberName 必填
    /** 用户名 */
    @ApiModelProperty(value = "用户名  必填")
    @NotBlank(message = "用户名不能为空")
    private String memberName;

    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    @NotBlank(message = "用户账号不能为空")
    private String userName;

    /** 用户密码 */
    @ApiModelProperty(value = "用户密码")
    @NotBlank(message = "用户密码不能为空")
    private String password;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String memo;

}
