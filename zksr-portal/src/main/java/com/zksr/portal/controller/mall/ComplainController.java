package com.zksr.portal.controller.mall;


import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.member.api.complain.MemComplainVO;
import com.zksr.member.api.complain.dto.MemComplainDTO;
import com.zksr.portal.service.mall.ComplainService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <p>
 * 投诉信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06
 */

@Api(tags = "商城 - 投诉接口")
@RestController
@Slf4j
@RequestMapping("/mall/complain")
public class ComplainController {

    @Autowired
    private ComplainService ComplainService;

    /**
     * 查询投诉列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "查询投诉列表")
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<MemComplainVO>> getComplainList( MemComplainVO memComplainVO) {
        PageResult<MemComplainVO> pageResult = ComplainService.getComplainList(memComplainVO);
        return success(pageResult);
    }


    /**
     * 新增投诉信息
     */
    @ApiOperation(value = "新增投诉信息")
    @RequiresMallLogin(isMember = true)
    @PostMapping("/addComplain")
    public CommonResult<String> add(@Valid @RequestBody MemComplainDTO memComplainDTO) {
        log.info("新增投诉信controller");
        return ComplainService.insertComplain(memComplainDTO);
    }


    /**
     * 查询投诉列表
     */
    @GetMapping("/{complainId}")
    @ApiOperation(value = "查询投诉详情")
    @RequiresMallLogin(isMember = true)
    public CommonResult<MemComplainVO> getComplainByComplainId( @PathVariable("complainId") String complainId) {
        MemComplainVO memComplainVO = ComplainService.getComplainBycomplainId(complainId);
        return success(memComplainVO);
    }

}
