package com.zksr.portal.controller.mall;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.portal.controller.mall.vo.car.*;
import com.zksr.portal.convert.car.CarConvert;
import com.zksr.portal.service.mall.ICarService;
import com.zksr.trade.api.car.vo.CarSaveReqVO;
import com.zksr.trade.api.car.vo.CarSelectedReqVO;
import com.zksr.trade.api.car.vo.CarTotalRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.ArrayList;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@RequestMapping("/mall/car")
@Api(tags = "商城 - 购物车接口")
@Slf4j
public class CarController {

    @Autowired
    private ICarService carService;

    @ApiOperation(value = "加入购物车", httpMethod = HttpMethod.POST)
    @PostMapping("/add")
    @RequiresMallLogin(isMember=true)
    public CommonResult<String> addCart(@Valid @RequestBody CarSaveReqVO saveReqVO) {
        saveReqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(carService.add(saveReqVO));
    }

    @ApiOperation(value = "批量加入购物车", httpMethod = HttpMethod.POST)
    @PostMapping("/add-batch")
    @RequiresMallLogin(isMember=true)
    public CommonResult<List<String>> addCartBatch(@Valid @RequestBody CarSaveBatchReqVO saveReqVO) {
        ArrayList<String> result = new ArrayList<>();
        CarSaveReqVO carSaveReqVO = CarConvert.INSTANCE.convert(saveReqVO);
        for (CarSaveBatchReqVO.BatchItemDTO itemCarId : saveReqVO.getCarIdList()) {
            carSaveReqVO.setCarId(itemCarId);
            carSaveReqVO.setOpQty(itemCarId.getOpQty());
            carSaveReqVO.setBranchId(MallSecurityUtils.getBranchId());
            carSaveReqVO.setCommandId(itemCarId.getCommandId());
            carSaveReqVO.setCommandMemo(saveReqVO.getCommandMemo());
            result.add(carService.add(carSaveReqVO));
        }
        return success(result);
    }

    @ApiOperation(value = "更新购物车数量", httpMethod = HttpMethod.POST)
    @PostMapping("/update-qty")
    @RequiresMallLogin(isMember=true)
    public CommonResult<Boolean> updateQty(@Valid @RequestBody CarSaveQtyReqVO saveReqVO) {
        saveReqVO.setBranchId(MallSecurityUtils.getBranchId());
        carService.updateQty(saveReqVO);
        return success(Boolean.TRUE);
    }

    @ApiOperation(value = "更新购物车选中", httpMethod = HttpMethod.POST)
    @PostMapping("/update-selected")
    @RequiresMallLogin(isMember=true)
    public CommonResult<List<String>> updateSelected(@Valid @RequestBody CarSelectedReqVO selectedReqVO) {
        selectedReqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(carService.updateSelected(selectedReqVO));
    }

    @ApiOperation(value = "删除购物车商品", httpMethod = HttpMethod.POST)
    @PostMapping("/update-remove")
    @RequiresMallLogin(isMember=true)
    public CommonResult<List<String>> removeCar(@Valid @RequestBody CarRemoveReqVO selectedReqVO) {
        selectedReqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(carService.removeCar(selectedReqVO));
    }

    @ApiOperation(value = "获取购物车角标统计", httpMethod = HttpMethod.POST)
    @PostMapping("/get-total")
    @RequiresMallLogin
    public CommonResult<CarTotalRespVO> getTotal(@Valid @RequestBody CarTotalReqVO totalReqVO) {
        totalReqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(carService.getTotal(totalReqVO));
    }

    @ApiOperation(value = "获取购物车选中数据", httpMethod = HttpMethod.POST)
    @PostMapping("/get-count")
    @RequiresMallLogin
    //!@小程序 - 6、购物车角标
    public CommonResult<CarCountRespVO> getCount(@Valid @RequestBody CarCountReqVO countReqVO) {
        try {
            countReqVO.setBranchId(MallSecurityUtils.getBranchId());
            return success( carService.getCount(countReqVO) );
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "获取购物车选中状态", httpMethod = HttpMethod.POST)
    @PostMapping("/get-select-stock")
    @RequiresMallLogin(isMember=true)
    public CommonResult<CarCountStatusRespVO> getSelectStock(@Valid @RequestBody CarCountStatusReqVO countReqVO) {
        countReqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(carService.getSelectStock(countReqVO));
    }

    @ApiOperation(value = "获取购物车分页数据", httpMethod = HttpMethod.POST)
    @PostMapping("/get-page")
    @RequiresMallLogin
    //!@小程序 - 5、购物车分页
    public CommonResult<CarPageRespVO> getPage(@Valid @RequestBody CarPageReqVO pageReqVO) {
        try {
            pageReqVO.setBranchId(MallSecurityUtils.getBranchId());
            return success(carService.getPage(pageReqVO));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "获取业务员推荐商品信息", httpMethod = HttpMethod.GET)
    @GetMapping("/get-recommend")
    @RequiresMallLogin(isMember = true)
    public CommonResult<CarRecommendRespVO> getRecommend() {
        return success(carService.getRecommend(MallSecurityUtils.getBranchId()));
    }

    @ApiOperation(value = "获取业务员推荐商品详情列表", httpMethod = HttpMethod.GET)
    @GetMapping("/get-recommend-list")
    @RequiresMallLogin(isMember = true)
    public CommonResult<CarPageRespVO> getRecommendList() {
        return success(carService.getRecommendList(MallSecurityUtils.getBranchId()));
    }

    @ApiOperation(value = "移除业务员推荐商品", httpMethod = HttpMethod.POST)
    @PostMapping("/remove-recommend")
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> removeRecommend() {
        carService.removeRecommend(MallSecurityUtils.getBranchId());
        return success(Boolean.TRUE);
    }

    @ApiOperation(value = "清除业务员推荐商品标识", httpMethod = HttpMethod.POST)
    @PostMapping("/remove-recommend-flag")
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> removeRecommendFlag() {
        carService.removeRecommendFlag(MallSecurityUtils.getBranchId());
        return success(Boolean.TRUE);
    }

    @ApiOperation(value = "关闭推荐弹窗", httpMethod = HttpMethod.POST)
    @PostMapping("/close-recommend-window")
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> closeRecommendWindow() {
        carService.closeRecommendWindow(MallSecurityUtils.getBranchId());
        return success(Boolean.TRUE);
    }

    @ApiOperation(value = "初始化购物车", httpMethod = HttpMethod.POST)
    @PostMapping("/init")
    public CommonResult<Boolean> init(Long branchId) {
        carService.init(branchId);
        return success(Boolean.TRUE);
    }

    @ApiOperation(value = "获取购物车指定促销商品数量", httpMethod = HttpMethod.POST)
    @PostMapping("/validateActivity")
    @RequiresMallLogin
    public CommonResult<CarValidateActivityRespVO> validateActivity(@Valid @RequestBody CarValidateActivityReqVO pageReqVO) {
        try {
            pageReqVO.setBranchId(MallSecurityUtils.getBranchId());
            return success(carService.validateActivity(pageReqVO));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "获取购物车分页数据（客户通APP）", httpMethod = HttpMethod.POST)
    @PostMapping("/getAppCartPage")
    public CommonResult<CarPageRespVO> getAppCartPage(@Valid @RequestBody CarPageReqVO pageReqVO) {
        try {
            return success(carService.getPage(pageReqVO));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
