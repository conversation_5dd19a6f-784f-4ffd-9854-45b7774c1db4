package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel("城市分类接口 响应")
public class AreaClassRespVO {

    @ApiModelProperty(value = "分类编号", required = true, example = "2")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaClassId;

    @ApiModelProperty(value = "父分类编号", required = true, example = "1")
    @NotNull(message = "父分类编号不能为空")
    private Long pid;

    @ApiModelProperty(value = "分类名称", required = true, example = "办公文具")
    @NotBlank(message = "分类名称不能为空")
    private String areaClassName;

    @ApiModelProperty(value = "分类图片", required = true)
    //@NotBlank(message = "分类图片不能为空")
    private String icon;

    @ApiModelProperty(value = "是否是电子围栏分类", required = true)
    private Long dzwlFlag;

    /** 入驻商id */
    @ApiModelProperty(value = "电子围栏入驻商id", required = false)
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

}
