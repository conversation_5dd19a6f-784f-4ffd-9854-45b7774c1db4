package com.zksr.portal.controller.mall;

import com.alibaba.fastjson.JSON;
import com.zksr.account.api.platformMerchant.PlatformMerchantApi;
import com.zksr.account.api.platformMerchant.vo.WxB2bColonelOpenidBindReqVO;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.member.api.colonel.vo.WxColonelPublishOpenidBindReqVO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.utils.MallJwtUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.auth.MallAuthUtil;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.third.wx.AES;
import com.zksr.member.api.branch.vo.MemberBranchReqVO;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.loginHis.LoginHisApi;
import com.zksr.member.api.loginHis.vo.MemLoginHisVO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.vo.MemberUpdatePwdVO;
import com.zksr.member.api.member.vo.MemberUpdateUserInfoVO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IIndexService;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.sms.dto.SmsCodeValidRespDTO;
import com.zksr.system.api.supplierArea.SupplierAreaApi;
import com.zksr.system.api.supplierArea.dto.SupplierDzwlAreaDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@RequestMapping("/mall/login")
@Api(tags = "商城 - 登录接口")
@Slf4j
public class LoginController {

    @Autowired
    private IIndexService indexService;//

    @Autowired
    private IPortalCacheService portalCacheService;

    @Resource
    private SupplierAreaApi supplierAreaApi;

    @Resource
    private MemberApi memberApi;

    @Resource
    private PlatformMerchantApi platformMerchantApi;

    @Resource
    private ColonelApi colonelApi;

    @Resource
    private LoginHisApi loginHisApi;

    /**
     * @Description: 验证码登录
     * @Param: String code
     * @return: CommonResult<AuthLoginResponse>
     * @Author: liuxingyu
     * @Date: 2024/5/10 15:24
     */
    @ApiOperation("验证码登录")
    @PostMapping("/LoginSms")
    public CommonResult<AuthLoginResponse> LoginSms(@RequestBody LoginSmsReq loginSmsReq) {
        return success(indexService.loginSms(loginSmsReq));
    }

    /**
     * @Description: 用户获取验证码
     * @Param: LoginSmsReq
     * @return: CommonResult<String>
     * @Author: liuxingyu
     * @Date: 2024/5/10 9:50
     */
    @ApiOperation("用户获取验证码")
    @PostMapping("/getSmsCode")
    public CommonResult<String> getSmsCode(@Valid @RequestBody LoginSmsReq loginSmsReq) {
        return success(indexService.getSmsCode(loginSmsReq));
    }

    /**
     * 校验 验证码
     *
     * @param loginSmsReq
     * @return
     */
    @ApiOperation("校验验证码")
    @PostMapping("/checkSmsCode")
    public CommonResult<SmsCodeValidRespDTO> checkSmsCode(@Valid @RequestBody LoginSmsReq loginSmsReq) {
        return success(indexService.checkSmsCode(loginSmsReq));
    }

    /**
     * 获取openId接口
     */
    @ApiOperation("获取openId")
    @PostMapping(value = "/getWxInfo")
    @ResponseBody
    public CommonResult<GetWxInfoResponse> getWxInfo(@Valid @RequestBody GetWxInfoRequest request) throws Exception {
        GetWxInfoResponse response = indexService.getWxInfo(request);
        return success(response);
    }

    /**
     * 绑定公众号openid
     */
    @ApiOperation("绑定公众号openid")
    @PostMapping(value = "/bindPublishCode")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> bindPublishCode(@Valid @RequestBody GetWxInfoRequest request) throws Exception {
        return success(indexService.bindPublishCode(request));
    }

    /**
     * 解绑定公众号openid
     */
    @ApiOperation("解绑公众号openid")
    @GetMapping(value = "/unbindPublishCode")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> unbindPublishCode() throws Exception {
        return success(indexService.unbindPublishCode());
    }

    /**
     * 解密获取手机号接口
     */
    @ApiOperation("解密获取手机号")
    @PostMapping(value = "/getWxMobile")
    @ResponseBody
    public CommonResult<GetWxMobileResp> getWxMobile(@Valid @RequestBody GetWxMobileReq request) throws IOException {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        String sessionKey = portalCacheService.getWxSessionKey(partnerDto.getSysCode() + ":" + request.getOpenid() + ":" + request.getCode());

        if (ToolUtil.isEmpty(sessionKey)) {
            log.error("无法根据请求参数获取sessionKey,解密失败");
            //TODO 返回一个错误码
            //return error(ErrorCode errorCode)
            return null;
        }
        String encryptedData = request.getEncryptedData();
        String iv = request.getIv();

        byte[] resultByte;
        try {
            resultByte = AES.decrypt(Base64.decodeBase64(encryptedData),
                    Base64.decodeBase64(sessionKey),
                    Base64.decodeBase64(iv));
            if (null != resultByte && resultByte.length > 0) {
                String userInfo = new String(resultByte, "UTF-8");
                log.info("userInfo:" + userInfo);
                GetWxMobileResp response = JSON.parseObject(userInfo, GetWxMobileResp.class);

                //TODO 判断手机号是否已经存在
                return success(response);
            }
            //TODO 返回一个错误码
            //return error(ErrorCode errorCode)
            return null;
        } catch (Exception e) {
            log.error("解密接口错误, ", e);
            return null;
        }
    }

    /**
     * 用户注册
     */
    @ApiOperation("用户注册")
    @PostMapping(value = "/register")
    @ResponseBody
    public CommonResult<RegisterResp> register(@RequestBody @Valid RegisterReq request) {
        RegisterResp response = indexService.register(request);
        return success(response);
    }

    /**
     * 用户简易注册
     */
    @ApiOperation("用户简易注册")
    @PostMapping(value = "/registerSimple")
    @ResponseBody
    public CommonResult<RegisterResp> registerSimple(@RequestBody @Valid RegisterSimpleReq request) {
        RegisterResp response = indexService.registerSimple(request);
        return success(response);
    }

    /**
     * 手机号快捷登录接口
     */
    @ApiOperation("手机号快捷登录接口")
    @PostMapping(value = "/authLogin")
    @ResponseBody
    public CommonResult<AuthLoginResponse> authLogin(@RequestBody @Valid AuthLoginRequest request) {
        AuthLoginResponse response = indexService.authLogin(request);
        return success(response);
    }


    /**
     * 游客登录接口
     */
    @ApiOperation("游客登录接口")
    @PostMapping(value = "/visitorLogin")
    @ResponseBody
    public CommonResult<VisitorLoginResponse> visitorLogin(@RequestBody @Valid VisitorLoginRequest request) {
        VisitorLoginResponse response = indexService.visitorLogin(request);
        return success(response);
    }

    /**
     * 获取区域城市接口
     */
    @ApiOperation("获取区域城市接口")
    @PostMapping(value = "/cityList")
    public CommonResult<List<AreaRespVO>> cityList(@RequestParam("sysSource") String sysSource) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        //根据sysCode获取 已经分配运营商的城市列表
        return success(indexService.getcityList(partnerDto.getSysCode()));
    }

    /**
     * 获取用户的门店列表
     *
     * @return
     */
    @ApiOperation("获取用户的门店列表")
    @PostMapping(value = "/branchList")
    @RequiresMallLogin
    public CommonResult<List<BranchReqVo>> branchList(@RequestBody MemberBranchReqVO memberBranchReqVO) {
        memberBranchReqVO.setMemberId(MallSecurityUtils.getMemberId());
        return success(indexService.getBranchList(memberBranchReqVO));
    }

    /**
     * 获取用户未审核的门店列表
     *
     * @return
     */
    @ApiOperation("获取用户未审核的门店列表")
    @PostMapping(value = "/unauditedBranchList")
    @RequiresMallLogin
    public CommonResult<List<BranchReqVo>> unauditedBranchList(@RequestBody MemberBranchReqVO memberBranchReqVO) {
        memberBranchReqVO.setMemberId(MallSecurityUtils.getMemberId());
        return success(indexService.getUnauditedBranchList(memberBranchReqVO));
    }

    /**
     * 切换门店
     *
     * @param memberId
     * @return
     */
    @ApiOperation("切换门店")
    @GetMapping(value = "/cutBranch")
    @RequiresMallLogin
    public CommonResult<AuthLoginResponse> cutBranch(@RequestParam("memberId") Long memberId, @RequestParam("branchId") Long branchId) {
        return success(indexService.cutBranch(memberId, branchId));
    }

    @ApiOperation("退出登陆")
    @DeleteMapping("/logout")
    public CommonResult<Boolean> logout(HttpServletRequest request) {
        String token = MallSecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            String username = MallJwtUtils.getMemberKey(token);
            // 删除用户缓存记录
            MallAuthUtil.logoutByToken(token);
        }
        return CommonResult.success(true);
    }


    /**
     * 用户账号密码登陆
     */
    @ApiOperation("用户账号密码登陆")
    @PostMapping(value = "/userLogin")
    @ResponseBody
    public CommonResult<AuthLoginResponse> userLogin(@RequestBody @Valid UserLoginRequest request) {
        AuthLoginResponse response = indexService.userLogin(request);
        return success(response);
    }

    @ApiOperation("增加子用户")
    @PostMapping(value = "/insertChildUser")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> insertChildUser(@RequestBody @Valid ChildUserReqVO reqVO) {
        indexService.insertChildUser(reqVO);
        return success(true);
    }

    /**
     * 获取用户的子用户列表
     *
     * @param memberId
     * @return
     */
    @ApiOperation("获取用户的子用户列表")
    @GetMapping(value = "/childUserList")
    @RequiresMallLogin(isMember = true)
    public CommonResult<List<ChildUserListRespVO>> childUserList(@RequestParam("memberId") Long memberId) {
        return success(indexService.childUserList(memberId));
    }

    /**
     * 更新用户密码（无验证码校验，验证码已单独校验）
     *
     * @param request
     * @return 返回一个CommonResult对象，其中包含操作结果的布尔值。
     * 如果操作成功，布尔值为true；如果操作失败，布尔值为false。
     */
    @ApiOperation("更新用户密码")
    @PostMapping("/updateMemberPassword")
    public CommonResult<Boolean> updateMemberPassword(@RequestBody @Valid MemberUpdatePwdVO request) {
        return success(indexService.updateMemberPassword(request));
    }

    /**
     * 根据城市ID获取到该区域内绑定电子围栏的入驻商信息
     *
     * @return
     */
    @ApiOperation("根据城市ID获取到该区域内绑定电子围栏的入驻商信息")
    @GetMapping(value = "/getDzwlSupplierInfoByAreaId")
    @ResponseBody
    public CommonResult<List<SupplierDzwlAreaDTO>> getDzwlSupplierInfoByAreaId(@RequestParam("areaId") Long areaId) {
        return success(supplierAreaApi.getDzwlSupplierInfoByAreaId(areaId).getCheckedData());
    }


    @ApiOperation("更新用户信息")
    @PostMapping(value = "/updateUserInfo")
    @ResponseBody
    public CommonResult<Boolean> updateUserInfo(@RequestBody @Valid MemberUpdateUserInfoVO request) {
        request.setMemberId(MallSecurityUtils.getMemberId());
        return success(memberApi.updateMemberUserInfoByMemberId(request).getCheckedData());
    }


    @ApiOperation(value = "获取用户信息", httpMethod = HttpMethod.GET)
    @GetMapping(value = "/userinfo")
    @ResponseBody
    @RequiresMallLogin
    public CommonResult<LoginUserInfoVO> userinfo() {
        return success(indexService.getUserinfo());
    }

    /**
     * 业务员APP登录商城接口
     */
    @ApiOperation("业务员APP登录商城接口")
    @PostMapping(value = "/colonelAppMallLogin")
    @ResponseBody
    public CommonResult<ColonelAppMallLoginResponse> colonelAppMallLogin(@RequestBody @Valid ColonelAppMallLoginRequest request) {
        return success(indexService.colonelAppMallLogin(request));
    }

    /**
     * 获取支付宝认证信息
     */
    @ApiOperation("获取支付宝认证信息")
    @PostMapping(value = "/getZfbInfo")
    @ResponseBody
    public CommonResult<GetWxInfoResponse> getZfbInfo(@Valid @RequestBody GetWxInfoRequest request) throws Exception {
        GetWxInfoResponse response = indexService.getZfbInfo(request);
        return success(response);
    }

    @ApiOperation(value = "业务员绑定商城小程序openid", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/updateWxB2bColonelOpenidBind")
    @ResponseBody
    public CommonResult<Boolean> updateWxB2bColonelOpenidBind(@Valid @RequestBody WxB2bColonelOpenidBindReqVO reqVO) {
        return platformMerchantApi.updateWxB2bColonelOpenidBind(reqVO);
    }

    @ApiOperation(value = "业务员绑定微信公众号openid", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/updateColonelPublishOpenidBind")
    @ResponseBody
    public CommonResult<Boolean> updateColonelPublishOpenidBind(@Valid @RequestBody WxColonelPublishOpenidBindReqVO reqVO) {
        return colonelApi.updateColonelPublishOpenidBind(reqVO);
    }

    /**
     * 新增账号日志
     */
    @ApiOperation("新增账号日志")
    @PostMapping(value = "/addLoginHis")
    @ResponseBody
    public CommonResult<Long> addLoginHis(@RequestBody @Valid MemLoginHisVO createReqVO) {
        LoginMember loginMember = MallSecurityUtils.getLoginMember();
        if (ToolUtil.isNotEmpty(loginMember) && loginMember.getMember() != null) {
            createReqVO.setMemberPhone(loginMember.getMember().getMemberPhone());
            createReqVO.setMemberUsername(loginMember.getMember().getUserName());
            createReqVO.setMemberId(loginMember.getMember().getMemberId());
            createReqVO.setBranchId(loginMember.getBranchId());
            createReqVO.setWxOpenid(MallSecurityUtils.getLoginMember().getMember().getXcxOpenid());
        }
        //获取当前日期
        createReqVO.setDateId(Long.parseLong(DateUtils.dateTime()));
        createReqVO.setIp(IpUtils.getIpAddr());
        return success(loginHisApi.addLoginHis(createReqVO).getCheckedData());
    }
}
