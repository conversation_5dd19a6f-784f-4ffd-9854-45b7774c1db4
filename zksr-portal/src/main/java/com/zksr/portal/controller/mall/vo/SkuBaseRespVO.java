package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("商城-SKU基本 VO对象")
public class SkuBaseRespVO {

    @ApiModelProperty(value = "主键", required = true, example = "1024")
    private Long skuId;

    @ApiModelProperty(value = "商品 SKU 名字", required = true, example = "娃哈哈纯净水")
    private String name;

    @ApiModelProperty(value = "商品SKU图片地址", example = "https://www.XXXX.cn/xx.png")
    private String iconUrl;


}
