package com.zksr.portal.controller.mall.vo;

import com.zksr.common.core.domain.dto.car.AppCarStorageDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.member.api.branch.dto.BranchDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Accessors(chain = true)
public class TradePriceCalculateRequest {


    /**
     * 用户编号
     *
     * 对应 MemMember 的 id 编号
     */
    private Long memberId;

    /**
     * 优惠劵编号  TODO
     *
     * 对应 CouponDO 的 id 编号
     */
    private List<Long> couponIds;

    /**
     * 营销活动集合
     */
    private List<ActivityVO> activitys;

    /**
     * 门店信息
     */
    private BranchDTO branchDto;

    /**
     * 商品 SKU 数组
     */
    @NotNull(message = "商品数组不能为空")
    private List<Item> items;

    @ApiModelProperty(value = "接口类型 0 购物车跳结算页  1 创建订单",hidden = true)
    private  Integer interfaceType;


    /**
     * 商品 SKU
     */
    @Data
    @Valid
    public static class Item {
        /**
         * SKU 编号
         */
        @NotNull(message = "商品 SKU 编号不能为空")
        private Long skuId;

        /**
         * SKU 数量
         */
        @NotNull(message = "商品 SKU 数量不能为空")
        @Min(value = 0L, message = "商品 SKU 数量必须大于等于 0")
        private Long count;

        /**
         * 是否参与活动（秒杀或特价）1：是 0： 否
         */
        private Integer isActivitySpSk;

        /**
         * 购物车项的编号
         */
        private String cartId;

        /**
         * 是否选中
         */
        @NotNull(message = "是否选中不能为空")
        private Boolean selected;

        /**
         * 城市上架商品ID
         */
        private Long areaItemId;

        /**
         * 供应商（本地）上架商品ＩＤ
         */
        private Long supplierItemId;

        /**
         * 入驻商ＩＤ
         */
        private Long supplierId;

        /**
         * 入驻商名称
         */
        private String supplierName;

        /**
         * 订单购买单位
         */
        private String unit;

        /**
         * 单位大小
         */
        private Integer unitSize;

        /**
         * 商品类型 0-普通商品, 1-组合商品
         */
        private Integer goodsType;

        /**
         * 组合商品ID （默认为 -1）
         */
        private Long spuCombineId = NumberPool.LOWER_GROUND_LONG;

        /**
         * 购物车商品数据
         */
        private AppCarStorageDTO carStorageDTO;

        @ApiModelProperty("是否支持负库存下单 0：否，1：是")
        private Integer isNegativeStock;

    }

//    /**
//     * 配送方式
//     *
//     * 枚举 {@link DeliveryTypeEnum}
//     */
//    private Integer deliveryType;



//    // ========== 秒杀活动相关字段 ==========
//    /**
//     * 秒杀活动编号
//     */
//    private Long seckillActivityId;
//
//    // ========== 拼团活动相关字段 ==========
//    /**
//     * 拼团活动编号
//     */
//    private Long combinationActivityId;
//
//    /**
//     * 拼团团长编号
//     */
//    private Long combinationHeadId;
//
//    // ========== 砍价活动相关字段 ==========
//    /**
//     * 砍价记录编号
//     */
//    private Long bargainRecordId;
}
