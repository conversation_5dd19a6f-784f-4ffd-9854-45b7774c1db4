package com.zksr.portal.controller.mall.vo.spu;

import com.zksr.promotion.api.activity.dto.ActivityDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商品关联促销活动标签
 * @date 2024/5/23 14:34
 */
@Data
@ApiModel(description = "商品详情关联促销活动标签")
public class SpuDetailActivityLabelVO extends ActivityDTO {
    @ApiModelProperty("规则信息")
    private List<Object> rules;
}
