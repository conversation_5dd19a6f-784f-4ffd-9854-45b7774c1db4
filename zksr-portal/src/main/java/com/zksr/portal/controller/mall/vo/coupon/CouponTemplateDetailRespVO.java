package com.zksr.portal.controller.mall.vo.coupon;

import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/7 9:23
 */
@ApiModel(description = "获取优惠券模版数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponTemplateDetailRespVO extends CouponTemplateDTO {

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty(value = "具体使用范围别称, 比如入驻商名称")
    private List<CouponSpuScopeDTO> spuScopeAs;
}
