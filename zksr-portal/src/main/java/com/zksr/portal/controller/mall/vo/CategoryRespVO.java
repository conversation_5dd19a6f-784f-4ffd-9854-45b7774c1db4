package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("分类接口 响应")
public class CategoryRespVO {

    @ApiModelProperty(value = "分类编号", required = true, example = "2")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long catgoryId;

    @ApiModelProperty(value = "父分类编号", required = true, example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long pid;

    @ApiModelProperty(value = "分类名称", required = true, example = "办公文具")
    private String catgoryName;

    @ApiModelProperty(value = "分类图片", required = true)
    private String icon;

    @ApiModelProperty(value = "是否是电子围栏分类", required = true)
    private Long dzwlFlag;

    /**
     * 入驻商id
     */
    @ApiModelProperty(value = "电子围栏入驻商id", required = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long supplierId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 级别
     */
    @ApiModelProperty(value = "级别")
    private Integer level;

    /**
     * 销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置
     */
    @ApiModelProperty(value = "销售占比利润, 百分比,最高29% = 0.29, 只有一级可以设置")
    private BigDecimal saleTotalRate;
}
