package com.zksr.portal.controller.mall.vo.activity;

import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@ApiModel(description = "根据活动id获取商品请求VO")
public class ActivityItemPageReqVO extends PageParam {
    @NotEmpty(message = "活动ids不能为空")
    @ApiModelProperty(value = "活动ids")
    private List<Long> activityIds;

    @ApiModelProperty("平台商source")
    private String sysSource;
}
