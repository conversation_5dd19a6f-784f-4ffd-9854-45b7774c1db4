package com.zksr.portal.controller.mall;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.account.api.recharge.RechargeApi;
import com.zksr.account.api.recharge.dto.RechargeSchemeDTO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.config.AppletAgreementPolicyRespVO;
import com.zksr.portal.controller.mall.vo.config.IndexPageV2ConfigVO;
import com.zksr.portal.controller.mall.vo.config.SysDictTypeVO;
import com.zksr.portal.convert.cfg.ConfigConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.domain.SysPartnerDictData;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerDict.PartnerDictTypeApi;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.SearchConfigDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.system.enums.ErrorCodeConstants.PAGES_CONFIG_NONE_EXITS;
import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商场配置
 * @date 2024/3/31 17:05
 */

@RestController
@RequestMapping("/mall/config")
@Api(tags = "商城 - 商城配置")
@Slf4j
public class ConfigController {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Resource
    private PartnerPolicyApi partnerPolicyApi;

    @Resource
    private PartnerDictTypeApi partnerDictTypeApi;

    @Resource
    private RechargeApi rechargeApi;

    @ApiOperation(value = "获取全局字典参数", httpMethod = HttpMethod.GET)
    @GetMapping("/getGlobalDictData/{dictType}")
    public CommonResult<List<SysDictData>> getGlobalDictData(@PathVariable String dictType) {
        return success(DictUtils.getDictCache(dictType));
    }

    @ApiOperation(value = "获取平台商字典参数", httpMethod = HttpMethod.GET)
    @GetMapping("/getGlobalPartnerDictData")
    public CommonResult<List<SysPartnerDictData>> getGlobalPartnerDictData(@RequestParam("dictType") String dictType, @RequestParam(value = "sysSource",required = false,defaultValue = "") String sysSource) {
        List<SysPartnerDictData> partnerDictCache = partnerDictTypeApi.selectDictDataByType(dictType,sysSource).getCheckedData();
        return success(partnerDictCache);
    }

    @ApiOperation(value = "获取全局字典参数List", httpMethod = HttpMethod.POST)
    @PostMapping("/getGlobalDictDataList")
    public CommonResult<List<SysDictData>> getGlobalDictDataList(@RequestBody @Valid SysDictTypeVO sysDictTypeVO) {
        List<SysDictData> dictDataList = new ArrayList<>();
        sysDictTypeVO.getDictTypeList().forEach(dictType -> {
            dictDataList.addAll(DictUtils.getDictCache(dictType));
        });
        return success(dictDataList);
    }

    @ApiOperation(value = "获取平台商字典参数List", httpMethod = HttpMethod.POST)
    @PostMapping("/getGlobalPartnerDictDataList")
    public CommonResult<List<SysPartnerDictData>> getGlobalPartnerDictDataList(@RequestBody @Valid SysDictTypeVO sysDictTypeVO) {
        List<SysPartnerDictData> dictDataList = new ArrayList<>();
        sysDictTypeVO.getDictTypeList().forEach(dictType -> {
            List<SysPartnerDictData> partnerDictCache = partnerDictTypeApi.selectDictDataByType(dictType,sysDictTypeVO.getSysSource()).getCheckedData();

            if (StringUtils.isNotEmpty(partnerDictCache)){
                dictDataList.addAll(partnerDictCache);
            }

        });
        return success(dictDataList);
    }


    /**
    * @Description: 获取商城小程序配置信息
    * @Param: Long sysSource
    * @return: CommonResult<AppletAgreementPolicyDTO>
    * @Author: liuxingyu
    * @Date: 2024/4/10 15:53
    */
    @GetMapping("/getAppConfig")
    @ApiOperation(value = "获取商城小程序配置信息", httpMethod = HttpMethod.GET)
    public CommonResult<AppletAgreementPolicyRespVO> getAppConfig(@RequestParam("sysSource") String sysSource){
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        if (ObjectUtil.isNull(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        return success(
                ConfigConvert.INSTANCE.convertAppletAgreementPolicyDTO(
                        portalCacheService.getAppletAgreementPolicy(partnerDto.getSysCode()),
                        portalCacheService.getAppletBaseConfigDTO(partnerDto.getSysCode()),
                        portalCacheService.getPayConfigDTO(partnerDto.getSysCode())
                )
        );
    }

    @GetMapping("/getIndexPageConfig")
    @ApiOperation(value = "获取商城小程序首页配置", httpMethod = HttpMethod.GET, notes = "channelId,areaId 没有就传入-1, 会查找大区默认配置")
    public CommonResult<PagesConfigDTO> getIndexPageConfig(
            @ApiParam(name = "sysSource", value = "平台标识") @RequestParam("sysSource") String sysSource){
        Long channelId = NumberPool.LOWER_GROUND_LONG;
        Long areaId = NumberPool.LOWER_GROUND_LONG;
        BranchDTO branchDto = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        if (Objects.nonNull(branchDto)) {
            channelId = branchDto.getChannelId();
            areaId = branchDto.getAreaId();
        }
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        if (ObjectUtil.isNull(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        channelId = Objects.isNull(channelId) ? NumberPool.LOWER_GROUND_LONG : channelId;

        AppletAgreementPolicyDTO appletAgreementPolicy =partnerPolicyApi.getAppletAgreementPolicy(partnerDto.getSysCode()).getCheckedData();
        if(ToolUtil.isNotEmpty(appletAgreementPolicy.getCityId()) && Objects.isNull(branchDto)){
            areaId=Long.parseLong(appletAgreementPolicy.getCityId());
        }else{
            areaId = Objects.isNull(areaId) ? NumberPool.LOWER_GROUND_LONG : areaId;
        }
        // 拿城市 + 渠道
        List<PagesConfigDTO> configDTOList = portalCacheService.getPagesConfigDTO(partnerDto.getSysCode(), channelId, areaId);
        if (!configDTOList.isEmpty()) {
            // 优先级已经排好了, 这里只需要验证时间是否有效就行了
            for (PagesConfigDTO configDTO : configDTOList) {
                if (configDTO.getStartTime().getTime() < System.currentTimeMillis() && configDTO.getEndTime().getTime() > System.currentTimeMillis()) {
                    return CommonResult.success(configDTO);
                }
            }
        }
        return CommonResult.success(null);
    }

    @GetMapping("/getIndexPageConfigV2")
    @ApiOperation(value = "获取商城小程序首页配置V2", httpMethod = HttpMethod.GET, notes = "channelId,areaId 没有就传入-1, 会查找大区默认配置")
    public CommonResult<IndexPageV2ConfigVO> getIndexPageConfigV2(
            @ApiParam(name = "sysSource", value = "平台标识") @RequestParam("sysSource") String sysSource,
            @Valid @NotNull @ApiParam(name = "branchId", value = "门店ID", required = true) @RequestParam("branchId") Long branchId
    ){
        Long channelId = NumberPool.LOWER_GROUND_LONG;
        Long areaId = NumberPool.LOWER_GROUND_LONG;
        if (branchId > NumberPool.LOWER_GROUND_LONG) {
            BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
            if (Objects.nonNull(branchDto)) {
                channelId = branchDto.getChannelId();
                areaId = branchDto.getAreaId();
            }
        }
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        if (ObjectUtil.isNull(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        channelId = Objects.isNull(channelId) ? NumberPool.LOWER_GROUND_LONG : channelId;

        AppletAgreementPolicyDTO appletAgreementPolicy =partnerPolicyApi.getAppletAgreementPolicy(partnerDto.getSysCode()).getCheckedData();
        if(ToolUtil.isNotEmpty(appletAgreementPolicy.getCityId()) && branchId == NumberPool.LOWER_GROUND_LONG){
            areaId=Long.parseLong(appletAgreementPolicy.getCityId());
        }else{
            areaId = Objects.isNull(areaId) ? NumberPool.LOWER_GROUND_LONG : areaId;
        }
        // 拿城市 + 渠道
        List<PagesConfigDTO> configDTOList = portalCacheService.getPagesConfigDTO(partnerDto.getSysCode(), channelId, areaId);
        return CommonResult.success(new IndexPageV2ConfigVO(configDTOList));
    }

    @GetMapping("/getAreaSearchConfig")
    @ApiOperation(value = "获取城市推荐配置", httpMethod = HttpMethod.GET)
    @RequiresMallLogin
    public CommonResult<SearchConfigDTO> getAreaSearchConfig() {
        return success(portalCacheService.getSearchConfigDTO(MallSecurityUtils.getLoginMember().getAreaId()));
    }


    @ApiOperation(value = "获取城市充值方案配置", httpMethod = HttpMethod.GET)
    @GetMapping("/getAreaRechargeScheme")
    public CommonResult<RechargeSchemeDTO> getAreaRechargeScheme() {
        List<RechargeSchemeDTO> rechargeSchemeDTOS = rechargeApi.getAreaRechargeSchemeList(MallSecurityUtils.getLoginMember().getAreaId()).getCheckedData();
        for (RechargeSchemeDTO schemeDTO : rechargeSchemeDTOS) {
            if (schemeDTO.validate()) {
                return CommonResult.success(schemeDTO);
            }
        }
        return CommonResult.success(null);
    }
}
