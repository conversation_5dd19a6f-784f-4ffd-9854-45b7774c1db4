package com.zksr.portal.controller.mall;

import com.alibaba.fastjson2.JSON;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.enums.PayChannelEnum;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.portal.controller.mall.vo.CreateOrderRequest;
import com.zksr.portal.controller.mall.vo.CreateOrderRespVO;
import com.zksr.portal.controller.mall.vo.SettleOrderRequest;
import com.zksr.portal.controller.mall.vo.SettleOrderResp;
import com.zksr.member.api.member.dto.MemMemberInvoicePageReqDTO;
import com.zksr.member.api.member.dto.MemMemberInvoiceRespDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.order.OrderVO;
import com.zksr.portal.controller.mall.vo.order.ShareOrderReqVO;
import com.zksr.portal.controller.mall.vo.yh.YhCreateOrderRequest;
import com.zksr.portal.controller.mall.vo.yh.YhSettleOrderRequest;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.OrderService;
import com.zksr.product.api.share.ProductShareApi;
import com.zksr.product.api.share.dto.BatchProductShareDTO;
import com.zksr.trade.api.driver.DriverApi;
import com.zksr.trade.api.driver.dto.DriverDTO;
import com.zksr.trade.api.driver.dto.DriverRatingDTO;
import com.zksr.trade.api.driver.vo.DriverRatingReqVO;
import com.zksr.trade.api.express.OrderExpressApi;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.api.hdfk.HdfkApi;
import com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkOrderRespVO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkReqVO;
import com.zksr.trade.api.order.dto.*;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.TrdOrderMiniHeadRespDTO;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import com.zksr.trade.api.order.vo.*;
import com.zksr.portal.service.mall.OrderService;
import com.zksr.trade.api.express.OrderExpressApi;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.supplierOrder.SupplierOrderApi;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderInvoicePageReqDTO;
import com.zksr.trade.api.supplierOrder.dto.TrdSupplierOrderInvoiceRespDTO;
import com.zksr.portal.service.mall.OrderService;
import com.zksr.trade.api.express.OrderExpressApi;
import com.zksr.trade.api.express.dto.ExpressResDTO;
import com.zksr.trade.api.order.OrderApi;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * @Description: 订单Controller
 * @Author: liuxingyu
 * @Date: 2024/3/30 9:23
 */
@Api(tags = "商城 - 订单接口")
@RestController
@RequestMapping("/mall/order")
@Slf4j
public class OrderController {

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderExpressApi orderExpressApi;

    @Resource
    private OrderApi orderApi;

    @Resource
    private HdfkApi hdfkApi;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Resource
    private ProductShareApi productShareApi;

    @Resource
    private DriverApi driverApi;

    @Resource
    private SupplierOrderApi supplierOrderApi;


    /**
     * @Description: 获取常购商品
     * @Param: List<SkuPageRespVO>
     * @return: CommonResult<List<StoreProductRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/5/7 9:35
     */
    @GetMapping("/frequentSpuList")
    @ApiOperation("获取常购商品")
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<StoreProductRespVO>> frequentSpuList(StoreProductRequest storeProductRequest) {
        return success(orderService.frequentSpuList(storeProductRequest));
    }

    /**
     * 预下单接口-购物车跳结算页
     */
    @ApiOperation("预下单接口-购物车跳结算页")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/settleOrder")
    public CommonResult<SettleOrderResp> settleOrder(@RequestBody @Valid SettleOrderRequest request) throws Exception {

        LoginMember loginMember = MallSecurityUtils.getLoginMember();
        Long memberId = MallSecurityUtils.getMemberId();
        Long branchId = loginMember.getBranchId();
        String memberKey = MallSecurityUtils.getMemberKey();
        log.info("memberId:{}", memberId);
        log.info("memberKey:{}", memberKey);
        log.info("loginMember:{}", JSON.toJSON(loginMember));
        return success(orderService.settleOrder(memberId, branchId, request));
    }

    @ApiOperation("创建订单")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/createOrder")
    //!@订单 - 创建 - 1、入口
    public CommonResult<CreateOrderRespVO> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        LoginMember loginMember = MallSecurityUtils.getLoginMember();
        Long memberId = MallSecurityUtils.getMemberId();
        Long branchId = loginMember.getBranchId();
        log.info(" 商城创建订单, {} ,{} ,{}", memberId, branchId, JsonUtils.toJsonString(request));

        return orderService.saveOrder(memberId, branchId, request);
    }

    @ApiOperation("获取订单支付方式")
    @RequiresMallLogin(isMember = true)
    @GetMapping(value = "/getOrderPayWay/{orderNo}")
    public CommonResult<OrderPayWayRespDTO> getOrderPayWay(@ApiParam(name = "orderNo", value = "订单编号", required = true) @PathVariable("orderNo") String orderNo) {
        return success(orderService.getOrderPayWay(orderNo));
    }

    @ApiOperation("货到付款订单默认支付成功接口")
    @RequiresMallLogin(isMember = true)
    @PutMapping(value = "/hdfkPaySuccess")
    public CommonResult<Boolean> hdfkPaySuccess(@ApiParam(name = "orderNo", value = "订单编号") @RequestParam("orderNo") String orderNo,
                                                @ApiParam(name = "payWay", value = "支付方式 1：储值支付") @RequestParam("payWay") String payWay) {
        orderService.hdfkPaySuccess(orderNo, payWay);
        return success(Boolean.TRUE);
    }


    @ApiOperation("订单再次购买（返回结算页请求数据走正常订单流程）")
    @RequiresMallLogin(isMember = true)
    @GetMapping(value = "/orderMorePurchase")
    //!@小程序 - 7、再次购买 - 1、入口
    public CommonResult<SettleOrderRequest> orderMorePurchase(@Valid TrdOrderPageReqDTO orderPageReqDto) {
        LoginMember loginMember = MallSecurityUtils.getLoginMember();
        Long memberId = MallSecurityUtils.getMemberId();
        Long branchId = MallSecurityUtils.getBranchId();
//        orderPageReqDto.setMemberId(memberId);
        orderPageReqDto.setBranchId(branchId);
        return success(orderService.orderMorePurchase(orderPageReqDto));
    }

    /**
     * 预下单接口-补货单跳结算页
     */
    @ApiOperation("预下单接口-补货单跳结算页")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/yhSettleOrder")
    public CommonResult<SettleOrderResp> yhSettleOrder(@RequestBody @Valid YhSettleOrderRequest request) {
        Long memberId = MallSecurityUtils.getMemberId();
        Long branchId = MallSecurityUtils.getBranchId();
        request.setBranchId(branchId)
                .setMemberId(memberId);
        return success(orderService.yhSettleOrder(request));
    }

    /**
     * 预下单接口-补货单跳结算页
     */
    @ApiOperation("创建订单接口-补货单创建订单")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/yhCreateOrder")
    public CommonResult<CreateOrderRespVO> yhCreateOrder(@RequestBody @Valid YhCreateOrderRequest request) {
        Long memberId = MallSecurityUtils.getMemberId();
        Long branchId = MallSecurityUtils.getBranchId();
        request.setBranchId(branchId)
                .setMemberId(memberId);
        return success(orderService.yhCreateOrder(request));
    }

    /**
     * @Description: 分页获取订单数据
     * @Param: Integer deliveryState(0:全部 1:待付款 2:待发货 3:待收货 4:待售后 5:已完成)
     * @return: CommonResult<List < OrderVO>>
     * @Author: liuxingyu
     * @Date: 2024/3/30 9:25
     */
    @GetMapping("/queryOrderList")
    @ApiOperation("分页获取用户订单列表")
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<TrdOrderMiniHeadRespDTO>> pageOrderList(TrdOrderPageReqDTO orderPageReqDto) {
//        orderPageReqDto.setMemberId(MallSecurityUtils.getMemberId()); // 当前登录用户
        orderPageReqDto.setBranchId(MallSecurityUtils.getBranchId());   // 当前切换的门店
//        return CommonResult.success(orderService.pageOrderList(orderPageReqDto));
        return orderApi.miniPageOrderList(orderPageReqDto);
    }


        /**
    * @Description: 获取订单详情
    * @Param: Long orderId
    * @return: CommonResult<OrderVO>
    * @Author: chenmingqing
    * @Date: 2024/7/31 9:19
    */
    @GetMapping("/getOrderInfo")
    @ApiOperation("获取用户订单详情")
    @RequiresMallLogin(isMember = true)
    public CommonResult<TrdOrderRespDTO> getOrderInfo(TrdOrderPageReqDTO orderPageReqDto){
        orderPageReqDto.setBranchId(MallSecurityUtils.getBranchId());   // 当前切换的门店
        return orderApi.getOrderInfo(orderPageReqDto);
    }

    @PostMapping("/shareOrder")
    @ApiOperation(value = "分享用户订单", httpMethod = HttpMethod.POST)
    @RequiresMallLogin(isMember = true)
    public CommonResult<String> shareOrder(@RequestBody @Valid ShareOrderReqVO shareOrderReqVO){
        TrdOrderShareSaveReqVO saveReqVO = new TrdOrderShareSaveReqVO();
        saveReqVO.setOrderId(shareOrderReqVO.getOrderId());
        saveReqVO.setSupplierOrderId(shareOrderReqVO.getSupplierOrderId());
        saveReqVO.setBranchId(MallSecurityUtils.getBranchId());   // 当前切换的门店
        saveReqVO.setRemoteIp(IpUtils.getIpAddr(ServletUtils.getRequest()));
        return orderApi.shareOrder(saveReqVO);
    }

    @PostMapping("/addProductShare")
    @ApiOperation(value = "批量新增商品分享信息", httpMethod = HttpMethod.POST)
    public CommonResult<String> addProductShare(@Valid @RequestBody BatchProductShareDTO reqVO){
        return productShareApi.batchInsertProductShare(reqVO);
    }

    @GetMapping("/getSupplierOrderInfo")
    @ApiOperation("获取入驻商订单详情（不使用当前登录门店查询）")
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<OrderVO>> getSupplierOrderInfo(TrdOrderPageReqDTO orderPageReqDto){
//        orderPageReqDto.setBranchId(MallSecurityUtils.getBranchId());   // 当前切换的门店
        return CommonResult.success(orderService.pageOrderList(orderPageReqDto));
    }


    /**
     * 获取订单明细物流信息
     *
     * @param phone
     * @param courierCompanyNo
     * @param courierNumber
     * @return
     */
    @GetMapping("/getExpressInfoByCourier")
    @ApiOperation("获取订单明细物流信息")
    @RequiresMallLogin(isMember = true)
    public CommonResult<ExpressResDTO> getExpressInfoByCourier(@RequestParam(value = "phone") String phone,
                                                               @RequestParam(value = "courierCompanyNo") String courierCompanyNo,
                                                               @RequestParam(value = "courierNumber") String courierNumber) {
        ExpressResDTO expressResDTO = orderExpressApi.getExpressInfoByCourier(phone, courierCompanyNo, courierNumber).getCheckedData();
        return CommonResult.success(expressResDTO);
    }

    /**
     * 订单全国商品确认收货
     *
     * @return
     */
    @PostMapping("/nationwideProductConfirmReceive")
    @ApiOperation("订单全国商品确认收货")
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> nationwideProductConfirmReceive(Long orderId) {
        orderApi.nationwideProductConfirmReceive(orderId).getCheckedData();
        return CommonResult.success(Boolean.TRUE);
    }

    @ApiOperation("订单手动取消")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/orderCancel")
    public CommonResult<Boolean> orderCancel(String supplierOrderNo) {
        orderApi.orderCancel(supplierOrderNo).getCheckedData();
        return success(Boolean.TRUE);
    }


    @GetMapping("/getMyOrderStatusQuantity")
    @ApiOperation("获取我的订单状态数量")
    @RequiresMallLogin(isMember = true)
    public CommonResult<OrderStatusVO> getExpressInfoByCourier() {
        log.info("获取我的订单状态数量");
        Long branchId = MallSecurityUtils.getBranchId();
        OrderStatusVO orderStatus = portalCacheService.getOrderStatusTotal(branchId);
        return CommonResult.success(orderStatus);
    }


    @GetMapping("/getMonthOrderAmount")
    @ApiOperation("统计单月的订单金额")
    @RequiresMallLogin(isMember = true)
    public CommonResult<OrderAmountStatisticsVO> monthOrderAmountStatistics() {
        log.info("统计单月的订单金额");
        OrderAmountStatisticsVO orderAmount = orderService.getMonthOrderAmountStatistics();
        return CommonResult.success(orderAmount);

    }

    @PostMapping("/getBranchHdfkList")
    @ApiOperation(value = "获取货到付款待付款订单列表", httpMethod = HttpMethod.POST)
    @RequiresMallLogin(isMember = true)
    public CommonResult<List<BranchHdfkOrderRespVO>> getBranchHdfkList(@RequestBody BranchHdfkReqVO branchHdfkReqVO) {
        branchHdfkReqVO.setBranchId(MallSecurityUtils.getLoginMember().getBranchId());
        return success(hdfkApi.getBranchHdfkList(branchHdfkReqVO).getCheckedData());
    }

    @PostMapping("/getBranchHdfkDetail")
    @ApiOperation(value = "获取货到付款待付款订单详情", httpMethod = HttpMethod.POST)
    @RequiresMallLogin(isMember = true)
    public CommonResult<BranchHdfkOrderRespVO> getBranchHdfkDetail(@RequestBody BranchHdfkReqVO branchHdfkReqVO) {
//        branchHdfkReqVO.setBranchId(MallSecurityUtils.getLoginMember().getBranchId());
        return success(null);
    }

    @GetMapping("/getBranchHdfkTotal")
    @ApiOperation(value = "获取货到付款待付款订单统计", httpMethod = HttpMethod.GET)
    @RequiresMallLogin(isMember = true)
    public CommonResult<HdfkNoPaymentTotalDTO> getBranchHdfkTotal() {
        AccAccountDTO account = portalCacheService.getAccount(MallSecurityUtils.getLoginMember().getBranchId(), MerchantTypeEnum.BRANCH_DEBT.getType(), PayChannelEnum.NONE.getCode());
        return success(new HdfkNoPaymentTotalDTO(account.getWithdrawableAmt()));
    }


    @ApiOperation("保存司机评价")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/saveDriverRating")
    public CommonResult<Boolean> saveDriverRating(@RequestBody DriverRatingReqVO DriverRatingReqVO) {
        LoginMember loginMember = MallSecurityUtils.getLoginMember();
        DriverRatingReqVO.setSysCode(loginMember.getSysCode());
        return success(driverApi.saveDriverRating(DriverRatingReqVO).getData());
    }

    @ApiOperation("获取司机评价信息")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/getDriverRatingInfo")
    public CommonResult<DriverRatingDTO> getDriverRatingInfo(@RequestParam("driverRatingId") String driverRatingId) {
        return success(driverApi.getDriverRatingInfo(Long.parseLong(driverRatingId)).getCheckedData());
    }

    @ApiOperation("获取司机基础信息")
    @RequiresMallLogin(isMember = true)
    @PostMapping(value = "/getDriverInfo")
    public CommonResult<DriverDTO> getDriverInfo(@RequestParam("driverId") String driverId) {
        return success(driverApi.getDriverInfo(Long.parseLong(driverId)).getCheckedData());
    }


    @ApiOperation("查询入驻商订单发票")
    @PostMapping(value = "/getSupplierOrderInvoice")
    @ResponseBody
    public CommonResult<TrdSupplierOrderInvoiceRespDTO> getSupplierOrderInvoice(@RequestBody @Valid TrdSupplierOrderInvoicePageReqDTO reqDTO) {
        return supplierOrderApi.getSupplierOrderInvoice(reqDTO);
    }

    /**
     * 根据入驻商订单号查询包裹和包裹明细
     * @param trdPackageReq 请求参数
     * @return 包裹和包裹明细信息
     */
    @PostMapping(value ="/getPackInfoBySupplierNo")
    @ApiOperation("根据入驻商订单号查询包裹和包裹明细")
    @RequiresMallLogin(isMember = true)
    public CommonResult<List<TrdPackageRepDto>> getPackInfoBySupplierNo(@RequestBody @Valid TrdPackageReq trdPackageReq) {
        return orderApi.getPackInfoBySupplierNo(trdPackageReq);
    }
    
    @ApiOperation("检查订单是否已经支付了")
    @RequiresMallLogin(isMember = true)
    @GetMapping(value = "/checkOrderHasPay")
    public CommonResult<Boolean> checkOrderHasPay(@ApiParam(name = "orderNo", value = "订单编号") @RequestParam("orderNo") String orderNo) {
        return success(orderService.checkOrderHasPay(orderNo));
    }

}
