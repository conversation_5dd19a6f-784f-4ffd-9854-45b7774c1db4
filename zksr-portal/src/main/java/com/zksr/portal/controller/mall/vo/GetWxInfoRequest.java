package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;


@Data
@ApiModel("GetWxInfo 接口请求")
public class GetWxInfoRequest {

    @ApiModelProperty(value = "code 必填")
    @NotBlank(message = "code不能为空")
    private String code;

    @ApiModelProperty(value = "sysSource 必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;//用来区分合伙人的标识，合伙人表中唯一

}
