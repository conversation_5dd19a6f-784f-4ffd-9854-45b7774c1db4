package com.zksr.portal.controller.mall.vo.car;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.domain.dto.car.AppCarStorageDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.serialize.LongToDateStringSerializer;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/12 11:07
 */

@Data
@ApiModel(description = "分组商品详情数据")
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarPageSupplierGroupItemVO {

    @ApiModelProperty(value = "购物车ID信息")
    private AppCarIdDTO carId;

    @ApiModelProperty("市场价")
    private BigDecimal markPrice = BigDecimal.ZERO;

    @ApiModelProperty("剩余库存")
    private BigDecimal stockQty = BigDecimal.ZERO;

    @ApiModelProperty("根据单位换算最大可加入购物车库存")
    private Long maxQty = NumberPool.LONG_ZERO;

    @ApiModelProperty("商品数量")
    private Integer productNum;

    @ApiModelProperty("业务员推荐数量")
    private Integer recommendNum = 0;

    @ApiModelProperty("是否选中")
    private Boolean selected;

    @ApiModelProperty(value = "是否业务员推荐, 0 否, 1 是")
    private Integer recommendFlag = 0;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("spu图片")
    private String spuThumb;

    @ApiModelProperty("sku图片")
    private String skuThumb;

    @ApiModelProperty("规格名称")
    private String specName;

    @ApiModelProperty("sku规格信息")
    private String skuProperties;

    @ApiModelProperty("sku规格单位")
    private Long skuUnit;

    /** 起订 */
    @ApiModelProperty("起订数")
    private Long minOq;

    /** 订货组数 */
    @ApiModelProperty("订货组数")
    private Long jumpOq;

    /** 限购 */
    @ApiModelProperty("限购数量")
    private Long maxOq;

    /** 上架状态-数据字典 */
    @ApiModelProperty("上架状态,1-已上架,其他状态都不可购买")
    private Integer shelfStatus;

    @ApiModelProperty(value = "预计应付金额")
    private BigDecimal payAmt;

    @ApiModelProperty(value = "秒杀/特价 活动价格")
    private BigDecimal activityPrice;

    @ApiModelProperty(value = "秒杀/特价 活动数量")
    private Integer activityNum;

    @ApiModelProperty(value = "参与活动应付金额", notes = "会根据促销计算实时调整")
    private BigDecimal activityPayAmt;

    @ApiModelProperty(value = "促销活动")
    private List<ActivityLabelInfoVO> activityList = new ArrayList<>();

    @ApiModelProperty(value = "商品促销标签列表")
    private List<CarSpuActivityLabelVO> spuActivityLabelList = new ArrayList<>();

    @ApiModelProperty(value = "unit, 单位")
    private String unit = StringPool.ZERO;

    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitType}
     */
    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位")
    private Integer unitSize = 1;

    @ApiModelProperty(value = "unit, 单位名称")
    private String unitName;

    @ApiModelProperty(value = "和最小单位库存转换比例")
    private BigDecimal stockConvertRate;

    @ApiModelProperty(value = "组合促销商品ID")
    private Long spuCombineId;

    @ApiModelProperty("0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    @ApiModelProperty(value = "图片素材")
    private String materialUrl;

    @ApiModelProperty(value = "SPU小单位具体单位")
    private Long minUnit;

    /*@JsonIgnore*/
    @ApiModelProperty(value = "加入购物车时间,毫秒值")
    @JsonSerialize(using = LongToDateStringSerializer.class)
    private Long addTime;

    @JsonIgnore
    public Long getSkuId() {
        return carId.getSkuId();
    }

    public CarPageSupplierGroupItemVO() {
    }

    public CarPageSupplierGroupItemVO(AppCarIdDTO carId, SpuDTO spu, SkuDTO sku, AppCarStorageDTO storageDTO) {
        this.setCarId(carId)
                .setSpuName(spu.getSpuName())
                .setSkuThumb(spu.getThumb())
                .setSkuProperties(sku.getProperties())
                .setSkuUnit(sku.getUnit())
                .setStockQty(sku.getStock())
                .setMinOq(sku.getMinOq())
                .setJumpOq(sku.getJumpOq())
                .setMaxOq(sku.getMaxOq())
                .setProductNum(storageDTO.getProductNum())
                .setRecommendFlag(storageDTO.getRecommendFlag())
                .setSpecName(spu.getSpecName());
    }

    public boolean isSpuCombineProduct() {
        return itemType == NumberPool.INT_ONE;
    }

    public String uniqueSkuKey() {
        if (itemType == NumberPool.INT_ZERO)
            return StringUtils.format("{}:{}", carId.getSkuId(), itemType);
        return StringUtils.format("{}:{}", spuCombineId, itemType);
    }
}
