package com.zksr.portal.controller.mall.vo.spu;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zksr.common.elasticsearch.model.dto.SearchRecommendRate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 猜你喜欢请求
 * @date 2025/2/5 9:36
 */
@Data
@ApiModel(description = "搜索推荐, 猜你喜欢 - request vo")
public class GuessLikeReqVO {

    @ApiModelProperty(value = "推荐商品数量", required = true)
    @Max(value = 20, message = "最大可获取商品数量20")
    @NotNull
    private Integer size;

    /** 商品类型 com.zksr.common.core.enums.ProductType */
    @ApiModelProperty("商品类型 local-本地, global-全国, group-全部")
    private String productType;

    @ApiModelProperty(value = "管理分类推荐指数")
    @Size(min = 0, max = 20, message = "最大20条推荐规则")
    private List<SearchRecommendRate> categoryRecommendRateList;

    @JsonIgnore
    public BigDecimal getFrequentlyRate() {
        if (Objects.isNull(categoryRecommendRateList)) {
            return null;
        }
        for (SearchRecommendRate recommendRate : categoryRecommendRateList) {
            if (recommendRate.getType() == 0) {
                return recommendRate.getRate();
            }
        }
        return null;
    }
}
