package com.zksr.portal.controller.mall.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.zksr.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CarGoodsDTO {

    /** 购物车id */
    private Long carId;

    /** 入驻商上架商品id */
    private Long supplierItemId;

    /** 城市上架商品id */
    private Long areaItemId;

    /** 商品SPU id */
    private Long spuId;

    /** 商品sku id */
    private Long skuId;

    /** 数量 */
    private Long qty;
}
