package com.zksr.portal.controller.mall.vo.order;

import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 货到付款支付请求
 * @date 2024/5/29 11:05
 */
@Data
@ApiModel("小程序支付请求")
public class PotalHdfkPayOrderSubmitReqVO extends PayOrderSubmitReqVO {

    @ApiModelProperty("平台来源")
    private String sysSource;

    @ApiModelProperty("入驻商订单商品列表")
    private List<Long> supplierOrderDtlIdList;

    @ApiModelProperty("订单id")
    private Long orderId;

    @ApiModelProperty("入驻商订单ID")
    private Long supplierOrderId;

    @ApiModelProperty("入驻商ID")
    private Long supplierId;
}
