package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.portal.controller.mall.vo.activity.ActivityFgBgDetailVO;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("preSaveOrder 预下单接口响应对象")
public class SettleOrderResp {


    @ApiModelProperty(value = "购物项数组", required = true)
    private List<Item> items;

    @ApiModelProperty(value = "费用", required = true)
    private Price price;

    @ApiModelProperty(value = "配送至-收获地址", required = true)
    private Address address;

    @ApiModelProperty(value = "支付方式数组", required = true)
    private List<PayWay> payWays;

    @ApiModelProperty(value = "买赠、满赠赠品活动返回详情列表")
    private List<ActivityFgBgDetailVO> fgBgDetailVOList;

    @ApiModelProperty(value = "促销活动提示信息")
    private String activityTipsInfo;

    @ApiModelProperty(value = "是否能下单（默认1），1：是 0：否")
    private Long isOrderConfirm = NumberPool.LONG_ONE;

    @ApiModelProperty(value = "门店余额账户信息")
    private BranchBalance branchBalance;

    @ApiModel(value = "购物项")
    @Data
    public static class Item {

        @ApiModelProperty(value = "入驻商id", required = true)
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long supplierId;

        @ApiModelProperty(value = "入驻商名", required = true)
        private String supplierName;

        @ApiModelProperty(value = "入驻商-购物项数组", required = true)
        private List<SupplierItem> supplierItems;

        @ApiModelProperty(value = "入驻商配送标签, 来自平台商字典")
        private String productDistributionLabel;

        @ApiModel(value = "购物项")
        @Data
        public static class SupplierItem {

            // ========== SPU 信息 ==========
            @ApiModelProperty(value = "品类编号", required = true)
            @JsonSerialize(using = CustomLongSerialize.class)
            private Long categoryId;

            @ApiModelProperty(value = "SPU 编号", required = true)
            @JsonSerialize(using = CustomLongSerialize.class)
            private Long spuId;

            @ApiModelProperty(value = "SPU编码", required = true)
            private String spuNo;

            @ApiModelProperty(value = "SPU 名字", required = true)
            private String spuName;

            // ========== SKU 信息 ==========
            @ApiModelProperty(value = "SKU 编号", required = true)
            @JsonSerialize(using = CustomLongSerialize.class)
            private Long skuId;

            @ApiModelProperty(value = "商品SKU条码")
            private String barcode;

            @ApiModelProperty(value = "价格", required = true)
            private BigDecimal price;

            @ApiModelProperty(value = "图片地址", required = true)
            private String picUrl;

            @ApiModelProperty(value = "属性数组", required = true)
            private String properties;

            @ApiModelProperty(value = "入驻商上架商品id")
            @JsonSerialize(using = CustomLongSerialize.class)
            private Long supplierItemId;

            @ApiModelProperty(value = "城市上架商品id")
            @JsonSerialize(using = CustomLongSerialize.class)
            private Long areaItemId;

            @ApiModelProperty(value = "城市上架商品id")
            private String itemScopeLable;

            // ========== 购物车信息 ==========
            @ApiModelProperty(value = "购物车编号, 组合键", required = true)
            private String carId;

            @ApiModelProperty(value = "购买数量", required = true, example = "1")
            private Integer count;

            // ========== 优惠信息 ==========
            @ApiModelProperty(value = "优惠劵优惠金额（分摊的）")
            private BigDecimal couponDiscountAmt;

            @ApiModelProperty(value = "优惠劵优惠金额（不分摊的）")
            private BigDecimal couponDiscountAmt2;

            @ApiModelProperty(value = "活动优惠金额")
            private BigDecimal activityDiscountAmt;

            @ApiModelProperty(value = "活动数量")
            private Integer activityCount;

            @ApiModelProperty(value = "活动单价")
            private BigDecimal activityPrice;

            @ApiModelProperty(value = "是否参与秒杀、特价活动  1：是 0： 否")
            private Integer isActivitySpSk;

            @ApiModelProperty(value = "下单商品单位")
            private String unit;

            @ApiModelProperty(value = "下单商品大小")
            private Integer unitSize;

            @ApiModelProperty(value = "组合商品明细数据")
            private List<SpuCombineSkuVO> spuCombineSkuVOList;
        }

    }

    @ApiModel(value = "费用（合计）")
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Price {

        @ApiModelProperty(value = "商品原价（总），单位：分", required = true, example = "500")
        private BigDecimal totalPrice;

        @ApiModelProperty(value = "订单优惠（总），单位：分", required = true, example = "150")
        private BigDecimal totalDiscountPrice;

//        @ApiModelProperty(value = "运费金额，单位：分", requiredMode = Schema.RequiredMode.REQUIRED, example = "50")
//        private Integer deliveryPrice;

        @ApiModelProperty(value = "优惠劵减免金额，单位：分", required = true, example = "100")
        private BigDecimal totalCouponDiscountPrice;

        @ApiModelProperty(value = "活动减免的金额，单位：分", required = true, example = "50")
        private BigDecimal totalActivityDiscountPrice;

        @ApiModelProperty(value = "实际支付金额（总），单位：分", required = true, example = "450")
        private BigDecimal payPrice;

    }

    @ApiModel(description = "地址信息")
    @Data
    public static class Address {

        @ApiModelProperty(value = "门店编号", required = true)
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long branchId;

        @ApiModelProperty(value = "收件人名称", required = true)
        private String branchName;

        @ApiModelProperty(value = "收件人名称", required = true)
        private String contactName;

        @ApiModelProperty(value = "手机号", required = true)
        private String contactPhone = "";

        @ApiModelProperty(value = "门店地址", required = true)
        private String branchAddr;

        @ApiModelProperty(value = "经度")
        private BigDecimal longitude;

        @ApiModelProperty(value = "纬度")
        private BigDecimal latitude;

        // ========== 门店其他信息 ==========
//        @ApiModelProperty(value = "城市id")
//        private Long areaId;
//
//        @ApiModelProperty(value = "渠道id")
//        private Long channelId;
//
//        /** 平台商城市分组id */
//        @ApiModelProperty(value = "平台商城市分组id")
//        private Long groupId;
//
//        @ApiModelProperty(value = "业务员ID")
//        private Long colonelId;
//
//        @ApiModelProperty(value = "业务员级别")
//        private Long colonelLevel;
//
//        @ApiModelProperty(value = "上级业务员ID")
//        private Long pColonelId;
//
//        @ApiModelProperty(value = "上级业务员级别")
//        private Long pColonelLevel;

//        @ApiModelProperty(value = "是否默认收件地址", required = true, example = "true")
//        private Boolean defaultStatus;

    }

//    @ApiModel(description = "支付方式")
//    @Data
//    @NoArgsConstructor
//    @AllArgsConstructor
//    public static class PayWay {
//        @ApiModelProperty(value = "支付方式编码（0-在线支付 1-储值支付 2-货到付款）")
//        private String payWayCode;
//
//        @ApiModelProperty(value = "支付方式名称")
//        private String payWayName;
//    }

}
