package com.zksr.portal.controller.mall;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.*;
import com.zksr.trade.api.order.OrderApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 用户发票Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Api(tags = "商城用户发票接口")
@Validated
@RestController
@RequestMapping("/mall/memberInvoice")
public class MemberInvoiceController {
    @Resource
    private MemberApi memberApi;
    @Resource
    private OrderApi orderApi;

    @ApiOperation("新增用户发票")
    @PostMapping(value = "/addMemberInvoice")
    @ResponseBody
    public CommonResult<Long> addMemberInvoice(@RequestBody @Valid MemMemberInvoiceSaveReqDTO createReqDTO) {

//        createReqDTO.setMemberId(MallSecurityUtils.getMemberId());

        return success(memberApi.addMemberInvoice(createReqDTO).getCheckedData());
    }

    @ApiOperation("修改用户发票")
    @PostMapping(value = "/updateMemberInvoice")
    @ResponseBody
    public CommonResult<Boolean> updateMemberInvoice(@RequestBody @Valid MemMemberInvoiceSaveReqDTO createReqDTO) {
//        createReqDTO.setMemberId(MallSecurityUtils.getMemberId());
        memberApi.updateMemberInvoice(createReqDTO);
        return success(Boolean.TRUE);
    }

    @ApiOperation("查询用户发票")
    @PostMapping(value = "/getMemberInvoice")
    @ResponseBody
    public CommonResult<PageResult<MemMemberInvoiceRespDTO>> getMemberInvoice(@RequestBody @Valid MemMemberInvoicePageReqDTO pageReqVO) {
        return memberApi.getMemberInvoice(pageReqVO);
    }


}
