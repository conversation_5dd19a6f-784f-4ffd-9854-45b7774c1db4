package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("门店列表 接口响应")
public class BranchReqVo {


    /** 门店id */
    @ApiModelProperty(value = "门店id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    /** 平台商id */
    @ApiModelProperty(value = "平台商id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long sysCode;

    /** 门店名称 */
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    /** 城市id */
    @ApiModelProperty(value = "城市id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 城市名称 */
    @ApiModelProperty(value = "城市名称")
    private String areaName;

    /** 业务员id */
    @ApiModelProperty(value = "业务员id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    /** 门店地址 */
    @ApiModelProperty(value = "门店地址")
    private String branchAddr;

    /** 经度 */
    @ApiModelProperty(value = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @ApiModelProperty(value = "纬度")
    private BigDecimal latitude;

    /** 渠道id */
    @ApiModelProperty(value = "渠道id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long channelId;

    /** 平台商城市分组id */
    @ApiModelProperty(value = "平台商城市分组id")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long groupId;

    /** 联系人 */
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 备注 */
    @ApiModelProperty(value = "备注")
    private String memo;

    /** 状态 */
    @ApiModelProperty(value = "状态")
    private Integer status;

    /** 删除状态(0:正常，2：删除) */
    @ApiModelProperty(value = "删除状态(0:正常，2：删除)")
    private Integer delFlag;

    /** 审核人 */
    @ApiModelProperty(value = "审核人")
    private String auditBy;

    /** 审核时间 */
    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核状态 */
    @ApiModelProperty(value = "审核状态")
    private Integer auditState = 0;

    /** 过期时间 */
    @ApiModelProperty(value = "过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDate; 		 // 过期时间

    @ApiModelProperty(value = "业务员级别")
    private Long colonelLevel;

    @ApiModelProperty(value = "上级业务员ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long pColonelId;

    @ApiModelProperty(value = "上级业务员级别")
    private Long pColonelLevel;

    /** 价格码-数据字典（1，2，3，4，5，6）） */
    @ApiModelProperty(value = "价格码-数据字典sys_price_code")
    private Long salePriceCode;

    /** 是否默认 */
    @ApiModelProperty(value = "是否默认")
    private String isDefault = "N";
}
