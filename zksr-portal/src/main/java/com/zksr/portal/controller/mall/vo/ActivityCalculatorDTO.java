package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024年05月22日 10:58
 * @description: ActivityCalculatorDTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("营销活动 计算信息DTO")
public class ActivityCalculatorDTO {

    @ApiModelProperty(value = "订单命中活动商品总金额")
    private BigDecimal totalAmt;

    @ApiModelProperty(value = "订单命中活动商品总数量")
    private Integer totalQty;

    @ApiModelProperty(value = "命中活动优惠总金额")
    private BigDecimal discountAmt;
}
