package com.zksr.portal.controller.mall.vo.spu;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 店铺集合展示, 简单商品数据
 * @date 2024/9/10 15:13
 */
@Data
@ApiModel(description = "店铺集合展示, 简单商品数据")
@AllArgsConstructor
@NoArgsConstructor
public class SupplierListSpuRespVO {

    @ApiModelProperty("商品列表")
    private List<SkuPageRespVO> itemList;

    @ApiModelProperty("入驻商ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    @ApiModelProperty("入驻商名称")
    private String supplierAvatar;

}
