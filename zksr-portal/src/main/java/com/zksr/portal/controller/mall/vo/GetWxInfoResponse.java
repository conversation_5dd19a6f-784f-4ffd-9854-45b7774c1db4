package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("GetWxInfo 接口响应")
public class GetWxInfoResponse {

    @ApiModelProperty(value = "微信小程序openid", example = "12321421213")
    private String openid;

    @ApiModelProperty(value = "会话秘钥")
    private String sessionKey;

    @ApiModelProperty(value = "微信unionid", example = "5123213")
    private String unionid;

    @ApiModelProperty(value = "是否新用户：1是0否", example = "1")
    private String isNew;//是否新用户：1是0否

    @ApiModelProperty(value = "手机号（如果是老用户，则返回）", example = "13412344321")
    private String memberPhone;//手机号（如果是老用户，则返回）

    @ApiModelProperty(value = "昵称", required = true, example = "小明")
    private String memberName;

    @ApiModelProperty(value = "头像", required = true, example = "xxx")
    private String avatar;

}
