package com.zksr.portal.controller.mall.vo.car;

import com.zksr.common.core.enums.ProductType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新增购物车商品
 * @date 2024/3/26 16:51
 */
@NoArgsConstructor
@Data
@ApiModel(description = "删除购物车商品VO")
public class CarRemoveReqVO {

    @ApiModelProperty(value = "购物车ID",required = true)
    private List<String> carId;

    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;

    @ApiModelProperty(value = "商品类型 local-本地商品, global-全国商品", required = true, example = "local")
    private String productType = ProductType.LOCAL.getType();

    @ApiModelProperty(value = "操作类型, 0-单个, 1-全选(重置购物车)", required = true)
    private Integer opType;

    public CarRemoveReqVO(List<String> carId, Long branchId, String productType, Integer opType) {
        this.carId = carId;
        this.branchId = branchId;
        this.productType = productType;
        this.opType = opType;
    }
}
