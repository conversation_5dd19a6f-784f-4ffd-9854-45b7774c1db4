package com.zksr.portal.controller.mall;

import cn.hutool.core.collection.ListUtil;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.AccountFlowApi;
import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.account.vo.ApiAccAccountFlowPageVO;
import com.zksr.account.api.account.vo.BranchBalanceRespVO;
import com.zksr.account.api.recharge.RechargeApi;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveReqVO;
import com.zksr.account.api.recharge.vo.BranchRechargeSaveRespVO;
import com.zksr.account.api.withdraw.WithdrawApi;
import com.zksr.account.api.withdraw.dto.WithdrawDTO;
import com.zksr.account.api.withdraw.vo.AccWithdrawPageReqVO;
import com.zksr.account.api.withdraw.vo.BranchWithdrawReqVO;
import com.zksr.common.core.business.AccountBusiTypeField;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.enums.MerchantTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.portal.controller.mall.vo.branch.BranchReqVO;
import com.zksr.portal.controller.mall.vo.branch.BranchWxMerchantStateRespVO;
import com.zksr.portal.controller.mall.vo.recharge.RechargeConsumeReqVO;
import com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO;
import com.zksr.portal.convert.pay.AccountFlowConvert;
import com.zksr.portal.service.mall.IMemBranchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * @Description: 门店模块Controller
 * @Author: liuxingyu
 * @Date: 2024/6/7 15:47
 */
@RestController
@RequestMapping("/mall/memBranch")
@Api(tags = "商城 - 门店接口")
public class MemBranchController {

    @Resource
    private RechargeApi rechargeApi;

    @Resource
    private AccountFlowApi accountFlowApi;

    @Resource
    private AccountApi accountApi;

    @Autowired
    private IMemBranchService branchService;

    @Resource
    private BranchApi branchApi;

    @Resource
    private WithdrawApi withdrawApi;

    /**
     * @Description: 添加门店
     * @Param: BranchReqVO
     * @return: Long
     * @Author: liuxingyu
     * @Date: 2024/6/7 16:34
     */
    @PostMapping("/addBranch")
    @ApiOperation(value = "添加门店", httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<Long> addBranch(@Valid @RequestBody BranchReqVO branchReqVO) {
        return success(branchService.addBranch(branchReqVO));
    }

    /**
     * 获取门店是否需要认证, 系统内部决策, 当前门店是否认证
     */
    @GetMapping("/getWxMerchantAuthState")
    @ApiOperation(value = "获取门店商家助手认证状态", httpMethod = HttpMethod.GET)
    @RequiresMallLogin(isMember = true)
    public CommonResult<BranchWxMerchantStateRespVO> getWxMerchantAuthState(@ApiParam(value = "微信授权openid", name = "openid") String openid) {
        if (StringUtils.isEmpty(openid)) {
            openid = StringPool.EMPTY;
        }
        return success(branchService.getWxMerchantAuthState(openid));
    }

    /**
     * 同步门店商户助手(预录入)
     */
    @GetMapping("/syncWxMerchant")
    @ApiOperation(value = "同步门店商户助手(预录入)", httpMethod = HttpMethod.GET)
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> syncWxMerchant() {
        branchService.syncWxMerchant();
        return success(Boolean.TRUE);
    }

    /**
     * 保存微信b端商户授权信息
     */
    @PutMapping("/saveWxAuthOpenid")
    @ApiOperation(value = "保存微信b端商户授权信息", httpMethod = HttpMethod.PUT)
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> saveWxAuthOpenid(@Valid @NotNull @NotEmpty @RequestParam("openid") String openid) {
        branchService.saveWxAuthOpenid(openid);
        return success(Boolean.TRUE);
    }


        /**
     * @Description: 编辑门店
     * @Param: BranchReqVO
     * @return: Long
     * @Author: liyi
     * @Date: 2024/12/19 16:34
     */
    @PostMapping("/editBranch")
    @ApiOperation(value = "编辑门店", httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<Boolean> editBranch(@Valid @RequestBody MemBranchSaveReqVO branchReqVO) {
        return success(branchApi.edit(branchReqVO).getCheckedData());
    }

    /**
     * 获取门店详情
     */
    @GetMapping("/getBranchDetail")
    @ApiOperation(value = "获取门店详情", httpMethod = HttpMethod.GET)
    @RequiresMallLogin(isMember = true)
    public CommonResult<BranchDTO> getBranchDetail(@RequestParam("branchId") Long branchId) {
        return success(branchApi.getByBranchId(branchId).getCheckedData());
    }


    @ApiOperation(value = "门店发起充值", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/branchRecharge")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<BranchRechargeSaveRespVO> branchRecharge(@Valid @RequestBody BranchRechargeSaveReqVO reqVO) {
        reqVO.setBranchId(MallSecurityUtils.getBranchId());
        return rechargeApi.createBranchRecharge(reqVO);
    }

    @ApiOperation(value = "获取门店储值消费记录", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/branchBill")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<RechargeConsumeRespVO>> branchBill(@Valid @RequestBody RechargeConsumeReqVO reqVO) {
        ApiAccAccountFlowPageVO flowPageVO = AccountFlowConvert.INSTANCE.convertFlowPageVO(reqVO);
        flowPageVO.setMerchantId(MallSecurityUtils.getBranchId());
        flowPageVO.setMerchantType(MerchantTypeEnum.BRANCH.getType());
        flowPageVO.setProcessFlag(NumberPool.INT_ONE);
        flowPageVO.setBusiFields(AccountBusiTypeField.WITHDRAWABLE_AMT.getField());
        PageResult<RechargeConsumeRespVO> pageResult = accountFlowApi.getBranchAccountFlowPge(flowPageVO).getCheckedData();
        return CommonResult.success(pageResult);
    }

    @ApiOperation(value = "获取门店账户余额", httpMethod = HttpMethod.GET)
    @GetMapping(value = "/getBranchBalance")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<BranchBalanceRespVO> getBranchBalance() {
        return accountApi.getBranchBalance(MallSecurityUtils.getBranchId());
    }

    @ApiOperation(value = "门店余额提现", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/balanceWithdraw")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> balanceWithdraw(@RequestBody BranchWithdrawReqVO withdrawReqVO) {
        withdrawReqVO.setBranchId(MallSecurityUtils.getBranchId());
        withdrawApi.createBranchWithdraw(withdrawReqVO).checkError();
        return success(Boolean.TRUE);
    }

    /**
     * 获取门店提现列表
     */
    @PostMapping("/withdrawPage")
    @ApiOperation(value = "获取门店提现列表", httpMethod = HttpMethod.POST )
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<WithdrawDTO>> withdrawPage(@RequestBody @Valid AccWithdrawPageReqVO pageReqVO) {
        // 入业务员 / 业务员
        pageReqVO.setMerchantTypeList(ListUtil.toList(MerchantTypeEnum.BRANCH.getType()));
        pageReqVO.setMerchantId(MallSecurityUtils.getBranchId());
        return withdrawApi.getWithdrawPage(pageReqVO);
    }
}
