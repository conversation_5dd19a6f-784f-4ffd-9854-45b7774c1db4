package com.zksr.portal.controller.mall.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("authLogin 接口响应")
public class AuthLoginResponse {
    @ApiModelProperty(value = "生成的token码", required = true, example = "1")
    private String token;//生成的token码

    @ApiModelProperty(value = "用户id", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long memberId;//用户id

    @ApiModelProperty(value = "昵称", required = true, example = "1")
    private String nickname;//昵称

    @ApiModelProperty(value = "头像", required = true, example = "1")
    private String avatar;//头像

    @ApiModelProperty(value = "过期时间", required = true, example = "1")
    private Long expiresIn;//过期时间

    @ApiModelProperty(value = "默认门店ID", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long  branchId; //默认门店ID

    @ApiModelProperty(value = "默认门店名称", required = true, example = "1")
    private String branchName;//默认门店名称

    @ApiModelProperty(value = "默认门店地址", required = true, example = "1")
    private String branchAddr;//默认门店地址

    @ApiModelProperty(value = "用户账号/手机号", required = true, example = "1")
    private String userName;//用户账号/手机号

    /** 是否为店长用户 */
    @ApiModelProperty(value = "是否为店长用户 0否 1是")
    private Integer isShopManager;

    /**
     * 城市id
     */
    @ApiModelProperty(value = "城市id", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /**
     * 业务员ID
     */
    @ApiModelProperty(value = "业务员ID", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long colonelId;

    @ApiModelProperty(value = "业务员联系电话", required = true, example = "1")
    private String colonelPhone;//业务员联系电话

    @ApiModelProperty(value = "业务员名称", required = true, example = "1")
    private String colonelName;//业务员名称

    /**
     * 是否存在门店 0: 已存在门店  1: 未存在门店，未提交门店信息 2: 未存在门店，已提交门店信息
     */
    @ApiModelProperty(value = "是否存在门店 0: 已存在门店  1: 未存在门店，未提交门店信息 2: 未存在门店，已提交门店信息", required = true, example = "1")
    private Integer isCreateBranch;


}
