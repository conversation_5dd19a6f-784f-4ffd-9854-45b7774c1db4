package com.zksr.portal.controller.mall;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsProductGroup;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.prdt.ActivitySkuSearchReqVO;
import com.zksr.portal.controller.mall.vo.spu.GuessLikeReqVO;
import com.zksr.portal.controller.mall.vo.spu.SpuItemListReqVO;
import com.zksr.portal.controller.mall.vo.spu.SupplierListSpuReqVO;
import com.zksr.portal.controller.mall.vo.spu.SupplierListSpuRespVO;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.portal.service.mall.ISkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@RequestMapping("/mall/spu")
@Api(tags = "商城 - 商品接口")
@Slf4j
public class SpuController {

    @Autowired
    private ISkuService skuService;

    @PostMapping("/areaSpuPagePost")
    @ApiOperation( "获得商品 SPU 分页 - 本地配送商品")
    @RequiresMallLogin
    public CommonResult<PageResult<SkuPageRespVO>> areaSpuPagePost(@RequestBody @Valid SpuPageReqVO pageVO) {
        return success(skuService.areaSpuPage(pageVO));
    }
    
    @PostMapping("/areaRetailSpuPagePost")
    @ApiOperation( "获得零售本地商品")
    @RequiresMallLogin
    //!@小程序 - 4、零售商品分页
    public CommonResult<PageResult<SkuPageRespVO>> supplierRetailSpuPagePost(@RequestBody @Valid SpuPageReqVO pageVO) {
        return success(skuService.areaRetailSpuPage(pageVO));
    }

    @GetMapping("/areaSpuDetail")
    @ApiOperation( "获得商品 SPU 详情-本地配送商品")
    @RequiresMallLogin
    public CommonResult<SpuDetailRespVO> areaSpuDetail(@RequestParam("areaItemId") Long areaItemId) {
        return success(skuService.areaSpuDetail(areaItemId));
    }

    @PostMapping("/supplierSpuPagePost")
    @ApiOperation( "获得商品 SPU 分页 - 全国")
    @RequiresMallLogin
    public CommonResult<PageResult<SkuPageRespVO>> supplierSpuPagePost(@RequestBody @Valid SpuPageReqVO pageVO) {
        return success(skuService.supplierSpuPage(pageVO));
    }
    
  

    @GetMapping("/supplierSpuDetail")
    @ApiOperation( "获得商品 SPU 详情-全国")
    @RequiresMallLogin
    public CommonResult<SpuDetailRespVO> supplierSpuDetail(@RequestParam("supplierItemId") Long supplierItemId) {
        return success(skuService.supplierSpuDetail(supplierItemId));
    }

    @Deprecated
    @PostMapping("/areaItemSpuDetailList")
    @ApiOperation( value = "获得本地配送商品列表, 仅本地", httpMethod = HttpMethod.POST, notes = "可以通过spuItemListDetailList这个接口平替, 此接口可以作废了")
    @RequiresMallLogin
    public CommonResult<List<SkuPageRespVO>> areaItemSpuDetailList(@RequestBody SpuItemListReqVO reqVO) {
        return success(skuService.areaItemSpuDetailList(reqVO));
    }

    @PostMapping("/spuItemListDetailList")
    @ApiOperation( value = "通过itemId查询商品集合, 支持全国和本地商品ID", httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<List<SkuPageRespVO>> spuItemListDetailList(@RequestBody SpuItemListReqVO reqVO) {
        return success(skuService.spuItemListDetailList(reqVO));
    }

    @PostMapping("/spuItemUnitListDetailList")
    @ApiOperation( value = "通过itemId和unitSize查询商品集合, 支持全国和本地商品ID", httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<List<SkuPageRespVO>> spuItemUnitListDetailList(@RequestBody SpuItemListReqVO reqVO) {
        return success(skuService.spuItemUnitListDetailList(reqVO));
    }

    @PostMapping("/searchSpuDetailList")
    @ApiOperation( value = "商品搜索",  httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    //!@小程序 - 3、商品搜索
    public CommonResult<List<SkuPageRespVO>> searchSpuDetailList(@RequestBody SkuSearchReqVO reqVO){
        return success(skuService.searchSpuDetailList(ProductConvert.INSTANCE.convertProductContentPageReqDTO(reqVO)).getList());
    }
    
    @PostMapping("/searchRetailSpuDetailList")
    @ApiOperation( value = "零售商品搜索",  httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<List<SkuPageRespVO>> searchRetailSpuDetailList(@RequestBody SkuSearchReqVO reqVO){
        return success(skuService.searchRetailSpuDetailList(ProductConvert.INSTANCE.convertProductContentPageReqDTO(reqVO)).getList());
    }
    
    @PostMapping("/testSpuDetailList")
    @ApiOperation( value = "测试零售商品搜索",  httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<EsPageInfo<EsProductGroup>> testSpuDetailList(@RequestBody SkuSearchReqVO reqVO){
        return success(skuService.testSpuDetailList(ProductConvert.INSTANCE.convertProductContentPageReqDTO(reqVO)));
    }

    @PostMapping("/searchSpuPropertiesList")
    @ApiOperation( value = "商品搜索属性（品牌，分类，供应商）",  httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<SpuSearchPropertiesVO> searchSpuPropertiesList(@RequestBody SkuSearchReqVO reqVO){
        return success(skuService.searchSpuPropertiesList(ProductConvert.INSTANCE.convertProductContentPageReqDTO(reqVO)));
    }
    
    @PostMapping("/searchRetailSpuPropertiesList")
    @ApiOperation( value = "[零售]商品搜索属性（品牌，分类，供应商）",  httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<SpuSearchPropertiesVO> searchRetailSpuPropertiesList(@RequestBody SkuSearchReqVO reqVO){
        return success(skuService.searchRetailSpuPropertiesList(ProductConvert.INSTANCE.convertProductContentPageReqDTO(reqVO)));
    }

    @PostMapping("/getActivityProductList")
    @ApiOperation(value = "获取商品促销活动商品", httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<PageResult<SkuPageRespVO>> getActivityProductList(@Valid @RequestBody ActivitySkuSearchReqVO reqVO){
        return success(skuService.searchActivitySpuDetailList(reqVO));
    }

    @PostMapping("/getSupplierListSpuItemCol")
    @ApiOperation(value = "获取入驻商店铺列表, 商品简略信息", httpMethod = HttpMethod.POST)
    @RequiresMallLogin
    public CommonResult<List<SupplierListSpuRespVO>> getSupplierListSpuItemCol(@Valid @RequestBody SupplierListSpuReqVO reqVO){
        if ( StringUtils.isNotEmpty(reqVO.getCondition()) && reqVO.getCondition().length() <= 1) {
            throw new ServiceException("至少搜索两个关键字");
        }
        return success(skuService.getSupplierListSpu(reqVO));
    }

    @PostMapping("/guessLike")
    @ApiOperation(value = "搜索推荐需求,猜你喜欢", httpMethod = HttpMethod.POST, notes = "搜索推荐需求,猜你喜欢")
    @RequiresMallLogin
    public CommonResult<PageResult<SkuPageRespVO>> guessLike(@Valid @RequestBody GuessLikeReqVO reqVO){
        return success(skuService.getGuessLikeList(reqVO));
    }
}
