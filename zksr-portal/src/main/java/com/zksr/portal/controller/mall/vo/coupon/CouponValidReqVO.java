package com.zksr.portal.controller.mall.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 请求有效优惠券
 * @date 2024/4/1 15:25
 */
@Data
@ToString
@ApiModel(description = "请求参数")
public class CouponValidReqVO {

    @ApiModelProperty(value = "参数, 传空对象")
    private HashMap<String, String> params;
}
