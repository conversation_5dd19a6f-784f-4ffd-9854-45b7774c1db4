package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import com.zksr.portal.controller.mall.vo.spu.SpuDetailActivityLabelVO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.property.dto.PropertyKeyDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@ApiModel("商城-SPU详情 VO对象")
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SpuDetailRespVO {

    @ApiModelProperty(value = "上架ID (适应于组合商品, 没有sku信息)", required = true)
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long itemId;

    @ApiModelProperty(value = "商品 SPU id", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty(value = "商品 SPU 编号", required = true, example = "123124")
    private String spuNo;


    // ========== 基本信息 =========

    @ApiModelProperty(value = "商品名称", required = true, example = "娃哈哈纯净水")
    private String spuName;

    @ApiModelProperty(value = "品牌名称", required = true, example = "娃哈哈")
    private String brandName;

    @ApiModelProperty(value = "商品详情", required = true, example = "我是商品描述")
    private String description;

    @ApiModelProperty(value = "商品封面图", required = true)
    private String thumb;

    @ApiModelProperty(value = "商品轮播图", required = true)
    private String images;

    /** 最旧生产日期 */
    @ApiModelProperty(value = "最旧生产日期", example = "2024-03-01 00:00:00")
    private String oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @ApiModelProperty(value = "最新生产日期", example = "2024-03-31 00:00:00")
    private String latestDate; 		 // 最新生产日期

    /** 详情信息(富文本) */
    @ApiModelProperty(value = "详情信息(富文本)")
    private String details;

    // ========== 营销相关字段 =========

//    @ApiModelProperty(value = "活动排序数组", required = true, example = "1024")
//    private List<Integer> activityOrders;

    // ========== SKU 相关字段 =========

    @ApiModelProperty(value = "是否开启多规格 1-是 0-否", required = true, example = "true")
    private Long isSpecs;

    @ApiModelProperty(value = "商品价格", required = true, example = "99.99")
    private BigDecimal markPrice;
    
    @ApiModelProperty(value = "分润金额", required = true, example = "99.99")
    private BigDecimal profitAmount;

    @ApiModelProperty(value = "促销 价格", required = true, example = "99.99") // 通过会员等级，计算出折扣后价格
    private BigDecimal promotionPrice;

    @ApiModelProperty(value = "库存", required = true, example = "666")
    private Integer stock;

    @ApiModelProperty("中单位转换比例, (一个中单位等于多少个小单位)")
    private BigDecimal midSize;

    @ApiModelProperty("大单位转换比例, (一个大单位等于多个小单位)")
    private BigDecimal largeSize;

    @ApiModelProperty("多规格属性组")
    private List<PropertyKeyDTO> propertyKeys;

    /**
     * SKU 数组
     */
    @ApiModelProperty("sku列表")
    private List<SkuDetailRespVO> skus;

    /** 商品规格 */
    @ApiModelProperty("商品规格")
    private String specName;

    /** 产地 */
    @ApiModelProperty(value = "产地", example = "示例值")
    private String originPlace;

    /** 保质期 */
    @ApiModelProperty(value = "保质期", example = "示例值")
    private Integer expirationDate; 		 // 保质期

    /** 入驻商id;入驻商id */
    @ApiModelProperty(value = "入驻商id;入驻商id", example = "示例值")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long supplierId;

    /**
     * 入驻商信息
     */
    @ApiModelProperty("入驻商信息")
    private SupplierRespVO supplier;

    /** 商品类型, local本地, global 全国 */
    @ApiModelProperty("商品类型, local本地, global 全国")
    private String type;

    @ApiModelProperty("促销活动列表")
    private List<SpuDetailActivityLabelVO> activityList;

    @ApiModelProperty("组合促销商品列表")
    private List<SpuCombineSkuVO> combineSkuList;

    @ApiModelProperty("第三级展示类目ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long classId;

    @ApiModelProperty("0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    /**
     * 最新起订
     */
    @ApiModelProperty(value = "最新起订", notes = "当前这个类对象是NULL值不返回")
    private Integer minOq;

    /**
     * 起订组数
     */
    @ApiModelProperty(value = "起订组数", notes = "当前这个类对象是NULL值不返回")
    private Integer jumpOq;

    /**
     * 最大限购
     */
    @ApiModelProperty(value = "最大限购", notes = "当前这个类对象是NULL值不返回")
    private Integer maxOq;

    /**
     * 单位-字典
     */
    @ApiModelProperty("单位-字典")
    private String unit;

    /**
     * 组合商品ID
     */
    @ApiModelProperty("组合商品ID")
    private Long spuCombineId;

    @ApiModelProperty("小单位-单位字段值")
    private Long minUnit;

    @ApiModelProperty("中单位-单位字段值")
    private Long midUnit;

    @ApiModelProperty("大单位-单位字段值")
    private Long largeUnit;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;
    
    /** 其他属性 */
    @ApiModelProperty("其他属性 - json形式")
    private String otherAttr;

    public SpuDetailRespVO setBrandName(BrandDTO brandDTO) {
        if (Objects.nonNull(brandDTO)) {
            this.brandName = brandDTO.getBrandName();
        }
        return this;
    }
    public SpuDetailRespVO setBrandName(String brandName) {
        this.brandName = brandName;
        return this;
    }
}
