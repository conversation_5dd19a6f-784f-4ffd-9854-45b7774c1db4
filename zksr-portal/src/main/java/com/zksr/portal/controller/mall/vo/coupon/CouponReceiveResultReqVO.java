package com.zksr.portal.controller.mall.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券领取结果查询
 * @date 2024/4/2 15:42
 */
@Data
@ApiModel(description = "优惠券领取结果查询")
public class CouponReceiveResultReqVO {

    @NotNull
    @Size(min = 1, max = 20, message = "优惠券模版ID长度必须在1-20之间")
    @ApiModelProperty(value = "状态ID集合", required = true)
    private List<Long> statusIds;
}
