package com.zksr.portal.controller.mall.vo.yh;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 8:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YhBatchStockCheckRespVO {

    @ApiModelProperty("问题商品集合")
    private List<StockCheckItem> checkItems = new ArrayList<>();

    @Data
    public static class StockCheckItem {

        @ApiModelProperty("商品数量")
        private Integer productNum;

        @ApiModelProperty("可购买数")
        private Long availableNum;

        @ApiModelProperty("SPU名称")
        private String spuName;

        @ApiModelProperty("SKU名称")
        private String skuName;

        @ApiModelProperty("要货ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long yhId;

        /** 上下架状态 */
        @ApiModelProperty("上架状态, true-上架, false-未上架")
        private boolean release;
    }
}
