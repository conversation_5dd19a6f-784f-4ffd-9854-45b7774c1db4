package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("colonelAppMallLogin 接口响应")
public class ColonelAppMallLoginResponse {

    @ApiModelProperty(value = "token")
    private String token;//生成的token码

    @ApiModelProperty(value = "平台商source")
    private String source;
}
