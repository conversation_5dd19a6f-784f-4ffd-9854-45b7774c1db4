package com.zksr.portal.controller.mall.vo.car;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务员推荐数据
 * @date 2024/7/26 15:14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "获取业务员推荐统计")
public class CarRecommendRespVO {

    @ApiModelProperty("业务员名称")
    private String colonelName;

    @ApiModelProperty("业务员头像")
    private String avatarImages;

    @ApiModelProperty("业务员ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long colonelId;

    @ApiModelProperty("商品种类")
    private Long productTypeNum;

    @ApiModelProperty("是否弹出业务员推荐窗口")
    private Boolean showWindow;

    @ApiModelProperty(value = "业务员推荐备注")
    private String commandMemo;
}
