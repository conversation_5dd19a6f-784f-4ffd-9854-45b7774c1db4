package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class RegisterResp {

    //手机号  手机号平台商级唯一 必填
    /** 手机号 */
    @ApiModelProperty(value = "手机号")
    private String mobile;


    //用户名  memberName 必填
    /** 用户名 */
    @ApiModelProperty(value = "用户名")
    private String memberName;

    //门店名称 branch_name 必填
    /** 门店名称 */
    @ApiModelProperty(value = "门店名称")
    private String branchName;

    //是否为自动审核  0 否  1是
    /** 是否为自动审核 */
    @ApiModelProperty(value = "是否为自动审核  0 否  1是")
    private Integer approveFlag;
}
