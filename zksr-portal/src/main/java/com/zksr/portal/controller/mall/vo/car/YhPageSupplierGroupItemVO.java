package com.zksr.portal.controller.mall.vo.car;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/12 11:07
 */

@Data
@Builder
@ApiModel(description = "分组商品详情数据")
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class YhPageSupplierGroupItemVO {

    @ApiModelProperty(value = "要货商品ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long yhId;

    @ApiModelProperty(value = "城市商品上架ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long itemId;

    @ApiModelProperty(value = "skuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty(value = "spuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    @ApiModelProperty(value = "入驻商ID")
    @JsonIgnore
    private Long supplierId;

    @ApiModelProperty(value = "品牌ID")
    @JsonIgnore
    private Long brandId;

    @ApiModelProperty(value = "管理分类ID")
    @JsonIgnore
    private Long categoryId;

    @ApiModelProperty("市场价")
    private BigDecimal markPrice = BigDecimal.ZERO;

    @ApiModelProperty("剩余库存")
    private Long stockQty = NumberPool.LONG_ZERO;

    @ApiModelProperty("根据单位换算最大可加入购物车库存")
    private Long maxQty = NumberPool.LONG_ZERO;

    @ApiModelProperty("商品数量")
    private Integer productNum;

    @ApiModelProperty("是否选中")
    private Boolean selected;

    @ApiModelProperty("spu名称")
    private String spuName;

    @ApiModelProperty("spu图片")
    private String spuThumb;

    @ApiModelProperty("sku图片")
    private String skuThumb;

    @ApiModelProperty("规格名称")
    private String specName;

    @ApiModelProperty("sku规格信息")
    private String skuProperties;

    @ApiModelProperty("sku规格单位")
    private String skuUnit;

    /** 起订 */
    @ApiModelProperty("起订数")
    private Long minOq;

    /** 订货组数 */
    @ApiModelProperty("订货组数")
    private Long jumpOq;

    /** 限购 */
    @ApiModelProperty("限购数量")
    private Long maxOq;

    /** 上架状态-数据字典 */
    @ApiModelProperty("上架状态,1-已上架,其他状态都不可购买")
    private Integer shelfStatus;

    @ApiModelProperty(value = "预计应付金额")
    private BigDecimal payAmt;

    @ApiModelProperty(value = "秒杀/特价 活动价格")
    private BigDecimal activityPrice;

    @ApiModelProperty(value = "秒杀/特价 活动数量")
    private Integer activityNum;

    @ApiModelProperty(value = "参与活动应付金额", notes = "会根据促销计算实时调整")
    private BigDecimal activityPayAmt;

    @ApiModelProperty(value = "商品促销标签列表")
    private List<CarSpuActivityLabelVO> spuActivityLabelList = new ArrayList<>();

    @ApiModelProperty(value = "促销活动")
    private List<ActivityLabelInfoVO> activityList = new ArrayList<>();

    @ApiModelProperty(value = "unit, 单位")
    private String unit = StringPool.ZERO;

    /**
     * 参见枚举 {@link com.zksr.common.core.enums.UnitType}
     */
    @ApiModelProperty(value = "单位类型, 1-最小单位, 2-中单位, 3-大单位")
    private Integer unitSize = 1;

    @ApiModelProperty(value = "和最小单位库存转换比例")
    private Long stockConvertRate;

    @ApiModelProperty("一个中单位等于多少个小单位")
    @JsonIgnore
    private BigDecimal midSize;

    @ApiModelProperty("一个大单位等于多少个小单位")
    @JsonIgnore
    private BigDecimal largeSize;

    @ApiModelProperty("要货信息")
    private EsYhProduct yhInfo;

    @ApiModelProperty(value = "0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;

    @ApiModelProperty("活动检验商品对象")
    private ActivityVerifyItemDTO activityVerifyItemDto;
}
