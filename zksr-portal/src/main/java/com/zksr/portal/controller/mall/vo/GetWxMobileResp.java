package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("微信平台 - 解密获取手机号接口响应")
public class GetWxMobileResp {

    @ApiModelProperty(value = "code 必填")
    private String phoneNumber;

    @ApiModelProperty(value = "code 必填")
    private String purePhoneNumber;
    private String countryCode;
    private Watermark watermark;

    @ApiModelProperty(value = "手机号是否已经存在：1是0否", example = "1")
    private String isExist;



}

class Watermark{
    private String appid;
    private Long timestamp;

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
}
