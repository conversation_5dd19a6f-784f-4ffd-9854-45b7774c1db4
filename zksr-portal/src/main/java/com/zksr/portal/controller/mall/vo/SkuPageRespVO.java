package com.zksr.portal.controller.mall.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.CustomStockIntSerialize;
import com.zksr.portal.controller.mall.vo.spu.SpuActivityLabelVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@ApiModel("商城-SPU列表 VO对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SkuPageRespVO {

    /** 上架商品ID */
    @ApiModelProperty("上架商品ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long itemId;

    /** 商品类型, local本地, global 全国 */
    @ApiModelProperty("商品类型, local本地, global 全国")
    private String type;

    /** 商品名称 */
    @ApiModelProperty("商品名称")
    private String spuName;

    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

    /** 单位 */
    @ApiModelProperty("单位名称")
    private String unitName;

    /** 单位 */
    @ApiModelProperty("单位尺寸")
    private Integer unitSize;

    /** 规格属性, 可能涉及搜索 */
    @ApiModelProperty("规格属性")
    private String properties;

    /** 封面图片 */
    @ApiModelProperty("封面图片")
    private String thumb;

    /** 封面视频 */
    @ApiModelProperty("封面视频")
    private String thumbVideo;

    /** SPU ID */
    @ApiModelProperty("SPU ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 默认-1, 兼容全国和本地商品售后 */
    @ApiModelProperty(" 默认-1, 兼容全国和本地商品售后")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 默认-1, 本地渠道 */
    @ApiModelProperty("默认-1, 本地渠道")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long channelId;

    /** 默认-1, 兼容全国和本地商品 平台商城市分组id */
    @ApiModelProperty("默认-1, 兼容全国和本地商品 平台商城市分组id ")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long groupId;

    /** SKU ID */
    @ApiModelProperty("SKU ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 平台展示分类ID */
    @ApiModelProperty(" 平台展示分类ID ")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long classId;

    /** 平台展示分类名称 */
    @ApiModelProperty("平台展示分类名称 ")
    private String className;

    /** 入驻商ID */
    @ApiModelProperty("入驻商ID ")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    @ApiModelProperty("入驻商名称 ")
    private String supplierName;

    /** 品牌ID */
    @ApiModelProperty("品牌ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 管理分类ID*/
    @ApiModelProperty("管理分类ID")
    @JsonIgnore
    private Long catgoryId;

    /** 品牌名称 */
    @ApiModelProperty("品牌名称")
    private String brandName;

    /** 建议零售价, 原价 */
    @ApiModelProperty("建议零售价, 原价")
    private BigDecimal suggestPrice;

    /** 销量 */
    @ApiModelProperty("销量")
    private Long saleQty;

    /** 库存 */
    @ApiModelProperty("库存")
    @JsonSerialize(using= CustomStockIntSerialize.class)
    private BigDecimal stock;

    @ApiModelProperty(value = "商品价格", required = true, example = "99.99")
    private BigDecimal price;

    @ApiModelProperty(value = "市场价", required = true, example = "99.99")
    private BigDecimal markPrice;

    @ApiModelProperty(value = "促销 价格", required = true, example = "99.99") // 通过会员等级，计算出折扣后价格
    private BigDecimal promotionPrice;

    @ApiModelProperty(value = "促销单位", notes = "秒杀. 特价, 最便宜的具体单位")
    @JsonIgnore
    private UnitTypeEnum promotionUnit;

    @ApiModelProperty(value = "促销SKUID", notes = "秒杀. 特价, 最便宜的SKU")
    @JsonIgnore
    private Long promotionSkuId;

    /** 最旧生产日期, 兼容管理分类指定生产日期格式 */
    @ApiModelProperty(value = "最旧生产日期", example = "2024-03-01 00:00:00")
    private String oldestDate; 		 // 最旧生产日期

    /** 最新生产日期, 兼容管理分类指定生产日期格式 */
    @ApiModelProperty(value = "最新生产日期", example = "2024-03-31 00:00:00")
    private String latestDate; 		 // 最新生产日期

    /** 商品规格 */
    @ApiModelProperty("商品规格")
    private String specName;

    // ========== 营销相关字段 =========

//    @ApiModelProperty(value = "活动排序数组", required = true, example = "1024")
//    private List<Integer> activityOrders;

    // ========== 统计相关字段 =========

//    @ApiModelProperty(value = "商品销量", required = true, example = "1024")
//    private Integer salesCount;

    /** 是否开启多规格 1-是 0-否 */
    @ApiModelProperty(value = "是否开启多规格 1-是 0-否",  example = "1-是 0-否")
    private Long isSpecs;

    /** 排序序号 */
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer sortNum;

    @ApiModelProperty(value = "商品促销标签列表")
    private List<SpuActivityLabelVO> spuActivityLabelList;

    @ApiModelProperty(value = "优惠券活动标签")
    private List<CouponLabelVO> couponLabelList;

    /** 起订 */
    @ApiModelProperty(value = "起订", example = "1")
    private Long minOq;

    /** 订货组数 */
    @ApiModelProperty(value = "订货组数", example = "1")
    private Long jumpOq;

    /** 限购 */
    @ApiModelProperty("限购数量")
    private Long maxOq;

    @ApiModelProperty("0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    @ApiModelProperty("组合促销商品ID")
    private Long spuCombineId;

    @ApiModelProperty("上架状态,0-已下架,1-上架中")
    private Integer shelfStatus;

    /** 素材URL */
    @ApiModelProperty("素材URL")
    private String materialUrl;

    /** 入驻商配送标签, 来自平台商字典 */
    @ApiModelProperty(value = "入驻商配送标签, 来自平台商字典")
    private String productDistributionLabel;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;

    @JsonIgnore
    public String getUniqueKey() {
        return StringUtils.format("{}_{}", skuId, Objects.isNull(unitSize) ? UnitTypeEnum.UNIT_SMALL.getType() : unitSize);
    }

    public boolean notSpuCombine() {
        return Objects.isNull(spuCombineId);
    }

    public boolean spuCombine() {
        return Objects.nonNull(spuCombineId);
    }

    @ApiModel(description = "促销互动标签")
    @Data
    public static class CouponLabelVO {
        @ApiModelProperty("优惠券名称")
        private String couponName;

        @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券")
        private Integer discountType;

        @ApiModelProperty(value = "优惠券面额;满多少元可以减多少元(满减券设置)")
        private BigDecimal discountAmt;

        @ApiModelProperty(value = "优惠折扣;折，如99折记9.9折(折扣券设置)")
        private BigDecimal discountPercent;
    }
}
