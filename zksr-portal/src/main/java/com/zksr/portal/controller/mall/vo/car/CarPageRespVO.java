package com.zksr.portal.controller.mall.vo.car;

import cn.hutool.core.collection.ListUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyResultDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车分页返回数据对象
 * @date 2024/3/26 19:35
 */
@Data
@ApiModel(description = "购物车分页方法")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CarPageRespVO {
    @ApiModelProperty("总入驻商列表数")
    private Long total;

    @ApiModelProperty("按入驻商分组数据")
    private List<CarPageSupplierGroupVO> supplierGroupList;

    @ApiModelProperty("支付平台")
    private String payPlatform;

    public CarPageRespVO() {
    }

    public CarPageRespVO(Integer total, List<CarPageSupplierGroupVO> supplierGroupList) {
        this.total = total.longValue();
        this.supplierGroupList = supplierGroupList;
    }

    public CarPageRespVO(Integer total, List<CarPageSupplierGroupVO> supplierGroupList, PayConfigDTO payConfigDTO) {
        this.total = total.longValue();
        this.supplierGroupList = supplierGroupList;
        if (Objects.nonNull(payConfigDTO)) {
            this.payPlatform = payConfigDTO.getStoreOrderPayPlatform();
        }
    }

    public static CarPageRespVO build(Integer total, List<CarPageSupplierGroupVO> supplierGroupList, PayConfigDTO payConfigDTO) {
        return new CarPageRespVO(total, supplierGroupList, payConfigDTO);
    }

    public static CarPageRespVO empty() {
        return new CarPageRespVO(NumberPool.INT_ZERO, ListUtil.empty());
    }

    @Data
    @ApiModel(description = "入驻商分组数据")
    public static class CarPageSupplierGroupVO {

        @ApiModelProperty("共享促销活动")
        private List<ActivityLabelInfoVO> activityList = new ArrayList<>();

        @ApiModelProperty("入驻商名称")
        private String supplierName;

        @ApiModelProperty("入驻商ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long supplierId;

        @ApiModelProperty("入驻商 - 暂停营业开始时间")
        private String shuttingStartTime;

        @ApiModelProperty("入驻商 - 暂停营业结束时间")
        private String shuttingEndTime;

        @ApiModelProperty("起送金额 (本地)")
        private BigDecimal minAmt;

        @ApiModelProperty("起送金额 (全国)")
        private BigDecimal globalMinAmt;

        @ApiModelProperty("是否验证起送: ture-验证, false-不验证")
        private Boolean minAmtQualified;

        @ApiModelProperty("是否欠款,true-已欠款,false-未欠款")
        private Boolean debt;

        @ApiModelProperty("应付金额")
        private BigDecimal totalAmt;

        @ApiModelProperty(value = "入驻商配送标签, 来自平台商字典")
        private String productDistributionLabel;

        @ApiModelProperty("是否支持负库存下单 0：否，1：是")
        private Integer isNegativeStock;

        @ApiModelProperty("入驻商商品列表")
        private List<CarPageSupplierGroupItemVO> itemList = new ArrayList<>();

        @JsonIgnore
        @ApiModelProperty("活动检验商品列表,处理逻辑使用")
        private List<ActivityVerifyItemDTO> activityVerifyItemDtoList = new ArrayList<>();

        @JsonIgnore
        @ApiModelProperty("当前入驻商所有活动列表，处理逻辑使用")
        private List<SupplierActivityDTO> serviceSupplierActivityList = new ArrayList<>();

        @JsonIgnore
        @ApiModelProperty("活动列表对象")
        private ActivityVerifyResultDTO verifyResultDTO;
    }

}
