package com.zksr.portal.controller.mall;


import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.domain.AjaxResult;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.vo.MemShopAppRecodeReqVO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.portal.controller.mall.vo.PartnerPolicyRespDTO;
import com.zksr.portal.controller.mall.vo.config.AppletAgreementPolicyRespVO;
import com.zksr.portal.convert.cfg.ConfigConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.vo.SysAreaCityPageReqVO;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.PartnerMiniSettingPolicyDTO;
import com.zksr.system.api.wx.WxQrApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@RequestMapping("/mall/index")
@Api(tags = "商城 - 首页数据接口")
@Slf4j
public class IndexController {

    @Resource
    private AreaCityApi areaCityApi;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private PartnerPolicyApi partnerPolicyApi;
    @Resource
    private ColonelApi colonelApi;
    @Resource
    private WxQrApi wxQrApi;

    @Autowired
    private ChannelApi channelApi;

    /**
    * @Description: 获取小程序首页配置信息
     * !@小程序 - 1、获取小程序首页配置信息
    * @Author: liuxingyu
    * @Date: 2024/4/24 9:45
    */
    @ApiOperation("获取小程序首页配置信息")
    @GetMapping("/getAppletAgreement")
    public CommonResult<AppletAgreementPolicyRespVO> getAppletAgreement(@RequestParam("sysSource") String sysSource){
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        if (ObjectUtil.isNull(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        return CommonResult.success(
                ConfigConvert.INSTANCE.convertAppletAgreementPolicyDTO(
                        portalCacheService.getAppletAgreementPolicy(partnerDto.getSysCode()),
                        portalCacheService.getAppletBaseConfigDTO(partnerDto.getSysCode()),
                        portalCacheService.getPayConfigDTO(partnerDto.getSysCode())
                )
        );
    }

    /**
     * @Description: 获取平台商小程序配置信息
     * !@小程序 - 2、获取平台商小程序配置信息
     * @Author: chenmingqing
     * @Date: 2024/6/13 9:45
     */
    @ApiOperation("获取平台商小程序配置信息")
    @GetMapping("/getPartnerMiniSettingPolicy")
    public CommonResult<PartnerPolicyRespDTO> getPartnerMiniSettingPolicy(@RequestParam("sysSource") String sysSource){
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        if (ObjectUtil.isNull(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        return CommonResult.success(
                ConfigConvert.INSTANCE.convertPartnerPolicyDTO_0(
                        portalCacheService.getPartnerMiniSettingPolicy(partnerDto.getSysCode()),partnerDto
                )
        );
    }

    /**
     * 获取省市区
     * @param pageReqVO 省市区请求
     * @return  省市区列表
     */
    @ApiOperation("获取系统省市区")
    @PostMapping("/getAreaCityPage")
    CommonResult<PageResult<SysAreaCityRespVO>> getAreaCityPage(@RequestBody SysAreaCityPageReqVO pageReqVO) {
        return areaCityApi.getPage(pageReqVO);
    }

    @ApiOperation("获取商城二维码 (返回base64字符图片)")
    @PostMapping("/getShopAppRecode")
    public CommonResult<String> getShopAppRecode(@RequestBody MemShopAppRecodeReqVO reqVO){
        return colonelApi.getShopAppRecode(reqVO);
    }

    @GetMapping("/wxQrAdd")
    @ApiOperation("微信二维码参数新增")
    public CommonResult wxQrAdd(@RequestParam("qrValue") String qrValue)
    {
        return wxQrApi.add(qrValue);
    }

    @GetMapping("/wxQrGetByQrKey")
    @ApiOperation("微信二维码参数查询")
    public CommonResult wxQrGetByQrKey(@RequestParam("qrKey") String qrKey)
    {
        return wxQrApi.getByQrKey(qrKey);
    }

    @ApiOperation("获取平台渠道列表")
    @GetMapping("/getChannelInfo")
    public CommonResult<List<ChannelDTO>> getChannelInfo(@RequestParam("sysSource") String sysSource,
                                                         @RequestParam(value = "channelName", required = false) String channelName)
    {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        return CommonResult.success(channelApi.getChannelListBySysCode(partnerDto.getSysCode(), channelName).getCheckedData());
    }
}
