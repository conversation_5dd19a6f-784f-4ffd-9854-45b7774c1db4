package com.zksr.portal.controller.mall.vo.yh;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 8:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YhBatchCountRespVO {

    @ApiModelProperty("入驻商促销集合")
    private List<YhSupplierActivityVO> supplierActivityList;

    @ApiModelProperty("结算金额")
    private BigDecimal totalAmt;

    @ApiModelProperty("合计种类商品数量")
    private Integer productTypeNum;

    @ApiModelProperty("总商品数量")
    private Integer productNumTotal;
}
