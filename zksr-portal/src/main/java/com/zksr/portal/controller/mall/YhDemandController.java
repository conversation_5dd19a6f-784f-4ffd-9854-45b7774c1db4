package com.zksr.portal.controller.mall;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.portal.controller.mall.vo.yh.*;
import com.zksr.portal.service.mall.IMallYhService;
import com.zksr.product.api.yhdata.vo.YhBatchListReqVO;
import com.zksr.product.api.yhdata.vo.YhBatchSaveReqVO;
import com.zksr.product.api.yhdata.vo.YhBatchSupplierSaleClassReqVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 要货需求
 * @date 2024/12/11 8:44
 */
@RestController
@RequestMapping("/mall/yh")
@Api(tags = "商城 - 要货接口")
@Slf4j
public class YhDemandController {

    @Autowired
    private IMallYhService mallYhService;

    @ApiOperation(value = "获取要货批次入驻商和分类集合", httpMethod = HttpMethod.POST)
    @PostMapping("/getSupplierSaleClass")
    @RequiresMallLogin(isMember=true)
    public CommonResult<YhBatchSupplierSaleClassRespVO> yhSupplierSaleClass(@Valid @RequestBody YhBatchSupplierSaleClassReqVO reqVO) {
        reqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(mallYhService.yhSupplierSaleClass(reqVO));
    }

    @ApiOperation(value = "获取要货批次入商品数据集合", httpMethod = HttpMethod.POST)
    @PostMapping("/getBatchList")
    @RequiresMallLogin(isMember=true)
    public CommonResult<YhBatchListRespVO> getBatchList(@Valid @RequestBody YhBatchListReqVO reqVO) {
        reqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(mallYhService.getBatchList(reqVO));
    }

    @ApiOperation(value = "要货批次结算统计", httpMethod = HttpMethod.POST)
    @PostMapping("/batchCount")
    @RequiresMallLogin
    public CommonResult<YhBatchCountRespVO> batchCount(@Valid @RequestBody YhBatchCountReqVO countReqVO) {
        countReqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(mallYhService.batchCount(countReqVO));
    }

    @ApiOperation(value = "最终库存验证", httpMethod = HttpMethod.POST)
    @PostMapping("/stockCheck")
    @RequiresMallLogin
    public CommonResult<YhBatchStockCheckRespVO> batchStockCheck(@Valid @RequestBody YhBatchCountReqVO countReqVO) {
        countReqVO.setBranchId(MallSecurityUtils.getBranchId());
        return success(mallYhService.batchStockCheck(countReqVO));
    }

    @ApiOperation(value = "要货单数据操作", httpMethod = HttpMethod.POST)
    @PostMapping("/batchYhSave")
    @RequiresMallLogin
    public CommonResult<Boolean> batchYhSave(@Valid @RequestBody YhBatchSaveReqVO reqVO) {
        return mallYhService.batchYhSave(reqVO);
    }
}
