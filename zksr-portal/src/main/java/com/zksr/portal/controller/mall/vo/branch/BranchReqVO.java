package com.zksr.portal.controller.mall.vo.branch;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 门店请求实体类
 * @Author: liuxingyu
 * @Date: 2024/6/7 15:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchReqVO {
    /**
     * 门店id
     */
    @ApiModelProperty("门店id")
    private Long branchId;

    /**
     * 平台商id
     */
    @ApiModelProperty("平台商id")
    private Long sysCode;

    /**
     * 门店名称
     */
    @ApiModelProperty("门店名称")
    @NotBlank(message = "门店名称不能为空")
    private String branchName;


    /**
     * 城市id
     */
    @ApiModelProperty("城市id")
    @NotNull(message = "城市不能为空")
    private Long areaId;


    /**
     * 门店地址
     */
    @ApiModelProperty("门店地址")
    private String branchAddr;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    @NotNull(message = "定位不能为空")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    @NotNull(message = "定位不能为空")
    private BigDecimal latitude;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String memo;

    /**
     * 门头照
     */
    @ApiModelProperty("门头照")
    private String branchImages;

    @ApiModelProperty("门店ID")
    private Long memberId;

    @ApiModelProperty("门店自动审核标识")
    private Integer branchApproveFlag;

    /** 门店过期时间;过了过期时间，则停用 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date branchExpirationDate;

    @ApiModelProperty("电子围栏入驻商ID信息，以,逗号间隔")
    private String supplierIds;

    @ApiModelProperty("三级区域城市ID, 省市区关联")
    private Long threeAreaCityId;


    /** 省份 */
    @Excel(name = "省份")
    @ApiModelProperty(value = "省份")
    private String provinceName;

    /** 城市 */
    @Excel(name = "城市")
    @ApiModelProperty(value = "城市")
    private String cityName;

    /** 区县 */
    @Excel(name = "区县")
    @ApiModelProperty(value = "区县")
    private String districtName;

    @ApiModelProperty(value = "渠道id")
    private Long channelId;
}
