package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("visitorLogin 接口请求")
public class VisitorLoginRequest {

    @ApiModelProperty(value = "sysSource  必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;

}
