package com.zksr.portal.controller.mall.vo.spu;

import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
*
 *
* <AUTHOR>
* @date 2024/4/15 15:18
*/
@ApiModel(value = "商品SKU上架商品请求VO")
@Data
public class SpuItemListReqVO {
    /**
     * 城市上架ID集合
     */
    @ApiModelProperty(value = "城市上架ID集合", hidden = true)
    private List<Long> areaItemIds;

    /**
     * 上架ID集合
     */
    @ApiModelProperty("上架ID集合")
    private List<Long> itemIds;

    @ApiModelProperty(value = "关键字", example = "好看")
    private String condition;

    /** 排序类型 */
    @ApiModelProperty("排序类型: none-无, sale-销量, price-价格")
    private String sortType = StringPool.NONE;

    /** 排序方式 @com.zksr.common.core.enums.ProductSortType */
    @ApiModelProperty("排序方式: none-无,des-降序, asc-升序")
    private String orderBy = StringPool.NONE;

    /** 默认-1, 兼容全国和本地商品售后 */
    @ApiModelProperty(value = "区域城市默认-1, 兼容全国和本地商品售后", hidden = true)
    private Long areaId;

    /** 默认-1, 本地渠道 */
    @ApiModelProperty(value = "区域城市默认-1, 兼容全国和本地商品售后", hidden = true)
    private Long channelId;

    /** 默认-1, 兼容全国和本地商品 平台商城市分组id */
    @ApiModelProperty(value = "区域城市默认-1, 兼容全国和本地商品售后", hidden = true)
    private Long groupId;

    @ApiModelProperty(value = "指定上架单位获取数据")
    private List<ItemUnitSize> itemUnitList;

    @Data
    public static class ItemUnitSize {

        @ApiModelProperty(value = "商品类型", required = true)
        private String productType;

        @ApiModelProperty(value = "上架商品ID", required = true)
        private Long itemId;

        @ApiModelProperty(value = "商品类型, 0-普通商品, 1-组合商品", required = true)
        private Integer itemType = NumberPool.INT_ZERO;

        @ApiModelProperty(value = "上架单位", required = true)
        private Integer unitSize;
    }
}
