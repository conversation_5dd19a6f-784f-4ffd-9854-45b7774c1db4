package com.zksr.portal.controller.mall.vo.recharge;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/13 14:17
 */
@Data
public class RechargeConsumeReqVO extends PageParam {

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("流水执行开始时间")
    private Date processStartTime;

    @JsonFormat(pattern = YYYY_MM_DD_HH_MM_SS, timezone = TIMEZONE)
    @ApiModelProperty("流水执行结束时间")
    private Date processEndTime;

    @ApiModelProperty("收入/支出, 0-收入,1-支出")
    private Integer ioType;
}
