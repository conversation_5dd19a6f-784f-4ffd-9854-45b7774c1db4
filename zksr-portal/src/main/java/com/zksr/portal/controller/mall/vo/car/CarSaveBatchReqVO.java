package com.zksr.portal.controller.mall.vo.car;

import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.domain.dto.car.AppCarSaveReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新增购物车商品
 * @date 2024/3/26 16:51
 */
@NoArgsConstructor
@Data
@ApiModel(description = "保存购物车商品VO")
public class CarSaveBatchReqVO {

    @ApiModelProperty(value = "购物车ID",required = true)
    private List<BatchItemDTO> carIdList;

    @ApiModelProperty(value = "操作类型, 0-增量, 1-覆盖", required = true)
    private Integer opType;

    @ApiModelProperty(value = "是否添加选中,默认=true")
    private Boolean selected = Boolean.TRUE;

    @ApiModelProperty(value = "业务员推荐备注")
    private String commandMemo;

    @Data
    public static class BatchItemDTO extends AppCarSaveReqDTO {

        @ApiModelProperty(value = "操作数量",required = true)
        private Integer opQty;

        @ApiModelProperty(value = "指令ID")
        private Long commandId;
    }
}
