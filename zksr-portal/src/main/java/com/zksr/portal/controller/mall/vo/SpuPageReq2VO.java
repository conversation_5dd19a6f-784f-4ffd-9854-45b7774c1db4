package com.zksr.portal.controller.mall.vo;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by 尹凯 on 2024/5/17 18:46.
 */
@Data
@ApiModel("商城-SPU详情 VO对象")
public class SpuPageReq2VO extends PageParam {

    /** 管理分类 */
    @ApiModelProperty("catgoryId")
    private String catgoryId;

    @ApiModelProperty(value = "入驻商id", example = "1")
    private Long supplierId;

    @ApiModelProperty(value = "关键字")
    private String condition;

    /** 排序类型 */
    @ApiModelProperty("排序类型: none-无, sale-销量, price-价格")
    private String sortType = StringPool.NONE;

    /** 排序方式 @com.zksr.common.core.enums.ProductSortType */
    @ApiModelProperty("排序方式: none-无,des-降序, asc-升序")
    private String orderBy = StringPool.NONE;

    /** 商品类型 com.zksr.common.core.enums.ProductType */
    @ApiModelProperty("商品类型 local-本地, global-全国, group-全部")
    private String productType;

    @ApiModelProperty(value = "平台编码", hidden = true)
    private String sysSource;
}
