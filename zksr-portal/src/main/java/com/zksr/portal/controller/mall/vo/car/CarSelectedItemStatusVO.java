package com.zksr.portal.controller.mall.vo.car;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.domain.dto.car.AppCarStorageDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.CustomStockIntSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新增购物车商品
 * @date 2024/3/26 16:51
 */
@Data
@ApiModel(description = "选择商品数据")
public class CarSelectedItemStatusVO extends AppCarIdDTO {

    @ApiModelProperty("商品数量")
    private Integer productNum;

    @ApiModelProperty("可购买数")
    @JsonSerialize(using = CustomStockIntSerialize.class)
    private BigDecimal availableNum;

    @ApiModelProperty("SPU名称")
    private String spuName;

    @ApiModelProperty("SKU名称")
    private String skuName;

    /** 上下架状态 */
    @ApiModelProperty("上架状态, true-上架, false-未上架")
    private boolean release;

    @ApiModelProperty("商品类型,0-普通商品, 1-组合商品")
    private Integer itemType = NumberPool.INT_ZERO;

    @JsonIgnore
    @ApiModelProperty(value = "商品与最小单位转换比例", notes = "一个大单位等于X个小单位")
    private BigDecimal stockConvertRate = BigDecimal.ONE;

    @JsonIgnore
    @ApiModelProperty("最大限购")
    private Long maxQty = NumberPool.LONG_ONE;

    @ApiModelProperty(value = "组合促销商品ID")
    private Long spuCombineId;

    @ApiModelProperty("是否支持负库存下单 0：否，1：是")
    private Integer isNegativeStock;

    public static CarSelectedItemStatusVO build(AppCarIdDTO carId) {
        return BeanUtil.toBean(carId, CarSelectedItemStatusVO.class);
    }

    public static CarSelectedItemStatusVO build(AppCarStorageDTO carStorage) {
        return BeanUtil.toBean(carStorage, CarSelectedItemStatusVO.class);
    }

    @JsonIgnore
    public boolean isSpuCombine() {
        return itemType == NumberPool.INT_ONE;
    }

    public String uniqueSkuKey() {
        if (itemType == NumberPool.INT_ZERO)
            return StringUtils.format("{}:{}", this.getSkuId(), itemType);
        return StringUtils.format("{}:{}", spuCombineId, itemType);
    }
}
