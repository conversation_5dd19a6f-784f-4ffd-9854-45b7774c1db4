package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.portal.convert.member.MemberConvert;
import com.zksr.system.api.area.dto.AreaDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 登陆用户信息
 * @date 2024/7/12 16:56
 */
@Data
@ApiModel(description = "当前在线用户信息")
public class LoginUserInfoVO {

    @ApiModelProperty("门店信息")
    private BranchVO branch;

    @ApiModelProperty("用户信息")
    private MemberVO member;

    @ApiModelProperty("当前平台支付平台")
    private String payPlatform;

    public LoginUserInfoVO(BranchDTO branch, MemberDTO member, ColonelDTO colonel, AreaDTO area, Integer isCreateBranch) {
        this.branch = MemberConvert.INSTANCE.convertBranchVO(branch);
        this.member = MemberConvert.INSTANCE.convertMemberVO(member);
        if (Objects.nonNull(this.member)) {
            this.member.setIsCreateBranch(isCreateBranch);
        }
        if (Objects.nonNull(this.branch) && Objects.nonNull(colonel)) {
            this.branch.setColonelName(colonel.getColonelName());
            this.branch.setColonelPhone(colonel.getColonelPhone());
        }
        if (Objects.nonNull(this.branch) && Objects.nonNull(area)) {
            this.branch.setAreaName(area.getAreaName());
        }
    }

    public LoginUserInfoVO() {
    }

    @Data
    @ApiModel(description = "门店信息")
    public static class BranchVO {

        /** 门店id */
        @ApiModelProperty("门店ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long branchId;

        private Long dcId;

        /** 平台商id */
        @ApiModelProperty("平台商ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long sysCode;

        /** 门店名称 */
        private String branchName;

        /** 城市id */
        @ApiModelProperty("城市id")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long areaId;

        /** 城市名称 */
        @ApiModelProperty("城市名称")
        private String areaName;

        /** 业务员id */
        @ApiModelProperty("业务员id")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long colonelId;

        /** 门店地址 */
        @ApiModelProperty("门店地址")
        private String branchAddr;

        /** 经度 */
        @ApiModelProperty("经度")
        private BigDecimal longitude;

        /** 纬度 */
        @ApiModelProperty("纬度")
        private BigDecimal latitude;

        /** 渠道id */
        @ApiModelProperty("全国渠道ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long channelId;

        /** 平台商城市分组id */
        @ApiModelProperty("全国平台商城市分组ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long groupId;

        /** 联系人 */
        @ApiModelProperty("联系人")
        private String contactName;

        /** 联系电话 */
        @ApiModelProperty("联系电话")
        private String contactPhone;

        /** 审核状态 */
        @ApiModelProperty(value = "1已审核 0未审核")
        private Integer auditState = 0;

        /** 过期时间 */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @ApiModelProperty("账户过期时间")
        private Date expirationDate; 		 // 过期时间

        /** 价格码-数据字典（1，2，3，4，5，6）） */
        @ApiModelProperty(value = "价格码-数据字典sys_price_code")
        private Long salePriceCode;

        /** 是否支持货到付款(0,否 1,是) */
        @ApiModelProperty("是否支持货到付款(0,否 1,是)")
        private Integer hdfkSupport;

        @Excel(name = "货到付款最大可欠款金额")
        @ApiModelProperty("货到付款最大可欠款金额")
        private BigDecimal hdfkMaxAmt;

        /** 业务员名称 */
        @ApiModelProperty(value = "业务员名称")
        private String colonelName;

        /** 业务员手机号 */
        @ApiModelProperty(value = "业务员手机号")
        private String colonelPhone;

        @ApiModelProperty("是否首单, 新客标识, 首单标识 1-是 0-否")
        private Integer firstOrderFlag;
    }

    @Data
    @ApiModel(description = "用户信息")
    public static class MemberVO {

        /**
         * 是否存在门店 0: 已存在门店  1: 未存在门店，未提交门店信息 2: 未存在门店，已提交门店信息
         */
        @ApiModelProperty(value = "是否存在门店 0: 已存在门店  1: 未存在门店，未提交门店信息 2: 未存在门店，已提交门店信息", required = true, example = "1")
        private Integer isCreateBranch;

        @ApiModelProperty("用户ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long memberId;

        @ApiModelProperty("平台商ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long sysCode;

        /** 用户手机号 */
        @ApiModelProperty("用户手机号")
        private String memberPhone;

        /** 用户名 */
        @ApiModelProperty("用户名称")
        private String memberName;

        /** 小程序openid */
        @ApiModelProperty("小程序openid")
        private String xcxOpenid;

        /** 用户账号 */
        @ApiModelProperty("用户登陆账号")
        private String userName;

        /** 是否为店长用户 */
        @ApiModelProperty("是否为店长用户")
        private Integer isShopManager;

        @ApiModelProperty("公众号openid")
        private String publishOpenid;

        /** 头像 */
        @ApiModelProperty("头像")
        private String avatar;

        @ApiModelProperty("是否是业务员 1：是 0：否")
        private Integer isColonel;

        @ApiModelProperty("关联业务员ID")
        private Long relateColonelId;

        /**
         * 运营商展示分类一级分类展示在顶部或左侧, 1顶部2左侧
         */
        @ApiModelProperty("运营商展示分类一级分类展示在顶部或左侧, 1顶部2左侧")
        private String classShow;
    }
}
