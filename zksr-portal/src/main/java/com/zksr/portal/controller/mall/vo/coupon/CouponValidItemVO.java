package com.zksr.portal.controller.mall.vo.coupon;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.coupon.dto.CouponNormalCacheDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 有效优惠券返回
 * @date 2024/4/1 15:25
 */
@Data
@Accessors(chain = true)
@ToString
@ApiModel(description = "有效可领取优惠券")
public class CouponValidItemVO {

    @ApiModelProperty("优惠券模版ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long couponTemplateId;

    @ApiModelProperty("平台ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long sysCode;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty("优惠券可领取开始时间")
    private Date templateStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    @ApiModelProperty("优惠券可领取结束时间")
    private Date templateEndDate;

    @ApiModelProperty(value = "有效期类型(数据字典);0-固定日期1-领取之后")
    private Integer expirationType;

    @ApiModelProperty("优惠券有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDateStart;

    @ApiModelProperty("优惠券有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationDateEnd;

    @ApiModelProperty("领取后 disableDays 至 expireDays 日之间可用, expirationType=1时有效")
    private Integer disableDays;

    @ApiModelProperty("领取后 disableDays 至 expireDays 日之间可用, expirationType=1时有效")
    private Integer expireDays;

    @ApiModelProperty("优惠券总数量")
    private Integer couponQty;

    @ApiModelProperty("优惠券名称")
    private String couponName;

    @ApiModelProperty(value = "数据字典coupon_spu_scope;0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券）")
    private Integer spuScope;

    @ApiModelProperty(value = "数据字典coupon_receive_scope领取范围; 0-全部可领取,1-指定渠道,2-指定城市,3-指定门店")
    private Integer receiveScope;

    @ApiModelProperty(value = "优惠类型(数据字典);0-满减券  1-折扣券")
    private Integer discountType;

    @ApiModelProperty(value = "优惠券启用金额(使用门槛);满多少元(商品适用范围内商品订单金额)可以使用")
    private BigDecimal triggerAmt;

    @ApiModelProperty(value = "优惠券面额;满多少元可以减多少元(满减券设置)")
    private BigDecimal discountAmt;

    @ApiModelProperty(value = "优惠折扣;折，如99折记9.9折(折扣券设置)")
    private BigDecimal discountPercent;

    @ApiModelProperty("每人限领数量")
    private Integer limit;

    @ApiModelProperty("已领取数量")
    private Integer receiveNum;

    @ApiModelProperty("入驻商名称")
    private Long supplierId;

    @ApiModelProperty("入驻商名称")
    private String supplierName;

    public static CouponValidItemVO convert(CouponNormalCacheDTO couponNormalCacheDTO) {
        return BeanUtil.toBean(couponNormalCacheDTO, CouponValidItemVO.class);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CouponValidItemVO that = (CouponValidItemVO) o;
        return Objects.equals(couponTemplateId, that.couponTemplateId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(couponTemplateId);
    }
}
