package com.zksr.portal.controller.mall.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.trade.api.order.dto.TrdSupplierOrderDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("订单实体")
public class OrderVO {

    @ApiModelProperty("订单id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long orderId;

    @ApiModelProperty("平台商id")
    private Long sysCode;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("运营商id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long dcId;

    @ApiModelProperty("城市id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long areaId;

    @ApiModelProperty("用户id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberId;

    @ApiModelProperty("门店id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty("经度")
    private BigDecimal longitude;

    @ApiModelProperty("纬度")
    private BigDecimal latitude;

    @ApiModelProperty("门店地址")
    private String branchAddr;

    @ApiModelProperty("支付状态 数据字典sys_order_status） 0-未支付 1-已支付 2-未付款取消")
    private Integer payState;

    @ApiModelProperty("支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmt;

    @ApiModelProperty("备注")
    private String memo;

    @ApiModelProperty("支付金额")
    private BigDecimal payAmt;

    @ApiModelProperty("单据金额")
    private BigDecimal orderAmt;

    @ApiModelProperty("创建者")
    private String createBy;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("门店名称")
    private String branchName;

    @ApiModelProperty("门店联系人")
    private String contactName;

    @ApiModelProperty("门店联系电话")
    private String contactPhone;

    @ApiModelProperty("支付方式")
    private String payWay;

    /** 到期支付时间 */
    @ApiModelProperty("到期支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirePayTime;

    @ApiModelProperty(value = "是否是业务员下单 1：是 0 否")
    private Integer colonelFlag;

    @ApiModelProperty(value = "业务员ID")
    private Long colonelId;

    @ApiModelProperty(value = "业务员名称")
    private String colonelName;

    @ApiModelProperty(value = "业务员头像")
    private String colonelAvatarImages;

    @ApiModelProperty(value = "订单类型：0 全国订单 1：本地订单")
    private Integer orderType;

    @ApiModelProperty(value = "司机ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long driverId;

    @ApiModelProperty(value = "司机评价状态 (0-未评价, 1-已评价)")
    private Integer driverRatingFlag;

    @ApiModelProperty(value = "司机评价ID")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long driverRatingId;

    private List<TrdSupplierOrderDTO> supplierOrderList;
}
