package com.zksr.portal.controller.mall.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/8 16:12
 */
@Data
@ApiModel("获取商品可领取优惠券")
public class ProductValidCouponReqVO {

    @ApiModelProperty(value = "商品ID: supplierItemId 或者 areaItemId", required = true)
    @NotNull(message = "[itemId] 商品类型必填")
    private Long itemId;

    @ApiModelProperty(value = "商品类型: local-本地商品, global-全国商品", required = true)
    @NotEmpty(message = "[type] 商品类型必填")
    private String type;
}
