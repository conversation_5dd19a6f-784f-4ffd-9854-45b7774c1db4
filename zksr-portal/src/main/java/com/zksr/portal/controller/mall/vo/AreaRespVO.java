package com.zksr.portal.controller.mall.vo;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@ApiModel("城市接口 响应")
public class AreaRespVO {

    @ApiModelProperty(value = "城市编号", required = true, example = "2")
    private Long areaId;

    @ApiModelProperty(value = "父编号", required = true, example = "1")
    @NotNull(message = "父编号")
    private Long pid;

    @ApiModelProperty(value = "城市名", required = true, example = "长沙市")
    private String areaName;

    @ApiModelProperty(value = "运营商id", example = "城市运营商")
    private String dcId;

    @ApiModelProperty(value = "是否开通本地配送业务", example = "城市运营商")
    private String localFlag;

    @ApiModelProperty(value = "状态")
    private Long status;

    @ApiModelProperty(value = "平台商城市分组id")
    private Long groupId;

    @ApiModelProperty(value = "平台商id")
    private Long sysCode;

    /** 级别 */
    @Excel(name = "级别")
    @ApiModelProperty(value = "级别")
    private Integer level;

    /** 排序号 */
    @Excel(name = "排序号")
    @ApiModelProperty(value = "排序号")
    private Integer sortNum;

}
