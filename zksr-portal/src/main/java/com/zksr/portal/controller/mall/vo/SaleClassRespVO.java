package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 平台商展示分类对象 prdt_sale_class
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
@Data
@ApiModel("平台商展示分类")
public class SaleClassRespVO {
    @ApiModelProperty(value = "分类编号", required = true, example = "2")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long saleClassId;

    @ApiModelProperty(value = "父分类编号", required = true, example = "1")
    @NotNull(message = "父分类编号不能为空")
    private Long pid;

    @ApiModelProperty(value = "分类名称", required = true, example = "办公文具")
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @ApiModelProperty(value = "分类图片", required = true)
    //@NotBlank(message = "分类图片不能为空")
    private String icon;
}
