package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024年03月27日 19:06
 * @description: 创建订单响应实体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("saveOrder 创建订单响应实体")
public class CreateOrderRespVO {
    @ApiModelProperty(value = "订单ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long orderId;

    @ApiModelProperty(value = "支付订单编号")
    private String orderNo;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "支付到期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirePayTime;

    @ApiModelProperty(value = "库存不足信息")
    private StockShortageRespVO stockShortageRespVO;
}
