package com.zksr.portal.controller.mall.vo.spu;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.TIMEZONE;
import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD_HH_MM_SS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/30 11:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SpuCombineSkuVO {

    @ApiModelProperty(value = "商品封面图")
    private String thumb;

    @ApiModelProperty(value = "商品名称")
    private String spuName;

    @ApiModelProperty(value = "单位, 需要根据字典前端渲染")
    private String unit;

    @ApiModelProperty(value = "skuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty(value = "spuId")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long spuId;

    /** 最旧生产日期, 兼容管理分类指定生产日期格式 */
    @ApiModelProperty(value = "最旧生产日期", example = "2024-03-01 00:00:00")
    private String oldestDate; 		 // 最旧生产日期

    /** 最新生产日期, 兼容管理分类指定生产日期格式 */
    @ApiModelProperty(value = "最新生产日期", example = "2024-03-31 00:00:00")
    private String latestDate; 		 // 最新生

    @ApiModelProperty(value = "套餐数量")
    private Integer qty;

    @ApiModelProperty(value = "单价")
    private BigDecimal markPrice;

    @ApiModelProperty(value = "原价")
    private BigDecimal suggestPrice;

    @ApiModelProperty(value = "在当前组合下, 当前商品最多可买多少")
    private Integer maxQty;

    @ApiModelProperty(value = "当前促销优惠金额，订单计算时使用")
    private BigDecimal discountAmt;

    @ApiModelProperty(value = "其他促销优惠金额（满减），订单计算时使用")
    private BigDecimal otherDiscountAmt;

    @ApiModelProperty(value = "优惠劵优惠金额（分摊），订单计算时使用")
    private BigDecimal couponDiscountAmt1;

    @ApiModelProperty(value = "优惠劵优惠金额（不分摊），订单计算时使用")
    private BigDecimal couponDiscountAmt2;

    @ApiModelProperty(value = "sku单位大小")
    private Integer skuUnitType;

    @ApiModelProperty(value = "全国上架商品ID")
    private Long supplierItemId;

    @ApiModelProperty(value = "本地上架商品ID")
    private Long areaItemId;
}
