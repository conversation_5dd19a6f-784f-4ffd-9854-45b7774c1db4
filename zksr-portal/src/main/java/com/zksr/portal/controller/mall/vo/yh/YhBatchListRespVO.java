
package com.zksr.portal.controller.mall.vo.yh;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 8:50
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public class YhBatchListRespVO {

    @ApiModelProperty("总数")
    private Long total;

    @ApiModelProperty("入驻商商品列表")
    private List<YhPageSupplierGroupItemVO> itemList = new ArrayList<>();

    //@ApiModelProperty("共享促销活动")
    //private List<ActivityLabelInfoVO> activityList = new ArrayList<>();

    public YhBatchListRespVO(Long total, List<YhPageSupplierGroupItemVO> itemList) {
        this.total = total;
        this.itemList = itemList;
    }
}
