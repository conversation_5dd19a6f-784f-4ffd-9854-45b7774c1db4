package com.zksr.portal.controller.mall;

import com.google.common.collect.Lists;
import com.zksr.account.api.balance.BalanceApi;
import com.zksr.account.api.balance.BalanceFlowApi;
import com.zksr.account.api.balance.dto.MemBranchBalanceFlowDTO;
import com.zksr.account.api.balance.vo.AccBalanceFlowRespVO;
import com.zksr.account.api.balance.vo.AccBalanceRespVO;
import com.zksr.account.api.balance.vo.MemBranchBalanceRespVO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 门店余额模块Controller
 */
@RestController
@RequestMapping("/mall/branchBalance")
@Api(tags = "商城 - 门店接口")
public class MemBranchBalanceController {

    @Resource
    private BalanceApi balanceApi;

    @Resource
    private BalanceFlowApi balanceFlowApi;

    @GetMapping("/getDetail")
    @ApiOperation(value = "获取充值或退款详情信息", httpMethod = HttpMethod.GET)
    @RequiresMallLogin(isMember = true)
    public CommonResult<MemBranchBalanceRespVO> getDetailInfo(@RequestParam(value = "branchId") Long branchId) {
        MemBranchBalanceRespVO balanceRespVO = new MemBranchBalanceRespVO();
        balanceRespVO.setBranchId(branchId);
        // 查询账户系统获取门店余额数据
        CommonResult<List<AccBalanceRespVO>> commonResult = balanceApi.getBalanceInfoList(Lists.newArrayList(branchId));
        List<AccBalanceRespVO> balanceRespVOList = commonResult.getCheckedData();
        Map<Long, BigDecimal> balanceMap = balanceRespVOList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AccBalanceRespVO::getBranchId, AccBalanceRespVO::getAmt, (x, y) -> x));
        balanceRespVO.setAmt(balanceMap.getOrDefault(branchId, BigDecimal.ZERO));
        return CommonResult.success(balanceRespVO);
    }

    @GetMapping("/getFlowPageList")
    @ApiOperation(value = "获得门店余额流水分页列表", httpMethod = HttpMethod.GET)
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<AccBalanceFlowRespVO>> getFlowPageList(@Valid MemBranchBalanceFlowDTO pageReqDTO) {
        return balanceFlowApi.getFlowPageList(pageReqDTO);
    }

}
