package com.zksr.portal.controller.mall.vo;

import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RateResultDTO {
    private BigDecimal softwareRate;
    private BigDecimal partnerRate;
    private BigDecimal colonelRate;
    private ColonelDTO colonel;
    private PayConfigDTO payConfigDTO;
}