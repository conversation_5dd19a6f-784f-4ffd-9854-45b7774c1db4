package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel("商城-入驻商信息 VO对象")
public class SupplierRespVO {
    /** 入驻商id */
    @ApiModelProperty(value = "入驻商id", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 入驻商编号 */
    @ApiModelProperty(value = "入驻商编号")
    private String supplierCode;

    /** 入驻商名称 */
    @ApiModelProperty(value = "入驻商名称")
    private String supplierName;

    /** 联系人 */
    @ApiModelProperty(value = "入驻商编号")
    private String contactName;

    /** 联系电话 */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /** 联系地址 */
    @ApiModelProperty(value = "联系地址")
    private String contactAddress;

    /** 起送价 */
    @ApiModelProperty(value = "起送价")
    private BigDecimal minAmt;

    /** 入驻商头像地址 */
    @ApiModelProperty(value = "入驻商头像地址")
    private String avatar;

    /** 入驻商配送标签, 来自平台商字典 */
    @ApiModelProperty(value = "入驻商配送标签, 来自平台商字典")
    private String productDistributionLabel;
}
