package com.zksr.portal.controller.mall.vo.branch;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 门店微信B端商家助手认证状态
 * @date 2024/7/31 9:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BranchWxMerchantStateRespVO {

    @ApiModelProperty("认证状态: 0-无需认证, 1-需要认证")
    private Integer state;

    public static BranchWxMerchantStateRespVO needAuth() {
        return BranchWxMerchantStateRespVO.builder()
                .state(NumberPool.INT_ONE)
                .build();
    }

    public static BranchWxMerchantStateRespVO success() {
        return BranchWxMerchantStateRespVO.builder()
                .state(NumberPool.INT_ZERO)
                .build();
    }
}
