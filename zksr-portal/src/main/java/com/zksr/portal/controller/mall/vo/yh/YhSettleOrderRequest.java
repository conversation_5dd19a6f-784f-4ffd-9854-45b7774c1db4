package com.zksr.portal.controller.mall.vo.yh;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.portal.controller.mall.vo.ActivityVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("YhSettleOrderRequest 补货预下单接口请求对象")
public class YhSettleOrderRequest {

    @ApiModelProperty(value = "门店编号")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "用户编号")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long memberId;

//    @ApiModelProperty(value = "要货批次号", required = true)
//    private String posYhBatchNo;

    @DateTimeFormat(pattern = YYYY_MM_DD)
    @JsonFormat(pattern = YYYY_MM_DD, timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "批次日期", required = true)
    private Date batchDate;

    @ApiModelProperty(value = "入驻商Id")
    private Long supplierId;

    @ApiModelProperty(value = "优惠劵编号集合")
    private List<Long> couponIds;

    @ApiModelProperty(value = "营销活动集合")
    private List<ActivityVO> activitys;




}
