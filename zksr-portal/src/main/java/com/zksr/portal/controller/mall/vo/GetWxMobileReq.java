package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
@ApiModel("微信平台 - 解密获取手机号接口请求")
public class GetWxMobileReq {

    @ApiModelProperty(value = "sysSource 必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;

    @ApiModelProperty(value = "openid 必填")
    @NotBlank(message = "openid不能为空")
    private String openid;

    @ApiModelProperty(value = "code 必填")
    @NotBlank(message = "code不能为空")
    private String code;

    @ApiModelProperty(value = "iv 必填")
    @NotBlank(message = "iv不能为空")
    private String iv;

    @ApiModelProperty(value = "encryptedData 必填")
    @NotBlank(message = "encryptedData不能为空")
    private String encryptedData;
}
