package com.zksr.portal.controller.mall.vo.prdt;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
*
 *
*   <AUTHOR>
*   @date 2024/4/22 10:11
 *  @description: 优惠券匹配可用商品
*/
@ApiModel(value = "商品SKU搜索商品请求VO")
@Data
public class CouponSkuSearchReqVO extends PageParam {

    @ApiModelProperty(value = "关键字", example = "好看")
    private String condition;

    @ApiModelProperty(value = "优惠券模版ID")
    @NotNull(message = "优惠券模版ID必填")
    private Long couponTemplateId;

    /** 排序类型 */
    @ApiModelProperty("排序类型: none-无, sale-销量, price-价格")
    private String sortType = StringPool.NONE;

    /** 排序方式 @com.zksr.common.core.enums.ProductSortType */
    @ApiModelProperty("排序方式: none-无,des-降序, asc-升序")
    private String orderBy = StringPool.NONE;

    @ApiModelProperty("平台商source")
    private String sysSource;
}
