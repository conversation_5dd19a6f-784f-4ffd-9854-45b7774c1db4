package com.zksr.portal.controller.mall.vo.car;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 验证特价/秒杀,是否达到上限
 * @date 2024/3/26 19:35
 */
@Data
@ApiModel(description = "验证特价/秒杀,是否达到上限")
public class CarValidateActivityReqVO extends CarPageReqVO {

    @ApiModelProperty(value = "促销ID")
    private Long activityId;
}
