package com.zksr.portal.controller.mall;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemMemberAddressPageReqDTO;
import com.zksr.member.api.member.dto.MemMemberAddressSaveReqDTO;
import com.zksr.member.api.member.vo.MemberAddressRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * 用户地址Controller
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Api(tags = "商城 - 用户地址接口")
@Validated
@RestController
@RequestMapping("/mall/memberAddress")
public class MemberAddressController {
    @Resource
    private MemberApi memberApi;

    @ApiOperation("新增用户地址")
    @PostMapping(value = "/addMemberAddress")
    @ResponseBody
    public CommonResult<Long> addMemberAddress(@RequestBody @Valid MemMemberAddressSaveReqDTO createReqDTO) {

//        createReqDTO.setMemberId(MallSecurityUtils.getMemberId());

        return success(memberApi.addMemberAddress(createReqDTO).getCheckedData());
    }

    @ApiOperation("修改用户地址")
    @PostMapping(value = "/updateMemberAddress")
    @ResponseBody
    public CommonResult<Boolean> updateMemberAddress(@RequestBody @Valid MemMemberAddressSaveReqDTO createReqDTO) {
//        createReqDTO.setMemberId(MallSecurityUtils.getMemberId());
        memberApi.updateMemberAddress(createReqDTO);
        return success(Boolean.TRUE);
    }

    @ApiOperation("查询用户地址")
    @PostMapping(value = "/getMemberAddress")
    @ResponseBody
    public CommonResult<PageResult<MemberAddressRespDTO>> getMemberAddress(@RequestBody @Valid MemMemberAddressPageReqDTO pageReqVO) {
        return memberApi.getMemberAddress(pageReqVO);
    }

}
