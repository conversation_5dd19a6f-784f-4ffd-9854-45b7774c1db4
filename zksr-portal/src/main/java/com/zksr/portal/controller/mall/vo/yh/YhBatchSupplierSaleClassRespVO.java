package com.zksr.portal.controller.mall.vo.yh;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 8:50
 */
@Data
@ApiModel(description = "批次要货单入驻商展示分类集合")
public class YhBatchSupplierSaleClassRespVO {

    @ApiModelProperty("入驻商一级展示分类集合")
    private List<Supplier> supplierList = new ArrayList<>();

    @Data
    @NoArgsConstructor
    public static class Supplier {

        @ApiModelProperty("入驻商名称")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long supplierId;

        @ApiModelProperty("入驻商名称")
        private String supplierName;

        @ApiModelProperty("起送金额 (本地)")
        private BigDecimal minAmt;

        @ApiModelProperty("一级展示类目集合")
        private List<SaleClass> firstSaleClassList = new ArrayList<>();

        public Supplier(SupplierDTO supplierDTO) {
            this.supplierId = supplierDTO.getSupplierId();
            this.supplierName = supplierDTO.getSupplierName();
            this.minAmt = supplierDTO.getMinAmt();
        }
    }

    @Data
    @NoArgsConstructor
    public static class SaleClass {

        @JsonSerialize(using = CustomLongSerialize.class)
        @ApiModelProperty("一级展示类目ID")
        private Long firstSaleClassId;

        @ApiModelProperty("展示类名称")
        private String saleClassName;

        @ApiModelProperty("分类图标")
        private String icon;

        /** 排序 */
        @ApiModelProperty("排序号")
        private Long sort;

        public SaleClass(AreaClassDTO areaClassDTO) {
            this.firstSaleClassId = areaClassDTO.getAreaClassId();
            this.icon = areaClassDTO.getIcon();
            this.saleClassName = areaClassDTO.getAreaClassName();
            this.sort = Objects.isNull(areaClassDTO.getSort()) ? NumberPool.LONG_ZERO : areaClassDTO.getSort();
        }
    }
}
