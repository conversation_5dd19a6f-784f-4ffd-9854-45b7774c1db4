package com.zksr.portal.controller.mall;

import com.zksr.common.core.domain.R;
import com.zksr.common.core.utils.file.FileUtils;
import com.zksr.file.api.file.FileApi;
import com.zksr.system.api.domain.SysFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2024年04月28日 17:11
 * @description: FileController
 */
@RestController
@RequestMapping("/mall/file")
@Api(tags = "商城 - 文件上传接口")
@Slf4j
public class FileController {

    @Autowired
    private FileApi remoteFileApi;

    /**
     * 文件上传请求
     */
    @PostMapping("upload")
    @ApiOperation("文件上传请求")
    public R<SysFile> upload(MultipartFile file)
    {
        try {
            // 上传并返回访问地址
            String url = remoteFileApi.uploadFileByMail(file).getCheckedData();
            SysFile sysFile = new SysFile();
            sysFile.setName(FileUtils.getName(url));
            sysFile.setUrl(url);
            return R.ok(sysFile);
        }
        catch (Exception e)
        {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }
}
