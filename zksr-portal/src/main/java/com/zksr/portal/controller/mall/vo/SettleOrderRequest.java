package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel("preSaveOrder 预下单接口请求对象")
public class SettleOrderRequest {

    @ApiModelProperty(value = "门店编号")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;

    @ApiModelProperty(value = "用户编号")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long memberId;

    @ApiModelProperty(value = "入驻商商品信息数组", required = true)
    private List<Item> items;

    @ApiModelProperty(value = "优惠劵编号集合")
    private List<Long> couponIds;

    @ApiModelProperty(value = "营销活动集合")
    private List<ActivityVO> activitys;

    @ApiModelProperty(value = "接口类型 0 购物车跳结算页  1 创建订单",hidden = true)
    private  Integer interfaceType;

    /**
     * 分销模式
     */
    @ApiModelProperty(value = "分销模式")
    private String distributionMode;

    /**
     * 收货地址
     */
    @ApiModelProperty(value = "收货地址")
    private String deliveryAddress;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String contactName;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String contactPhone;

    /**
     * 用户地址ID
     */
    @ApiModelProperty(value = "用户地址ID")
    private Long memberAddressId;

    /** 用户发票id */
    @Excel(name = "用户发票id")
    @JsonSerialize(using = CustomLongSerialize.class)
    private Long memberInvoiceId;

    @ApiModel(value = "购物项")
    @Data
    public static class Item {

        @ApiModelProperty(value = "入驻商id", required = true)
        @JsonSerialize(using= CustomLongSerialize.class)
        private Long supplierId;

        @ApiModelProperty(value = "备注")
        private String memo;

        @ApiModelProperty(value = "入驻商-购物项数组", required = true)
        private List<SupplierItem> supplierItems;

        @ApiModel(value = "购物项")
        @Data
        public static class SupplierItem {

            // ========== SPU 信息 ==========
            @ApiModelProperty(value = "SPU 编号", required = true)
            @JsonSerialize(using= CustomLongSerialize.class)
            private Long spuId;

            // ========== SKU 信息 ==========
            @ApiModelProperty(value = "SKU 编号", required = true)
            @JsonSerialize(using= CustomLongSerialize.class)
            private Long skuId;

            @ApiModelProperty(value = "入驻商上架商品id", required = true)
            @JsonSerialize(using= CustomLongSerialize.class)
            private Long supplierItemId;

            @ApiModelProperty(value = "城市上架商品id", required = true)
            @JsonSerialize(using= CustomLongSerialize.class)
            private Long areaItemId;

            // ========== 购物车信息 ==========
            @ApiModelProperty(value = "购物车编号", required = true)
            private String carId;

            @ApiModelProperty(value = "购买数量", required = true)
            private Long count;

            @ApiModelProperty(value = "是否参与活动（秒杀或特价）1：是 0： 否", required = true)
            private Integer isActivitySpSk;

            @ApiModelProperty(value = "订单购买单位", required = true)
            private String unit;

            @ApiModelProperty(value = "单位大小", required = true)
            private Integer unitSize;

            @ApiModelProperty(value = "0-普通商品, 1-组合商品", required = true)
            private Integer goodsType = NumberPool.INT_ZERO;
        }

    }
}
