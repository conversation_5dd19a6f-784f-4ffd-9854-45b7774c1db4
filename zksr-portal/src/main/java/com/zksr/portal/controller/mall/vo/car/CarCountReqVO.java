package com.zksr.portal.controller.mall.vo.car;

import com.zksr.common.core.enums.ProductType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车选中合计数据
 * @date 2024/3/26 19:16
 */
@Data
@ApiModel(description = "购物车门店选中数据")
public class CarCountReqVO {

    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;

    @ApiModelProperty(value = "商品类型 local-本地商品, global-全国商品", required = true, example = "local")
    private String productType = ProductType.LOCAL.getType();

    @ApiModelProperty("忽略欠款选中, 默认-false, 欠款入驻商商品不会下发选中")
    private Boolean ignoreDebt = false;
}
