package com.zksr.portal.controller.mall.vo.after;

import com.zksr.portal.controller.mall.vo.SettleOrderRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年04月20日 10:07
 * @description: OrderAfterRequest
 */
@Data
@ApiModel("订单售后实体 请求")
public class OrderAfterRequest {
    @ApiModelProperty(value = "订单ID", required = true)
    private Long orderId;

    @ApiModelProperty(value = "订单编号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "入驻商订单集合")
    private List<SupplierOrder> supplierOrders;

    @ApiModel(value = "入驻商订单项")
    @Data
    public static class SupplierOrder {

        @ApiModelProperty(value = "入驻商id", required = true)
        private Long supplierId;

        @ApiModelProperty(value = "入驻商订单id", required = true)
        private Long supplierOrderId;

        @ApiModelProperty(value = "入驻商订单单号", required = true)
        private Long supplierOrderNo;

        @ApiModelProperty(value = "入驻商订单明细项数组")
        private List<SupplierOrderDtl> supplierOrderDtls;

        @ApiModel(value = "入驻商订单明细项")
        @Data
        public static class SupplierOrderDtl {

            @ApiModelProperty(value = "入驻商订单明细ID")
            private Long supplierOrderDtlId;

            @ApiModelProperty(value = "入驻商订单明细ID")
            private String supplierOrderDtlNo;

            @ApiModelProperty(value = "退货商品数量")
            private Long afterItemNum;
        }

    }
}
