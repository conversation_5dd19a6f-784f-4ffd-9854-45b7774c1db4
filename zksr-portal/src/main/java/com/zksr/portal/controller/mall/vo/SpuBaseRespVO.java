package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("商城-SPU基本 VO对象")
public class SpuBaseRespVO {

    @ApiModelProperty(value = "商品 SPU id", required = true, example = "1")
    private Long spuId;

    @ApiModelProperty(value = "商品 SPU 编号", required = true, example = "123124123")
    private String spuNo;

    @ApiModelProperty(value = "图片地址", example = "https://www.XXXXX.cn/xx.png")
    private String picUrl;

    @ApiModelProperty(value = "销售价格", required = true, example = "99.99")
    private BigDecimal price;

    @ApiModelProperty(value = "库存", required = true, example = "1")
    private Integer stock;

    /**
     * 属性数组
     */
    //private List<SkuPropertiesVO> properties;
}
