package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("createOrder 创建单接口请求对象")
public class CreateOrderRequest extends SettleOrderRequest{

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "支付方式")
    private String payWay;

    @ApiModelProperty(value = "订单来源{mini：商城小程序下单， ywyApp : 业务员APP下单，androidMallApp：商城APP下单}")
    private String orderSource;

    @ApiModelProperty(value = "是否余额支付 0：否，1：是")
    private Integer isPayBalance;
}
