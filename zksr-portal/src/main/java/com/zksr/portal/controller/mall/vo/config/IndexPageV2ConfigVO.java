package com.zksr.portal.controller.mall.vo.config;

import com.zksr.system.api.page.dto.PagesConfigDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2025/2/5 16:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "首页装修模版")
public class IndexPageV2ConfigVO {

    @ApiModelProperty("首页装修模版集合, 可能存在到时间生效的模版")
    private List<PagesConfigDTO> pagesConfigList;
}
