package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.common.core.web.CustomStockIntSerialize;
import com.zksr.product.api.spu.dto.SkuUnitDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "用户 App - 商品 SPU 明细的 SKU 信息")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SkuDetailRespVO {

    @ApiModelProperty(value = "上架ID", example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long itemId;

    @ApiModelProperty(value = "商品 SKU id", example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    @ApiModelProperty(value = "SKU图片地址", required = true, example = "https://www.XXXX.cn/xx.png")
    private String iconUrl;

    @ApiModelProperty(value = "库存", required = true, example = "1")
    @JsonSerialize(using = CustomStockIntSerialize.class)
    private BigDecimal stock;

    /** 保质期 */
    @ApiModelProperty(value = "保质期", required = true, example = "180")
    private Integer expirationDate; 		 // 保质期

    /**
     * 商品属性数组
     */
    @ApiModelProperty(value = "规格属性")
    private List<SkuPropertiesVO> propertieList;

    @ApiModelProperty("单位集合")
    private List<SkuUnit> unitList;

    @ApiModelProperty("最小单位价格")
    private BigDecimal minUnitSuggestPrice;

    @Data
    public static class SkuUnit {

        @ApiModelProperty("单位标准价")
        private BigDecimal markPrice;
        
        @ApiModelProperty("分润金额")
        private BigDecimal profitAmount;

        @ApiModelProperty("建议零售价 (划线价)")
        private BigDecimal suggestPrice;

        @ApiModelProperty("活动价, 促销价")
        private BigDecimal promotionPrice;

        @ApiModelProperty("条码")
        private String barcode;

        @ApiModelProperty("单位-字典")
        private String unit;

        @ApiModelProperty("单位名称")
        private String unitName;

        /**
         * 参见枚举 {@link com.zksr.common.core.enums.UnitType}
         */
        @ApiModelProperty("单位类型, 1-最小单位, 2-中单位, 3-大单位")
        private Integer unitSize;

        @ApiModelProperty("单位起订")
        private Long minOq;

        @ApiModelProperty("单位订货组")
        private Long jumpOq;

        @ApiModelProperty("单位最大限购")
        private Long maxOq;

        @ApiModelProperty(value = "业务员参考佣金", required = true, example = "99.99")
        private BigDecimal commissionPrice = BigDecimal.ZERO;
    }

}
