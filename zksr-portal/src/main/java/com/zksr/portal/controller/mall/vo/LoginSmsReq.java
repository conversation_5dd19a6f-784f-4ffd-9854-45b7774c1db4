package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
* @Description: 登录-短信实体类
* @Author: liuxingyu
* @Date: 2024/5/10 10:03
*/
@Data
public class LoginSmsReq {

    @ApiModelProperty(value = "手机号", required = true)
    @NotEmpty(message = "手机号不能为空")
    private String phone;

    @ApiModelProperty(value = "source", required = true)
    @NotEmpty(message = "source不能为空")
    private String sysSource;

    @ApiModelProperty(value = "验证码", required = true)
    private String code;

    @ApiModelProperty(value = "设备ID", required = true)
    private String deviceId;
}
