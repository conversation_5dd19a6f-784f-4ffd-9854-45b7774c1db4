package com.zksr.portal.controller.mall.vo.car;

import com.zksr.common.core.enums.ProductType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车选中状态验证
 * @date 2024/3/26 19:16
 */
@Data
@ApiModel(description = "购物车门店选中数据")
public class CarCountStatusReqVO {

    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;

    @ApiModelProperty(value = "门店ID", required = true)
    private Long supplierId;

    @ApiModelProperty(value = "商品类型 local-本地商品, global-全国商品", required = true, example = "local")
    private String productType = ProductType.LOCAL.getType();
}
