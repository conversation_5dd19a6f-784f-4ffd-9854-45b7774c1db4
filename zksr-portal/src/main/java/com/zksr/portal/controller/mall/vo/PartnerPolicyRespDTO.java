package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "平台商相关系统配置")
public class PartnerPolicyRespDTO {
    @ApiModelProperty("小程序用户注册类型 (0：简易注册  1：常规注册)")
    private String registerType;

    @ApiModelProperty("是否开启微信b端商家助手认证 (1 开启, 0 不开启)")
    private String openWechatMerchantAuth;

    @ApiModelProperty("强制微信b端商家助手认证 (1 开启, 0 不开启)")
    private String forceWechatMerchantAuth;

    @ApiModelProperty("商城是否开启二级分类展示 (1 开启, 0 不开启)")
    private String openLevelTwoCategoryShow;

    @ApiModelProperty("是否展示佣金 0-关闭 1-开启")
    private String isShowCommission;

    @ApiModelProperty("平台本地商品售后类型 （0：入驻商审核，1：业务员审核）")
    private String localItemAfterType;

    @ApiModelProperty("平台本地商品售后审核类型 （0：入驻商审核，1：业务员审核）")
    private String localItemAfterApproveType;

    @ApiModelProperty("平台账号默认密码")
    private String defalutPassword;

    @ApiModelProperty("库存显示模式")
    private String stockShowMode;

    @ApiModelProperty("库存显示临界值")
    private String stockShowThreshold;
}
