package com.zksr.portal.controller.mall.vo.coupon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/7 9:23
 */
@ApiModel(description = "获取优惠券模版数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponTemplateDetailReqVO {

    @ApiModelProperty(value = "优惠券ID集合", required = true)
    @Size(min = 1, max = 20, message = "最小1最大20个优惠券模版ID")
    private List<Long> couponTemplateIdList;
}
