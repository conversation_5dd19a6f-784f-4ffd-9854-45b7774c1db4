package com.zksr.portal.controller.mall.vo.coupon;

import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/7 9:23
 */
@ApiModel(description = "获取优惠券模版数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponTemplateFragmentRespVO {

    @ApiModelProperty("优惠券商品列表片段集合")
    private List<FragmentVO> couponItemList = new ArrayList<>();

    @ApiModel(description = "优惠券商品列表片段信息")
    @Data
    public static class FragmentVO {

        @ApiModelProperty("优惠券信息")
        private CouponValidItemVO couponTemplate;

        @ApiModelProperty("商品列表")
        private List<SkuPageRespVO> itemList;
    }

}
