package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
*
 *   子用户 请求实体
* <AUTHOR>
* @date 2024/4/25 10:32
*/
@Data
@ApiModel("子用户 请求实体")
public class ChildUserReqVO {

    //用户名  memberName 必填
    /** 用户名 */
    @ApiModelProperty(value = "用户名  必填")
    @NotBlank(message = "用户名不能为空")
    private String memberName;

    /** 用户账号 */
    @ApiModelProperty(value = "用户账号")
    @NotBlank(message = "用户账号不能为空")
    private String userName;

    /** 用户密码 */
    @ApiModelProperty(value = "用户密码")
    @NotBlank(message = "用户密码不能为空")
    private String password;

    /** 父ID */
    @ApiModelProperty(value = "父ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long pid;

    /** 当前门店ID */
    @ApiModelProperty(value = "当前门店ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long branchId;


}
