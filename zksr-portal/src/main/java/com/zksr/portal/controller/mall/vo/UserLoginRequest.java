package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户登陆 接口请求")
public class UserLoginRequest {

    @ApiModelProperty(value = "用户账号 必填")
    // @NotBlank(message = "用户账号不能为空")
    private String username;

    @ApiModelProperty(value = "用户密码  必填")
    // @NotBlank(message = "用户密码不能为空")
    private String password;

    @ApiModelProperty(value = "sysSource  必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "登录来源")
    private String loginFrom;

    @ApiModelProperty(value = "美云销，身份")
    private String identity;

    @ApiModelProperty(value = "美云销，用户登录凭证")
    private String iscToken;

    @ApiModelProperty(value = "美云销，维度数据编码列表")
    private List<String> dataDimensionCodeList;
}
