package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
*
 *
* <AUTHOR>
* @date 2024/4/22 10:12
*/
@ApiModel(value = "商品SKU搜索商品响应VO")
@Data
public class SkuSearchRespVO {



    /** 上架商品ID */
    @ApiModelProperty("上架商品ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long itemId;

    /** 商品类型, local本地, global 全国 */
    @ApiModelProperty("商品类型, local本地, global 全国")
    private String type;

    /** 商品名称 */
    @ApiModelProperty("商品名称")
    private String spuName;


    /** 单位 */
    @ApiModelProperty("单位")
    private String unit;

    /** 规格属性, 可能涉及搜索 */
    @ApiModelProperty("规格属性")
    private String properties;

    /** 封面图片 */
    @ApiModelProperty("封面图片")
    private String thumb;

    /** 封面视频 */
    @ApiModelProperty("封面视频")
    private String thumbVideo;

    /** SPU ID */
    @ApiModelProperty("SPU ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long spuId;

    /** 默认-1, 兼容全国和本地商品售后 */
    @ApiModelProperty(" 默认-1, 兼容全国和本地商品售后")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long areaId;

    /** 默认-1, 本地渠道 */
    @ApiModelProperty("默认-1, 本地渠道")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long channelId;

    /** 默认-1, 兼容全国和本地商品 平台商城市分组id */
    @ApiModelProperty("默认-1, 兼容全国和本地商品 平台商城市分组id ")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long groupId;

    /** SKU ID */
    @ApiModelProperty("SKU ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long skuId;

    /** 平台展示分类ID */
    @ApiModelProperty(" 平台展示分类ID ")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long classId;

    /** 平台展示分类名称 */
    @ApiModelProperty("平台展示分类名称 ")
    private String className;

    /** 入驻商ID */
    @ApiModelProperty("入驻商ID ")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long supplierId;

    /** 品牌ID */
    @ApiModelProperty("品牌ID")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long brandId;

    /** 品牌名称 */
    @ApiModelProperty("品牌名称")
    private String brandName;

    /** 建议零售价, 原价 */
    @ApiModelProperty("建议零售价, 原价")
    private BigDecimal suggestPrice;

    /** 销售最低价 */
    @ApiModelProperty("销售最低价")
    private BigDecimal minPrice;

    /** 最低价规格 */
    @ApiModelProperty("最低价规格")
    private BigDecimal minSkuName;

    /** 销售价1 */
    @ApiModelProperty("销售价1")
    private BigDecimal salePrice;

    /** 销量 */
    @ApiModelProperty("销量")
    private Long saleQty;

    /** 库存 */
    @ApiModelProperty("库存")
    private Long stock;

    @ApiModelProperty(value = "商品价格", required = true, example = "99.99")
    private BigDecimal price;

    @ApiModelProperty(value = "市场价", required = true, example = "99.99")
    private BigDecimal markPrice;

    @ApiModelProperty(value = "促销 价格", required = true, example = "99.99") // 通过会员等级，计算出折扣后价格
    private BigDecimal promotionPrice;

    /** 最旧生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最旧生产日期", example = "2024-03-01 00:00:00")
    private Date oldestDate; 		 // 最旧生产日期

    /** 最新生产日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最新生产日期", example = "2024-03-31 00:00:00")
    private Date latestDate; 		 // 最新生产日期


    /** 商品规格 */
    @ApiModelProperty("商品规格")
    private String specName;


    /** 是否开启多规格 1-是 0-否 */
    @ApiModelProperty(value = "是否开启多规格 1-是 0-否",  example = "1-是 0-否")
    private Long isSpecs;

    /** 起订 */
    @ApiModelProperty(value = "起订", example = "1")
    private Long minOq;

    /** 订货组数 */
    @ApiModelProperty(value = "订货组数", example = "1")
    private Long jumpOq;

    /** 单位 */
    @ApiModelProperty("单位尺寸,1-小单位,2-中单位,3=大单位")
    private Integer unitSize;
}
