package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@ApiModel(description = "支付方式")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayWay {
    @ApiModelProperty(value = "支付方式编码（0-在线支付 1-储值支付 2-货到付款）")
    private String payWayCode;

    @ApiModelProperty(value = "支付方式名称")
    private String payWayName;

    @ApiModelProperty(value = "钱包余额")
    private BigDecimal walletAmt = BigDecimal.ZERO;
}
