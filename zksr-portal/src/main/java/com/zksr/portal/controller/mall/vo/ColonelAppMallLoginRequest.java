package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("colonelAppMallLogin 接口请求")
public class ColonelAppMallLoginRequest {
    @ApiModelProperty(value = "业务员Id  必填", required = true)
    @NotNull(message = "业务员Id不能为空")
    private Long colonelId;

    @ApiModelProperty(value = "门店Id  必填", required = true)
    @NotNull(message = "门店Id不能为空")
    private Long branchId;

    @ApiModelProperty(value = "随机字符串  必填", required = true)
    @NotBlank(message = "随机字符串不能为空")
    private String randomStr;

    @ApiModelProperty(value = "签名 sign  必填", required = true)
    @NotBlank(message = "签名 sign不能为空")
    private String sign;

    public String getSignInfo() {
        return colonelId + "" + branchId + randomStr;
    }
}
