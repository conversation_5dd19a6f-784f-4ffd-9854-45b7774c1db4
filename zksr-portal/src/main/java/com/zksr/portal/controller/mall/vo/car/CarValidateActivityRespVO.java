package com.zksr.portal.controller.mall.vo.car;

import com.zksr.common.core.pool.NumberPool;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 验证特价/秒杀,是否达到上限
 * @date 2024/3/26 19:35
 */
@Data
@ApiModel(description = "验证特价/秒杀,是否达到上限")
@AllArgsConstructor
@NoArgsConstructor
public class CarValidateActivityRespVO {

    @ApiModelProperty("活动商品数量")
    private Long activityItemCnt = NumberPool.LONG_ZERO;
}
