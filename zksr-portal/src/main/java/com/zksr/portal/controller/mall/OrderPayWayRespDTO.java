package com.zksr.portal.controller.mall;

import com.zksr.portal.controller.mall.vo.PayWay;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@ApiModel(description = "支付方式")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderPayWayRespDTO {
    @ApiModelProperty(value = "实付金额")
    private BigDecimal payAmt;

    @ApiModelProperty(value = "订单支付信息内容提示")
    private String orderInfoTip;

    @ApiModelProperty(value = "支付方式数组", required = true)
    private List<PayWay> payWays;
}
