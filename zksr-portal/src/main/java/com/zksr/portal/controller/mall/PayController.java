package com.zksr.portal.controller.mall;

import cn.hutool.core.map.MapUtil;
import com.zksr.account.api.pay.PayApi;
import com.zksr.account.model.pay.dto.order.PayOrderRespDTO;
import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.constant.PayConstants;
import com.zksr.common.core.constant.PayOrderSubmitExtras;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.portal.controller.mall.vo.order.PotalHdfkPayOrderSubmitReqVO;
import com.zksr.portal.controller.mall.vo.order.PotalPayOrderSubmitReqVO;
import com.zksr.portal.controller.mall.vo.pay.PayPreSuccessReqVO;
import com.zksr.portal.convert.pay.PayConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.OrderService;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.trade.api.after.AfterApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import java.util.HashMap;

import static com.zksr.account.enums.ErrorCodeConstants.PAY_ORDER_PRE_PAY_SUCCESS;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单支付, 退款模块
 * @date 2024/3/16 9:12
 */
@RestController
@RequestMapping("/mall/pay")
@Api(tags = "商城 - 支付接口")
@Slf4j
public class PayController {

    @Resource
    private PayApi payApi;

    @Resource
    private AfterApi afterApi;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private OrderService orderService;

    @ApiOperation(value = "发起订单支付", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/create")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<PayOrderRespDTO> create(@Valid @RequestBody PotalPayOrderSubmitReqVO payOrderSubmitReqVO) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(payOrderSubmitReqVO.getSysSource());
        payOrderSubmitReqVO.setSysCode(partnerDto.getSysCode());
        // 如果下单以后小程序通知服务器这一笔订单已经支付了, 那么第二次发起的时候给予提示
        if (orderService.validatePreSuccess(MallSecurityUtils.getBranchId(), payOrderSubmitReqVO.getOrderNo())) {
            throw exception(PAY_ORDER_PRE_PAY_SUCCESS);
        }
        // 扩展参数当前支付门店ID
        payOrderSubmitReqVO.setExtras(MapUtil.of(PayOrderSubmitExtras.BRANCH_ID, MallSecurityUtils.getBranchId().toString()));
        return payApi.submitPayOrder(PayConvert.INSTANCE.convert(payOrderSubmitReqVO));
    }

    @ApiOperation(value = "订单预支付成功", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/payPreSuccess")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<Boolean> payPreSuccess(@Valid @RequestBody PayPreSuccessReqVO reqVO) {
        reqVO.setBranchId(MallSecurityUtils.getBranchId());
        orderService.payPreSuccess(reqVO);
        return CommonResult.success(Boolean.TRUE);
    }

    @ApiOperation(value = "发起货到付款支付", httpMethod = HttpMethod.POST)
    @PostMapping(value = "/createHdfkOrderPay")
    @ResponseBody
    @RequiresMallLogin(isMember = true)
    public CommonResult<PayOrderRespDTO> createHdfkOrderPay(@Valid @RequestBody PotalHdfkPayOrderSubmitReqVO payOrderSubmitReqVO) {
        // 发起货到付款支付 前 校验是否存在未完成的售后信息
        boolean isExist = afterApi.checkNotFinishAfterByOrderId(payOrderSubmitReqVO.getOrderId()).getCheckedData();
        if (isExist) {
            throw new ServiceException("当前订单【"+payOrderSubmitReqVO.getOrderId()+"】存在未完成的售后信息, 请先处理后再进行支付！");
        }

        PartnerDto partnerDto = portalCacheService.getPartnerDto(payOrderSubmitReqVO.getSysSource());
        payOrderSubmitReqVO.setSysCode(partnerDto.getSysCode());

        HashMap<String, String> extMap = new HashMap<>();
        extMap.put(PayOrderSubmitExtras.BRANCH_ID, MallSecurityUtils.getBranchId().toString());
        extMap.put(PayConstants.SUPPLIER_ORDER_DTL_ID_LIST, StringUtils.join(payOrderSubmitReqVO.getSupplierOrderDtlIdList(), StringPool.COMMA));
        payOrderSubmitReqVO.setExtras(extMap);
        return payApi.submitPayOrder(PayConvert.INSTANCE.convert(payOrderSubmitReqVO));
    }

}
