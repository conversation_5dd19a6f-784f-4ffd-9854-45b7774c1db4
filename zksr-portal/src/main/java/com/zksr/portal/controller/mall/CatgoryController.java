package com.zksr.portal.controller.mall;

import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.convert.cfg.ConfigConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.ISaleClassService;
import com.zksr.portal.service.mall.ISkuService;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@RestController
@RequestMapping("/mall/category")
@Api(tags = "商城 - 分类接口")
@Slf4j
public class CatgoryController {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private ISkuService skuService;

    @Autowired
    private ISaleClassService saleClassService;

    @Autowired
    private Cache<Long, SupplierOtherSettingPolicyDTO> supplierOtherSettingPolicyDTOCache;

    /**
    * @Description: 获取商家管理分类
    * @Param: String supplierId
    * @return: CommonResult<List<CategoryRespVO>>
    * @Author: liuxingyu
    * @Date: 2024/5/31 10:53
    */
    @GetMapping("/getCatgoryBySupplierId")
    @ApiOperation("获取商家管理分类")
    @RequiresMallLogin
    public CommonResult<List<CategoryRespVO>> getCatgoryBySupplierId(@RequestParam("supplierId") Long supplierId) {
        return success(saleClassService.getSupplierInfoCategoryList(supplierId));
    }

    /**
     * @Description: 获取平台管理分类
     * @Param: String sysSource
     * @return: CommonResult<List < CategoryRespVO>>
     * @Author: liuxingyu
     * @Date: 2024/5/7 10:25
     */
    @GetMapping("/getCatgoryList")
    @ApiOperation("获取平台管理分类")
    @RequiresMallLogin(isMember = true)
    public CommonResult<List<CategoryRespVO>> getCatgoryList(@RequestParam("sysSource") String sysSource) {
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        if (ObjectUtil.isNull(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        List<CatgoryDTO> catgoryList = portalCacheService.getCatgoryBySysCode(partnerDto.getSysCode());
        return success(HutoolBeanUtils.toBean(catgoryList, CategoryRespVO.class));
    }


    @GetMapping("/nationalList")
    @ApiOperation("全国-商品分类列表")
    @RequiresMallLogin
    public CommonResult<List<SaleClassRespVO>> nationalList(@RequestParam("sysSource") String sysSource) {
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(sysSource);
        if (ObjectUtil.isNull(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        return success(HutoolBeanUtils.toBean(saleClassService.getSaleClassDTOS(partnerDto.getSysCode(), Objects.nonNull(branchDTO) ? branchDTO.getGroupId() : null), SaleClassRespVO.class));
    }

    @GetMapping("/localList")
    @ApiOperation("本地配送-商品分类列表")
    @RequiresMallLogin
    public CommonResult<List<AreaClassRespVO>> localList() {
        //此接口更改为游客模式可以访问，游客模式有设置默认城市
        LoginMember loginMember = MallSecurityUtils.getLoginMember();
        Long branchId = loginMember.getBranchId();
        List<AreaClassRespVO> list = new ArrayList<>();

        Long areaId = loginMember.getAreaId();
        Long channelId = null;

        if (ToolUtil.isNotEmpty(branchId) && ToolUtil.isNotEmpty(portalCacheService.getBranchDto(branchId))) {
            //如果当前门店所在城市没开通 本地配送业务，则返回空列表即可
            BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
            if (ObjectUtil.isNotNull(branchDto)) {
                areaId = branchDto.getAreaId();
                channelId = branchDto.getChannelId();
            }
        }
        if (ObjectUtil.isNull(areaId)) {
            throw new ServiceException("无区域城市");
        }
        List<AreaClassDTO> areaList = saleClassService.getAreaClassDTOS(areaId, channelId, branchId);
        return success(HutoolBeanUtils.toBean(areaList, AreaClassRespVO.class));
    }


    @GetMapping("/getSupplierDetails")
    @ApiOperation("获取入驻商详情信息")
    @RequiresMallLogin
    public CommonResult<SupplierDetailVO> supplierCategoryList(@ApiParam(value = "入驻商id") @RequestParam("supplierId") Long supplierId) {
        SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(supplierId);
        SupplierOtherSettingPolicyDTO policyDTO = portalCacheService.getPartnerSupplierOtherSettingPolicy(supplierId);
        return success(ConfigConvert.INSTANCE.buildSetSupplierDetailVO(supplierDTO, policyDTO));
    }

    @GetMapping("/getProductListBasedOnProductType")
    @ApiOperation("根据商品类型获取商品列表")
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<SkuPageRespVO>> getProductListBasedOnProductType(SpuPageReq2VO pageVO) {
        PageResult<SkuPageRespVO> skuPage = skuService.getProductListByType(pageVO);
        return success(skuPage);
    }


}
