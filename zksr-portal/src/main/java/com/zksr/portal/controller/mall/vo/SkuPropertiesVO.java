package com.zksr.portal.controller.mall.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.annotation.Excel;
import com.zksr.common.core.web.CustomLongSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "商品SKU属性值的明细VO")
@Data
public class SkuPropertiesVO {

    @ApiModelProperty(value = "属性的编号", required = true, example = "1")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long propertyId;

    @ApiModelProperty(value = "属性的名称", required = true, example = "颜色")
    private String propertyName;

    @ApiModelProperty(value = "属性值的编号", required = true, example = "1024")
    @JsonSerialize(using= CustomLongSerialize.class)
    private Long propertyValId;

    @ApiModelProperty(value = "属性值的名称", required = true, example = "红色")
    private String valName;
}
