package com.zksr.portal.controller.mall.vo.yh;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import static com.zksr.common.core.utils.DateUtils.YYYY_MM_DD;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 8:50
 */
@Data
@ApiModel(description = "要货单结算统计")
public class YhBatchCountReqVO {

    @ApiModelProperty(value = "入驻商ID", hidden = true)
    private Long branchId;

    @ApiModelProperty(value = "入驻商ID")
    private Long supplierId;

    @DateTimeFormat(pattern = YYYY_MM_DD)
    @JsonFormat(pattern = YYYY_MM_DD, timezone = "Asia/Shanghai")
    @ApiModelProperty(value = "批次日期")
    private Date batchDate;
}
