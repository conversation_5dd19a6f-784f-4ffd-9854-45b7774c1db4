package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("saveOrder 保存订单-购物车对象")
public class OrderCarVO {

    /** 购物车id */
    @ApiModelProperty(value = "入驻商id")
    private Long carId;

    /** 入驻商上架商品id */
    @ApiModelProperty(value = "入驻商上架商品id")
    private Long supplierItemId;

    /** 城市上架商品id */
    private Long areaItemId;

    /** 商品SPU id */
    private Long spuId;

    /** 商品sku id */
    private Long skuId;

    /** 数量 */
    private Long qty;

}
