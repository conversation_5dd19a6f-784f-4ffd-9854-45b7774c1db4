package com.zksr.portal.controller.mall.vo.order;

import com.zksr.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分享订单请求 request - VO
 * @date 2024/10/29 15:28
 */
@Data
@ApiModel(description = "分享订单详情")
public class ShareOrderInfoReqVO {

    @ApiModelProperty(value = "分享key", required = true)
    @NotNull(message = "分享key不能为空")
    @NotEmpty(message = "分享key不能为空")
    private String shareKey;

    @Excel(name = "来源平台")
    @ApiModelProperty(value = "来源平台", required = true)
    private String sysSource;
}
