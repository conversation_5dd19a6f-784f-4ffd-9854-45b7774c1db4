package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024年05月18日 11:34
 * @description: 营销活动VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("营销活动VO")
public class ActivityVO {
    @ApiModelProperty(value = "活动id", required = true)
    private Long activityId;

    @ApiModelProperty(value = "活动规则id", required = true)
    private Long activityRuleId;

    @ApiModelProperty(value = "活动类型，字典{TrdDiscountType}", required = true)
    private String activityType;
}
