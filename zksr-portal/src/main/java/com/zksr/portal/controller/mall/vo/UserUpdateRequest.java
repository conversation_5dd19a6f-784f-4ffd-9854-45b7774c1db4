package com.zksr.portal.controller.mall.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户登陆 接口请求")
public class UserUpdateRequest {
    @ApiModelProperty(value = "用户Id 必填")
    @NotBlank(message = "用户Id不能为空")
    private Long memberId;

    @ApiModelProperty(value = "用户密码  必填")
    private String password;

    @ApiModelProperty(value = "sysSource  必填")
    @NotBlank(message = "sysSource不能为空")
    private String sysSource;
}
