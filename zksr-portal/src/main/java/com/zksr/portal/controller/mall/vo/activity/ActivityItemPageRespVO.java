package com.zksr.portal.controller.mall.vo.activity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@ApiModel(description = "根据活动id获取商品返回VO")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ActivityItemPageRespVO {

    @JsonSerialize(using = CustomLongSerialize.class)
    private Long activityId;

    private List<SkuPageRespVO> items;

    @JsonSerialize(using = CustomLongSerialize.class)
    private Long total;

    private String activityMemo;
}
