package com.zksr.portal.controller.mall.vo.pay;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 预支付成功, 小程序发起调用, 用于在支付回调之前拦截二次支付请求
 * @date 2024/8/19 9:06
 */
@Data
public class PayPreSuccessReqVO {

    @ApiModelProperty(value = "门店ID", required = true, hidden = true)
    private Long branchId;

    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;
}
