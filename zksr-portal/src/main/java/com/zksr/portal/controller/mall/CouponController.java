package com.zksr.portal.controller.mall;

import com.zksr.common.core.constant.HttpMethod;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.annotation.RequiresMallLogin;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.controller.mall.vo.coupon.*;
import com.zksr.portal.controller.mall.vo.prdt.CouponSkuSearchReqVO;
import com.zksr.portal.service.mall.ICouponService;
import com.zksr.portal.service.mall.ISkuService;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.promotion.api.coupon.vo.OrderAvailableRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券活动
 * @date 2024/4/1 15:19
 */
@Slf4j
@RestController
@RequestMapping("/mall/coupon")
@Api(tags = "商城 - 优惠券接口")
public class CouponController {

    @Autowired
    private ICouponService couponService;

    @Autowired
    private ISkuService skuService;

    @ApiOperation(value = "获取可领取优惠券 (作废)", httpMethod = HttpMethod.POST)
    @PostMapping("/validCouponList")
    @RequiresMallLogin(isMember = true)
    @Deprecated
    public CommonResult<CouponValidRespVO> validCouponList(@RequestBody CouponValidReqVO couponValidReq) {
        // return success(new CouponValidRespVO(couponService.getValidCouponList(couponValidReq)));
        // 没有针对全平台可用的优惠券
        return success(null);
    }

    @ApiOperation(value = "获取商品可领取优惠券", httpMethod = HttpMethod.POST)
    @PostMapping("/productValidCouponList")
    @RequiresMallLogin(isMember = true)
    public CommonResult<CouponSpuScopeValidRespVO> productValidCouponList(@Valid @RequestBody ProductValidCouponReqVO couponValidReq) {
        return success(new CouponSpuScopeValidRespVO(couponService.getProductValidCouponList(couponValidReq)));
    }

    @ApiOperation(value = "领取优惠券", httpMethod = HttpMethod.POST)
    @PostMapping("/receiveCoupon")
    @RequiresMallLogin(isMember = true)
    public CommonResult<CouponReceiveRespVO> receiveCoupon(@Valid @RequestBody CouponReceiveReqVO receiveReq) {
        return success(new CouponReceiveRespVO(couponService.saveReceiveCoupon(receiveReq)));
    }

    @ApiOperation(value = "领取优惠券", httpMethod = HttpMethod.POST)
    @PostMapping("/receiveCoupon2")
    public CommonResult<CouponReceiveRespVO> receiveCoupon2(@Valid @RequestBody CouponReceiveReqVO receiveReq) {
        return success(new CouponReceiveRespVO(couponService.saveReceiveCoupon2(receiveReq)));
    }

    @ApiOperation(value = "轮询优惠券领取结果", httpMethod = HttpMethod.POST)
    @PostMapping("/getReceiveCouponStatus")
    @RequiresMallLogin(isMember = true)
    public CommonResult<CouponReceiveResultRespVO> getReceiveCouponStatus(@Valid @RequestBody CouponReceiveResultReqVO receiveReq) {
        return success(new CouponReceiveResultRespVO(couponService.getReceiveCouponStatus(receiveReq)));
    }

    @ApiOperation(value = "获取我的优惠券列表", httpMethod = HttpMethod.POST)
    @PostMapping("/getCouponList")
    @RequiresMallLogin(isMember = true)
    public CommonResult<PageResult<CouponRecordVO>> getCouponList(@Valid @RequestBody CouponPageReqVO receiveReq) {
        return success(couponService.getCouponList(receiveReq));
    }

    @ApiOperation(value = "获取下单可使用优惠券", httpMethod = HttpMethod.POST)
    @PostMapping("/getOrderAvailable")
    @RequiresMallLogin(isMember = true)
    public CommonResult<OrderAvailableRespVO> getOrderAvailable(@RequestBody @Valid CouponOrderValidReqVO req) {
        req.setBranchId(MallSecurityUtils.getLoginMember().getBranchId());
        return success(couponService.getOrderAvailable(req));
    }

    @ApiOperation(value = "获取优惠券模版详情信息", httpMethod = HttpMethod.POST)
    @PostMapping("/getCouponTemplateDetail")
    @RequiresMallLogin
    public CommonResult<List<CouponTemplateDetailRespVO>> getCouponTemplateDetail(@RequestBody @Valid CouponTemplateDetailReqVO req) {
        return success(couponService.getCouponTemplateDetail(req));
    }

    @ApiOperation(value = "优惠券商品片段组", httpMethod = HttpMethod.POST)
    @PostMapping("/getCouponSpuFragmentList")
    @RequiresMallLogin
    public CommonResult<CouponTemplateFragmentRespVO> getCouponSpuFragmentList(@RequestBody @Valid CouponTemplateFragmentReqVO req) {
        return success(couponService.getCouponSpuFragmentList(req));
    }

    @ApiOperation(value = "优惠券商品列表", httpMethod = HttpMethod.POST)
    @PostMapping("/getCouponSpuList")
    @RequiresMallLogin
    public CommonResult<PageResult<SkuPageRespVO>> getCouponSpuList(@RequestBody @Valid CouponSkuSearchReqVO req) {
        return success(skuService.searchCouponSpuDetailList(req));
    }
}
