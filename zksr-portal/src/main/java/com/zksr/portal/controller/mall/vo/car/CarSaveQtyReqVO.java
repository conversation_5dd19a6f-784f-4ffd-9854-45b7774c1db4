package com.zksr.portal.controller.mall.vo.car;

import com.zksr.common.core.enums.ProductType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 更新购物车商品数量
 * @date 2024/3/26 16:51
 */
@Data
@ApiModel(description = "修改购物车商品数量VO")
public class CarSaveQtyReqVO {

    @ApiModelProperty(value = "购物车操作数量集合", required = true)
    @Size(min = 1, max = 999, message = "最小操作一条购物车数据")
    private List<QtyItem> carIdQty;

    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;

    @ApiModelProperty(value = "商品类型 local-本地商品, global-全国商品", required = true, example = "local")
    private String productType = ProductType.LOCAL.getType();

    @Data
    @ApiModel(description = "物车商品数量VO")
    public static class QtyItem {

        @ApiModelProperty(value = "购物车", required = true)
        private String carId;

        @ApiModelProperty(value = "数量最小0, 等于0会删除商品", required = true)
        @Min(0)
        private Integer qty;

        public QtyItem() {
        }

        public QtyItem(String carId, Integer qty) {
            this.carId = carId;
            this.qty = qty;
        }
    }
}
