package com.zksr.portal.controller.mall.vo.car;

import com.zksr.common.core.enums.ProductType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车选中合计数据
 * @date 2024/3/26 19:16
 */
@Data
@ApiModel(description = "购物车门店统计数据")
public class CarTotalReqVO {

    @ApiModelProperty(value = "门店ID", required = true)
    private Long branchId;
}
