package com.zksr.portal.controller.mall.vo.car;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zksr.common.core.web.CustomLongSerialize;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 购物车选中合计数据
 * @date 2024/3/26 19:16
 */
@Data
@ApiModel(description = "购物车门店选中数据")
public class CarCountRespVO {
    @ApiModelProperty("金额")
    private BigDecimal totalAmt;

    @ApiModelProperty("商品种类数量")
    private Integer productTypeNum;

    @ApiModelProperty("商品数量")
    private Integer productNum;

    @ApiModelProperty("入驻商数量")
    private Integer supplierNum;

    @ApiModelProperty("选中的carId")
    private List<CarSelectedItemVO> selectedCarId;

    @ApiModelProperty("促销活动")
    private List<Activity> activityList = new ArrayList<>();

    public CarCountRespVO() {
    }

    public CarCountRespVO(BigDecimal totalAmt, Integer productTypeNum, Integer productNum, Integer supplierNum, List<CarSelectedItemVO> selectedCarId) {
        this.totalAmt = totalAmt;
        this.productTypeNum = productTypeNum;
        this.productNum = productNum;
        this.supplierNum = supplierNum;
        this.selectedCarId = selectedCarId;
    }

    @Data
    @ApiModel(description = "促销活动列表")
    public static class Activity {

        @ApiModelProperty("促销ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long activityId;

        @ApiModelProperty("促销规则ID")
        @JsonSerialize(using = CustomLongSerialize.class)
        private Long activityRuleId;

        @ApiModelProperty("促销类型")
        private String activityType;
    }
}
