package com.zksr.portal.controller.mall.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 分享订单请求 request - VO
 * @date 2024/10/29 15:28
 */
@Data
@ApiModel(description = "分享订单请求")
public class ShareOrderReqVO {

    @ApiModelProperty(value = "订单ID", required = true)
    @NotNull(message = "订单ID必填")
    private Long orderId;

    @ApiModelProperty(value = "入驻商订单ID", required = true)
    @NotNull(message = "入驻商订单ID必填")
    private Long supplierOrderId;
}
