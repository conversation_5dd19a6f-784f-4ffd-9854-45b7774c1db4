package com.zksr.portal.convert.prdt;

import com.zksr.common.elasticsearch.domain.EsGlobalProduct;
import com.zksr.common.elasticsearch.domain.EsLocalProduct;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.domain.EsProductGroup;
import com.zksr.common.elasticsearch.model.dto.ProductSearchDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageReqVO;
import com.zksr.portal.controller.mall.vo.prdt.CouponSkuSearchReqVO;
import com.zksr.portal.controller.mall.vo.spu.SpuAreaItemRespVO;
import com.zksr.portal.controller.mall.vo.coupon.CouponValidItemVO;
import com.zksr.portal.controller.mall.vo.prdt.ActivitySkuSearchReqVO;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import com.zksr.product.api.combine.SpuCombineDtlDTO;
import com.zksr.product.api.content.dto.ProductContentDTO;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SkuUnitDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/24 14:28
 */
@Mapper
public interface ProductConvert {

    ProductConvert INSTANCE = Mappers.getMapper(ProductConvert.class);

    ProductContentPageReqDTO convertProductContentPageReqDTO(SkuSearchReqVO reqVO);

    ProductContentPageReqDTO convertProductContentPageReqDTO(ActivitySkuSearchReqVO reqVO);

    List<SkuDetailRespVO.SkuUnit> convertSkuUnitList(List<SkuUnitDTO> unitList);

    SpuAreaItemRespVO convertSpuAreaItemResp(ProductContentDTO contentDTO);

    SkuPageRespVO convertSpuPageResp(ProductContentDTO contentDTO);

    List<SkuPageRespVO.CouponLabelVO> convertCouponLabel(HashSet<CouponValidItemVO> result);

    ProductContentPageReqDTO convertProductContentPageReqDTO(SpuPageReqVO pageVO);

    @Mappings({
            @Mapping(source = "class3Id", target = "classId")
    })
    SkuPageRespVO convertSpuPageResp(EsProductGroup esProductGroup);

    List<SkuPageRespVO> convertSpuPageRespList(List<EsProductGroup> list);

    List<SkuPageRespVO> convertSpuPageRespList02(List<EsProduct> list);

    @Mappings({
            @Mapping(ignore = true, target = "latestDate"),
            @Mapping(ignore = true, target = "oldestDate"),
    })
    SpuDetailRespVO convertSpuDetail(SpuDTO spuDTO);


    @Mappings({
            @Mapping(source = "spu.thumb", target = "thumb"),
            @Mapping(source = "spu.spuName", target = "spuName"),
            @Mapping(ignore = true, target = "latestDate"),
            @Mapping(ignore = true, target = "oldestDate"),
            @Mapping(source = "sku.skuId", target = "skuId"),
            @Mapping(source = "sku.spuId", target = "spuId"),
            @Mapping(source = "sku.markPrice", target = "markPrice"),
            @Mapping(source = "sku.suggestPrice", target = "suggestPrice"),
            @Mapping(source = "combineDtl.qty", target = "qty"),
    })
    @BeanMapping(ignoreByDefault = true)
    SpuCombineSkuVO builderCombineSkuVO(SpuDTO spu, SkuDTO sku, SpuCombineDtlDTO combineDtl);

    ProductSearchDTO convertProductSearchDTO(SpuPageReqVO pageVO);

    ProductSearchDTO convertProductSearchDTO(ProductContentPageReqDTO pageReqDTO);

    @Mappings({
            @Mapping(source = "class3Id", target = "classId")
    })
    SkuPageRespVO convertSpuPageResp(EsProduct contentDTO);

    ActivitySkuSearchReqVO convertActivitySkuSearchReqVO(ProductContentPageReqDTO pageReqVO);

    EsPageInfo<EsProduct> convertEsProductPageByGlobal(EsPageInfo<EsGlobalProduct> esGlobalProductEsPageInfo);

    EsPageInfo<EsProduct> convertEsProductPageByLocal(EsPageInfo<EsLocalProduct> esLocalProductEsPageInfo);

    @Mappings({
            @Mapping(source = "settingPolicy.productDistributionLabel", target = "productDistributionLabel"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSetSupplier(@MappingTarget SkuPageRespVO product, SupplierOtherSettingPolicyDTO settingPolicy);

    ProductContentPageReqDTO convertProductContentPageReqDTO(CouponSkuSearchReqVO reqVO);
}
