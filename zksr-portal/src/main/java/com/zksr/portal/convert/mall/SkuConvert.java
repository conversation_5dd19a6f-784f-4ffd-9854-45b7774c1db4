package com.zksr.portal.convert.mall;


import com.zksr.portal.controller.mall.vo.*;
import com.zksr.trade.api.order.vo.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.*;

/**
 * SKU相关对象转换
 */
@Mapper
public interface SkuConvert {

    SkuConvert INSTANCE = Mappers.getMapper(SkuConvert.class);
    List<SkuPageRespVO> convert2SkuPageRespVO(List<StoreProductRespVO> data);
}
