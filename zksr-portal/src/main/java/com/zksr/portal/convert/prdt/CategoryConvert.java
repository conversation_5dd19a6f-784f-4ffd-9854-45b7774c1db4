package com.zksr.portal.convert.prdt;

import com.zksr.portal.controller.mall.vo.CategoryRespVO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/12/21
 * @desc
 */
@Mapper
public interface CategoryConvert {

    CategoryConvert INSTANCE = Mappers.getMapper(CategoryConvert.class);

    List<CategoryRespVO> converRespVOList(List<CatgoryDTO> catgoryList);
}
