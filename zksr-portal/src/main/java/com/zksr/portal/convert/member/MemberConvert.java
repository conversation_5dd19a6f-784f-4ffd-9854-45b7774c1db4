package com.zksr.portal.convert.member;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.portal.api.dto.BranchRegisterReqDTO;
import com.zksr.portal.api.dto.BranchRegisterRespDTO;
import com.zksr.portal.controller.mall.vo.LoginUserInfoVO;
import com.zksr.portal.controller.mall.vo.RegisterReq;
import com.zksr.portal.controller.mall.vo.RegisterResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/7/12
 * @desc
 */
@Mapper
public interface MemberConvert {

    MemberConvert INSTANCE = Mappers.getMapper(MemberConvert.class);

    LoginUserInfoVO.MemberVO convertMemberVO(MemberDTO member);

    LoginUserInfoVO.BranchVO convertBranchVO(BranchDTO branch);

    BranchRegisterRespDTO convert2BranchRegisterRespDTO(RegisterResp registerResp);

    RegisterResp convert2RegisterResp(BranchRegisterRespDTO branchRegisterRespDTO);

    BranchRegisterReqDTO convert2BranchRegisterReqDTO(RegisterReq registerReq);

    RegisterReq convert2RegisterReq(BranchRegisterReqDTO branchRegisterReqDTO);
}
