package com.zksr.portal.convert.car;

import com.zksr.common.core.domain.dto.car.AppCarStorageDTO;
import com.zksr.portal.controller.mall.vo.car.CarPageRespVO;
import com.zksr.portal.controller.mall.vo.car.CarPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.car.CarSaveBatchReqVO;
import com.zksr.portal.controller.mall.vo.car.CarSelectedItemVO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.trade.api.car.vo.CarSaveReqVO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/13
 * @desc
 */
@Mapper
public interface CarConvert {
    CarConvert INSTANCE = Mappers.getMapper(CarConvert.class);

    @Mappings({
            @Mapping(source = "supplier.supplierName", target = "supplierName"),
            @Mapping(source = "supplier.minAmt", target = "minAmt"),
            @Mapping(source = "supplier.globalMinAmt", target = "globalMinAmt"),
            @Mapping(source = "settingPolicy.productDistributionLabel", target = "productDistributionLabel"),
            @Mapping(source = "settingPolicy.shuttingStartTime", target = "shuttingStartTime"),
            @Mapping(source = "settingPolicy.shuttingEndTime", target = "shuttingEndTime"),
            @Mapping(source = "minAmtQualified", target = "minAmtQualified"),
            @Mapping(source = "debt", target = "debt")
    })
    @BeanMapping(ignoreByDefault = true)
    void convert(@MappingTarget CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO, SupplierDTO supplier, Boolean minAmtQualified, boolean debt, SupplierOtherSettingPolicyDTO settingPolicy);

    CarSaveReqVO convert(CarSaveBatchReqVO saveReqVO);

    AppCarIdDTO convert(CarSaveBatchReqVO.BatchItemDTO itemCarId);

    CarSelectedItemVO convert(AppCarStorageDTO storageDTO, Long branchId, String type);

    List<ActivityVerifyItemDTO> convertActivityVerifyItem(List<CarPageSupplierGroupItemVO> itemList);

    CarSelectedItemVO copayCarSelectedItem(CarSelectedItemVO selectedItem);

    AppCarIdDTO convert(AppCarStorageDTO storageDTO);
}
