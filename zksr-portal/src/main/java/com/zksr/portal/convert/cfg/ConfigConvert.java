package com.zksr.portal.convert.cfg;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.portal.controller.mall.vo.PartnerPolicyRespDTO;
import com.zksr.portal.controller.mall.vo.SupplierDetailVO;
import com.zksr.portal.controller.mall.vo.config.AppletAgreementPolicyRespVO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.PartnerMiniSettingPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Optional;

import static com.zksr.common.core.constant.UserConstants.DEFAULT_PASSWORD;

/**
 * <AUTHOR>
 * @time 2024/6/14
 * @desc
 */
@Mapper
public interface ConfigConvert {

    ConfigConvert INSTANCE = Mappers.getMapper(ConfigConvert.class);

    @Mappings({
            @Mapping(source = "appletAgreementPolicy.userAgreement", target = "userAgreement"),
            @Mapping(source = "appletAgreementPolicy.userPrivacyAgreement", target = "userPrivacyAgreement"),
            @Mapping(source = "appletAgreementPolicy.logo", target = "logo"),
            @Mapping(source = "appletAgreementPolicy.partnerName", target = "partnerName"),
            @Mapping(source = "appletAgreementPolicy.configName", target = "configName"),
            @Mapping(source = "appletAgreementPolicy.configType", target = "configType"),
            @Mapping(source = "appletAgreementPolicy.shopShareBgImgConfig", target = "shopShareBgImgConfig"),
            @Mapping(source = "appletBaseConfigDTO.publishAppId", target = "publishAppId"),
            @Mapping(source = "appletBaseConfigDTO.publishAccountName", target = "publishAccountName"),
            @Mapping(source = "appletBaseConfigDTO.appVersion", target = "appVersion"),
            @Mapping(source = "appletBaseConfigDTO.appVersionName", target = "appVersionName"),
            @Mapping(source = "appletBaseConfigDTO.appVersionContent", target = "appVersionContent"),
            @Mapping(source = "appletBaseConfigDTO.appUpdateUrl", target = "appUpdateUrl"),
            @Mapping(source = "appletBaseConfigDTO.forceUpdate", target = "forceUpdate"),
            @Mapping(source = "appletBaseConfigDTO.iosVersion", target = "iosVersion"),
            @Mapping(source = "appletBaseConfigDTO.iosVersionName", target = "iosVersionName"),
            @Mapping(source = "appletBaseConfigDTO.saleClassSwitch", target = "saleClassSwitch"),
            @Mapping(source = "appletBaseConfigDTO.zksrShowLabel",target = "zksrShowLabel"),
            @Mapping(source = "payConfigDTO.switchWalletPay", target = "switchWalletPay"),
            @Mapping(source = "payConfigDTO.switchStoreOrderPay", target = "switchStoreOrderPay"),
    })
    @BeanMapping(ignoreByDefault = true)
    AppletAgreementPolicyRespVO convertAppletAgreementPolicyDTO(AppletAgreementPolicyDTO appletAgreementPolicy, AppletBaseConfigDTO appletBaseConfigDTO, PayConfigDTO payConfigDTO);


    @Mappings({
            @Mapping(source = "partnerMiniSettingPolicyDTO.registerType", target = "registerType"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.openWechatMerchantAuth", target = "openWechatMerchantAuth"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.forceWechatMerchantAuth", target = "forceWechatMerchantAuth"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.openLevelTwoCategoryShow", target = "openLevelTwoCategoryShow"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.isShowCommission", target = "isShowCommission"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.localItemAfterType", target = "localItemAfterType"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.localItemAfterApproveType", target = "localItemAfterApproveType"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.defalutPassword", target = "defalutPassword"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.stockShowMode", target = "stockShowMode"),
            @Mapping(source = "partnerMiniSettingPolicyDTO.stockShowThreshold", target = "stockShowThreshold"),
    })
    @BeanMapping(ignoreByDefault = true)
    PartnerPolicyRespDTO convertPartnerPolicyDTO(PartnerMiniSettingPolicyDTO partnerMiniSettingPolicyDTO);

    default PartnerPolicyRespDTO convertPartnerPolicyDTO_0(PartnerMiniSettingPolicyDTO partnerMiniSettingPolicyDTO) {
        PartnerPolicyRespDTO partnerSettingPolicyDTO = convertPartnerPolicyDTO(partnerMiniSettingPolicyDTO);
        if (ToolUtil.isEmpty(partnerSettingPolicyDTO)) {
            partnerSettingPolicyDTO = new PartnerPolicyRespDTO();
        }
        // 是否开启余额管理/支付(0:关闭，1:开启，默认为关闭)
        String openBalancePay = partnerMiniSettingPolicyDTO.getOpenBalancePay();
        partnerSettingPolicyDTO.setOpenBalancePay(StringUtils.isBlank(openBalancePay) ? StringPool.ZERO : openBalancePay);
        partnerSettingPolicyDTO.setDefalutPassword(
                Optional.ofNullable(partnerSettingPolicyDTO.getDefalutPassword())
                        .map(defalutPassword -> {
                            return ToolUtil.isEmptyReturn(defalutPassword, DEFAULT_PASSWORD);
                        }).orElse(DEFAULT_PASSWORD)

        );
        return partnerSettingPolicyDTO;
    }

    @Mappings({
            @Mapping(source = "supplierDTO.supplierId", target = "supplierId"),
    })
    SupplierDetailVO buildSetSupplierDetailVO(SupplierDTO supplierDTO, SupplierOtherSettingPolicyDTO policyDTO);
}
