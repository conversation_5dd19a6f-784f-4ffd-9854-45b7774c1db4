package com.zksr.portal.convert.mall;

import com.zksr.account.api.account.vo.BranchBalanceRespVO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.enums.OrderPayWayEnum;
import com.zksr.common.core.enums.PayStateEnum;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.portal.controller.mall.OrderPayWayRespDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.activity.ActivityFgBgDetailVO;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import com.zksr.portal.controller.mall.vo.yh.YhBatchListRespVO;
import com.zksr.portal.controller.mall.vo.yh.YhCreateOrderRequest;
import com.zksr.portal.controller.mall.vo.yh.YhSettleOrderRequest;
import com.zksr.product.api.sku.dto.OrderSkuDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.promotion.utils.ActivityScopeUtil;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import com.zksr.trade.api.order.vo.TrdOrderDiscountDtlSaveVO;
import com.zksr.trade.api.order.vo.TrdOrderSaveVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderDtlSaveVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderSaveVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;

/**
 * 参考网址 ：https://www.iocoder.cn/Spring-Boot/MapStruct/
 * 密码：coke
 */
@Mapper
public interface TradeOrderConvert {

    TradeOrderConvert INSTANCE = Mappers.getMapper(TradeOrderConvert.class);

    default TradePriceCalculateRequest convert(BranchDTO branchDto, SettleOrderRequest request, List<AppCarIdDTO> carList, List<SupplierDTO> suppliers) {
        TradePriceCalculateRequest calculateRequest = new TradePriceCalculateRequest();
        calculateRequest.setBranchDto(branchDto)
                .setActivitys(request.getActivitys())
                .setCouponIds(request.getCouponIds());
        //设置请求计算价格方法的接口类型
        calculateRequest.setInterfaceType(request.getInterfaceType());
        calculateRequest.setItems(new ArrayList<>(request.getItems().size()));

        // 商品项的构建
        Map<String, AppCarIdDTO> cartMap = convertMap(carList, AppCarIdDTO::getId);
        Map<Long, SupplierDTO> supplierMap = convertMap(suppliers, SupplierDTO::getSupplierId);
        for (SettleOrderRequest.Item item : request.getItems()) {
            SupplierDTO supplier = supplierMap.get(item.getSupplierId());
            List<SettleOrderRequest.Item.SupplierItem> supplierItems = item.getSupplierItems();
            for (SettleOrderRequest.Item.SupplierItem supplierItem : supplierItems) {

                // 情况一：skuId + count
                if (supplierItem.getSkuId() != null) {
                    calculateRequest.getItems().add(new TradePriceCalculateRequest.Item().setSkuId(supplierItem.getSkuId()).setCount(supplierItem.getCount()).setIsActivitySpSk(supplierItem.getIsActivitySpSk())
                            .setAreaItemId(supplierItem.getAreaItemId()).setSupplierItemId(supplierItem.getSupplierItemId()).setSupplierId(item.getSupplierId())
                            .setCartId(supplierItem.getCarId()).setSupplierName(supplier.getSupplierName())
                            .setUnit(supplierItem.getUnit()).setUnitSize(supplierItem.getUnitSize())
                            .setGoodsType(supplierItem.getGoodsType()) // 商品类型  0：普通商品；1：组合商品
                            .setIsNegativeStock(supplier.getIsNegativeStock())
                            .setSelected(true)); // true 的原因，下单一定选中
                    continue;
                }
                // 情况二：cartId
                AppCarIdDTO cart = cartMap.get(supplierItem.getCarId());
                if (cart == null) {
                    continue;
                }
                calculateRequest.getItems().add(new TradePriceCalculateRequest.Item().setSkuId(cart.getSkuId()).setCount(supplierItem.getCount()).setIsActivitySpSk(supplierItem.getIsActivitySpSk())
                        .setAreaItemId(supplierItem.getAreaItemId()).setSupplierItemId(supplierItem.getSupplierItemId()).setSupplierId(item.getSupplierId())
                        .setCartId(cart.getId()).setSupplierName(supplier.getSupplierName())
                        .setUnit(supplierItem.getUnit()).setUnitSize(supplierItem.getUnitSize())
                        .setGoodsType(supplierItem.getGoodsType()) // 商品类型  0：普通商品；1：组合商品
                        .setIsNegativeStock(supplier.getIsNegativeStock())
                        .setSelected(true)); // true 的原因，下单一定选中
            }
        }
        return calculateRequest;
    }


    default SettleOrderResp convert(TradePriceCalculateResp resp, BranchDTO branch, ChannelDTO channelDTO, List<ActivityFgBgDetailVO> fgBgDetailVOList,
                                    PayConfigDTO payConfigDTO, BranchBalanceRespVO branchBalanceRespVO, BranchBalance branchBalance) {
        SettleOrderResp respVO = convert0(resp, branch, fgBgDetailVOList);
        respVO.setBranchBalance(branchBalance);
        respVO.setPayWays(new ArrayList<>());

        //获取订单支付方式枚举字典集合
        Set<OrderPayWayEnum> orderPayWayEnumSet = EnumSet.allOf(OrderPayWayEnum.class);

        // 本次下单商品是否存在全国商品
        boolean isGlobal = resp.getItems().stream().flatMap((supplierItem -> supplierItem.getSupplierItems().stream())).
                anyMatch(item -> Objects.equals(item.getItemType(), ProductType.GLOBAL.getCode()));

        // 业务员代客下单,且本次下单存在全国商品，不返回 支付方式且 是否下单字段为 0
        if (ToolUtil.isNotEmpty(MallSecurityUtils.getColonelId()) && isGlobal) {
            respVO.setIsOrderConfirm(NumberPool.LONG_ZERO);
        }
        // 业务员代客下单 ，默认直接返回 货到付款 支付方式
//        else if (ToolUtil.isNotEmpty(MallSecurityUtils.getColonelId())) {
//            respVO.getPayWays().add(
//                    PayWay.builder()
//                            .payWayCode(OrderPayWayEnum.HDFK.getPayWay())
//                            .payWayName(OrderPayWayEnum.HDFK.getName())
//                            .build()
//            );
//        }
        else {
            // 过滤当前下单的入驻商是否有不允许使用储值支付
            boolean isSwitchWalletPay = resp.getItems().stream().anyMatch(supplier -> Objects.equals(supplier.getSwitchWalletPay(), StringPool.ZERO));

            orderPayWayEnumSet.forEach(orderPayWay -> {
                // 货到付款方式验证
                if (orderPayWay.getPayWay().equals(OrderPayWayEnum.HDFK.getPayWay())) {
                    if (isGlobal)
                        return;
                    if (ToolUtil.isEmpty(branch.getHdfkSupport()) && (ToolUtil.isEmpty(channelDTO.getHdfkSupport()) || Objects.equals(channelDTO.getHdfkSupport(), StatusConstants.FLAG_FALSE)))
                        return;
                    if (Objects.equals(branch.getHdfkSupport(), StatusConstants.FLAG_FALSE))
                        return;
                }
                // 当订单中存在不允许储值支付的入驻商 || 平台不支持储值支付，|| 业务员代客下单 则不返回
                if (orderPayWay.getPayWay().equals(OrderPayWayEnum.WALLET.getPayWay()) &&
                        (isSwitchWalletPay || Objects.equals(ToolUtil.isEmptyReturn(payConfigDTO.getSwitchWalletPay(), StringPool.ZERO), StringPool.ZERO) || ToolUtil.isNotEmpty(MallSecurityUtils.getColonelId()))
                ) {
                    return;
                }
                // 当平台不允许在线支付，则不返回
                if (orderPayWay.getPayWay().equals(OrderPayWayEnum.ONLINE.getPayWay()) && ToolUtil.isNotEmpty(payConfigDTO) && Objects.equals(payConfigDTO.getSwitchStoreOrderPay(), StringPool.ZERO)) {
                    return;
                }
                respVO.getPayWays().add(
                        PayWay.builder()
                                .payWayCode(orderPayWay.getPayWay())
                                .payWayName(orderPayWay.getName())
                                .walletAmt(OrderPayWayEnum.isWallet(orderPayWay.getPayWay()) ? branchBalanceRespVO.getTotalBalanceAmt() : BigDecimal.ZERO)
                                .build()
                );
            });
        }
        return respVO;
    }

    SettleOrderResp convert0(TradePriceCalculateResp resp, BranchDTO address, List<ActivityFgBgDetailVO> fgBgDetailVOList);


    @Mappings({
            @Mapping(source = "branchDto.branchId", target = "branchId"),
            @Mapping(source = "branchDto.longitude", target = "longitude"),
            @Mapping(source = "branchDto.latitude", target = "latitude"),
            @Mapping(source = "branchDto.branchAddr", target = "branchAddr"),
            @Mapping(source = "branchDto.areaId", target = "areaId"),
            @Mapping(source = "resp.price.totalPrice", target = "orderAmt"),
            @Mapping(source = "resp.price.payPrice", target = "payAmt"),
            @Mapping(source = "resp.price.totalDiscountPrice", target = "discountAmt"),
            @Mapping(source = "colonel.colonelId", target = "colonelId"),
            @Mapping(source = "colonel.colonelLevel", target = "colonelLevel"),
            @Mapping(source = "pcolonel.colonelId", target = "pcolonelId"),
            @Mapping(source = "pcolonel.colonelLevel", target = "pcolonelLevel"),
            @Mapping(source = "request.memo", target = "memo"),
            @Mapping(source = "branchDto.sysCode", target = "sysCode"),
            @Mapping(source = "branchDto.dcId", target = "dcId"),
            @Mapping(source = "request.deliveryAddress", target = "deliveryAddress"),
            @Mapping(source = "request.distributionMode", target = "distributionMode"),
            @Mapping(source = "request.contactName", target = "contactName"),
            @Mapping(source = "request.contactPhone", target = "contactPhone"),
            @Mapping(source = "request.memberAddressId", target = "memberAddressId"),
            @Mapping(source = "request.memberInvoiceId", target = "memberInvoiceId"),
    })
    TrdOrderSaveVO convertOrder(BranchDTO branchDto, CreateOrderRequest request, TradePriceCalculateResp resp, ColonelDTO colonel, ColonelDTO pcolonel);

    /**
     * 订单主数据组装
     * @param branchDto 门店信息
     * @param request 请求订单数据信息
     * @param resp  处理完成的订单数据信息
     * @param colonel 业务员信息
     * @param pcolonel  管理业务员信息
     * @param areaDTO 区域信息
     * @param colonelFlag 业务员是否是本身
     * @param balanceRespVO 订单门店余额信息
     * @return
     */
    default TrdOrderSaveVO convertOrderDataAssemble(BranchDTO branchDto, CreateOrderRequest request, TradePriceCalculateResp resp, ColonelDTO colonel,
                                                    ColonelDTO pcolonel, AreaDTO areaDTO, Integer colonelFlag, BranchBalance balanceRespVO) {
        TrdOrderSaveVO orderSaveVO = convertOrder(branchDto, request, resp, colonel, pcolonel);
        orderSaveVO.setMemo(request.getMemo())
                .setSysCode(MallSecurityUtils.getLoginMember() == null ? branchDto.getSysCode() : MallSecurityUtils.getLoginMember().getSysCode())
                .setPayWay(request.getPayWay())
                .setDcId(areaDTO.getDcId())
                .setPayState(PayStateEnum.getNotPayState())
                .setOrderType(resp.getItems().get(0).getSupplierItems().get(0).getItemType()) // 订单类型：0 全国订单 1：本地订单 根据订单商品第一条数据来确定
                .setColonelFlag(colonelFlag)
                .setSource(request.getOrderSource())
                .setDeliveryAddress(request.getDeliveryAddress())
                .setDistributionMode(request.getDistributionMode())
                .setContactName(request.getContactName())
                .setContactPhone(request.getContactPhone())
                .setMemberAddressId(request.getMemberAddressId())
                .setMemberInvoiceId(request.getMemberInvoiceId())
                .setSource(request.getOrderSource())
                .setTotalPayAmt(orderSaveVO.getPayAmt())
                .setIsPayBalance(request.getIsPayBalance());
        // 去除优惠券和活动后的总支付金额,不会为空
        BigDecimal payAmt = orderSaveVO.getPayAmt();
        // 支付金额为0,
        if (payAmt.compareTo(BigDecimal.ZERO) == 0) {
            return orderSaveVO;
        }
        // 是否余额支付 0：否，1：是
        Integer isPayBalance = request.getIsPayBalance();
        if (Objects.isNull(isPayBalance) || isPayBalance.equals(0)) {
            return orderSaveVO;
        }
        // 门店余额金额
        BigDecimal balanceAmt = balanceRespVO.getBalanceAmt();
        if (Objects.isNull(balanceAmt) || balanceAmt.compareTo(BigDecimal.ZERO) == 0) {
            return orderSaveVO;
        }
        // 余额支付金额
        BigDecimal payBalanceAmt = BigDecimal.ZERO;
        // 若门店余额大于要支付金额，则全部用门店余额支付
        if (balanceAmt.compareTo(payAmt) >= 0) {
            payBalanceAmt = payBalanceAmt.add(payAmt);
            orderSaveVO.setPayAmt(BigDecimal.ZERO);
        } else {
            payBalanceAmt = payBalanceAmt.add(balanceAmt);
            orderSaveVO.setPayAmt(payAmt.subtract(balanceAmt));
        }
        orderSaveVO.setPayBalanceAmt(payBalanceAmt);
        return orderSaveVO;
    }

    @Mappings({
            @Mapping(source = "supplier.supplierId", target = "supplierId"),
            @Mapping(source = "supplier.supplierName", target = "supplierName"),
            @Mapping(source = "supplier.totalAmt", target = "subOrderAmt"),
            @Mapping(source = "supplier.payAmt", target = "subPayAmt"),
            @Mapping(source = "supplier.discountAmt", target = "subDiscountAmt"),
            @Mapping(source = "supplier.totalNum", target = "subOrderNum")
    })
    TrdSupplierOrderSaveVO convertSupplierOrder(TradePriceCalculateResp.OrderItem supplier);

    @Mappings({
            @Mapping(source = "supplier.supplierId", target = "supplierId"),
            @Mapping(source = "supplierItem.supplierItemId", target = "supplierItemId"),
            @Mapping(source = "supplierItem.areaItemId", target = "areaItemId"),
            @Mapping(source = "supplierItem.spuId", target = "spuId"),
            @Mapping(source = "supplierItem.skuId", target = "skuId"),
            @Mapping(source = "supplierItem.spuName", target = "spuName"),
            @Mapping(source = "supplierItem.picUrl", target = "thumb"),
            @Mapping(source = "supplierItem.videoUrl", target = "thumbVideo"),
            @Mapping(source = "supplierItem.count", target = "orderUnitQty"),
            @Mapping(source = "supplierItem.price", target = "price"),
            @Mapping(source = "supplierItem.minSalePrice", target = "salePrice"),
            @Mapping(source = "supplierItem.salePrice", target = "orderUnitPrice"),
            @Mapping(source = "supplierItem.unit", target = "orderUnit"),
            @Mapping(source = "supplierItem.unitSize", target = "orderUnitType"),
            @Mapping(source = "supplierItem.specName", target = "specName"),
            @Mapping(source = "supplierItem.itemType", target = "itemType"),
            @Mapping(source = "supplierItem.brandId", target = "brandId"),
            @Mapping(source = "supplierItem.commandId", target = "commandId")
    })
    TrdSupplierOrderDtlSaveVO convertSupplierOrderDtl(TradePriceCalculateResp.OrderItem supplier, TradePriceCalculateResp.OrderItem.SupplierItem supplierItem);

    @Mappings({
            @Mapping(source = "spuCombineSkuVO.supplierItemId", target = "supplierItemId"),
            @Mapping(source = "spuCombineSkuVO.areaItemId", target = "areaItemId"),
            @Mapping(source = "spuCombineSkuVO.spuId", target = "spuId"),
            @Mapping(source = "spuCombineSkuVO.skuId", target = "skuId"),
            @Mapping(source = "spuDTO.spuName", target = "spuName"),
            @Mapping(source = "spuDTO.thumb", target = "thumb"),
            @Mapping(source = "spuDTO.thumbVideo", target = "thumbVideo"),
            @Mapping(source = "spuDTO.images", target = "images"),
            @Mapping(source = "spuDTO.details", target = "details"),
            @Mapping(source = "spuCombineSkuVO.discountAmt", target = "activityDiscountAmt"),
            @Mapping(source = "spuCombineSkuVO.couponDiscountAmt1", target = "couponDiscountAmt"),
            @Mapping(source = "spuCombineSkuVO.couponDiscountAmt2", target = "couponDiscountAmt2"),
            @Mapping(source = "spuDTO.catgoryId", target = "categoryId"),
            @Mapping(source = "spuDTO.specName", target = "specName"),
            @Mapping(source = "spuCombineSkuVO.unit", target = "orderUnit"),
            @Mapping(source = "spuCombineSkuVO.skuUnitType", target = "orderUnitType"),
            @Mapping(source = "spuDTO.oldestDate", target = "oldestDate"),
            @Mapping(source = "spuDTO.latestDate", target = "latestDate"),
            @Mapping(source = "promotionItem.uuIdNo", target = "uuIdNo")
    })
    @BeanMapping(ignoreByDefault = true)
    TrdSupplierOrderDtlSaveVO convertCbSupplierOrderDtl(SpuCombineSkuVO spuCombineSkuVO, TradePriceCalculateResp.PromotionItem promotionItem, SpuDTO spuDTO);



    default List<OrderValidItemDTO> convertOrderValidItemList(List<TradePriceCalculateResp.OrderItem> suppliers) {
        List<OrderValidItemDTO> orderItemList = new ArrayList<>();
        suppliers.forEach(supplier -> {
            supplier.getSupplierItems().forEach(supplierItem -> {
                OrderValidItemDTO orderValidItemDTO = convertOrderValidItemDtlList(supplierItem, supplier);
                orderValidItemDTO.setAmt(orderValidItemDTO.getAmt().multiply(new BigDecimal(orderValidItemDTO.getNum()+"")));
                orderItemList.add(orderValidItemDTO);
            });
        });
        return orderItemList;
    }

    @Mappings({
            @Mapping(source = "supplierItems.count", target = "num"),
            @Mapping(source = "supplierItems.price", target = "amt")
    })
    OrderValidItemDTO convertOrderValidItemDtlList(TradePriceCalculateResp.OrderItem.SupplierItem supplierItems, TradePriceCalculateResp.OrderItem supplier);

    /**
     * 订单再次购买
     * @param orderResp
     * @return
     */
    default SettleOrderRequest convertOrderMorePurchase(TrdOrderRespDTO orderResp, List<OrderSkuDTO> resultOrderSkuDTOS) {
        Map<Long, OrderSkuDTO> orderSkuDTOMap = convertMap(resultOrderSkuDTOS, OrderSkuDTO::getSkuId);

        SettleOrderRequest request = new SettleOrderRequest();
        request.setMemberId(orderResp.getMemberId());
        request.setBranchId(MallSecurityUtils.getLoginMember().getBranchId());
        request.setDistributionMode(orderResp.getDistributionMode());
        request.setItems(new ArrayList<>());
        orderResp.getSupplierOrderList().forEach(supplierOrder -> {
            SettleOrderRequest.Item supplier = new SettleOrderRequest.Item();
            supplier.setSupplierId(supplierOrder.getSupplierId())
                    .setSupplierItems(new ArrayList<>());

            supplierOrder.getSupplierOrderDtlDTOList().forEach(supplierOrderDtl -> {
                if (!orderSkuDTOMap.containsKey(supplierOrderDtl.getSkuId())  || Objects.equals(supplierOrderDtl.getGiftFlag(), NumberPool.INT_ONE))
                    return;

                SettleOrderRequest.Item.SupplierItem supplierItem = new SettleOrderRequest.Item.SupplierItem();
                supplierItem.setSpuId(supplierOrderDtl.getSpuId())
                        .setSkuId(supplierOrderDtl.getSkuId())
                        .setSupplierItemId(supplierOrderDtl.getSupplierItemId())
                        .setAreaItemId(supplierOrderDtl.getAreaItemId())
                        .setCount(supplierOrderDtl.getTotalNum())
                        .setUnit(supplierOrderDtl.getOrderUnit())
                        .setUnitSize(supplierOrderDtl.getOrderUnitType().intValue())
                        ;
                String itemType = "";
                if (ProductType.LOCAL.getCode().equals(supplierOrderDtl.getItemType())) {
                    itemType = ProductType.LOCAL.getType();
                }
                if (ProductType.GLOBAL.getCode().equals(supplierOrderDtl.getItemType())) {
                    itemType = ProductType.GLOBAL.getType();
                }
                String carId = new AppCarIdDTO(itemType, MallSecurityUtils.getLoginMember().getBranchId(), supplier.getSupplierId(),
                        supplierItem.getSupplierItemId(), supplierItem.getAreaItemId(), supplierItem.getSkuId(), supplierItem.getSpuId(), supplierItem.getUnitSize()).getId();
                supplierItem.setCarId(carId);
                supplier.getSupplierItems().add(supplierItem);
            });

            if (supplier.getSupplierItems().size() <= 0)
                return;

            request.getItems().add(supplier);
        });
        return request;
    }

    @Mappings({
            @Mapping(source = "promotion.id", target = "discountId"),
            @Mapping(source = "promotion.type", target = "discountType"),
            @Mapping(source = "promotionItem.count", target = "spSkCount"),
            @Mapping(source = "promotionItem.unitSizeQty", target = "spSkUnitSizeQty"),
            @Mapping(source = "promotionItem.activityRuleId", target = "discountRuleId"),
            @Mapping(source = "promotionItem.couponTemplateId", target = "couponTemplateId"),
            @Mapping(source = "promotion.name", target = "discountName"),
            @Mapping(source = "promotionItem.unitSize", target = "spSkUnitSize"),
    })
    TrdOrderDiscountDtlSaveVO convertOrderDiscountDtl(TradePriceCalculateResp.Promotion promotion, TradePriceCalculateResp.PromotionItem promotionItem);


    /**
     * 订单明细构建商品赠品数据
     * @param
     * @return
     */
    default TrdSupplierOrderDtlSaveVO supplierDtlSaveAddItemGifts(TrdSupplierOrderDtlSaveVO trdSupplierOrderDtlSaveVO, TradePriceCalculateResp.PromotionItem promotionItem, SkuDTO skuDTO, SpuDTO spuDTO) {
        TrdSupplierOrderDtlSaveVO supplierOrderDtlSaveVO = supplierDtlSaveAddItemGifts1(trdSupplierOrderDtlSaveVO, promotionItem, skuDTO, spuDTO);
        supplierOrderDtlSaveVO.setSupplierItemId(null)
                .setAreaItemId(null)
                .setTotalAmt(BigDecimal.ZERO)
                .setPrice(BigDecimal.ZERO)
                .setSalePrice(BigDecimal.ZERO)
                .setGiftFlag(StatusConstants.FLAG_TRUE)
//        supplierOrderDtlSaveVO.setMemo("");
                .setCouponDiscountAmt(BigDecimal.ZERO)
                .setCouponDiscountAmt2(BigDecimal.ZERO)
                .setActivityDiscountAmt(promotionItem.getGiftSkuUnitPrice().multiply(BigDecimal.valueOf(promotionItem.getGiftQty())))
                .setDeliveryState(DeliveryStatusEnum.WAIT_PH.getCode())
                .setExactPrice(BigDecimal.ZERO)
                .setExactTotalAmt(BigDecimal.ZERO)
                .setSubOrderAmt(BigDecimal.ZERO)
                // 赠品赠品单位 大中小
                .setOrderUnitType(promotionItem.getGiftSkuUnitType())
                // 赠品 单位字典
                .setOrderUnit(spuDTO.getUnit(promotionItem.getGiftSkuUnitType()))
                // 单位换算数量
                .setOrderUnitSize(spuDTO.getUnitSizeQty(promotionItem.getGiftSkuUnitType()))
                // 单位赠送数量
                .setOrderUnitQty(promotionItem.getGiftQty().longValue())
                // 商品销售单位单价
                .setOrderUnitPrice(promotionItem.getGiftSkuUnitPrice())
                // 商品销售单位实际销售单价(减去促销之后的单价)
                .setOrderSalesUnitPrice(BigDecimal.ZERO)
                // 赠送最小单位数量
                .setTotalNum(BigDecimal.valueOf(supplierOrderDtlSaveVO.getOrderUnitQty()).multiply(supplierOrderDtlSaveVO.getOrderUnitSize()));

        // 最小单位原单价
        supplierOrderDtlSaveVO.setSalePrice(promotionItem.getGiftSkuUnitPrice().divide(supplierOrderDtlSaveVO.getOrderUnitSize(), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP))
                .setSubOrderAmt(supplierOrderDtlSaveVO.getActivityDiscountAmt())
                .setExactGiftSharePrice(BigDecimal.ZERO)
                .setAfterGiftSharePrice(supplierOrderDtlSaveVO.getSalePrice())
                .setAfterGiftShareSubtotalAmt(supplierOrderDtlSaveVO.getSubOrderAmt())
        ;


        return supplierOrderDtlSaveVO;
    }

    @Mappings({
            @Mapping(source = "skuDTO.skuId", target = "skuId"),
            @Mapping(source = "spuDTO.spuId", target = "spuId"),
            @Mapping(source = "spuDTO.spuName", target = "spuName"),
            @Mapping(source = "spuDTO.thumb", target = "thumb"),
            @Mapping(source = "spuDTO.thumbVideo", target = "thumbVideo"),
            @Mapping(source = "spuDTO.images", target = "images"),
            @Mapping(source = "spuDTO.details", target = "details"),
            @Mapping(source = "spuDTO.specName", target = "specName"),
            @Mapping(source = "spuDTO.supplierId", target = "supplierId"),
            @Mapping(source = "promotionItem.activityDiscountAmt", target = "activityDiscountAmt"),
            @Mapping(source = "promotionItem.couponDiscountAmt", target = "couponDiscountAmt"),
            @Mapping(source = "promotionItem.couponDiscountAmt2", target = "couponDiscountAmt2"),
            @Mapping(source = "trdSupplierOrderDtlSaveVO.itemType", target = "itemType"),
            @Mapping(source = "promotionItem.uuIdNo", target = "uuIdNo"),
            @Mapping(source = "trdSupplierOrderDtlSaveVO.memo", target = "memo"),
            @Mapping(source = "spuDTO.brandId", target = "brandId"),
            @Mapping(source = "trdSupplierOrderDtlSaveVO.oldestDate", target = "oldestDate"),
            @Mapping(source = "trdSupplierOrderDtlSaveVO.latestDate", target = "latestDate")
    })
    TrdSupplierOrderDtlSaveVO supplierDtlSaveAddItemGifts1(TrdSupplierOrderDtlSaveVO trdSupplierOrderDtlSaveVO, TradePriceCalculateResp.PromotionItem promotionItem, SkuDTO skuDTO, SpuDTO spuDTO);

    ActivityScopeUtil.ActivitySpuScopeValidVO convertActivityValidVO(TradePriceCalculateResp.OrderItem.SupplierItem item);

    /**
     * Pos补货订单预下单数据转换
     * @param itemVOS
     * @return
     */
    default CreateOrderRequest convertOrderSettlePosYhData(YhSettleOrderRequest yhRequest, YhBatchListRespVO itemVOS) {
        CreateOrderRequest request = new CreateOrderRequest();
        request.setMemberId(yhRequest.getMemberId());
        request.setBranchId(yhRequest.getBranchId());
        request.setItems(new ArrayList<>());
        request.setActivitys(yhRequest.getActivitys());
        request.setCouponIds(yhRequest.getCouponIds());
        // 补货数据根据入驻商分组
        Map<Long, List<YhPageSupplierGroupItemVO>> supplierYhMap = itemVOS.getItemList().stream().collect(Collectors.groupingBy(YhPageSupplierGroupItemVO::getSupplierId));

        supplierYhMap.forEach((supplierId, itemVO) -> {
            SettleOrderRequest.Item supplierOrder = new SettleOrderRequest.Item();
            supplierOrder.setSupplierId(supplierId);
            supplierOrder.setSupplierItems(new ArrayList<>());

            itemVO.forEach(item -> {
                SettleOrderRequest.Item.SupplierItem supplierItem = new SettleOrderRequest.Item.SupplierItem();
                supplierItem.setSpuId(item.getSpuId())
                        .setSkuId(item.getSkuId())
                        .setSupplierItemId(null) // 默认为空，补货只会补本地上架商品
                        .setAreaItemId(item.getItemId())
                        .setCount((long) (item.getProductNum() - ToolUtil.isEmptyReturn(item.getActivityNum(), NumberPool.INT_ZERO)))
                        .setIsActivitySpSk(NumberPool.INT_ZERO) // 默认不参与秒杀或特价活动
                        .setUnit(item.getUnit())
                        .setUnitSize(item.getUnitSize())
                        .setGoodsType(item.getItemType()) // 商品类型：0：普通，1：组合
                        ;

                // 这个购物车ID需要生成，但实际上用处不大，防止获取购物车验证报错
                String carId = new AppCarIdDTO(ProductType.LOCAL.getType(), MallSecurityUtils.getLoginMember().getBranchId(), supplierOrder.getSupplierId(),
                        supplierItem.getSupplierItemId(), supplierItem.getAreaItemId(), supplierItem.getSkuId(), supplierItem.getSpuId(), supplierItem.getUnitSize()).getId();
                supplierItem.setCarId(carId);
                supplierOrder.getSupplierItems().add(supplierItem);
                // 商品有秒杀、特价活动，则添加活动商品
                if (ToolUtil.isNotEmpty(item.getActivityNum()) && item.getActivityNum() > 0) {
                    if (item.getProductNum() > item.getActivityNum()) {
                        // 将原数据复制到活动数据
                        SettleOrderRequest.Item.SupplierItem activitySupplierItem = convertSettleOrderRequestItemVo(supplierItem);
                        activitySupplierItem.setCount(Long.valueOf(item.getActivityNum())) // 参与活动数量
                                .setIsActivitySpSk(NumberPool.INT_ONE) // 默认参与秒杀或特价活动
                        ;
                        supplierOrder.getSupplierItems().add(activitySupplierItem);
                    } else {
                        supplierItem.setCount(Long.valueOf(item.getProductNum()))
                                .setIsActivitySpSk(NumberPool.INT_ONE) // 默认参与秒杀或特价活动
                        ;
                    }
                }
            });
            request.getItems().add(supplierOrder);
        });
        return request;
    }

    /**
     * Pos补货订单创建订单数据转换
     * @param itemVOS
     * @return
     */
    default CreateOrderRequest convertOrderCreatePosYhData(YhCreateOrderRequest yhRequest, YhBatchListRespVO itemVOS) {
        CreateOrderRequest request = convertOrderSettlePosYhData(yhRequest, itemVOS);
        request.setMemo(yhRequest.getMemo());
        request.setPayWay(yhRequest.getPayWay());
        request.setOrderSource(yhRequest.getOrderSource());
        return request;
    }

    SettleOrderRequest.Item.SupplierItem convertSettleOrderRequestItemVo(SettleOrderRequest.Item.SupplierItem item);


    default void convertOrderDtlPrice(TrdSupplierOrderDtlSaveVO dtlVo) {
        BigDecimal discountAmt = dtlVo.getCouponDiscountAmt().add(dtlVo.getCouponDiscountAmt2()).add(dtlVo.getActivityDiscountAmt()); // 优惠总金额
        BigDecimal saleTotalAmt = dtlVo.getSubOrderAmt();  // 现商品下单单位原销售价总金额

        // 现商品下单最小单位数量
        BigDecimal minSaleQty = BigDecimal.valueOf(dtlVo.getOrderUnitQty()).multiply(dtlVo.getOrderUnitSize());
        //现下单单位销售总金额 = 现商品下单单位原销售价总金额 - 优惠总金额
        dtlVo.setTotalAmt(saleTotalAmt.subtract(discountAmt))
                .setTotalNum(minSaleQty)
                .setExactPrice(dtlVo.getTotalAmt().divide(dtlVo.getTotalNum(), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP)) // 商品销售精准单价（保留6位小数） 现销售总金额 / 商品数量
                .setExactTotalAmt(dtlVo.getExactPrice().multiply(dtlVo.getTotalNum().setScale(StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP)))
                .setPrice(dtlVo.getExactPrice().setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP)) // 商品销售单价（保留2位小数） = 商品销售精准单价（保留6位小数）四舍五入
                .setOrderSalesUnitPrice(dtlVo.getTotalAmt().divide(BigDecimal.valueOf(dtlVo.getOrderUnitQty()), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP)) // 商品销售单位实际销售单价（保留6位小数） = 商品销售总金额 / 商品下单单位数量
                .setExactGiftSharePrice(BigDecimal.ZERO)
                .setAfterGiftSharePrice(dtlVo.getExactPrice())
                .setAfterGiftShareUnitPrice(dtlVo.getOrderSalesUnitPrice())
                .setAfterGiftShareSubtotalAmt(dtlVo.getTotalAmt())
        ;
    }

    default OrderPayWayRespDTO convertOrderPayWayResp(TrdOrderRespDTO orderResp, BranchDTO branch, ChannelDTO channel,
                                                      PayConfigDTO payConfigDTO, BranchBalanceRespVO branchBalanceRespVO) {
        BigDecimal payAmt = orderResp.getPayAmt();
        String orderInfoTip;
        if (Objects.nonNull(orderResp.getPayBalanceAmt()) && orderResp.getPayBalanceAmt().compareTo(BigDecimal.ZERO) > 0) {
            payAmt = payAmt.subtract(orderResp.getPayBalanceAmt());
            orderInfoTip = StringUtils.format("订单金额：￥{}元，余额支付￥{}元，已优惠￥{}元", orderResp.getOrderAmt(), orderResp.getPayBalanceAmt().setScale(2, RoundingMode.HALF_UP), orderResp.getDiscountAmt());
        } else {
            orderInfoTip = StringUtils.format("订单金额：￥{}元，已优惠￥{}元", orderResp.getOrderAmt(), orderResp.getDiscountAmt());
        }
        OrderPayWayRespDTO respVO = OrderPayWayRespDTO.builder().payAmt(payAmt).orderInfoTip(orderInfoTip)
                .payWays(new ArrayList<>())
                .build();

        //获取订单支付方式枚举字典集合
        Set<OrderPayWayEnum> orderPayWayEnumSet = EnumSet.allOf(OrderPayWayEnum.class);

        // 本次下单商品是否存在全国商品
        boolean isGlobal = Objects.equals(orderResp.getOrderType(), ProductType.GLOBAL.getCode());

        // 业务员代客下单 ，默认直接返回 货到付款 支付方式
//        if (ToolUtil.isNotEmpty(MallSecurityUtils.getColonelId())) {
//            respVO.getPayWays().add(
//                    PayWay.builder()
//                            .payWayCode(OrderPayWayEnum.HDFK.getPayWay())
//                            .payWayName(OrderPayWayEnum.HDFK.getName())
//                            .build()
//            );
//        } else {
            // 过滤当前下单的入驻商是否有不允许使用储值支付
            boolean isSwitchWalletPay = orderResp.getSupplierOrderList().stream().anyMatch(supplier -> Objects.equals(supplier.getSwitchWalletPay(), StringPool.ZERO));

            orderPayWayEnumSet.forEach(orderPayWay -> {
                // 货到付款方式验证
                if (orderPayWay.getPayWay().equals(OrderPayWayEnum.HDFK.getPayWay())) {
                    if (isGlobal)
                        return;
                    if (ToolUtil.isEmpty(branch.getHdfkSupport()) && (ToolUtil.isEmpty(channel.getHdfkSupport()) || Objects.equals(channel.getHdfkSupport(), StatusConstants.FLAG_FALSE)))
                        return;
                    if (Objects.equals(branch.getHdfkSupport(), StatusConstants.FLAG_FALSE))
                        return;
                }
                // 当订单中存在不允许储值支付的入驻商 || 平台不支持储值支付 ，则不返回
                if (orderPayWay.getPayWay().equals(OrderPayWayEnum.WALLET.getPayWay()) &&
                        (isSwitchWalletPay || Objects.equals(ToolUtil.isEmptyReturn(payConfigDTO.getSwitchWalletPay(), StringPool.ZERO), StringPool.ZERO) || ToolUtil.isNotEmpty(MallSecurityUtils.getColonelId()))
                ) {
                    return;
                }
                // 当平台不允许在线支付，则不返回
                if (orderPayWay.getPayWay().equals(OrderPayWayEnum.ONLINE.getPayWay()) && ToolUtil.isNotEmpty(payConfigDTO) && Objects.equals(payConfigDTO.getSwitchStoreOrderPay(), StringPool.ZERO)) {
                    return;
                }
                respVO.getPayWays().add(
                        PayWay.builder()
                                .payWayCode(orderPayWay.getPayWay())
                                .payWayName(orderPayWay.getName())
                                .walletAmt(OrderPayWayEnum.isWallet(orderPayWay.getPayWay()) ? branchBalanceRespVO.getTotalBalanceAmt() : BigDecimal.ZERO)
                                .build()
                );
            });
//        }
        return respVO;
    }

}
