package com.zksr.portal.convert.activity;

import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityDetailRespVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityFgBgDetailVO;
import com.zksr.portal.controller.mall.vo.car.CarPageRespVO;
import com.zksr.portal.controller.mall.vo.car.CarPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.car.CarSelectedItemVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageRespVO;
import com.zksr.portal.controller.mall.vo.car.CarSpuActivityLabelVO;
import com.zksr.portal.controller.mall.vo.spu.SpuActivityLabelVO;
import com.zksr.portal.controller.mall.vo.spu.SpuDetailActivityLabelVO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2024/5/21
 * @desc
 */
@Mapper
public interface ActivityConvert {

    ActivityConvert INSTANCE = Mappers.getMapper(ActivityConvert.class);

    default List<ActivityLabelInfoVO> convertActivityDTO(List<ActivityLabelInfoVO> adequateList) {
        for (ActivityLabelInfoVO infoVO : adequateList) {
            infoVO.setActivity(INSTANCE.convertActivityDTO((SupplierActivityDTO) infoVO.getActivity()));
        }
        return adequateList;
    }

    ActivityDTO convertActivityDTO(SupplierActivityDTO activity);

    @Mappings({
            @Mapping(source = "validItem.spuId", target = "spuId"),
            @Mapping(source = "validItem.skuId", target = "skuId"),
            @Mapping(source = "validItem.supplierId", target = "supplierId"),
            @Mapping(source = "validItem.brandId", target = "brandId"),
            @Mapping(source = "validItem.unitSize", target = "unitSize"),
            @Mapping(source = "validItem.unit", target = "unit"),
            @Mapping(source = "spuDTO.catgoryId", target = "categoryId"),
            @Mapping(source = "spuDTO.midSize", target = "midSize"),
            @Mapping(source = "spuDTO.largeSize", target = "largeSize"),
            @Mapping(source = "validItem.num", target = "num"),
            @Mapping(source = "salePrice", target = "itemPrice"),
            @Mapping(source = "appCarIdDTO.type", target = "type"),
    })
    @BeanMapping(ignoreByDefault = true)
    ActivityVerifyItemDTO convertActivityDTO(OrderValidItemDTO validItem, AppCarIdDTO appCarIdDTO, SpuDTO spuDTO, BigDecimal salePrice);

    @Mappings({
            @Mapping(source = "validItem.spuId", target = "spuId"),
            @Mapping(source = "validItem.skuId", target = "skuId"),
            @Mapping(source = "validItem.supplierId", target = "supplierId"),
            @Mapping(source = "validItem.brandId", target = "brandId"),
            @Mapping(source = "validItem.unitSize", target = "unitSize"),
            @Mapping(source = "validItem.unit", target = "unit"),
            @Mapping(source = "validItem.categoryId", target = "categoryId"),
            @Mapping(source = "validItem.num", target = "num"),
            @Mapping(source = "validItem.productType", target = "type"),
            @Mapping(source = "salePrice", target = "itemPrice"),
    })
    @BeanMapping(ignoreByDefault = true)
    ActivityVerifyItemDTO convertActivityDTO(OrderValidItemDTO validItem, BigDecimal salePrice);

    default SpuActivityLabelVO convertSpuActivityLabelVO(SupplierActivityDTO supplierActivityDTO) {
        SpuActivityLabelVO activityLabelVO = new SpuActivityLabelVO();
        activityLabelVO.setActivityId(supplierActivityDTO.getActivityId());
        activityLabelVO.setSpuScope(supplierActivityDTO.getSpuScope());
        activityLabelVO.setPrmNo(supplierActivityDTO.getPrmNo());
        activityLabelVO.setAmtOrQty(supplierActivityDTO.getAmtOrQty());
        if (PrmNoEnum.isBg(supplierActivityDTO.getPrmNo())) {
            activityLabelVO.setRules(new ArrayList<>(supplierActivityDTO.getBgRules()));
        }
        if (PrmNoEnum.isFg(supplierActivityDTO.getPrmNo())) {
            activityLabelVO.setRules(new ArrayList<>(supplierActivityDTO.getFgRules()));
        }
        if (PrmNoEnum.isFd(supplierActivityDTO.getPrmNo())) {
            activityLabelVO.setRules(new ArrayList<>(supplierActivityDTO.getFdRules()));
        }
        return activityLabelVO;
    }

    List<SpuActivityLabelVO> convertSpuListActivity(List<SupplierActivityDTO> supplierActivityDTOS);

    SpuDetailActivityLabelVO convertSpuDetailActivityVO(SupplierActivityDTO supplierActivityDTO);


    @Mappings({
            @Mapping(source = "cityScopes", target = "cityScopeList"),
            @Mapping(source = "channelScopes", target = "channelScopeList"),
            @Mapping(source = "branchScopes", target = "branchScopeList")
    })
    SupplierActivityDTO convert(PrmActivityDTO activityDTO);

    @Mappings({
            @Mapping(source = "rule.bgRuleId", target = "ruleId"),
            @Mapping(source = "rule.ruleQty", target = "fullValue"),
    })
    ActivityFgBgDetailVO.Gift convertGiftRule(BgRuleDTO rule);

    List<ActivityFgBgDetailVO.Gift> convertBgGiftRule(Set<BgRuleDTO> ruleList);

    @Mappings({
            @Mapping(source = "rule.fgRuleId", target = "ruleId"),
            @Mapping(source = "rule.fullAmt", target = "fullValue"),
    })
    ActivityFgBgDetailVO.Gift convertGiftRule(FgRuleDTO rule);

    ActivityFgBgDetailVO.Gift convertGiftRule(GiveRuleDTO ruleDTO);

    List<ActivityFgBgDetailVO.Gift> convertFgGiftRule(Set<FgRuleDTO> ruleList);

    ActivityFgBgDetailVO convertActivityFgBgDetailVO(PrmActivityDTO activityDto);

    default CarSpuActivityLabelVO convertCarSpuListActivity(SupplierActivityDTO supplierActivityDTO) {
        CarSpuActivityLabelVO activityLabelVO = new CarSpuActivityLabelVO();
        activityLabelVO.setActivityId(supplierActivityDTO.getActivityId());
        activityLabelVO.setSpuScope(supplierActivityDTO.getSpuScope());
        activityLabelVO.setPrmNo(supplierActivityDTO.getPrmNo());
        activityLabelVO.setAmtOrQty(supplierActivityDTO.getAmtOrQty());

        if (PrmNoEnum.isBg(supplierActivityDTO.getPrmNo())) {
            activityLabelVO.setRules(new ArrayList<>(supplierActivityDTO.getBgRules()));
        }
        if (PrmNoEnum.isFg(supplierActivityDTO.getPrmNo())) {
            activityLabelVO.setRules(new ArrayList<>(supplierActivityDTO.getFgRules()));
        }
        if (PrmNoEnum.isFd(supplierActivityDTO.getPrmNo())) {
            activityLabelVO.setRules(new ArrayList<>(supplierActivityDTO.getFdRules()));
        }
        return activityLabelVO;
    }

    List<CarSpuActivityLabelVO> convertCarSpuListActivityList(List<SupplierActivityDTO> supplierActivityDTOS);


    @Mappings({
            @Mapping(source = "carId.type", target = "type"),
            @Mapping(source = "carId.spuId", target = "spuId"),
            @Mapping(source = "carId.skuId", target = "skuId"),
            @Mapping(expression = "java(carId.itemId())", target = "itemId"),
            @Mapping(source = "spu.supplierId", target = "supplierId"),
            @Mapping(source = "spu.catgoryId", target = "categoryId"),
            @Mapping(source = "spu.brandId", target = "brandId"),
            @Mapping(source = "spu.midSize", target = "midSize"),
            @Mapping(source = "spu.largeSize", target = "largeSize"),
            @Mapping(source = "validItem.productNum", target = "num"),
            @Mapping(source = "validItem.markPrice", target = "itemPrice"),
            @Mapping(source = "validItem.selected", target = "selected"),
            @Mapping(source = "validItem.unit", target = "unit"),
            @Mapping(source = "validItem.unitSize", target = "unitSize"),
            @Mapping(source = "validItem.stockConvertRate", target = "stockConvertRate"),
            @Mapping(source = "validItem.addTime", target = "carTime"),
    })
    @BeanMapping(ignoreByDefault = true)
    ActivityVerifyItemDTO buildVerifyItemDTO(AppCarIdDTO carId, SpuDTO spu, CarPageSupplierGroupItemVO validItem);

    @Mappings({
            @Mapping(source = "carId.type", target = "type"),
            @Mapping(source = "carId.spuId", target = "spuId"),
            @Mapping(source = "carId.skuId", target = "skuId"),
            @Mapping(expression = "java(carId.itemId())", target = "itemId"),
            @Mapping(source = "supplierGroupVO.supplierId", target = "supplierId"),
            @Mapping(source = "validItem.productNum", target = "num"),
            @Mapping(source = "validItem.markPrice", target = "itemPrice"),
            @Mapping(source = "validItem.selected", target = "selected"),
            @Mapping(source = "validItem.unit", target = "unit"),
            @Mapping(source = "validItem.unitSize", target = "unitSize"),
            @Mapping(source = "validItem.stockConvertRate", target = "stockConvertRate"),
            @Mapping(source = "validItem.addTime", target = "carTime"),
            @Mapping(source = "spuCombine.categoryId", target = "categoryId"),
    })
    @BeanMapping(ignoreByDefault = true)
    ActivityVerifyItemDTO buildVerifyItemDTO(AppCarIdDTO carId, CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO, CarPageSupplierGroupItemVO validItem, SpuCombineDTO spuCombine);

    @Mappings({
            @Mapping(source = "item.type", target = "type"),
            @Mapping(source = "item.spuId", target = "spuId"),
            @Mapping(source = "item.skuId", target = "skuId"),
            @Mapping(source = "item.productNum", target = "num"),
            @Mapping(source = "item.salePrice", target = "itemPrice"),
            @Mapping(source = "item.unit", target = "unit"),
            @Mapping(source = "item.unitSize", target = "unitSize"),
            @Mapping(source = "item.addTime", target = "carTime"),
            @Mapping(expression = "java(item.itemId())", target = "itemId"),
            @Mapping(source = "spu.supplierId", target = "supplierId"),
            @Mapping(source = "spu.catgoryId", target = "categoryId"),
            @Mapping(source = "spu.brandId", target = "brandId"),
            @Mapping(source = "spu.midSize", target = "midSize"),
            @Mapping(source = "spu.largeSize", target = "largeSize"),
    })
    @BeanMapping(ignoreByDefault = true)
    ActivityVerifyItemDTO buildVerifyItemDTO(CarSelectedItemVO item, SpuDTO spu);

    @Mappings({
            @Mapping(source = "item.type", target = "type"),
            @Mapping(source = "item.spuId", target = "spuId"),
            @Mapping(source = "item.skuId", target = "skuId"),
            @Mapping(source = "item.productNum", target = "num"),
            @Mapping(source = "item.salePrice", target = "itemPrice"),
            @Mapping(source = "item.unit", target = "unit"),
            @Mapping(source = "item.unitSize", target = "unitSize"),
            @Mapping(source = "item.addTime", target = "carTime"),
            @Mapping(expression = "java(item.itemId())", target = "itemId"),
            @Mapping(source = "item.supplierId", target = "supplierId"),
            @Mapping(source = "spuCombine.categoryId", target = "categoryId"),
    })
    @BeanMapping(ignoreByDefault = true)
    ActivityVerifyItemDTO buildVerifyItemDTO(CarSelectedItemVO item, SpuCombineDTO spuCombine);

    @Mappings({
            @Mapping(source = "skuPageVO.catgoryId", target = "categoryId")
    })
    ActivityVerifyItemDTO convertVerifyItemListBySpuCombine(SkuPageRespVO skuPageVO);

    List<ActivityVerifyItemDTO> convertVerifyItemListBySpuCombine(List<SkuPageRespVO> combineProductList);

    @Mappings({
            @Mapping(source = "spuCombine.categoryId", target = "categoryId"),
            @Mapping(source = "spuCombine.supplierId", target = "supplierId"),
            @Mapping(expression = "java(com.zksr.common.core.pool.NumberPool.LOWER_GROUND_LONG)", target = "spuId"),
            @Mapping(expression = "java(com.zksr.common.core.pool.NumberPool.LOWER_GROUND_LONG)", target = "skuId"),
            @Mapping(expression = "java(com.zksr.common.core.pool.NumberPool.LOWER_GROUND_LONG)", target = "brandId"),
            @Mapping(expression = "java(com.zksr.common.core.pool.NumberPool.INT_ONE)", target = "unitSize"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSetOrderValid(@MappingTarget OrderValidItemDTO orderValid, SpuCombineDTO spuCombine);

    @Mappings({
            @Mapping(source = "spu.spuId", target = "spuId"),
            @Mapping(source = "spu.brandId", target = "brandId"),
            @Mapping(source = "spu.catgoryId", target = "categoryId"),
            @Mapping(source = "spu.supplierId", target = "supplierId"),
            @Mapping(source = "carId.type", target = "productType"),
            @Mapping(source = "carId.unitSize", target = "unitSize"),
            @Mapping(source = "carId.skuId", target = "skuId"),
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSetOrderValid(@MappingTarget OrderValidItemDTO item, SpuDTO spu, AppCarIdDTO carId);

    ActivityItemPageRespVO convertActivityItem(List<SkuPageRespVO> items, Long activityId, Long total);

    SupplierActivityDTO convertSupplierActivityDTO(PrmActivityDTO activityDto);

    ActivityDetailRespVO convertRespVO(PrmActivityDTO activityDTO);
}
