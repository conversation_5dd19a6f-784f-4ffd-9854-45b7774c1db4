package com.zksr.portal.convert.pay;

import com.zksr.account.model.pay.vo.PayOrderSubmitReqVO;
import com.zksr.portal.controller.mall.vo.order.PotalHdfkPayOrderSubmitReqVO;
import com.zksr.portal.controller.mall.vo.order.PotalPayOrderSubmitReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/5/29
 * @desc
 */
@Mapper
public interface PayConvert {

    PayConvert INSTANCE = Mappers.getMapper(PayConvert.class);

    PayOrderSubmitReqVO convert(PotalPayOrderSubmitReqVO payOrderSubmitReqVO);

    PayOrderSubmitReqVO convert(PotalHdfkPayOrderSubmitReqVO payOrderSubmitReqVO);
}
