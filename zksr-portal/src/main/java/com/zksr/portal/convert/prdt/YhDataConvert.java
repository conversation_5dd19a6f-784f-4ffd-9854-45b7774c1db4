package com.zksr.portal.convert.prdt;

import com.zksr.common.elasticsearch.model.dto.YhProductSearchDTO;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.yh.YhBatchCountReqVO;
import com.zksr.product.api.yhdata.vo.YhBatchListReqVO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/12/13
 * @desc
 */
@Mapper
public interface YhDataConvert {

    YhDataConvert INSTANCE = Mappers.getMapper(YhDataConvert.class);

    YhBatchListReqVO convertBatchListReq(YhBatchCountReqVO countReqVO);

    @Mappings({
            @Mapping(source = "spuId", target = "spuId"),
            @Mapping(source = "skuId", target = "skuId"),
            @Mapping(source = "supplierId", target = "supplierId"),
            @Mapping(source = "categoryId", target = "categoryId"),
            @Mapping(source = "brandId", target = "brandId"),
            @Mapping(source = "productNum", target = "num"),
            @Mapping(source = "markPrice", target = "itemPrice"),
            @Mapping(source = "selected", target = "selected"),
            @Mapping(source = "unit", target = "unit"),
            @Mapping(source = "unitSize", target = "unitSize"),
            @Mapping(source = "midSize", target = "midSize"),
            @Mapping(source = "largeSize", target = "largeSize"),
            @Mapping(source = "stockConvertRate", target = "stockConvertRate"),
            @Mapping(source = "itemId", target = "itemId"),
    })
    ActivityVerifyItemDTO buildActivityVerifyItemDTO(YhPageSupplierGroupItemVO itemVO);

    YhProductSearchDTO convertESSearchDTO(YhBatchListReqVO reqVO);

    YhProductSearchDTO convertESSearchDTO(YhBatchCountReqVO countReqVO);

}
