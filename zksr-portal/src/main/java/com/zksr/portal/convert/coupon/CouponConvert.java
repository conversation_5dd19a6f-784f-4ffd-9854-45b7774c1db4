package com.zksr.portal.convert.coupon;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.controller.mall.vo.coupon.CouponRecordVO;
import com.zksr.portal.controller.mall.vo.coupon.CouponTemplateDetailRespVO;
import com.zksr.promotion.api.coupon.dto.CouponNormalCacheDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.promotion.api.coupon.vo.CouponSpuScopeValidReqVO;
import com.zksr.portal.controller.mall.vo.coupon.CouponValidItemVO;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.utils.CouponScopeUtil;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2024/5/17
 * @desc
 */
@Mapper
public interface CouponConvert {

    CouponConvert INSTANCE = Mappers.getMapper(CouponConvert.class);

    @Mappings({
            @Mapping(source = "supplier.supplierName", target = "supplierName")
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSet(@MappingTarget CouponValidItemVO item, SupplierDTO supplier);

    PageResult<CouponRecordVO> convertRecordPage(PageResult<CouponDTO> pageResult);

    @Mappings({
            @Mapping(source = "supplier.supplierName", target = "supplierName")
    })
    @BeanMapping(ignoreByDefault = true)
    void buildSet(@MappingTarget CouponRecordVO item, SupplierDTO supplier);

    @Mappings({
            @Mapping(source = "coupon.supplierId", target = "supplierId"),
            @Mapping(source = "supplierItem.categoryId", target = "categoryId"),
            @Mapping(source = "supplierItem.brandId", target = "brandId"),
            @Mapping(source = "supplierItem.skuId", target = "skuId"),
    })
    @BeanMapping(ignoreByDefault = true)
    CouponSpuScopeValidReqVO convertCouponSpuScopeValid(CouponDTO coupon, TradePriceCalculateResp.OrderItem.SupplierItem supplierItem);

    CouponScopeUtil.ReceiveScopeValidVO convertCouponReceiveScopeValid(BranchDTO branchDto);

    CouponNormalCacheDTO convertCacheDTO(CouponTemplateDTO couponTemplate);

    CouponValidItemVO convertValidItemVO(CouponTemplateDTO couponTemplate);

    CouponTemplateDetailRespVO convertDetailRespVO(CouponTemplateDTO couponTemplate);
}
