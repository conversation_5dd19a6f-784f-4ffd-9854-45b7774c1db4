package com.zksr.portal.convert.pay;

import com.zksr.account.api.account.dto.AccAccountFlowDTO;
import com.zksr.account.api.account.vo.ApiAccAccountFlowPageVO;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.portal.controller.mall.vo.recharge.RechargeConsumeReqVO;
import com.zksr.account.api.withdraw.vo.RechargeConsumeRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @time 2025/2/13
 * @desc
 */
@Mapper
public interface AccountFlowConvert {

    AccountFlowConvert INSTANCE = Mappers.getMapper(AccountFlowConvert.class);

    ApiAccAccountFlowPageVO convertFlowPageVO(RechargeConsumeReqVO reqVO);

    PageResult<RechargeConsumeRespVO> convertConsumeRespVO(PageResult<AccAccountFlowDTO> pageResult);
}
