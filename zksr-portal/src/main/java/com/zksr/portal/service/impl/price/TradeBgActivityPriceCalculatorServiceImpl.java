package com.zksr.portal.service.impl.price;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.FgActivityTimesRuleEnum;
import com.zksr.common.core.enums.SpActivityTimesRuleEnum;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.core.web.page.TableDataInfo;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.portal.controller.mall.vo.ActivityCalculatorDTO;
import com.zksr.portal.controller.mall.vo.ActivityVO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.mall.TradeOrderConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.utils.ActivityScopeUtil;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import redis.clients.jedis.bloom.BFReserveParams;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collector;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年05月18日 10:33
 * @description: 买赠活动价格计算
 */
@Component
@Order(TradePriceCalculatorService.ORDER_BG_ACTIVITY)
@Slf4j
@SuppressWarnings("all")
public class TradeBgActivityPriceCalculatorServiceImpl implements TradePriceCalculatorService{
    @Autowired
    private IPortalCacheService portalCacheService;
    @Autowired
    private RedisActivityService redisActivityService;
    @Autowired
    private RedisService redisService;

    @Override
    public void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result) {
        /**
         * 如果传递数据为空，不计算
         */
        if (ToolUtil.isEmpty(param) || ToolUtil.isEmpty(result) || ToolUtil.isEmpty(param.getActivitys())){
            return;
        }

        // 查询满减促销活动信息并按照 全场 > 品类 > 品牌 > 商品 排序
        List<PrmActivityDTO> prmActivityList = param.getActivitys().stream()
                .filter(activityVO -> activityVO.getActivityType().equalsIgnoreCase(TrdDiscountTypeEnum.BG.getType()))
                .map(activityVO -> { return portalCacheService.getActivityDto(activityVO.getActivityId()); })
                .filter(ToolUtil.distinctByKey(ActivityDTO::getActivityId)) // 过滤去重
                .sorted(Comparator.comparing(PrmActivityDTO::getSpuScope, Comparator.nullsFirst(Integer::compareTo)))
                .collect(Collectors.toList());

        StringBuilder tipsInfo = new StringBuilder();
        prmActivityList.forEach(prmActivity -> {
            // 2、获取活动适用范围  城市, 渠道, 门店
            SupplierActivityDTO supplierActivity = ActivityConvert.INSTANCE.convert(prmActivity);
            Boolean boo = redisActivityService.validateActivity(supplierActivity, param.getBranchDto());
            if (!boo) {
                log.error("促销单【{}】验证数据未通过！", prmActivity.getPrmSheetNo());
                return;
            }
            // 3、获取买赠促销活动规则
            Set<BgRuleDTO> bgRuleList = portalCacheService.getActivityRuleDto(prmActivity.getActivityId(), TrdDiscountTypeEnum.BG.getType());

            TradePriceCalculateResp.Promotion promotion = new TradePriceCalculateResp.Promotion();
            promotion.setId(prmActivity.getActivityId())
                    .setName(prmActivity.getActivityName())
                    .setType(TrdDiscountTypeEnum.BG.getType())
                    .setSupplierId(prmActivity.getSupplierId())
                    .setPromotionItems(new ArrayList<>());


            List<ActivityVO> activityVOList = param.getActivitys().stream().filter(activityVO -> activityVO.getActivityId().equals(prmActivity.getActivityId())).collect(Collectors.toList());
            Map<Long, Long> processedMap = new HashMap();
            result.getItems().stream()
                    .filter(supplierOrder -> supplierOrder.getSupplierId().equals(prmActivity.getSupplierId()))
                    .forEach(supplierOrder -> {
                        activityScope(prmActivity, bgRuleList, supplierOrder, processedMap, promotion, activityVOList, tipsInfo, param.getInterfaceType());
                    });
            if (promotion.getPromotionItems().size() > 0) {
                result.getPromotions().add(promotion);
            }
        });
        result.setActivityTipsInfo(result.getActivityTipsInfo() + tipsInfo.toString());
    }

    @Override
    public void updateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.BG.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    Long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    // 更新 缓存买赠活动已购数量
                    redisActivityService.setBgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), BigDecimal.valueOf(orderDiscount.getGiftQty()), time, TimeUnit.SECONDS);
                });

    }

    @Override
    public void undoUpdateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.BG.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    Long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    // 更新 缓存买赠活动已购数量
                    redisActivityService.setBgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), BigDecimal.valueOf(orderDiscount.getGiftQty()).negate(), null, null);
                });

    }

    /**
     *  获取买赠活动
     * @param prmActivity 活动信息
     * @param ruleList 活动规则信息
     * @param supplierOrder 入驻商商品信息
     * @param processedMap 已验证过满减的商品信息
     */
    private void activityScope(PrmActivityDTO prmActivity, Set<BgRuleDTO> ruleList, TradePriceCalculateResp.OrderItem supplierOrder, Map<Long, Long> processedMap,
                               TradePriceCalculateResp.Promotion promotion, List<ActivityVO> activityVOList, StringBuilder tipsInfo, Integer interfaceType) {
        // 获取满足本次买赠的商品数据
        List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList = getFilteredSupplierOrderList(supplierOrder, prmActivity, processedMap);

        if (ToolUtil.isEmpty(supplierOrderList)) // 没有满足条件的数据
            return;

        // 命中的总数量
//        Integer totalQty = supplierOrderList.stream()
//                .filter(item -> Objects.nonNull(item.getGiftFlag()) && item.getGiftFlag() == StatusConstants.FLAG_FALSE)
//                .map((supplierItem -> StockUtil.stockMultiply(supplierItem.getCount(), supplierItem.getOrderUnitSize())))
//                .reduce(BigDecimal.ZERO, BigDecimal::add)
//                .intValue();


        // 满足条件的买赠规则
        List<BgRuleDTO> bgRuleDTOList = getSatisfyConditionRule(ruleList, supplierOrderList, activityVOList);
        if (ToolUtil.isEmpty(bgRuleDTOList)) // 没有满足条件的规则
            return;

        //写入优惠赠品信息
        setSupplierItemActivity(prmActivity, bgRuleDTOList, promotion, supplierOrderList, tipsInfo, interfaceType);

        // 更新processedMap以标记已处理的项
        updateProcessedMap(supplierOrderList, processedMap, prmActivity);

    }

    /**
     * 获取满足本次买赠的商品数据
     * @param supplierOrder
     * @param activity
     * @param processedMap
     * @return
     */
    private List<TradePriceCalculateResp.OrderItem.SupplierItem> getFilteredSupplierOrderList(TradePriceCalculateResp.OrderItem supplierOrder, PrmActivityDTO activity, Map<Long, Long> processedMap) {
        List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList = supplierOrder.getSupplierItems();

        // 获取促销活动参与商品范围限定
        List<ActivitySpuScopeDTO> scopeList = portalCacheService.getActivitySpuScopeList(activity.getActivityId());

        // 判断商品是否在活动参与范围内
        supplierOrderList = supplierOrderList.stream().filter(item -> ActivityScopeUtil.validateSpuScope(TradeOrderConvert.INSTANCE.convertActivityValidVO(item), scopeList)).collect(Collectors.toList());

        return supplierOrderList;
    }


    /**
     * 根据条件查询出满足条件的买赠规则
     * @param ruleList
     * @param totalQty
     * @return
     */
    private List<BgRuleDTO> getSatisfyConditionRule(Set<BgRuleDTO> ruleList, List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList, List<ActivityVO> activityVOList){
        List<BgRuleDTO> bgRuleList = null;
        Set<Long> activityRuleIds = activityVOList.stream().filter(activityVO -> ToolUtil.isNotEmpty(activityVO.getActivityRuleId())).map(activityVO -> activityVO.getActivityRuleId()).collect(Collectors.toSet());
        // 命中的总数量 大于等于 买赠规则按照数量 倒序排序
        bgRuleList = ruleList.stream()
                .filter(fdRule -> {
                    Integer totalCount = supplierOrderList.stream()
                            .filter(item -> Objects.nonNull(item.getGiftFlag()) && item.getGiftFlag() == StatusConstants.FLAG_FALSE)
                            .map(supplierItem -> {
                                SpuDTO spuDTO = portalCacheService.getSpuDTO(supplierItem.getSpuId());
                                BigDecimal convertRatio = spuDTO.getUnitSizeConvertRatio(supplierItem.getUnitSize(), fdRule.getRuleUnitType());
                                return convertRatio.multiply(new BigDecimal(supplierItem.getCount()));
                            }).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
                    return totalCount >= fdRule.getRuleQty();
                })
                .filter(fdRule -> ToolUtil.isEmpty(activityRuleIds) || activityRuleIds.contains(fdRule.getBgRuleId()))
                .sorted(Comparator.comparing(BgRuleDTO::getRuleQty, Comparator.nullsFirst(Integer::compareTo)).reversed())
                .collect(Collectors.toList());
        return bgRuleList;
    }


    /**
     * 写入订单营销信息
     * @param bgRuleDTOList 活动规则
     * @param promotion 营销信息
     */
    private void setSupplierItemActivity (PrmActivityDTO prmActivity, List<BgRuleDTO> bgRuleDTOList, TradePriceCalculateResp.Promotion promotion, List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList, StringBuilder tipsInfo, Integer interfaceType) {
//        Map<Integer, Integer> giftGroupMap = new HashMap<>();
//        AtomicInteger counter = new AtomicInteger(0);
        List<TradePriceCalculateResp.PromotionItem> promotionItemList =  bgRuleDTOList.stream().map(rule -> {
            TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
            promotionItem.setGiftType(rule.getGiftType());
            promotionItem.setActivityRuleId(rule.getBgRuleId());
            promotionItem.setDiscountCondition(String.valueOf(rule.getRuleQty())); // 活动当前选择规则的买赠条件数量

            Integer qty = 0;
            if (prmActivity.getLadderFlag() == NumberPool.INT_ONE) { // 满赠
                qty = rule.getOnceGiftQty();
            } else { // 每赠
                Integer totalCount = supplierOrderList.stream()
                        .filter(item -> Objects.nonNull(item.getGiftFlag()) && item.getGiftFlag() == StatusConstants.FLAG_FALSE)
                        .map(supplierItem -> {
                            SpuDTO spuDTO = portalCacheService.getSpuDTO(supplierItem.getSpuId());
                            BigDecimal convertRatio = spuDTO.getUnitSizeConvertRatio(supplierItem.getUnitSize(), rule.getRuleUnitType());
                            return convertRatio.multiply(new BigDecimal(supplierItem.getCount()));
                        }).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
                qty = (totalCount / rule.getRuleQty()) * rule.getOnceGiftQty();
            }

            promotionItem.setGiftQty(qty);
            // 商品赠品
            if (rule.getGiftType() == NumberPool.INT_ZERO) {
                promotionItem.setGiftSkuId(rule.getSkuId());
                promotionItem.setGiftSkuUnitType(rule.getGiftSkuUnitType());
                SkuDTO skuDTO = portalCacheService.getSkuDTO(rule.getSkuId());
                if (UnitTypeEnum.S(rule.getGiftSkuUnitType())) { // 小单位
                    promotionItem.setGiftSkuUnitPrice(skuDTO.getMarkPrice());
                } else if(UnitTypeEnum.M(rule.getGiftSkuUnitType())) { // 中单位
                    promotionItem.setGiftSkuUnitPrice(skuDTO.getMidMarkPrice());
                } else if (UnitTypeEnum.L(rule.getGiftSkuUnitType())) { // 大单位
                    promotionItem.setGiftSkuUnitPrice(skuDTO.getLargeMarkPrice());
                }

                // 保存订单 验证所选赠品单位是否存在
                if (Objects.equals(interfaceType, NumberPool.INT_ONE)) {
                    SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
                    try {
                        Assert.isTrue(spuDTO.isUnitExist(rule.getGiftSkuUnitType()),
                                "买赠活动【{}】中的赠品商品【{}】所赠送的单位不存在",
                                prmActivity.getActivityName(),
                                spuDTO.getSpuName()
                        );
                    } catch (IllegalArgumentException e) {
                        throw new ServiceException(e.getMessage());
                    }
                }
            }
            // 优惠劵赠品
            if (rule.getGiftType() == NumberPool.INT_ONE) {
                promotionItem.setGiftCouponTemplateId(rule.getCouponTemplateId());
            }


            // 总数 - 已赠 < 本地需要赠送
            if (!redisActivityService.validateRuleStock(rule, qty)) {
                tipsInfo.append("买赠活动【" + prmActivity.getActivityName() + "】起送数量【" + rule.getRuleQty() + "】规则赠品库存不足！;");
                log.info("买赠活动【" + prmActivity.getActivityName() + "】起送数量【" + rule.getRuleQty() + "】规则赠品库存不足！");
                return null;
            }

            // 赠品分组 方便前端选择
//            if (giftGroupMap.containsKey(rule.getRuleQty())){
//                promotionItem.setGiftGroupNum(giftGroupMap.get(rule.getRuleQty()));
//            } else {
//                counter.getAndIncrement(); // 自增
//                promotionItem.setGiftGroupNum(counter.get());
//                giftGroupMap.put(rule.getRuleQty(), counter.get());
//            }
            promotionItem.setUuIdNo("");
            return promotionItem;
        }).filter(promotionItem -> ToolUtil.isNotEmpty(promotionItem)).collect(Collectors.toList());
        // 营销信息
        promotion.getPromotionItems().addAll(promotionItemList);
    }


    /**
     * 更新processedMap以标记已处理的项
     * @param supplierOrderList
     * @param processedMap
     */
    private void updateProcessedMap(List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList, Map<Long, Long> processedMap, PrmActivityDTO prmActivity) {
        supplierOrderList.forEach(supplierItem -> {
            supplierItem.setActivityIdInfo(supplierItem.getActivityIdInfo()+";"+prmActivity.getActivityId()); // 商品参与过的活动ID
            // 当前数据已处理处理过，跳过本次操作
            if (processedMap.containsKey(supplierItem.getCategoryId()) || processedMap.containsKey(supplierItem.getBrandId()) || processedMap.containsKey(supplierItem.getSkuId())) {
                return;
            }

            processedMap.put(supplierItem.getSkuId(), supplierItem.getSkuId());
            processedMap.put(supplierItem.getCategoryId(), supplierItem.getCategoryId());
            processedMap.put(supplierItem.getBrandId(), supplierItem.getBrandId());

            if (ToolUtil.isNotEmpty(supplierItem.getSkuId())) {
                processedMap.put(supplierItem.getSkuId(), supplierItem.getSkuId());
            }
            if (ToolUtil.isNotEmpty(supplierItem.getBrandId())) {
                processedMap.put(supplierItem.getBrandId(), supplierItem.getBrandId());
            }
            if (ToolUtil.isNotEmpty(supplierItem.getSpuId())) {
            }
        });
    }





}
