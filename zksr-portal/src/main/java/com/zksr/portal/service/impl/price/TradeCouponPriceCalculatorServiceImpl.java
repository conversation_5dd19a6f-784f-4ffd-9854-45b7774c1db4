package com.zksr.portal.service.impl.price;

import com.google.common.collect.Lists;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.convert.mall.TradeOrderConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.ICouponService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.promotion.api.coupon.dto.CouponDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年04月07日 11:20
 * @description: 订单优惠劵价格计算
 */
@Component
@SuppressWarnings("all")
@Order(TradePriceCalculatorService.ORDER_COUPON)
public class TradeCouponPriceCalculatorServiceImpl implements TradePriceCalculatorService {


    @Autowired
    private ICouponService couponService;
    @Autowired
    private IPortalCacheService portalCacheService;

    @Override
    public void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result) {
        /**
         * 如果优惠劵集合为NULL 或者 集合为空，不计算优惠劵逻辑
         */
        if (ToolUtil.isEmpty(param.getCouponIds()) || param.getCouponIds().size() <= 0){
            return;
        }
        // 入驻商商品分组
        Map<Long, List<TradePriceCalculateResp.OrderItem>> supplierMap = result.getItems().stream().collect(Collectors.groupingBy(TradePriceCalculateResp.OrderItem::getSupplierId));
        /**
         * 获取优惠劵列表
         */
        List<OrderValidItemDTO> orderItemList = TradeOrderConvert.INSTANCE.convertOrderValidItemList(result.getItems());
        List<CouponDTO> coupons = couponService.validateCreatOrder(orderItemList, param.getCouponIds(), param.getBranchDto());
        coupons.forEach(coupon -> {
            List<Long> supplierIdList = coupon.getSupplierIdList();
            // 多入驻商兼容旧数据
            if (CollectionUtils.isEmpty(supplierIdList)) {
                supplierIdList = Lists.newArrayList(coupon.getSupplierId());
            }
            supplierIdList = supplierIdList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
            // 命中入驻商商品列表
            List<TradePriceCalculateResp.OrderItem.SupplierItem> validItemList = new ArrayList<>();
            Map<Long, List<TradePriceCalculateResp.OrderItem.SupplierItem>> supplierItemMap = new HashMap<>();
            for (Long supplierId : supplierIdList) {
                // 符合优惠券要求的入驻商订单
                List<TradePriceCalculateResp.OrderItem> orderItems = supplierMap.get(supplierId);
                if (CollectionUtils.isEmpty(orderItems)) {
                    continue;
                }
                // 入驻商订单商品
                List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierItems = orderItems.stream().map(TradePriceCalculateResp.OrderItem::getSupplierItems).flatMap(Collection::stream).collect(Collectors.toList());
                List<TradePriceCalculateResp.OrderItem.SupplierItem> validSupplierItemList = couponService.validSupplierItems(supplierItems, coupon);
                if (CollectionUtils.isNotEmpty(validSupplierItemList)) {
                    validItemList.addAll(validSupplierItemList);
                    supplierItemMap.put(supplierId, validSupplierItemList);
                }
            }
            // 命中商品列表为空
            if (CollectionUtils.isEmpty(validItemList)) {
                return;
            }
            // 获取优惠券符合要求的商品总金额
            BigDecimal totalAmt = validItemList.stream().filter(Objects::nonNull).map(TradePriceCalculateResp.OrderItem.SupplierItem::getPayPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            // 计算优惠金额
            BigDecimal couponAmt = getCouponAmt(coupon, totalAmt);
            BigDecimal ratioAmt = new BigDecimal(String.valueOf(couponAmt));
            List<TradePriceCalculateResp.Promotion> promotionList = new ArrayList<>();
            AtomicInteger exeIndex = new AtomicInteger(); // 执行次数
            for (Map.Entry<Long, List<TradePriceCalculateResp.OrderItem.SupplierItem>> entry : supplierItemMap.entrySet()) {
                // 组装供应商优惠券信息
                TradePriceCalculateResp.Promotion promotion = new TradePriceCalculateResp.Promotion();
                promotion.setId(coupon.getCouponId())
                        .setName(coupon.getCouponName())
                        .setType(TrdDiscountTypeEnum.COUPON.getType())
                        .setSupplierId(entry.getKey())
                        .setPromotionItems(new ArrayList<>());
                exeIndex.getAndIncrement();
                // 当前供应商订单总优惠金额
                BigDecimal supplierCouponAmt = BigDecimal.ZERO;
                boolean lastFlag = exeIndex.get() == supplierItemMap.size();
                List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierItemList = entry.getValue();
                for (int i = 0; i < supplierItemList.size(); i++) {
                    TradePriceCalculateResp.OrderItem.SupplierItem supplierItem = supplierItemList.get(i);
                    BigDecimal allocationAmt = BigDecimal.ZERO;
                    if (i == supplierItemList.size() - 1 && lastFlag) {
                        allocationAmt = couponAmt.subtract(supplierCouponAmt);
                    } else {
                        allocationAmt = supplierItem.getPayPrice().divide(totalAmt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(ratioAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                    }
                    supplierCouponAmt = supplierCouponAmt.add(allocationAmt);
                    setSupplierItemCouponAmt(coupon, supplierItem, allocationAmt, promotion);  //写入优惠金额
                }
                promotion.setDiscountTotalPrice(supplierCouponAmt);
                couponAmt = couponAmt.subtract(supplierCouponAmt);
                promotionList.add(promotion);
            }
            if (CollectionUtils.isNotEmpty(promotionList)) {
                result.getPromotions().addAll(promotionList);
            }
        });
    }

    /**
     *  获得改张优惠劵的总优惠金额
     * @param coupon  优惠劵信息
     * @param countAmt  优惠命中数据的总金额
     * @return
     */
    private BigDecimal getCouponAmt (CouponDTO coupon, BigDecimal countAmt) {
        BigDecimal couponAmt = BigDecimal.ZERO;
        if (coupon.getDiscountType() == 0) couponAmt = coupon.getDiscountAmt();
        if (coupon.getDiscountType() == 1){
            BigDecimal couponAfterAmt = coupon.getDiscountPercent().divide(new BigDecimal("10"), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(countAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
            couponAmt = countAmt.subtract(couponAfterAmt); // 折扣优惠金额
            if (ToolUtil.isNotEmpty(coupon.getDiscountLimitAmt())){
                couponAmt = couponAmt.compareTo(coupon.getDiscountLimitAmt()) > 0 ? coupon.getDiscountLimitAmt() : couponAmt.setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP); // 当折扣优惠金额大于折扣上限金额，取折扣上线优惠金额
            }

        }
        return couponAmt;
    }

    /**
     *  写入订单明细信息优惠劵优惠金额
     * @param coupon  优惠劵信息
     * @param allocationAmt  优惠金额
     * @return
     */
    private void setSupplierItemCouponAmt (CouponDTO coupon, TradePriceCalculateResp.OrderItem.SupplierItem supplierItem, BigDecimal allocationAmt, TradePriceCalculateResp.Promotion promotion) {
        // 当前商品是组合商品 写入子商品的促销信息后直接返回
        if (Objects.equals(supplierItem.getGoodsType(), NumberPool.INT_ONE)) {
            couponCompatibleCbActivity(coupon, supplierItem, allocationAmt, promotion);
            return;
        }

        // 营销信息
        TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
        promotion.getPromotionItems().add(promotionItem);
        promotionItem.setSkuId(supplierItem.getSkuId());
        promotionItem.setUuIdNo(supplierItem.getUuIdNo());
        promotionItem.setCouponTemplateId(coupon.getCouponTemplateId());
        if (coupon.getCostFlag() == 1){
            supplierItem.setCouponDiscountAmt(supplierItem.getCouponDiscountAmt().add(allocationAmt));  //写入优惠金额 分摊
            promotionItem.setCouponDiscountAmt(allocationAmt);
        } else {
            supplierItem.setCouponDiscountAmt2(supplierItem.getCouponDiscountAmt2().add(allocationAmt));  //写入优惠金额 不分摊
            promotionItem.setCouponDiscountAmt2(allocationAmt);
        }
    }


    /**
     * 入驻商优惠劵
     * @param couponSpuScopeList
     * @param result
     * @param coupon
     */
    private void couponScopeSupplier(List <CouponSpuScopeDTO> couponSpuScopeList, TradePriceCalculateResp result, CouponDTO coupon, TradePriceCalculateResp.Promotion promotion) {
        /**
         * 同一个优惠劵可能会命中多个入驻商
         */
        // 命中入驻商的总金额
        BigDecimal supplierAmt = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            return result.getItems().stream()
                    .filter((supplier -> supplier.getSupplierId().equals(couponSpuScopeDTO.getApplyId()) ))
                    .map((supplier -> supplier.getTotalAmt()))
                    .reduce((amt1 , amt2) -> amt1.add(amt2)).get();
        }).reduce((amt1, amt2) -> amt1.add(amt2)).get();

        // 总数 = 命中入驻商订单下商品的总数
        Long count = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            return result.getItems().stream()
                    .filter((supplier -> supplier.getSupplierId().equals(couponSpuScopeDTO.getApplyId())))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .count();
        }).reduce((amt1, amt2) -> amt1+amt2).get();

        BigDecimal couponAmt = getCouponAmt(coupon, supplierAmt); // 优惠金额
        final BigDecimal[] allocationAmtCount = {BigDecimal.ZERO};  // 优惠已扣减总金额
        AtomicInteger i = new AtomicInteger();

        promotion.setDiscountTotalPrice(couponAmt);
        promotion.setPromotionItems(new ArrayList<>());

        couponSpuScopeList.forEach(couponScope -> {
            result.getItems().stream().filter((supplier -> supplier.getSupplierId().equals(couponScope.getApplyId())))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .forEach(supplierItem -> {
                        i.getAndIncrement();
                        BigDecimal allocationAmt = BigDecimal.ZERO;
                        if (i.get() == count) {
                            allocationAmt = couponAmt.subtract(allocationAmtCount[0]);
                        } else {
                            allocationAmt = supplierItem.getPayPrice().divide(supplierAmt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(couponAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                        }
                        allocationAmtCount[0] = allocationAmtCount[0].add(allocationAmt);
                        setSupplierItemCouponAmt(coupon, supplierItem, allocationAmt, promotion);  //写入优惠金额
                    });
        });
    }

    /**
     * 品类优惠劵
     * @param couponSpuScopeList
     * @param result
     * @param coupon
     */
    private void couponScopeClass(List <CouponSpuScopeDTO> couponSpuScopeList, TradePriceCalculateResp result, CouponDTO coupon, TradePriceCalculateResp.Promotion promotion) {
        // 品类优惠劵命中的总金额
        BigDecimal amt = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            Optional<BigDecimal> optional =  result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> supplierItem.getCategoryId().equals(couponSpuScopeDTO.getApplyId())))
                    .map((supplierItem -> supplierItem.getPayPrice()))
                    .reduce((amt1 , amt2) -> amt1.add(amt2));
            return optional.isPresent() ? optional.get() : BigDecimal.ZERO;
        }).reduce((amt1, amt2) -> amt1.add(amt2)).get();


        // 品类优惠劵命中商品总数
        Long count = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            return result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> supplierItem.getCategoryId().equals(couponSpuScopeDTO.getApplyId())))
                    .count();
        }).reduce((amt1, amt2) -> amt1+amt2).get();

        BigDecimal couponAmt = getCouponAmt(coupon, amt); // 优惠金额
        final BigDecimal[] allocationAmtCount = {BigDecimal.ZERO};  // 优惠已扣减总金额
        AtomicInteger i = new AtomicInteger(); // 执行次数

        promotion.setDiscountTotalPrice(couponAmt);
        promotion.setPromotionItems(new ArrayList<>());

        couponSpuScopeList.forEach(couponScope -> {
            result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> supplierItem.getCategoryId().equals(couponScope.getApplyId())))
                    .forEach(supplierItem -> {
                        i.getAndIncrement();
                        BigDecimal allocationAmt = BigDecimal.ZERO;
                        if (i.get() == count) {
                            allocationAmt = couponAmt.subtract(allocationAmtCount[0]);
                        } else {
                            allocationAmt = supplierItem.getPayPrice().divide(amt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(couponAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                        }
                        allocationAmtCount[0] = allocationAmtCount[0].add(allocationAmt);
                        setSupplierItemCouponAmt(coupon, supplierItem, allocationAmt, promotion);  //写入优惠金额
                    });
        });
    }

    /**
     * 品牌优惠劵
     * @param couponSpuScopeList
     * @param result
     * @param coupon
     */
    private void couponScopeBrand(List <CouponSpuScopeDTO> couponSpuScopeList, TradePriceCalculateResp result, CouponDTO coupon, TradePriceCalculateResp.Promotion promotion) {
        // 品牌优惠劵命中的总金额
        BigDecimal amt = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            Optional<BigDecimal> optional =  result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> (ToolUtil.isNotEmpty(supplierItem.getBrandId()) && supplierItem.getBrandId().equals(couponSpuScopeDTO.getApplyId()))))
                    .map((supplierItem -> supplierItem.getPayPrice()))
                    .reduce((amt1 , amt2) -> amt1.add(amt2));
            return optional.isPresent() ? optional.get() : BigDecimal.ZERO;
        }).reduce((amt1, amt2) -> amt1.add(amt2)).orElse(BigDecimal.ZERO);


        // 品牌优惠劵命中商品总数
        Long count = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            return result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> supplierItem.getBrandId().equals(couponSpuScopeDTO.getApplyId())))
                    .count();
        }).reduce((amt1, amt2) -> amt1+amt2).get();

        // 价格计算
        BigDecimal couponAmt = getCouponAmt(coupon, amt); // 优惠金额
        final BigDecimal[] allocationAmtCount = {BigDecimal.ZERO};  // 优惠已扣减总金额
        AtomicInteger i = new AtomicInteger(); // 执行次数

        promotion.setDiscountTotalPrice(couponAmt);
        promotion.setPromotionItems(new ArrayList<>());

        couponSpuScopeList.forEach(couponScope -> {
            result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> (ToolUtil.isNotEmpty(supplierItem.getBrandId()) && supplierItem.getBrandId().equals(couponScope.getApplyId()))))
                    .forEach(supplierItem -> {
                        i.getAndIncrement();
                        BigDecimal allocationAmt = BigDecimal.ZERO;
                        if (i.get() == count) {
                            allocationAmt = couponAmt.subtract(allocationAmtCount[0]);
                        } else {
                            allocationAmt = supplierItem.getPayPrice().divide(amt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(couponAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                        }
                        allocationAmtCount[0] = allocationAmtCount[0].add(allocationAmt);
                        setSupplierItemCouponAmt(coupon, supplierItem, allocationAmt, promotion); //写入优惠金额
                    });
        });
    }

    /**
     * 商品SPU优惠劵
     * @param couponSpuScopeList
     * @param result
     * @param coupon
     */
    private void couponScopeItemSpu(List <CouponSpuScopeDTO> couponSpuScopeList, TradePriceCalculateResp result, CouponDTO coupon, TradePriceCalculateResp.Promotion promotion) {
        // 商品SPU优惠劵命中的总金额
        BigDecimal amt = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            Optional<BigDecimal> optional = result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> supplierItem.getSkuId().equals( couponSpuScopeDTO.getApplyId())))
                    .map((supplierItem -> supplierItem.getPayPrice()))
                    .reduce((amt1 , amt2) -> amt1.add(amt2));
            return optional.isPresent() ? optional.get() : BigDecimal.ZERO;
        }).reduce((amt1, amt2) -> amt1.add(amt2)).get();


        // 商品SPU优惠劵命中商品总数
        Long count = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            return result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> supplierItem.getSkuId().equals(couponSpuScopeDTO.getApplyId())))
                    .count();
        }).reduce((amt1, amt2) -> amt1+amt2).get();

        BigDecimal couponAmt = getCouponAmt(coupon, amt); // 优惠金额
        final BigDecimal[] allocationAmtCount = {BigDecimal.ZERO};  // 优惠已扣减总金额
        AtomicInteger i = new AtomicInteger(); // 执行次数

        promotion.setDiscountTotalPrice(couponAmt);
        promotion.setPromotionItems(new ArrayList<>());

        couponSpuScopeList.forEach(couponScope -> {
            result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .filter((supplierItem -> supplierItem.getSkuId().equals(couponScope.getApplyId())))
                    .forEach(supplierItem -> {
                        i.getAndIncrement();
                        BigDecimal allocationAmt = BigDecimal.ZERO;
                        if (i.get() == count) {
                            allocationAmt = couponAmt.subtract(allocationAmtCount[0]);
                        } else {
                            allocationAmt = supplierItem.getPayPrice().divide(amt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(couponAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                        }
                        allocationAmtCount[0] = allocationAmtCount[0].add(allocationAmt);
                        setSupplierItemCouponAmt(coupon, supplierItem, allocationAmt, promotion);  //写入优惠金额


                    });
        });
    }

    /**
     * 全场优惠劵
     * @param couponSpuScopeList
     * @param result
     * @param coupon
     */
    private void couponScopeAll(List <CouponSpuScopeDTO> couponSpuScopeList, TradePriceCalculateResp result, CouponDTO coupon, TradePriceCalculateResp.Promotion promotion) {
        // 全场优惠劵命中的总金额
        Optional<BigDecimal> optional = result.getItems().stream()
                .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                .flatMap(supplier -> supplier.getSupplierItems().stream())
                .map((supplierItem -> supplierItem.getPayPrice()))
                .reduce((amt1 , amt2) -> amt1.add(amt2));
        BigDecimal amt = optional.isPresent() ? optional.get() : BigDecimal.ZERO;


        // 全场优惠劵命中商品总数
        Long count = couponSpuScopeList.stream().map(couponSpuScopeDTO -> {
            return result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .count();
        }).reduce((amt1, amt2) -> amt1+amt2).get();

        // 价格计算
        BigDecimal couponAmt = getCouponAmt(coupon, amt); // 优惠金额
        final BigDecimal[] allocationAmtCount = {BigDecimal.ZERO};  // 优惠已扣减总金额
        AtomicInteger i = new AtomicInteger(); // 执行次数

        promotion.setDiscountTotalPrice(couponAmt);
        promotion.setPromotionItems(new ArrayList<>());

        couponSpuScopeList.forEach(couponScope -> {
            result.getItems().stream()
                    .filter(supplier -> supplier.getSupplierId().equals(coupon.getSupplierId()))
                    .flatMap(supplier -> supplier.getSupplierItems().stream())
                    .forEach(supplierItem -> {
                        i.getAndIncrement();
                        BigDecimal allocationAmt = BigDecimal.ZERO;
                        if (i.get() == count) {
                            allocationAmt = couponAmt.subtract(allocationAmtCount[0]);
                        } else {
                            allocationAmt = supplierItem.getPayPrice().divide(amt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(couponAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                        }
                        allocationAmtCount[0] = allocationAmtCount[0].add(allocationAmt);
                        setSupplierItemCouponAmt(coupon, supplierItem, allocationAmt, promotion);  //写入优惠金额
                    });
        });
    }

    /**
     * 优惠劵兼容组合促销 计算优惠子商品数据
     * @param coupon
     * @param supplierItem
     * @param allocationAmt
     * @param promotion
     */
    private void couponCompatibleCbActivity(CouponDTO coupon, TradePriceCalculateResp.OrderItem.SupplierItem supplierItem, BigDecimal allocationAmt, TradePriceCalculateResp.Promotion promotion) {
        // 组合商品下的明细商品原总金额
        BigDecimal originalTotalAmt = supplierItem.getSpuCombineSkuVOList().stream().map(combineSku -> {
            return combineSku.getSuggestPrice().multiply(BigDecimal.valueOf(combineSku.getQty())).multiply(BigDecimal.valueOf(supplierItem.getCount()));
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 已扣减金额
        final BigDecimal[] deductionAmt = {BigDecimal.ZERO};
        // 执行次数
        AtomicInteger i = new AtomicInteger();
        supplierItem.getSpuCombineSkuVOList().forEach(combineSku -> {
            /** 组合商品明细数据优惠金额计算逻辑
             *
             * 总金额：100
             * 原总金额：312
             * 优惠金额：212
             * 商品1优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
             * 商品2优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
             * 商品3优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
             * 商品4优惠金额（最后一个商品的优惠金额是扣减的） ： 212 - 67.95 - 67.95 -67.95 = 8.15
             */
            i.getAndIncrement();
            BigDecimal discountAmt = BigDecimal.ZERO; // 当前子商品优惠劵优惠金额
            if (i.get() == supplierItem.getSpuCombineSkuVOList().size()) { // 最后一个的金额不是计算处理的，是扣减出来的
                // 当前子商品优惠劵优惠金额
                discountAmt = allocationAmt.subtract(deductionAmt[0]).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
            } else {
                // 当前子商品优惠劵优惠金额
                discountAmt = combineSku.getSuggestPrice().multiply(BigDecimal.valueOf(combineSku.getQty())).multiply(BigDecimal.valueOf(supplierItem.getCount()))
                        .divide(originalTotalAmt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP)
                        .multiply(allocationAmt)
                        .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
            }
            deductionAmt[0] = deductionAmt[0].add(discountAmt);


            // 营销信息
            TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
            promotion.getPromotionItems().add(promotionItem);
            promotionItem.setSkuId(combineSku.getSkuId());
            promotionItem.setUuIdNo(StringUtils.format("{}_{}_{}", supplierItem.getUuIdNo(), combineSku.getSkuId(), combineSku.getSkuUnitType()));
            promotionItem.setCouponTemplateId(coupon.getCouponTemplateId());

            if (coupon.getCostFlag() == 1){
                combineSku.setCouponDiscountAmt1(combineSku.getCouponDiscountAmt1().add(discountAmt)); // 子商品优惠金额
                supplierItem.setCouponDiscountAmt(supplierItem.getCouponDiscountAmt().add(discountAmt));  //写入优惠金额 分摊
                promotionItem.setCouponDiscountAmt(allocationAmt);
            } else {
                combineSku.setCouponDiscountAmt2(combineSku.getCouponDiscountAmt2().add(discountAmt)); // 子商品优惠金额
                supplierItem.setCouponDiscountAmt2(supplierItem.getCouponDiscountAmt2().add(discountAmt));  //写入优惠金额 不分摊
                promotionItem.setCouponDiscountAmt2(allocationAmt);
            }
        });
    }
}
