package com.zksr.portal.service.impl.handler.transaction;

import com.alibaba.fastjson.JSON;
import com.zksr.account.api.balance.BalanceApi;
import com.zksr.account.api.balance.BalanceFlowApi;
import com.zksr.account.api.balance.dto.MemBranchBalanceDTO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.context.SecurityContextHolder;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.transaction.TransactionHandler;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import com.zksr.trade.api.order.vo.TrdOrderSaveVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderSaveVO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 余额支付事务处理器
 * @Date: 2025/07/12
 */
@SuppressWarnings("rawtypes")
@Slf4j
public class BalancePaymentTransactionHandler implements TransactionHandler {

    private final RemoteSaveOrderVO orderVo;

    // 门店
    private final BranchDTO branchDto;

    // 余额支付成功
    private boolean balancePaySuccess;

    // 余额支付交易流水id
    private String balanceFlowId;

    public BalancePaymentTransactionHandler(RemoteSaveOrderVO orderVo, BranchDTO branchDto) {
        this.orderVo = orderVo;
        this.branchDto = branchDto;
    }

    @Override
    public void doBusiness() {
        // 检查是否使用余额支付
        BigDecimal payBalanceAmt = orderVo.getOrderSaveVo().getPayBalanceAmt();
        if (Objects.isNull(payBalanceAmt) || payBalanceAmt.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        // 分摊余额支付金额
        calculateBalancePaymentAmounts(orderVo);
        try {
            // 发起余额支付
            payBalance(orderVo);
            balancePaySuccess = true;
        } catch (Exception e) {
            log.error("余额支付处理失败, orderNo: {}", orderVo.getOrderSaveVo().getOrderNo(), e);
            throw new ServiceException("余额支付处理失败: " + e.getMessage());
        }
    }

    @Override
    public void rollback() {
        if (balancePaySuccess && StringUtils.isNotBlank(balanceFlowId)) {
            BalanceFlowApi balanceFlowApi = SpringUtils.getBean(BalanceFlowApi.class);
            CommonResult<Boolean> balancePayResult = balanceFlowApi.rollBackByFlowId(balanceFlowId);
            if (balancePayResult.isError()) {
                throw new ServiceException("门店余额支付订单处理失败");
            }
        }
    }


    /**
     * 余额支付（扣减门店余额）
     */
    private void payBalance(RemoteSaveOrderVO orderVo) {
        // 组装请求余额支付的参数
        MemBranchBalanceDTO balanceDTO = new MemBranchBalanceDTO();
        balanceDTO.setBranchId(branchDto.getBranchId());
        balanceDTO.setSysCode(branchDto.getSysCode());
        // 主交易单号
        balanceDTO.setTradeNo(orderVo.getOrderSaveVo().getOrderNo());
        // 入驻商订单号，用,隔开
        String sourceTradeNo = orderVo.getSupplierOrderSaveVOs().stream().filter(Objects::nonNull).map(TrdSupplierOrderSaveVO::getSupplierOrderNo).filter(Objects::nonNull).collect(Collectors.joining(","));
        balanceDTO.setSourceTradeNo(sourceTradeNo);
        balanceDTO.setOpAmt(orderVo.getOrderSaveVo().getPayBalanceAmt());
        balanceDTO.setRemark(orderVo.getOrderSaveVo().getMemo());
        balanceDTO.setOperUserName(SecurityContextHolder.getUserName());
        // 发起订单门店余额支付
        BalanceApi balanceApi = SpringUtils.getBean(BalanceApi.class);
        log.info("=====================发起订单门店余额支付参数：{}", JSON.toJSONString(balanceDTO));
        CommonResult<String> balancePayResult = balanceApi.orderPay(balanceDTO);
        if (balancePayResult.isError()) {
            throw new ServiceException("门店余额支付订单处理失败");
        }
        balanceFlowId = balancePayResult.getCheckedData();
    }

    /**
     * 分摊余额支付金额
     */
    private void calculateBalancePaymentAmounts(RemoteSaveOrderVO orderVo) {
        TrdOrderSaveVO orderSaveVO = orderVo.getOrderSaveVo();
        // 支付金额，已扣减了余额支付的
        BigDecimal payAmt = orderSaveVO.getPayAmt();
        BigDecimal totalPayAmt = orderSaveVO.getTotalPayAmt();
        if (payAmt.compareTo(BigDecimal.ZERO) == 0) {
            // 设置子单余额支付金额
            for (TrdSupplierOrderSaveVO supplierOrder : orderVo.getSupplierOrderSaveVOs()) {
                BigDecimal subPayAmt = supplierOrder.getSubPayAmt();
                supplierOrder.setSubPayBalanceAmt(subPayAmt); // 全部使用余额支付
                supplierOrder.setSubPayAmt(BigDecimal.ZERO); // 剩余待支付金额为0
            }
        } else {
            // 按比例分配子单余额支付金额
            BigDecimal payBalanceAmt = orderSaveVO.getPayBalanceAmt();
            List<TrdSupplierOrderSaveVO> supplierOrders = orderVo.getSupplierOrderSaveVOs();
            for (int i = 0; i < supplierOrders.size(); i++) {
                TrdSupplierOrderSaveVO supplierOrder = supplierOrders.get(i);
                // 子订单支付金额
                BigDecimal subPayAmt = supplierOrder.getSubPayAmt();
                BigDecimal subPayBalanceAmt;
                if (i == supplierOrders.size() - 1) {
                    // 最后一个子单，使用剩余余额
                    subPayBalanceAmt = payBalanceAmt;
                } else {
                    // 按比例分配
                    subPayBalanceAmt = subPayAmt.divide(totalPayAmt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(payBalanceAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                    payBalanceAmt = payBalanceAmt.subtract(subPayBalanceAmt);
                }
                supplierOrder.setSubPayBalanceAmt(subPayBalanceAmt);
                supplierOrder.setSubPayAmt(subPayAmt.subtract(subPayBalanceAmt));
            }
        }
    }

}
