package com.zksr.portal.service.impl.mall;

import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.complain.ComplainApi;
import com.zksr.member.api.complain.MemComplainVO;
import com.zksr.member.api.complain.dto.MemComplainDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.ComplainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商城购物车服务
 * @date 2024/3/26 17:16
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class ComplainServiceImpl implements ComplainService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private ComplainApi complaintApi;

    @Autowired
    private IPortalCacheService portalCacheService;


    /**
     * 查询用户的投诉列表
     *
     * @param memComplainVO
     * @return
     */
    @Override
    public PageResult<MemComplainVO> getComplainList(MemComplainVO memComplainVO) {
        Long memberId = MallSecurityUtils.getMemberId();
        memComplainVO.setMemberId(memberId.toString());
        PageResult<MemComplainVO> pageResult = complaintApi.getComplainList(memComplainVO);
        return pageResult;
    }

    /**
     * 新增投诉信息
     *
     * @param memComplainVO
     * @return
     */
    @Override
    public CommonResult<String> insertComplain(MemComplainDTO memComplainDTO) {
        log.info("新增投诉信息Serviceimpl");
        LoginMember loginMember = MallSecurityUtils.getLoginMember();
        memComplainDTO.setBranchId(loginMember.getBranchId());
        BranchDTO branchDto = portalCacheService.getBranchDto(loginMember.getBranchId());
        memComplainDTO.setSysCode(branchDto.getSysCode());
        memComplainDTO.setStatus(StringPool.ZERO);
        MemberDTO member = loginMember.getMember();
        String memberName = member.getMemberName();
        memComplainDTO.setCreateBy(memberName);
        CommonResult<String> result = complaintApi.addComplain(memComplainDTO);
        //把api调用放回的错误抛出来
        return result;
    }

    /**
     * 查询投诉详情
     *
     * @param complainId
     * @return
     */
    @Override
    public MemComplainVO getComplainBycomplainId(String complainId) {
        MemComplainVO complainVO = complaintApi.getComplainById(complainId);
        //complainVO.setStrComplainId(String.valueOf(complainVO.getComplainId()));
        return complainVO;
    }
}
