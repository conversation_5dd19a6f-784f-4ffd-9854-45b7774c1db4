package com.zksr.portal.service.impl.handler.transaction;

import com.zksr.common.core.transaction.TransactionHandler;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.TrdOrderResDto;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;

/**
 * 创建订单事务处理器
 */
public class SaveOrderTransactionHandler implements TransactionHandler<TrdOrderResDto> {

    public SaveOrderTransactionHandler(RemoteSaveOrderVO orderVo) {
        this.orderVo = orderVo;
    }

    private final RemoteSaveOrderVO orderVo;
    private TrdOrderResDto resDto;
    private final OrderApi orderApi = SpringUtils.getBean(OrderApi.class);

    @Override
    public void doBusiness() {
        resDto = orderApi.saveOrder(orderVo).getCheckedData();
    }

    @Override
    public void rollback() {
        Long orderId = resDto.getOrderId();
        orderApi.deleteOrder(orderId);
    }

    @Override
    public TrdOrderResDto getResult() {
        return resDto;
    }
}
