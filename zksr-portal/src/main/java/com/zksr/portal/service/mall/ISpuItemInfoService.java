package com.zksr.portal.service.mall;

import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.controller.mall.vo.SpuDetailRespVO;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.system.api.domain.SysDictData;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2024/12/30
 * @desc
 */
public interface ISpuItemInfoService {

    /**
     * 渲染商品列表数据, 组合商品信息
     */
    void renderItemList(SkuPageRespVO product, BranchDTO branchDTO);

    /**
     * 渲染商品分润比例
     */
    void renderOddRate(SpuDetailRespVO detailReslut, SpuDTO spuDTO, BranchDTO branchDto);

    /**
     * 渲染商品详情数据 (本地)
     */
    SpuDetailRespVO getItemInfo(BranchDTO branchDto, AreaItemDTO areaItemDTO);

    /**
     * 渲染商品详情数据 (全国)
     */
    SpuDetailRespVO getItemInfo(BranchDTO branchDto, SupplierItemDTO supplierItemDTO);

    /**
     * 渲染入驻商商品集合, 与促销活动关系
     * @param branchDTO                 门店信息
     * @param itemList                  入驻商商品集合
     * @param supplierActivityList      入驻商促销活动集合
     */
    void renderActivityItemList(BranchDTO branchDTO, List<SkuPageRespVO> itemList, List<SupplierActivityDTO> supplierActivityList);

    /**
     * 渲染入驻商商品详情, 具体促销活动
     * @param branch        门店
     * @param detailReslut  商品详情
     */
    void renderActivityItemDetail(BranchDTO branch, SpuDetailRespVO detailReslut);

    /**
     * 获取要货单商品详情
     * @param areaItem      本地上架商品
     * @param esItem        要货单ES数据
     * @param branchDTO     门店数据
     * @return  要货单商品详情
     */
    YhPageSupplierGroupItemVO getYhItemInfoVO(AreaItemDTO areaItem, EsYhProduct esItem, BranchDTO branchDTO);

    /**
     * 通过订单验证信息, 转换促销验证商品信息
     * @param branch            门店
     * @param supplierItem      全国上架信息
     * @return  促销验证数据
     */
    ActivityVerifyItemDTO buildActivityVerifyItemByCouponOrderValidate(BranchDTO branch, SupplierItemDTO supplierItem, OrderValidItemDTO orderValidItem);

    /**
     * 通过订单验证信息, 转换促销验证商品信息
     * @param branch            门店
     * @param areaItem          本地上架信息
     * @return  促销验证数据
     */
    ActivityVerifyItemDTO buildActivityVerifyItemByCouponOrderValidate(BranchDTO branch, AreaItemDTO areaItem, OrderValidItemDTO orderValidItem);
}
