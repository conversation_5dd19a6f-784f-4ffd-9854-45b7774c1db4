package com.zksr.portal.service.impl.price;

import cn.hutool.core.lang.Assert;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.portal.controller.mall.vo.ActivityVO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.mall.TradeOrderConvert;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.utils.ActivityScopeUtil;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年01月04日 10:33
 * @description: 组合促销活动价格计算
 */
@Component
@Order(TradePriceCalculatorService.ORDER_CB_ACTIVITY)
@Slf4j
@SuppressWarnings("all")
public class TradeCbActivityPriceCalculatorServiceImpl implements TradePriceCalculatorService{
    @Autowired
    private IPortalCacheService portalCacheService;
    @Autowired
    private RedisActivityService redisActivityService;
    @Autowired
    private RedisStockService redisStockService;
    @Autowired
    private RedisService redisService;

    @Override
    public void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result) {
        /**
         * 如果传递数据为空，不计算
         */
        if (ToolUtil.isEmpty(param) || ToolUtil.isEmpty(result) || ToolUtil.isEmpty(param.getActivitys())){
            return;
        }

        // 查询组合促销活动
        List<PrmActivityDTO> prmActivityList = param.getActivitys().stream()
                .filter(activityVO -> activityVO.getActivityType().equalsIgnoreCase(TrdDiscountTypeEnum.CB.getType()))
                .map(activityVO -> { return portalCacheService.getActivityDto(activityVO.getActivityId()); })
                .filter(ToolUtil.distinctByKey(ActivityDTO::getActivityId)) // 过滤去重
                .sorted(Comparator.comparing(PrmActivityDTO::getSpuScope, Comparator.nullsFirst(Integer::compareTo)))
                .collect(Collectors.toList());

        prmActivityList.forEach(prmActivity -> {
            // 2、获取活动适用范围  城市, 渠道, 门店
            SupplierActivityDTO supplierActivity = ActivityConvert.INSTANCE.convert(prmActivity);
            Boolean boo = redisActivityService.validateActivity(supplierActivity, param.getBranchDto());
            try {
                // 组合商品活动校验未通过直接报错
                Assert.isTrue(
                        boo,
                        StringUtils.format("促销单【{}】验证数据未通过！", prmActivity.getPrmSheetNo())
                );
            } catch (IllegalArgumentException e) {
                throw new ServiceException(e.getMessage());
            }
            // 3、获取组合商品促销活动规则
            Set<CbRuleDTO> cbRuleList = portalCacheService.getActivityRuleDto(prmActivity.getActivityId(), TrdDiscountTypeEnum.CB.getType());

            TradePriceCalculateResp.Promotion promotion = new TradePriceCalculateResp.Promotion();
            promotion.setId(prmActivity.getActivityId())
                    .setName(prmActivity.getActivityName())
                    .setType(TrdDiscountTypeEnum.CB.getType())
                    .setSupplierId(prmActivity.getSupplierId())
                    .setPromotionItems(new ArrayList<>());


            List<ActivityVO> activityVOList = param.getActivitys().stream().filter(activityVO -> activityVO.getActivityId().equals(prmActivity.getActivityId())).collect(Collectors.toList());
            Map<Long, Long> processedMap = new HashMap();
            result.getItems().stream()
                    .filter(supplierOrder -> supplierOrder.getSupplierId().equals(prmActivity.getSupplierId()))
                    .forEach(supplierOrder -> {
                        activityScope(prmActivity, cbRuleList, supplierOrder, processedMap, promotion, activityVOList);
                    });
            if (promotion.getPromotionItems().size() > 0) {
                result.getPromotions().add(promotion);
            }
        });
    }

    @Override
    public void updateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        resp.getPromotions().stream()
                .filter(promotion -> Objects.equals(promotion.getType(), TrdDiscountTypeEnum.CB.getType()))
                .forEach(promotion -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(promotion.getId());
                    Long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());
                    promotion.getPromotionItems()
                            .stream().collect(Collectors.groupingBy(TradePriceCalculateResp.PromotionItem::getActivityRuleId))
                            .forEach((key, value) -> {
                                // 增加活动商品已使用数量
                                redisActivityService.setCbSaleNum(promotion.getId(), key, BigDecimal.valueOf(value.get(NumberPool.INT_ZERO).getCount()), time, TimeUnit.SECONDS);
                            });

                    if (Objects.nonNull(activity.getTimesRule())) {
                        Long ruleTime = time; //默认时间到活动截止时间到期  仅一次
                        if (activity.getTimesRule() == NumberPool.INT_ZERO) { // 每日一次
                            ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
                        }
                        // 标记活动已经达到限制
                        redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(promotion.getId(), orderVo.getOrderSaveVo().getBranchId()), StringPool.ONE, ruleTime, TimeUnit.SECONDS);
                    }
                });
    }

    @Override
    public void undoUpdateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        resp.getPromotions().stream()
                .filter(promotion -> Objects.equals(promotion.getType(), TrdDiscountTypeEnum.CB.getType()))
                .forEach(promotion -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(promotion.getId());
                    Long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());
                    promotion.getPromotionItems()
                            .stream().collect(Collectors.groupingBy(TradePriceCalculateResp.PromotionItem::getActivityRuleId))
                            .forEach((key, value) -> {
                                // 减少活动商品已使用数量
                                redisActivityService.setCbSaleNum(promotion.getId(), key, BigDecimal.valueOf(value.get(NumberPool.INT_ZERO).getCount()).negate(), null, null);
                            });

                    if (Objects.nonNull(activity.getTimesRule())) {
                        Long ruleTime = time; //默认时间到活动截止时间到期  仅一次
                        if (activity.getTimesRule() == NumberPool.INT_ZERO) { // 每日一次
                            ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
                        }
                        // 删除活动已经达到限制的标记
                        redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(promotion.getId(), orderVo.getOrderSaveVo().getBranchId()));
                    }
                });
    }


    /**
     *  验证活动范围
     * @param prmActivity 活动信息
     * @param ruleList 活动规则信息
     * @param supplierOrder 入驻商商品信息
     * @param processedMap 已验证过的商品信息
     */
    private void activityScope(PrmActivityDTO prmActivity, Set<CbRuleDTO> ruleList, TradePriceCalculateResp.OrderItem supplierOrder, Map<Long, Long> processedMap,
                               TradePriceCalculateResp.Promotion promotion, List<ActivityVO> activityVOList) {
        // 获取满足本次组合商品促销的商品数据
        List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList = getFilteredSupplierOrderList(supplierOrder, prmActivity);
        if (ToolUtil.isEmpty(supplierOrderList)) // 没有满足条件的数据
            return;

        // 满足本次商品条件的组合商品促销规则
        List<CbRuleDTO> cbRuleDTOList = getSatisfyConditionRule(ruleList, supplierOrderList);
        if (ToolUtil.isEmpty(cbRuleDTOList)) // 没有满足条件的规则
            return;

        // 当前组合优惠金额计算
        priceCalculator(prmActivity, cbRuleDTOList, promotion, supplierOrderList);

        // 更新processedMap以标记已处理的项
        updateProcessedMap(supplierOrderList, processedMap, prmActivity);

    }

    /**
     * 获取满足本次组合商品促销的商品数据
     * @param supplierOrder
     * @param activity
     * @param processedMap
     * @return
     */
    private List<TradePriceCalculateResp.OrderItem.SupplierItem> getFilteredSupplierOrderList(TradePriceCalculateResp.OrderItem supplierOrder, PrmActivityDTO activity) {
        List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList = supplierOrder.getSupplierItems();

        // 获取促销活动参与商品范围限定
        List<ActivitySpuScopeDTO> scopeList = portalCacheService.getActivitySpuScopeList(activity.getActivityId());

        // 判断商品是否在活动参与范围内 且 商品类型是组合商品
        supplierOrderList = supplierOrderList.stream()
                .filter(item -> ActivityScopeUtil.validateSpuScope(TradeOrderConvert.INSTANCE.convertActivityValidVO(item), scopeList) && Objects.equals(item.getGoodsType(), NumberPool.INT_ONE))
                .collect(Collectors.toList());

        return supplierOrderList;
    }


    /**
     * 根据条件查询出满足条件的组合商品促销规则
     * @param ruleList
     * @param totalQty
     * @return
     */
    private List<CbRuleDTO> getSatisfyConditionRule(Set<CbRuleDTO> ruleList, List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList){
        Map<Long, TradePriceCalculateResp.OrderItem.SupplierItem> itemMap = CollectionUtils.convertMap(supplierOrderList, TradePriceCalculateResp.OrderItem.SupplierItem::getSpuCombineId);

        // 命中的规则数据
        return ruleList.stream()
                .filter(rule -> itemMap.containsKey(rule.getSpuCombineId()))
                .collect(Collectors.toList());
    }

    /**
     *  计算活动商品的优惠金额
     * @param activity 活动信息
     * @param supplierOrderList 入驻商商品信息
     * @param promotion 营销信息
     */
    private void priceCalculator(PrmActivityDTO prmActivity, List<CbRuleDTO> cbRuleDTOList, TradePriceCalculateResp.Promotion promotion, List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList) {
        supplierOrderList.forEach(item -> {
            cbRuleDTOList.stream()
                    .filter(rule -> Objects.equals(item.getSpuCombineId(), rule.getSpuCombineId()))
                    .forEach(rule -> {
                        SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(item.getSpuCombineId());
                        /**
                         * 组合商品明细数据优惠金额计算
                         */
                        // 组合商品下的明细商品原总金额
                        BigDecimal originalTotalAmt = item.getSpuCombineSkuVOList().stream().map(combineSku -> {
                            return combineSku.getSuggestPrice().multiply(BigDecimal.valueOf(combineSku.getQty())).multiply(BigDecimal.valueOf(item.getCount()));
                        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        // 组合促销优惠总金额
                        BigDecimal deductionTotalAmt = originalTotalAmt.subtract(item.getPayPrice());

                        // 组合促销已扣减金额
                        final BigDecimal[] deductionAmt = {BigDecimal.ZERO};
                        // 执行次数
                        AtomicInteger i = new AtomicInteger();
                        item.getSpuCombineSkuVOList().forEach(combineSku -> {
                            /** 组合商品明细数据价格计算逻辑
                             * 总金额：100
                             * 原总金额：312
                             * 优惠金额：212
                             * 商品1优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
                             * 商品2优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
                             * 商品3优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
                             * 商品4优惠金额（最后一个商品的优惠金额是扣减的） ： 212 - 67.95 - 67.95 -67.95 = 8.15
                             */
                            i.getAndIncrement();
                            BigDecimal discountAmt = BigDecimal.ZERO; // 当前子商品组合促销优惠金额
                            if (i.get() == item.getSpuCombineSkuVOList().size()) { // 最后一个的金额不是计算处理的，是扣减出来的
                                // 当前子商品组合促销优惠金额
                                discountAmt = deductionTotalAmt.subtract(deductionAmt[0]).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                            } else {
                                // 当前子商品组合促销优惠金额
                                discountAmt = combineSku.getSuggestPrice().multiply(BigDecimal.valueOf(combineSku.getQty())).multiply(BigDecimal.valueOf(item.getCount()))
                                        .divide(originalTotalAmt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP)
                                        .multiply(deductionTotalAmt)
                                        .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                            }
                            deductionAmt[0] = deductionAmt[0].add(discountAmt);
                            combineSku.setDiscountAmt(discountAmt);

                            // 写入优惠金额信息
                            setSupplierItemActivity(rule, promotion, item, combineSku, discountAmt);
                        });

                        // 活动已购总数量
                        BigDecimal totalSaleQty = redisActivityService.getCbSaleNum(prmActivity.getActivityId(), rule.getCbRuleId());
                        Integer total = ToolUtil.isEmptyReturn(spuCombineDTO.getTotalLimit(), 0) > 0 ? ToolUtil.isEmptyReturn(spuCombineDTO.getTotalLimit(), 0) :  Integer.MAX_VALUE;
                        try {
                            Assert.isTrue(
                                    BigDecimal.valueOf(total).compareTo(totalSaleQty.add(BigDecimal.valueOf(item.getCount()))) >= 0,
                                    StringUtils.format("组合商品促销活动组合商品【{}】总限购数量【{}】，已购数量【{}】，超出限购数量！",
                                            prmActivity.getActivityName(),
                                            spuCombineDTO.getTotalLimit(),
                                            totalSaleQty)
                                    );
                        } catch (IllegalArgumentException e) {
                            throw new ServiceException(e.getMessage());
                        }
                    });
        });
    }


    /**
     * 写入订单营销信息
     * @param bgRuleDTOList 活动规则
     * @param promotion 营销信息
     */
    private void setSupplierItemActivity (CbRuleDTO rule, TradePriceCalculateResp.Promotion promotion, TradePriceCalculateResp.OrderItem.SupplierItem item, SpuCombineSkuVO combineSku, BigDecimal discountAmt) {
        TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
        promotion.getPromotionItems().add(promotionItem);
        promotionItem.setSkuId(combineSku.getSkuId()); // 组合商品下明细商品的SKUID
        promotionItem.setActivityDiscountAmt(discountAmt); // 优惠金额
        promotionItem.setActivityRuleId(rule.getCbRuleId()); // 规则ID
        promotionItem.setDiscountCondition(String.valueOf(rule.getSpuCombineId())); // 活动当前选择规则的条件
        promotionItem.setCount(item.getCount()); // 用于订单下单成功扣减活动数量
        // 这里需要组合下UUID
        promotionItem.setUuIdNo(StringUtils.format("{}_{}_{}", item.getUuIdNo(), combineSku.getSkuId(), combineSku.getSkuUnitType()));
        promotionItem.setId(promotion.getId());
    }


    /**
     * 更新processedMap以标记已处理的项
     * @param supplierOrderList
     * @param processedMap
     */
    private void updateProcessedMap(List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList, Map<Long, Long> processedMap, PrmActivityDTO prmActivity) {
        supplierOrderList.forEach(supplierItem -> {
            supplierItem.setActivityIdInfo(supplierItem.getActivityIdInfo()+";"+prmActivity.getActivityId()); // 商品参与过的活动ID
            // 当前数据已处理处理过，跳过本次操作
            if (processedMap.containsKey(supplierItem.getSpuCombineId())) {
                return;
            }

            processedMap.put(supplierItem.getSpuCombineId(), supplierItem.getSpuCombineId());
        });
    }





}
