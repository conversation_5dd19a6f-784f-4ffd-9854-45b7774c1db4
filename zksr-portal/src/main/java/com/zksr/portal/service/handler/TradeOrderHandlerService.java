package com.zksr.portal.service.handler;

import com.zksr.portal.controller.mall.vo.CreateOrderRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.trade.api.order.dto.TrdOrderResDto;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024年03月28日 19:54
 * @description: 订单活动特殊逻辑处理器 handler 接口
 * 提供订单生命周期钩子接口；订单创建前、订单创建后、订单支付后、订单取消
 */
@Component
public interface TradeOrderHandlerService {
    /**
     * 订单创建前
     *
     * @param orderVo 订单
     */
    default void beforeOrderCreate(RemoteSaveOrderVO orderVo) {}


    /**
     * 订单创建后
     *
     * @param request
     */
    default void afterOrderCreate(CreateOrderRequest request, TrdOrderResDto orderResDto, RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {}


    /**
     * 订单创建异常时
     *
     * @param orderVo
     */
    default void errorOrderCreate(RemoteSaveOrderVO orderVo) {}
}
