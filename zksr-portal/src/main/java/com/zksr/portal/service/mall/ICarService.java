package com.zksr.portal.service.mall;

import com.zksr.portal.controller.mall.vo.car.*;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.trade.api.car.vo.CarSaveReqVO;
import com.zksr.trade.api.car.vo.CarSelectedReqVO;
import com.zksr.trade.api.car.vo.CarTotalRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/3/26
 * @desc    购物车服务
 */
public interface ICarService {

    /**
     * 加入购物车
     * @param saveReqVO
     * @return 本次加入购物车ID
     */
    String add(CarSaveReqVO saveReqVO);

    /**
     * 更新购物车数量
     * @param saveReqVO
     * @return
     */
    void updateQty(CarSaveQtyReqVO saveReqVO);

    /**
     * 更新购物车选中
     * @param selectedReqVO
     * @return 选中的carId
     */
    List<String> updateSelected(CarSelectedReqVO selectedReqVO);

    /**
     * @param selectedReqVO
     * @return 删除购物车
     */
    List<String> removeCar(CarRemoveReqVO selectedReqVO);

    /**
     * @param countReqVO
     * @return 购物车选中数据合计
     */
    CarCountRespVO getCount(CarCountReqVO countReqVO);

    /**
     * @param countReqVO
     * @return 购物车选中库存数
     */
    CarCountStatusRespVO getSelectStock(CarCountStatusReqVO countReqVO);

    /**
     * 获取购物车统计
     * @param totalReqVO
     * @return
     */
    CarTotalRespVO getTotal(CarTotalReqVO totalReqVO);

    /**
     * @param pageReqVO 分页数据
     * @return  获取购物车分页数据
     */
    CarPageRespVO getPage(CarPageReqVO pageReqVO);

    /**
     * 判断当前购物车商品是否存在
     * @param carIdDTO  购物车ID
     * @return
     */
    Boolean carIdExist(AppCarIdDTO carIdDTO);

    /**
     * 从数据库初始化购物车
     */
    void init(Long branchId);

    /**
     * 获取业务员推荐信息
     * @param branchId  门店
     * @return  推荐信息
     */
    CarRecommendRespVO getRecommend(Long branchId);

    /**
     * 一键删除业务员推荐商品数据
     * @param branchId  门店ID
     */
    void removeRecommend(Long branchId);

    /**
     * 一键移除业务员推荐标识
     * @param branchId  门店ID
     */
    void removeRecommendFlag(Long branchId);

    /**
     * 获取业务员推荐商品列表
     * @param branchId  门店ID
     * @return  购物车标准列表数据
     */
    CarPageRespVO getRecommendList(Long branchId);

    /**
     * 仅关闭推荐弹窗
     * @param branchId  门店ID
     */
    void closeRecommendWindow(Long branchId);

    CarValidateActivityRespVO validateActivity(CarValidateActivityReqVO pageReqVO);
}
