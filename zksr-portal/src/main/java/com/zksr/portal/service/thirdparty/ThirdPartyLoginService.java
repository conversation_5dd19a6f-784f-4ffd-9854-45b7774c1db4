package com.zksr.portal.service.thirdparty;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.portal.controller.mall.vo.AuthLoginResponse;
import com.zksr.portal.controller.mall.vo.UserLoginRequest;

import java.util.function.BiFunction;

public interface ThirdPartyLoginService {

    /**
     * 支持的第三方登录来源
     */
    boolean support(String loginFrom);

    /**
     * 第三方登录
     * @param request       登录请求
     * @param genTokenFunc  获取token的回调方法
     * @return              返回token和用户信息
     */
    AuthLoginResponse login(UserLoginRequest request, BiFunction<MemberDTO, BranchDTO, AuthLoginResponse> genTokenFunc);
}
