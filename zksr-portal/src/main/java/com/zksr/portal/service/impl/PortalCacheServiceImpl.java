package com.zksr.portal.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alicp.jetcache.Cache;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.Colonel<PERSON>pi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.product.api.areaChannelPrice.AreaChannelPriceApi;
import com.zksr.product.api.areaClass.AreaClassApi;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.BrandApi;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.CatgoryApi;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.material.MaterialApi;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.saleClass.SaleClassApi;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.SkuPriceApi;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.api.spu.SpuApi;
import com.zksr.product.api.spu.dto.SkuUnitGroupDTO;
import com.zksr.product.api.spu.dto.SkuUnitGroupKeyDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.spuCombine.SpuCombineApi;
import com.zksr.product.api.supplierGroupPrice.SupplierGrouplPriceApi;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.ActivityApi;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponReceiveScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.ChannelApi;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.DcApi;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.opensource.OpensourceApi;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.page.PagesConfigApi;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.api.partner.PartnerApi;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.PartnerConfigApi;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.HeLiBaoPayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.VisualApi;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.trade.api.hdfk.HdfkApi;
import com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO;
import com.zksr.trade.api.hdfk.vo.BranchHdfkReqVO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.OrderCutAmtDTO;
import com.zksr.trade.api.order.vo.OrderStatusReqVO;
import com.zksr.trade.api.order.vo.OrderStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.xmlbeans.impl.xb.ltgfmt.Code;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

import static com.zksr.common.redis.enums.RedisConstants.DAY_SECONDS;

@Service
@Slf4j
@SuppressWarnings("all")
public class PortalCacheServiceImpl implements IPortalCacheService {

    @Autowired
    private RedisService redisService;

    @Autowired
    private Cache<String, PartnerDto> partnerDtoCache;

    @Autowired
    private Cache<Long, BranchDTO> branchDTOCache;

    @Autowired
    private Cache<Long, AreaDTO> areaDtoCache;

    @Autowired
    private Cache<Long, ChannelDTO> channelDTOCache;

    @Autowired
    private Cache<Long, SupplierDTO> supplierDTOCache;
    @Autowired
    private Cache<Long, DcDTO> dcDtoCache;

    @Autowired
    private Cache<Long, AreaClassDTO> areaClassDtoCache;

    @Autowired
    @Qualifier("areaClassBranchListDtoCache")
    private Cache<Long, List<AreaClassDTO>> areaClassBranchListDtoCache;

    @Autowired
    @Qualifier("areaClassAreaChannelListDtoCache")
    private Cache<String, List<AreaClassDTO>> areaClassAreaChannelListDtoCache;

    @Autowired
    private Cache<Long, CatgoryDTO> catgoryDtoCache;

    @Autowired
    @Qualifier("catgorySysCodeListDtoCache")
    private Cache<Long, List<CatgoryDTO>> catgorySysCodeListDtoCache;

    @Autowired
    @Qualifier("catgorySupplierListDtoCache")
    private Cache<Long, List<CatgoryDTO>> catgorySupplierListDtoCache;

    @Autowired
    private Cache<Long, SaleClassDTO> saleClassDtoCache;

    @Autowired
    private Cache<Long, List<SaleClassDTO>> saleClassDtoListCache;

    @Autowired
    private Cache<Long, BrandDTO> brandDtoCache;

    @Autowired
    private Cache<Long, BasicSettingPolicyDTO> basicSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, AreaItemDTO> areaItemDTOCache;

    @Autowired
    private Cache<Long, SupplierItemDTO> supplierItemDTOCache;

    @Autowired
    private Cache<Long, SkuDTO> skuDTOCache;

    @Autowired
    private Cache<Long, SpuDTO> spuDTOCache;

    @Autowired
    private Cache<String, CatgoryRateDTO> catgoryRateCache;

    @Autowired
    private Cache<Long, AppletBaseConfigDTO> appletBaseConfigDTOCache;

    @Autowired
    @Qualifier("areaSalePriceCodeCache")
    private Cache<String, Integer> areaSalePriceCodeCache;

    @Autowired
    @Qualifier("supplierSalePriceCodeCache")
    private Cache<String, Integer> supplierSalePriceCodeCache;

    @Autowired
    private Cache<String, SkuPriceDTO> skuPriceDTOByAreaTypeCache;

    @Autowired
    private Cache<Long, ColonelDTO> colonelDTOCache;

    @Autowired
    private Cache<Long, CouponTemplateDTO> couponTemplateCache;

    @Autowired
    private Cache<Long, List<CouponSpuScopeDTO>> couponSpuScopeCache;

    @Autowired
    private Cache<Long, List<CouponReceiveScopeDTO>> couponReceiveScopeCache;

    @Autowired
    private Cache<Long,AppletAgreementPolicyDTO> appletAgreementPolicyDTOCache;

    @Autowired
    private Cache<String, List<PagesConfigDTO>> pagesConfigCache;

    @Autowired
    private Cache<Long, PayConfigDTO> payConfigDTOCache;

    @Autowired
    private Cache<String, AccAccountDTO> accountCache;

    @Autowired
    private Cache<Long, List<SupplierActivityDTO>> supplierActivityCache;

    @Autowired
    private Cache<Long, List<ActivitySpuScopeDTO>> activitySpuScopeCache;

    @Autowired
    private Cache<Long, List<ActivityBranchScopeDTO>> activityBranchScopeCache;

    @Autowired
    private Cache<Long, List<ActivityCityScopeDTO>> activityCityScopeCache;

    @Autowired
    private Cache<Long, List<ActivityChannelScopeDTO>> activityChannelScopeCache;

    @Autowired
    private Cache<Long, List<ActivitySupplierScopeDTO>> activitySupplierScopeCache;

    @Autowired
    private Cache<Long, List<SkRuleDTO>> activitySkRuleCache;

    @Autowired
    private Cache<Long, List<SpRuleDTO>> activitySpRuleCache;

    @Autowired
    private Cache<Long, List<BgRuleDTO>> activityBgRuleCache;

    @Autowired
    private Cache<Long, List<FgRuleDTO>> activityFgRuleCache;

    @Autowired
    private Cache<Long, PrmActivityDTO> activityDTOCache;

    @Autowired
    private Cache<Long, HdfkNoPaymentTotalDTO> hdfkNoPaymentTotalCache;

    @Autowired
    private Cache<String, List<SkuUnitGroupDTO>> skuUnitGroupDTOCache;

    @Autowired
    private Cache<Long, PartnerMiniSettingPolicyDTO> partnerMiniSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, OrderStatusVO> orderStatusTotalCache;

    @Autowired
    private Cache<Long, MemberDTO> memberDTOCache;

    @Autowired
    @Qualifier("visualSettingMasterBySupplierIdCache")
    private Cache<Long, VisualSettingMasterDto> visualSettingMasterBySupplierIdCache;

    @Autowired
    private Cache<Long, HeLiBaoPayConfigDTO> heLiBaoPayConfigCache;

    @Autowired
    private Cache<Long, OrderSettingPolicyDTO> orderSettingPolicyCache;

    @Autowired
    private Cache<Long, OpensourceDto> opensourceDtoByMerchantIdCache;

    @Autowired
    private Cache<Long, SupplierOtherSettingPolicyDTO> partnerSupplierOtherSettingPolicyDTOCache;

    @Autowired
    private Cache<Long, SpuCombineDTO> spuCombineCache;

    @Autowired
    private Cache<String, MaterialCacheVO> materialCache;

    @Autowired
    private Cache<Long, SearchConfigDTO> searchConfigCache;

    @Autowired
    private Cache<String, OrderCutAmtDTO> orderCutAmtCache;

    @Autowired
    private PartnerApi partnerApi;

    @Autowired
    private AreaApi areaApi;

    @Autowired
    private ChannelApi channelApi;

    @Autowired
    private BranchApi branchApi;

    @Autowired
    private SupplierApi supplierApi;

    @Autowired
    private DcApi dcApi;

    @Autowired
    private AreaClassApi areaClassApi;

    @Autowired
    private CatgoryApi catgoryApi;

    @Autowired
    private SaleClassApi saleClassApi;

    @Autowired
    private BrandApi brandApi;

    @Autowired
    private PartnerPolicyApi partnerPolicyApi;

    @Autowired
    private AreaItemApi areaItemApi;

    @Autowired
    private SupplierItemApi supplierItemApi;

    @Autowired
    private SpuApi spuApi;

    @Autowired
    private SkuApi skuApi;

    @Autowired
    private PartnerConfigApi partnerConfigApi;

    @Autowired
    private AreaChannelPriceApi areaChannelPriceApi;

    @Autowired
    private SupplierGrouplPriceApi supplierGrouplPriceApi;

    @Autowired
    private SkuPriceApi skuPriceApi;

    @Autowired
    private ColonelApi colonelApi;

    @Autowired
    private CouponApi couponApi;

    @Autowired
    private PagesConfigApi pagesConfigApi;

    @Autowired
    private AccountApi accountApi;

    @Resource
    private ActivityApi activityApi;

    @Resource
    private HdfkApi hdfkApi;

    @Resource
    private OrderApi orderApi;

    @Resource
    private MemberApi memberApi;

    @Resource
    private OpensourceApi opensourceApi;

    @Resource
    private SpuCombineApi spuCombineApi;

    @Resource
    private MaterialApi materialApi;

    private static final String WX_SESSION_KEY = "wx_session_key:";

    //!@缓存 - 总入口
    @PostConstruct
    public void init() {
        //自动load（read through）
        partnerDtoCache.config().setLoader(this::loadPartnerDtoFromApi);
        branchDTOCache.config().setLoader(this::loadBranchDTOFromApi);
        areaDtoCache.config().setLoader(this::loadAreaDtoFromApi);
        channelDTOCache.config().setLoader(this::loadChannelDTOFromApi);
        supplierDTOCache.config().setLoader(this::loadSupplierDTOFromApi);
        dcDtoCache.config().setLoader(this::loadDcDtoFromApi);
        areaClassDtoCache.config().setLoader(this::loadAreaClassDtoFromApi);
        areaClassBranchListDtoCache.config().setLoader(this::loadAreaClassBranchListDtoFromApi);
        areaClassAreaChannelListDtoCache.config().setLoader(this::loadAreaClassAreaChannelListDtoFromApi);
        catgoryDtoCache.config().setLoader(this::loadCatgoryDtoFromApi);
        catgorySysCodeListDtoCache.config().setLoader(this::loadCatgorySysCodeListDtoFromApi);
        catgorySupplierListDtoCache.config().setLoader(this::loadCatgorySupplierListDtoCacheFromApi);
        saleClassDtoCache.config().setLoader(this::loadSaleClassDtoFromApi);
        saleClassDtoListCache.config().setLoader(this::loadSaleClassDtoListFromApi);
        brandDtoCache.config().setLoader(this::loadBrandDtoFromApi);
        basicSettingPolicyDTOCache.config().setLoader(this::loadBasicSettingPolicyDtoFromApi);
        areaItemDTOCache.config().setLoader(this::loadAreaItemDtoFromApi);
        supplierItemDTOCache.config().setLoader(this::loadSupplierItemDtoFromApi);
        spuDTOCache.config().setLoader(this::loadSpuDtoFromApi);
        skuDTOCache.config().setLoader(this::loadSkuDtoFromApi);
        appletBaseConfigDTOCache.config().setLoader(this::loadAppletBaseConfigDtoFromApi);
        areaSalePriceCodeCache.config().setLoader(this::loadAreaChannelSalePriceCodeFromApi);
        supplierSalePriceCodeCache.config().setLoader(this::loadSupplierGroupSalePriceCodeFromApi);
        skuPriceDTOByAreaTypeCache.config().setLoader(this::loadSkuPriceDTOByAreaTypeFromApi);
        catgoryRateCache.config().setLoader(this::loadCatgoryRateCache);
        colonelDTOCache.config().setLoader(this::loadColonelDtoFromApi);
        couponTemplateCache.config().setLoader(this::loadCouponTemplateFromApi);
        couponSpuScopeCache.config().setLoader(this::loadCouponSpuScopeCache);
        couponReceiveScopeCache.config().setLoader(this::loadCouponReceiveScopeCache);
        appletAgreementPolicyDTOCache.config().setLoader(this::loadAppletAgreementPolicyDTOFromApi);
        pagesConfigCache.config().setLoader(this::loadPagesConfig);
        payConfigDTOCache.config().setLoader(this::loadPayConfigDTO);
        accountCache.config().setLoader(this::loadAccount);
        supplierActivityCache.config().setLoader(this::loadSupplierActivityFromApi);
        activitySpuScopeCache.config().setLoader(this::loadActivitySpuScopeListFromApi);
        activityBranchScopeCache.config().setLoader(this::loadActivityBranchScopeListFromApi);
        activityCityScopeCache.config().setLoader(this::loadActivityCityScopeListFromApi);
        activityChannelScopeCache.config().setLoader(this::loadActivityChannelScopeListFromApi);
        activitySupplierScopeCache.config().setLoader(this::loadActivitySupplierScopeListFromApi);
        activitySkRuleCache.config().setLoader(this::loadActivitySkRuleListFromApi);
        activitySpRuleCache.config().setLoader(this::loadActivitySpRuleListFromApi);
        activityBgRuleCache.config().setLoader(this::loadActivityBgRuleListFromApi);
        activityFgRuleCache.config().setLoader(this::loadActivityFgRuleListFromApi);
        activityDTOCache.config().setLoader(this::loadActivityDTOFromApi);
        hdfkNoPaymentTotalCache.config().setLoader(this::loadHdfkNoPaymentTotalFromApi);
        skuUnitGroupDTOCache.config().setLoader(this::loadSkuUnitGroupFromApi);
        partnerMiniSettingPolicyDTOCache.config().setLoader(this::loadPartnerMiniSettingPolicyDTOFromApi);
        orderStatusTotalCache.config().setLoader(this::loadOrderStatusTotalFromApi);
        memberDTOCache.config().setLoader(this::loadMemberDTOFromApi);
        visualSettingMasterBySupplierIdCache.config().setLoader(this::loadVisualSettingMasterBySupplierIdFromApi);
        heLiBaoPayConfigCache.config().setLoader(this::loadHelibaoConfigCache);
        orderSettingPolicyCache.config().setLoader(this::loadOrderSettingPolicyFromApi);
        opensourceDtoByMerchantIdCache.config().setLoader(this::loadOpensourceByMerchantIdFromApi);
        partnerSupplierOtherSettingPolicyDTOCache.config().setLoader(this::loadPartnerSupplierOtherSettingPolicyDTOFromApi);
        spuCombineCache.config().setLoader(this::loadSpuCombineDTOFromApi);
        materialCache.config().setLoader(this::loadMaterialFromApi);
        searchConfigCache.config().setLoader(this::loadSearchConfigFromApi);
        orderCutAmtCache.config().setLoader(this::loadOrderCutAmtConfigFromApi);
    }

    private OrderCutAmtDTO loadOrderCutAmtConfigFromApi(String toString) {
        return orderApi.getOrderCntAmt(OrderCutAmtDTO.CacheKey.init(toString)).getCheckedData();
    }

    private SpuCombineDTO loadSpuCombineDTOFromApi(Long spuCombineId) {
        return spuCombineApi.getSpuCombine(spuCombineId).getCheckedData();
    }

    private MaterialCacheVO loadMaterialFromApi(String keyInfo) {
        MaterialCacheVO.CacheKey cacheKey = MaterialCacheVO.parseKey(keyInfo);
        return materialApi.getMaterialCache(cacheKey.getApplyType(), cacheKey.getApplyId()).getCheckedData();
    }

    private SearchConfigDTO loadSearchConfigFromApi(Long areaId) {
        return partnerPolicyApi.getSearchConfig(areaId).getCheckedData();
    }


    private OrderSettingPolicyDTO loadOrderSettingPolicyFromApi(Long dcId) {
        return partnerPolicyApi.getOrderSettingPolicy(dcId).getCheckedData();
    }

    private HeLiBaoPayConfigDTO loadHelibaoConfigCache(Long sysCode) {
        return partnerConfigApi.getHeLiBaoConfig(sysCode).getCheckedData();
    }

    private MemberDTO loadMemberDTOFromApi(Long memberId) {
        return memberApi.getMemBerByMemberId(memberId).getCheckedData();
    }

    private OrderStatusVO loadOrderStatusTotalFromApi(Long branchId) {
        // 这里是查询门店订单角标缓存
        return orderApi.getOrderStatus(new OrderStatusReqVO(1, branchId));
    }

    private List<SkuUnitGroupDTO> loadSkuUnitGroupFromApi(String dataKey) {
        String[] split = dataKey.split(StringPool.UNDERSCORE);
        Long spuId = Long.valueOf(split[0]);
        String productType = split[1];
        Long areaId = Long.valueOf(split[2]);
        Long classId = Long.valueOf(split[3]);
        List<SkuUnitGroupDTO> skuUnitList = spuApi.getSkuUnitGroupList(spuId, areaId, classId, productType).getCheckedData();
        if (Objects.isNull(skuUnitList)) {
            skuUnitList = new ArrayList<>();
        }
        return skuUnitList;
    }

    /**
    * @Description: 入驻商管理分类列表
    * @Author: liuxingyu
    * @Date: 2024/5/31 11:04
    */
    private List<CatgoryDTO> loadCatgorySupplierListDtoCacheFromApi(Long supplierId) {
        return catgoryApi.getCatgoryListBySupplierId(supplierId).getCheckedData();
    }

    private List<SpRuleDTO> loadActivitySpRuleListFromApi(Long activityId) {
        return activityApi.getActivitySpRule(activityId).getCheckedData();
    }

    private HdfkNoPaymentTotalDTO loadHdfkNoPaymentTotalFromApi(Long branchId) {
        HdfkNoPaymentTotalDTO checkedData = hdfkApi.getBranchHdfkNoPaymentTotal(
                BranchHdfkReqVO
                        .builder()
                        .branchId(branchId)
                        .build()
        ).getCheckedData();
        if (Objects.isNull(checkedData)) {
            return new HdfkNoPaymentTotalDTO();
        }
        return checkedData;
    }

    private List<BgRuleDTO> loadActivityBgRuleListFromApi(Long activityId) {
        return activityApi.getActivityBgRule(activityId).getCheckedData();
    }

    private List<FgRuleDTO> loadActivityFgRuleListFromApi(Long activityId) {
        return activityApi.getActivityFgRule(activityId).getCheckedData();
    }

    private List<SkRuleDTO> loadActivitySkRuleListFromApi(Long activityId) {
        return activityApi.getActivitySkRule(activityId).getCheckedData();
    }

    private List<ActivitySpuScopeDTO> loadActivitySpuScopeListFromApi(Long activityId) {
        List<ActivitySpuScopeDTO> result = activityApi.getActivitySpuScopeList(activityId).getCheckedData();
        if (Objects.isNull(result)) {
            result = new ArrayList<>();
        }
        return result;
    }

    private List<ActivityBranchScopeDTO> loadActivityBranchScopeListFromApi(Long activityId) {
        List<ActivityBranchScopeDTO> result = activityApi.getActivityBranchScopeList(activityId).getCheckedData();
        if (Objects.isNull(result)) {
            result = new ArrayList<>();
        }
        return result;
    }

    private List<ActivityCityScopeDTO> loadActivityCityScopeListFromApi(Long activityId) {
        List<ActivityCityScopeDTO> result = activityApi.getActivityCityScopeList(activityId).getCheckedData();
        if (Objects.isNull(result)) {
            result = new ArrayList<>();
        }
        return result;
    }

    private List<ActivityChannelScopeDTO> loadActivityChannelScopeListFromApi(Long activityId) {
        List<ActivityChannelScopeDTO> result = activityApi.getActivityChannelScopeList(activityId).getCheckedData();
        if (Objects.isNull(result)) {
            result = new ArrayList<>();
        }
        return result;
    }

    private List<ActivitySupplierScopeDTO> loadActivitySupplierScopeListFromApi(Long activityId) {
        List<ActivitySupplierScopeDTO> result = activityApi.getActivitySupplierScopeList(activityId).getCheckedData();
        if (Objects.isNull(result)) {
            result = new ArrayList<>();
        }
        return result;
    }

    private List<SupplierActivityDTO> loadSupplierActivityFromApi(Long supplierId) {
        List<SupplierActivityDTO> result = activityApi.getSupplierActivity(supplierId).getCheckedData();
        if (Objects.isNull(result)) {
            result = new ArrayList<>();
        }
        return result;
    }

    /**
    * @Description: 根据平台编号获取平台管理分类
    * @Author: liuxingyu
    * @Date: 2024/5/7 11:10
    */
    private List<CatgoryDTO> loadCatgorySysCodeListDtoFromApi(Long sysCode) {
        return catgoryApi.getListBySysCode(sysCode).getCheckedData();
    }

    private AccAccountDTO loadAccount(String cacheKey){
        String[] data = cacheKey.split(StringPool.COLON);
        Long merchantId = Long.parseLong(data[0]);
        String merchantType = data[1];
        String platform = data[2];
        AccAccountDTO accountDTO = accountApi.getAccount(
                Long.parseLong(data[0]),
                merchantType,
                platform
        ).getCheckedData();
        if (Objects.isNull(accountDTO)) {
            accountDTO = new AccAccountDTO();
            accountDTO.setWithdrawableAmt(BigDecimal.ZERO)
                    .setFrozenAmt(BigDecimal.ZERO)
                    .setCreditAmt(BigDecimal.ZERO)
            ;
        }
        return accountDTO;
    }

    private PayConfigDTO loadPayConfigDTO(Long sysCode){
        return partnerConfigApi.getPayConfig(sysCode).getCheckedData();
    }

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:39
    */
    private List<AreaClassDTO> loadAreaClassAreaChannelListDtoFromApi(String key) {
        List<AreaClassDTO> checkedData = areaClassApi.getAreaClassAreaChannelList(key).getCheckedData();
        if (ObjectUtil.isEmpty(checkedData)) {
            return null;
        }
        return checkedData;
    }

    private List<PagesConfigDTO> loadPagesConfig(String pageKey) {
        return pagesConfigApi.getPagesConfig(pageKey).getCheckedData();
    }

    /**
    * @Description: 获取平台商商城小程序配置
    * @Author: liuxingyu
    * @Date: 2024/4/10 16:02
    */
    private AppletAgreementPolicyDTO loadAppletAgreementPolicyDTOFromApi(Long sysCode) {
        return partnerPolicyApi.getAppletAgreementPolicy(sysCode).getCheckedData();
    }


    private List<CouponSpuScopeDTO> loadCouponSpuScopeCache(Long couponTemplateId) {
        return couponApi.getCouponSpuScopeList(couponTemplateId).getCheckedData();
    }

    private List<CouponReceiveScopeDTO> loadCouponReceiveScopeCache(Long couponTemplateId) {
        return couponApi.getCouponReceiveScopeList(couponTemplateId).getCheckedData();
    }

    /**
    * @Description: 获取渠道绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/27 9:43
    */
    private CatgoryRateDTO loadCatgoryRateCache(String cacheKey) {
        String[] infos = cacheKey.split(":");
        return catgoryApi.getCatgoryByIdAndAreaId(Long.parseLong(infos[0]), Long.parseLong(infos[1])).getCheckedData();
    }

    /**
    * @Description: 获取门店展示绑定的展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:37
    */
    private List<AreaClassDTO> loadAreaClassBranchListDtoFromApi(Long branchId) {
        List<Long> supplierIds = branchApi.getSupplierByBranchId(branchId).getCheckedData();
        if (ObjectUtil.isEmpty(supplierIds)){
            return null;
        }
        List<AreaClassDTO> areaClassDTO = areaClassApi.getAreaClassBranchList(supplierIds).getCheckedData();
        if (ObjectUtil.isEmpty(areaClassDTO)){
            return null;
        }
        return areaClassDTO;
    }
    /**
    * @Description: 获取平台展示分类列表
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:27
    */
    private List<SaleClassDTO> loadSaleClassDtoListFromApi(Long sysCode) {
        return saleClassApi.getSaleClassListBySysCode(sysCode).getCheckedData();
    }

    private SupplierDTO loadSupplierDTOFromApi(Long supplierId) {
        return supplierApi.getBySupplierId(supplierId).getCheckedData();
    }

    private ChannelDTO loadChannelDTOFromApi(Long channelId) {
        return channelApi.getByChannelId(channelId).getCheckedData();
    }

    private AreaDTO loadAreaDtoFromApi(Long areaId) {
        return areaApi.getAreaByAreaId(areaId).getCheckedData();
    }

    private BranchDTO loadBranchDTOFromApi(Long branchId) {
        return branchApi.getByBranchId(branchId).getCheckedData();
    }

    private PartnerDto loadPartnerDtoFromApi(String key) {
        if (!NumberUtil.isNumber(key)){
            return partnerApi.getPartnerBySource(key).getCheckedData();
        } else {
            return partnerApi.getBySysCode(Long.valueOf(key)).getCheckedData();
        }
    }

    private DcDTO loadDcDtoFromApi(Long dcId) {
        return dcApi.getDcById(dcId).getCheckedData();
    }

    private AreaClassDTO loadAreaClassDtoFromApi(Long areaClassId) {
        return areaClassApi.getAreaClassByAreaClassId(areaClassId).getCheckedData();
    }

    private CatgoryDTO loadCatgoryDtoFromApi(Long catgoryId) {
        return catgoryApi.getCatgoryByCatgoryId(catgoryId).getCheckedData();
    }

    private SaleClassDTO loadSaleClassDtoFromApi(Long saleClassId) {
        return saleClassApi.getSaleClassBySaleClassId(saleClassId).getCheckedData();
    }

    private AppletBaseConfigDTO loadAppletBaseConfigDtoFromApi(Long sysCode){
        return partnerConfigApi.getAppletBaseConfig(sysCode).getCheckedData();
    }

    private BrandDTO loadBrandDtoFromApi(Long brandId) {
        return brandApi.getBrandByBrandId(brandId).getCheckedData();
    }

    private BasicSettingPolicyDTO loadBasicSettingPolicyDtoFromApi(Long dcId) {
        return partnerPolicyApi.getBasicSettingPolicy(dcId).getCheckedData();
    }


    private AreaItemDTO loadAreaItemDtoFromApi(Long areaItemId) {return areaItemApi.getAreaItemId(areaItemId).getCheckedData();}

    private SupplierItemDTO loadSupplierItemDtoFromApi(Long supplierItemId){return supplierItemApi.getBySupplierItemId(supplierItemId).getCheckedData();}

    private SpuDTO loadSpuDtoFromApi(Long spuId){return spuApi.getBySpuId(spuId).getCheckedData();}

    private SkuDTO loadSkuDtoFromApi(Long skuId){return skuApi.getBySkuId(skuId).getCheckedData();}

    private Integer loadAreaChannelSalePriceCodeFromApi(String key){
        //为了避免重复、频繁的查询数据库获取价格码 如果为空也设置进Redis  在后台新增修改价格码时 进行维护
        Integer checkedData = areaChannelPriceApi.getPriceByKey(key).getCheckedData();
        if(Objects.isNull(checkedData)){
            checkedData = Code.LESS_THAN;
        }
        return checkedData;
    }

    private Integer loadSupplierGroupSalePriceCodeFromApi(String key){
        //为了避免重复、频繁的查询数据库获取价格码 如果为空也设置进Redis  在后台新增修改价格码时 进行维护
        Integer checkedData = supplierGrouplPriceApi.getPriceByKey(key).getCheckedData();
        if(Objects.isNull(checkedData)){
            checkedData = Code.LESS_THAN;
        }
        return checkedData;
    }

    private SkuPriceDTO loadSkuPriceDTOByAreaTypeFromApi(String key){
        SkuPriceDTO skuPriceDTO = skuPriceApi.getSkuPriceByKey(key).getCheckedData();
        if(Objects.isNull(skuPriceDTO)){
            skuPriceDTO = new SkuPriceDTO();
        }
        return skuPriceDTO;
    }
    private ColonelDTO loadColonelDtoFromApi(Long colonelId){return colonelApi.getByColonelId(colonelId).getCheckedData();}

    private CouponTemplateDTO loadCouponTemplateFromApi(Long couponTemplateId){
        return couponApi.getCouponTemplate(couponTemplateId).getCheckedData();
    }

    private PrmActivityDTO loadActivityDTOFromApi(Long activityId) {
        return activityApi.getActivityDto(activityId).getCheckedData();
    }

    private PartnerMiniSettingPolicyDTO loadPartnerMiniSettingPolicyDTOFromApi(Long sysCode) {
        return partnerPolicyApi.getPartnerMiniSettingPolicy(sysCode).getCheckedData();
    }

    private VisualSettingMasterDto loadVisualSettingMasterBySupplierIdFromApi(Long supplierId) {
        VisualSettingMasterDto visualSettingMasterDto = opensourceApi.getVisualSettingMasterByMerchantId(supplierId).getCheckedData();
        if (Objects.isNull(visualSettingMasterDto)) {
            return new VisualSettingMasterDto();
        }
        return visualSettingMasterDto;
    }

    private OpensourceDto loadOpensourceByMerchantIdFromApi(Long merchantId) {
        OpensourceDto opensourceDto = opensourceApi.getOpensourceByMerchantId(merchantId).getCheckedData();
        if (Objects.isNull(opensourceDto)) {
            return new OpensourceDto();
        }
        return opensourceDto;
    }

    private SupplierOtherSettingPolicyDTO loadPartnerSupplierOtherSettingPolicyDTOFromApi(Long supplierId) {
        return partnerPolicyApi.getPartnerSupplierOtherSettingPolicy(supplierId).getCheckedData();
    }

    @Override
    public void setWxSessionKey(String key, String sessionKey) {
        redisService.setCacheObject(WX_SESSION_KEY + key, sessionKey);
        redisService.expire(WX_SESSION_KEY + key, DAY_SECONDS);
    }

    @Override
    public String getWxSessionKey(String key) {
        return redisService.getCacheObject(WX_SESSION_KEY + key);
    }

    @Override
    public PartnerDto getPartnerDto(String key) {
        return partnerDtoCache.get(key);
    }

    /**
     * 查询运营商
     * @param key
     * @return
     */
    @Override
    public DcDTO getDcDTO(Long key) {
        return dcDtoCache.get(key);
    }

    @Override
    public BranchDTO getBranchDto(Long branchId) {
        if (Objects.isNull(branchId)) {
            return null;
        }
        return branchDTOCache.get(branchId);
    }

    @Override
    public ChannelDTO getChannelDto(Long channelId) {
        return channelDTOCache.get(channelId);
    }

    @Override
    public AreaDTO getAreaDto(Long areaId) {
        if (Objects.isNull(areaId)) {
             return new AreaDTO();
        }
        return areaDtoCache.get(areaId);
    }

    @Override
    public SupplierDTO getSupplierDTO(Long supplierId) {
        if (Objects.isNull(supplierId)) {
            return null;
        }
        return supplierDTOCache.get(supplierId);
    }

    @Override
    public BasicSettingPolicyDTO getBasicSettingPolicyDTO(Long dcId) {
        return basicSettingPolicyDTOCache.get(dcId);
    }

    @Override
    public AreaItemDTO getAreaItemDTO(Long areaItemId) {
        return areaItemDTOCache.get(areaItemId);
    }

    @Override
    public SupplierItemDTO getSupplierItemDTO(Long supplierItemId) {
        return supplierItemDTOCache.get(supplierItemId);
    }

    @Override
    public SpuDTO getSpuDTO(Long spuId) {
        if (Objects.isNull(spuId)) {
            return null;
        }
        return spuDTOCache.get(spuId);
    }

    @Override
    public SkuDTO getSkuDTO(Long skuId) {
        if (Objects.isNull(skuId) && skuId > NumberPool.LONG_ZERO) {
            return null;
        }
        return skuDTOCache.get(skuId);
    }

    /**
    * @Description: 获取门店绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 18:59
    */
    @Override
    public List<AreaClassDTO> getAreaClassBranch(Long branchId) {
        return areaClassBranchListDtoCache.get(branchId);
    }

    /**
     * 获取管理分类分润比例
     * @param catgoryId 管理分类ID
     * @param areaId    区域城市ID
     * @return
     */
    @Override
    public CatgoryRateDTO getCatgoryByIdAndAreaId(Long catgoryId, Long areaId) {
        return catgoryRateCache.get(RedisConstants.getCategoryRate(catgoryId, areaId));
    }

    /**
    * @Description: 获取平台展示分类列表
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:30
    */
    @Override
    public List<SaleClassDTO> getSaleClassListBySysCode(Long sysCode) {
        return saleClassDtoListCache.get(sysCode);
    }

    /**
    * @Description: 获取渠道绑定的城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/27 9:51
    */


    @Override
    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode) {
        return appletBaseConfigDTOCache.get(sysCode);
    }

    @Override
    public Integer getAreaSalePriceCodeCache(String key) {
        return areaSalePriceCodeCache.get(key);
    }

    @Override
    public Integer getSupplierSalePriceCodeCache(Long areaId, Long areaGourpId) {
        return supplierSalePriceCodeCache.get(StringUtils.format("{}-{}", areaId, areaGourpId));
    }

    @Override
    public SkuPriceDTO getSkuPriceDTOByAreaTypeCache(Long areaId, Long skuId, Integer productType) {
        return skuPriceDTOByAreaTypeCache.get(StringUtils.format("{}-{}-{}", areaId, skuId, productType));
    }
    @Override
    public ColonelDTO getColonel(Long colonelId) {
        if (Objects.isNull(colonelId)) {
            return null;
        }
        return colonelDTOCache.get(colonelId);
    }

    @Override
    public BrandDTO getBrandDTO(Long brandId) {
        // 如果品牌不存在, 或者小于等于0, 就不查了直接返回null
        if (Objects.isNull(brandId) || brandId <= NumberPool.LONG_ZERO) {
            return null;
        }
        return brandDtoCache.get(brandId);
    }

    @Override
    public CouponTemplateDTO getCouponTemplate(Long couponTemplateId) {
        return couponTemplateCache.get(couponTemplateId);
    }

    @Override
    public List<CouponSpuScopeDTO> getCouponSpuScopeList(Long couponTemplateId) {
        return couponSpuScopeCache.get(couponTemplateId);
    }

    @Override
    public List<CouponReceiveScopeDTO> getCouponReceiveScopeList(Long couponTemplateId) {
        return couponReceiveScopeCache.get(couponTemplateId);
    }

    @Override
    public AppletAgreementPolicyDTO getAppletAgreementPolicy(Long sysCode) {
        return appletAgreementPolicyDTOCache.get(sysCode);
    }

    @Override
    public List<PagesConfigDTO> getPagesConfigDTO(Long sysCode, Long channel, Long areaId) {
        return pagesConfigCache.get(RedisConstants.getPageConfigKey(sysCode, channel, areaId));
    }

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:36
    */
    @Override
    public List<AreaClassDTO> getAreaClassAreaChannel(Long areaId, Long channelId) {
        if (ObjectUtil.isNull(channelId)){
            channelId = 0L;
        }
        String key = areaId+"-"+channelId;
        return areaClassAreaChannelListDtoCache.get(key);
    }

    @Override
    public PayConfigDTO getPayConfigDTO(Long sysCode) {
        if (Objects.isNull(sysCode)) {
            return null;
        }
        return payConfigDTOCache.get(sysCode);
    }

    @Override
    public AccAccountDTO getAccount(Long merchantId, String merchantType, String platform) {
        return accountCache.get(RedisConstants.getAccountKey(merchantId, merchantType, platform));
    }

    /**
    * @Description: 获取商家平台管理分类
    * @Author: liuxingyu
    * @Date: 2024/5/7 10:35
    */
    @Override
    public List<CatgoryDTO> getCatgoryBySysCode(Long sysCode) {
        return catgorySysCodeListDtoCache.get(sysCode);
    }

    @Override
    public List<SupplierActivityDTO> getSupplierActivity(Long supplierId) {
        return supplierActivityCache.get(supplierId);
    }

    @Override
    public List<ActivitySpuScopeDTO> getActivitySpuScopeList(Long activityId) {
        return activitySpuScopeCache.get(activityId);
    }

    @Override
    public List<ActivityBranchScopeDTO> getActivityBranchScopeList(Long activityId) {
        return activityBranchScopeCache.get(activityId);
    }

    @Override
    public List<ActivityCityScopeDTO> getActivityCityScopeList(Long activityId) {
        return activityCityScopeCache.get(activityId);
    }

    @Override
    public List<ActivityChannelScopeDTO> getActivityChannelScopeList(Long activityId) {
        return activityChannelScopeCache.get(activityId);
    }

    @Override
    public List<ActivitySupplierScopeDTO> getActivitySupplierScopeList(Long activityId) {
        List<ActivitySupplierScopeDTO> activitySupplierScopeDTOS = activitySupplierScopeCache.get(activityId);
        if (CollectionUtils.isEmpty(activitySupplierScopeDTOS)) {
            return new ArrayList<>();
        }
        return activitySupplierScopeDTOS;
    }

    @Override
    public List<SkRuleDTO> getActivitySkRuleList(Long activityId) {
        return activitySkRuleCache.get(activityId);
    }

    @Override
    public List<BgRuleDTO> getActivityBgRuleList(Long activityId) {
        return activityBgRuleCache.get(activityId);
    }

    @Override
    public List<FgRuleDTO> getActivityFgRuleList(Long activityId) {
        return activityFgRuleCache.get(activityId);
    }

    @Override
    public List<SpRuleDTO> getActivitySpRuleList(Long activityId) {
        return activitySpRuleCache.get(activityId);
    }

    @Override
    public PrmActivityDTO getActivityDto(Long activityId) {
        if (Objects.isNull(activityId)) {
            return null;
        }
        return activityDTOCache.get(activityId);
    }

    @Override
    public <T> Set<T> getActivityRuleDto(Long actitvityId, String activityType) {
        String ruleKey = RedisConstants.getActivityRuleKey(activityType, actitvityId);
        Set<T> ruleDTOS = redisService.getCacheSet(ruleKey);

        if (ToolUtil.isNotEmpty(ruleDTOS)) {
            return ruleDTOS;
        }

        List list = null;
        if (TrdDiscountTypeEnum.SK.getType().equalsIgnoreCase(activityType)) { // 秒杀
            list = activityApi.getActivitySkRule(actitvityId).getCheckedData();
        }
        if (TrdDiscountTypeEnum.SP.getType().equalsIgnoreCase(activityType)) { // 特价
            list = activityApi.getActivitySpRule(actitvityId).getCheckedData();
        }
        if (TrdDiscountTypeEnum.BG.getType().equalsIgnoreCase(activityType)) { // 买赠
            list = activityApi.getActivityBgRule(actitvityId).getCheckedData();
        }
        if (TrdDiscountTypeEnum.FG.getType().equalsIgnoreCase(activityType)) { // 满赠
            list = activityApi.getActivityFgRule(actitvityId).getCheckedData();
        }
        if (TrdDiscountTypeEnum.FD.getType().equalsIgnoreCase(activityType)) { // 满减
            list = activityApi.getActivityFdRule(actitvityId).getCheckedData();
        }
        if (TrdDiscountTypeEnum.CB.getType().equalsIgnoreCase(activityType)) { // 组合促销
            list = activityApi.getActivityCbRule(actitvityId).getCheckedData();
        }

        ruleDTOS = new HashSet<>(list);
        if (ToolUtil.isNotEmpty(ruleDTOS)) {
            redisService.setCacheSet(ruleKey, ruleDTOS);
            redisService.expire(ruleKey, 24 * 60 * 60);//过期时间设置为24小时
        }

        return ruleDTOS;
    }

    @Override
    public HdfkNoPaymentTotalDTO getHdfkNoPaymentTotal(Long branchId) {
        return hdfkNoPaymentTotalCache.get(branchId);
    }

    /**
    * @Description: 获取商家管理分类
    * @Author: liuxingyu
    * @Date: 2024/5/31 10:56
    */
    @Override
    public List<CatgoryDTO> getCatgoryBySupplierId(Long supplierId) {
        return catgorySupplierListDtoCache.get(supplierId);
    }

    @Override
    public List<SkuUnitGroupDTO> getSkuUnitGroupDTO(Long spuId, Long areaId, Long classId, ProductType productType) {
        return skuUnitGroupDTOCache.get(RedisConstants.getSkuUnitKey(spuId, areaId, classId, productType.getType()));
    }

    @Override
    public List<SkuUnitGroupDTO> getSkuUnitGroupDTO(SkuUnitGroupKeyDTO spuGroupKey) {
        return getSkuUnitGroupDTO(spuGroupKey.getSpuId(), spuGroupKey.getAreaId(), spuGroupKey.getClassId(), spuGroupKey.getProductType());
    }

    @Override
    public PartnerMiniSettingPolicyDTO getPartnerMiniSettingPolicy(Long sysCode) {
        return partnerMiniSettingPolicyDTOCache.get(sysCode);
    }

    @Override
    public OrderStatusVO getOrderStatusTotal(Long branchId) {
        return orderStatusTotalCache.get(branchId);
    }

    @Override
    public void incrPendingPaymentOrderTotal(Long branchId) {
        Boolean hasKey = redisService.hasKey(RedisConstants.TRD_ORDER_STATUS + branchId);
        if (hasKey) {
            OrderStatusVO statusVO = orderStatusTotalCache.get(branchId);
            statusVO.setPendingPayment(statusVO.getPendingPayment() + 1);
            orderStatusTotalCache.put(branchId, statusVO);
        }
    }

    @Override
    public MemberDTO getMemberDTO(Long memberId) {
        if (Objects.isNull(memberId)) {
            return null;
        }
        return memberDTOCache.get(memberId);
    }

    @Override
    public VisualSettingMasterDto getVisualMasterBySupplierId(Long supplierId) {
        return visualSettingMasterBySupplierIdCache.get(supplierId);
    }

    @Override
    public HeLiBaoPayConfigDTO getHeLiBaoConfig(Long sysCode) {
        return heLiBaoPayConfigCache.get(sysCode);
    }

    @Override
    public OrderSettingPolicyDTO getOrderSettingPolicyInfo(Long dcId) {
        return orderSettingPolicyCache.get(dcId);
    }

    @Override
    public OpensourceDto getOpensourceByMerchantId(Long merchantId) {
        return opensourceDtoByMerchantIdCache.get(merchantId);
    }

    @Override
    public SupplierOtherSettingPolicyDTO getPartnerSupplierOtherSettingPolicy(Long supplierId) {
        if (Objects.isNull(supplierId)) {
            return null;
        }
        return partnerSupplierOtherSettingPolicyDTOCache.get(supplierId);
    }
    @Override
    public AreaClassDTO getAreaClassDTO(Long saleClassId) {
        if (Objects.isNull(saleClassId)) {
            return null;
        }
        return areaClassDtoCache.get(saleClassId);
    }

    @Override
    public SpuCombineDTO getSpuCombineDTO(Long spuCombineId) {
        if (Objects.isNull(spuCombineId)) {
            return null;
        }
        return spuCombineCache.get(spuCombineId);
    }

    @Override
    public MaterialCacheVO getMaterial(String cacheKey) {
        return materialCache.get(cacheKey);
    }

    @Override
    public SearchConfigDTO getSearchConfigDTO(Long areaId) {
        if (Objects.isNull(areaId)) {
            return null;
        }
        return searchConfigCache.get(areaId);
    }

    @Override
    public SaleClassDTO getSaleClassDTO(Long saleClassId) {
        if (Objects.isNull(saleClassId)) {
            return null;
        }
        return saleClassDtoCache.get(saleClassId);
    }

    @Override
    public boolean checkOrderCutAmt(OrderCutAmtDTO.CacheKey cacheKey) {
        // 如果没有设置截团时间, 则没有补单机制, 需要每次都验证起送金额
        SupplierOtherSettingPolicyDTO settingPolicy = getPartnerSupplierOtherSettingPolicy(cacheKey.getSupplierId());
        // 入驻商没有设置, 或者关闭加单机制, 都不用管已下单金额
        if (Objects.isNull(settingPolicy) || StringUtils.isEmpty(settingPolicy.getBetOrder()) || StringPool.ZERO.equals(settingPolicy.getBetOrder())) {
            return true;
        }
        // 计算截单时间
        cacheKey.setCutTime(settingPolicy.getCutTime());
        cacheKey.setDate(DateUtils.getCutDate(cacheKey.getCutTime(), DateUtil.date()));
        OrderCutAmtDTO cutAmtDTO = orderCutAmtCache.get(cacheKey.toString());
        SupplierDTO supplierDTO = getSupplierDTO(cacheKey.getSupplierId());
        // 比对数据
        if (ProductType.LOCAL.getType().equals(cacheKey.getProductType())) {
            return NumberUtil.isGreater(supplierDTO.getMinAmt(), cutAmtDTO.getOrderAmt());
        } else {
            return NumberUtil.isGreater(supplierDTO.getGlobalMinAmt(), cutAmtDTO.getOrderAmt());
        }
    }

    @Override
    public void removeOrderCutAmt(OrderCutAmtDTO.CacheKey cacheKey) {
        // 如果没有设置截团时间, 则没有补单机制, 需要每次都验证起送金额
        SupplierOtherSettingPolicyDTO settingPolicy = getPartnerSupplierOtherSettingPolicy(cacheKey.getSupplierId());
        // 入驻商没有设置, 或者关闭加单机制, 都不用管已下单金额
        if (Objects.isNull(settingPolicy) || StringUtils.isEmpty(settingPolicy.getBetOrder()) || StringPool.ZERO.equals(settingPolicy.getBetOrder())) {
            return;
        }
        // 计算截单时间
        cacheKey.setCutTime(settingPolicy.getCutTime());
        cacheKey.setDate(DateUtils.getCutDate(cacheKey.getCutTime(), DateUtil.date()));
        try {
            orderCutAmtCache.remove(cacheKey.toString());
        } catch (Exception e) {
            log.error("重置订单截团金额计算异常 " + cacheKey.toString(), e);
        }
    }
}
