package com.zksr.portal.service.impl.price;

import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.FdActivityTimesRuleEnum;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.portal.controller.mall.vo.ActivityCalculatorDTO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.mall.TradeOrderConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.utils.ActivityScopeUtil;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年05月18日 10:32
 * @description: 满减活动价格计算
 */
@Component
@Order(TradePriceCalculatorService.ORDER_FD_ACTIVITY)
@Slf4j
public class TradeFdActivityPriceCalculatorServiceImpl implements  TradePriceCalculatorService{
    @Autowired
    private IPortalCacheService portalCacheService;
    @Autowired
    private RedisActivityService redisActivityService;
    @Autowired
    private RedisService redisService;
    @Override
    public void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result) {
        /**
         * 如果传递数据为空，不计算
         */
        if (ToolUtil.isEmpty(param) || ToolUtil.isEmpty(result) || ToolUtil.isEmpty(param.getActivitys())){
            return;
        }

        // 查询满减促销活动信息并按照 全场 > 品类 > 品牌 > 商品 排序
       List<PrmActivityDTO> prmActivityList = param.getActivitys().stream()
                .filter(activityVO -> activityVO.getActivityType().equalsIgnoreCase(TrdDiscountTypeEnum.FD.getType()))
                .map(activityVO -> { return portalCacheService.getActivityDto(activityVO.getActivityId()); })
                .filter(ToolUtil.distinctByKey(ActivityDTO::getActivityId)) // 过滤去重
                .sorted(Comparator.comparing(PrmActivityDTO::getSpuScope, Comparator.nullsFirst(Integer::compareTo)))
                .collect(Collectors.toList());


        prmActivityList.forEach(prmActivity -> {
            // 2、获取活动适用范围  城市, 渠道, 门店
            SupplierActivityDTO supplierActivity = ActivityConvert.INSTANCE.convert(prmActivity);
            Boolean boo = redisActivityService.validateActivity(supplierActivity, param.getBranchDto());
            if (!boo) {
                log.error("促销单【{}】验证数据未通过！", prmActivity.getPrmSheetNo());
                return;
            }
            // 3、获取满减促销活动规则
            Set<FdRuleDTO> fdRuleList = portalCacheService.getActivityRuleDto(prmActivity.getActivityId(), TrdDiscountTypeEnum.FD.getType());
            // 满减活动设置多入驻商
            List<Long> supplierScopes = portalCacheService.getActivitySupplierScopeList(prmActivity.getActivityId()).stream().map(ActivitySupplierScopeDTO::getSupplierId).collect(Collectors.toList());
            Map<Long, Long> processedFdMap = new HashMap<>();
            // 兼容旧数据
            if (CollectionUtils.isEmpty(supplierScopes)) {
                TradePriceCalculateResp.Promotion promotion = new TradePriceCalculateResp.Promotion();
                promotion.setId(prmActivity.getActivityId())
                        .setName(prmActivity.getActivityName())
                        .setType(TrdDiscountTypeEnum.FD.getType())
                        .setSupplierId(prmActivity.getSupplierId())
                        .setPromotionItems(new ArrayList<>());
                result.getItems().stream()
                        .filter(supplierOrder -> supplierOrder.getSupplierId().equals(prmActivity.getSupplierId()))
                        .forEach(supplierOrder -> {
                            activityScope(prmActivity, fdRuleList, supplierOrder, processedFdMap, promotion);
                        });
                if (!promotion.getPromotionItems().isEmpty()) {
                    result.getPromotions().add(promotion);
                }
            } else {
                // 活动多入驻商计算
                List<TradePriceCalculateResp.Promotion> promotionList = new ArrayList<>();
                // 筛选入驻商的订单
                List<TradePriceCalculateResp.OrderItem> filterItemList = result.getItems().stream()
                        .filter(supplierOrder -> supplierScopes.contains(supplierOrder.getSupplierId())).collect(Collectors.toList());
                this.calcSupplierListPromotionList(prmActivity, fdRuleList, filterItemList, processedFdMap, promotionList);
                if (CollectionUtils.isNotEmpty(promotionList)) {
                    result.getPromotions().addAll(promotionList);
                }
            }
        });
    }

    /**
     * 计算多入驻商满减活动金额
     */
    private void calcSupplierListPromotionList(PrmActivityDTO prmActivity, Set<FdRuleDTO> fdRuleList, List<TradePriceCalculateResp.OrderItem> filterItemList, Map<Long, Long> processedFdMap, List<TradePriceCalculateResp.Promotion> promotionList) {
        if (CollectionUtils.isEmpty(filterItemList)) {
            return;
        }
        // 多入驻商满足活动条件的总金额和总数量的计算
        BigDecimal totalAmt = BigDecimal.ZERO;
        int totalQty = 0;
        // 满足条件的入驻商订单列表
        List<TradePriceCalculateResp.OrderItem> supplierOrderList = new ArrayList<>();
        for (TradePriceCalculateResp.OrderItem supplierOrder : filterItemList) {
            List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderItemList = getFilteredSupplierOrderList(supplierOrder, prmActivity, processedFdMap);
            // 没有满足条件的数据
            if (ToolUtil.isEmpty(supplierOrderItemList)) {
                continue;
            }
            // 命中的入驻商订单总金额
            BigDecimal supplierOrdeAmt = supplierOrderItemList.stream()
                    .map((supplierItem -> supplierItem.getSalePrice().multiply(new BigDecimal(String.valueOf(supplierItem.getCount()))).subtract(supplierItem.getActivityDiscountAmt())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmt = totalAmt.add(supplierOrdeAmt);
            // 命中的入驻商订单总数量
            Integer supplierOrderCount = supplierOrderItemList.stream().filter(Objects::nonNull).map(TradePriceCalculateResp.OrderItem.SupplierItem::getCount).filter(Objects::nonNull).reduce(Integer::sum).orElse(NumberPool.INT_ZERO);
            totalQty += supplierOrderCount;
            supplierOrderList.add(supplierOrder);
        }
        // 满足条件的满减规则 只取最大的一条  当前没有满足规则的条件，直接返回
        FdRuleDTO fdRuleDTO = this.getSatisfyConditionRule(prmActivity, fdRuleList, totalAmt, totalQty);
        if (ToolUtil.isEmpty(fdRuleDTO)) {
            return;
        }
        // 最终优惠金额
        BigDecimal totalDiscountAmt = this.getActivityDiscountAmt(prmActivity, fdRuleDTO, totalAmt, totalQty);
        if (totalDiscountAmt.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        // 分摊各入驻商优惠金额
        // 规则：若满减活动总优惠金额为40，入驻商1，优惠金额：13.3、入驻商2优惠金额：13.3、入驻商3优惠金额：40-26.6=13.4；
        BigDecimal ratioTemp = new BigDecimal(String.valueOf(totalDiscountAmt));
        AtomicInteger exeIndex = new AtomicInteger();
        Map<Long, TradePriceCalculateResp.Promotion> promotionMap = new HashMap<>();
        for (TradePriceCalculateResp.OrderItem supplierOrder : supplierOrderList) {
            if (totalDiscountAmt.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            // 满足活动条件的商品
            List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderItemList = getFilteredSupplierOrderList(supplierOrder, prmActivity, processedFdMap);
            // 没有满足条件的数据
            if (ToolUtil.isEmpty(supplierOrderItemList)) {
                continue;
            }
            exeIndex.getAndIncrement();
            // 组装优惠信息
            Long supplierId = supplierOrder.getSupplierId();
            TradePriceCalculateResp.Promotion promotion;
            if (promotionMap.containsKey(supplierId)) {
                promotion = promotionMap.get(supplierId);
            } else {
                promotion = new TradePriceCalculateResp.Promotion();
                promotion.setId(prmActivity.getActivityId())
                        .setName(prmActivity.getActivityName())
                        .setType(TrdDiscountTypeEnum.FD.getType())
                        .setSupplierId(supplierOrder.getSupplierId());
                // 营销优惠信息
                promotion.setPromotionItems(new ArrayList<>());
                promotionMap.put(supplierId, promotion);
                promotionList.add(promotion);
            }
            // 当前入驻商订单优惠金额
            BigDecimal supplierDiscountAmt = BigDecimal.ZERO;
            // 是否最后一个订单
            boolean lastIndex = exeIndex.get() == supplierOrderList.size();
            for (int i = 0; i < supplierOrderItemList.size(); i++) {
                TradePriceCalculateResp.OrderItem.SupplierItem supplierItem = supplierOrderItemList.get(i);
                BigDecimal supplierItemDiscountAmt = BigDecimal.ZERO;
                // 最后一个订单的最后一个商品用扣减之前的
                if (i == supplierOrderItemList.size() - 1 && lastIndex) {
                    supplierItemDiscountAmt = totalDiscountAmt.subtract(supplierDiscountAmt);
                } else {
                    // 金额满减
                    if (prmActivity.getAmtOrQty() == NumberPool.INT_ZERO) {
                        // 商品优惠金额 = （商品总金额 / 优惠命中中金额） * 总优惠金额
                        supplierItemDiscountAmt = supplierItem.getPayPrice().divide(totalAmt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(ratioTemp).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                    } else { // 数量满减
                        // 商品优惠金额 = （商品总数量 / 优惠命中中数量） * 总优惠金额
                        supplierItemDiscountAmt = BigDecimal.valueOf(supplierItem.getCount()).divide(BigDecimal.valueOf(totalQty), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(ratioTemp).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                    }
                }
                supplierDiscountAmt = supplierDiscountAmt.add(supplierItemDiscountAmt);
                processedFdMap.put(supplierItem.getSkuId(), supplierItem.getSkuId());
                processedFdMap.put(supplierItem.getCategoryId(), supplierItem.getCategoryId());
                processedFdMap.put(supplierItem.getBrandId(), supplierItem.getBrandId());
                //写入优惠金额
                setSupplierItemActivityAmt(supplierItem, supplierItemDiscountAmt, promotion);
            }
            promotion.setDiscountTotalPrice(supplierDiscountAmt);
            totalDiscountAmt = totalDiscountAmt.subtract(supplierDiscountAmt);
        }
    }

    @Override
    public void updateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.FD.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    if (Objects.nonNull(activity.getTimesRule())) {
                        if (Objects.requireNonNull(FdActivityTimesRuleEnum.formValue(activity.getTimesRule())) == FdActivityTimesRuleEnum.RULE_LEVEL_0) { // 每日一次
                            time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
                        }
                        // 标记活动已经达到限制
                        redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS);
                    }
                });
    }

    @Override
    public void undoUpdateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.FD.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    if (Objects.nonNull(activity.getTimesRule())) {
                        if (Objects.requireNonNull(FdActivityTimesRuleEnum.formValue(activity.getTimesRule())) == FdActivityTimesRuleEnum.RULE_LEVEL_0) { // 每日一次
                            time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
                        }
                        // 标记活动已经达到限制
                        redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()));
                    }
                });
    }

    /**
     * 验证活动规则 且 计算商品优惠
     * @param prmActivity
     * @param fdRuleList
     * @param supplierOrder
     * @param processedMap
     * @return
     */
    private void activityScope(PrmActivityDTO prmActivity, Set<FdRuleDTO> fdRuleList, TradePriceCalculateResp.OrderItem supplierOrder, Map<Long, Long> processedMap, TradePriceCalculateResp.Promotion promotion) {
        List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList = getFilteredSupplierOrderList(supplierOrder, prmActivity, processedMap);
        // 没有满足条件的数据
        if (ToolUtil.isEmpty(supplierOrderList))
            return;

        // 命中的总金额
        BigDecimal totalAmt = supplierOrderList.stream()
                .map((supplierItem -> supplierItem.getSalePrice().multiply(new BigDecimal(supplierItem.getCount()+"")).subtract(supplierItem.getActivityDiscountAmt())))
                .reduce((amt1 , amt2) -> amt1.add(amt2)).orElse(BigDecimal.ZERO);

        // 命中的总数量
        Integer totalQty = supplierOrderList.stream()
                .map((supplierItem -> supplierItem.getCount()))
                .reduce((qty1 , qty2) -> qty1 + qty2).orElse(NumberPool.INT_ZERO);


        // 满足条件的满减规则 只取最大的一条  当前没有满足规则的条件，直接返回
        FdRuleDTO fdRuleDTO = getSatisfyConditionRule(prmActivity, fdRuleList, totalAmt, totalQty);
        if (ToolUtil.isEmpty(fdRuleDTO))
            return;

        // 最终优惠金额
        BigDecimal discountAmt = getActivityDiscountAmt(prmActivity, fdRuleDTO, totalAmt, totalQty);

        // 计算活动商品的优惠金额
        priceCalculator(prmActivity, supplierOrderList, processedMap, promotion, new ActivityCalculatorDTO()
                .setTotalAmt(totalAmt)
                .setTotalQty(totalQty)
                .setDiscountAmt(discountAmt));

    }

    /**
     * 获取满足本次活动的商品数据
     * @param supplierOrder
     * @param prmActivity
     * @param processedMap
     * @return
     */
    private List<TradePriceCalculateResp.OrderItem.SupplierItem> getFilteredSupplierOrderList(TradePriceCalculateResp.OrderItem supplierOrder, PrmActivityDTO prmActivity, Map<Long, Long> processedMap) {
        List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList = supplierOrder.getSupplierItems();

        // 获取促销活动参与商品范围限定
        List<ActivitySpuScopeDTO> scopeList = portalCacheService.getActivitySpuScopeList(prmActivity.getActivityId());

        // 判断商品是否在活动参与范围内
        supplierOrderList = supplierOrderList.stream().filter(item -> ActivityScopeUtil.validateSpuScope(TradeOrderConvert.INSTANCE.convertActivityValidVO(item), scopeList)).collect(Collectors.toList());

        return supplierOrderList;
    }

    /**
     *  计算活动商品的优惠金额
     * @param activity 活动信息
     * @param supplierOrderList 入驻商商品信息
     * @param processedFdMap 已验证过满减的商品信息
     * @param promotion 营销信息
     * @param activityCalculator 活动类型返回的计算信息
     */
    private void priceCalculator(PrmActivityDTO activity, List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList, Map<Long, Long> processedFdMap, TradePriceCalculateResp.Promotion promotion, ActivityCalculatorDTO activityCalculator){
        // 优惠已扣减总金额
        BigDecimal allocationAmtCount = BigDecimal.ZERO;

        // 营销优惠信息
        promotion.setDiscountTotalPrice(activityCalculator.getDiscountAmt());
        promotion.setPromotionItems(new ArrayList<>());


        for (int i = 0; i < supplierOrderList.size(); i++) {
            TradePriceCalculateResp.OrderItem.SupplierItem supplierItem = supplierOrderList.get(i);

            BigDecimal allocationAmt = BigDecimal.ZERO;
            if (i == supplierOrderList.size() - 1) {
                allocationAmt = activityCalculator.getDiscountAmt().subtract(allocationAmtCount);
            } else {
                // 金额满减
                if (activity.getAmtOrQty() == NumberPool.INT_ZERO) {
                    // 商品优惠金额 = （商品总金额 / 优惠命中中金额） * 总优惠金额
                    allocationAmt = supplierItem.getPayPrice().divide(activityCalculator.getTotalAmt(), StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(activityCalculator.getDiscountAmt()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                }
                else { // 数量满减
                    // 商品优惠金额 = （商品总数量 / 优惠命中中数量） * 总优惠金额
                    allocationAmt = BigDecimal.valueOf(supplierItem.getCount()).divide(BigDecimal.valueOf(activityCalculator.getTotalQty()), StatusConstants.PRICE_RESERVE_6, BigDecimal.ROUND_HALF_UP).multiply(activityCalculator.getDiscountAmt()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                }

            }
            allocationAmtCount = allocationAmtCount.add(allocationAmt);
            processedFdMap.put(supplierItem.getSkuId(), supplierItem.getSkuId());
            processedFdMap.put(supplierItem.getCategoryId(), supplierItem.getCategoryId());
            processedFdMap.put(supplierItem.getBrandId(), supplierItem.getBrandId());

            //写入优惠金额
            setSupplierItemActivityAmt(supplierItem, allocationAmt, promotion);
        }
    }




    /**
     * 根据条件查询出满足条件的满减规则 只取第一条
     * @param fdRuleList
     * @param totalAmt
     * @param totalQty
     * @return
     */
    private FdRuleDTO getSatisfyConditionRule(PrmActivityDTO activity, Set<FdRuleDTO> fdRuleList, BigDecimal totalAmt, Integer totalQty){
        List<FdRuleDTO> fdRuleDTOS = null;
        if (activity.getAmtOrQty() == NumberPool.INT_ZERO) { // 金额满减
            // 命中的总金额 大于等于 满减规则按照金额 倒序排序 且只取出满足条件的第一条数据
            fdRuleDTOS = fdRuleList.stream()
                    .filter(fdRule -> NumberUtil.isGreaterOrEqual(totalAmt, fdRule.getFullAmt()))
                    .sorted(Comparator.comparing(FdRuleDTO::getFullAmt, Comparator.nullsFirst(BigDecimal::compareTo)).reversed())
                    .skip(0)
                    .limit(3)
                    .collect(Collectors.toList());
        } else { // 数量满减
            // 命中的总数量 大于等于 满减规则按照数量 倒序排序 且只取出满足条件的第一条数据
            fdRuleDTOS = fdRuleList.stream()
                    .filter(fdRule -> totalQty >= fdRule.getFullQty())
                    .sorted(Comparator.comparing(FdRuleDTO::getFullQty, Comparator.nullsFirst(Integer::compareTo)).reversed())
                    .skip(0)
                    .limit(3)
                    .collect(Collectors.toList());
        }
        return fdRuleDTOS.isEmpty() ? null : fdRuleDTOS.get(0);
    }

    /**
     *  获得活动的总优惠金额
     * @param activity  活动信息
     * @param rule  命中的活动规则
     * @param totalAmt  活动命中数据的总金额
     * @param totalQty  活动命中数据的总数量
     * @return 优惠总金额
     */
    private BigDecimal getActivityDiscountAmt (PrmActivityDTO activity, FdRuleDTO rule, BigDecimal totalAmt, Integer totalQty) {
        BigDecimal discountAmt = BigDecimal.ZERO;
        if (activity.getLadderFlag() == NumberPool.INT_ONE) { // 满减
            discountAmt = rule.getDiscountAmt();
        } else {
            if (activity.getAmtOrQty() == NumberPool.INT_ZERO) { // 金额每减 (总金额 / 条件金额) * 优惠金额
                discountAmt = totalAmt.divide(rule.getFullAmt(), 0, RoundingMode.DOWN).multiply(rule.getDiscountAmt()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
            } else { // 数量每减 (总数量 / 条件数量) * 优惠金额
                discountAmt = BigDecimal.valueOf(totalQty).divide(BigDecimal.valueOf(rule.getFullQty()), 0, RoundingMode.DOWN).multiply(rule.getDiscountAmt()).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
            }
        }
        return discountAmt;
    }

    /**
     * 写入订单明细信息优惠劵优惠金额
     * @param supplierItem 明细
     * @param allocationAmt 优惠金额
     * @param promotion 营销信息
     */
    private void setSupplierItemActivityAmt (TradePriceCalculateResp.OrderItem.SupplierItem supplierItem, BigDecimal allocationAmt, TradePriceCalculateResp.Promotion promotion) {
        // 当前商品是组合商品 写入子商品的促销信息后直接返回
        if (Objects.equals(supplierItem.getGoodsType(), NumberPool.INT_ONE)) {
            fdCompatibleCbActivity(supplierItem, allocationAmt, promotion);
            return;
        }
        // 营销信息
        TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
        promotion.getPromotionItems().add(promotionItem);
        promotionItem.setSkuId(supplierItem.getSkuId());
        promotionItem.setActivityDiscountAmt(allocationAmt);
        promotionItem.setUuIdNo(supplierItem.getUuIdNo());

        supplierItem.setActivityDiscountAmt(supplierItem.getActivityDiscountAmt().add(allocationAmt));
    }

    /**
     * 满减活动兼容组合促销 计算优惠子商品数据
     * @param supplierItem
     * @param allocationAmt
     * @param promotion
     */
    private void fdCompatibleCbActivity(TradePriceCalculateResp.OrderItem.SupplierItem supplierItem, BigDecimal allocationAmt, TradePriceCalculateResp.Promotion promotion) {
        // 组合商品下的明细商品原总金额
        BigDecimal originalTotalAmt = supplierItem.getSpuCombineSkuVOList().stream().map(combineSku -> {
            return combineSku.getSuggestPrice().multiply(BigDecimal.valueOf(combineSku.getQty())).multiply(BigDecimal.valueOf(supplierItem.getCount()));
        }).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 已扣减金额
        final BigDecimal[] deductionAmt = {BigDecimal.ZERO};
        // 执行次数
        AtomicInteger i = new AtomicInteger();
        supplierItem.getSpuCombineSkuVOList().forEach(combineSku -> {
            /** 组合商品明细数据 满减活动优惠金额计算逻辑
             *
             * 总金额：100
             * 原总金额：312
             * 优惠金额：212
             * 商品1优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
             * 商品2优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
             * 商品3优惠金额 ： （100 * 1）/ 312 * 212 = 67.95
             * 商品4优惠金额（最后一个商品的优惠金额是扣减的） ： 212 - 67.95 - 67.95 -67.95 = 8.15
             */
            i.getAndIncrement();
            BigDecimal discountAmt = BigDecimal.ZERO; // 当前子商品优惠劵优惠金额
            if (i.get() == supplierItem.getSpuCombineSkuVOList().size()) { // 最后一个的金额不是计算处理的，是扣减出来的
                // 当前子商品优惠劵优惠金额
                discountAmt = allocationAmt.subtract(deductionAmt[0]).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
            } else {
                // 当前子商品优惠劵优惠金额
                discountAmt = combineSku.getSuggestPrice().multiply(BigDecimal.valueOf(combineSku.getQty())).multiply(BigDecimal.valueOf(supplierItem.getCount()))
                        .divide(originalTotalAmt, StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP)
                        .multiply(allocationAmt)
                        .setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
            }
            deductionAmt[0] = deductionAmt[0].add(discountAmt);

            // 营销信息
            TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
            promotion.getPromotionItems().add(promotionItem);
            promotionItem.setSkuId(combineSku.getSkuId());
            promotionItem.setActivityDiscountAmt(discountAmt);
            promotionItem.setUuIdNo(StringUtils.format("{}_{}_{}", supplierItem.getUuIdNo(), combineSku.getSkuId(), combineSku.getSkuUnitType()));

            combineSku.setOtherDiscountAmt(combineSku.getOtherDiscountAmt().add(discountAmt)); // 子商品优惠金额
            supplierItem.setActivityDiscountAmt(supplierItem.getActivityDiscountAmt().add(discountAmt));
        });
    }
}
