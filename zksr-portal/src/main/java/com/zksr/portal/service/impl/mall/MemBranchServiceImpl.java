package com.zksr.portal.service.impl.mall;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.utils.RedisWxTokenUtil;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.common.third.wx.vo.RetailInfoRespVO;
import com.zksr.member.api.b2bAuth.B2bAuthApi;
import com.zksr.member.api.b2bAuth.dto.MemMemberOpenAuthDTO;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.member.api.branchRegister.BranchRegisterApi;
import com.zksr.member.api.branchRegister.dto.BranchRegisterDTO;
import com.zksr.portal.controller.mall.vo.branch.BranchReqVO;
import com.zksr.portal.controller.mall.vo.branch.BranchWxMerchantStateRespVO;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IMemBranchService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.AreaCityApi;
import com.zksr.system.api.area.vo.SysAreaCityRespVO;
import com.zksr.system.api.dcArea.DcAreaApi;
import com.zksr.system.api.dcArea.dto.DcAreaDTO;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

import static com.zksr.common.core.constant.StatusConstants.*;

@Slf4j
@Service
public class MemBranchServiceImpl implements IMemBranchService {

    @Resource
    private BranchRegisterApi branchRegisterApi;

    @Resource
    private AreaApi areaApi;

    @Resource
    private DcAreaApi remoteDcAreaApi;

    @Resource
    private BranchApi branchApi;

    @Resource
    private AreaCityApi areaCityApi;

    @Resource
    private B2bAuthApi b2bAuthApi;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisService redisService;

    /**
     * @Description: 添加门店
     * @Author: liuxingyu
     * @Date: 2024/6/7 16:20
     */
    @Override
    public Long addBranch(BranchReqVO branchReqVO) {
        //校验商品区域城市 是否是二级
        areaApi.checkAreaByAreaId(branchReqVO.getAreaId());
        BranchRegisterDTO registerDTO = HutoolBeanUtils.toBean(branchReqVO, BranchRegisterDTO.class);
        Long sysCode = MallSecurityUtils.getLoginMember().getSysCode();
        registerDTO.setSysCode(sysCode);
        registerDTO.setStatus(NumberPool.INT_ONE);
        registerDTO.setUserType(NumberPool.INT_ONE);
        registerDTO.setMemberId(MallSecurityUtils.getMemberId());
        registerDTO.setBranchApproveFlag(NumberPool.INT_ZERO);
        registerDTO.setApproveFlag(AUDIT_STATE_0);
        registerDTO.setSupplierIds(branchReqVO.getSupplierIds());
        registerDTO.setChannelId(branchReqVO.getChannelId());
        //根据城市ID获取对应的运营商基础配置
        CommonResult<DcAreaDTO> dcAreaResult = remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null, branchReqVO.getAreaId(), sysCode);
        if (ToolUtil.isEmpty(dcAreaResult.getData())) {
            throw new ServiceException("该城市未分配运营商");
        }

        DcAreaDTO dcAreaDTO = dcAreaResult.getData();
        //获取运营商配置
        BasicSettingPolicyDTO config = portalCacheService.getBasicSettingPolicyDTO(dcAreaDTO.getDcId());
        //校验运营商配置
        if (ToolUtil.isNotEmpty(config)) {
            if (Integer.valueOf(config.getShopAutoCheck()) == STATE_ENABLE) {
                registerDTO.setBranchApproveFlag(REGISTER_APPROVE_FLAG_1);
            }
        }
        // 新增门店时，如果新增的门店和当前登录门店是同一城市，则新增的门店的业务员 默认为 当前门店的业务员
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        if (ToolUtil.isNotEmpty(branchDTO) && Objects.equals(branchDTO.getAreaId(), branchReqVO.getAreaId())) {
            registerDTO.setColonelId(branchDTO.getColonelId());
        }
        return branchRegisterApi.addBranch(registerDTO).getCheckedData();
    }

    @Override
    public BranchWxMerchantStateRespVO getWxMerchantAuthState(String openid) {
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());

        // 2024年10月31日10:19:13, 不强制门店需要微信B2B门店认证
        /*PartnerMiniSettingPolicyDTO miniSettingPolicy = portalCacheService.getPartnerMiniSettingPolicy(branchDTO.getSysCode());
        if (Objects.isNull(miniSettingPolicy)
                || StringUtils.isEmpty(miniSettingPolicy.getOpenWechatMerchantAuth())
                || StringPool.ZERO.equals(miniSettingPolicy.getOpenWechatMerchantAuth())
                ) {
            return BranchWxMerchantStateRespVO.success();
        }*/
        AppletBaseConfigDTO baseConfigDTO = portalCacheService.getAppletBaseConfigDTO(branchDTO.getSysCode());
        if (Objects.isNull(baseConfigDTO)) {
            log.error("getWxMerchantAuthState sysCode={} 小程序配置为空", branchDTO.getSysCode());
            return BranchWxMerchantStateRespVO.success();
        }
        String accessToken = RedisWxTokenUtil.getAppletAccessToken(baseConfigDTO.getAppId());
        if (StringUtils.isEmpty(accessToken)) {
            log.error("getWxMerchantAuthState sysCode={} accessToken不存在", branchDTO.getSysCode());
            return BranchWxMerchantStateRespVO.success();
        }
        // appid + branchId + openid 是否授权
        MemMemberOpenAuthDTO memberOpenAuthDTO = b2bAuthApi.getBind(baseConfigDTO.getAppId(), openid, branchDTO.getBranchId()).getCheckedData();
        if (Objects.nonNull(memberOpenAuthDTO)) {
            return BranchWxMerchantStateRespVO.success();
        }

        RetailInfoRespVO retailInfoRespVO = null;
        try {
            retailInfoRespVO = WxUtils.getRetailInfo(openid, accessToken);
            if (retailInfoRespVO.getState() == 0) {
                for (RetailInfoRespVO.Merchant merchant : retailInfoRespVO.getMerchants()) {
                    // 如果有命中的门店就更新未不需要认证了
                    if (branchDTO.getBranchName().equals(merchant.getRetailName())) {
                        // 更新授权
                        this.updateBranchWxMerchantState(branchDTO.getBranchId(), merchant.getOpenid());
                        return BranchWxMerchantStateRespVO.success();
                    }
                }
                return BranchWxMerchantStateRespVO.needAuth();
            } else if (9404102 == retailInfoRespVO.getState().intValue()) {
                return BranchWxMerchantStateRespVO.needAuth();
            }
            return BranchWxMerchantStateRespVO.success();
        } catch (Exception e) {
            log.error("查询微信B端商户信息失败", e);
            return BranchWxMerchantStateRespVO.success();
        }
    }

    @Override
    public void syncWxMerchant() {
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        try {
            SysAreaCityRespVO three = areaCityApi.getById(branchDTO.getThreeAreaCityId()).getCheckedData();
            SysAreaCityRespVO second = areaCityApi.getById(three.getPid()).getCheckedData();
            SysAreaCityRespVO first = areaCityApi.getById(second.getPid()).getCheckedData();
            AppletBaseConfigDTO baseConfigDTO = portalCacheService.getAppletBaseConfigDTO(branchDTO.getSysCode());
            if (Objects.isNull(baseConfigDTO)) {
                log.error("syncWxMerchant sysCode={} 小程序配置为空", branchDTO.getSysCode());
                return;
            }
            String accessToken = RedisWxTokenUtil.getAppletAccessToken(baseConfigDTO.getAppId());
            ;
            if (StringUtils.isEmpty(accessToken)) {
                log.error("syncWxMerchant sysCode={} accessToken不存在", branchDTO.getSysCode());
                return;
            }
            String result = WxUtils.createRetail(
                    branchDTO.getContactPhone(),
                    branchDTO.getBranchName(),
                    first.getName(),
                    second.getName(),
                    three.getName(),
                    branchDTO.getBranchAddr(),
                    accessToken
            );
            log.info("branchId={}, 微信商户助手预录入结果: {}", branchDTO.getBranchId(), result);
        } catch (Exception e) {
            log.error("syncWxMerchant 信息预录入异常", e);
        }
    }

    @Override
    public void saveWxAuthOpenid(String openid) {
        this.updateBranchWxMerchantState(MallSecurityUtils.getBranchId(), openid);
    }

    @Override
    public Long editBranch(BranchReqVO branchReqVO) {
        BranchDTO branchDTO = branchApi.getByBranchId(branchReqVO.getBranchId()).getCheckedData();
        if (Objects.isNull(branchDTO)) {
            throw new ServiceException("找不到该门店信息：" + branchReqVO.getBranchName());
        }
        //门店经纬度不能为空
        if (Objects.isNull(branchReqVO.getLatitude()) || Objects.isNull(branchReqVO.getLongitude())) {
            throw new ServiceException("经纬度不能为空");
        }
        //暂时只允许编辑定位地址和详细地址
        MemBranchSaveReqVO reqVO = new MemBranchSaveReqVO();
        reqVO.setBranchId(branchReqVO.getBranchId());
        reqVO.setBranchNo(branchDTO.getBranchNo());
        reqVO.setLatitude(branchReqVO.getLatitude());
        reqVO.setLongitude(branchReqVO.getLongitude());
        reqVO.setBranchAddr(branchReqVO.getBranchAddr());
        reqVO.setAreaId(branchReqVO.getAreaId());
        branchApi.edit(reqVO);
        return branchReqVO.getBranchId();
    }

    public void updateBranchWxMerchantState(Long branchId, String openid) {
        branchApi.updateWechatMerchantAuthOpenid(branchId, openid);
        BranchDTO branchDTO = portalCacheService.getBranchDto(branchId);
        AppletBaseConfigDTO baseConfigDTO = portalCacheService.getAppletBaseConfigDTO(branchDTO.getSysCode());
        if (Objects.isNull(baseConfigDTO)) {
            return;
        }
        // 更新授权
        b2bAuthApi.updateAuth(
                MemMemberOpenAuthDTO.builder()
                        .appid(baseConfigDTO.getAppId())
                        .openid(openid)
                        .branchId(branchId)
                        .authState(NumberPool.INT_ONE)
                        .build()
        );
    }


}
