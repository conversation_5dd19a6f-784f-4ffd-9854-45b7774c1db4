package com.zksr.portal.service.impl.mall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson2.JSONArray;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.spu.SpuActivityLabelVO;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.spu.dto.SkuUnitGroupDTO;
import com.zksr.product.api.spu.dto.SkuUnitGroupKeyDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.api.activity.vo.UnitSuperPriceVO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.constant.ProductConstant.PRDT_STATUS_0;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SPU_EXISTS;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/30 8:47
 */
@Service("spuNormalService")
@Slf4j
@SuppressWarnings("all")
public class SpuNormalServiceImpl extends AbsSpuItemInfoService {

    @Autowired
    private RedisActivityService redisActivityService;

    @Override
    public void renderItemList(SkuPageRespVO product, BranchDTO branchDTO) {
        Integer unitSize = product.getUnitSize();
        if (ProductType.isGlobal(product.getType())) {
            //设置销售价
            product.setMarkPrice(skuPriceService.getSupplierSkuPrice(branchDTO, unitSize, product.getSkuId()));
        } else {
            //设置销售价
            BigDecimal markPrice = skuPriceService.getAreaSkuPrice(branchDTO, unitSize, product.getSkuId());
            product.setMarkPrice(markPrice);
            if (Objects.nonNull(markPrice) && markPrice.compareTo(NumberPool.BIGDECIMAL_GROUND) == 0) {
                return;
            }
        }
        //设置库存
        product.setStock(redisStockService.getSurplusSaleQtyBigDecimal(product.getSkuId()));
        SkuDTO skuDTO = portalCacheService.getSkuDTO(product.getSkuId());
        SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
        // 单位转换比例
        BigDecimal unitSizeQty = spuDTO.getUnitSizeQty(unitSize);
        Long validNum = StockUtil.stockDivide(product.getStock(), unitSizeQty).longValue();
        // 最大限购不存在, 或者有效购买数 < 最大限购
        if (Objects.isNull(product.getMaxOq()) || product.getMaxOq() == 0 || validNum < product.getMaxOq() ) {
            // 根据库存数量计算最大限购数量
            product.setMaxOq(validNum);
            if (validNum == NumberPool.LONG_ZERO) {
                product.setStock(BigDecimal.ZERO);
            }
        }
        // 设置实时计算有效库存
        product.setStock(StockUtil.bigDecimal(validNum));
        // 渲染入驻商标签
        SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(product.getSupplierId());
        ProductConvert.INSTANCE.buildSetSupplier(product, portalCacheService.getPartnerSupplierOtherSettingPolicy(product.getSupplierId()));
        // 是否支持负库存下单
        product.setIsNegativeStock(supplierDTO.getIsNegativeStock());

        // 渲染生产日期
        this.produceFormat(product, spuDTO);
    }

    @Override
    public void renderOddRate(SpuDetailRespVO detailReslut, SpuDTO spuDTO, BranchDTO branchDto) {
        // 没有门店不计算利润佣金
        if (Objects.isNull(branchDto)) {
            return;
        }
        //获各级分润比列
        RateResultDTO rateResultDTO = calculateProfitSharingRates(branchDto.getSysCode(), branchDto.getAreaId(), spuDTO, branchDto);
        PrdtSkuSaleTotalRateVO skuSaleTotalRateVO = skuApi.getSkuSaleTotalRate(
                PrdtSkuSaleTotalRateReqVO.builder()
                        .sysCode(branchDto.getSysCode())
                        .skuIdList(detailReslut.getSkus().stream().map(SkuDetailRespVO::getSkuId).collect(Collectors.toList()))
                        .build()
        ).getCheckedData();

        for (SkuDetailRespVO skus : detailReslut.getSkus()) {
            for (SkuDetailRespVO.SkuUnit unit : skus.getUnitList()) {
                if(ToolUtil.isNotEmpty(rateResultDTO.getColonel())){
                    // 计算利润模式
                    BigDecimal profit;
                    if(ToolUtil.isNotEmpty(rateResultDTO.getPayConfigDTO().getProfitModel())
                            && rateResultDTO.getPayConfigDTO().getProfitModel().equals(StringPool.ZERO)){
                        //(售价*比例=利润)
                        BigDecimal relate = skuSaleTotalRateVO.getSkuRate(skus.getSkuId()).getRate();
                        if (Objects.isNull(relate) || NumberUtil.isGreater(BigDecimal.ZERO, relate)) {
                            profit = BigDecimal.ZERO;
                        } else {
                            profit = (unit.getMarkPrice()).multiply(relate).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.DOWN);
                        }
                    }else{
                        //(售价-进货价=利润)
                        SkuDTO skuDTO = portalCacheService.getSkuDTO(skus.getSkuId());
                        //佣金
                        if (UnitTypeEnum.S(unit.getUnitSize())) {
                            //(售价-小单位单位成本价=利润)
                            profit = unit.getMarkPrice().subtract(skuDTO.getCostPrice());
                        } else if (UnitTypeEnum.M(unit.getUnitSize())) {
                            //(售价-中单位成本价=利润)
                            profit = unit.getMarkPrice().subtract(skuDTO.getMidCostPrice());
                        } else if (UnitTypeEnum.L(unit.getUnitSize())) {
                            //(售价-大单位成本价=利润)
                            profit = unit.getMarkPrice().subtract(skuDTO.getLargeCostPrice());
                        } else {
                            profit = BigDecimal.ZERO;
                        }

                    }
                    // 去掉平台商和软件商的利润
                    profit = profit.subtract(rateResultDTO.getPartnerRate().add(rateResultDTO.getSoftwareRate()).multiply(profit));
                    if (NumberUtil.isGreater(profit, BigDecimal.ZERO)) {
                        // 业务员提成系数
                        BigDecimal percentagerRate = ToolUtil.isEmptyOrZeroReturn(rateResultDTO.getColonel().getPercentageRate(), BigDecimal.valueOf(100)).multiply(new BigDecimal("0.01"));
                        // 剩下的, 再算业务员
                        unit.setCommissionPrice(profit.multiply(rateResultDTO.getColonelRate()).multiply(percentagerRate).setScale(2, RoundingMode.HALF_UP));
                    }
                }
            }
        }
    }


    @Override
    public SpuDetailRespVO getItemInfo(BranchDTO branchDto, AreaItemDTO areaItemDTO) {
        SpuDetailRespVO detailReslut = new SpuDetailRespVO();
        Long spuId = areaItemDTO.getSpuId();
        //获取商品信息
        SpuDTO spuDTO = portalCacheService.getSpuDTO(spuId);
        if(ToolUtil.isNotEmpty(spuDTO) && spuDTO.getStatus() == PRDT_STATUS_0){
            throw exception(PRDT_SPU_EXISTS);
        }
        detailReslut = ProductConvert.INSTANCE.convertSpuDetail(spuDTO);
        detailReslut.setBrandName(portalCacheService.getBrandDTO(spuDTO.getBrandId()))
                .setClassId(areaItemDTO.getAreaClassId())
                .setItemType(areaItemDTO.getItemType())
                .setType(ProductType.LOCAL.getType());
        // 获取商品SPU, 在同范围内的上架商品集合
        List<SkuUnitGroupDTO> skuUnitGroupDTOList = portalCacheService.getSkuUnitGroupDTO(areaItemDTO.getSpuId(), areaItemDTO.getAreaId(), areaItemDTO.getAreaClassId(), ProductType.LOCAL);
        List<SkuDetailRespVO> skuList = new ArrayList<>();
        //组装规格数据 前端显示优先
        for (SkuUnitGroupDTO groupDTO : skuUnitGroupDTOList) {
            List<SkuDetailRespVO.SkuUnit> skuUnits = ProductConvert.INSTANCE.convertSkuUnitList(groupDTO.getUnitList());
            for (SkuDetailRespVO.SkuUnit unit : skuUnits) {
                unit.setMarkPrice(skuPriceService.getAreaSkuPrice(branchDto, unit.getUnitSize(), groupDTO.getSkuId()));
            }
            SkuDTO skuDTO = portalCacheService.getSkuDTO(groupDTO.getSkuId());
            SkuDetailRespVO detailRespVO = SkuDetailRespVO.builder()
                    .itemId(groupDTO.getItemId())
                    .skuId(groupDTO.getSkuId())
                    .stock(redisStockService.getSurplusSaleQtyBigDecimal(groupDTO.getSkuId())) // 这里根据多规格商品的skuId, 获取实时库存
                    .iconUrl(groupDTO.getThumb())
                    .expirationDate(groupDTO.getExpirationDate())
                    .propertieList(JSONArray.parseArray(groupDTO.getProperties(), SkuPropertiesVO.class))
                    .unitList(skuUnits)
                    .minUnitSuggestPrice(skuDTO.getSuggestPrice(UnitTypeEnum.UNIT_SMALL.getType()))
                    .build();
            skuList.add(detailRespVO);
        }
        // 获取最低价
        List<SkuDetailRespVO.SkuUnit> unitList = skuList.stream().flatMap(item -> item.getUnitList().stream()).filter(item -> Objects.nonNull(item.getMarkPrice())).collect(Collectors.toList());
        // 排序显示最小金额
        unitList.sort(Comparator.comparing(SkuDetailRespVO.SkuUnit::getMarkPrice));
        // 是否多规格
        detailReslut.setIsSpecs(unitList.size() == NumberPool.INT_ONE ? NumberPool.LONG_ZERO : NumberPool.LONG_ONE)
                // 组装所有的规格
                .setPropertyKeys(getProperty(skuList))
                .setSkus(skuList)
                // 设置入驻商信息
                .setSupplier(getSupplierByid(detailReslut.getSupplierId()))
                // 设置最小单位价格
                .setMarkPrice(Objects.nonNull(unitList) ? unitList.get(0).getMarkPrice() : detailReslut.getMarkPrice())
        ;
        // 增加分润计算
        this.renderOddRate(detailReslut, spuDTO, branchDto);
        // 设置生产日期格式
        this.produceFormat(detailReslut, spuDTO);
        return detailReslut;
    }

    @Override
    public SpuDetailRespVO getItemInfo(BranchDTO branchDto, SupplierItemDTO supplierItemDTO) {
        SpuDetailRespVO detailReslut = new SpuDetailRespVO();
        //获取商品信息
        SpuDTO spuDTO = portalCacheService.getSpuDTO(supplierItemDTO.getSpuId());
        if(ToolUtil.isNotEmpty(spuDTO) && spuDTO.getStatus() == PRDT_STATUS_0){
            throw exception(PRDT_SPU_EXISTS);
        }
        detailReslut = ProductConvert.INSTANCE.convertSpuDetail(spuDTO);
        detailReslut.setBrandName(portalCacheService.getBrandDTO(spuDTO.getBrandId()))
                .setType(ProductType.GLOBAL.getType())
                .setClassId(supplierItemDTO.getSaleClassId())
                .setItemType(supplierItemDTO.getItemType());
        // 获取SPU范围上架商品组合
        List<SkuUnitGroupDTO> skuUnitGroupDTOList = portalCacheService.getSkuUnitGroupDTO(supplierItemDTO.getSpuId(), NumberPool.LOWER_GROUND_LONG, supplierItemDTO.getSaleClassId(), ProductType.GLOBAL);
        List<SkuDetailRespVO> skuList = new ArrayList<>();
        //组装规格数据 前端显示优先
        for (SkuUnitGroupDTO groupDTO : skuUnitGroupDTOList) {
            List<SkuDetailRespVO.SkuUnit> skuUnits = ProductConvert.INSTANCE.convertSkuUnitList(groupDTO.getUnitList());
            for (SkuDetailRespVO.SkuUnit unit : skuUnits) {
                unit.setMarkPrice(skuPriceService.getSupplierSkuPrice(branchDto, unit.getUnitSize(), groupDTO.getSkuId()));
            }
            SkuDTO skuDTO = portalCacheService.getSkuDTO(groupDTO.getSkuId());
            SkuDetailRespVO detailRespVO = SkuDetailRespVO.builder()
                    .itemId(groupDTO.getItemId())
                    .skuId(groupDTO.getSkuId())
                    .stock(redisStockService.getSurplusSaleQtyBigDecimal(groupDTO.getSkuId()))
                    .iconUrl(groupDTO.getThumb())
                    .expirationDate(groupDTO.getExpirationDate())
                    .propertieList(JSONArray.parseArray(groupDTO.getProperties(), SkuPropertiesVO.class))
                    .unitList(skuUnits)
                    .minUnitSuggestPrice(skuDTO.getSuggestPrice(UnitTypeEnum.UNIT_SMALL.getType()))
                    .build();
            skuList.add(detailRespVO);
        }
        detailReslut.setPropertyKeys(getProperty(skuList));
        detailReslut.setSkus(skuList);
        // 获取最低价
        List<SkuDetailRespVO.SkuUnit> unitList = skuList.stream().flatMap(item -> item.getUnitList().stream()).filter(item -> Objects.nonNull(item.getMarkPrice())).collect(Collectors.toList());
        // 排序显示最小金额
        unitList.sort(Comparator.comparing(SkuDetailRespVO.SkuUnit::getMarkPrice));
        // 是否多规格
        detailReslut.setIsSpecs(unitList.size() == NumberPool.INT_ONE ? NumberPool.LONG_ZERO : NumberPool.LONG_ONE)
                //设置入驻商信息
                .setSupplier(getSupplierByid(spuDTO.getSupplierId()))
                // 设置最小单位价格
                .setMarkPrice(Objects.nonNull(unitList) ? unitList.get(0).getMarkPrice() : detailReslut.getMarkPrice());
        // 增加分润计算
        this.renderOddRate(detailReslut, spuDTO, branchDto);
        // 设置生产日期格式
        this.produceFormat(detailReslut, spuDTO);
        return detailReslut;
    }

    @Override
    public void renderActivityItemList(BranchDTO branchDTO, List<SkuPageRespVO> itemList, List<SupplierActivityDTO> supplierActivityList) {
        itemList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getType)).forEach((productTypeStr, productList) -> {
            ProductType productType = ProductType.formValue(productTypeStr);
            // 渲染素材图片
            for (SkuPageRespVO pageRespVO : productList) {
                MaterialCacheVO materialCacheVO = portalCacheService.getMaterial(MaterialCacheVO.getProductCacheKey(productType, pageRespVO.getItemId()));
                pageRespVO.setMaterialUrl(materialCacheVO.getValidateMaterial());
            }
            Map<SkuUnitGroupKeyDTO, Set<SkuUnitGroupDTO>> spuGroupMap = new HashMap<>();
            // 查询已上架的SKU
            productList.forEach((item) -> {
                SkuUnitGroupKeyDTO spuGroupKey = new SkuUnitGroupKeyDTO(item.getSpuId(), branchDTO.getAreaId(), item.getClassId(), productType);
                if (spuGroupMap.containsKey(spuGroupKey)) {
                    spuGroupMap.get(spuGroupKey).addAll(portalCacheService.getSkuUnitGroupDTO(spuGroupKey));
                } else {
                    spuGroupMap.put(spuGroupKey, new HashSet<>(portalCacheService.getSkuUnitGroupDTO(spuGroupKey)));
                }
            });
            Map<Long, List<SupplierActivityDTO>> skuActivity = this.getActivityService().applySpuActivity(branchDTO, productType, spuGroupMap);
            log.info(" skuActivity,{},spuGroupMap,{}", JsonUtils.toJsonString(skuActivity), JsonUtils.toJsonString(spuGroupMap));
            productList.forEach((pageRespVO) -> {
                Long spuId = pageRespVO.getSpuId();
                // sku 绑定活动列表
                List<SupplierActivityDTO> activityDTOS = skuActivity.get(spuId);
                // 如果没有活动跳过
                if (Objects.isNull(activityDTOS)) {
                    return;
                }
                // 渲染活动素材
                List<SpuActivityLabelVO> labelVOList = ActivityConvert.INSTANCE.convertSpuListActivity(activityDTOS);
                if (Objects.nonNull(labelVOList)) {
                    for (SpuActivityLabelVO labelVO : labelVOList) {
                        MaterialCacheVO material = portalCacheService.getMaterial(MaterialCacheVO.getActivityCacheKey(labelVO.getActivityId()));
                        if (StringUtils.isNotEmpty(material.getValidateMaterial())) {
                            pageRespVO.setMaterialUrl(material.getValidateMaterial());
                        }
                    }
                }
                // 查询已上架的SKU
                SkuUnitGroupKeyDTO spuGroupKey = new SkuUnitGroupKeyDTO(pageRespVO.getSpuId(), branchDTO.getAreaId(), pageRespVO.getClassId(), productType);
                Set<SkuUnitGroupDTO> unitGroupDTO = spuGroupMap.get(spuGroupKey);
                Map<Long, SkuUnitGroupDTO> releaseSkuMap = unitGroupDTO.stream().collect(Collectors.toMap(SkuUnitGroupDTO::getSkuId, item -> item));
                List<UnitSuperPriceVO> allPrice = this.metchNormalProductActivityPrice(branchDTO, activityDTOS, spuId, releaseSkuMap);
                log.info(" allPrice,{}", JsonUtils.toJsonString(allPrice));
                // 匹配最小价格活动单位
                if (!allPrice.isEmpty()) {
                    List<UnitSuperPriceVO> priceSortList = allPrice.stream().sorted(Comparator.comparing(UnitSuperPriceVO::getSuperPrice)).collect(Collectors.toList());
                    if (!priceSortList.isEmpty()) {
                        // 设置单位特价
                        UnitSuperPriceVO superPriceVO = priceSortList.get(NumberPool.INT_ZERO);
                        pageRespVO.setPromotionPrice(superPriceVO.getSuperPrice());
                        pageRespVO.setPromotionUnit(superPriceVO.getUnitType());
                        pageRespVO.setPromotionSkuId(superPriceVO.getSkuId());

                        // 替换单位, 渲染秒杀, 特价单位信息
                        SkuUnitGroupDTO groupDTO = releaseSkuMap.get(superPriceVO.getSkuId());
                        groupDTO.getUnitList().forEach(unit -> {
                            if (unit.getUnitSize().equals(superPriceVO.getUnitType().getType())) {
                                pageRespVO.setSuggestPrice(unit.getSuggestPrice());
                                pageRespVO.setMarkPrice(unit.getMarkPrice());
                                pageRespVO.setJumpOq(unit.getJumpOq());
                                pageRespVO.setMaxOq(unit.getMaxOq());
                                pageRespVO.setMinOq(unit.getMinOq());
                                pageRespVO.setUnit(unit.getUnit());
                                pageRespVO.setUnitSize(unit.getUnitSize());
                                pageRespVO.setUnitName(unit.getUnitName());

                                // 计算秒杀特价优先展示, 活动与最小库存转换关系
                                SpuDTO spuDTO = portalCacheService.getSpuDTO(spuId);
                                BigDecimal stock = redisStockService.getSkuStock(superPriceVO.getSkuId());
                                // 单位转换比例
                                BigDecimal unitSizeQty = spuDTO.getUnitSizeQty(unit.getUnitSize());
                                // 应购买数量最小为1，故转换成整数类型计算，小数位舍弃
                                Long validNum = StockUtil.stockDivide(stock, unitSizeQty).longValue();
                                if (Objects.isNull(pageRespVO.getMaxOq()) || pageRespVO.getMaxOq() == 0 || validNum < pageRespVO.getMaxOq() ) {
                                    // 根据库存数量计算最大限购数量
                                    pageRespVO.setMaxOq(validNum);
                                    if (validNum == NumberPool.LONG_ZERO) {
                                        pageRespVO.setStock(BigDecimal.ZERO);
                                    }
                                }
                            }
                        });
                    }
                }
                // 需要仅保留, 匹配成功的特价/秒杀活动
                Set<Long> validateSpAcitvity = allPrice.stream().map(UnitSuperPriceVO::getActivityId).collect(Collectors.toSet());
                log.info(" labelVOList,{}",JsonUtils.toJsonString(labelVOList));
                // 是特价或者秒杀, 标签是实际还有效的
                labelVOList = labelVOList.stream().filter(activity -> !PrmNoEnum.isSpOrSk(activity.getPrmNo()) || validateSpAcitvity.contains(activity.getActivityId())).collect(Collectors.toList());
                pageRespVO.setSpuActivityLabelList(labelVOList);
            });
        });

    }

    @Override
    public void renderActivityItemDetail(BranchDTO branch, SpuDetailRespVO detailReslut) {
        ProductType productType = ProductType.formValue(detailReslut.getType());
        Long spuId = detailReslut.getSpuId();
        // 列表spu管理的上架SKU
        Map<SkuUnitGroupKeyDTO, Set<SkuUnitGroupDTO>> spuGroupMap = new HashMap<>();
        SkuUnitGroupKeyDTO groupKey = new SkuUnitGroupKeyDTO(spuId, branch.getAreaId(), detailReslut.getClassId(), productType);
        spuGroupMap.put(groupKey, new HashSet<>(portalCacheService.getSkuUnitGroupDTO(groupKey)));
        // 获取spu 和 促销活动的绑定关系
        Map<Long, List<SupplierActivityDTO>> skuActivity = this.getActivityService().applySpuActivity(branch, productType, spuGroupMap);// sku 绑定活动列表
        List<SupplierActivityDTO> activityDTOS = skuActivity.get(spuId);
        // 如果没有活动跳过 或者是组合促销商品
        if (Objects.isNull(activityDTOS) || detailReslut.getItemType() == NumberPool.INT_ONE) {
            return;
        }
        // 处理秒杀和特价特殊逻辑
        List<UnitSuperPriceVO> allPrice = this.metchNormalProductActivityPrice(branch, activityDTOS, spuId, spuGroupMap.get(groupKey).stream().collect(Collectors.toMap(SkuUnitGroupDTO::getSkuId, item -> item)));
        // 匹配最小价格活动单位
        if (!allPrice.isEmpty()) {
            List<UnitSuperPriceVO> priceSortList = allPrice.stream().filter(item -> Objects.nonNull(item.getSuperPrice())).sorted(Comparator.comparing(UnitSuperPriceVO::getSuperPrice)).collect(Collectors.toList());
            if (!priceSortList.isEmpty()) {
                Map<Long, SkuDetailRespVO> releaseSkuMap = detailReslut.getSkus().stream().collect(Collectors.toMap(SkuDetailRespVO::getSkuId, item -> item));
                // 循环特价/秒杀 匹配详情数据
                for (UnitSuperPriceVO superPriceVO : priceSortList) {
                    if (releaseSkuMap.containsKey(superPriceVO.getSkuId())) {
                        SkuDetailRespVO skuUnitGroupDTO = releaseSkuMap.get(superPriceVO.getSkuId());
                        Map<Integer, SkuDetailRespVO.SkuUnit> skuUnitMap = skuUnitGroupDTO.getUnitList().stream().collect(Collectors.toMap(SkuDetailRespVO.SkuUnit::getUnitSize, unit -> unit));
                        // 具体单位数据
                        SkuDetailRespVO.SkuUnit skuUnit = skuUnitMap.get(superPriceVO.getUnitType().getType());
                        if (Objects.nonNull(skuUnit)) {
                            skuUnit.setPromotionPrice(superPriceVO.getSuperPrice());
                        }
                    }
                }
            }
        }
        // 需要仅保留, 匹配成功的特价/秒杀活动
        Set<Long> validateSpAcitvity = allPrice.stream().map(UnitSuperPriceVO::getActivityId).collect(Collectors.toSet());
        // 是特价或者秒杀, 标签是实际还有效的
        activityDTOS = activityDTOS.stream().filter(activity -> !PrmNoEnum.isSpOrSk(activity.getPrmNo()) || validateSpAcitvity.contains(activity.getActivityId())).collect(Collectors.toList());
        detailReslut.setActivityList(this.getActivityService().getSpuDetailActivityLabelVOS(activityDTOS));
    }

    @Override
    public YhPageSupplierGroupItemVO getYhItemInfoVO(AreaItemDTO areaItem, EsYhProduct esItem, BranchDTO branchDTO) {
        SpuDTO spuDTO = portalCacheService.getSpuDTO(esItem.getSpuId());
        SkuDTO skuDTO = portalCacheService.getSkuDTO(esItem.getSkuId());

        YhPageSupplierGroupItemVO itemVO = YhPageSupplierGroupItemVO.builder()
                .yhId(esItem.getYhId())
                .itemId(esItem.getAreaItemId())
                .spuId(esItem.getSpuId())
                .skuId(esItem.getSkuId())
                .supplierId(spuDTO.getSupplierId())
                .brandId(spuDTO.getBrandId())
                .categoryId(spuDTO.getCatgoryId())
                .midSize(spuDTO.getMidSize())
                .largeSize(spuDTO.getLargeSize())
                .markPrice(skuPriceService.getAreaSkuPrice(branchDTO, esItem.getMallUnitType(), esItem.getSkuId()))
                .productNum(esItem.getPosSuggestQty())
                .selected(esItem.getChecked() == NumberPool.INT_ONE)
                .spuName(spuDTO.getSpuName())
                .spuThumb(spuDTO.getThumb())
                .specName(PropertyAndValDTO.getProperties(skuDTO.getProperties()))
                .skuUnit(spuDTO.getUnit(esItem.getMallUnitType()))
                .minOq(skuDTO.getMinOq(esItem.getMallUnitType()))
                .jumpOq(skuDTO.getJumpOq(esItem.getMallUnitType()))
                .maxOq(skuDTO.getMaxOq(esItem.getMallUnitType()))
                .shelfStatus(areaItem.getShelfStatus(esItem.getMallUnitType()))
                .stockConvertRate(spuDTO.stockConvert(esItem.getMallUnitType()).longValue())
                .unitSize(esItem.getMallUnitType())
                .yhInfo(esItem)
                .build();

        // 剩余库存
        Long balanceStock = redisStockService.getSurplusSaleQty(esItem.getSkuId());
        itemVO.setStockQty(balanceStock);
        // 剩余库存数量 / 转换比例 = 当前最大可购买
        itemVO.setMaxQty(balanceStock / itemVO.getStockConvertRate());
        // 如有有提供单位信息, 回显单位
        itemVO.setUnit(spuDTO.getUnit(esItem.getMallUnitType()));
        // 如果商品下架了, 那就取消选中
        if (itemVO.getShelfStatus() == NumberPool.INT_ZERO) {
            itemVO.setSelected(false);
        }
        // 设置应付金额
        itemVO.setPayAmt(itemVO.getMarkPrice().multiply(new BigDecimal(esItem.getPosSuggestQty())));
        return itemVO;
    }

    @Override
    public ActivityVerifyItemDTO buildActivityVerifyItemByCouponOrderValidate(BranchDTO branch, SupplierItemDTO supplierItem, OrderValidItemDTO item) {
        AppCarIdDTO carId = AppCarIdDTO.build(item.getCarId());
        SkuDTO skuDTO = portalCacheService.getSkuDTO(supplierItem.getSkuId());
        SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
        // 全国商品价格
        BigDecimal salePrice = skuPriceService.getSupplierSkuPrice(branch, carId.getUnitSize(), skuDTO.getSkuId());
        // 组装订单商品验证信息
        item.setAmt(salePrice.multiply(new BigDecimal(item.getNum())));
        ActivityConvert.INSTANCE.buildSetOrderValid(item, spuDTO, carId);
        // 组装促销验证信息
        ActivityVerifyItemDTO verifyItemDTO = ActivityConvert.INSTANCE.convertActivityDTO(item, carId, spuDTO, salePrice);
        verifyItemDTO.setStockConvertRate(spuDTO.stockConvert(verifyItemDTO.getUnitSize()));
        verifyItemDTO.setItemId(supplierItem.getSupplierItemId());
        verifyItemDTO.setDiscountAmt(BigDecimal.ZERO);
        // 计算应付金额
        verifyItemDTO.setAdequateList(new ArrayList<>());
        verifyItemDTO.setPayAmt(NumberUtil.toBigDecimal(verifyItemDTO.getNum()).multiply(verifyItemDTO.getItemPrice()));
        return verifyItemDTO;
    }

    @Override
    public ActivityVerifyItemDTO buildActivityVerifyItemByCouponOrderValidate(BranchDTO branch, AreaItemDTO areaItem, OrderValidItemDTO item) {
        AppCarIdDTO carId = AppCarIdDTO.build(item.getCarId());
        SkuDTO skuDTO = portalCacheService.getSkuDTO(areaItem.getSkuId());
        SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
        // 本地商品价格
        BigDecimal salePrice = skuPriceService.getAreaSkuPrice(branch, carId.getUnitSize(), skuDTO.getSkuId());
        // 组装订单商品验证信息
        item.setAmt(salePrice.multiply(new BigDecimal(item.getNum())));
        ActivityConvert.INSTANCE.buildSetOrderValid(item, spuDTO, carId);
        // 组装促销验证信息
        ActivityVerifyItemDTO verifyItemDTO = ActivityConvert.INSTANCE.convertActivityDTO(item, carId, spuDTO, salePrice);
        verifyItemDTO.setStockConvertRate(spuDTO.stockConvert(verifyItemDTO.getUnitSize()));
        verifyItemDTO.setItemId(areaItem.getAreaItemId());
        // 计算应付金额
        verifyItemDTO.setDiscountAmt(BigDecimal.ZERO);
        verifyItemDTO.setAdequateList(new ArrayList<>());
        verifyItemDTO.setPayAmt(NumberUtil.toBigDecimal(verifyItemDTO.getNum()).multiply(verifyItemDTO.getItemPrice()));
        return verifyItemDTO;
    }

    // 获取各级分润比例
    private RateResultDTO calculateProfitSharingRates(Long sysCode, Long areaId, SpuDTO spuDTO, BranchDTO branchDto) {
        //分润模式
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(sysCode);
        if(ToolUtil.isEmpty(payConfigDTO) || ToolUtil.isEmpty(branchDto)){
            return null;
        }
        RateResultDTO rateResultDTO = new RateResultDTO();
        if (ToolUtil.isNotEmpty(branchDto.getColonelId())) {
            BigDecimal softwareRate = BigDecimal.ZERO;
            BigDecimal partnerRate = BigDecimal.ZERO;
            BigDecimal colonelRate = BigDecimal.ZERO;
            // 获取业务员信息
            ColonelDTO colonel;
            if (Objects.nonNull(MallSecurityUtils.getColonelId())) {
                // 优先获取当前业务员身份
                colonel = portalCacheService.getColonel(MallSecurityUtils.getColonelId());
            } else {
                colonel = portalCacheService.getColonel(branchDto.getColonelId());
            }
            if (ToolUtil.isNotEmpty(colonel)) {
                // 获取利润分成比例
                CatgoryRateDTO catgoryRate = portalCacheService.getCatgoryByIdAndAreaId(spuDTO.getCatgoryId(), areaId);
                if (Objects.nonNull(catgoryRate)) {
                    partnerRate = StringUtils.isNull(catgoryRate.getPartnerRate()) ? BigDecimal.ZERO : catgoryRate.getPartnerRate();
                    // 获取软件商分润比例
                    PartnerDto partnerDto = portalCacheService.getPartnerDto(branchDto.getSysCode() + "");
                    softwareRate = ToolUtil.isEmpty(partnerDto.getSoftwareRate()) || partnerDto.getSoftwareRate().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO
                            ? BigDecimal.ZERO : partnerDto.getSoftwareRate();
                    if (StringUtils.isNotNull(catgoryRate)) {
                        if (StringUtils.isNotNull(colonel) && StringUtils.isNotNull(colonel.getPcolonelId()) && !"Y".equalsIgnoreCase(colonel.getIsColonelAdmin())) {
                            // 业务员
                            if (Objects.nonNull(catgoryRate.getColonel2Rate())) {
                                colonelRate = colonelRate.add(catgoryRate.getColonel2Rate());
                            }
                        } else {
                            // 业务员管理员
                            if (Objects.nonNull(catgoryRate.getColonel1Rate())) {
                                colonelRate = colonelRate.add(catgoryRate.getColonel1Rate());
                            }
                            if (Objects.nonNull(catgoryRate.getColonel2Rate())) {
                                colonelRate = colonelRate.add(catgoryRate.getColonel2Rate());
                            }
                        }
                    }
                }
            }
            rateResultDTO.setColonelRate(colonelRate);
            rateResultDTO.setPartnerRate(partnerRate);
            rateResultDTO.setSoftwareRate(softwareRate);
            rateResultDTO.setColonel(colonel);
            rateResultDTO.setPayConfigDTO(payConfigDTO);
        }
        return rateResultDTO;
    }

    @NotNull
    private List<UnitSuperPriceVO> metchNormalProductActivityPrice(BranchDTO branchDTO, List<SupplierActivityDTO> activityDTOS, Long spuId, Map<Long, SkuUnitGroupDTO> releaseSkuMap) {
        Set<Long> releaseSku = releaseSkuMap.keySet();
        // 特价活动
        List<SupplierActivityDTO> spAcitivtyList = activityDTOS.stream().filter(item -> PrmNoEnum.isSp(item.getPrmNo())).collect(Collectors.toList());
        // 秒杀活动
        List<SupplierActivityDTO> skAcitivtyList = activityDTOS.stream().filter(item -> PrmNoEnum.isSk(item.getPrmNo())).collect(Collectors.toList());
        if (spAcitivtyList.isEmpty() && skAcitivtyList.isEmpty()) {
            return ListUtil.empty();
        }
        SpuDTO spuDTO = portalCacheService.getSpuDTO(spuId);
        // 特价秒杀活动单位
        ArrayList<UnitSuperPriceVO> allPrice = new ArrayList<>();
        if (!skAcitivtyList.isEmpty()) {
            // 获取最低秒杀价
            // spu 纬度去命中的活动可能活命中多个活动
            // 所有spu相关的秒杀规则
            List<SkRuleDTO> skRuleDTOS = skAcitivtyList.stream().map(SupplierActivityDTO::getSkRules).flatMap(
                    ruleList -> ruleList.stream().filter(
                            rule -> Objects.nonNull(rule.getSpuId()) && rule.getSpuId().equals(spuId) && releaseSku.contains(rule.getSkuId())
                    )
            ).collect(Collectors.toList());
            skRuleDTOS.forEach(rule -> {
                // 上架单位判断
                releaseSkuMap.get(rule.getSkuId())
                        .getUnitList()          // 获得sku上架单位信息
                        .stream()
                        .filter(definiteUnit -> redisActivityService.validateRuleStock(branchDTO.getBranchId(), rule, spuDTO.stockConvert(definiteUnit.getUnitSize()), definiteUnit.getUnitSize())) // 过滤活动库存不足的
                        .forEach(definiteUnit -> allPrice.add(new UnitSuperPriceVO(UnitTypeEnum.formValue(definiteUnit.getUnitSize()), rule))); // 添加到结果集, 因为同一个spu可能存在其他价格更低的sku
            });
        } else if (!spAcitivtyList.isEmpty()) {
            // 获取最低特价
            // spu 纬度去命中的活动可能活命中多个活动
            // 所有spu相关的秒杀规则, 过滤出能和上架SKU绑定的活动
            List<SpRuleDTO> spRuleDTOS = spAcitivtyList.stream().map(SupplierActivityDTO::getSpRules).flatMap(
                    ruleList -> ruleList.stream().filter(
                            rule -> Objects.nonNull(rule.getSpuId()) && rule.getSpuId().equals(spuId) && releaseSku.contains(rule.getSkuId())
                    )
            ).collect(Collectors.toList());
            spRuleDTOS.forEach(rule -> {
                // 上架单位判断
                releaseSkuMap.get(rule.getSkuId())
                        .getUnitList()          // 获得sku上架单位信息
                        .stream()
                        .filter(definiteUnit -> redisActivityService.validateRuleStock(branchDTO.getBranchId(), rule, spuDTO.stockConvert(definiteUnit.getUnitSize()), definiteUnit.getUnitSize())) // 过滤活动库存不足的
                        .forEach(definiteUnit -> allPrice.add(new UnitSuperPriceVO(UnitTypeEnum.formValue(definiteUnit.getUnitSize()), rule))); // 添加到结果集, 因为同一个spu可能存在其他价格更低的sku
            });
        }
        // 有些并不是所有的单位都参与的特价或者秒杀
        return allPrice.stream().filter(item -> Objects.nonNull(item.getSuperPrice())).collect(Collectors.toList());
    }
}
