package com.zksr.portal.service.impl.handler;


import cn.hutool.core.util.NumberUtil;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.enums.request.SyncSourceType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.service.*;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.CreateOrderRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.controller.mall.vo.car.CarRemoveReqVO;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.handler.TradeOrderHandlerService;
import com.zksr.portal.service.mall.ICarService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.product.api.blockScheme.BlockSchemeApi;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponApplyDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.sysconfig.GlobalPayConfigDTO;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.OrderCutAmtDTO;
import com.zksr.trade.api.order.dto.TrdOrderResDto;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderDtlSaveVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderSaveVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderSettleSaveVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年03月28日 19:58
 * @description: 正常订单实现操作
 */
@Component
@Slf4j
public class TradeNormalOrderHandlerServiceImpl implements TradeOrderHandlerService {

    @Autowired
    private ICarService carService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private RedisStockService redisStockService;
    @Autowired
    private RedisCacheService redisCacheService;
    @Autowired
    private AccountApi accountApi;
    @Autowired
    private IPortalCacheService portalCacheService;
    @Autowired
    private RedisSysConfigService redisSysConfigService;
    @Autowired
    private CouponApi couponApi;
    @Autowired
    private RedisActivityService redisActivityService;
    @Resource
    private BranchApi branchApi;
    @Autowired
    private BlockSchemeApi blockSchemeApi;

    @Resource
    private List<TradePriceCalculatorService> priceCalculators;

    @Resource
    private OrderApi orderApi;


    @Override
    public void beforeOrderCreate(RemoteSaveOrderVO orderVo) {
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(MallSecurityUtils.getLoginMember().getSysCode());
        if (ToolUtil.isEmpty(payConfigDTO) || ToolUtil.isEmpty(payConfigDTO.getStoreOrderPayPlatform())) {
            throw new ServiceException("平台【" + MallSecurityUtils.getLoginMember().getSysCode() + "】未设置支付平台！");
        }

        // 扣减库存
        // 放到OccupyB2bStockTransactionHandler事务处理器执行
//        orderVo.getSupplierOrderDtlSaveVOS().forEach(supplierItem -> {
//            if (redisStockService.decreaseSkuStock(supplierItem.getSkuId(), supplierItem.getTotalNum())) {
//                redisStockService.incrSkuSaledQty(supplierItem.getSkuId(), supplierItem.getTotalNum());
//            } else {
//                throw new ServiceException("下单失败，商品【" + supplierItem.getSkuId() + "】库存不足！");
//            }
//        });

        /**
         * 验证入驻商信息是否可以下单
         */
        checkSupplierPlatFromAccount(orderVo, payConfigDTO);

        /**
         * 验证门店是否可以下单
         */
        checkBranchArrears(orderVo);

        // 累加门店未支付角标统计
        portalCacheService.incrPendingPaymentOrderTotal(orderVo.getOrderSaveVo().getBranchId());

        // 累加业务员未支付角标统计
        portalCacheService.incrPendingPaymentOrderTotal(orderVo.getOrderSaveVo().getColonelId());

        /**
         * 计算订单支付手续费用
         */
        calculatorPayFee(orderVo, payConfigDTO);

        orderVo.getSupplierOrderDtlSaveVOS().forEach(dtl -> {
            VisualSettingMasterDto visualMaster = portalCacheService.getVisualMasterBySupplierId(dtl.getSupplierId());
            if (visualMaster != null && SyncSourceType.ANNTOERP.getCode().equals(visualMaster.getSourceType())) {
                // 对接安得ERP，检查是否有ERP商品编码
                SkuDTO skuDTO = portalCacheService.getSkuDTO(dtl.getSkuId());
                if (skuDTO == null || StringUtils.isBlank(skuDTO.getSourceNo())) {
                    throw new ServiceException("下单失败，商品不存在");
                }
            }
        });

        // 扣减库存
//        orderVo.getSupplierOrderDtlSaveVOS().forEach(supplierItem -> {
//            redisStockService.incrSkuSaledQty(supplierItem.getSkuId(), supplierItem.getTotalNum());
//            redisStockService.incrSkuOccupiedQty(supplierItem.getSkuId(), supplierItem.getTotalNum());
//        });

    }

    @Override
    public void afterOrderCreate(CreateOrderRequest request, TrdOrderResDto orderResDto, RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        /**
         * 订单创建成功后，删除购物车数据
         */
        CarRemoveReqVO carRemoveVo = new CarRemoveReqVO();
        carRemoveVo.setBranchId(request.getBranchId());
        carRemoveVo.setOpType(0);
        List<String> cars = new ArrayList<>();
        request.getItems().forEach(supplier -> {
            supplier.getSupplierItems().forEach(item -> {
                cars.add(item.getCarId());
            });
        });
        carRemoveVo.setCarId(cars);
        carRemoveVo.setProductType(ProductType.getProduct(orderVo.getOrderSaveVo().getOrderType()).getType());
        carService.removeCar(carRemoveVo);

        /**
         * 若存在优惠卷列表，将优惠劵列表核销
         */
//        放到ApplyCouponTransactionHandler事务处理器中执行
//        applyCoupon(orderVo);

        /**
         * 更新本次订单使用活动已购数量缓存
         */

        updateRedisActivitySaleQty(orderVo, resp);
//        放到ActivitySaleQtyUpdateTransactionHandler事务处理器中执行
//        priceCalculators.forEach(service -> service.updateRedisActivitySaleQty(orderVo, resp));

        // 放到OrderSuccessEditBranchTransactionHandler事务处理器中执行
        // 门店
//        BranchDTO branchDTO = portalCacheService.getBranchDto(orderVo.getOrderSaveVo().getBranchId());
//        if (Objects.isNull(branchDTO.getFirstOrderFlag()) || branchDTO.getFirstOrderFlag() == NumberPool.INT_ZERO) {
//            // 系统首单, 更新已下单
//            MemBranchSaveReqVO updateBranch = new MemBranchSaveReqVO();
//            updateBranch.setBranchId(orderVo.getOrderSaveVo().getBranchId());
//            updateBranch.setFirstOrderFlag(NumberPool.INT_ONE);
//            updateBranch.setFirstOrderNo(orderResDto.getOrderNo());
//            branchApi.edit(updateBranch);
//        }

        /**
         * 保存成功后，支付的订单添加到缓存中
         */
//        放到AddUnpayOrderTransactionHandler事务处理器中执行
//        Long createTime = System.currentTimeMillis();
//        if (ToolUtil.isNotEmpty(orderResDto.getCreateTime())) {
//            createTime = orderResDto.getCreateTime().getTime();
//        }
//        // 订单保存成功，添加定时超时未支付缓存
//        redisCacheService.addUnpayOrderList(new TrdOrderInfo(orderResDto.getOrderNo(), createTime, orderResDto.getSysCode(), orderResDto.getDcId()));

        /**
         *  这个方法里面, 在此时已经完成订单事务了, 可以直接删除门店截单数据缓存
         */
        orderVo.getSupplierOrderDtlSaveVOS().stream().collect(Collectors.groupingBy(TrdSupplierOrderDtlSaveVO::getSupplierId)).forEach((supplierId, trdSupplierOrderDtlSaveVOS) -> {
            trdSupplierOrderDtlSaveVOS.stream().map(TrdSupplierOrderDtlSaveVO::getItemType).filter(Objects::nonNull).forEach((itemType) -> {
                portalCacheService.removeOrderCutAmt(
                        OrderCutAmtDTO.CacheKey.builder()
                                .productType(itemType == NumberPool.INT_ONE ? ProductType.LOCAL.getType() : ProductType.GLOBAL.getType())
                                .branchId(orderVo.getOrderSaveVo().getBranchId())
                                .supplierId(supplierId)
                                .build()
                );
            });
        });

        /**
         * 发送货到付款订单支付成功或订单应付支付金额为消息 执行订单支付成功回调流程
         */
        orderApi.sendOrderHdfkOrPaySuccess(orderVo);
    }

    @Override
    public void errorOrderCreate(RemoteSaveOrderVO orderVo) {
        // 返还库存
//        orderVo.getSupplierOrderDtlSaveVOS().forEach(supplierItem -> {
//            redisStockService.incrSkuSaledQty(supplierItem.getSkuId(), supplierItem.getTotalNum().multiply(NumberPool.BIGDECIMAL_GROUND));
//        });
    }

    /**
     * 验证入驻商是否维护第三方支付信息，
     * 验证入驻商余额是否大于本次订单利润金额,
     * 如未维护，则不允许下单
     */
    private void checkSupplierPlatFromAccount(RemoteSaveOrderVO orderVo, PayConfigDTO payConfigDTO) {
        AtomicBoolean boo = new AtomicBoolean(false);
        StringBuilder errorMsg = new StringBuilder("");
//        Map<Long, BigDecimal> profitAmtMap = getSupplierProfitAmt(orderVo);

        orderVo.getSupplierOrderSaveVOs().forEach(supplier -> {
            AccAccountDTO account = accountApi.getSupplierAccount(supplier.getSupplierId()).getCheckedData();

            if (ToolUtil.isEmpty(account)) {
                boo.set(true);
                errorMsg.append("入驻商【" + supplier.getSupplierName() + "】不存在资金账户！");
                return;
            }
            // 判断入驻商有没有欠款
            if (!Objects.equals(payConfigDTO.getStoreOrderPayPlatform(), PayChannelEnum.WX_B2B_PAY.getCode()) && account.debt()) {
                boo.set(true);
                errorMsg.append("入驻商【" + supplier.getSupplierName() + "】预留金不足,请先充值~");
                return;
            }

            /**
             * 验证入驻商是否维护第三方支付信息 在线支付才校验
             */
            if (Objects.equals(OrderPayWayEnum.ONLINE.getPayWay(), orderVo.getOrderSaveVo().getPayWay())
                    && PayChannelEnum.getPayOnlineSupportDivide(payConfigDTO.getStoreOrderPayPlatform())
                    && (ToolUtil.isEmpty(account.getPlatformAccount()) || ToolUtil.isEmpty(account.getPlatformAccount().getAltMchNo()))) {
                boo.set(true);
                errorMsg.append("入驻商【" + supplier.getSupplierName() + "】未完善第三方支付信息！");
            }
        });
        // 当前支付平台为b2b微信支付，同一个订单只能下一个入驻商的订单
//        if (Objects.equals(payConfigDTO.getStoreOrderPayPlatform(), PayChannelEnum.WX_B2B_PAY.getCode()) && orderVo.getSupplierOrderSaveVOs().size() > 1) {
//            boo.set(true);
//            errorMsg.append("支付平台【"+PayChannelEnum.WX_B2B_PAY.getName()+"】一个订单只支持一个入驻商！");
//        }

        if (boo.get()) throw new ServiceException(errorMsg.toString());
    }

    /**
     * 验证门店是否可以下单
     *
     * @param orderVo
     */
    public void checkBranchArrears(RemoteSaveOrderVO orderVo) {
        // 支付方式不是 货到付款 直接返回
        if (!Objects.equals(OrderPayWayEnum.HDFK.getPayWay(), orderVo.getOrderSaveVo().getPayWay())) {
            return;
        }

        AtomicBoolean boo = new AtomicBoolean(false);
        StringBuilder errorMsg = new StringBuilder("");

        // 货到付款时校验 账户 当前门店可欠款余额
        BranchDTO branchDTO = portalCacheService.getBranchDto(orderVo.getOrderSaveVo().getBranchId());
        BigDecimal hdfxMaxAmt = ToolUtil.isNotEmpty(branchDTO.getHdfkMaxAmt()) ? branchDTO.getHdfkMaxAmt() : BigDecimal.ZERO; // 门店最大可欠款金额
        AccAccountDTO accountBranch = portalCacheService.getAccount(MallSecurityUtils.getLoginMember().getBranchId(), MerchantTypeEnum.BRANCH_DEBT.getType(), PayChannelEnum.NONE.getCode());
        BigDecimal accountBranchAmt = (ToolUtil.isEmpty(accountBranch) || ToolUtil.isEmpty(accountBranch.getWithdrawableAmt())) ? BigDecimal.ZERO : accountBranch.getWithdrawableAmt().abs();
        // 门店可欠款金额是否大于等于 本次订单支付金额 + 门店账户欠款金额
        if (!NumberUtil.isGreaterOrEqual(hdfxMaxAmt, accountBranchAmt.add(orderVo.getOrderSaveVo().getPayAmt()))) {
            boo.set(true);
            errorMsg.append("门店【" + branchDTO.getBranchName() + "】欠款已达上限，请先还款后再下单！");
        }

        if (boo.get()) throw new ServiceException(errorMsg.toString());
    }

    /**
     * 入驻商本次分成总金额
     *
     * @param orderVo
     * @return
     */
    private Map<Long, BigDecimal> getSupplierProfitAmt(RemoteSaveOrderVO orderVo) {
        return orderVo.getSupplierOrderSettleSaveVOS().stream().collect(Collectors.groupingBy(TrdSupplierOrderSettleSaveVO::getSupplierId,
                Collectors.reducing(
                        BigDecimal.ZERO,
                        TrdSupplierOrderSettleSaveVO::sumProfitAmt,
                        BigDecimal::add
                )));
    }

    /**
     * 计算支付手续费
     */
    private void calculatorPayFee(RemoteSaveOrderVO orderVo, PayConfigDTO payConfigDTO) {
        /**
         * 计算总订单的支付手续费
         */
        BigDecimal payRate = BigDecimal.ZERO; // 支付平台手续费率
        BigDecimal payFee = BigDecimal.ZERO; // 本次订单手续费总金额

        // 获得订单手续费率和手续费总金额
        if (OrderPayWayEnum.ONLINE.getPayWay().equals(orderVo.getOrderSaveVo().getPayWay())) {  // 当等于在线支付时才获取手续费率
            GlobalPayConfigDTO globalPayConfigDTO = redisSysConfigService.getGlobalPayConfig(payConfigDTO.getStoreOrderPayPlatform());
            if (ToolUtil.isEmpty(globalPayConfigDTO) || ToolUtil.isEmpty(globalPayConfigDTO.getPay())) {
                throw new ServiceException("支付平台【" + payConfigDTO.getStoreOrderPayPlatform() + "】未设置手续费！");
            }
            payRate = globalPayConfigDTO.getPay().getFreeRate();
            if (ToolUtil.isNotEmpty(payRate) && payRate.compareTo(BigDecimal.ZERO) == 1) {
                payFee = orderVo.getOrderSaveVo().getPayAmt().multiply(payRate).setScale(StatusConstants.PRICE_RESERVE_2, BigDecimal.ROUND_HALF_UP);  //订单手续费总金额
                if (payFee.compareTo(BigDecimal.ZERO) == 0)
                    payFee = new BigDecimal("0.01"); // 最低手续费为0.01
            }
        }

        orderVo.getOrderSaveVo().setPayRate(payRate);
        orderVo.getOrderSaveVo().setPayFee(payFee);


        Map<String, List<TrdSupplierOrderSettleSaveVO>> sOrderSettleS = orderVo.getSupplierOrderSettleSaveVOS().stream().collect(Collectors.groupingBy(TrdSupplierOrderSettleSaveVO::getSupplierOrderNo));
        /**
         * 计算入驻商订单的手续费
         */
        AtomicInteger i = new AtomicInteger();
        final BigDecimal[] allocationAmtCount = {BigDecimal.ZERO};  // 已扣减总金额
        BigDecimal finalPayFee = payFee;

        List<TrdSupplierOrderSaveVO> supplierOrderSaveVOS = orderVo.getSupplierOrderSaveVOs().stream().sorted(Comparator.comparing(TrdSupplierOrderSaveVO::getSubPayAmt, Comparator.nullsFirst(BigDecimal::compareTo))).collect(Collectors.toList());
        BigDecimal finalPayRate = payRate;
        supplierOrderSaveVOS.forEach(supplierOrder -> {
            i.getAndIncrement();
            BigDecimal subPayFee = BigDecimal.ZERO;  // 入驻商订单手续费金额
            if (i.get() == orderVo.getSupplierOrderSaveVOs().size()) {
                subPayFee = finalPayFee.subtract(allocationAmtCount[0]);   // 最后一个入驻商得到剩下的手续金额
            } else {
                subPayFee = supplierOrder.getSubPayAmt().multiply(finalPayRate).setScale(StatusConstants.PRICE_RESERVE_2, BigDecimal.ROUND_HALF_UP);
            }
            allocationAmtCount[0] = allocationAmtCount[0].add(subPayFee);
            supplierOrder.setPayRate(finalPayRate);
            supplierOrder.setSubPayFee(subPayFee);

            /**
             * 计算订单结算数据手续费
             */
            final BigDecimal[] allocationSupplierAmtCount = {BigDecimal.ZERO};  // 入驻商已扣减总金额
            AtomicInteger k = new AtomicInteger();
            sOrderSettleS.get(supplierOrder.getSupplierOrderNo()).forEach(settle -> {
                k.getAndIncrement();
                BigDecimal subDtlPayFee = BigDecimal.ZERO;  // 入驻商商品手续费金额
                if (k.get() == sOrderSettleS.get(supplierOrder.getSupplierOrderNo()).size()) {
                    subDtlPayFee = supplierOrder.getSubPayFee().subtract(allocationSupplierAmtCount[0]);   // 最后一个商品得到剩下的入驻商手续金额
                } else {
                    subDtlPayFee = settle.getPayAmt().multiply(finalPayRate).setScale(StatusConstants.PRICE_RESERVE_2, BigDecimal.ROUND_HALF_UP);
                }
                allocationSupplierAmtCount[0] = allocationSupplierAmtCount[0].add(subDtlPayFee);
                settle.setPayRate(finalPayRate);
                settle.setPayFee(subDtlPayFee);
                settle.setSupplierDivideAmt(settle.getPayAmt().subtract(settle.getPayFee()));
            });
        });


    }

    /**
     * 优惠劵核销
     */
    private void applyCoupon(RemoteSaveOrderVO orderVo) {
        if (ToolUtil.isNotEmpty(orderVo.getOrderDiscountDtlSaveVOS()) && orderVo.getOrderDiscountDtlSaveVOS().size() > 0) {
            List<CouponApplyDTO> couponApplyDTOS = orderVo.getOrderDiscountDtlSaveVOS().stream()
                    .filter(orderDiscount -> orderDiscount.getDiscountType().equals(TrdDiscountTypeEnum.COUPON.getType()))
                    .map(orderDiscount -> {
                        CouponApplyDTO couponApplyDTO = new CouponApplyDTO();
                        couponApplyDTO.setCouponId(orderDiscount.getDiscountId());
                        couponApplyDTO.setRelateOrderNo(orderDiscount.getOrderNo());
                        return couponApplyDTO;
                    }).distinct().collect(Collectors.toList());

            if (ToolUtil.isEmpty(couponApplyDTOS) || couponApplyDTOS.size() <= 0) return;
            // 优惠劵核销
            couponApi.applyCoupon(couponApplyDTOS).getCheckedData();
        }
    }

    /**
     * 更新使用活动已购数量缓存
     */
    private void updateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        if (ToolUtil.isNotEmpty(orderVo.getOrderDiscountDtlSaveVOS()) && !orderVo.getOrderDiscountDtlSaveVOS().isEmpty()) {
            orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> !orderDiscount.getDiscountType().equals(TrdDiscountTypeEnum.COUPON.getType()))
                    .forEach(orderDiscount -> {
                        PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                        Long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                        // 秒杀活动
//                        if (orderDiscount.getDiscountType().equals(TrdDiscountTypeEnum.SK.getType())) {
//                            // 秒杀促销活动使用数量
//                            BigDecimal saleQty = BigDecimal.valueOf(orderDiscount.getSpSkCount()).multiply(orderDiscount.getSpSkUnitSizeQty());
//                            // set秒杀活动商品门店已购数量
//                            redisActivityService.setSkSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(),
//                                    saleQty, time, TimeUnit.SECONDS);
//                            // set秒杀活动商品已购总数量
//                            redisActivityService.setSkSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(),
//                                    saleQty, time, TimeUnit.SECONDS);
//                        }

                        // 特价活动
//                        if (orderDiscount.getDiscountType().equals(TrdDiscountTypeEnum.SP.getType())) {
//                            // 特价促销活动使用数量
//                            BigDecimal saleQty = BigDecimal.valueOf(orderDiscount.getSpSkCount()).multiply(orderDiscount.getSpSkUnitSizeQty());
//                            // 总活动缓存过期时间
//                            long ruleTime = time;
//                            // 活动商品缓存过期时间
//                            long itemRuleTime = time;
//                            // 活动商品已使用数量
//                            BigDecimal itemSaleQty =  BigDecimal.valueOf(orderDiscount.getSpSkCount()).multiply(orderDiscount.getSpSkUnitSizeQty());
//                            if (Objects.nonNull(activity.getTimesRule())) {
//                                switch (SpActivityTimesRuleEnum.formValue(activity.getTimesRule())){
//                                    case RULE_LEVEL_0: // 每日一次
//                                        ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
//                                        saleQty = NumberPool.BIGDECIMAL_MAX;
//                                        break;
//                                    case RULE_LEVEL_1: // 活动期间内仅一次
//                                        // 仅一次
//                                        saleQty = NumberPool.BIGDECIMAL_MAX;
//                                        // 标记活动已经达到限制
//                                        redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS);
//                                        break;
//                                    case RULE_LEVEL_4: // 商品级别每日一次
//                                        saleQty = BigDecimal.ZERO; // 不设置门店活动已购数量
//                                        ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
//                                        itemSaleQty = NumberPool.BIGDECIMAL_MAX; // 当天商品仅销售一次，将商品活动缓存数量拉到最大
//                                        itemRuleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate()); // 商品活动缓存过期时间 调整为当天23：59：59过期
//                                        break;
//                                    case RULE_LEVEL_5: // 商品级别仅一次
//                                        saleQty = BigDecimal.ZERO; // 不设置门店活动已购数量
//                                        ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
//                                        itemSaleQty = NumberPool.BIGDECIMAL_MAX; // 商品活动期间内仅销售一次，将商品活动缓存数量拉到最大
//                                        break;
//                                }
//                            }
//                            // 写门店+活动ID 缓存已购数量  直接更改成99999
//                            redisActivityService.setSpTotalSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(),
//                                    saleQty, ruleTime, TimeUnit.SECONDS);
//
//                            // set特价活动商品门店已购数量
//                            redisActivityService.setSpSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(),
//                                    itemSaleQty, itemRuleTime, TimeUnit.SECONDS);
//
//                            // set特价活动商品已购总数量
//                            redisActivityService.setSpSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(),
//                                    itemSaleQty, itemRuleTime, TimeUnit.SECONDS);
//                        }

                        // 买赠活动
//                        if (orderDiscount.getDiscountType().equals(TrdDiscountTypeEnum.BG.getType())) {
//                            redisActivityService.setBgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), BigDecimal.valueOf(orderDiscount.getGiftQty()), time, TimeUnit.SECONDS);
//                        }

                        // 满赠活动
//                        if (orderDiscount.getDiscountType().equals(TrdDiscountTypeEnum.FG.getType())) {
//                            redisActivityService.setFgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), BigDecimal.valueOf(orderDiscount.getGiftQty()), time, TimeUnit.SECONDS);
//
//                            if (Objects.nonNull(activity.getTimesRule()) && activity.getTimesRule() == NumberPool.INT_ONE) {
//                                // 活动只能参加一次
//                                // 标记活动已经达到限制
//                                redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS);
//                            }
//                        }
//                    });

//            resp.getPromotions().stream()
//                    .filter(promotion -> Objects.equals(promotion.getType(), TrdDiscountTypeEnum.CB.getType()))
//                    .forEach(promotion -> {
//                        PrmActivityDTO activity = portalCacheService.getActivityDto(promotion.getId());
//                        Long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());
//                        promotion.getPromotionItems()
//                                .stream().collect(Collectors.groupingBy(TradePriceCalculateResp.PromotionItem::getActivityRuleId))
//                                .forEach((key, value) -> {
//                                    redisActivityService.setCbSaleNum(promotion.getId(), key, BigDecimal.valueOf(value.get(NumberPool.INT_ZERO).getCount()), time, TimeUnit.SECONDS);
//                                });
//
//                        if (Objects.nonNull(activity.getTimesRule())) {
//                            Long ruleTime = time; //默认时间到活动截止时间到期  仅一次
//                            if (activity.getTimesRule() == NumberPool.INT_ZERO) { // 每日一次
//                                ruleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
//                            }
//                            // 标记活动已经达到限制
//                            redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(promotion.getId(), orderVo.getOrderSaveVo().getBranchId()), StringPool.ONE, ruleTime, TimeUnit.SECONDS);
//                        }
                    });


        }
    }


}
