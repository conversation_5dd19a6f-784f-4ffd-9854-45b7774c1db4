package com.zksr.portal.service.impl.mall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.CategoryRespVO;
import com.zksr.portal.convert.prdt.CategoryConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IActivityService;
import com.zksr.portal.service.mall.ISaleClassService;
import com.zksr.portal.service.mall.ISkuService;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.promotion.api.activity.dto.CbRuleDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 展示分类 service
 * @date 2024/7/10 15:02
 */
@Service
public class SaleClassServiceImpl implements ISaleClassService {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisActivityService redisActivityService;

    @Autowired
    private IActivityService activityService;

    @Override
    public List<AreaClassDTO> getAreaClassDTOS(Long areaId, Long channelId, Long branchId) {
        AreaDTO areaDto = portalCacheService.getAreaDto(areaId);
        if (ObjectUtil.isNull(areaDto) || ObjectUtil.equal(areaDto.getLocalFlag(), NumberPool.INT_ZERO)) {
            return ListUtil.empty();
        }
        //根据门店区域和门店渠道获取城市展示分类
        List<AreaClassDTO> areaClassAreaChannelList = portalCacheService.getAreaClassAreaChannel(areaId, channelId);
        //获取没有渠道的展示分类
        List<AreaClassDTO> areaClassAreaList = portalCacheService.getAreaClassAreaChannel(areaId, 0L);
        //合并list,并剔除重复数据
        if (ObjectUtil.isNull(areaClassAreaChannelList)) {
            areaClassAreaChannelList = new ArrayList<>();
        }
        if (ObjectUtil.isEmpty(areaClassAreaList)) {
            areaClassAreaList = new ArrayList<>();
        }
        List<AreaClassDTO> areaClassDTOList = Stream.concat(areaClassAreaChannelList.stream(), areaClassAreaList.stream()).distinct().collect(Collectors.toList());
//        3.根据门店经纬度匹配入驻商，查询入驻商查询所匹配到的电子围栏分类
        List<AreaClassDTO> areaClassSupplier = new ArrayList<>();
        if (ObjectUtil.isNotNull(branchId)) {
            areaClassSupplier = portalCacheService.getAreaClassBranch(branchId);
        }
        if (ToolUtil.isEmpty(areaClassSupplier)) {
            areaClassSupplier = new ArrayList<>();
        }

        List<AreaClassDTO> areaList =
                Stream.of(areaClassDTOList, areaClassSupplier)
                .flatMap(Collection::stream)
                .distinct()
                        .filter(item -> StringPool.ONE.equals(item.getStatus()))
                        .sorted(Comparator.comparing(AreaClassDTO::getSort))
                        .collect(Collectors.toList());

        Set<Long> validThreeSaleClassIdList = ConcurrentHashMap.newKeySet();
        validThreeSaleClassIdList.addAll(redisService.getCacheSetMembers(RedisConstants.getReleaseCityClassSet(areaDto.getAreaId())));
        // 添加组合商品特殊类目
        // 组合商品的可见类目是实时计算出来的
        if (Objects.nonNull(branchId)) {
            BranchDTO branchDTO = portalCacheService.getBranchDto(branchId);
            // 获取当前城市发布过组合促销的入驻商
            // 获取当前城市发布过组合促销的入驻商
            List<CbRuleDTO> branchCbRules = this.getBranchCbRules(branchDTO, ProductType.LOCAL);
            validThreeSaleClassIdList.addAll(branchCbRules.stream().map(CbRuleDTO::getShelfClassId).collect(Collectors.toSet()));
        }

        // 搭建三级树, 过滤没有完整三级的数据
        // 针对场景:  一级是电子围栏, 二级, 三级又不是的, 傻逼情况
        // 有上架的商品的三级, 才正常显示分类
        ArrayList<AreaClassDTO> result = new ArrayList<>();
        Map<Long, List<AreaClassDTO>> pidMap = areaList.stream().filter(item -> Objects.nonNull(item.getPid())).collect(Collectors.groupingBy(AreaClassDTO::getPid));
        List<AreaClassDTO> firstList = areaList.stream().filter(item -> Objects.nonNull(item.getLevel()) && item.getLevel() == 1).collect(Collectors.toList());
        for (AreaClassDTO first : firstList) {
            List<AreaClassDTO> secondList = pidMap.get(first.getAreaClassId());
            if (Objects.nonNull(secondList)) {
                List<AreaClassDTO> validSecondList = new ArrayList<>();
                for (AreaClassDTO second : secondList) {
                    List<AreaClassDTO> threeList = pidMap.get(second.getAreaClassId());
                    if (ObjectUtil.isEmpty(threeList)) {
                        continue;
                    }
                    boolean hashValidThree = false;
                    for (AreaClassDTO three : threeList) {
                        if (validThreeSaleClassIdList.contains(three.getAreaClassId())) {
                            result.add(three);
                            hashValidThree = true;
                        }
                    }
                    if (hashValidThree) {
                        // 添加有效的三级
                        validSecondList.add(second);
                    }
                }
                if (!validSecondList.isEmpty()) {
                    // 添加有效的二级
                    result.addAll(validSecondList);
                    result.add(first);
                }
            }
        }
        return result;
    }

    @Override
    public List<SaleClassDTO> getSaleClassDTOS(Long sysCode, Long groupId) {
        List<SaleClassDTO> saleClassList = portalCacheService.getSaleClassListBySysCode(sysCode);
        // 2024年10月29日10:12:23 , 谭湘江, 说明全国城市分组不具备数据隔离属性
        /*if (Objects.nonNull(groupId)) {
            resList = resList.stream().filter(item ->
                    Objects.isNull(item.getGroupId())
                            || item.getGroupId() < NumberPool.INT_ZERO
                            || item.getGroupId().equals(groupId)
            ).collect(Collectors.toList());
        } else {
            resList = resList.stream().filter(item -> Objects.isNull(item.getGroupId()) || item.getGroupId() < NumberPool.INT_ZERO).collect(Collectors.toList());
        }*/
        Set<Long> validThreeSaleClassIdList = redisService.getCacheSetMembers(RedisConstants.getGlobalReleaseClassSet(sysCode));
        // 组合商品的可见类目是实时计算出来的
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        if (Objects.nonNull(branchDTO)) {
            // 获取当前平台商发布过组合促销的入驻商
            List<CbRuleDTO> branchCbRules = this.getBranchCbRules(branchDTO, ProductType.GLOBAL);
            validThreeSaleClassIdList.addAll(branchCbRules.stream().map(CbRuleDTO::getShelfClassId).collect(Collectors.toSet()));
        }
        List<SaleClassDTO> result = new ArrayList<>();
        Map<Long, List<SaleClassDTO>> pidMap = saleClassList.stream().filter(item -> Objects.nonNull(item.getPid())).collect(Collectors.groupingBy(SaleClassDTO::getPid));
        List<SaleClassDTO> firstList = saleClassList.stream().filter(item -> Objects.isNull(item.getPid()) || item.getPid() == 0).collect(Collectors.toList());
        for (SaleClassDTO first : firstList) {
            List<SaleClassDTO> secondList = pidMap.get(first.getSaleClassId());
            if (Objects.nonNull(secondList)) {
                List<SaleClassDTO> validSecondList = new ArrayList<>();
                for (SaleClassDTO second : secondList) {
                    List<SaleClassDTO> threeList = pidMap.get(second.getSaleClassId());
                    if (ObjectUtil.isEmpty(threeList)) {
                        continue;
                    }
                    boolean hashValidThree = false;
                    for (SaleClassDTO three : threeList) {
                        if (validThreeSaleClassIdList.contains(three.getSaleClassId())) {
                            result.add(three);
                            hashValidThree = true;
                        }
                    }
                    if (hashValidThree) {
                        // 添加有效的三级
                        validSecondList.add(second);
                    }
                }
                if (!validSecondList.isEmpty()) {
                    // 添加有效的二级
                    result.addAll(validSecondList);
                    result.add(first);
                }
            }
        }
        return result;
    }

    @Override
    public List<CategoryRespVO> getSupplierInfoCategoryList(Long supplierId) {
        Long branchId = MallSecurityUtils.getBranchId();
        BranchDTO branchDTO = portalCacheService.getBranchDto(branchId);
        SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(supplierId);
        List<CatgoryDTO> catgoryList = portalCacheService.getCatgoryBySupplierId(supplierId);
        // 获取有效的三级管理分类ID
        HashSet<Long> validThreeSaleClassIdList = new HashSet<>();
        Set<Long> globalCategoryList = redisService.getCacheSetMembers(RedisConstants.getGlobalReleaseCategorySet(supplierDTO.getSysCode()));
        // 如果没有门店就使用默认城市
        Set<Long> localCategoryList = redisService.getCacheSetMembers(RedisConstants.getReleaseCityCategorySet(
                Objects.nonNull(branchDTO) ? branchDTO.getAreaId() : MallSecurityUtils.getLoginMember().getAreaId())
        );
        // 合并全国, 本地商品有效管理分类数据
        // 后续可以实现仅看全国和仅看本地
        if (ObjectUtil.isNotEmpty(globalCategoryList)) {
            validThreeSaleClassIdList.addAll(globalCategoryList);
        }
        if (ObjectUtil.isNotEmpty(localCategoryList)) {
            validThreeSaleClassIdList.addAll(localCategoryList);
        }
        // 获取当前门店可见组合促销商品集合
        if (Objects.nonNull(branchDTO)) {
            List<CbRuleDTO> ruleDTOS = getBranchCbRules(branchDTO, ProductType.GLOBAL, ProductType.LOCAL);
            Set<Long> spuCombineCategoryId = ruleDTOS.stream()
                    .map(CbRuleDTO::getSpuCombineId)
                    .distinct()
                    .map(portalCacheService::getSpuCombineDTO)
                    .filter(Objects::nonNull)
                    .map(SpuCombineDTO::getCategoryId)
                    .filter(ObjectUtil::isNotEmpty)
                    .collect(Collectors.toSet());
            validThreeSaleClassIdList.addAll(spuCombineCategoryId);
        }
        List<CatgoryDTO> result = validateCatgoryDTOS(catgoryList, validThreeSaleClassIdList);
        return CategoryConvert.INSTANCE.converRespVOList(result);
    }


    /**
     * 获取门店可见的组合促销规则
     * @param branchDTO         门店
     * @param productTypes      上架类型
     * @return
     */
    @Override
    public List<CbRuleDTO> getBranchCbRules(BranchDTO branchDTO, ProductType... productTypes) {
        Set<CbRuleDTO> cbRuleDTOS = ConcurrentHashMap.newKeySet();
        for (ProductType productType : productTypes) {
            Set<Long> supplierList;
            if (productType == ProductType.GLOBAL) {
                supplierList = redisService.getCacheSet(RedisActivityConstants.ACTIVITY_CB_GLOBAL_SUPPLIER_SET + branchDTO.getSysCode());
            } else {
                supplierList = redisService.getCacheSet(RedisActivityConstants.ACTIVITY_CB_LOCAL_SUPPLIER_SET + branchDTO.getAreaId());
            }
            if (ObjectUtil.isNotEmpty(supplierList)) {
                supplierList.parallelStream().forEach(supplierId -> {
                    List<SupplierActivityDTO> activityNotExtraRule = activityService.getSupplierActivityNotExtraRule(supplierId);
                    List<CbRuleDTO> cbAcitivtyList = redisActivityService.calculateBranchCbAcitivtyList(branchDTO, productType, activityNotExtraRule);
                    // 加入有效三级分类
                    cbRuleDTOS.addAll(cbAcitivtyList);
                });
            }
        }
        return ListUtil.toList(cbRuleDTOS);
    }

    /**
     *
     * @param catgoryList                   当前门店入驻商的管理分类列表
     * @param validThreeSaleClassIdList     有商品的管理分类两列表
     * @return  有效匹配数据结果
     */
    private static List<CatgoryDTO> validateCatgoryDTOS(List<CatgoryDTO> catgoryList, HashSet<Long> validThreeSaleClassIdList) {
        List<CatgoryDTO> result = new ArrayList<>();
        Map<Long, List<CatgoryDTO>> pidMap = catgoryList.stream().filter(item -> Objects.nonNull(item.getPid())).collect(Collectors.groupingBy(CatgoryDTO::getPid));
        List<CatgoryDTO> firstList = catgoryList.stream().filter(item -> Objects.isNull(item.getPid()) || item.getPid() == 0).collect(Collectors.toList());
        for (CatgoryDTO first : firstList) {
            List<CatgoryDTO> secondList = pidMap.get(first.getCatgoryId());
            if (Objects.nonNull(secondList)) {
                List<CatgoryDTO> validSecondList = new ArrayList<>();
                for (CatgoryDTO second : secondList) {
                    List<CatgoryDTO> threeList = pidMap.get(second.getCatgoryId());
                    if (ObjectUtil.isEmpty(threeList)) {
                        continue;
                    }
                    boolean hashValidThree = false;
                    for (CatgoryDTO three : threeList) {
                        if (validThreeSaleClassIdList.contains(three.getCatgoryId())) {
                            result.add(three);
                            hashValidThree = true;
                        }
                    }
                    if (hashValidThree) {
                        // 添加有效的三级
                        validSecondList.add(second);
                    }
                }
                if (!validSecondList.isEmpty()) {
                    // 添加有效的二级
                    result.addAll(validSecondList);
                    result.add(first);
                }
            }
        }
        return result;
    }


}
