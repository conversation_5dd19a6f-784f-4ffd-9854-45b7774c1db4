package com.zksr.portal.service.mall;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.OrderPayWayRespDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.order.OrderVO;
import com.zksr.portal.controller.mall.vo.pay.PayPreSuccessReqVO;
import com.zksr.portal.controller.mall.vo.yh.YhCreateOrderRequest;
import com.zksr.portal.controller.mall.vo.yh.YhSettleOrderRequest;
import com.zksr.trade.api.order.vo.StoreProductRespVO;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.order.vo.OrderAmountStatisticsVO;
import com.zksr.trade.api.order.vo.OrderStatusVO;
import com.zksr.trade.api.order.vo.StoreProductRequest;

public interface OrderService {
    /**
     * 获取订单结算信息
     * @param memberId
     * @param branchId
     * @param request
     * @return
     */
    public SettleOrderResp settleOrder(Long memberId, Long branchId, SettleOrderRequest request);

    /**
     * 创建订单
     * @param memberId
     * @param branchId
     * @param request
     * @return
     */
    public CommonResult<CreateOrderRespVO> saveOrder(Long memberId, Long branchId, CreateOrderRequest request);

    /**
     * 订单再次购买
     * @param orderPageReqDto
     */
    public SettleOrderRequest orderMorePurchase(TrdOrderPageReqDTO orderPageReqDto);

    /**
     * 预下单接口-补货单跳结算页
     * @param request
     * @return
     */
    public SettleOrderResp yhSettleOrder(YhSettleOrderRequest request);

    /**
     * 创建接口-补货单生成订单
     * @param request
     * @return
     */
    public CreateOrderRespVO yhCreateOrder(YhCreateOrderRequest request);

    /**
     * @Description: 分页获取订单数据
     * @Author: liuxingyu
     * @Date: 2024/3/30 10:13
     */
    PageResult<OrderVO> pageOrderList(TrdOrderPageReqDTO orderPageReqDto);

    /**
     * 获取我的订单的但前状态数量
     * @return
     */
    OrderStatusVO getOrderStatus();

    /**
     * 统计单月的订单金额
     * @return
     */
    OrderAmountStatisticsVO getMonthOrderAmountStatistics();

    /**
    * @Description: 获取常购商品
    * @Author: liuxingyu
    * @Date: 2024/5/7 11:29
    */
    PageResult<StoreProductRespVO> frequentSpuList(StoreProductRequest storeProductRequest);

    /**
     * 订单预支付成功
     * @param reqVO
     */
    void payPreSuccess(PayPreSuccessReqVO reqVO);

    /**
     * 验证是否已经预支付成功
     * @param branchId  门店ID
     * @param orderNo   订单号
     * @return  是否已经提前验证支付成功
     */
    boolean validatePreSuccess(Long branchId, String orderNo);

    /**
     * 经营屏蔽转换
     * @param pageResult
     * @param branchDto
     * @return
     */
    PageResult<StoreProductRespVO> splitSku(PageResult<StoreProductRespVO> pageResult, BranchDTO branchDto);
    /**
     * 获取订单支付方式
     * @param orderNo
     * @return
     */
    public OrderPayWayRespDTO getOrderPayWay(String orderNo);

    /**
     * 货到付款订单默认支付成功接口
     * @param orderNo
     */
    public void hdfkPaySuccess(String orderNo, String payWay);

}
