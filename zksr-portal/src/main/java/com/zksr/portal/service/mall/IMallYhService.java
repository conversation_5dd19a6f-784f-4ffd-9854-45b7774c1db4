package com.zksr.portal.service.mall;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.portal.controller.mall.vo.yh.*;
import com.zksr.product.api.yhdata.vo.YhBatchListReqVO;
import com.zksr.product.api.yhdata.vo.YhBatchSaveReqVO;
import com.zksr.product.api.yhdata.vo.YhBatchSupplierSaleClassReqVO;

/**
 * <AUTHOR>
 * @time 2024/12/11
 * @desc
 */
public interface IMallYhService {

    /**
     * 获取要货批次入驻商和分类集合
     */
    YhBatchSupplierSaleClassRespVO yhSupplierSaleClass(YhBatchSupplierSaleClassReqVO reqVO);

    /**
     * 获取批次补货数据列表
     */
    YhBatchListRespVO getBatchList(YhBatchListReqVO reqVO);

    /**
     * 统计要货单结算信息
     */
    YhBatchCountRespVO batchCount(YhBatchCountReqVO countReqVO);

    /**
     * 批次库存状态验证
     */
    YhBatchStockCheckRespVO batchStockCheck(YhBatchCountReqVO countReqVO);

    /**
     * 要货数据操作
     */
    CommonResult<Boolean> batchYhSave(YhBatchSaveReqVO reqVO);
}
