package com.zksr.portal.service.mall;


import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.complain.MemComplainVO;
import com.zksr.member.api.complain.dto.MemComplainDTO;

/**
 * <p>
 * 投诉信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
public interface ComplainService  {
    /**
     * 查询用户的投诉列表
     * @param memComplainVO
     * @return
     */
    PageResult<MemComplainVO> getComplainList(MemComplainVO memComplainVO);

    /**
     * 新增投诉信息
     * @param
     * @return
     */
    CommonResult<String> insertComplain(MemComplainDTO memComplainDTO);

    /**
     * 查询投诉详情
     * @param complainId
     * @return
     */
    MemComplainVO getComplainBycomplainId(String complainId);
}
