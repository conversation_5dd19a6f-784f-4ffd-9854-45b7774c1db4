package com.zksr.portal.service.impl.price;

import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.dto.car.AppCarStorageDTO;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;

/**
 * <AUTHOR>
 * @date 2024年03月26日 09:32
 * @description: TradePriceCalculatorHelper
 */
public class TradePriceCalculatorHelper {

    public static TradePriceCalculateResp buildCalculateResp(TradePriceCalculateRequest param,
                                                             List<SpuDTO> spuList, List<SkuDTO> skuList, List<SpuCombineDTO> spuCobineList,
                                                             Map<String, BigDecimal> priceMap,  List<AreaItemDTO> areaItemList, List<SupplierItemDTO> supplierItemList) {
        // 创建 PriceCalculateRespDTO 对象
        TradePriceCalculateResp result = new TradePriceCalculateResp();


        Map<Long, List<TradePriceCalculateRequest.Item>> supplierCarMap =  param.getItems().stream().collect(Collectors.groupingBy(TradePriceCalculateRequest.Item::getSupplierId));

        // 创建它的 OrderItem 属性
        result.setItems(new ArrayList<>(supplierCarMap.size()));
        Map<Long, SpuDTO> spuMap = convertMap(spuList, SpuDTO::getSpuId);
        Map<Long, SkuDTO> skuMap = convertMap(skuList, SkuDTO::getSkuId);
        Map<Long, SpuCombineDTO> spuCombineMap = convertMap(spuCobineList, SpuCombineDTO::getSpuCombineId);
        Map<Long, AreaItemDTO> areaItemMap = convertMap(areaItemList, AreaItemDTO::getAreaItemId);
        Map<Long, SupplierItemDTO> supplierItemMap = convertMap(supplierItemList, SupplierItemDTO::getSupplierItemId);
        supplierCarMap.forEach((k, v) -> {
            TradePriceCalculateResp.OrderItem orderItem = new TradePriceCalculateResp.OrderItem();
            orderItem.setSupplierId(k);
            orderItem.setSupplierName(v.get(0).getSupplierName());
            orderItem.setSupplierItems(new ArrayList<>(v.size()));
            orderItem.setTotalNum(0);


            v.forEach(item->{
                // 当前商品为普通商品且未查询到SKU信息时 直接返回
                if (Objects.equals(item.getGoodsType(), NumberPool.INT_ZERO) && !skuMap.containsKey(item.getSkuId())) {
                    return;
                }
                SkuDTO sku = skuMap.containsKey(item.getSkuId()) ? skuMap.get(item.getSkuId()) : new SkuDTO();

                // 当前商品为普通商品且未查询到SPU信息时 直接返回
                if (Objects.equals(item.getGoodsType(), NumberPool.INT_ZERO) && !spuMap.containsKey(sku.getSpuId())) {
                    return;
                }
                SpuDTO spu = spuMap.containsKey(sku.getSpuId()) ? spuMap.get(sku.getSpuId()) : new SpuDTO();

                // 当前商品为组合商品且未查询到 SPU组合商品信息时 直接返回
                if (Objects.equals(item.getGoodsType(), NumberPool.INT_ONE) && !spuCombineMap.containsKey(item.getSpuCombineId())) {
                    return;
                }
                SpuCombineDTO spuCombine = spuCombineMap.containsKey(item.getSpuCombineId()) ? spuCombineMap.get(item.getSpuCombineId()) : new SpuCombineDTO();


                TradePriceCalculateResp.OrderItem.SupplierItem supplierItem = new TradePriceCalculateResp.OrderItem.SupplierItem();

                orderItem.getSupplierItems().add(supplierItem);

                supplierItem.setSpuId(sku.getSpuId())
                        .setSkuId(sku.getSkuId())
                        .setSpuCombineId(spuCombine.getSpuCombineId())
                        .setGoodsType(item.getGoodsType())
                        .setUuIdNo(UUID.randomUUID().toString()) // uuID 唯一值
                        .setCount(item.getCount().intValue())
                        .setIsActivitySpSk(item.getIsActivitySpSk())
                        .setIsNegativeStock(item.getIsNegativeStock())
                        .setCarId(item.getCartId()).setSelected(item.getSelected());

                // 价格信息
                BigDecimal price = BigDecimal.ZERO;
                Integer itemType = 0;
                if (StringUtils.isNotNull(item.getSupplierItemId()) && -1 != item.getSupplierItemId() && StringUtils.isNotNull(priceMap)) {
                    price = priceMap.get(item.getSupplierItemId() + "-" + item.getUnitSize());
                    itemType = 0; // 全国商品
                    SupplierItemDTO supplierItemDTO = supplierItemMap.get(item.getSupplierItemId());
                    supplierItem.setSupplierItemId(item.getSupplierItemId())
                            .setClassId(supplierItemDTO.getSaleClassId())
                            .setProductType(ProductType.GLOBAL.getType())
                    ;

                }

                if (StringUtils.isNotNull(item.getAreaItemId()) && -1 != item.getAreaItemId() && StringUtils.isNotNull(priceMap)) {
                    price = priceMap.get(item.getAreaItemId() + "-" + item.getUnitSize());
                    itemType = 1; // 本地商品
                    AreaItemDTO areaItemDTO = areaItemMap.get(item.getAreaItemId());
                    supplierItem.setAreaItemId(item.getAreaItemId())
                            .setClassId(areaItemDTO.getAreaClassId())
                            .setProductType(ProductType.LOCAL.getType())
                    ;
                }

                supplierItem.setPrice(StringUtils.isNotNull(price) ? price : BigDecimal.ZERO)
                        .setSalePrice(supplierItem.getPrice())
                        .setUnit(item.getUnit()).setUnitSize(item.getUnitSize()).setOrderUnitSize(spu.getUnitSizeQty(item.getUnitSize()))
                        .setSubOrderAmt(supplierItem.getSalePrice().multiply(new BigDecimal(supplierItem.getCount()+""))) // 商品原总金额：原价格*商品数量
                        .setMinSalePrice(supplierItem.getSalePrice().divide(new BigDecimal(supplierItem.getOrderUnitSize()+""), StatusConstants.PRICE_RESERVE_6, BigDecimal.ROUND_HALF_UP))
                        .setPayPrice(supplierItem.getPrice().multiply(new BigDecimal(supplierItem.getCount()+"")))// 应付金额：价格*商品数量

                ;

                // sku 信息  兼容组合商品
                supplierItem.setPicUrl(ToolUtil.isEmptyReturn(spu.getThumb(), spuCombine.getThumb()))
                        .setProperties(PropertyAndValDTO.getProperties(sku.getProperties()));

                // spu 信息 兼容组合商品
                supplierItem.setSpuName(ToolUtil.isEmptyReturn(spu.getSpuName(), spuCombine.getSpuCombineName()))
                        .setVideoUrl(ToolUtil.isEmptyReturn(spu.getThumbVideo(), spuCombine.getThumbVideo()))
                        .setSpecName(ToolUtil.isEmptyReturn(spu.getSpecName(), spuCombine.getSpecName()))
                        .setBrandId(spu.getBrandId())
                        .setCategoryId(ToolUtil.isEmptyReturn(spu.getCatgoryId(), spuCombine.getCategoryId()))
                        .setSpuNo(ToolUtil.isEmptyReturn(spu.getSpuNo(), spuCombine.getSpuCombineNo()))
                        .setBarcode(sku.getBarcode())
                        .setOldestDate(spu.getOldestDate())
                        .setLatestDate(spu.getLatestDate())
                ;

                // 商品上架信息
                supplierItem.setItemType(itemType)
                        .setGiftFlag(StatusConstants.FLAG_FALSE); // 是否是赠品  默认否

                // 业务员加单指令Id
                supplierItem.setCommandId(
                        Optional.ofNullable(item.getCarStorageDTO())
                                .map(AppCarStorageDTO::getCommandId)
                                .orElse(NumberPool.LOWER_GROUND_LONG)
                );

                // 优惠金额字段
                supplierItem.setCouponDiscountAmt(BigDecimal.ZERO)
                        .setCouponDiscountAmt2(BigDecimal.ZERO)
                        .setActivityDiscountAmt(BigDecimal.ZERO);

                //入驻商数量总信息
                orderItem.setTotalNum((int) (orderItem.getTotalNum() + (item.getCount() * spu.getUnitSizeQty(item.getUnitSize()).longValue())));

            });
            orderItem.setTotalAmt(BigDecimal.ZERO);
            orderItem.setPayAmt(BigDecimal.ZERO);
            orderItem.setDiscountAmt(BigDecimal.ZERO);
            result.getItems().add(orderItem);

        });

        // 创建它的 Price 属性
        result.setPrice(new TradePriceCalculateResp.Price());
        // 创建它的 promotions 属性
        result.setPromotions(new ArrayList<>());
        result.setActivityTipsInfo("");
//        recountAllPrice(result);
        return result;
    }

    /**
     * 基于订单项，重新计算 price 总价
     *
     * @param result 计算结果
     */
    public static void recountAllPrice(TradePriceCalculateResp result) {
        // 先重置
        TradePriceCalculateResp.Price price = result.getPrice();
        price.setTotalPrice(BigDecimal.ZERO);  //原价（总）
        price.setPayPrice(BigDecimal.ZERO);   // 支付金额（总）
        price.setTotalCouponDiscountPrice(BigDecimal.ZERO); // 优惠劵优惠总金额
        price.setTotalActivityDiscountPrice(BigDecimal.ZERO); // 活动优惠总金额
        // 再合计 item
        result.getItems().forEach(item -> {
            item.getSupplierItems().forEach(supplierItem -> {
                if (!supplierItem.getSelected()) {
                    return;
                }
                // 总订单金额数据
                price.setTotalPrice(price.getTotalPrice().add(supplierItem.getSubOrderAmt()));
                price.setPayPrice(price.getPayPrice().add(supplierItem.getSubOrderAmt())
                        .subtract(supplierItem.getCouponDiscountAmt())
                        .subtract(supplierItem.getCouponDiscountAmt2())
                        .subtract(supplierItem.getActivityDiscountAmt()));
                price.setTotalCouponDiscountPrice(price.getTotalCouponDiscountPrice().add(supplierItem.getCouponDiscountAmt()).add(supplierItem.getCouponDiscountAmt2()));
                price.setTotalActivityDiscountPrice(price.getTotalActivityDiscountPrice().add(supplierItem.getActivityDiscountAmt()));

                // 入驻商订单金额数据
                item.setDiscountAmt(item.getDiscountAmt().add(supplierItem.getCouponDiscountAmt()).add(supplierItem.getCouponDiscountAmt2()).add(supplierItem.getActivityDiscountAmt()));
                item.setTotalAmt(item.getTotalAmt().add(supplierItem.getSubOrderAmt()));
                item.setPayAmt(item.getPayAmt().add(supplierItem.getSubOrderAmt())
                        .subtract(supplierItem.getCouponDiscountAmt())
                        .subtract(supplierItem.getCouponDiscountAmt2())
                        .subtract(supplierItem.getActivityDiscountAmt()));

            });
            // 入驻商商品应付金额小于等于0，则设置为0
            BigDecimal payAmt = item.getPayAmt();
            if (Objects.nonNull(payAmt) && payAmt.compareTo(BigDecimal.ZERO) <= 0) {
                item.setPayAmt(BigDecimal.ZERO);
            }
        });
        price.setTotalDiscountPrice(price.getTotalCouponDiscountPrice().add(price.getTotalActivityDiscountPrice())); // 订单优惠总金额
    }
}
