package com.zksr.portal.service.impl.mall;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.erpUtils.SecretUtil;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.config.AnntoProxyConfig;
import com.zksr.common.security.service.MallTokenService;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.common.third.wx.dto.WxCode2SessionResponse;
import com.zksr.common.third.zfb.ZfbUtils;
import com.zksr.common.third.zfb.vo.AlipaySystemOauthVO;
import com.zksr.member.api.LoginMember;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.vo.MemberBranchReqVO;
import com.zksr.member.api.branchRegister.BranchRegisterApi;
import com.zksr.member.api.colonel.ColonelApi;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.member.api.member.vo.MemberUpdatePwdVO;
import com.zksr.member.api.memberRegister.MemberRegisterApi;
import com.zksr.member.api.memberRegister.dto.MemberRegisterDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.mq.mall.MallMqProducer;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IIndexService;
import com.zksr.portal.service.thirdparty.ThirdPartyLoginService;
import com.zksr.system.api.area.AreaApi;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dcArea.DcAreaApi;
import com.zksr.system.api.dcArea.dto.DcAreaDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.HeLiBaoPayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.PartnerPolicyApi;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.partnerPolicy.dto.BasicSettingPolicyDTO;
import com.zksr.system.api.sms.SmsApi;
import com.zksr.system.api.sms.dto.SmsCodeReqDTO;
import com.zksr.system.api.sms.dto.SmsCodeRespDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_MEMBER_PASSWORD_NOT_LENGTH;
import static com.zksr.member.enums.ErrorCodeConstants.MEM_MEMBER_USERNAME_CHECK_ERR;

@Service
@Slf4j
public class IndexServiceImpl implements IIndexService {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Resource
    private MemberApi remoteMemberApi;

    @Autowired
    private MallTokenService mallTokenService;

    @Autowired
    private AreaApi remoteAreaApi;

    @Autowired
    private DcAreaApi remoteDcAreaApi;

    @Autowired
    private BranchApi remoteBranchApi;

    @Autowired
    private MemberRegisterApi remoteMemberRegisterApi;

    @Autowired
    private ColonelApi colonelApi;
    @Autowired
    private RedisService redisService;

    @Autowired
    private SmsApi smsApi;
    @Autowired
    private MallMqProducer mallMqProducer;
    @Autowired
    private MemberApi memberApi;
    @Autowired
    private BranchRegisterApi branchRegisterApi;
    @Resource
    private AnntoProxyConfig anntoProxyConfig;

    @Resource
    private PartnerPolicyApi partnerPolicyApi;

    @Autowired
    private List<ThirdPartyLoginService> thirdPartyLoginServiceList;

    @Override
    public GetWxInfoResponse getWxInfo(GetWxInfoRequest request) throws Exception {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        Long sysCode = partnerDto.getSysCode();
        AppletBaseConfigDTO appletBaseConfigDTO = portalCacheService.getAppletBaseConfigDTO(sysCode);

        if (ToolUtil.isEmpty(appletBaseConfigDTO) || (ToolUtil.isNotEmpty(appletBaseConfigDTO)
                && (ToolUtil.isEmpty(appletBaseConfigDTO.getAppId()) || ToolUtil.isEmpty(appletBaseConfigDTO.getAppSecret())))) {
            throw new ServiceException("该平台商未配置小程序相关设置");
        }

        String userAppid = appletBaseConfigDTO.getAppId();
        String userAppsecret = appletBaseConfigDTO.getAppSecret();
        Proxy proxy = null;
        //是否使用代理
        if(anntoProxyConfig.isEnable() && org.apache.commons.lang3.StringUtils.isNotEmpty(anntoProxyConfig.getHost())) {
            log.warn(" 使用代理请求...");
            // 设置代理
            proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(anntoProxyConfig.getHost(), anntoProxyConfig.getPort()));

        }
        WxCode2SessionResponse code2SessionResponse = WxUtils.getOpenId(userAppid, userAppsecret, request.getCode(), proxy);

        if (ToolUtil.isEmpty(code2SessionResponse)) {
            log.error("调取微信获取openid失败");
            throw new ServiceException("调取微信获取openid失败");
        }

        if (ToolUtil.isNotEmpty(code2SessionResponse.getErrcode()) || ToolUtil.isNotEmpty(code2SessionResponse.getErrmsg())) {
            String msg = code2SessionResponse.getErrcode() + ":" + code2SessionResponse.getErrmsg();
            log.error("调取微信获取openid失败" + msg);
            throw new ServiceException("调取微信获取openid失败" + msg);
        }

        String openid = code2SessionResponse.getOpenid();
        String unionid = code2SessionResponse.getUnionid();
        String sessionKey = code2SessionResponse.getSession_key();
        //redis根据openid+code 存入sessionKey
        portalCacheService.setWxSessionKey(sysCode + ":" + openid + ":" + request.getCode(), sessionKey);

//        MemberDTO member = memberService.getMemberByWxInfo(partnerDto.getSysCode(), null, unionid);
        //是否新用户：1是0否
        String isNew = "";
        String mobile = "";
        String nickname = "";
        String avatar = "";
        isNew = "1";
        //需要根据unionid 和 openid 查询是否是新用户
        /*MemberDTO member = remoteMemberApi.getInfoByMobileAndOpenid(sysCode, null, openid, null).getCheckedData();
        if (ToolUtil.isEmpty(member)) {
            isNew = "1";
        }*/
        /*{
            //老用户传返回手机号
            mobile = member.getMemberPhone();
            nickname = member.getMemberName();
            avatar = member.getAvatar();
            isNew = "0";
        }*/
        GetWxInfoResponse response = new GetWxInfoResponse(openid, sessionKey, unionid, isNew, mobile, nickname, avatar);
        return response;
    }

    @Override
    public GetWxInfoResponse getZfbInfo(GetWxInfoRequest request) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        // 获取合利宝配置
        HeLiBaoPayConfigDTO heLiBaoConfig = portalCacheService.getHeLiBaoConfig(partnerDto.getSysCode());
        if (StringUtils.isEmpty(heLiBaoConfig.getAlipayXcxAppId())) {
            throw new ServiceException("合利宝支付宝中间小程序未配置");
        }
        AlipaySystemOauthVO authInfo = ZfbUtils.getXcxAuthInfo(
                request.getCode(),
                heLiBaoConfig.getAlipayXcxAppId(),
                heLiBaoConfig.getAlipayXcxPublicKey(),
                heLiBaoConfig.getAlipayXcxPrivateKey()
        );
        GetWxInfoResponse response = new GetWxInfoResponse();
        response.setOpenid(authInfo.getUserId());
        return response;
    }

    @Override
    public AuthLoginResponse authLogin(AuthLoginRequest request) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        Long sysCode = partnerDto.getSysCode();
        /** 1,验证用户是否授权 */
        //根据sys_code+手机号+openid 判断是否是新用户
        //需要先判断是否有sessionKey 如果没有sessionKey则不让登录
        String sessionKey = portalCacheService.getWxSessionKey(sysCode + ":" + request.getOpenid() + ":" + request.getCode());
        if (ToolUtil.isEmpty(sessionKey)) {
            log.error("未查询到sessionKey，不允许登录，{}", sessionKey);
            throw new ServiceException("未查询到sessionKey，不允许登录");
        }

        /** 根据手机号登录 */
        MemberDTO member = remoteMemberApi.getInfoByMobileAndOpenid(sysCode, request.getMobile(), null, null).getCheckedData();

        if (ToolUtil.isEmpty(member)) {
            throw new ServiceException("该手机号未注册");
        }

        //TODO 需要判断 member 的状态，如是否审核、是否启用等状态
        //如果状态为停用 则不允许登陆
        if (member.getStatus() == STATE_DISABLE) {
            throw new ServiceException("该用户已停用，请联系相关人员");
        }

        if (ToolUtil.isNotEmpty(member)) {
            // 更新用户登录信息
            member.setXcxOpenid(request.getOpenid());
            memberApi.updateMemberToken(member).checkError();
//            member.setAvatar(request.getAvatar());
        } else {
            /** 注册新用户 */
//            MemMemberSaveReqVO registerVo = new MemMemberSaveReqVO();
//            registerVo.setSysCode(sysCode);
//            registerVo.setMemberPhone(request.getMobile());
//            registerVo.setMemberName(request.getMemberName());
//            registerVo.setWxUnionid(request.getUnionid());
//            registerVo.setAvatar(request.getAvatar());
//            registerVo.setXcxOpenid(request.getOpenid());
//            member = remoteMemberApi.memberRegister(registerVo).getCheckedData();
        }

        //获取门店信息
        BranchDTO branchDTO = remoteBranchApi.getDefaultBranchByMemberId(member.getMemberId(), sysCode).getCheckedData();
        return getToken(member, sysCode, branchDTO);
    }

    @Override
    public VisitorLoginResponse visitorLogin(VisitorLoginRequest request) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        Long sysCode = partnerDto.getSysCode();

        LoginMember loginMember = new LoginMember();
        loginMember.setSysCode(sysCode);

        AppletAgreementPolicyDTO appletAgreementPolicy =partnerPolicyApi.getAppletAgreementPolicy(sysCode).getCheckedData();
        if(ToolUtil.isNotEmpty(appletAgreementPolicy)  && ToolUtil.isNotEmpty(appletAgreementPolicy.getCityId())){
            loginMember.setAreaId(Long.parseLong(appletAgreementPolicy.getCityId()));
        }else{
            AreaDTO areaDTO = remoteAreaApi.getDefaultBySyscode(sysCode).getCheckedData();
            if (ToolUtil.isNotEmpty(areaDTO)) {
                loginMember.setAreaId(areaDTO.getAreaId());
            }
        }


        Map<String, Object> tokenMap = mallTokenService.createToken(loginMember);
        String token = (String) tokenMap.get("access_token");
        Long expiresIn = (Long) tokenMap.get("expires_in");

        VisitorLoginResponse response = new VisitorLoginResponse();
        if (ToolUtil.isNotEmpty(loginMember.getAreaId())) {
            response.setAreaId(loginMember.getAreaId());
        }
        response.setToken(token);
        response.setExpiresIn(expiresIn);
        return response;
    }

    @Override
    public List<AreaRespVO> getcityList(Long sysCode) {
        List<AreaRespVO> areaRespVOS = HutoolBeanUtils.toBean(remoteAreaApi.getListBySysCode(sysCode).getData(), AreaRespVO.class);
        areaRespVOS = areaRespVOS.stream().filter(item -> Objects.nonNull(item.getStatus())).filter(item -> item.getStatus() == NumberPool.INT_ONE).collect(Collectors.toList());
        return areaRespVOS;
    }

    @Override
    public List<BranchReqVo> getBranchList(MemberBranchReqVO memberBranchReqVO) {
        List<BranchReqVo> branchList = new ArrayList<>();
        List<BranchDTO> checkedData = remoteBranchApi.getBranchListByMemberId(memberBranchReqVO).getCheckedData();
        if (ToolUtil.isNotEmpty(checkedData)) {
            branchList = HutoolBeanUtils.toBean(checkedData, BranchReqVo.class);
            // 返回门店列表添加城市名称
            branchList.forEach(branch -> {
                branch.setAreaName(portalCacheService.getAreaDto(branch.getAreaId()).getAreaName());
            });
        }
        return branchList;
    }

    @Override
    public List<BranchReqVo> getUnauditedBranchList(MemberBranchReqVO memberBranchReqVO) {
        List<BranchReqVo> branchList = new ArrayList<>();
        List<BranchDTO> checkedData = remoteBranchApi.getUnauditedBranchListByMemberId(memberBranchReqVO).getCheckedData();
        if (ToolUtil.isNotEmpty(checkedData)) {
            branchList = HutoolBeanUtils.toBean(checkedData, BranchReqVo.class);
        }
        return branchList;
    }

    @Override
    public AuthLoginResponse cutBranch(Long memberId, Long branchId) {
        Long sysCode = MallSecurityUtils.getLoginMember().getSysCode();
        //切换门店 会刷新token、重新设置用户的默认门店
        //校验是否是当前默认门店
        if (ToolUtil.isNotEmpty(MallSecurityUtils.getLoginMember().getBranchId()) && MallSecurityUtils.getLoginMember().getBranchId().equals(branchId)) {
            throw new ServiceException("当前门店已经是默认门店，无需切换");
        }
        //修改当前用户的默认门店
        remoteMemberApi.updateDefaultBranch(memberId, branchId, sysCode);
        BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
        return getToken(MallSecurityUtils.getLoginMember().getMember(), sysCode, branchDto);

    }

    @Override
    public AuthLoginResponse userLogin(UserLoginRequest request) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        String loginFrom = request.getLoginFrom();
        if (StringUtils.isNotBlank(loginFrom)) {
            return thirdPartyLogin(request);
        }
        //获取登陆信息
        Long sysCode = partnerDto.getSysCode();
        String username = request.getUsername();
        String password = request.getPassword();

        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
//        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
//                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
//            throw new ServiceException("用户密码不在指定范围");
//        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new ServiceException("用户名不在指定范围");
        }
        //获取用户信息
        CommonResult<MemberDTO> memberResult = remoteMemberApi.getInfoByMobileAndOpenid(sysCode, null, null, username);
        if (ToolUtil.isEmpty(memberResult) || ToolUtil.isEmpty(memberResult.getData())) {
            throw new ServiceException("用户不存在！");
        }

        if (R.FAIL == memberResult.getCode()) {
            throw new ServiceException(memberResult.getMsg());
        }
        MemberDTO member = memberResult.getData();
        //校验密码是否正确
        if (!SecurityUtils.matchesPassword(password, member.getPassword())) {
            throw new ServiceException("密码错误！");
        }
        if (member.getStatus() == STATE_DISABLE) {
//           throw new ServiceException("对不起，您的账号：" + username + " 已停用，请联系相关人员");
            CommonResult<ColonelDTO> colonel = colonelApi.getByColonelId(member.getRegisterColonelId());
            if (colonel.isSuccess()) {
                String colonelPhone = colonel.getData().getColonelPhone();
                throw new ServiceException("对不起，您的账号：" + username + " 已停用，请联系业务员，联系方式：" + colonelPhone);
            }
            throw new ServiceException("对不起，您的账号：" + username + " 已停用，请联系相关业务员");
        }

        //校验是否开启了设备校验  默认不开启
        AppletAgreementPolicyDTO appletAgreementPolicyResult = partnerPolicyApi.getAppletAgreementPolicy(sysCode).getCheckedData();
        String checkDevice = null;
        if(ToolUtil.isNotEmpty(appletAgreementPolicyResult)){
            checkDevice = appletAgreementPolicyResult.getCheckDevice();
        }
        //校验设备ID是否一致
        if(ToolUtil.isNotEmpty(checkDevice) && checkDevice.equals("1")) {
            if (ToolUtil.isEmpty(member.getDeviceId())) {
                member.setDeviceId(request.getDeviceId());
                remoteMemberApi.updateMemberDeviceId(member);
            } else if (ToolUtil.isNotEmpty(request.getDeviceId()) && !member.getDeviceId().equals(request.getDeviceId())) {
                throw new ServiceException("设备ID不一致，请使用【短信验证码登录】");
            }
        }

        //校验通过  创建token
        //获取门店信息
        BranchDTO branchDTO = remoteBranchApi.getDefaultBranchByMemberId(member.getMemberId(), sysCode).getCheckedData();
        return getToken(member, sysCode, branchDTO);
    }

    @Override
    public void insertChildUser(ChildUserReqVO reqVO) {
        Long sysCode = MallSecurityUtils.getLoginMember().getSysCode();
        //校验当前手机号 是否已注册用户
        MemberDTO checkMember = remoteMemberApi.getInfoByMobileAndOpenid(sysCode, null, null, reqVO.getUserName()).getCheckedData();
        if (ToolUtil.isNotEmpty(checkMember)) {
            throw new ServiceException("当前手机号已注册用户");
        }
        //校验当前手机号 是否在用户注册信息中
        MemberRegisterDTO checkRegister = remoteMemberRegisterApi.getMemberRegisterByUserName(sysCode, reqVO.getUserName()).getCheckedData();
        if (ToolUtil.isNotEmpty(checkRegister)) {
            throw new ServiceException("当前手机号已申请用户注册，请联系相关人员处理");
        }
        // 校验密码
        String password = reqVO.getPassword();
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new ServiceException("密码长度必须在8到16个字符之间");
        }
        if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, password)) {
            throw new ServiceException("必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
        }

        //组装用户信息
        MemberDTO memberDTO = new MemberDTO();
        memberDTO.setSysCode(sysCode);
        memberDTO.setMemberPhone(reqVO.getUserName());
        memberDTO.setMemberName(reqVO.getMemberName());
        memberDTO.setUserName(reqVO.getUserName());
        memberDTO.setPassword(SecurityUtils.encryptPassword(reqVO.getPassword()));
        memberDTO.setIsShopManager(FLAG_FALSE);
        memberDTO.setPid(reqVO.getPid());
        memberDTO.setBranchId(reqVO.getBranchId());

        //新增子用户
        remoteMemberApi.insertChildUser(memberDTO);
    }

    @Override
    public List<ChildUserListRespVO> childUserList(Long memberId) {
        //校验是否是店长用户
        MemberDTO checkMember = remoteMemberApi.getMemBerByMemberId(memberId).getCheckedData();
        if (checkMember.getIsShopManager() != FLAG_TRUE) {
            throw new ServiceException("当前用户不是店长用户，无法查看子用户列表");
        }
        return HutoolBeanUtils.toBean(remoteMemberApi.childUserList(memberId).getCheckedData(), ChildUserListRespVO.class);
    }

    @Override
    public RegisterResp register(RegisterReq request) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        Long sysCode = partnerDto.getSysCode();

        //校验该手机号是否已注册
        /** 根据手机号登录 */
        MemberDTO member = remoteMemberApi.getInfoByMobileAndOpenid(sysCode, request.getUserName(), null, null).getCheckedData();
        if (ToolUtil.isNotEmpty(member)) {
            if (member.getStatus() == STATE_DISABLE) {
                throw new ServiceException("该手机号已注册，但已停用，请联系相关人员");
            }
            throw new ServiceException("该手机号已注册，可直接登陆");
        }

        //校验当前手机号 是否在用户注册信息中
        MemberRegisterDTO checkRegister = remoteMemberRegisterApi.getMemberRegisterByUserName(sysCode, request.getUserName()).getCheckedData();
        if (ToolUtil.isNotEmpty(checkRegister)) {
            throw new ServiceException("当前手机号已申请用户注册，请联系相关人员处理");
        }

        RegisterResp resp = new RegisterResp();

        //1.先根据所选城市 查询是否开启自动审核
        Long areaId = request.getAreaId();
        if (ToolUtil.isEmpty(areaId)) {
            throw new ServiceException("未选择城市");
        }

        //根据城市ID获取对应的运营商基础配置
        CommonResult<DcAreaDTO> dcAreaResult = remoteDcAreaApi.getDcAreaByDcIdOrAreaId(null, areaId, sysCode);
        if (ToolUtil.isEmpty(dcAreaResult.getData())) {
            throw new ServiceException("该城市未分配运营商");
        }
        ;
        DcAreaDTO dcAreaDTO = dcAreaResult.getData();
        //获取运营商配置
        BasicSettingPolicyDTO config = portalCacheService.getBasicSettingPolicyDTO(dcAreaDTO.getDcId());

        //组装用户注册数据
        MemberRegisterDTO registerDTO = new MemberRegisterDTO();
        registerDTO.setMemberName(request.getMemberName());
        registerDTO.setBranchNo(request.getBranchNo());
        registerDTO.setBranchName(request.getBranchName());
        registerDTO.setAreaId(request.getAreaId());
        registerDTO.setBranchAddr(request.getBranchAddr());
        registerDTO.setLongitude(request.getLongitude());
        registerDTO.setLatitude(request.getLatitude());
        registerDTO.setColonelId(request.getColonelId());
        registerDTO.setChannelId(request.getChannelId());
        registerDTO.setProvinceName(request.getProvinceName());
        registerDTO.setCityName(request.getCityName());
        registerDTO.setDistrictName(request.getDistrictName());
        registerDTO.setSalePriceCode(request.getSalePriceCode());

        //账号、密码
        // 密码如果不在指定范围内 错误
        if (request.getPassword().length() < UserConstants.PASSWORD_MIN_LENGTH
                || request.getPassword().length() > UserConstants.PASSWORD_MAX_LENGTH
                || !Pattern.matches(UserConstants.PASSWORD_PATTERN, request.getPassword())
        ) {
            throw exception(MEM_MEMBER_PASSWORD_NOT_LENGTH);
        }
        if (!request.getUserName().matches("\\d+") || request.getUserName().length() != 11) {
            throw exception(MEM_MEMBER_USERNAME_CHECK_ERR);
        }
        registerDTO.setUserName(request.getUserName());
        registerDTO.setPassword(SecurityUtils.encryptPassword(request.getPassword()));
        registerDTO.setBranchImages(request.getBranchImages());
        registerDTO.setMemo(request.getMemo());
        registerDTO.setSysCode(sysCode);
        registerDTO.setThreeAreaCityId(request.getThreeAreaCityId());

        //设置默认值
        registerDTO.setStatus(STATE_ENABLE);
        registerDTO.setApproveFlag(AUDIT_STATE_0);
        registerDTO.setMemberApproveFlag(REGISTER_APPROVE_FLAG_0);
        registerDTO.setBranchApproveFlag(REGISTER_APPROVE_FLAG_0);

        resp.setApproveFlag(AUDIT_STATE_0);


        //校验运营商配置
        if (ToolUtil.isNotEmpty(config)) {
            if (Integer.valueOf(config.getUserAutoCheck()).equals(STATE_ENABLE)) {
                //2.如果是开启自动审核，则 mem_member 和 mem_branch 是启用状态 分别设置一个过期时间
                registerDTO.setMemberApproveFlag(REGISTER_APPROVE_FLAG_1);
                if (ToolUtil.isNotEmpty(config.getUserDay())
                        && Integer.valueOf(config.getUserDay()).compareTo(NumberPool.INT_ZERO) > 0) {
                    registerDTO.setMemberExpirationDate(DateUtils.getDateAdd(Integer.parseInt(config.getUserDay())));
                }

                //设置出参的配置状态
                resp.setApproveFlag(AUDIT_STATE_1);
            }
            if (Integer.valueOf(config.getShopAutoCheck()).equals(STATE_ENABLE)) {
                registerDTO.setBranchApproveFlag(REGISTER_APPROVE_FLAG_1);
                if (ToolUtil.isNotEmpty(config.getShopDay())
                        && Integer.valueOf(config.getShopDay()).compareTo(NumberPool.INT_ZERO) > 0) {
                    registerDTO.setBranchExpirationDate(DateUtils.getDateAdd(Integer.parseInt(config.getShopDay())));
                }
            }
        }
        //3.如果是关闭自动审核，则 mem_member 和 mem_branch 是停用状态，提示用户 “注册信息提交成功，待审核后方可使用， 审核通过之前只能通过游客模式进入小程序”， 小程序页面返回至登录页面
        //审核通过后，通过短信通知的方式，告知用户审核通过，可以登录，已经以游客身份登录过的，需要退出重新登录


        //调取memberapi 保存 mem_member 和 mem_branch
        CommonResult<Boolean> registerResult = remoteMemberRegisterApi.insertUserMemberRegister(registerDTO);
        Boolean result = registerResult.getCheckedData();
        if (!result) {
            throw new ServiceException("注册失败");
        }

        resp.setBranchName(request.getBranchName());
        resp.setMemberName(request.getMemberName());
        resp.setMobile(request.getUserName());

        return resp;
    }

    @Override
    public RegisterResp registerSimple(RegisterSimpleReq request) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        Long sysCode = partnerDto.getSysCode();
        if (!request.getUserName().matches("\\d+") || request.getUserName().length() != 11) {
            throw exception(MEM_MEMBER_USERNAME_CHECK_ERR);
        }
        //校验该手机号是否已注册
        /** 根据手机号登录 */
        MemberDTO member = remoteMemberApi.getInfoByMobileAndOpenid(sysCode, request.getUserName(), null, null).getCheckedData();
        if (ToolUtil.isNotEmpty(member)) {
            if (member.getStatus() == STATE_DISABLE) {
                throw new ServiceException("该手机号已注册，但已停用，请联系相关人员");
            }
            throw new ServiceException("该手机号已注册，可直接登陆");
        }

        //校验当前手机号 是否在用户注册信息中
        MemberRegisterDTO checkRegister = remoteMemberRegisterApi.getMemberRegisterByUserName(sysCode, request.getUserName()).getCheckedData();
        if (ToolUtil.isNotEmpty(checkRegister)) {
            throw new ServiceException("当前手机号已申请用户注册，请联系相关人员处理");
        }

        //账号、密码
        // 密码如果不在指定范围内 错误
        if (request.getPassword().length() < UserConstants.PASSWORD_MIN_LENGTH
                || request.getPassword().length() > UserConstants.PASSWORD_MAX_LENGTH
                || !Pattern.matches(UserConstants.PASSWORD_PATTERN, request.getPassword())
        ) {
            throw exception(MEM_MEMBER_PASSWORD_NOT_LENGTH);
        }

        RegisterResp resp = new RegisterResp();

        // 组装用户注册数据
        MemberRegisterDTO registerDTO = new MemberRegisterDTO();
        registerDTO.setMemberName(request.getMemberName());

        registerDTO.setUserName(request.getUserName());
        registerDTO.setPassword(SecurityUtils.encryptPassword(request.getPassword()));
        registerDTO.setMemo(request.getMemo());
        registerDTO.setSysCode(sysCode);

        //设置默认值  简易注册默认自动审核
        registerDTO.setStatus(STATE_ENABLE);
        registerDTO.setApproveFlag(AUDIT_STATE_1);
        registerDTO.setMemberApproveFlag(REGISTER_APPROVE_FLAG_1);
        registerDTO.setBranchApproveFlag(REGISTER_APPROVE_FLAG_0);

        //调取memberapi 保存 mem_member 和 mem_branch
        Boolean result = remoteMemberRegisterApi.insertUserMemberRegister(registerDTO).getCheckedData();
        if (!result) {
            throw new ServiceException("注册失败");
        }

        resp.setMemberName(request.getMemberName());
        resp.setMobile(request.getUserName());
        resp.setApproveFlag(AUDIT_STATE_1);
        return resp;
    }


    /**
     * @Description: 用户获取验证码
     * @Author: liuxingyu
     * @Date: 2024/5/10 15:44
     */
    @Override
    public String getSmsCode(LoginSmsReq loginSmsReq) {
        String ip = ServletUtils.getClientIP();
        String key = "login_sms_" + loginSmsReq.getPhone() + "_" + ip;
        //校验缓存信息,缓存时间一分钟
        Object smsCountObj = redisService.getCacheObject(key);
        if (ObjectUtil.isNotNull(smsCountObj)) {
            throw new ServiceException("请勿频繁获取验证码");
        }
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(loginSmsReq.getSysSource());
        if (Objects.isNull(partnerDto)) {
            throw new ServiceException("平台信息异常");
        }
        //获取Sms验证码
        SmsCodeReqDTO smsCodeReqDTO = new SmsCodeReqDTO(partnerDto.getSysCode(), loginSmsReq.getPhone(), ServletUtils.getClientIP());
        SmsCodeRespDTO checkedData = smsApi.sendSmsCode(smsCodeReqDTO).getCheckedData();
        if (Objects.isNull(checkedData) || org.apache.commons.lang3.StringUtils.isBlank(checkedData.getCode())) {
            throw new ServiceException("获取验证码失败");
        }
        redisService.setCacheObject(key, checkedData.getCode(), 60L, TimeUnit.SECONDS);
        return "获取验证码成功";
    }

    /**
     * @Description: 用户获取验证码
     * @Author: liuxingyu
     * @Date: 2024/5/10 15:46
     */
    @Override
    public AuthLoginResponse loginSms(LoginSmsReq loginSmsReq) {
        if (org.apache.commons.lang3.StringUtils.isBlank(loginSmsReq.getCode())) {
            throw new ServiceException("验证码异常");
        }
        //通过source拿到平台的sysCode
        PartnerDto partnerDto = portalCacheService.getPartnerDto(loginSmsReq.getSysSource());
        String ip = ServletUtils.getClientIP();
        String key = "login_sms_" + loginSmsReq.getPhone() + "_" + ip;
        //校验验证码是否正确
        Object smsCountObj = redisService.getCacheObject(key);
        if (ObjectUtil.isNull(smsCountObj)) {
            throw new ServiceException("验证码过期,请重新获取");
        }
        if (ObjectUtil.notEqual(loginSmsReq.getCode(), smsCountObj.toString())) {
            throw new ServiceException("验证码错误");
        }
        //校验手机号是否已经注册
        MemberDTO member = remoteMemberApi.getInfoByMobileAndOpenid(partnerDto.getSysCode(), loginSmsReq.getPhone(), null, null).getCheckedData();
        if (ToolUtil.isEmpty(member)) {
            throw new ServiceException("该手机号未注册");
        }
        //如果状态为停用 则不允许登陆
        if (member.getStatus() == STATE_DISABLE) {
            throw new ServiceException("该用户已停用，请联系相关人员");
        }
        //校验member是否有设备ID 如果为空则直接更新设备id  如果不为空则判断设备是否一致  如果不一致 则更新设备ID
        if (ToolUtil.isEmpty(member.getDeviceId()) || !member.getDeviceId().equals(loginSmsReq.getDeviceId())) {
            member.setDeviceId(loginSmsReq.getDeviceId());
            remoteMemberApi.updateMemberDeviceId(member);
        }

        //获取门店信息
        BranchDTO branchDTO = remoteBranchApi.getDefaultBranchByMemberId(member.getMemberId(), partnerDto.getSysCode()).getCheckedData();
        //删除缓存
        redisService.deleteObject(key);
        return getToken(member, partnerDto.getSysCode(), branchDTO);
    }

    @Override
    public Boolean bindPublishCode(GetWxInfoRequest request) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        Long sysCode = partnerDto.getSysCode();
        AppletBaseConfigDTO appletBaseConfigDTO = portalCacheService.getAppletBaseConfigDTO(sysCode);
        if (ToolUtil.isEmpty(appletBaseConfigDTO) || (ToolUtil.isNotEmpty(appletBaseConfigDTO)
                && (ToolUtil.isEmpty(appletBaseConfigDTO.getAppId()) || ToolUtil.isEmpty(appletBaseConfigDTO.getAppSecret())))) {
            throw new ServiceException("该平台商未配置小程序相关设置");
        }
        String userAppid = appletBaseConfigDTO.getPublishAppId();
        String userAppsecret = appletBaseConfigDTO.getPublishSecret();

        try {
            WxCode2SessionResponse code2SessionResponse = WxUtils.getPublishOpenId(userAppid, userAppsecret, request.getCode());
            if (StringUtils.isEmpty(code2SessionResponse.getOpenid())) {
                throw new ServiceException("绑定公众号失败");
            }
            Long memberId = MallSecurityUtils.getLoginMember().getMemberId();
            // 更新用户公众号openid
            remoteMemberApi.updateMemberPublishOpenid(memberId, code2SessionResponse.getOpenid());
        } catch (IOException e) {
            log.error(" bindPublishCode异常,", e);
            throw new RuntimeException(e);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean unbindPublishCode() {
        Long memberId = MallSecurityUtils.getMemberId();
        // 更新用户公众号openid
        remoteMemberApi.updateMemberPublishOpenid(memberId, StringPool.EMPTY);
        return Boolean.TRUE;
    }

    @Override
    public SmsCodeValidRespDTO checkSmsCode(LoginSmsReq loginSmsReq) {
        SmsCodeValidDTO smsCodeValidDTO = new SmsCodeValidDTO(loginSmsReq.getPhone(), loginSmsReq.getCode(), ServletUtils.getClientIP());
        return smsApi.validateSmsCode(smsCodeValidDTO).getCheckedData();
    }

    @Override
    public Boolean updateMemberPassword(MemberUpdatePwdVO request) {
        request.setPassword(SecurityUtils.encryptPassword(request.getPassword()));
        return memberApi.updateMemberPwd(request).getCheckedData();
    }

    @Override
    public LoginUserInfoVO getUserinfo() {
        Long memberId = MallSecurityUtils.getMemberId();
        Long branchId = MallSecurityUtils.getBranchId();

        MemberDTO memberDTO = portalCacheService.getMemberDTO(memberId);
        BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
        AreaDTO areaDto = portalCacheService.getAreaDto(branchDto.getAreaId());
        log.info("区域城市---{}", JsonUtils.toJsonString(areaDto));
        branchDto.setDcId(areaDto.getDcId());

        // 门店信息为空，说明是简易注册账户，设置一个默认的门店城市信息
        Integer isCreateBranch = NumberPool.INT_ZERO;
        if (Objects.isNull(branchId) && Objects.nonNull(memberDTO)) {
            isCreateBranch = NumberPool.INT_ONE;
            // 查询门店是否存在提交信息
            Boolean isExist  = branchRegisterApi.getBranchRegisterByUserName(memberDTO.getUserName()).getCheckedData();
            if (isExist) {
                isCreateBranch = NumberPool.INT_TWO;
            }
        }


        LoginUserInfoVO infoVO = new LoginUserInfoVO(
                branchDto,
                memberDTO,
                Objects.nonNull(branchDto) ? portalCacheService.getColonel(branchDto.getColonelId()) : null,
                Objects.nonNull(branchDto) ? portalCacheService.getAreaDto(branchDto.getAreaId()) : null,
                isCreateBranch
        );

        // 获取支付平台
        if (Objects.nonNull(memberDTO)) {
            PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(memberDTO.getSysCode());
            if (Objects.nonNull(payConfigDTO)) {
                infoVO.setPayPlatform(payConfigDTO.getStoreOrderPayPlatform());
            }
        }

        BasicSettingPolicyDTO basicSettingPolicyDTO = portalCacheService.getBasicSettingPolicyDTO(areaDto.getDcId());
        if(null != basicSettingPolicyDTO && null != infoVO.getMember()){
            infoVO.getMember().setClassShow(basicSettingPolicyDTO.getClassShow());
        }

        return infoVO;
    }

    @Override
    public ColonelAppMallLoginResponse colonelAppMallLogin(ColonelAppMallLoginRequest request) {
        ColonelDTO colonelDTO = portalCacheService.getColonel(request.getColonelId());
        if (ToolUtil.isEmpty(colonelDTO)) {
            throw new ServiceException("不存在的业务员信息");
        }
        BranchDTO branchDTO = portalCacheService.getBranchDto(request.getBranchId());
        if (ToolUtil.isEmpty(branchDTO)) {
            throw new ServiceException("不存在的门店信息");
        }
        PartnerDto partnerDto = portalCacheService.getPartnerDto(colonelDTO.getSysCode() + "");
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        // 签名验证
        String sign = "";
        try {
            sign = SecretUtil.decode(request.getSign(), colonelDTO.getSignSecretPrivate());
//            sign =  SecretUtil.encrypt(request.getColonelId() + "" + request.getBranchId() + request.getRandomStr(), colonelDTO.getSignSecret());
        } catch (Exception e) {
            throw new ServiceException("签名验证失败:"+e);
        }
        if (!Objects.equals(sign,request.getSignInfo())){
            throw new ServiceException("签名比对验证失败！");
        }

        MemberDTO memberDTO = memberApi.getMemBerByColonelId(colonelDTO.getColonelId(), colonelDTO.getSysCode()).getCheckedData();
        if (ToolUtil.isEmpty(memberDTO)) { // 未报错，且未查询到业务员商城用户信息，则创建业务员商城用户
            // 创建业务员商城用户
            MemberDTO member = new MemberDTO();
            member.setMemberName(colonelDTO.getColonelName());
            member.setIsColonel(NumberPool.INT_ONE);
            member.setRelateColonelId(colonelDTO.getColonelId());
            member.setSysCode(colonelDTO.getSysCode());
            memberDTO = memberApi.createColonelMember(member).getCheckedData();
        }

        LoginMember loginMember = new LoginMember();
        loginMember.setSysCode(colonelDTO.getSysCode());
        loginMember.setMember(memberDTO);
        loginMember.setBranchId(branchDTO.getBranchId());
        loginMember.setAreaId(branchDTO.getAreaId());
        Map<String, Object> tokenMap = mallTokenService.createToken(loginMember);

        ColonelAppMallLoginResponse response = new ColonelAppMallLoginResponse();
        String token = (String) tokenMap.get("access_token");
        response.setToken(token);
        response.setSource(partnerDto.getSource());
        return response;
    }

    public AuthLoginResponse getToken(MemberDTO member, Long sysCode, BranchDTO branchDTO) {
        Long branchId = branchDTO.getBranchId();
        if (branchId != null) {
            //每次获取token时 都需要更新门店的最后一次登陆时间
            remoteBranchApi.updateLastLoginTimeByApi(sysCode, branchId);

            //发送业务员APP更新客户信息MQ
            mallMqProducer.sendEsColonelAppBranchEvent(new ColonelAppBranchDTO(branchId, sysCode));
        }

        LoginMember loginMember = new LoginMember();
        loginMember.setSysCode(sysCode);
        loginMember.setMember(member);
        loginMember.setBranchId(branchId);
        loginMember.setAreaId(branchDTO.getAreaId());
        // 门店信息为空，说明是简易注册账户，设置一个默认的门店城市信息
        Integer isCreateBranch = NumberPool.INT_ZERO;
        if (ToolUtil.isEmpty(branchDTO) || ToolUtil.isEmpty(branchDTO.getBranchId())) {
            AreaDTO areaDTO = remoteAreaApi.getDefaultBySyscode(sysCode).getCheckedData();
            if (ToolUtil.isNotEmpty(areaDTO)) {
                loginMember.setAreaId(areaDTO.getAreaId());
            }

            isCreateBranch = NumberPool.INT_ONE;
            // 查询门店是否存在提交信息
            Boolean isExist  = branchRegisterApi.getBranchRegisterByUserName(member.getUserName()).getCheckedData();
            if (isExist) {
                isCreateBranch = NumberPool.INT_TWO;
            }

        }
        Map<String, Object> tokenMap = mallTokenService.createToken(loginMember);

        //先删了老的在member的login token字段  再更新的，新的在loginMember的token字段
        MemberDTO dot=new MemberDTO();
        dot.setMemberId(member.getMemberId());
        dot.setLoginToken(loginMember.getToken());
        //将新生成的Token 更新member到的Token字段
        remoteMemberApi.updateMemberToken(dot);

        String token = (String) tokenMap.get("access_token");
        Long expiresIn = (Long) tokenMap.get("expires_in");


        AuthLoginResponse response = new AuthLoginResponse();
        response.setAvatar(member.getAvatar())
                .setMemberId(member.getMemberId())
                .setNickname(member.getMemberName())
                .setToken(token)
                .setExpiresIn(expiresIn)
                .setBranchId(branchId)
                .setBranchName(branchDTO.getBranchName())
                .setBranchAddr(branchDTO.getBranchAddr())
                .setUserName(member.getUserName())
                .setIsShopManager(member.getIsShopManager())
                .setIsCreateBranch(isCreateBranch)
                .setAreaId(branchDTO.getAreaId());

        //获取业务员信息
        Long colonelId = branchDTO.getColonelId();
        if (colonelId != null) {
            ColonelDTO colonel = portalCacheService.getColonel(colonelId);
            if (ToolUtil.isNotEmpty(colonel)) {
                response.setColonelId(colonel.getColonelId())
                        .setColonelName(colonel.getColonelName())
                        .setColonelPhone(colonel.getColonelPhone());
            }
        }
        return response;
    }

    public AuthLoginResponse thirdPartyLogin(UserLoginRequest request) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        ThirdPartyLoginService loginHandler = null;
        for (ThirdPartyLoginService loginService : thirdPartyLoginServiceList) {
            if (loginService.support(request.getLoginFrom())) {
                loginHandler = loginService;
                break;
            }
        }
        if (null == loginHandler) {
            throw new ServiceException("暂不支持该平台登录");
        }
        Long sysCode = partnerDto.getSysCode();
        return loginHandler.login(request, (memberDto, branchDto) -> getToken(memberDto, sysCode, branchDto));
    }
}
