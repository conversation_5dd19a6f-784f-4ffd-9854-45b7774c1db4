package com.zksr.portal.service.impl.price;

import cn.hutool.core.lang.Assert;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.FgActivityTimesRuleEnum;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024年05月18日 10:36
 * @description: 秒杀活动价格计算
 */
@Component
@Order(TradePriceCalculatorService.ORDER_SK_ACTIVITY)
@Slf4j
public class TradeSkActivityPriceCalculatorServiceImpl implements TradePriceCalculatorService {
    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisActivityService redisActivityService;

    @Autowired
    private RedisStockService redisStockService;

    @Override
    public void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result) {
        /**
         * 如果活动集合为NULL 或者 集合为空，不计算
         */
        if (ToolUtil.isEmpty(param.getActivitys()) || param.getActivitys().size() <= 0){
            return;
        }

        List<PrmActivityDTO> prmActivityList = param.getActivitys().stream()
                .filter(activityVO -> activityVO.getActivityType().equalsIgnoreCase(TrdDiscountTypeEnum.SK.getType()))
                .map(activityVO -> { return portalCacheService.getActivityDto(activityVO.getActivityId()); })
                .filter(ToolUtil.distinctByKey(ActivityDTO::getActivityId)) // 过滤去重
                .collect(Collectors.toList());

        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, String> unitMap = Optional.ofNullable(dictCache).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (v1, v2) -> v2));

        prmActivityList.forEach(activityVO -> {
                    //1、获取促销活动和使用范围
                    PrmActivityDTO activityDTO = portalCacheService.getActivityDto(activityVO.getActivityId());
                    if (ToolUtil.isEmpty(activityDTO) || ToolUtil.isEmpty(activityDTO.getActivityId())) {
                        log.error("促销单ID未查到到数据！");
                        return;
                    }

                    // 2、获取活动适用范围
                    SupplierActivityDTO supplierActivity = ActivityConvert.INSTANCE.convert(activityDTO);
                    Boolean boo = redisActivityService.validateActivity(supplierActivity, param.getBranchDto());
                    if (!boo) {
                        log.error("促销单【{}】验证数据未通过！", activityDTO.getPrmSheetNo());
                        return;
                    }


                    // 3、获取秒杀促销活动规则
                    Set<SkRuleDTO> skRuleDTOS = portalCacheService.getActivityRuleDto(activityVO.getActivityId(), TrdDiscountTypeEnum.SK.getType());
                    Map<Long, SkRuleDTO> skRuleDTOMap = CollectionUtils.convertMap(skRuleDTOS, SkRuleDTO::getSkuId);


                    TradePriceCalculateResp.Promotion promotion = new TradePriceCalculateResp.Promotion();
                    promotion.setId(activityDTO.getActivityId())
                            .setName(activityDTO.getActivityName())
                            .setType(TrdDiscountTypeEnum.SK.getType())
                            .setSupplierId(activityDTO.getSupplierId())
                            .setPromotionItems(new ArrayList<>());

                    // 门店当前下单秒杀
//                    Map<String, Long> branchActivityStockMap = new HashMap<>();
//                    Map<String, Long> activityStockMap = new HashMap<>();

                    List<Long> supplierScopes = portalCacheService.getActivitySupplierScopeList(activityVO.getActivityId()).stream().map(ActivitySupplierScopeDTO::getSupplierId).collect(Collectors.toList());

                    result.getItems().stream().filter(supplier -> supplierScopes.contains(supplier.getSupplierId()))
                            .forEach(supplier -> {
//                                List<TradePriceCalculateResp.OrderItem.SupplierItem> items = new ArrayList<>();
                                supplier.getSupplierItems().stream().filter(supplierItem -> skRuleDTOMap.containsKey(supplierItem.getSkuId()) && supplierItem.getIsActivitySpSk() == 1)
                                        .collect(Collectors.groupingBy(TradePriceCalculateResp.OrderItem.SupplierItem::getSkuId))
                                        .forEach((key, value) -> {
                                            SkRuleDTO skRule = skRuleDTOMap.get(key);
                                            // 门店已购小单位数量
                                            BigDecimal branchSaleQty = redisActivityService.getSkSaleNum(param.getBranchDto().getBranchId(), skRule.getActivityId(), skRule.getSkRuleId());
                                            // 门店已购中单位数量
                                            BigDecimal branchMidSaleQty = redisActivityService.getSkMidSaleNum(param.getBranchDto().getBranchId(), skRule.getActivityId(), skRule.getSkRuleId());
                                            // 门店已购大单位数量
                                            BigDecimal branchLargeSaleQty = redisActivityService.getSkLargeSaleNum(param.getBranchDto().getBranchId(), skRule.getActivityId(), skRule.getSkRuleId());
                                            // 活动已购总数量
                                            BigDecimal totalSaleQty = redisActivityService.getSkSaleNum(skRule.getActivityId(), skRule.getSkRuleId());
                                            BigDecimal midTotalSaleQty = redisActivityService.getSkMidSaleNum(skRule.getActivityId(), skRule.getSkRuleId());
                                            BigDecimal largeTotalSaleQty = redisActivityService.getSkLargeSaleNum(skRule.getActivityId(), skRule.getSkRuleId());

                                            // 如果没有门店限量, 那门店限量就无限制
                                            int onceLimit = (Objects.nonNull(skRule.getOnceLimit()) ? skRule.getOnceLimit() : Integer.MAX_VALUE);
                                            int midLimit = (Objects.nonNull(skRule.getMidLimit()) ? skRule.getMidLimit() : Integer.MAX_VALUE);
                                            int largeLimit = (Objects.nonNull(skRule.getLargeLimit()) ? skRule.getLargeLimit() : Integer.MAX_VALUE);
                                            // 如果没有总限量, 那总限量就是SKU库存
                                            Integer seckillStock = skRule.getSeckillStock();
                                            Integer seckillMidStock = skRule.getSeckillMidStock();
                                            Integer seckillLargeStock = skRule.getSeckillLargeStock();

                                            // 本次下单SKU购买的小单位数量
                                            BigDecimal smallBuyCount = value.stream().filter(item -> UnitTypeEnum.UNIT_SMALL.getType().equals(item.getUnitSize())).map(item -> BigDecimal.valueOf(item.getCount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                            // 本次下单SKU购买的中单位数量
                                            BigDecimal midBuyCount = value.stream().filter(item -> UnitTypeEnum.UNIT_MIDDLE.getType().equals(item.getUnitSize())).map(item -> BigDecimal.valueOf(item.getCount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                            // 本次下单SKU购买的大单位数量
                                            BigDecimal largeBuyCount = value.stream().filter(item -> UnitTypeEnum.UNIT_LARGE.getType().equals(item.getUnitSize())).map(item -> BigDecimal.valueOf(item.getCount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                                            String smallUnitName = value.stream().filter(item -> UnitTypeEnum.UNIT_SMALL.getType().equals(item.getUnitSize())).map(TradePriceCalculateResp.OrderItem.SupplierItem::getUnit).findFirst().map(unitMap::get).orElse("");
                                            String midUnitName = value.stream().filter(item -> UnitTypeEnum.UNIT_MIDDLE.getType().equals(item.getUnitSize())).map(TradePriceCalculateResp.OrderItem.SupplierItem::getUnit).findFirst().map(unitMap::get).orElse("");
                                            String largeUnitName = value.stream().filter(item -> UnitTypeEnum.UNIT_LARGE.getType().equals(item.getUnitSize())).map(TradePriceCalculateResp.OrderItem.SupplierItem::getUnit).findFirst().map(unitMap::get).orElse("");

                                            try {
                                                // 超出门店限购（小单位）
                                                Assert.isTrue(
                                                        BigDecimal.valueOf(onceLimit).compareTo(branchSaleQty.add(smallBuyCount)) >= 0,
                                                        StringUtils.format("购买商品【{}】超出秒杀活动【{}】门店限购数量【{}{}】",
                                                                value.get(0).getSpuName(),
                                                                activityDTO.getActivityName(),
                                                                onceLimit,
                                                                smallUnitName
                                                                ));
                                                // 超出门店限购（中单位）
                                                Assert.isTrue(
                                                        BigDecimal.valueOf(midLimit).compareTo(branchMidSaleQty.add(midBuyCount)) >= 0,
                                                        StringUtils.format("购买商品【{}】超出秒杀活动【{}】门店限购数量【{}{}】",
                                                                value.get(0).getSpuName(),
                                                                activityDTO.getActivityName(),
                                                                midLimit,
                                                                midUnitName
                                                        ));
                                                // 超出门店限购（大单位）
                                                Assert.isTrue(
                                                        BigDecimal.valueOf(largeLimit).compareTo(branchLargeSaleQty.add(largeBuyCount)) >= 0,
                                                        StringUtils.format("购买商品【{}】超出秒杀活动【{}】门店限购数量【{}{}】",
                                                                value.get(0).getSpuName(),
                                                                activityDTO.getActivityName(),
                                                                largeLimit,
                                                                largeUnitName
                                                        ));

                                                // 总库存限购（小单位）
                                                if (Objects.nonNull(seckillStock)) {
                                                    // 活动限量
                                                    Assert.isTrue(
                                                            BigDecimal.valueOf(seckillStock).compareTo(totalSaleQty.add(smallBuyCount)) >= 0,
                                                            StringUtils.format("购买商品【{}】超出秒杀活动【{}】剩余库存【{}{}】",
                                                                    value.get(0).getSpuName(),
                                                                    activityDTO.getActivityName(),
                                                                    BigDecimal.valueOf(seckillStock).subtract(totalSaleQty),
                                                                    smallUnitName
                                                            ));
                                                }
                                                // 总库存限购（中单位）
                                                if (Objects.nonNull(seckillMidStock)) {
                                                    // 活动限量
                                                    Assert.isTrue(
                                                            BigDecimal.valueOf(seckillMidStock).compareTo(midTotalSaleQty.add(midBuyCount)) >= 0,
                                                            StringUtils.format("购买商品【{}】超出秒杀活动【{}】剩余库存【{}{}】",
                                                                    value.get(0).getSpuName(),
                                                                    activityDTO.getActivityName(),
                                                                    BigDecimal.valueOf(seckillMidStock).subtract(midTotalSaleQty),
                                                                    midUnitName
                                                            ));
                                                }
                                                // 总库存限购（大单位）
                                                if (Objects.nonNull(seckillLargeStock)) {
                                                    // 活动限量
                                                    Assert.isTrue(
                                                            BigDecimal.valueOf(seckillLargeStock).compareTo(largeTotalSaleQty.add(largeBuyCount)) >= 0,
                                                            StringUtils.format("购买商品【{}】超出秒杀活动【{}】剩余库存【{}{}】",
                                                                    value.get(0).getSpuName(),
                                                                    activityDTO.getActivityName(),
                                                                    BigDecimal.valueOf(seckillLargeStock).subtract(largeTotalSaleQty),
                                                                    largeUnitName
                                                            ));
                                                }

                                                // 本次下单SKU所购买 的最小单位的总数量
                                                BigDecimal skuCount = value.stream()
                                                        .map((item -> BigDecimal.valueOf(item.getCount()).multiply(item.getOrderUnitSize()))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                                                // 可用库存限量
                                                BigDecimal surplusSaleQty = redisStockService.getSurplusSaleQtyBigDecimal(skRule.getSkuId());
                                                Assert.isTrue(
                                                        surplusSaleQty.compareTo(skuCount) >= 0,
                                                        StringUtils.format("购买商品【{}】超出剩余库存【{}{}】",
                                                                value.get(0).getSpuName(),
                                                                surplusSaleQty,
                                                                smallUnitName
                                                        ));

                                            } catch (IllegalArgumentException e) {
                                                throw new ServiceException(e.getMessage());
                                            }

                                            // 验证是否超出秒杀库存
                                            /*Assert.isTrue((branchSaleQty + skuCount) <= onceLimit && (totalSaleQty +skuCount) <= seckillStock,
                                                    "购买商品【{}】超出秒杀活动【{}】门店限购数量【{}】或活动总限购数量【{}】 ,门店已购数量【{}】、活动总已购数量【{}】, 本次购买数量【{}】",
                                                    value.get(0).getSpuName(), activityDTO.getActivityName(), onceLimit, seckillStock, branchSaleQty, totalSaleQty, skuCount);*/


                                            value.forEach(supplierItem -> {
                                                // 秒杀价格
                                                BigDecimal activityPrice = skRule.getSeckilPriceByUnitType(supplierItem.getUnitSize());

                                                // 优惠商品数量后商品总金额
                                                BigDecimal itemAmt = new BigDecimal(supplierItem.getCount() + "").multiply(activityPrice).setScale(StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                                                // 商品优惠金额 = 商品原价总金额 - 优惠后商品总金额
                                                BigDecimal discountAmt = new BigDecimal(supplierItem.getCount() + "").multiply(supplierItem.getPrice()).subtract(itemAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                                                // 优惠商品优惠金额
                                                supplierItem.setActivityDiscountAmt(supplierItem.getActivityDiscountAmt().add(discountAmt));
                                                supplierItem.setSalePrice(supplierItem.getPrice()); // 原销售价
                                                supplierItem.setPrice(activityPrice); // 现价
                                                supplierItem.setPayPrice(supplierItem.getPrice().multiply(new BigDecimal(supplierItem.getCount()+""))); // 现总金额
                                                supplierItem.setIsActivitySpSk(NumberPool.INT_ONE); // 参与促销

                                                // 营销信息
                                                TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
                                                promotion.getPromotionItems().add(promotionItem);
                                                promotionItem.setActivityRuleId(skRule.getSkRuleId());
                                                promotionItem.setSkuId(supplierItem.getSkuId());
                                                promotionItem.setActivityDiscountAmt(discountAmt);
                                                promotionItem.setUuIdNo(supplierItem.getUuIdNo());
                                                promotionItem.setCount(supplierItem.getCount());
                                                promotionItem.setUnitSizeQty(supplierItem.getOrderUnitSize()); // 单位转换数量
                                                promotionItem.setUnitSize(supplierItem.getUnitSize());
                                            });
                                        });


                                    // 商品大中小改造之前的代码逻辑如下
//                                supplier.getSupplierItems().stream().filter(supplierItem -> skRuleDTOMap.containsKey(supplierItem.getSkuId()) && supplierItem.getIsActivitySpSk() == 1)
//                                        .forEach(supplierItem -> {
//                                            SkRuleDTO skRule = skRuleDTOMap.get(supplierItem.getSkuId());
//                                            // 门店已购数量
//                                            Integer branchSaleQty = redisActivityService.getSkSaleNum(param.getBranchDto().getBranchId(), skRule.getActivityId(), skRule.getSkRuleId());
//                                            // 活动已购总数量
//                                            Integer totalSaleQty = redisActivityService.getSkSaleNum(skRule.getActivityId(), skRule.getSkRuleId());
//                                            Assert.isTrue((branchSaleQty + supplierItem.getCount()) <= skRule.getOnceLimit() && (totalSaleQty + supplierItem.getCount()) <= skRule.getSeckillStock(),
//                                                    "购买商品【{}】超出秒杀活动【{}】门店限购数量【{}】或活动总限购数量【{}】 ,门店已购数量【{}】、活动总已购数量【{}】, 本次购买数量【{}】",
//                                                    supplierItem.getSpuName(), activityDTO.getActivityName(), skRule.getOnceLimit(), skRule.getSeckillStock(), branchSaleQty, totalSaleQty, supplierItem.getCount());
//
//
//
//                                            // 优惠商品数量后商品总金额
//                                            BigDecimal itemAmt = new BigDecimal(supplierItem.getCount() + "").multiply(skRule.getSeckillPrice()).setScale(StatusConstants.PRICE_RESERVE_6, BigDecimal.ROUND_HALF_UP);
//                                            // 商品优惠金额 = 商品原价总金额 - 优惠后商品总金额
//                                            BigDecimal discountAmt = new BigDecimal(supplierItem.getCount() + "").multiply(supplierItem.getPrice()).subtract(itemAmt).setScale(StatusConstants.PRICE_RESERVE_2, BigDecimal.ROUND_HALF_UP);
//                                            // 优惠商品优惠金额
//                                            supplierItem.setActivityDiscountAmt(supplierItem.getActivityDiscountAmt().add(discountAmt));
//                                            supplierItem.setSalePrice(supplierItem.getPrice()); // 原销售价
//                                            supplierItem.setPrice(skRule.getSeckillPrice()); // 现价
//                                            supplierItem.setPayPrice(supplierItem.getPrice().multiply(new BigDecimal(supplierItem.getCount()+""))); // 现总金额
//                                            supplierItem.setIsActivitySpSk(NumberPool.INT_ONE); // 参与促销
//
//                                            // 营销信息
//                                            TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
//                                            promotion.getPromotionItems().add(promotionItem);
//                                            promotionItem.setActivityRuleId(skRule.getSkRuleId());
//                                            promotionItem.setSkuId(supplierItem.getSkuId());
//                                            promotionItem.setActivityDiscountAmt(discountAmt);
//                                            promotionItem.setUuIdNo(supplierItem.getUuIdNo());
//                                            promotionItem.setCount(supplierItem.getCount());
//                                        });
                            });

                    if (promotion.getPromotionItems().size() > 0) {
                        result.getPromotions().add(promotion);
                    }
                });

    }

    @Override
    public void updateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.SK.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    Long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    BigDecimal spSkCount = BigDecimal.valueOf(orderDiscount.getSpSkCount());
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        // set秒杀活动商品门店已购数量（小单位）
                        redisActivityService.setSkSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, time, TimeUnit.SECONDS);
                        // set秒杀活动商品已购总数量（小单位）
                        redisActivityService.setSkSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, time, TimeUnit.SECONDS);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        // set秒杀活动商品门店已购数量（中单位）
                        redisActivityService.setSkMidSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, time, TimeUnit.SECONDS);
                        // set秒杀活动商品已购总数量（中单位）
                        redisActivityService.setSkMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, time, TimeUnit.SECONDS);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        // set秒杀活动商品门店已购数量（大单位）
                        redisActivityService.setSkLargeSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, time, TimeUnit.SECONDS);
                        // set秒杀活动商品已购总数量（大单位）
                        redisActivityService.setSkLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, time, TimeUnit.SECONDS);
                    }
                });
    }

    @Override
    public void undoUpdateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.SK.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    Long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    BigDecimal spSkCount = BigDecimal.valueOf(orderDiscount.getSpSkCount());
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        // set秒杀活动商品门店已购数量（小单位）
                        redisActivityService.setSkSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                        // set秒杀活动商品已购总数量（小单位）
                        redisActivityService.setSkSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        // set秒杀活动商品门店已购数量（中单位）
                        redisActivityService.setSkMidSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                        // set秒杀活动商品已购总数量（中单位）
                        redisActivityService.setSkMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        // set秒杀活动商品门店已购数量（大单位）
                        redisActivityService.setSkLargeSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                        // set秒杀活动商品已购总数量（大单位）
                        redisActivityService.setSkLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                    }
                });
    }
}
