package com.zksr.portal.service.impl.mall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.enums.CouponReceiveType;
import com.zksr.common.core.enums.CouponStateEnum;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisCouponConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.controller.mall.vo.coupon.*;
import com.zksr.portal.controller.mall.vo.prdt.CouponSkuSearchReqVO;
import com.zksr.portal.convert.coupon.CouponConvert;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IActivityService;
import com.zksr.portal.service.mall.ICouponService;
import com.zksr.portal.service.mall.ISkuPriceService;
import com.zksr.portal.service.mall.ISkuService;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.*;
import com.zksr.promotion.api.coupon.handler.CouponSpuScopeStrategy;
import com.zksr.promotion.api.coupon.handler.GlobalProductSpuScopeStrategy;
import com.zksr.promotion.api.coupon.handler.LocalProductSpuScopeStrategy;
import com.zksr.promotion.api.coupon.vo.CouponBatchListVO;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.promotion.api.coupon.vo.CouponSpuScopeValidReqVO;
import com.zksr.promotion.api.coupon.vo.OrderAvailableRespVO;
import com.zksr.promotion.utils.CouponScopeUtil;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 优惠券服务
 * @date 2024/4/1 15:23
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class CouponServiceImpl implements ICouponService {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private CouponApi couponApi;

    @Autowired
    private ISkuPriceService skuPriceService;

    @Autowired
    private IActivityService activityService;

    /**
     * 获取可领取有效优惠券
     * @param couponValidReq
     * @return
     */
    @Override
    public List<CouponValidItemVO> getValidCouponList(CouponValidReqVO couponValidReq) {
        /*Long branchId = MallSecurityUtils.getLoginMember().getBranchId();
        // 获取门店
        BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
        // 获取缓存优惠券列表
        List<CouponNormalCacheDTO> cacheCouponList = redisService.getCacheList(RedisCouponConstants.getSysCodeCouponList(branchDto.getSysCode()));
        if (Objects.isNull(cacheCouponList)) {
            return ListUtil.empty();
        }
        // 过滤有效期
        cacheCouponList = filterDate(cacheCouponList, branchDto);
        // 获取领取范围
        cacheCouponList = filterReceiveScope(cacheCouponList, branchDto);
        // 过滤库存
        cacheCouponList = filterStock(cacheCouponList, branchDto);
        // 转换对象输出
        List<CouponValidItemVO> resultList = cacheCouponList.stream().map(item -> {
            // 转换成返回对象
            CouponValidItemVO validItemVO = CouponValidItemVO.convert(item);
            // 设置入驻商名称
            CouponConvert.INSTANCE.buildSet(validItemVO, portalCacheService.getSupplierDTO(item.getSupplierId()));
            return validItemVO;
        })
        // 过滤是否领取, 重复领取
        .filter(item -> this.filterReceiveLimit(item, branchDto, Boolean.TRUE))
        .collect(Collectors.toList());
        return resultList;*/

        return null;
    }

    @Override
    public List<CouponValidItemVO> getProductValidCouponList(ProductValidCouponReqVO couponValidReq) {
        Long branchId = MallSecurityUtils.getLoginMember().getBranchId();
        // 获取门店
        BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
        List<CouponValidItemVO> result = new ArrayList<>();
        Long brandId = null;
        Long skuId = null;
        Long supplierId = null;
        SpuDTO spu = null;
        if (ProductType.isGlobal(couponValidReq.getType())) {
            SupplierItemDTO supplierItem = portalCacheService.getSupplierItemDTO(couponValidReq.getItemId());
            if (Objects.isNull(supplierItem)) {
                return ListUtil.empty();
            }
            if (supplierItem.isSpuCombine()) {
                SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(supplierItem.getSpuCombineId());
                supplierId = spuCombineDTO.getSupplierId();
            } else {
                spu = portalCacheService.getSpuDTO(supplierItem.getSpuId());
                skuId = supplierItem.getSkuId();
                supplierId = spu.getSupplierId();
            }
        } else {
            AreaItemDTO areaItem = portalCacheService.getAreaItemDTO(couponValidReq.getItemId());
            if (Objects.isNull(areaItem)) {
                return ListUtil.empty();
            }
            if (areaItem.isSpuCombine()) {
                SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(areaItem.getSpuCombineId());
                supplierId = spuCombineDTO.getSupplierId();
            } else {
                spu = portalCacheService.getSpuDTO(areaItem.getSpuId());
                skuId = areaItem.getSkuId();
                supplierId = spu.getSupplierId();
            }
        }
        CouponSpuScopeValidReqVO scopeValidReqVO = new CouponSpuScopeValidReqVO(spu, skuId, supplierId);
        // 查询商品不同的使用范围
        return this.getScopeValidCouponList(scopeValidReqVO, branchDto, supplierId, couponValidReq.getType());
    }

    @Override
    public List<CouponValidItemVO> getScopeValidCouponList(CouponSpuScopeValidReqVO spuScopeInfos, BranchDTO branch, Long supplierId, String productType) {
        // 获取入驻商优惠券信息
        List<CouponNormalCacheDTO> cacheCouponList = redisService.getCacheList(RedisCouponConstants.getSupplierCouponList(supplierId));
        if (Objects.isNull(cacheCouponList)) {
            return new ArrayList<>();
        }
        // 过滤全国还是本地商品
        if (ProductType.isGlobal(productType)) {
            cacheCouponList = cacheCouponList.stream().filter(CouponNormalCacheDTO::isGlobal).collect(Collectors.toList());
        } else {
            cacheCouponList = cacheCouponList.stream().filter(CouponNormalCacheDTO::isLocal).collect(Collectors.toList());
        }
        // 过滤有效期
        cacheCouponList = filterDate(cacheCouponList, branch);
        // 过滤使用范围
        cacheCouponList = filterSpuScope(spuScopeInfos, cacheCouponList, branch);
        // 过滤领取范围
        cacheCouponList = filterReceiveScope(cacheCouponList, branch);
        // 过滤库存
        cacheCouponList = filterStock(cacheCouponList, branch);
        // 转换对象输出
        return cacheCouponList
                .stream()
                .map(CouponValidItemVO::convert)
                // 过滤是否领取, 重复领取
                .filter(item -> this.filterReceiveLimit(item, branch, Boolean.FALSE))
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponReceiveStatusDTO> saveReceiveCoupon(CouponReceiveReqVO receiveReq) {
        BranchDTO branchDto = portalCacheService.getBranchDto(MallSecurityUtils.getLoginMember().getBranchId());
        List<CouponNormalCacheDTO> cacheCouponList = receiveReq.getCouponTemplateIds().stream()
                // 获取缓存优惠券模版
                .map(portalCacheService::getCouponTemplate)
                // 非空过滤 并且是用户领取类型
                .filter(item -> Objects.nonNull(item) && CouponReceiveType.NORMAL.getType().equals(item.getReceiveType()))
                // 转换成可领取优惠券类型
                .map(CouponTemplateDTO::convertCacheNormal)
                .collect(Collectors.toList());
        // 把领取验证参数再验证一遍
        // 过滤有效期
        cacheCouponList = filterDate(cacheCouponList, branchDto);
        // 获取领取范围
        cacheCouponList = filterReceiveScope(cacheCouponList, branchDto);
        // 过滤库存
        cacheCouponList = filterStock(cacheCouponList, branchDto);
        // 过滤是否领取, 重复领取
        cacheCouponList = filterReceive(cacheCouponList, branchDto);
        if (cacheCouponList.isEmpty()) {
            // 没有有效可领取优惠券
            throw exception(COUPON_STOCK_ERR);
        }
        /*ArrayList<CouponReceiveStatusDTO> resultList = new ArrayList<>();
        for (CouponNormalCacheDTO coupon : cacheCouponList) {
            Long couponTemplateId = shelf().receiveCoupon(branchDto.getBranchId(), MallSecurityUtils.getLoginMember().getMemberId(), coupon.getCouponTemplateId());
            CommonResult<List<CouponReceiveStatusDTO>> result = couponApi.receiveCouponNotCheck(new CouponReceiveDTO(
                    ListUtil.toList(couponTemplateId),
                    branchDto.getBranchId(),
                    MallSecurityUtils.getLoginMember().getMemberId()
            ));
            resultList.addAll(result.getCheckedData());
        }*/
        // 准备发送异步领取
        CommonResult<List<CouponReceiveStatusDTO>> result = couponApi.receiveCoupon(
                new CouponReceiveDTO(
                        cacheCouponList.stream().map(CouponNormalCacheDTO::getCouponTemplateId).collect(Collectors.toList()),
                        branchDto.getBranchId(),
                        MallSecurityUtils.getLoginMember().getMemberId(),
                        null
                )
        );
        if (!result.isSuccess()) {
            throw exception(RECEIVE_MQ_CHANNEL_ERR);
        }
        return result.getCheckedData();
    }

    @Override
    public List<CouponReceiveStatusDTO> saveReceiveCoupon2(CouponReceiveReqVO receiveReq) {

        // 准备发送异步领取
        CommonResult<List<CouponReceiveStatusDTO>> result = couponApi.receiveCoupon2(
                new CouponReceiveDTO(
                        Arrays.asList(594258349517307904L),
                        1L,
                        1L,
                        null
                )
        );

        return result.getCheckedData();
    }

    @Override
    public List<CouponReceiveResultDTO> getReceiveCouponStatus(CouponReceiveResultReqVO receiveReq) {
        ArrayList<CouponReceiveResultDTO> result = new ArrayList<>();
        for (Long statusId : receiveReq.getStatusIds()) {
            CouponReceiveResultDTO couponReceiveResult = redisService.getCacheObject(RedisCouponConstants.getCouponReceiveResult(statusId));
            if (Objects.isNull(couponReceiveResult)) {
                couponReceiveResult = new CouponReceiveResultDTO();
                couponReceiveResult.setErr(COUPON_RECEIVE_RESULT_PAST);
            }
            result.add(couponReceiveResult);
        }
        return result;
    }

    /**
     * 获取我的优惠券列表
     * @param receiveReq    分页请求对象
     * @return
     */
    @Override
    public PageResult<CouponRecordVO> getCouponList(CouponPageReqVO receiveReq) {
        // 设置门店ID
        receiveReq.setBranchId(MallSecurityUtils.getLoginMember().getBranchId());
        // 分页获取优惠券数据
        PageResult<CouponDTO> pageResult = couponApi.getCouponList(receiveReq);
        // 转换对象
        PageResult<CouponRecordVO> voPageResult = CouponConvert.INSTANCE.convertRecordPage(pageResult);
        // 封装数据
        voPageResult.getList().stream().collect(Collectors.groupingBy(CouponRecordVO::getCouponTemplateId)).forEach((coupontTemplateId, couponRecordVOList) -> {
            // 加载优惠券模版数据
            List<CouponSpuScopeDTO> couponSpuScopeList = portalCacheService.getCouponSpuScopeList(coupontTemplateId);
            CouponTemplateDTO couponTemplate = portalCacheService.getCouponTemplate(coupontTemplateId);
            // 循环计算优惠券
            couponRecordVOList.forEach(couponRecordVO -> {
                // 计算优惠券是否过期
                if (Objects.nonNull(couponRecordVO.getExpirationDateEnd()) && couponRecordVO.getExpirationDateEnd().getTime() < System.currentTimeMillis()) {
                    couponRecordVO.setState(CouponStateEnum.PAST_DUE.getState());
                }
                // 设置优惠券的可用范围
                couponRecordVO.setSpuScopeAs(couponSpuScopeList);
                if (CollectionUtils.isNotEmpty(couponTemplate.getSupplierIdList())) {
                    String supplierName = couponTemplate.getSupplierIdList().stream().map(t -> {
                        SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(t);
                        if (Objects.nonNull(supplierDTO)) {
                            return supplierDTO.getSupplierName();
                        }
                        return StringPool.EMPTY;
                    }).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                    couponRecordVO.setSupplierName(supplierName);
                } else {
                    // 设置入驻商
                    CouponConvert.INSTANCE.buildSet(
                            couponRecordVO,
                            portalCacheService.getSupplierDTO(couponRecordVO.getSupplierId())
                    );
                }
            });
        });
        return voPageResult;
    }

    /**
     * 获取下单可用优惠券
     * @param req
     * @return
     */
    @Override
    public OrderAvailableRespVO getOrderAvailable(CouponOrderValidReqVO req) {
        OrderAvailableRespVO result = new OrderAvailableRespVO();
        BranchDTO branch = portalCacheService.getBranchDto(req.getBranchId());
        // 组装商品信息, 商品实际使用范围
        assembleSpuScope(req, branch);
        // 拥有的优惠券列表
        List<CouponDTO> couponList = couponApi.getCouponListByBranchIdAndState(branch.getBranchId(), CouponStateEnum.NORMAL.getState()).getCheckedData();
        // 过滤全国or本地
        if (ProductType.isGlobal(req.getItems().get(0).getProductType())) {
            couponList = couponList.stream().filter(item -> item.getFuncScope() == 1).collect(Collectors.toList());
        } else {
            couponList = couponList.stream().filter(item -> item.getFuncScope() == 2).collect(Collectors.toList());
        }
        // 校验过滤黑白名单,判断优惠券是否可用
        couponList = couponList.stream().filter(t -> {
                    List<CouponReceiveScopeDTO> couponSpuScopeList = portalCacheService.getCouponReceiveScopeList(t.getCouponTemplateId());
                    return CouponScopeUtil.conditionReceiveCopeApplyId(HutoolBeanUtils.toBean(branch, CouponScopeUtil.ReceiveScopeValidVO.class), couponSpuScopeList);
                }
        ).collect(Collectors.toList());
        // 有效优惠券
        HashSet<CouponDTO> available = new HashSet<>();
        // 转换数据类型
        Map<Long, CouponTemplateDTO> couponMap = new HashMap<>();
        // 加载适用范围
        couponList.stream().collect(Collectors.groupingBy(CouponDTO::getCouponTemplateId)).forEach((couponTemplateId, list) -> {
            List<CouponSpuScopeDTO> couponSpuScopeList = portalCacheService.getCouponSpuScopeList(couponTemplateId);
            CouponTemplateDTO couponTemplate = portalCacheService.getCouponTemplate(couponTemplateId);
            list.forEach(item -> {
                item.setSpuScopeAs(couponSpuScopeList);
                // 多入驻商列表数据
                item.setSupplierIdList(couponTemplate.getSupplierIdList());
                couponMap.put(item.getCouponId(), couponTemplate);
                // 设置入驻商名称
                if (CollectionUtils.isNotEmpty(couponTemplate.getSupplierIdList())) {
                    String supplierName = couponTemplate.getSupplierIdList().stream().map(t -> {
                        SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(t);
                        if (Objects.nonNull(supplierDTO)) {
                            return supplierDTO.getSupplierName();
                        }
                        return StringPool.EMPTY;
                    }).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
                    item.setSupplierName(supplierName);
                } else {
                    // 设置入驻商
                    SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(item.getSupplierId());
                    if (Objects.nonNull(supplierDTO)) {
                        item.setSupplierName(supplierDTO.getSupplierName());
                    }
                }
            });
        });
        // 默认全无效
        Set<CouponDTO> unavailable = couponList.stream().filter(CouponDTO::timeValidate).collect(Collectors.toSet());
        // 完全无效
        Set<CouponDTO> unavailable02 = couponList.stream().filter(item -> !item.timeValidate()).collect(Collectors.toSet());
        // 构建优惠券验证规则策略
        List<CouponSpuScopeStrategy> strategyList = ListUtil.toList(
                GlobalProductSpuScopeStrategy.getInstance(),
                LocalProductSpuScopeStrategy.getInstance()
        );
        List<CouponTemplateDTO> templateDTOS = couponList.stream().map(CouponDTO::getCouponTemplateId).map(portalCacheService::getCouponTemplate).collect(Collectors.toList());
        // 验证优惠券
        strategyList.forEach(item -> item.productFilter(
                req.getItems(),
                unavailable,
                couponMap,
                available
        ));
        result.setAvaliableList(ListUtil.toList(available));
        // 移除完全无效
        unavailable.addAll(unavailable02);
        result.setUnavaliableList(ListUtil.toList(unavailable));
        return result;
    }

    /**
     * 验证下单优惠券
     * @param orderItemList 订单商品列表
     * @param couponIdList  订单优惠券ID集合
     * @param branch        下单门店
     * @return
     */
    @Override
    public List<CouponDTO> validateCreatOrder(List<OrderValidItemDTO> orderItemList, List<Long> couponIdList, BranchDTO branch) {
        // 返回空集合
        if (ObjectUtil.isEmpty(couponIdList)) {
            return ListUtil.empty();
        }

        // 优惠券集合存在重复数据
        if (couponIdList.stream().collect(Collectors.toSet()).isEmpty()) {
            throw exception(COUPON_REPEAT);
        }

        // 拥有的优惠券列表 branch.getBranchId(),
        List<CouponDTO> couponList = couponApi.getCouponListByCouponIds(new CouponBatchListVO(branch.getBranchId(), couponIdList)).getCheckedData();
        // 过滤全国or本地
        if (ProductType.isGlobal(orderItemList.get(0).getProductType())) {
            couponList = couponList.stream().filter(item -> item.getFuncScope() == 1).collect(Collectors.toList());
        } else {
            couponList = couponList.stream().filter(item -> item.getFuncScope() == 2).collect(Collectors.toList());
        }

        // 查询优惠券的优惠券模板列表
        List<CouponTemplateDTO> couponTemplateList = couponList.stream().map(CouponDTO::getCouponTemplateId).map(portalCacheService::getCouponTemplate).collect(Collectors.toList());
        // 优惠券类型分组
        Map<Integer, List<CouponTemplateDTO>> spuScopeMap = couponTemplateList.stream().collect(Collectors.groupingBy(CouponTemplateDTO::getSpuScope));
        {
            // 不同类型优惠券是否排它校验
            if (MapUtils.isNotEmpty(spuScopeMap) && spuScopeMap.size() > 1) {
                boolean diffTypeExcludable = false;
                for (Map.Entry<Integer, List<CouponTemplateDTO>> entry : spuScopeMap.entrySet()) {
                    diffTypeExcludable = entry.getValue().stream().anyMatch(t -> Objects.nonNull(t) && Objects.nonNull(t.getExcludable()) && StringUtils.equals(t.getExcludable(), StringPool.ONE));
                    if (diffTypeExcludable) {
                        break;
                    }
                }
                if (diffTypeExcludable && couponList.size() > NumberPool.INT_ONE) {
                    // 优惠券数据不能叠加
                    log.error("不同类型优惠券不能叠加使用, 存在不可叠加优惠券");
                    throw exception(COUPON_UNSUPPORTED_ORDER);
                }
            }
        }
        // 全国商品和本地商品不能叠加
        // ...
        {
            long globalCount = couponTemplateList.stream().filter(item -> NumberPool.INT_ONE == item.getFuncScope()).count();
            long localCount = couponTemplateList.stream().filter(item -> NumberPool.INT_ZERO == item.getFuncScope()).count();
            if (globalCount > NumberPool.INT_ZERO && localCount > NumberPool.INT_ZERO) {
                log.error("全国商品和本地商品不能叠加");
                throw exception(COUPON_UNSUPPORTED_ORDER);
            }
        }
        // 使用范围只能叠加一张
        // 按照使用范围分组, 计算使用范围内是不是选择了多张
        {
            // 同类型叠加使用是否排它校验
            spuScopeMap.forEach((key, value) -> {
                // 任一一个设置同类型是否排它为是
                boolean sameTypeExcludable = value.stream().anyMatch(t -> Objects.nonNull(t) && Objects.nonNull(t.getSameTypeExcludable()) && t.getSameTypeExcludable().equals(NumberPool.INT_ONE));
                if (sameTypeExcludable && spuScopeMap.get(key).size() > NumberPool.INT_ONE) {
                    log.error("同类型优惠券只能使用一张券");
                    throw exception(COUPON_UNSUPPORTED_ORDER);
                }
            });
        }

        // 加载适用范围
        couponList.stream().collect(Collectors.groupingBy(CouponDTO::getCouponTemplateId)).forEach((couponTemplateId, list) -> {
            CouponTemplateDTO couponTemplateDTO = portalCacheService.getCouponTemplate(couponTemplateId);
            list.forEach(item -> {
                item.setSpuScopeAs(portalCacheService.getCouponSpuScopeList(couponTemplateId));
                // 多入驻商列表数据
                item.setSupplierIdList(couponTemplateDTO.getSupplierIdList());
            });
        });
        // 有效优惠券
        HashSet<CouponDTO> available = new HashSet<>();
        // 默认全无效
        HashSet<CouponDTO> unavailable = new HashSet<>(couponList.stream().filter(CouponDTO::timeValidate).collect(Collectors.toSet()));
        // 构建优惠券验证规则
        List<CouponSpuScopeStrategy> strategyList = ListUtil.toList(GlobalProductSpuScopeStrategy.getInstance(), LocalProductSpuScopeStrategy.getInstance());
        // 转换数据类型
        Map<Long, CouponTemplateDTO> couponMap = new HashMap<>();
        couponList.forEach(item -> couponMap.put(item.getCouponId(), portalCacheService.getCouponTemplate(item.getCouponTemplateId())));
        // 验证数据
        strategyList.forEach(item -> item.productFilter(
                orderItemList,
                unavailable,
                couponMap,
                available
        ));
        if (!unavailable.isEmpty()) {
            log.error("下单使用优惠券存在不支持的优惠券, 优惠券ID : {}, 订单商品 : {}, 门店ID : {}", JSONUtil.toJsonStr(couponIdList), JSONUtil.toJsonStr(orderItemList), branch.getBranchId());
            throw exception(COUPON_UNSUPPORTED_ORDER);
        }
        return couponList;
    }

    @Override
    @DistributedLock(prefix = RedisLockConstants.LOCK_COUPON_RECEIVE_REFRESH, condition = "#couponTemplateId")
    public Long receiveCoupon(Long branchId, Long memberId, Long couponTemplateId) {
        // 优惠券不存在
        CouponTemplateDTO couponTemplate = portalCacheService.getCouponTemplate(couponTemplateId);
        if (Objects.isNull(couponTemplate)) {
            throw exception(COUPON_NOT_EXITS);
        }
        // 再次校验是否过期, 或者下架, 防止积压消息消费
        if (Objects.isNull(couponTemplate.getTemplateStartDate())
                || Objects.isNull(couponTemplate.getTemplateEndDate())
                || couponTemplate.getStatus() == NumberPool.INT_ONE
                || couponTemplate.getTemplateEndDate().getTime() < System.currentTimeMillis())
        {
            throw exception(COUPON_IS_DISABLE);
        }
        // 验证库存
        String stockUsedKey = RedisCouponConstants.getStockUsedKey(couponTemplateId);
        if ( Objects.nonNull(couponTemplate.getCouponQty()) && couponTemplate.getCouponQty() > NumberPool.INT_ZERO ) {
            Integer stockUsed = redisService.getCacheObject(stockUsedKey);
            if (Objects.isNull(stockUsed) || stockUsed.intValue() + 1 > couponTemplate.getCouponQty().intValue()) {
                // 已领数量大于 库存数量
                throw exception(COUPON_STOCK_ERR);
            }
        }
        // 是否已经领取过 || 超过领取限制
        String couponBranchReceiveQty = RedisCouponConstants.getCouponBranchReceiveQty(couponTemplateId, branchId);
        if (Objects.nonNull(couponTemplate.getLimit()) && !couponTemplate.getLimit().equals(NumberPool.INT_ZERO)) {
            // 已领取数量
            Integer receiveNum = redisService.getCacheObject(couponBranchReceiveQty);
            // 超过限制
            if (Objects.nonNull(receiveNum) && (receiveNum + 1 > couponTemplate.getLimit())) {
                throw exception(COUPON_RECEIVE_LIMIT);
            }
        }
        // 累加已领取
        redisService.incrByCacheObject(stockUsedKey, NumberPool.INT_ONE);
        // 适配重复领取规则
        //repeatRole(couponTemplate, branchId);
        return couponTemplateId;
    }

    /**
     * 加载商品参与的优惠券活动
     * @param branchDTO     门店
     * @param productType   商品类型
     * @param itemList      商品列表
     */
    @Override
    public void renderItemList(BranchDTO branchDTO, ProductType productType, List<SkuPageRespVO> itemList) {
        // 如果没有门店不渲染优惠券信息
        if (Objects.isNull(branchDTO)) {
            return;
        }
        itemList.parallelStream().forEach(item -> {
            CouponSpuScopeValidReqVO scopeValidReqVO = CouponSpuScopeValidReqVO.builder()
                    .supplierId(item.getSupplierId())
                    .categoryId(item.getCatgoryId())
                    .brandId(item.getBrandId())
                    .skuId(item.getSkuId())
                    .build();
            // 查询商品不同的使用范围
            HashSet<CouponValidItemVO> result = new HashSet<>(this.getScopeValidCouponList(scopeValidReqVO, branchDTO, item.getSupplierId(), productType.getType()));
            item.setCouponLabelList(ProductConvert.INSTANCE.convertCouponLabel(result));
        });
    }

    @Override
    public List<TradePriceCalculateResp.OrderItem.SupplierItem> validSupplierItems(List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierItems, CouponDTO coupon) {
        List<TradePriceCalculateResp.OrderItem.SupplierItem> validItemList = new ArrayList<>();
        // 获取优惠券使用范围
        List <CouponSpuScopeDTO> couponSpuScopeList = portalCacheService.getCouponSpuScopeList(coupon.getCouponTemplateId());
        // 按照使用范围分组
        Map<Integer, List<CouponSpuScopeDTO>> scopeMap = couponSpuScopeList.stream().collect(Collectors.groupingBy(CouponSpuScopeDTO::getSpuScope));
        for (TradePriceCalculateResp.OrderItem.SupplierItem supplierItem: supplierItems) {
            // 验证商品是否通过优惠券黑白名单验证
            if (CouponScopeUtil.conditionSpuCopeApplyId(CouponConvert.INSTANCE.convertCouponSpuScopeValid(coupon, supplierItem), scopeMap)) {
                validItemList.add(supplierItem);
            }
        }
        return validItemList;
    }

    /**
     * 组装商品范围信息
     * @param req
     * @param branch
     */
    private void assembleSpuScope(CouponOrderValidReqVO req, BranchDTO branch) {
        // 获取sku service, 需要里面商品类型处理器
        ISkuService skuService = SpringUtils.getBean(ISkuService.class);
        ArrayList<ActivityVerifyItemDTO> activityVerifyItemDTOS = new ArrayList<>();
        for (OrderValidItemDTO item : req.getItems()) {
            AppCarIdDTO carId = AppCarIdDTO.build(item.getCarId());
            if (ProductType.isGlobal(carId.getType())) {
                SupplierItemDTO supplierItem = portalCacheService.getSupplierItemDTO(carId.getSupplierItemId());
                activityVerifyItemDTOS.add(
                        skuService.spuItemInfoService(supplierItem.getItemType())
                                .buildActivityVerifyItemByCouponOrderValidate(branch, supplierItem, item)
                );
            } else {
                AreaItemDTO areaItem = portalCacheService.getAreaItemDTO(carId.getAreaItemId());
                activityVerifyItemDTOS.add(
                        skuService.spuItemInfoService(areaItem.getItemType())
                                .buildActivityVerifyItemByCouponOrderValidate(branch, areaItem, item)
                );
            }
        }
        // 叠加促销后的应付金额
        activityService.applyCouponItemPayAmt(branch, req.getItems(), activityVerifyItemDTOS);
    }

    // 过滤有效期
    @Override
    public List<CouponNormalCacheDTO> filterDate(List<CouponNormalCacheDTO> cacheCouponList, BranchDTO branchDto) {
        return cacheCouponList.stream().filter(item ->
                Objects.nonNull(item.getTemplateStartDate())
                && Objects.nonNull(item.getTemplateEndDate())
                && item.getTemplateStartDate().getTime() < System.currentTimeMillis()
                && item.getTemplateEndDate().getTime() > System.currentTimeMillis()
                && (Objects.nonNull(branchDto) && item.getSysCode().equals(branchDto.getSysCode()))
        ).collect(Collectors.toList());
    }

    // 过滤使用范围
    private List<CouponNormalCacheDTO> filterSpuScope(CouponSpuScopeValidReqVO spuScopeInfos, List<CouponNormalCacheDTO> cacheCouponList, BranchDTO branch) {
        ArrayList<CouponNormalCacheDTO> result = new ArrayList<>();
        cacheCouponList.stream().collect(Collectors.groupingBy(CouponNormalCacheDTO::getCouponTemplateId)).forEach((couponTemplateId, templateList) -> {
            CouponNormalCacheDTO normalCacheDTO = templateList.get(0);
            // 商品非优惠券入驻商的品
            if (Objects.isNull(spuScopeInfos.getSupplierId()) || !normalCacheDTO.getSupplierId().equals(spuScopeInfos.getSupplierId())) {
                return;
            }
            List<CouponSpuScopeDTO> couponSpuScopeList = portalCacheService.getCouponSpuScopeList(normalCacheDTO.getCouponTemplateId());
            // 验证商品是否通过优惠券黑白名单验证
            if (CouponScopeUtil.conditionSpuCopeApplyId(spuScopeInfos, couponSpuScopeList)) {
                result.addAll(templateList);
            }
        });
        return result;
    }

    // 过滤领取范围
    @Override
    public List<CouponNormalCacheDTO> filterReceiveScope(List<CouponNormalCacheDTO> cacheCouponList, BranchDTO branchDto) {
        ArrayList<CouponNormalCacheDTO> result = new ArrayList<>();
        cacheCouponList.stream().collect(Collectors.groupingBy(CouponNormalCacheDTO::getCouponTemplateId)).forEach((couponTemplateId, templateList) -> {
            CouponNormalCacheDTO normalCacheDTO = templateList.get(0);
            // 其他则需要判断黑白名单
            List<CouponReceiveScopeDTO> couponSpuScopeList = portalCacheService.getCouponReceiveScopeList(normalCacheDTO.getCouponTemplateId());
            if (CouponScopeUtil.conditionReceiveCopeApplyId(CouponConvert.INSTANCE.convertCouponReceiveScopeValid(branchDto), couponSpuScopeList)){
                result.addAll(templateList);
            }
        });
        return result;
    }

    // 过滤库存
    @Override
    public List<CouponNormalCacheDTO> filterStock(List<CouponNormalCacheDTO> cacheCouponList, BranchDTO branchDto) {
        ArrayList<CouponNormalCacheDTO> result = new ArrayList<>();
        for (CouponNormalCacheDTO cache : cacheCouponList) {
            if (NumberPool.INT_ZERO == cache.getCouponQty()) {
                // 0 是不限量
                result.add(cache);
            } else {
                // 判断库存数量
                Long couponTemplateId = cache.getCouponTemplateId();
                String stockUsedKey = RedisCouponConstants.getStockUsedKey(couponTemplateId);
                String stockKey = RedisCouponConstants.getStockKey(couponTemplateId);
                // 已领取数量
                Integer stockUsed = redisService.getCacheObject(stockUsedKey);
                // 总可领取数量
                Integer stock = redisService.getCacheObject(stockKey);
                if (Objects.nonNull(stockUsed) && Objects.nonNull(stock)) {
                    if (stock > stockUsed) {
                        result.add(cache);
                    }
                }
            }
        }
        return result;
    }

    /**
     * 判断有没有达到领取限制
     * @param validItem     优惠券数据
     * @param branchDto     门店
     * @param ignoreDone    是否过滤已经领取完的
     * @return
     */
    @Override
    public Boolean filterReceiveLimit(CouponValidItemVO validItem, BranchDTO branchDto, boolean ignoreDone) {
        CouponTemplateDTO couponTemplate = portalCacheService.getCouponTemplate(validItem.getCouponTemplateId());
        Integer num = getReceiveNum(couponTemplate, branchDto);
        if ( Objects.isNull(num) || num == NumberPool.INT_ZERO ) {
            // 0 就是没限制
            validItem.setReceiveNum(NumberPool.INT_ZERO);
            return Boolean.TRUE;
        }
        if (num + 1 > couponTemplate.getLimit() && ignoreDone) {
            return Boolean.FALSE;
        }
        validItem.setReceiveNum(num);
        return Boolean.TRUE;
    }

    @Override
    public CouponTemplateFragmentRespVO getCouponSpuFragmentList(CouponTemplateFragmentReqVO req) {
        BranchDTO cacheBranch = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        BranchDTO branchDTO = Objects.isNull(cacheBranch) ? new BranchDTO() : cacheBranch;
        CouponTemplateFragmentRespVO respVO = new CouponTemplateFragmentRespVO();
        for (Long couponTemplateId : req.getCouponTemplateIdList()) {
            // 获取优惠券信息
            CouponTemplateDTO couponTemplate = portalCacheService.getCouponTemplate(couponTemplateId);
            // 验证范围
            List<CouponNormalCacheDTO> cacheCouponList = ListUtil.toList(couponTemplate.convertCacheNormal());
            // 过滤有效期
            cacheCouponList = filterDate(cacheCouponList, branchDTO);
            // 获取领取范围
            cacheCouponList = filterReceiveScope(cacheCouponList, branchDTO);
            // 过滤库存
            cacheCouponList = filterStock(cacheCouponList, branchDTO);
            if (cacheCouponList.isEmpty()) {
                continue;
            }

            // 查询商品
            CouponSkuSearchReqVO searchReq = new CouponSkuSearchReqVO();
            searchReq.setCouponTemplateId(couponTemplateId);
            searchReq.setPageSize(req.getSize());
            searchReq.setSysSource(req.getSysSource());
            PageResult<SkuPageRespVO> respVOPageResult = skuService().searchCouponSpuDetailList(searchReq);
            if (respVOPageResult.isEmpty()) {
                continue;
            }
            // 封装数据
            CouponTemplateFragmentRespVO.FragmentVO item = new CouponTemplateFragmentRespVO.FragmentVO();
            CouponValidItemVO couponValidItemVO = CouponConvert.INSTANCE.convertValidItemVO(couponTemplate);
            SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(couponTemplate.getSupplierId());
            couponValidItemVO.setSupplierName(supplierDTO.getSupplierName());
            //  加载领取次数
            if (ObjectUtil.isNotEmpty(cacheBranch)) {
                this.filterReceiveLimit(couponValidItemVO, branchDTO, Boolean.FALSE);
            }
            // 放入返回数据
            item.setCouponTemplate(couponValidItemVO);
            item.setItemList(respVOPageResult.getList());
            respVO.getCouponItemList().add(item);
        }
        return respVO;
    }

    @Override
    public List<CouponTemplateDetailRespVO> getCouponTemplateDetail(CouponTemplateDetailReqVO req) {
        List<CouponTemplateDetailRespVO> respVOList = req.getCouponTemplateIdList()
                .stream()
                .map(portalCacheService::getCouponTemplate).collect(Collectors.toList())
                .stream()
                .filter(Objects::nonNull)
                .map(CouponConvert.INSTANCE::convertDetailRespVO)
                .peek(item -> {
                    // 入驻商名称
                    SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(item.getSupplierId());
                    if (Objects.nonNull(supplierDTO)) {
                        item.setSupplierName(supplierDTO.getSupplierName());
                    }
                    // 具体使用范围
                    List<CouponSpuScopeDTO> couponSpuScopeList = portalCacheService.getCouponSpuScopeList(item.getCouponTemplateId());
                    item.setSpuScopeAs(couponSpuScopeList);
                }).collect(Collectors.toList());
        return respVOList;
    }

    // 过滤已领取, 或者达到限制优惠券
    private List<CouponNormalCacheDTO> filterReceive(List<CouponNormalCacheDTO> cacheCouponList, BranchDTO branchDto) {
        List<CouponNormalCacheDTO> result = new ArrayList<>();
        for (CouponNormalCacheDTO cache : cacheCouponList) {
            CouponTemplateDTO couponTemplate = portalCacheService.getCouponTemplate(cache.getCouponTemplateId());
            Long couponTemplateId = couponTemplate.getCouponTemplateId();
            Long branchId = branchDto.getBranchId();
            String branchReceiveQty = RedisCouponConstants.getCouponBranchReceiveQty(couponTemplateId, branchId);
            if (Objects.isNull(couponTemplate.getLimit()) || couponTemplate.getLimit().equals(NumberPool.INT_ZERO)) {
                // 没有领取限制
                result.add(cache);
            } else {
                // 已领取数量
                Integer receiveNum = redisService.getCacheObject(branchReceiveQty);
                if (Objects.isNull(receiveNum) || receiveNum < couponTemplate.getLimit()) {
                    // 没有领取 或者 还可以领取
                    result.add(cache);
                }
            }
        }
        return result;
    }

    // 获取已领取数量
    private Integer getReceiveNum(CouponTemplateDTO couponTemplate, BranchDTO branchDto) {
        Long couponTemplateId = couponTemplate.getCouponTemplateId();
        Long branchId = branchDto.getBranchId();
        String branchReceiveQty = RedisCouponConstants.getCouponBranchReceiveQty(couponTemplateId, branchId);
        if (Objects.isNull(couponTemplate.getLimit()) || couponTemplate.getLimit().equals(NumberPool.INT_ZERO)) {
            // 没有领取限制
            return NumberPool.INT_ZERO;
        } else {
            // 已领取数量
            return redisService.getCacheObject(branchReceiveQty);
        }
    }


    ICouponService shelf() {
        return SpringUtils.getBean(ICouponService.class);
    }

    ISkuService skuService() {
        return SpringUtils.getBean(ISkuService.class);
    }
}
