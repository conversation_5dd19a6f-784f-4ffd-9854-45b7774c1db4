package com.zksr.portal.service.impl.mall;

import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.config.AnntoProxyConfig;
import com.zksr.common.security.service.MallTokenService;
import com.zksr.common.third.wx.WxUtils;
import com.zksr.common.third.wx.dto.WxCode2SessionResponse;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.portal.controller.mall.vo.GetWxInfoRequest;
import com.zksr.portal.controller.mall.vo.GetWxInfoResponse;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.ILoginService;
import com.zksr.system.api.partner.dto.PartnerDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.InetSocketAddress;
import java.net.Proxy;

@Service
@Slf4j
public class LoginServiceImpl implements ILoginService {
    @Autowired
    private IPortalCacheService portalCacheService;

    @Resource
    private MemberApi memberApi;
    @Resource
    private AnntoProxyConfig anntoProxyConfig;

    @Override
    public GetWxInfoResponse getWxInfo(GetWxInfoRequest request) throws Exception {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if(ToolUtil.isEmpty(partnerDto)){
            throw new ServiceException("不存在的平台商信息");
        }
//TODO 从平台商配置获取 @lxy
//        String userAppid = partnerDto.getUserAppid();
//        String userAppsecret = partnerDto.getUserAppsecret();
        Long sysCode = partnerDto.getSysCode();
        String userAppid = "";
        String userAppsecret = "";

        Proxy proxy = null;
        //是否使用代理
        if(anntoProxyConfig.isEnable() && org.apache.commons.lang3.StringUtils.isNotEmpty(anntoProxyConfig.getHost())) {
            log.warn(" 使用代理请求...");
            // 设置代理
            proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(anntoProxyConfig.getHost(), anntoProxyConfig.getPort()));

        }
        WxCode2SessionResponse code2SessionResponse = WxUtils.getOpenId(userAppid, userAppsecret, request.getCode(), proxy);

        if(ToolUtil.isEmpty(code2SessionResponse)) {
            log.error("调取微信获取openid失败");
            throw new ServiceException("调取微信获取openid失败");
        }

        if(ToolUtil.isNotEmpty(code2SessionResponse.getErrcode()) || ToolUtil.isNotEmpty(code2SessionResponse.getErrmsg())) {
            String msg = code2SessionResponse.getErrcode() + ":" + code2SessionResponse.getErrmsg();
            log.error("调取微信获取openid失败"+msg);
            throw new ServiceException("调取微信获取openid失败"+msg);
        }

        String openid = code2SessionResponse.getOpenid();
        String unionid = code2SessionResponse.getUnionid();
        String sessionKey = code2SessionResponse.getSession_key();
        //redis根据openid+code 存入sessionKey
        portalCacheService.setWxSessionKey(sysCode+":"+openid+":"+request.getCode(), sessionKey);

        //需要根据unionid 和 openid 查询是否是新用户  TODO 这里可能会有问题，应根据手机号判断
        MemberDTO member = memberApi.getInfoByMobileAndOpenid(sysCode, null, openid, null).getCheckedData();
//        MemberDTO member = memberService.getMemberByWxInfo(partnerDto.getSysCode(), null, unionid);
        //是否新用户：1是0否
        String isNew = "";
        String mobile = "";
        String nickname = "";
        String avatar = "";
        if(ToolUtil.isEmpty(member)){
            isNew = "1";
        }else{
            //老用户传返回手机号
            mobile = member.getMemberPhone();
            nickname = member.getMemberName();
            avatar = member.getAvatar();
            isNew = "0";
        }
        GetWxInfoResponse response = new GetWxInfoResponse(openid, unionid, isNew, mobile, nickname, avatar, sessionKey);
        return response;
    }
}
