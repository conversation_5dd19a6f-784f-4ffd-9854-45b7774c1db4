package com.zksr.portal.service.mall;

import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.controller.mall.vo.SpuDetailRespVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityDetailRespVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityFgBgDetailVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageReqVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageRespVO;
import com.zksr.portal.controller.mall.vo.car.CarCountRespVO;
import com.zksr.portal.controller.mall.vo.car.CarPageRespVO;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.spu.SpuDetailActivityLabelVO;
import com.zksr.portal.controller.mall.vo.yh.YhBatchCountRespVO;
import com.zksr.portal.controller.mall.vo.yh.YhBatchListRespVO;
import com.zksr.portal.controller.mall.vo.yh.YhSupplierActivityVO;
import com.zksr.product.api.spu.dto.SkuUnitGroupDTO;
import com.zksr.product.api.spu.dto.SkuUnitGroupKeyDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.api.activity.vo.UnitSuperPriceVO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @time 2024/5/20
 * @desc 促销活动接口
 */
public interface IActivityService {

    /**
     * 渲染要货单促销数据
     */
    void renderYhList(BranchDTO branchDTO, YhBatchListRespVO respVO);

    /**
     * 渲染要货单金额计算
     */
    YhBatchCountRespVO renderYhCount(BranchDTO branchDTO, List<YhPageSupplierGroupItemVO> productList, YhBatchCountRespVO respVO);

    /**
     * 渲染购物车列表 促销列表
     * @param pageRespVO
     * @return 渲染购物车分页数据
     */
    CarPageRespVO renderCarList(BranchDTO branchDTO, CarPageRespVO pageRespVO);

    /**
     * 渲染购物车选中数据统计
     * @param branchDTO     门店
     * @param countRespVO   购物车选中数据
     * @return 渲染购物车选中数据
     */
    CarCountRespVO renderCarSelectTotal(BranchDTO branchDTO, CarCountRespVO countRespVO);

    /**
     * 通过spu 查询 spu 绑定的 促销活动
     * @param branchDTO     门店
     * @param productType   商品类型
     * @return Map<SpuId, Set<Activity>>
     */
    Map<Long, List<SupplierActivityDTO>> applySpuActivity(BranchDTO branchDTO, ProductType productType, List<ActivityVerifyItemDTO> verifyItemDTOS);
    /**
     * 通过spu 查询 spu 绑定的 促销活动
     * @param branchDTO     门店
     * @param productType   商品类型
     * @return Map<SpuId, Set<Activity>>
     */
    Map<Long, List<SupplierActivityDTO>> applySpuActivity(BranchDTO branchDTO, ProductType productType, Map<SkuUnitGroupKeyDTO, Set<SkuUnitGroupDTO>> spuList, List<ActivityVerifyItemDTO> verifyItemDTOS);

    /**
     * 通过spu 查询 spu 绑定的 促销活动
     * @param branchDTO     门店
     * @param productType   商品类型
     * @param spuList       SPU集合
     */
    Map<Long, List<SupplierActivityDTO>> applySpuActivity(BranchDTO branchDTO, ProductType productType, Map<SkuUnitGroupKeyDTO, Set<SkuUnitGroupDTO>> spuList);

    /**
     * 叠加促销活动验证优惠券
     * @param branch                    门店
     * @param items                     优惠券商品
     * @param activityVerifyItemDTOS    优惠券验证商品
     */
    void applyCouponItemPayAmt(BranchDTO branch, List<OrderValidItemDTO> items, ArrayList<ActivityVerifyItemDTO> activityVerifyItemDTOS);

    /**
     * 渲染全国商品列表促销活动调整
     * @param branch    门店
     * @param pageList  商品列表
     */
    void renderItemList(BranchDTO branch, ProductType productType, List<SkuPageRespVO> pageList);

    /**
     * 渲染商品详情返回数据, 提供主SKUID 所绑定的促销数据
     * @param branch        门店
     * @param productType   商品类型
     * @param detailReslut  详情返回VO
     */
    void renderItemDetail(BranchDTO branch, ProductType productType, SpuDetailRespVO detailReslut);

    /**
     * 获取买赠, 满赠促销详情
     * @param activityId    促销活动ID
     * @return  买满赠促销数据
     */
    ActivityFgBgDetailVO getFgOrBgActivityDetail(Long activityId);

    /**
     * 获取买赠, 满赠促销详情
     * @param activityId    活动ID
     * @param ruleIds       指定规则ID集合
     * @return  买满赠促销数据
     */
    ActivityFgBgDetailVO getActivityFgBgDetailVO(Long activityId, List<Long> ruleIds);

    /**
     * 获取入驻商促销活动集合
     */
    List<SupplierActivityDTO> getSupplierActivity(Long supplierId);

    /**
     * 获取入驻商促销活动集合 ( 不需要加载额外的规则 )
     */
    List<SupplierActivityDTO> getSupplierActivityNotExtraRule(Long supplierId);

    /**
     * 转换活动信息, 干掉一些计算数据, 只保留活动显示基础信息
     */
    List<SpuDetailActivityLabelVO> getSpuDetailActivityLabelVOS(List<SupplierActivityDTO> supplierActivityDTOS);

    List<ActivityItemPageRespVO> getItemsByActivityIds(ActivityItemPageReqVO pageReqVO);

    /**
     * 获取通用活动详情
     * @param activityId    促销活动ID
     * @return  促销详情数据
     */
    ActivityDetailRespVO getActivityDetail(Long activityId);
}
