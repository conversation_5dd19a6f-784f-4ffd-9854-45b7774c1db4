package com.zksr.portal.service.mall;

import com.zksr.portal.controller.mall.vo.branch.BranchReqVO;
import com.zksr.portal.controller.mall.vo.branch.BranchWxMerchantStateRespVO;

/**
 * @Description: 门店Service
 * @Author: liuxingyu
 * @Date: 2024/6/7 15:48
 */
public interface IMemBranchService {

    /**
     * @Description: 添加门店
     * @Author: liuxingyu
     * @Date: 2024/6/7 16:20
     */
    Long addBranch(BranchReqVO branchReqVO);

    /**
     * 获取门店微信商家助手认证信息
     *
     * @return 认证状态
     */
    BranchWxMerchantStateRespVO getWxMerchantAuthState(String openid);

    /**
     * 预录入数据 门店微信商家
     */
    void syncWxMerchant();

    /**
     * 保存商家助手认证openid
     *
     * @param openid 认证后的openid
     */
    void saveWxAuthOpenid(String openid);

    /**
     * @Description: 编辑门店
     * @Author: liyi
     * @Date: 2024/12/19 16:20
     */
    Long editBranch(BranchReqVO branchReqVO);
}
