package com.zksr.portal.service.impl.mall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.JsonUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.controller.mall.vo.SpuDetailRespVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityDetailRespVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityFgBgDetailVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageReqVO;
import com.zksr.portal.controller.mall.vo.activity.ActivityItemPageRespVO;
import com.zksr.portal.controller.mall.vo.car.*;
import com.zksr.portal.controller.mall.vo.prdt.ActivitySkuSearchReqVO;
import com.zksr.portal.controller.mall.vo.spu.SpuDetailActivityLabelVO;
import com.zksr.portal.controller.mall.vo.spu.SpuItemListReqVO;
import com.zksr.portal.controller.mall.vo.yh.YhBatchCountRespVO;
import com.zksr.portal.controller.mall.vo.yh.YhBatchListRespVO;
import com.zksr.portal.controller.mall.vo.yh.YhSupplierActivityVO;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.car.CarConvert;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.portal.convert.prdt.YhDataConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IActivityService;
import com.zksr.portal.service.mall.ISkuPriceService;
import com.zksr.portal.service.mall.ISkuService;
import com.zksr.portal.service.mall.ISpuItemInfoService;
import com.zksr.product.api.areaItem.AreaItemApi;
import com.zksr.product.api.areaItem.vo.ApiAreaItemPageReqVO;
import com.zksr.product.api.areaItem.vo.PrdtAreaItemPageRespVO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SkuUnitGroupDTO;
import com.zksr.product.api.spu.dto.SkuUnitGroupKeyDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.SupplierItemApi;
import com.zksr.product.api.supplierItem.vo.ApiSupplierItemPageReqVO;
import com.zksr.product.api.supplierItem.vo.PrdtSupplierItemPageRespVO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 促销活动
 * @date 2024/5/20 18:22
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class ActivityServiceImpl implements IActivityService {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisActivityService redisActivityService;

    @Autowired
    private ISkuPriceService skuPriceService;

    @Autowired
    private AreaItemApi areaItemApi;
    @Autowired
    private SupplierItemApi supplierItemApi;
    @Autowired
    @Lazy
    private ISkuService skuService;
    @Autowired
    private ProductContentApi productContentApi;
    @Autowired
    private RedisStockService redisStockService;

    @Override
    public void renderYhList(BranchDTO branchDTO, YhBatchListRespVO respVO) {
        List<ActivityVerifyItemDTO> allActivityVerifyItemList = new ArrayList<>();
        // 先转换成活动匹配商品
        for (YhPageSupplierGroupItemVO itemVO : respVO.getItemList()) {
            ActivityVerifyItemDTO verifyItemDTO = YhDataConvert.INSTANCE.buildActivityVerifyItemDTO(itemVO);
            verifyItemDTO.setType(ProductType.LOCAL.getType());
            // 计算应付金额
            verifyItemDTO.setAdequateList(new ArrayList<>());
            verifyItemDTO.setPayAmt(NumberUtil.toBigDecimal(verifyItemDTO.getNum()).multiply(verifyItemDTO.getItemPrice()));
            verifyItemDTO.setDiscountAmt(BigDecimal.ZERO);
            allActivityVerifyItemList.add(verifyItemDTO);
            itemVO.setActivityVerifyItemDto(verifyItemDTO);
        }
        List<YhPageSupplierGroupVO> groupVOList = new ArrayList<>();
        Map<Long, List<YhPageSupplierGroupItemVO>> supplierGroupItemMap = respVO.getItemList().stream().collect(Collectors.groupingBy(YhPageSupplierGroupItemVO::getSupplierId));
        for (Map.Entry<Long, List<YhPageSupplierGroupItemVO>> entry : supplierGroupItemMap.entrySet()) {
            Long supplierId = entry.getKey();
            YhPageSupplierGroupVO vo = new YhPageSupplierGroupVO();
            vo.setSupplierId(supplierId);
            List<YhPageSupplierGroupItemVO> valueList = entry.getValue();
            // 根据入驻商去匹配活动
            List<ActivityVerifyItemDTO> activityVerifyItemDTOS = valueList.stream().map(YhPageSupplierGroupItemVO::getActivityVerifyItemDto).collect(Collectors.toList());
            vo.setActivityVerifyItemDtoList(activityVerifyItemDTOS);
            // 获取入驻商促销活动
            List<SupplierActivityDTO> serviceSupplierActivityList = getSupplierActivity(supplierId);
            vo.setServiceSupplierActivityList(serviceSupplierActivityList);
            // 先处理秒杀和特价活动
            ActivityVerifyResultDTO verifyResultDTO = redisActivityService.calculateSkAndSpActivity(branchDTO, serviceSupplierActivityList, activityVerifyItemDTOS);
            vo.setVerifyResultDTO(verifyResultDTO);
            groupVOList.add(vo);
        }
        for (YhPageSupplierGroupVO supplierGroupVO : groupVOList) {
            // 根据入驻商去匹配活动
            List<ActivityVerifyItemDTO> activityVerifyItemDTOS = supplierGroupVO.getActivityVerifyItemDtoList();
            Long supplierId = supplierGroupVO.getSupplierId();
            // 获取入驻商促销活动
            List<SupplierActivityDTO> serviceSupplierActivityList = supplierGroupVO.getServiceSupplierActivityList();
            ActivityVerifyResultDTO verifyResultDTO = supplierGroupVO.getVerifyResultDTO();
            // 获取促销活动
            redisActivityService.calculateActivity(branchDTO, serviceSupplierActivityList, activityVerifyItemDTOS, allActivityVerifyItemList, supplierId, verifyResultDTO);
            // 全局促销
            // respVO.setActivityList(ActivityConvert.INSTANCE.convertActivityDTO(verifyResultDTO.getActivityList()));
            // 商品SKU 活动
            Map<String, ActivityVerifyItemDTO> verifyItemMap = verifyResultDTO.getVerifyItemList().stream().collect(Collectors.toMap(ActivityVerifyItemDTO::getUniqueKey, item -> item));
            // 使用线程安全的map
            Map<String, List<SupplierActivityDTO>> skuUnitMap = new HashMap<>();
            Map<SupplierActivityDTO, List<ActivityVerifyItemDTO>> activityProductRelation = verifyResultDTO.getActivityProductRelation();
            activityProductRelation.forEach((activity, itemList) -> {
                // 商品列表
                for (ActivityVerifyItemDTO itemDTO : itemList) {
                    if (!itemDTO.getSupplierId().equals(supplierId)) {
                        continue;
                    }
                    String uniqueKey = itemDTO.getUniqueKey();
                    if (!skuUnitMap.containsKey(uniqueKey)) {
                        skuUnitMap.put(uniqueKey, new ArrayList<>());
                    }
                    skuUnitMap.get(uniqueKey).add(activity);
                }
            });

            for (YhPageSupplierGroupItemVO groupItemVO : respVO.getItemList()) {
                String uniqueKey = StringUtils.format("{}_{}", groupItemVO.getItemId(), groupItemVO.getUnitSize());
                if (verifyItemMap.containsKey(uniqueKey)) {
                    ActivityVerifyItemDTO verifyItemDTO = verifyItemMap.get(uniqueKey);
                    groupItemVO.setPayAmt(verifyItemDTO.getPayAmt());
                    groupItemVO.setActivityPrice(verifyItemDTO.getActivityPrice());
                    groupItemVO.setActivityNum(verifyItemDTO.getActivityNum());
                    groupItemVO.setActivityPayAmt(verifyItemDTO.getActivityPayAmt());
                    groupItemVO.setActivityList(ActivityConvert.INSTANCE.convertActivityDTO(verifyItemDTO.getAdequateList()));
                }
                if (skuUnitMap.containsKey(uniqueKey)) {
                    groupItemVO.setSpuActivityLabelList(ActivityConvert.INSTANCE.convertCarSpuListActivityList(skuUnitMap.get(uniqueKey)));
                }
            }
        }
    }

    @Override
    public YhBatchCountRespVO renderYhCount(BranchDTO branchDTO, List<YhPageSupplierGroupItemVO> productList, YhBatchCountRespVO respVO) {
        List<YhSupplierActivityVO> result = new ArrayList<>();
        List<ActivityVerifyItemDTO> allActivityVerifyItemList = new ArrayList<>();
        // 先转换成活动匹配商品
        for (YhPageSupplierGroupItemVO itemVO : productList) {
            ActivityVerifyItemDTO verifyItemDTO = YhDataConvert.INSTANCE.buildActivityVerifyItemDTO(itemVO);
            verifyItemDTO.setType(ProductType.LOCAL.getType());
            verifyItemDTO.setDiscountAmt(BigDecimal.ZERO);
            // 计算应付金额
            verifyItemDTO.setAdequateList(new ArrayList<>());
            verifyItemDTO.setPayAmt(NumberUtil.toBigDecimal(verifyItemDTO.getNum()).multiply(verifyItemDTO.getItemPrice()));
            allActivityVerifyItemList.add(verifyItemDTO);
            itemVO.setActivityVerifyItemDto(verifyItemDTO);
        }
        List<YhPageSupplierGroupVO> groupVOList = new ArrayList<>();
        Map<Long, List<YhPageSupplierGroupItemVO>> supplierGroupItemMap = productList.stream().collect(Collectors.groupingBy(YhPageSupplierGroupItemVO::getSupplierId));
        for (Map.Entry<Long, List<YhPageSupplierGroupItemVO>> entry : supplierGroupItemMap.entrySet()) {
            Long supplierId = entry.getKey();
            YhPageSupplierGroupVO vo = new YhPageSupplierGroupVO();
            vo.setSupplierId(supplierId);
            List<YhPageSupplierGroupItemVO> valueList = entry.getValue();
            // 根据入驻商去匹配活动
            List<ActivityVerifyItemDTO> activityVerifyItemDTOS = valueList.stream().map(YhPageSupplierGroupItemVO::getActivityVerifyItemDto).collect(Collectors.toList());
            vo.setActivityVerifyItemDtoList(activityVerifyItemDTOS);
            // 获取入驻商促销活动
            List<SupplierActivityDTO> serviceSupplierActivityList = getSupplierActivity(supplierId);
            vo.setServiceSupplierActivityList(serviceSupplierActivityList);
            // 先处理秒杀和特价活动
            ActivityVerifyResultDTO verifyResultDTO = redisActivityService.calculateSkAndSpActivity(branchDTO, serviceSupplierActivityList, activityVerifyItemDTOS);
            vo.setVerifyResultDTO(verifyResultDTO);
            groupVOList.add(vo);
        }
        for (YhPageSupplierGroupVO supplierGroupVO : groupVOList) {
            // 根据入驻商去匹配活动
            List<ActivityVerifyItemDTO> activityVerifyItemDTOS = supplierGroupVO.getActivityVerifyItemDtoList();
            Long supplierId = supplierGroupVO.getSupplierId();
            // 获取入驻商促销活动
            List<SupplierActivityDTO> serviceSupplierActivityList = supplierGroupVO.getServiceSupplierActivityList();
            ActivityVerifyResultDTO verifyResultDTO = supplierGroupVO.getVerifyResultDTO();
            // 获取促销活动
            redisActivityService.calculateActivity(branchDTO, serviceSupplierActivityList, activityVerifyItemDTOS, allActivityVerifyItemList, supplierId, verifyResultDTO);
            // 获取所有共享活动
            List<ActivityLabelInfoVO> activityList = verifyResultDTO.getActivityList();
            // 商品独享活动
            List<ActivityLabelInfoVO> activityLabelInfoVOS = verifyResultDTO.getVerifyItemList().stream().map(ActivityVerifyItemDTO::getAdequateList).flatMap(Collection::stream).collect(Collectors.toList());
            activityList.addAll(activityLabelInfoVOS);
            if (activityList.isEmpty()) {
                continue;
            }
            YhSupplierActivityVO supplierActivityVO = new YhSupplierActivityVO(portalCacheService.getSupplierDTO(supplierId), activityList);
            result.add(supplierActivityVO);
            // 只需要减掉优惠金额就行了
            BigDecimal totalAmt = respVO.getTotalAmt();
            respVO.setTotalAmt(respVO.getTotalAmt().subtract(verifyResultDTO.getTotalCouponAmt()));
        }
        // 入驻商促销集合
        respVO.setSupplierActivityList(result);
        return respVO;
    }

    /**
     * 渲染购物车列表促销活动
     * @param pageRespVO
     * @return
     */
    @Override
    public CarPageRespVO renderCarList(BranchDTO branchDTO, CarPageRespVO pageRespVO) {
        List<CarPageRespVO.CarPageSupplierGroupVO> supplierGroupVOS = pageRespVO.getSupplierGroupList();
        // 预先处理数据
        List<ActivityVerifyItemDTO> allActivityVerifyItemList = new ArrayList<>();
        for (CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO : supplierGroupVOS) {
            List<ActivityVerifyItemDTO> activityVerifyItemDTOS = new ArrayList<>();
            BigDecimal totalAmt = BigDecimal.ZERO;
            for (CarPageSupplierGroupItemVO groupItemVO : supplierGroupVO.getItemList()) {
                AppCarIdDTO carId = groupItemVO.getCarId();
                ActivityVerifyItemDTO verifyItemDTO;
                if (groupItemVO.isSpuCombineProduct()) {
                    // 组合商品
                    SpuCombineDTO spuCombine = portalCacheService.getSpuCombineDTO(groupItemVO.getSpuCombineId());
                    verifyItemDTO = ActivityConvert.INSTANCE.buildVerifyItemDTO(carId, supplierGroupVO, groupItemVO, spuCombine);
                } else {
                    // 普通商品
                    SpuDTO spu = portalCacheService.getSpuDTO(carId.getSpuId());
                    verifyItemDTO = ActivityConvert.INSTANCE.buildVerifyItemDTO(carId, spu, groupItemVO);
                }
                verifyItemDTO.setDiscountAmt(BigDecimal.ZERO);
                // 计算应付金额
                verifyItemDTO.setAdequateList(new ArrayList<>());
                verifyItemDTO.setPayAmt(NumberUtil.toBigDecimal(verifyItemDTO.getNum()).multiply(verifyItemDTO.getItemPrice()));
                activityVerifyItemDTOS.add(verifyItemDTO);
                if (Objects.nonNull(groupItemVO.getSelected()) && groupItemVO.getSelected()) {
                    totalAmt = totalAmt.add(NumberUtil.toBigDecimal(groupItemVO.getProductNum()).multiply(groupItemVO.getMarkPrice()));
                }
            }
            allActivityVerifyItemList.addAll(activityVerifyItemDTOS);
            supplierGroupVO.setActivityVerifyItemDtoList(activityVerifyItemDTOS);
            supplierGroupVO.setTotalAmt(totalAmt);
            // 查询当前入驻商的活动
            List<SupplierActivityDTO> serviceSupplierActivityList = getSupplierActivity(supplierGroupVO.getSupplierId());
            supplierGroupVO.setServiceSupplierActivityList(serviceSupplierActivityList);
            // 先处理秒杀和特价活动
            ActivityVerifyResultDTO verifyResultDTO = redisActivityService.calculateSkAndSpActivity(branchDTO, serviceSupplierActivityList, activityVerifyItemDTOS);
            supplierGroupVO.setVerifyResultDTO(verifyResultDTO);
        }
        for (CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO : supplierGroupVOS) {
            List<SupplierActivityDTO> serviceSupplierActivityList = supplierGroupVO.getServiceSupplierActivityList();
            List<ActivityVerifyItemDTO> activityVerifyItemDtoList = supplierGroupVO.getActivityVerifyItemDtoList();
            Long supplierId = supplierGroupVO.getSupplierId();
            ActivityVerifyResultDTO verifyResultDTO = supplierGroupVO.getVerifyResultDTO();
            // 获取促销活动
            redisActivityService.calculateActivity(branchDTO, serviceSupplierActivityList, activityVerifyItemDtoList, allActivityVerifyItemList, supplierId, verifyResultDTO);
            // 渲染活动里面的赠品详情数据
            renderGiftInfo(verifyResultDTO);
            // 数据交接
            supplierGroupVO.setActivityList(ActivityConvert.INSTANCE.convertActivityDTO(verifyResultDTO.getActivityList()));
            // 商品SKU 活动
            Map<String, ActivityVerifyItemDTO> verifyItemMap = verifyResultDTO.getVerifyItemList().stream().collect(Collectors.toMap(ActivityVerifyItemDTO::getUniqueKey, item -> item));
            // 使用线程安全的map
            Map<String, List<SupplierActivityDTO>> skuUnitMap = new HashMap<>();
            Map<SupplierActivityDTO, List<ActivityVerifyItemDTO>> activityProductRelation = verifyResultDTO.getActivityProductRelation();
            activityProductRelation.forEach((activity, itemList) -> {
                // 商品列表
                for (ActivityVerifyItemDTO itemDTO : itemList) {
                    if (!supplierId.equals(itemDTO.getSupplierId())) {
                        continue;
                    }
                    String uniqueKey = itemDTO.getUniqueKey();
                    if (!skuUnitMap.containsKey(uniqueKey)) {
                        skuUnitMap.put(uniqueKey, new ArrayList<>());
                    }
                    skuUnitMap.get(uniqueKey).add(activity);
                }
            });

            for (CarPageSupplierGroupItemVO groupItemVO : supplierGroupVO.getItemList()) {
                String uniqueKey = groupItemVO.getCarId().getUniqueKey();
                if (verifyItemMap.containsKey(uniqueKey)) {
                    ActivityVerifyItemDTO verifyItemDTO = verifyItemMap.get(uniqueKey);
                    groupItemVO.setPayAmt(verifyItemDTO.getPayAmt());
                    groupItemVO.setActivityPrice(verifyItemDTO.getActivityPrice());
                    groupItemVO.setActivityNum(verifyItemDTO.getActivityNum());
                    groupItemVO.setActivityPayAmt(verifyItemDTO.getActivityPayAmt());
                    groupItemVO.setActivityList(ActivityConvert.INSTANCE.convertActivityDTO(verifyItemDTO.getAdequateList()));
                }
                if (skuUnitMap.containsKey(uniqueKey)) {
                    // 渲染促销活动素材
                    List<CarSpuActivityLabelVO> labelVOList = ActivityConvert.INSTANCE.convertCarSpuListActivityList(skuUnitMap.get(uniqueKey));
                    /*if (Objects.nonNull(labelVOList)) {
                        for (CarSpuActivityLabelVO labelVO : labelVOList) {
                            MaterialCacheVO material = portalCacheService.getMaterial(MaterialCacheVO.getActivityCacheKey(labelVO.getActivityId()));
                            if (StringUtils.isNotEmpty(material.getValidateMaterial())) {
                                groupItemVO.setMaterialUrl(material.getValidateMaterial());
                            }
                        }
                    }*/
                    groupItemVO.setSpuActivityLabelList(labelVOList);
                }
            }
            BigDecimal totalAmt = supplierGroupVO.getTotalAmt();
            supplierGroupVO.setTotalAmt(totalAmt.subtract(verifyResultDTO.getTotalCouponAmt()));
        }
        return pageRespVO;
    }

    @Override
    public CarCountRespVO renderCarSelectTotal(BranchDTO branchDTO, CarCountRespVO countRespVO) {
        List<CarSelectedItemVO> itemVOS = countRespVO.getSelectedCarId();
        ArrayList<ActivityVerifyItemDTO> activityVerifyItemDTOS = new ArrayList<>();
        for (CarSelectedItemVO itemVO : itemVOS) {
            ActivityVerifyItemDTO verifyItemDTO;
            if (itemVO.isSpuCombineProduct()) {
                // 组合商品
                SpuCombineDTO spuCombine = portalCacheService.getSpuCombineDTO(itemVO.getSpuCombineId());
                verifyItemDTO = ActivityConvert.INSTANCE.buildVerifyItemDTO(itemVO, spuCombine);
            } else {
                // 普通商品
                SpuDTO spu = portalCacheService.getSpuDTO(itemVO.getSpuId());
                verifyItemDTO = ActivityConvert.INSTANCE.buildVerifyItemDTO(itemVO, spu);
                // 设置单位转换比例
                if (UnitTypeEnum.M(verifyItemDTO.getUnitSize())) {
                    verifyItemDTO.setStockConvertRate(spu.getMidSize());
                } else if (UnitTypeEnum.L(verifyItemDTO.getUnitSize())) {
                    verifyItemDTO.setStockConvertRate(spu.getLargeSize());
                }
            }
            // 计算应付金额
            verifyItemDTO.setAdequateList(new ArrayList<>());
            verifyItemDTO.setPayAmt(NumberUtil.toBigDecimal(verifyItemDTO.getNum()).multiply(verifyItemDTO.getItemPrice()));
            verifyItemDTO.setDiscountAmt(BigDecimal.ZERO);
            activityVerifyItemDTOS.add(verifyItemDTO);
        }
        List<YhPageSupplierGroupVO> groupVOList = new ArrayList<>();
        Map<Long, List<ActivityVerifyItemDTO>> supplierMap = activityVerifyItemDTOS.stream().collect(Collectors.groupingBy(ActivityVerifyItemDTO::getSupplierId));
        for (Map.Entry<Long, List<ActivityVerifyItemDTO>> entry : supplierMap.entrySet()) {
            Long supplierId = entry.getKey();
            YhPageSupplierGroupVO vo = new YhPageSupplierGroupVO();
            vo.setSupplierId(supplierId);
            // 根据入驻商去匹配活动
            List<ActivityVerifyItemDTO> verifyItemDtoList = entry.getValue();
            vo.setActivityVerifyItemDtoList(verifyItemDtoList);
            // 获取入驻商促销活动
            List<SupplierActivityDTO> serviceSupplierActivityList = getSupplierActivity(supplierId);
            vo.setServiceSupplierActivityList(serviceSupplierActivityList);
            // 先处理秒杀和特价活动
            ActivityVerifyResultDTO verifyResultDTO = redisActivityService.calculateSkAndSpActivity(branchDTO, serviceSupplierActivityList, verifyItemDtoList);
            vo.setVerifyResultDTO(verifyResultDTO);
            groupVOList.add(vo);
        }
        // 购物车SKU
        Map<String, CarSelectedItemVO> skuMap = countRespVO.getSelectedCarId().stream().collect(Collectors.toMap(CarSelectedItemVO::getUniqueKey, item -> item));
        for (YhPageSupplierGroupVO supplierGroupVO : groupVOList) {
            List<ActivityVerifyItemDTO> verifyItemDtoList = supplierGroupVO.getActivityVerifyItemDtoList();
            Long supplierId = supplierGroupVO.getSupplierId();
            List<SupplierActivityDTO> serviceSupplierActivityList = supplierGroupVO.getServiceSupplierActivityList();
            // 获取促销活动
            ActivityVerifyResultDTO resultDTO = supplierGroupVO.getVerifyResultDTO();
            redisActivityService.calculateActivity(branchDTO, serviceSupplierActivityList, verifyItemDtoList, activityVerifyItemDTOS, supplierId, resultDTO);
            // 整合促销
            resultDTO.getActivityList().forEach(item -> countRespVO.getActivityList().addAll(buildCountActivityResp(item)));
            for (ActivityVerifyItemDTO verifyItemDTO : resultDTO.getVerifyItemList()) {
                if (!verifyItemDTO.getSupplierId().equals(supplierId)) {
                    continue;
                }
                CarSelectedItemVO selectedItem = skuMap.get(verifyItemDTO.getUniqueKey());
                if (Objects.nonNull(selectedItem)) {
                    if (Objects.nonNull(verifyItemDTO.getActivityNum()) && verifyItemDTO.getActivityNum() > NumberPool.INT_ZERO) {
                        // 促销超过可购买数量
                        if (verifyItemDTO.getActivityNum() < verifyItemDTO.getNum()) {
                            CarSelectedItemVO newItem = CarConvert.INSTANCE.copayCarSelectedItem(selectedItem);
                            newItem.setProductNum(verifyItemDTO.getActivityNum());
                            newItem.setSalePrice(verifyItemDTO.getActivityPrice());
                            newItem.setIsActivityspsk(NumberPool.INT_ONE);
                            countRespVO.getSelectedCarId().add(newItem);

                            selectedItem.setProductNum(selectedItem.getProductNum() - verifyItemDTO.getActivityNum());
                        } else if (verifyItemDTO.getActivityNum().equals(verifyItemDTO.getNum())) {
                            selectedItem.setIsActivityspsk(NumberPool.INT_ONE);
                            selectedItem.setSalePrice(verifyItemDTO.getActivityPrice());
                        }
                    }
                }
                // 绑定商品上的促销活动
                verifyItemDTO.getAdequateList().forEach(item -> countRespVO.getActivityList().addAll(buildCountActivityResp(item)));
            }
            // 只需要减掉优惠金额就行了
            countRespVO.setTotalAmt(countRespVO.getTotalAmt().subtract(resultDTO.getTotalCouponAmt()));
        }
        return countRespVO;
    }

    @Override
    public Map<Long, List<SupplierActivityDTO>> applySpuActivity(BranchDTO branchDTO, ProductType productType, Map<SkuUnitGroupKeyDTO, Set<SkuUnitGroupDTO>> spuList) {
        return applySpuActivity(branchDTO, productType, spuList, null);
    }

    @Override
    public Map<Long, List<SupplierActivityDTO>> applySpuActivity(BranchDTO branchDTO, ProductType productType, Map<SkuUnitGroupKeyDTO, Set<SkuUnitGroupDTO>> spuList, List<ActivityVerifyItemDTO> verifyItemDTOS) {
        List<ActivityVerifyItemDTO> activityVerifyItemDTOS = new ArrayList<>();
        if (Objects.nonNull(verifyItemDTOS)) {
            verifyItemDTOS.addAll(verifyItemDTOS);
        }
        spuList.forEach((groupKey, spuUnitList) -> {
            SpuDTO spuDTO = portalCacheService.getSpuDTO(groupKey.getSpuId());
            if (Objects.isNull(spuDTO)) {
                return;
            }
            for (SkuUnitGroupDTO skuUnit : spuUnitList) {
                ActivityVerifyItemDTO verifyItemDTO = ActivityVerifyItemDTO.builder()
                        .type(productType.getType())
                        .spuId(spuDTO.getSpuId())
                        .skuId(skuUnit.getSkuId())
                        .supplierId(spuDTO.getSupplierId())
                        .categoryId(spuDTO.getCatgoryId())
                        .brandId(spuDTO.getBrandId())
                        .num(NumberPool.INT_ZERO)
                        .adequateList(new ArrayList<>())
                        .itemPrice(BigDecimal.ZERO)
                        .build();
                if (UnitTypeEnum.M(verifyItemDTO.getUnitSize())) {
                    // 设置大单位
                    verifyItemDTO.setStockConvertRate(spuDTO.getMidSize());
                } else if (UnitTypeEnum.L(verifyItemDTO.getUnitSize())) {
                    // 设置大单位
                    verifyItemDTO.setStockConvertRate(spuDTO.getLargeSize());
                }
                activityVerifyItemDTOS.add(verifyItemDTO);
            }
        });
        return applySpuActivity(branchDTO, productType, activityVerifyItemDTOS);
    }

    @Override
    public Map<Long, List<SupplierActivityDTO>> applySpuActivity(BranchDTO branchDTO, ProductType productType, List<ActivityVerifyItemDTO> activityVerifyItemDTOS) {
        // 使用线程安全的map
        Map<Long, Set<SupplierActivityDTO>> activityDictMap = new ConcurrentHashMap<>();
        // 按照入驻商商品进行分组验证
        Map<Long, List<ActivityVerifyItemDTO>> supplierMap = activityVerifyItemDTOS.stream().collect(Collectors.groupingBy(ActivityVerifyItemDTO::getSupplierId));
        // 拆分入驻商, 使用多线程并发处理, 使用线程安全结果返回, 因为是集群环境, redis 也可以支持多线程操作
        ListUtil.partition(ListUtil.toList(supplierMap.keySet()), NumberPool.INT_ONE).stream().parallel().forEach(supplierIdList ->{
            Long supplierId = supplierIdList.stream().findFirst().get();
            List<SupplierActivityDTO> serviceSupplierActivityList = getSupplierActivity(supplierId);
            // 获取促销活动
            Map<SupplierActivityDTO, List<ActivityVerifyItemDTO>> resultDTO = redisActivityService.calculateActivityBind(branchDTO, serviceSupplierActivityList, activityVerifyItemDTOS);
            for (SupplierActivityDTO activityDTO : resultDTO.keySet()) {
                // 商品列表
                List<ActivityVerifyItemDTO> verifyItemDTOS = resultDTO.get(activityDTO);
                for (ActivityVerifyItemDTO itemDTO : verifyItemDTOS) {
                    Long spuId = itemDTO.getSpuId();
                    if (!activityDictMap.containsKey(spuId)) {
                        activityDictMap.put(spuId, new HashSet<>());
                    }
                    activityDictMap.get(spuId).add(activityDTO);
                }
            }
        });
        // 转换集合
        Map<Long, List<SupplierActivityDTO>> result = new HashMap<>();
        activityDictMap.forEach((key, set) -> {
            result.put(key, ListUtil.toList(set));
        });
        return result;
    }


    @Override
    public void applyCouponItemPayAmt(BranchDTO branch, List<OrderValidItemDTO> items, ArrayList<ActivityVerifyItemDTO> activityVerifyItemDTOS) {
        Map<Long, List<ActivityVerifyItemDTO>> supplierMap = activityVerifyItemDTOS.stream().collect(Collectors.groupingBy(ActivityVerifyItemDTO::getSupplierId));
        List<YhPageSupplierGroupVO> groupVOList = new ArrayList<>();
        for (Map.Entry<Long, List<ActivityVerifyItemDTO>> entry : supplierMap.entrySet()) {
            Long supplierId = entry.getKey();
            YhPageSupplierGroupVO vo = new YhPageSupplierGroupVO();
            vo.setSupplierId(supplierId);
            // 根据入驻商去匹配活动
            List<ActivityVerifyItemDTO> verifyItemDTOList = entry.getValue();
            vo.setActivityVerifyItemDtoList(verifyItemDTOList);
            // 获取入驻商促销活动
            List<SupplierActivityDTO> serviceSupplierActivityList = getSupplierActivity(supplierId);
            vo.setServiceSupplierActivityList(serviceSupplierActivityList);
            // 先处理秒杀和特价活动
            ActivityVerifyResultDTO verifyResultDTO = redisActivityService.calculateSkAndSpActivity(branch, serviceSupplierActivityList, verifyItemDTOList);
            vo.setVerifyResultDTO(verifyResultDTO);
            groupVOList.add(vo);
        }
        for (YhPageSupplierGroupVO supplierGroupVO : groupVOList) {
            List<ActivityVerifyItemDTO> verifyItemDTOList = supplierGroupVO.getActivityVerifyItemDtoList();
            Long supplierId = supplierGroupVO.getSupplierId();
            List<SupplierActivityDTO> serviceSupplierActivityList = supplierGroupVO.getServiceSupplierActivityList();
            // 获取促销活动
            ActivityVerifyResultDTO activityVerifyResultDTO = supplierGroupVO.getVerifyResultDTO();
            redisActivityService.calculateActivity(branch, serviceSupplierActivityList, verifyItemDTOList, activityVerifyItemDTOS, supplierId, activityVerifyResultDTO);
            Map<String, ActivityVerifyItemDTO> verifyItemDTOMap = activityVerifyResultDTO.getVerifyItemList().stream().collect(Collectors.toMap(ActivityVerifyItemDTO::getUniqueKey, item -> item));
            for (OrderValidItemDTO item : items) {
                if (!item.getSupplierId().equals(supplierId)) {
                    continue;
                }
                if (verifyItemDTOMap.containsKey(item.getUniqueKey())) {
                    ActivityVerifyItemDTO verifyItemDTO = verifyItemDTOMap.get(item.getUniqueKey());
                    // 重新计算整份金额
                    item.setAmt(verifyItemDTO.getPayAmt());
                }
            }
        }
    }

    @Override
    public void renderItemList(BranchDTO branch, ProductType productType, List<SkuPageRespVO> pageItemList) {
        // 没有门店就不做渲染
        if (Objects.isNull(branch)) {
            return;
        }
        pageItemList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getSupplierId)).forEach((supplierId, supplierProductList) -> {
            // 获取入驻商促销集合
            List<SupplierActivityDTO> supplierActivityList = this.getSupplierActivity(supplierId);
            // 开始轮询入驻商商品处理
            supplierProductList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getItemType)).forEach((itemType, itemList) -> {
                // 商品内容处理
                ISpuItemInfoService spuItemInfoService = spuItemInfoService(itemType);
                try {
                    spuItemInfoService.renderActivityItemList(branch, itemList, supplierActivityList);
                } catch (Exception e) {
                    log.error("渲染促销数据异常", e);
                }
            });
        });
    }

    @Override
    public void renderItemDetail(BranchDTO branch, ProductType productType, SpuDetailRespVO detailReslut) {
        // 没有门店就不做渲染
        if (Objects.isNull(branch)) {
            return;
        }
        // 获取商品内容渲染
        ISpuItemInfoService spuItemInfoService = spuItemInfoService(detailReslut.getItemType());
        spuItemInfoService.renderActivityItemDetail(branch, detailReslut);
    }

    @Override
    public ActivityFgBgDetailVO getFgOrBgActivityDetail(Long activityId) {
        return getActivityFgBgDetailVO(activityId, null);
    }

    @Override
    public ActivityDetailRespVO getActivityDetail(Long activityId) {
        PrmActivityDTO activityDto = portalCacheService.getActivityDto(activityId);
        ActivityDetailRespVO activityDetailRespVO = ActivityConvert.INSTANCE.convertRespVO(activityDto);
        // 买赠
        List<ActivityFgBgDetailVO.Gift> list = new ArrayList<>();
        if (PrmNoEnum.isBg(activityDetailRespVO.getPrmNo())) {
            List<BgRuleDTO> ruleList = portalCacheService.getActivityBgRuleList(activityId);
            this.renderGiveInfo(ruleList);
            activityDetailRespVO.setRules(new ArrayList<>(ruleList));
        }
        // 满赠
        if (PrmNoEnum.isFg(activityDetailRespVO.getPrmNo())) {
            List<FgRuleDTO> ruleList = portalCacheService.getActivityFgRuleList(activityId);
            this.renderGiveInfo(ruleList);
            activityDetailRespVO.setRules(new ArrayList<>(ruleList));
        }
        // 满减
        if (PrmNoEnum.isFd(activityDetailRespVO.getPrmNo())) {
            activityDetailRespVO.setRules(new ArrayList<>(portalCacheService.getActivityRuleDto(activityId, activityDto.getPrmNo())));
        }
        List<CatgoryDTO> applyList = portalCacheService.getCatgoryBySysCode(activityDto.getSysCode());
        Map<Long, CatgoryDTO> catgoryMap = applyList.stream().collect(Collectors.toMap(CatgoryDTO::getCatgoryId, item -> item));
        // 获取范围
        if (Objects.nonNull(activityDto.getSpuScope())) {
            List<ActivitySpuScopeDTO> activitySpuScopeList = portalCacheService.getActivitySpuScopeList(activityId);
            try {
                activitySpuScopeList.forEach(item -> {
                    switch (ActivitySpuScopeEnum.formValue(item.getApplyType())) {
                        case SUPPLIER:
                            item.setApplyName(portalCacheService.getSupplierDTO(item.getApplyId()).getSupplierName());
                            break;
                        case CLASS:
                            item.setApplyName(catgoryMap.get(item.getApplyId()).getCatgoryName());
                            break;
                        case BRAND:
                            item.setApplyName(portalCacheService.getBrandDTO(item.getApplyId()).getBrandName());
                            break;
                        case SKU:
                            item.setApplyName(portalCacheService.getSpuDTO(item.getSpuId()).getSpuName());
                            break;
                        default:
                            break;
                    }
                });
            } catch (Exception e) {
                log.error("渲染商品适应范围异常", e);
            }
            activityDetailRespVO.setSpuScopeList(activitySpuScopeList);
        }
        return activityDetailRespVO;
    }

    /**
     * 获取买赠满赠促销活动详情
     * @param activityId    活动ID
     * @param ruleIds       指定规则ID集合
     * @return
     */
    @Override
    public ActivityFgBgDetailVO getActivityFgBgDetailVO(Long activityId, List<Long> ruleIds) {
        PrmActivityDTO activityDto = portalCacheService.getActivityDto(activityId);
        List<ActivityFgBgDetailVO.Gift> list = new ArrayList<>();
        if (PrmNoEnum.isFg(activityDto.getPrmNo())) {
            Set<FgRuleDTO> fgRuleDTOS = portalCacheService.getActivityRuleDto(activityId, activityDto.getPrmNo());
            list.addAll(ActivityConvert.INSTANCE.convertFgGiftRule(fgRuleDTOS));
        }
        if (PrmNoEnum.isBg(activityDto.getPrmNo())) {
            Set<BgRuleDTO> fgRuleDTOS = portalCacheService.getActivityRuleDto(activityId, activityDto.getPrmNo());
            list.addAll(ActivityConvert.INSTANCE.convertBgGiftRule(fgRuleDTOS));
        }
        // 过滤其他规则, 只查询指定规则
        if (Objects.nonNull(ruleIds) && !ruleIds.isEmpty()) {
            HashSet<Long> tempSet = new HashSet<>(ruleIds);
            list = list.stream().filter(item -> tempSet.contains(item.getRuleId())).collect(Collectors.toList());
        }
        // 加载买赠满赠活动赠品信息
        for (ActivityFgBgDetailVO.Gift gift : list) {
            if (NumberPool.INT_ZERO == gift.getGiftType()) {
                // SKU
                gift.setGiftObj(getProductGift(gift));
            } else {
                // 优惠券
                gift.setGiftObj(portalCacheService.getCouponTemplate(gift.getCouponTemplateId()));
            }
        }
        // 转换数据
        ActivityFgBgDetailVO activityFgBgDetailVO = ActivityConvert.INSTANCE.convertActivityFgBgDetailVO(activityDto);
        // 按照条件分组
        Map<BigDecimal, List<ActivityFgBgDetailVO.Gift>> conditionMap = list.stream().collect(Collectors.groupingBy(ActivityFgBgDetailVO.Gift::getFullValue));
        conditionMap.forEach((condition, giftList) -> {
            ActivityFgBgDetailVO.Rule rule = new ActivityFgBgDetailVO.Rule();
            rule.setFullValue(condition);
            ActivityFgBgDetailVO.Gift gift = giftList.get(0);
            //设置满赠信息
            if(ToolUtil.isNotEmpty(gift)){
                rule.setGiftGroupType(gift.getGiftGroupType());
                rule.setBuySkuNum(gift.getBuySkuNum());
                rule.setGiftSkuUnitQty(gift.getGiftSkuUnitQty());
            }
            rule.setGiftList(giftList);
            activityFgBgDetailVO.getRuleList().add(rule);
        });
        // 获取范围
        if (Objects.nonNull(activityDto.getSpuScope())) {
            List<ActivitySpuScopeDTO> activitySpuScopeList = portalCacheService.getActivitySpuScopeList(activityId);
            Set<Long> applySet = activitySpuScopeList.stream().map(ActivitySpuScopeDTO::getApplyId).collect(Collectors.toSet());
            if (ActivitySpuScopeEnum.CLASS.getScope().equals(activityDto.getSpuScope())) {
                List<CatgoryDTO> applyList = portalCacheService.getCatgoryBySysCode(activityDto.getSysCode());
                if (Objects.nonNull(applyList)) {
                    activityFgBgDetailVO.setScopeAsName(applyList.stream().filter(item -> applySet.contains(item.getCatgoryId())).map(CatgoryDTO::getCatgoryName).collect(Collectors.toList()));
                }
            } else if (ActivitySpuScopeEnum.BRAND.getScope().equals(activityDto.getSpuScope())) {
                activityFgBgDetailVO.setScopeAsName(applySet.stream().map(portalCacheService::getBrandDTO).map(BrandDTO::getBrandName).collect(Collectors.toList()));
            } else if (ActivitySpuScopeEnum.SKU.getScope().equals(activityDto.getSpuScope())) {
                activityFgBgDetailVO.setScopeAsName(
                        applySet.stream()
                                .parallel()
                                .map(portalCacheService::getSkuDTO)
                                .map(SkuDTO::getSpuId)
                                .filter(Objects::nonNull)
                                .distinct()
                                .map(portalCacheService::getSpuDTO)
                                .map(SpuDTO::getSpuName)
                                .collect(Collectors.toList()));
            }
        }
        return activityFgBgDetailVO;
    }

    @Override
    public List<ActivityItemPageRespVO> getItemsByActivityIds(ActivityItemPageReqVO pageReqVO) {
        ProductContentPageReqDTO pageReqDTO = new ProductContentPageReqDTO();
        pageReqDTO.setPageNo(pageReqVO.getPageNo());
        pageReqDTO.setPageSize(pageReqVO.getPageSize());
        pageReqDTO.setSysCode(MallSecurityUtils.getLoginMember().getSysCode());
        pageReqDTO.setAreaId(MallSecurityUtils.getLoginMember().getAreaId());
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getLoginMember().getBranchId());
        if (branchDTO != null) {
            pageReqDTO.setAreaId(branchDTO.getAreaId());
            pageReqDTO.setGroupId(branchDTO.getGroupId());
            pageReqDTO.setChannelId(branchDTO.getChannelId());
            pageReqDTO.setSysCode(branchDTO.getSysCode());
        }

        return pageReqVO.getActivityIds().stream().map(activityId -> {
            ActivitySkuSearchReqVO skuSearchReqVO = ProductConvert.INSTANCE.convertActivitySkuSearchReqVO(pageReqDTO);
            skuSearchReqVO.setActivityId(activityId);
            skuSearchReqVO.setSysSource(pageReqVO.getSysSource());
            PageResult<SkuPageRespVO> respVOPageResult = skuService.searchActivitySpuDetailList(skuSearchReqVO);
            if (ObjectUtil.isEmpty(respVOPageResult.getList())) {
                return null;
            }
            PrmActivityDTO activityDto =portalCacheService.getActivityDto(activityId);
            return ActivityItemPageRespVO
                    .builder()
                    .items(respVOPageResult.getList())
                    .activityId(activityId)
                    .activityMemo(activityDto.getMemo())
                    .total(respVOPageResult.getTotal())
                    .build();
        }).filter(ObjectUtil::isNotEmpty).collect(Collectors.toList());
    }

    /**
     * 根據skuIds和areaId查詢城市上架商品
     * @param skuIds
     * @param areaId
     * @return Map<skuId, Set<areaItemId>>
     */
    private Map<Long, Set<Long>> getAreaItemIdBySkuIdsAndAreaId(List<Long> skuIds, Long areaId) {
        ApiAreaItemPageReqVO apiAreaItemPageReqVO = new ApiAreaItemPageReqVO();
        apiAreaItemPageReqVO.setSkuIdList(skuIds);
        apiAreaItemPageReqVO.setAreaId(areaId);
        apiAreaItemPageReqVO.setShelfStatus(Integer.valueOf(ShelfStatus.ON.getShelfStatus()));
        apiAreaItemPageReqVO.setPageNo(1);
        apiAreaItemPageReqVO.setPageSize(-1);
        CommonResult<PageResult<PrdtAreaItemPageRespVO>> result = areaItemApi.getAreaItemPageByApi(apiAreaItemPageReqVO);
        Map<Long, Set<Long>> resultMap = Maps.newHashMap();
        if (result != null && result.isSuccess() && result.getData() != null) {
            result.getData().getList().forEach(areaItemDTO -> {
                resultMap.computeIfAbsent(areaItemDTO.getSkuId(), k -> Sets.newHashSet()).add(areaItemDTO.getAreaItemId());
            });
        }
        return resultMap;
    }

    /**
     * 根據skuIds查詢全國上架商品
     * @param skuIds
     * @return Map<skuId, Set<supplierItemId>>
     */
    private Map<Long, Set<Long>> getSupplierItemBySkuIds(List<Long> skuIds) {
        ApiSupplierItemPageReqVO apiSupplierItemPageReqVO = new ApiSupplierItemPageReqVO();
        apiSupplierItemPageReqVO.setSkuIdList(skuIds);
        apiSupplierItemPageReqVO.setShelfStatus(Integer.valueOf(ShelfStatus.ON.getShelfStatus()));
        apiSupplierItemPageReqVO.setPageNo(1);
        apiSupplierItemPageReqVO.setPageSize(-1);
        CommonResult<PageResult<PrdtSupplierItemPageRespVO>> result = supplierItemApi.getSupplierItemPageByApi(apiSupplierItemPageReqVO);
        Map<Long, Set<Long>> resultMap = Maps.newHashMap();
        if (result != null && result.isSuccess() && result.getData() != null) {
            result.getData().getList().forEach(supplierItemDTO -> {
                resultMap.computeIfAbsent(supplierItemDTO.getSkuId(), k -> Sets.newHashSet()).add(supplierItemDTO.getSupplierItemId());
            });
        }
        return resultMap;
    }

    /**
     * 根據上架商品id查詢商品信息
     * @param itemIds
     * @return
     */
    private Map<Long, List<SkuPageRespVO>> getSkuByItemIds(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Maps.newHashMap();
        }
        SpuItemListReqVO reqVO = new SpuItemListReqVO();
        reqVO.setItemIds(itemIds);
        List<SkuPageRespVO> skuPageRespVOS = skuService.spuItemListDetailList(reqVO);
        if (CollectionUtils.isEmpty(skuPageRespVOS)) {
            return Maps.newHashMap();
        }
        return skuPageRespVOS.stream().collect(Collectors.groupingBy(SkuPageRespVO::getSkuId));
    }

    /**
     * 渲染命中预选活动赠品信息
     * @param verifyResultDTO
     */
    private void renderGiftInfo(ActivityVerifyResultDTO verifyResultDTO) {
        ArrayList<FgRuleDTO> fgRuleDTOS = new ArrayList<>();
        ArrayList<BgRuleDTO> bgRuleDTOS = new ArrayList<>();

        List<ActivityLabelInfoVO> labelInfoVOS = new ArrayList<>();
        for (ActivityVerifyItemDTO item : verifyResultDTO.getVerifyItemList()) {
            labelInfoVOS.addAll(item.getAdequateList());
        }
        labelInfoVOS.addAll(verifyResultDTO.getActivityList());
        labelInfoVOS = labelInfoVOS.stream().filter(Objects::nonNull).collect(Collectors.toList());

        for (ActivityLabelInfoVO activityLabelInfoVO : labelInfoVOS) {
            String prmNo = activityLabelInfoVO.getActivity().getPrmNo();
            List<ActivityFgBgDetailVO.Gift> giftList = new ArrayList<>();
            // 满赠买赠渲染赠品信息
            if (PrmNoEnum.isFg(prmNo)) {
                fgRuleDTOS.addAll(activityLabelInfoVO.getAdequateRole());
                if (Objects.nonNull(activityLabelInfoVO.getBetterRole())) {
                    fgRuleDTOS.add((FgRuleDTO) activityLabelInfoVO.getBetterRole());
                }
            }
            if (PrmNoEnum.isBg(prmNo)) {
                bgRuleDTOS.addAll(activityLabelInfoVO.getAdequateRole());
                if (Objects.nonNull(activityLabelInfoVO.getBetterRole())) {
                    bgRuleDTOS.add((BgRuleDTO) activityLabelInfoVO.getBetterRole());
                }
            }
        }
        this.renderGiveInfo(fgRuleDTOS);
        this.renderGiveInfo(bgRuleDTOS);
    }


    private ActivityFgBgDetailVO.Item getProductGift(ActivityFgBgDetailVO.Gift gift) {
        SkuDTO skuDTO = portalCacheService.getSkuDTO(gift.getSkuId());
        SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
        ActivityFgBgDetailVO.Item item = ActivityFgBgDetailVO.Item.builder()
                .spuName(spuDTO.getSpuName())
                .skuName(PropertyAndValDTO.getProperties(skuDTO.getProperties()))
                .thumb(spuDTO.getThumb())
                .markPrice(skuDTO.getMarkPrice(gift.getGiftSkuUnitType()))
                .build();
        return item;
    }

    @Override
    public List<SpuDetailActivityLabelVO> getSpuDetailActivityLabelVOS(List<SupplierActivityDTO> supplierActivityDTOS) {
        if (Objects.isNull(supplierActivityDTOS)) {
            return null;
        }
        List<SpuDetailActivityLabelVO> activityLabelVOS = new ArrayList<>();
        for (SupplierActivityDTO supplierActivityDTO : supplierActivityDTOS) {
            SpuDetailActivityLabelVO activityLabelVO = ActivityConvert.INSTANCE.convertSpuDetailActivityVO(supplierActivityDTO);
            activityLabelVOS.add(activityLabelVO);
        }
        return activityLabelVOS;
    }

    private List<CarCountRespVO.Activity> buildCountActivityResp(ActivityLabelInfoVO infoVO) {
        ActivityDTO activityDTO = infoVO.getActivity();
        List<CarCountRespVO.Activity> result = new ArrayList<>();
        for (Object object : infoVO.getAdequateRole()) {
            CarCountRespVO.Activity activity = new CarCountRespVO.Activity();
            if (PrmNoEnum.isSk(activityDTO.getPrmNo())) {
                SkRuleDTO rule = getRule(object);
                activity.setActivityRuleId(rule.getSkRuleId());
            } else if (PrmNoEnum.isSp(activityDTO.getPrmNo())) {
                SpRuleDTO rule = getRule(object);
                activity.setActivityRuleId(rule.getSpRuleId());
            } else if (PrmNoEnum.isFg(activityDTO.getPrmNo())) {
                FgRuleDTO rule = getRule(object);
                activity.setActivityRuleId(rule.getFgRuleId());
            } else if (PrmNoEnum.isBg(activityDTO.getPrmNo())) {
                BgRuleDTO rule = getRule(object);
                activity.setActivityRuleId(rule.getBgRuleId());
            } else if (PrmNoEnum.isFd(activityDTO.getPrmNo())) {
                FdRuleDTO rule = getRule(object);
                activity.setActivityRuleId(rule.getFdRuleId());
            }
            activity.setActivityId(activityDTO.getActivityId());
            activity.setActivityType(activityDTO.getPrmNo());
            result.add(activity);
        }
        return result;
    }


    public <T> T getRule(Object o) {
        return (T) o;
    }

    /**
     * 加载活动秒杀, 特价等数据量较大的促销活动拆分数据
     * @param serviceSupplierActivityList
     */
    private void buildActivityRule(List<SupplierActivityDTO> serviceSupplierActivityList) {
        for (SupplierActivityDTO activityDTO : serviceSupplierActivityList) {
            Long activityId = activityDTO.getActivityId();
            List<ActivitySpuScopeDTO> scopeList = portalCacheService.getActivitySpuScopeList(activityId);
            if (Objects.nonNull(scopeList)) {
                activityDTO.setSpuScopeList(scopeList.stream().map(ActivitySpuScopeDTO::getApplyId).collect(Collectors.toSet()));
                activityDTO.setActivitySpuScopeList(scopeList);
            }
            if (PrmNoEnum.isSk(activityDTO.getPrmNo())) {
                activityDTO.setSkRules(portalCacheService.getActivitySkRuleList(activityId));
            }
            if (PrmNoEnum.isSp(activityDTO.getPrmNo())) {
                activityDTO.setSpRules(portalCacheService.getActivitySpRuleList(activityId));
            }
            if (PrmNoEnum.isBg(activityDTO.getPrmNo())) {
                activityDTO.setBgRules(portalCacheService.getActivityBgRuleList(activityId));
            }
            if (PrmNoEnum.isFg(activityDTO.getPrmNo())) {
                activityDTO.setFgRules(portalCacheService.getActivityFgRuleList(activityId));
            }
            // 设置活动入驻商ID列表
            List<ActivitySupplierScopeDTO> supplierScopeList = portalCacheService.getActivitySupplierScopeList(activityId);
            if (ToolUtil.isNotEmpty(supplierScopeList)) {
                activityDTO.setSupplierIdList(supplierScopeList.stream().filter(Objects::nonNull).map(ActivitySupplierScopeDTO::getSupplierId).filter(Objects::nonNull).collect(Collectors.toList()));
            } else {
                // 兼容旧数据
                activityDTO.setSupplierIdList(Lists.newArrayList(activityDTO.getSupplierId()));
            }
        }
    }

    /**
     * 渲染买赠, 满赠赠品信息
     */
    public void renderGiveInfo(List<? extends GiveRuleDTO> giveRuleDTOS) {
        for (GiveRuleDTO ruleDTO : giveRuleDTOS) {
            ActivityFgBgDetailVO.Gift gift = ActivityConvert.INSTANCE.convertGiftRule(ruleDTO);
            if (NumberPool.INT_ZERO == ruleDTO.getGiftType()) {
                // SKU
                ruleDTO.setGiftObj(getProductGift(gift));
            } else {
                // 优惠券
                ruleDTO.setGiftObj(portalCacheService.getCouponTemplate(gift.getCouponTemplateId()));
            }
        }
    }

    /**
     * 获取渲染完整的入驻商促销活动
     * @param supplierId    入驻商ID
     * @return  入驻商促销集合
     */
    @Override
    public List<SupplierActivityDTO> getSupplierActivity(Long supplierId) {
        List<SupplierActivityDTO> supplierActivity = portalCacheService.getSupplierActivity(supplierId);
        log.info(" {}入驻商绑定的活动数据,{}",supplierId, JsonUtils.toJsonString(supplierActivity));
        // 加载比较的大的key促销活动组成数据
        buildActivityRule(supplierActivity);
        return supplierActivity;
    }

    /**
     * 获取渲染完整的入驻商促销活动
     * @param supplierId    入驻商ID
     * @return  入驻商促销集合
     */
    @Override
    public List<SupplierActivityDTO> getSupplierActivityNotExtraRule(Long supplierId) {
        List<SupplierActivityDTO> supplierActivity = portalCacheService.getSupplierActivity(supplierId);
        return supplierActivity;
    }


    @Autowired
    @Qualifier("spuCombineService")
    private ISpuItemInfoService spuCombineService;

    @Autowired
    @Qualifier("spuNormalService")
    private ISpuItemInfoService spuNormalService;

    /**
     * @param itemType  0-普通商品, 1-组合商品
     */
    private ISpuItemInfoService spuItemInfoService(Integer itemType) {
        // 直接通过条件判断最高效获取具体逻辑 service
        if (itemType == 1) {
            return spuCombineService;
        }
        return spuNormalService;
    }
}
