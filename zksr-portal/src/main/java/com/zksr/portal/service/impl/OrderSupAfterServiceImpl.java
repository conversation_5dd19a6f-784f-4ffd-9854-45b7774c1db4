package com.zksr.portal.service.impl;

import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.portal.service.IOrderSupAfterService;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.order.vo.AfterApproveDtlEditVO;
import com.zksr.common.core.domain.vo.openapi.TrdSupAfterDtlRequest;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.zksr.common.core.web.pojo.CommonResult.success;

@Service
public class OrderSupAfterServiceImpl implements IOrderSupAfterService {

    @Resource
    private AfterApi afterApi;
    @Override
    @Transactional
    public CommonResult saveOrderAfter(TrdSupAfterDtlRequest request) {
        AfterApproveDtlEditVO dtlEditVO = afterApi.getAfterDtlByDtlId(request);
        if (ObjectUtils.isNotEmpty(dtlEditVO)){
            afterApi.approveAfterReturn(dtlEditVO);
            afterApi.approveAfterRefund(dtlEditVO);
        }
        return success(true);
    }
}
