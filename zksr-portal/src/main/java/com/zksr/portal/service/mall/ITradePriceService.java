package com.zksr.portal.service.mall;

import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.trade.api.order.vo.*;

public interface ITradePriceService {

    /**
     * 价格计算
     * @param calculateRequest
     * @return
     */
    public TradePriceCalculateResp calculatePrice(TradePriceCalculateRequest calculateRequest);

    /**
     * 订单明细分成比例计算
     * @param dtlVo
     * @return
     */
    public TrdSupplierOrderSettleSaveVO calculateRateAmt(BranchDTO branchDto, TrdSupplierOrderDtlSaveVO dtlVo,
                                                         TrdOrderSaveVO orderSaveVO, Colonel<PERSON><PERSON> colonel, <PERSON><PERSON><PERSON> pcolonel, String profitModel, PrdtSkuSaleTotalRateVO skuSaleTotalRateVO);


    /**
     * 订单赠品分摊计算
     * @param activityId 活动ID
     * @param remoteSaveOrderVO  订单数据
     */
    public void orderGiftShareCalculate(Long activityId, RemoteSaveOrderVO remoteSaveOrderVO);

    /**
     * 订单明细SKU平均最小单位单价计算
     * @param remoteSaveOrderVO
     */
    public void calculateOrderDtlSkuAvgPrice(RemoteSaveOrderVO remoteSaveOrderVO);
}
