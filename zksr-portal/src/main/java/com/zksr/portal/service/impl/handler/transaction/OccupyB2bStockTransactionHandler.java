package com.zksr.portal.service.impl.handler.transaction;

import com.zksr.common.core.enums.SupplierNegativeStockEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.transaction.TransactionHandler;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderDtlSaveVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@SuppressWarnings("rawtypes")
@Slf4j
public class OccupyB2bStockTransactionHandler implements TransactionHandler {
    private final RedisStockService redisStockService = SpringUtils.getBean(RedisStockService.class);
    private final IPortalCacheService portalCacheService = SpringUtils.getBean(IPortalCacheService.class);
    private final RemoteSaveOrderVO orderVo;
    private final List<TrdSupplierOrderDtlSaveVO> supplierItems = new ArrayList<>();

    public OccupyB2bStockTransactionHandler(RemoteSaveOrderVO orderVo) {
        this.orderVo = orderVo;
    }

    @Override
    public void doBusiness() {
        try {
            orderVo.getSupplierOrderDtlSaveVOS().forEach(supplierItem -> {
                Integer isNegativeStock = portalCacheService.getSupplierDTO(supplierItem.getSupplierId()).getIsNegativeStock();
                if (SupplierNegativeStockEnum.NEGATIVE_STOCK.getCode().equals(isNegativeStock)) {
                    // 支持负库存下单
                    redisStockService.incrSkuOccupiedQtySafe(supplierItem.getSkuId(), supplierItem.getTotalNum(), true);
                } else {
                    // 不支持负库存下单
                    boolean result = redisStockService.incrSkuOccupiedQtySafe(supplierItem.getSkuId(), supplierItem.getTotalNum(), false);
                    if (!result) {
                        // 占用库存失败，抛异常
                        String spuName = Optional.ofNullable(portalCacheService.getSkuDTO(supplierItem.getSkuId()))
                                .map(skuDTO -> portalCacheService.getSpuDTO(skuDTO.getSpuId()))
                                .map(SpuDTO::getSpuName)
                                .orElse(String.valueOf(supplierItem.getSkuId()));
                        throw new ServiceException("下单失败，商品【" + spuName + "】库存不足！");
                    }
                }
                redisStockService.incrSkuSaledQty(supplierItem.getSkuId(), supplierItem.getTotalNum());
                supplierItems.add(supplierItem);
            });
        } catch (Exception e) {
            log.error("占用B2B库存失败, 进行库存回滚操作, error: ", e);
            this.rollback();
            throw e;
        }
    }

    @Override
    public void rollback() {
        if (CollectionUtils.isNotEmpty(supplierItems)) {
            // 占用库存失败，回滚已增加的库存
            supplierItems.forEach(supplierItem -> {
                redisStockService.incrSkuSaledQty(supplierItem.getSkuId(), supplierItem.getTotalNum().negate());
                redisStockService.incrSkuOccupiedQty(supplierItem.getSkuId(), supplierItem.getTotalNum().negate());
            });
        }
    }
}
