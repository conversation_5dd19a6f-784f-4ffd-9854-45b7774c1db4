package com.zksr.portal.service.price;

import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;

/**
 * <AUTHOR>
 * @date 2024年03月26日 10:40
 * @description: 价格计算的接口
 */
public interface TradePriceCalculatorService {
    /**
     * 门店正常价格计算
     */
    int ORDER_MEMBER_LEVEL = 1;

    /**
     * 组合促销活动优惠
     */
    int ORDER_CB_ACTIVITY = 8;

    /**
     * 特价活动优惠
     */
    int ORDER_SP_ACTIVITY = 4;
    /**
     * 秒杀活动优惠
     */
    int ORDER_SK_ACTIVITY = 4;

    /**
     * 满减活动优惠
     */
    int ORDER_FD_ACTIVITY = 5;

    //
    /**
     * 满赠活动优惠
     */
    int ORDER_FG_ACTIVITY = 6;
    /**
     * 买赠活动优惠
     */
    int ORDER_BG_ACTIVITY = 6;
    /**
     * 订单优惠劵价格计算
     */
    int ORDER_COUPON = 7;


    /**
     * 促销活动计算验证（是否享受此促销）
     * @param param
     * @param result
     */
    void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result);

    /**
     * 更新本次订单使用活动已购数量缓存
     * @param orderVo
     * @param resp
     */
    default void updateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp){}

    default void undoUpdateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp){}
}
