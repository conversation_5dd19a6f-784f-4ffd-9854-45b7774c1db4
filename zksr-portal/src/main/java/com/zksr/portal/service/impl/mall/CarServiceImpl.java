package com.zksr.portal.service.impl.mall;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.dto.car.*;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisCarConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisCacheService;
import com.zksr.common.redis.service.RedisCarService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.portal.controller.mall.vo.car.*;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.car.CarConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IActivityService;
import com.zksr.portal.service.mall.ICarService;
import com.zksr.portal.service.mall.ISaleClassService;
import com.zksr.portal.service.mall.ISkuPriceService;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.promotion.api.activity.vo.ActivityLabelInfoVO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.car.CarApi;
import com.zksr.trade.api.car.dto.AppCarEventDTO;
import com.zksr.trade.api.car.vo.CarSaveReqVO;
import com.zksr.trade.api.car.vo.CarSelectedReqVO;
import com.zksr.trade.api.car.vo.CarTotalRespVO;
import com.zksr.trade.api.order.dto.OrderCutAmtDTO;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商城购物车服务
 * @date 2024/3/26 17:16
 */
@Service
@Slf4j
@SuppressWarnings("all")
public class CarServiceImpl implements ICarService {

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisCacheService redisCacheService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private RedisCarService redisCarService;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private ISkuPriceService skuPriceService;

    @Autowired
    private CarApi carApi;

    @Autowired
    private AccountApi accountApi;

    @Autowired
    private IActivityService activityService;

    @Autowired
    private ISaleClassService saleClassService;

    @Autowired
    private SpuCombineServiceImpl spuCombineService;

    /**
     * 加入购物车
     *
     * @param saveReqVO
     * @return
     */
    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_BRANCH_CAR, condition = "#saveReqVO.branchId")
    public String add(CarSaveReqVO saveReqVO) {
        AppCarSaveReqDTO saveReq = saveReqVO.getCarId();
        log.info("加入购物车 carId={}, num={}", saveReq.getId(), saveReqVO.getOpQty());
        // 验证加入购物车数据
        validateCarAdd(saveReq);

        // 如果数量等于0
        if (saveReqVO.getOpQty() == NumberPool.INT_ZERO) {
            // 走删除逻辑
            removeCar(new CarRemoveReqVO(ListUtil.toList(saveReq.getId()), saveReq.getBranchId(), saveReq.getType(), NumberPool.INT_ZERO));
            return saveReq.getId();
        }

        // 不存在时才更新
        // 更新入驻商商品列表并且优先排序
        redisCarService.updateSupplierItemList(saveReq);

        // 如果是业务员, 需要增加业务员统计信息
        Long colonelId = MallSecurityUtils.getColonelId();

        // 购物车实际加购操作数量
        Boolean newProduct = redisCarService.updateCartItem(saveReq, saveReqVO, colonelId);

        // 是否选中 只有新增商品才选中, 增加商品不选中
        if (newProduct || (Objects.nonNull(saveReqVO.getSelected()) && saveReqVO.getSelected())) {
            // 选中
            redisCarService.selected(saveReqVO, saveReq);
            // 增加购物车角标统计
            redisCarService.addTotal(saveReq);
        }
        // 设置过期时间
        // refreshKeyExpireTime(newProduct, carId);
        return saveReq.getId();
    }

    /**
     * 更新购物车商品数量
     * @param saveReqVO
     */
    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_BRANCH_CAR, condition = "#saveReqVO.branchId")
    public void updateQty(CarSaveQtyReqVO saveReqVO) {
        List<String> removeCarIdList = saveReqVO.getCarIdQty().stream().filter(item -> item.getQty() == NumberPool.INT_ZERO).map(CarSaveQtyReqVO.QtyItem::getCarId).collect(Collectors.toList());
        if (!removeCarIdList.isEmpty())  {
            // 删除购物车中指定商品
            redisCarService.removeItemList(saveReqVO.getBranchId(), removeCarIdList.stream().map(AppCarIdDTO::build).collect(Collectors.toList()), ListUtil.toList(saveReqVO.getProductType()));
        }
        // 如果是业务员, 需要增加业务员统计信息
        Long colonelId = MallSecurityUtils.getColonelId();
        for (CarSaveQtyReqVO.QtyItem item : saveReqVO.getCarIdQty()) {
            AppCarSaveReqDTO saveReq = AppCarSaveReqDTO.build(item.getCarId());
            // 验证普通商品, 获取SPUID
            if (saveReq.validateSku()) {
                SkuDTO skuDTO = portalCacheService.getSkuDTO(saveReq.getSkuId());
                if (Objects.isNull(skuDTO)) {
                    throw exception(TRD_CAR_SKU_NONE);
                }
                saveReq.setSpuId(skuDTO.getSpuId());
            }
            if (item.getQty() > NumberPool.INT_ZERO) {
                CarSaveReqVO carSaveReqVO = new CarSaveReqVO(saveReq, item.getQty(), NumberPool.INT_ONE, Boolean.FALSE);
                // 加单指令
                saveReq.setCommandId(saveReq.getCommandId());
                // 更新购物车数量
                redisCarService.updateCartItem(saveReq, carSaveReqVO, colonelId);
            }
        }
    }

    /**
     * 更新购物车选中
     *
     * @param selectedReqVO
     * @return
     */
    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_BRANCH_CAR, condition = "#selectedReqVO.branchId")
    public List<String> updateSelected(CarSelectedReqVO selectedReqVO) {
        // 更新选中
        AppCarEventDTO appCarEventDTO = redisCarService.updateSelected(
                selectedReqVO.getBranchId(),
                selectedReqVO.getOpType(),
                selectedReqVO.getProductType(),
                selectedReqVO.getCarId().stream().map(AppCarIdDTO::build).map(AppCarUnitDTO::convert).collect(Collectors.toList())
        );
        return ListUtil.empty();
    }

    /**
     * 删除购物车
     *
     * @param selectedReqVO
     * @return
     */
    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_BRANCH_CAR, condition = "#removeReqVO.branchId")
    public List<String> removeCar(CarRemoveReqVO removeReqVO) {
        if (NumberPool.INT_ZERO == removeReqVO.getOpType()) {
            // 删除购物车中指定商品
            redisCarService.removeItemList(removeReqVO.getBranchId(), removeReqVO.getCarId().stream().map(AppCarIdDTO::build).collect(Collectors.toList()), ListUtil.toList(removeReqVO.getProductType()));
        } else {
            // 重置购物车
            redisCarService.resetCar(removeReqVO.getBranchId(), removeReqVO.getProductType());
        }
        return removeReqVO.getCarId();
    }

    /**
     * 获取购物车选中数据
     *
     * @param branchId 门店ID
     * @return
     */
    @Override
    public CarCountRespVO getCount(CarCountReqVO countReqVO) {
        ProductType productType = ProductType.formValue(countReqVO.getProductType());
        // 兼容游客
        if (Objects.isNull(countReqVO.getBranchId()) || countReqVO.getBranchId() <= NumberPool.LONG_ZERO) {
            return new CarCountRespVO(BigDecimal.ZERO, NumberPool.INT_ZERO, NumberPool.INT_ZERO, NumberPool.INT_ZERO, ListUtil.empty());
        }
        BranchDTO branchDto = portalCacheService.getBranchDto(countReqVO.getBranchId());
        Set<String> unitList = redisService.getCacheSet(RedisCarConstants.getBranchSelectKey(countReqVO.getBranchId(), countReqVO.getProductType()));
        List<AppCarUnitDTO> appCarUnitDTOList = unitList.stream().map(AppCarUnitDTO::convert).collect(Collectors.toList());
        // 管道获取购物车商品信息
        Map<String, AppCarStorageDTO> carStorageMap = redisCarService.getCarStoregeList(countReqVO.getBranchId(), countReqVO.getProductType(), appCarUnitDTOList);
        List<CarSelectedItemVO> allItems = appCarUnitDTOList
                .stream()
                .filter(carUnit -> carStorageMap.containsKey(carUnit.toString()))
                .map(carUnit -> carStorageMap.get(carUnit.toString()))
                .map(storage -> CarConvert.INSTANCE.convert(storage, countReqVO.getBranchId(), countReqVO.getProductType()))
                .collect(Collectors.toList());
        // 预制分组
        HashMap<Long, List<CarSelectedItemVO>> supplierValidateMap = new HashMap<>();
        allItems.stream().map(CarSelectedItemVO::getSupplierId).filter(Objects::nonNull).forEach(supplierId -> supplierValidateMap.put(supplierId, Collections.synchronizedList(new ArrayList<>())));
        allItems.parallelStream().forEach(item -> {
            // 只需要上架的
            if (!isRelease(countReqVO.getProductType(), item.getAreaItemId(), item.getSupplierItemId(), item.getUnitSize())) {
                return;
            }
            // 总价格
            if (Objects.nonNull(item.getSpuCombineId())) {
                // 组合商品
                SpuCombineDTO combineDTO = portalCacheService.getSpuCombineDTO(item.getSpuCombineId());
                item.setSalePrice(skuPriceService.getSpuCombinePrice(branchDto, combineDTO, productType));
                item.setItemType(NumberPool.INT_ONE);
            } else {
                Integer isNegativeStock = Optional.ofNullable(portalCacheService.getSupplierDTO(item.getSupplierId())).map(SupplierDTO::getIsNegativeStock).orElse(SupplierNegativeStockEnum.NOT_NEGATIVE_STOCK.getCode());
                if (!SupplierNegativeStockEnum.NEGATIVE_STOCK.getCode().equals(isNegativeStock)) {
                    // 库存不足
                    Long stock = redisStockService.getSurplusSaleQty(item.getSkuId());
                    if (stock <= NumberPool.INT_ZERO) {
                        return;
                    }
                }
                // 普通商品
                item.setSalePrice(getSalePrice(item, branchDto, item.getSkuId(), item.getUnitSize()));
            }
            supplierValidateMap.get(item.getSupplierId()).add(item);
        });
        // 合计数据
        List<CarSelectedItemVO> validateItems = supplierValidateMap
                .values().stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        Integer productNum = NumberPool.INT_ZERO;
        BigDecimal totalAmt = BigDecimal.ZERO;
        for (CarSelectedItemVO item : validateItems) {
            productNum += item.getProductNum();
            totalAmt = totalAmt.add(StockUtil.stockMultiply(item.getProductNum(), item.getSalePrice()));
        }
        CarCountRespVO countRespVO = new CarCountRespVO(totalAmt, validateItems.size(), productNum, supplierValidateMap.size(), validateItems);
        // 返回数据
        return activityService.renderCarSelectTotal(branchDto, countRespVO);
    }

    @Override
    public CarCountStatusRespVO getSelectStock(CarCountStatusReqVO countReqVO) {
        List<CarSelectedItemStatusVO> carIdDTOList = new ArrayList<>();
        var branchId = countReqVO.getBranchId();
        var productType = countReqVO.getProductType();
        BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
        Set<String> carIdList = redisService.getCacheSet(RedisCarConstants.getBranchSelectKey(branchId, productType));
        List<AppCarUnitDTO> appCarUnitDTOList = carIdList.stream().map(AppCarUnitDTO::convert).collect(Collectors.toList());

        // 如果没有支付配置就直接返回得了
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(branchDto.getSysCode());

        LFUCache<Long, SkuDTO> skuCache = CacheUtil.newLFUCache(0);
        LFUCache<Long, SpuDTO> spuCache = CacheUtil.newLFUCache(0);
        for (AppCarUnitDTO carUnitDTO : appCarUnitDTOList) {
            String carItemKey = RedisCarConstants.getSupplierItemKey(countReqVO.getBranchId(), countReqVO.getProductType(), carUnitDTO.getUnitSize(), carUnitDTO.getItemId());
            // 指获取商品数量字段
            AppCarStorageDTO carStorage = AppCarStorageDTO.build(redisService.getCacheMap(carItemKey));
            if (Objects.nonNull(carStorage)) {
                CarSelectedItemStatusVO selectedItemVO = CarSelectedItemStatusVO.build(carStorage);
                // 如果指定了入驻商, 那就验证指定入驻商的
                if (Objects.nonNull(countReqVO.getSupplierId()) && !countReqVO.getSupplierId().equals(selectedItemVO.getSupplierId())) {
                    continue;
                }
                // 返回门店
                selectedItemVO.setBranchId(countReqVO.getBranchId());
                // 商品类型
                selectedItemVO.setType(countReqVO.getProductType());
                // 设置商品数量
                selectedItemVO.setProductNum(carStorage.getProductNum());
                // 是否上架
                selectedItemVO.setRelease(
                        isRelease(countReqVO.getProductType(), selectedItemVO.getAreaItemId(), selectedItemVO.getSupplierItemId(), carStorage.getUnitSize())
                );
                // 检查是否组合商品
                if (carStorage.isSpuCombine()) {
                    SpuCombineDTO combineDTO = portalCacheService.getSpuCombineDTO(carStorage.getSpuCombineId());
                    selectedItemVO.setSpuName(combineDTO.getSpuCombineName());
                    selectedItemVO.setSpuName(combineDTO.getSpecName());
                    // 获取组合商品库存
                    selectedItemVO.setAvailableNum(StockUtil.bigDecimal(spuCombineService.getSpuCombineMinStock(combineDTO)));
                    selectedItemVO.setMaxQty(Objects.nonNull(combineDTO.getMaxOq()) ? combineDTO.getMaxOq().longValue() : Long.MAX_VALUE);
                    // 促销活动
                    PrmActivityDTO activityDTO = portalCacheService.getActivityDto(carStorage.getActivityId());
                    // 验证是否在活动范围内
                    // 活动不存在, 或者活动已过期, 或者活动已下架
                    if (Objects.isNull(activityDTO) || activityDTO.getEndTime().getTime() < System.currentTimeMillis() || activityDTO.getPrmStatus() != NumberPool.INT_ONE) {
                        // 组合促销活动已下架
                        selectedItemVO.setRelease(false);
                    }
                } else {
                    // 封装商品
                    SkuDTO skuDTO = skuCache.get(carStorage.getSkuId(), () -> portalCacheService.getSkuDTO(carStorage.getSkuId()));
                    SpuDTO spuDTO = spuCache.get(selectedItemVO.getSpuId(), () -> portalCacheService.getSpuDTO(selectedItemVO.getSpuId()));
                    selectedItemVO.setSpuName(spuDTO.getSpuName());
                    selectedItemVO.setSkuName(PropertyAndValDTO.getProperties(skuDTO.getProperties()));
                    // 获取最新库存
                    selectedItemVO.setAvailableNum(redisStockService.getSurplusSaleQtyBigDecimal(carStorage.getSkuId()));
                    selectedItemVO.setMaxQty(skuDTO.getMaxOq());
                    if (UnitTypeEnum.M(selectedItemVO.getUnitSize())) {
                        // 设置中单位
                        selectedItemVO.setStockConvertRate(spuDTO.getMidSize());
                        selectedItemVO.setMaxQty(skuDTO.getMidMaxOq());
                    } else if (UnitTypeEnum.L(selectedItemVO.getUnitSize())) {
                        // 设置大单位
                        selectedItemVO.setStockConvertRate(spuDTO.getLargeSize());
                        selectedItemVO.setMaxQty(skuDTO.getLargeMaxOq());
                    }
                }
                // 设置是否允许负库存下单
                Optional.ofNullable(portalCacheService.getSupplierDTO(selectedItemVO.getSupplierId())).ifPresent(supplierDTO -> {
                    selectedItemVO.setIsNegativeStock(supplierDTO.getIsNegativeStock());
                });
                carIdDTOList.add(selectedItemVO);
            }
        }
        // 计算商品大中小规格可加入购物车库存
        Map<String, List<CarSelectedItemStatusVO>> skuMap = carIdDTOList.stream().collect(Collectors.groupingBy(CarSelectedItemStatusVO::uniqueSkuKey));
        skuMap.forEach((uniqueKey , itemList) -> {
            // 库存
            BigDecimal stockQty = itemList.get(0).getAvailableNum();
            if (Objects.isNull(stockQty)) {
                return;
            }
            // 先卖大单位
            // 单位类型, 1-最小单位, 2-中单位, 3-大单位
            itemList.sort(Comparator.comparing(CarSelectedItemStatusVO::getUnitSize).reversed());
            for (CarSelectedItemStatusVO groupItemVO : itemList) {
                // 与最小单位的转换比例
                BigDecimal stockConvertRate = groupItemVO.getStockConvertRate();
                // 最大限购 = 限购数量 * 单位转换比例
                Long maxQty = Objects.nonNull(groupItemVO.getMaxQty()) ? (groupItemVO.getMaxQty()) : Long.MAX_VALUE;
                // 如果购买数量大于限购数量
                Long productNum = groupItemVO.getProductNum().longValue();
                if (Objects.nonNull(maxQty) && maxQty > 0 && productNum > maxQty) {
                    productNum = maxQty;
                }
                if (Objects.nonNull(stockConvertRate)) {
                    // 有效可购买数
                    long validNum = StockUtil.stockDivide(stockQty, stockConvertRate).longValue();
                    if (validNum < productNum) {
                        // 可购买数 小于 购买数
                        productNum = validNum;
                    }
                    // 剩余库存 = 剩余库存 - (当前购买数量 * 库存比例)
                    stockQty = stockQty.subtract(
                        StockUtil.stockMultiply(
                            StockUtil.bigDecimal(productNum),
                            stockConvertRate
                        )
                    );
                }
                // 设置最大有效值
                groupItemVO.setAvailableNum(StockUtil.bigDecimal(productNum));
            }
        });
        // 只下发, 有问题的数据
        carIdDTOList = carIdDTOList.stream().filter(item -> !item.isRelease() || (!SupplierNegativeStockEnum.NEGATIVE_STOCK.getCode().equals(item.getIsNegativeStock()) && item.getProductNum() > item.getAvailableNum().intValue())).collect(Collectors.toList());
        return new CarCountStatusRespVO(carIdDTOList);
    }

    @Override
    public CarTotalRespVO getTotal(CarTotalReqVO totalReqVO) {
        // 兼容游客
        if (Objects.isNull(totalReqVO.getBranchId()) || totalReqVO.getBranchId() <= NumberPool.LONG_ZERO) {
            return new CarTotalRespVO(NumberPool.LONG_ZERO, NumberPool.LONG_ZERO);
        }
        // 获取角标接口可以初始化购物车
        return redisCarService.getTotal(totalReqVO.getBranchId());
    }

    @Override
    public CarPageRespVO getPage(CarPageReqVO pageReqVO) {
        // 兼容游客
        if (Objects.isNull(pageReqVO.getBranchId()) || pageReqVO.getBranchId() <= NumberPool.LONG_ZERO) {
            return CarPageRespVO.empty();
        }
        BranchDTO branchDto = portalCacheService.getBranchDto(pageReqVO.getBranchId());

        AreaDTO areaDto = portalCacheService.getAreaDto(branchDto.getAreaId());
        DcDTO dcDTO = null;
        if(null != areaDto && null != areaDto.getDcId()){
            dcDTO = portalCacheService.getDcDTO(areaDto.getDcId());
        }

        // 获取支付平台
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(branchDto.getSysCode());

        Integer pageNo = pageReqVO.getPageNo();
        Integer pageSize = pageReqVO.getPageSize();
        Integer offset = (pageNo - 1) * pageSize;
        Long branchId = pageReqVO.getBranchId();
        String productType = pageReqVO.getProductType();

        List<CarPageRespVO.CarPageSupplierGroupVO> supplierGroupList = new ArrayList<>();
        // 分页入驻商列表, 降序排列, 分数是操作时间时间戳(毫秒值), 则最近操作时间的入驻商在最前
        Set<Long> supplierList = redisTemplate.opsForZSet().reverseRange(RedisCarConstants.getSupplierListKey(pageReqVO.getBranchId(), pageReqVO.getProductType()), offset, offset + pageSize - 1);
        // 如果有指定查询的入驻商, 则只查指定入驻商
        if (Objects.nonNull(pageReqVO.getSupplierId())) {
            supplierList.clear();
            supplierList.add(pageReqVO.getSupplierId());
        }
        for (Long supplierId : supplierList) {
            // 设置入驻商组
            CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO = new CarPageRespVO.CarPageSupplierGroupVO();
            supplierGroupVO.setSupplierId(supplierId);
            // 获取入驻商账户余额
            AccAccountDTO account = getSupplierAccount(supplierId);
            // 获取当前入驻商是否已经达到起送
            CarConvert.INSTANCE.convert(
                    supplierGroupVO,
                    portalCacheService.getSupplierDTO(supplierId),
                    portalCacheService.checkOrderCutAmt(
                            OrderCutAmtDTO.CacheKey
                                    .builder()
                                    .supplierId(supplierId)
                                    .branchId(branchId)
                                    .productType(productType)
                                    .build()
                    ),
                    account.debt(),
                    portalCacheService.getPartnerSupplierOtherSettingPolicy(supplierId)
            );

            if(null != dcDTO && Objects.nonNull(dcDTO.getGlobalMinAmt()) && dcDTO.getGlobalMinAmt().compareTo(BigDecimal.ZERO) > 0){
                supplierGroupVO.setGlobalMinAmt(BigDecimal.valueOf(-1L));
            }

            if(null != dcDTO && Objects.nonNull(dcDTO.getMinAmt()) && dcDTO.getMinAmt().compareTo(BigDecimal.ZERO) > 0){
                supplierGroupVO.setMinAmt(BigDecimal.valueOf(-1L));
            }

            // 倒序排列, 最新商品在上
            Set<String> unitList = redisTemplate.opsForZSet().reverseRange(RedisCarConstants.getSupplierItemListKey(pageReqVO.getBranchId(), pageReqVO.getProductType(), supplierId), NumberPool.INT_ZERO, NumberPool.LOWER_GROUND_LONG);
            // 转换成标准单位实体
            List<AppCarUnitDTO> appCarUnitDTOList = unitList.stream().map(AppCarUnitDTO::convert).collect(Collectors.toList());
            // 返回入驻商是否支持负库存下单
            Optional.ofNullable(portalCacheService.getSupplierDTO(supplierId)).ifPresent(supplierDTO -> {
                supplierGroupVO.setIsNegativeStock(supplierDTO.getIsNegativeStock());
            });
            // 加载商品信息
            buildSetterProduct(pageReqVO, appCarUnitDTOList, branchDto, supplierGroupVO);
            // 加入返回数据
            // 可能因为异常原因等导致一些商品无法获取到数据
            if (!supplierGroupVO.getItemList().isEmpty()) {
                supplierGroupList.add(supplierGroupVO);
            }
        }
        // 叠加促销活动
        return activityService.renderCarList(
                branchDto,
                CarPageRespVO.build(supplierList.size(), supplierGroupList, payConfigDTO)
        );
    }

    private void buildSetterProduct(CarPageReqVO pageReqVO, List<AppCarUnitDTO> appCarUnitDTOList, BranchDTO branchDTO, CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO) {
        buildSetterProduct(pageReqVO, appCarUnitDTOList, branchDTO, supplierGroupVO, false);
    }


    /**
     * 通过商品单位信息加载标准购物车列表数据数据
     * @param pageReqVO             购物车列表请求对象
     * @param appCarUnitDTOList     购物车单位集合
     * @param branchDTO             门店
     * @param supplierGroupVO       入驻商存储体
     * @param isRecommend           是否推荐
     */
    private void buildSetterProduct(CarPageReqVO pageReqVO, List<AppCarUnitDTO> appCarUnitDTOList, BranchDTO branchDTO, CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO, Boolean isRecommend) {
        // 获取支付平台
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(branchDTO.getSysCode());
        // 门店ID
        Long branchId = pageReqVO.getBranchId();
        // 商品类型local-本地, global-全国
        String productType = pageReqVO.getProductType();
        // 3并发处理, 并发处理以后数据可能是无序的
        // 通过map重新进行有序排列
        ConcurrentHashMap<String, CarPageSupplierGroupItemVO> hashMap = new ConcurrentHashMap();
        // 管道获取购物车数据
        Map<String, AppCarStorageDTO> skuStorageMap = redisCarService.getCarStoregeList(branchId, productType, appCarUnitDTOList);
        // 其余的都是需要单独取数据的
        Lists.partition(ListUtil.toList(appCarUnitDTOList), NumberPool.INT_THREE).stream().parallel().forEach(segmentList -> {
            for (AppCarUnitDTO appCarUnitDTO : segmentList) {
                AppCarStorageDTO storageDTO = skuStorageMap.get(appCarUnitDTO.toString());
                AppCarIdDTO carId = CarConvert.INSTANCE.convert(storageDTO);
                if (Objects.nonNull(storageDTO)) {
                    if (isRecommend) {
                        // 如果是推荐商品, 使用推荐商品替换商品数量
                        storageDTO.setProductNum(storageDTO.getRecommendNum());
                    }
                    // 封装购物车商品信息,加载更新显示信息
                    // 购物车最小单位
                    CarPageSupplierGroupItemVO carItem = Objects.nonNull(storageDTO.getSpuCombineId()) ?
                            // 组合商品
                            combineSupplierGroupItemVO(carId, storageDTO, branchDTO, supplierGroupVO, pageReqVO, payConfigDTO) :
                            // 普通商品
                            normalSupplierGroupItemVO(carId, storageDTO, branchDTO, supplierGroupVO, pageReqVO, payConfigDTO);
                    // 购物车ID 唯一
                    hashMap.put(appCarUnitDTO.toString(), carItem);
                }
            }
        });
        // 重新排序
        appCarUnitDTOList.stream().map(AppCarUnitDTO::toString).filter(hashMap::containsKey).forEach(item -> supplierGroupVO.getItemList().add(hashMap.get(item)));
        if (!supplierGroupVO.getItemList().isEmpty()) {
            // 渲染规格参数
            List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
            Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, item -> item));
            // 计算商品大中小规格可加入购物车库存
            Map<String, List<CarPageSupplierGroupItemVO>> skuMap = supplierGroupVO.getItemList().stream().collect(Collectors.groupingBy(CarPageSupplierGroupItemVO::uniqueSkuKey));
            skuMap.forEach((uniqueKey , itemList) -> {
                // 库存
                BigDecimal stockQty = itemList.get(0).getStockQty();
                if (Objects.isNull(stockQty)) {
                    return;
                }
                // 优先卖大单位的
                itemList.sort(Comparator.comparing(CarPageSupplierGroupItemVO::getUnitSize).reversed());
                for (CarPageSupplierGroupItemVO groupItemVO : itemList) {
                    if (Objects.nonNull(groupItemVO.getStockConvertRate())) {
                        groupItemVO.setMaxQty(stockQty.divide(groupItemVO.getStockConvertRate(), 2, RoundingMode.HALF_UP).longValue());
                        stockQty = stockQty.subtract(new BigDecimal(groupItemVO.getProductNum()).multiply(groupItemVO.getStockConvertRate()));
                    }
                }
                // 渲染单位名称
                itemList.forEach(groupItemVO -> {
                    if (unitMap.containsKey(groupItemVO.getUnit())) {
                        // 设置规格单位名称
                        groupItemVO.setUnitName(unitMap.get(groupItemVO.getUnit()).getDictLabel());
                    }
                });
            });
        }
    }

    /**
     * 获取产品销售价格
     * @param carId     购物车ID
     * @param branchDto 门店
     * @param skuId     skuId
     * @return
     */
    private BigDecimal getSalePrice(AppCarIdDTO carId, BranchDTO branchDto, Long skuId, Integer unitSize) {
        if (Objects.nonNull(carId.getAreaItemId()) && carId.getAreaItemId() > NumberPool.LONG_ZERO) {
            // 本地商品价格
            return skuPriceService.getAreaSkuPrice(branchDto, unitSize ,skuId);
        } else {
            // 全国商品价格
            return skuPriceService.getSupplierSkuPrice(branchDto, unitSize, skuId);
        }
    }

    /**
     * 购物车ID是否存在
     * @param carId
     * @return
     */
    @Override
    public Boolean carIdExist(AppCarIdDTO carId) {
        String carItemKey = RedisCarConstants.getSupplierItemKey(carId.getBranchId(), carId.getType(), carId.getUnitSize(), carId.itemId());
        return redisTemplate.hasKey(carItemKey);
    }

    @Override
    public void init(Long branchId) {
        /*Long minId = 0L;
        CarSaveReqVO item = new CarSaveReqVO();
        for (;;) {
            List<AppCarInitDTO> initDataList = carApi.getInitData(branchId, minId).getCheckedData();
            // 不用发送事件
            item.setSendEvent(Boolean.FALSE);
            // 覆盖操作
            item.setOpType(NumberPool.INT_ONE);
            for (AppCarInitDTO appCarInitDTO : initDataList) {
                item.setCarId(appCarInitDTO);
                item.setOpQty(appCarInitDTO.getProductNum());
                // 不用选中
                item.setSelected(NumberPool.INT_ONE == appCarInitDTO.getSelected());
                // 新增
                add(item);
                *//*if () {
                    // 选中
                    batchSelected(appCarInitDTO.getBranchId(), appCarInitDTO.getType(),Collections.singletonList(appCarInitDTO));
                }*//*
            }
            if (initDataList.size() < 500) {
                break;
            }
            minId = initDataList.get(initDataList.size() - NumberPool.INT_ONE).getTrdCarId();
        }*/
    }

    @Override
    public CarRecommendRespVO getRecommend(Long branchId) {
        BranchDTO branchDTO = portalCacheService.getBranchDto(branchId);
        if (Objects.nonNull(branchDTO.getColonelId())) {
            ColonelDTO colonel = portalCacheService.getColonel(branchDTO.getColonelId());
            if (Objects.nonNull(colonel)) {
                // 购物车统计信息
                AppCarInfoDTO carInfo = redisCarService.getCarInfo(branchId);

                // 返回数据
                return CarRecommendRespVO
                        .builder()
                        .colonelName(colonel.getColonelName())
                        .colonelId(colonel.getColonelId())
                        .avatarImages(colonel.getAvatarImages())
                        .productTypeNum(redisService.getCacheSetCard(RedisCarConstants.getColonelRecommendKey(branchId)))
                        .showWindow(carInfo.getShowWindow())
                        .commandMemo(carInfo.getCommandMemo())
                        .build();
            }
        }
        return null;
    }

    @Override
    public void removeRecommend(Long branchId) {
        // 推荐列表
        Set<AppCarUnitDTO> recommendList = redisService.getCacheSet(RedisCarConstants.getColonelRecommendKey(branchId));
        for (AppCarUnitDTO carUnitDTO : recommendList) {
            // 商品存储信息
            AppCarStorageDTO storageDTO = redisCarService.getAppCarStorage(carUnitDTO, branchId, ProductType.LOCAL);
            if (Objects.nonNull(storageDTO)) {
                AppCarIdDTO carId = storageDTO.getCarId(ProductType.LOCAL, branchId);
                // 存在团长推荐商品 && 商品数量大于推荐数量, 则修改商品数量
                if (storageDTO.getRecommendNum() > 0 && storageDTO.getRecommendNum() <= storageDTO.getProductNum()) {
                    CarSaveQtyReqVO reqVO = new CarSaveQtyReqVO();
                    reqVO.setBranchId(branchId);
                    reqVO.setCarIdQty(ListUtil.toList(new CarSaveQtyReqVO.QtyItem(carId.getId(), storageDTO.getProductNum() - storageDTO.getRecommendNum())));
                    // 更新数量
                    this.updateQty(reqVO);
                } else if (storageDTO.getProductNum() <= storageDTO.getRecommendNum()) {
                    // 商品数量还小于推荐数量
                    // 直接删除
                    redisCarService.removeItemList(branchId, ListUtil.toList(carId), ListUtil.toList(ProductType.LOCAL.getType()));
                }
            }
        }
        // 消除show弹窗
        this.closeRecommendWindow(branchId);
    }

    @Override
    public void removeRecommendFlag(Long branchId) {
        // 推荐列表
        Set<AppCarUnitDTO> recommendList = redisService.getCacheSet(RedisCarConstants.getColonelRecommendKey(branchId));
        for (AppCarUnitDTO carUnitDTO : recommendList) {
            // 商品存储信息
            AppCarStorageDTO storageDTO = redisCarService.getAppCarStorage(carUnitDTO, branchId, ProductType.LOCAL);
            if (Objects.nonNull(storageDTO)) {
                AppCarIdDTO carId = storageDTO.getCarId(ProductType.LOCAL, branchId);
                String carItemKey = RedisCarConstants.getSupplierItemKey(carId.getBranchId(), carId.getType(), carId.getUnitSize(), carId.itemId());
                // 清除业务员在购物车里面的痕迹
                redisService.remCacheSet(
                        RedisCarConstants.getColonelRecommendKey(carId.getBranchId()),
                        AppCarUnitDTO.build(storageDTO.itemId(), storageDTO.getUnitSize())
                );
                redisService.setCacheMapValue(carItemKey, RedisCarConstants.PRODUCT_FIELD_RECOMMEND_NUM, NumberPool.INT_ZERO);
                redisService.setCacheMapValue(carItemKey, RedisCarConstants.PRODUCT_FIELD_RECOMMEND, NumberPool.INT_ZERO);
            }
        }
        // 消除show弹窗
        this.closeRecommendWindow(branchId);
    }

    @Override
    public CarPageRespVO getRecommendList(Long branchId) {
        CarPageReqVO pageReqVO = new CarPageReqVO();
        pageReqVO.setBranchId(branchId);
        pageReqVO.setProductType(ProductType.LOCAL.getType());

        BranchDTO branchDto = portalCacheService.getBranchDto(pageReqVO.getBranchId());
        Integer pageNo = pageReqVO.getPageNo();
        Integer pageSize = pageReqVO.getPageSize();
        Integer offset = (pageNo - 1) * pageSize;
        String productType = ProductType.LOCAL.getType();

        // 推荐列表
        Set<AppCarUnitDTO> recommendList = redisService.getCacheSet(RedisCarConstants.getColonelRecommendKey(branchId));
        // 管道获取购物车数据
        Map<String, AppCarStorageDTO> skuStorageMap = redisCarService.getCarStoregeList(branchId, productType, ListUtil.toList(recommendList));
        Map<Long, List<AppCarStorageDTO>> supplierCacheMap = skuStorageMap.values().stream().collect(Collectors.groupingBy(AppCarStorageDTO::getSupplierId));

        List<CarPageRespVO.CarPageSupplierGroupVO> supplierGroupList = new ArrayList<>(supplierCacheMap.size());
        supplierCacheMap.forEach((supplierId, cacheList) -> {
            // 设置入驻商组
            CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO = new CarPageRespVO.CarPageSupplierGroupVO();
            supplierGroupVO.setSupplierId(supplierId);
            // 获取入驻商账户余额
            AccAccountDTO account = getSupplierAccount(supplierId);
            // 获取入驻商新
            // 获取当前入驻商是否已经达到起送
            CarConvert.INSTANCE.convert(
                    supplierGroupVO,
                    portalCacheService.getSupplierDTO(supplierId),
                    portalCacheService.checkOrderCutAmt(
                            OrderCutAmtDTO.CacheKey
                                    .builder()
                                    .supplierId(supplierId)
                                    .branchId(branchId)
                                    .productType(productType)
                                    .build()
                    ),
                    account.debt(),
                    portalCacheService.getPartnerSupplierOtherSettingPolicy(supplierId)
            );
            // 倒序排列, 最新商品在上
            Set<String> unitList = cacheList.stream().map(AppCarStorageDTO::getUniqueKey).collect(Collectors.toSet());
            // 转换成标准单位实体
            List<AppCarUnitDTO> appCarUnitDTOList = unitList.stream().map(AppCarUnitDTO::convert).collect(Collectors.toList());
            // 加载商品信息
            buildSetterProduct(pageReqVO, appCarUnitDTOList, branchDto, supplierGroupVO, true);
            if (!supplierGroupVO.getItemList().isEmpty()) {
                // 加入返回数据
                supplierGroupList.add(supplierGroupVO);
            }
        });
        // 构建返回数据
        CarPageRespVO pageRespVO = new CarPageRespVO(supplierGroupList.size(), supplierGroupList);
        // 叠加促销活动
        return activityService.renderCarList(branchDto, pageRespVO);
    }

    @Override
    public void closeRecommendWindow(Long branchId) {
        String infoKey = RedisCarConstants.getInfoKey(branchId);
        redisService.setCacheMapValue(infoKey, AppCarInfoDTO.FIELD_SHOW_WINDOW, Boolean.FALSE);
    }

    @Override
    public CarValidateActivityRespVO validateActivity(CarValidateActivityReqVO pageReqVO) {
        // 获取促销活动信息
        PrmActivityDTO activityDTO = portalCacheService.getActivityDto(pageReqVO.getActivityId());
        PrmFuncScopeEnum funcScopeEnum = PrmFuncScopeEnum.formValue(activityDTO.getFuncScope());
        // 设置查询购物车类型
        pageReqVO.setProductType(funcScopeEnum.getProductType());
        pageReqVO.setSupplierId(activityDTO.getSupplierId());
        // 获取购物车数据
        CarPageRespVO page = getPage(pageReqVO);
        List<CarPageSupplierGroupItemVO> groupItemVOS = page.getSupplierGroupList().stream().map(CarPageRespVO.CarPageSupplierGroupVO::getItemList).flatMap(Collection::stream).collect(Collectors.toList());
        Long cnt = NumberPool.LONG_ZERO;
        for (CarPageSupplierGroupItemVO carPageSupplierGroupItemVO : groupItemVOS) {
            for (ActivityLabelInfoVO labelInfoVO : carPageSupplierGroupItemVO.getActivityList()) {
                if (labelInfoVO.getActivity().getActivityId().equals(pageReqVO.getActivityId())) {
                    cnt++;
                }
            }
        }
        return new CarValidateActivityRespVO(cnt);
    }

    /**
     * 获取入驻商账户
     * @param supplierId
     * @return
     */
    private AccAccountDTO getSupplierAccount(Long supplierId) {
        SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(supplierId);
        AccAccountDTO accountDTO = new AccAccountDTO();
        accountDTO.setWithdrawableAmt(BigDecimal.ZERO)
                .setFrozenAmt(BigDecimal.ZERO)
                .setCreditAmt(BigDecimal.ZERO)
                ;
        if (Objects.isNull(supplierDTO)) {
            return accountDTO;
        }
        // 如果没有支付配置就直接返回得了
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(supplierDTO.getSysCode());
        if (Objects.isNull(payConfigDTO) || StringUtils.isEmpty(payConfigDTO.getInteriorStoredPayPlatform())) {
            return accountDTO;
        }
        AccAccountDTO account = portalCacheService.getAccount(supplierId, MerchantTypeEnum.SUPPLIER.getType(), payConfigDTO.getInteriorStoredPayPlatform());
        if (Objects.nonNull(account)) {
            return account;
        }
        return accountDTO;
    }



    /**
     * 更新购物车缓存时间
     * @param newProduct    是否新加购
     * @param carId         购物ID
     */
    private void refreshKeyExpireTime(Boolean newProduct, AppCarIdDTO carId) {
        // 刷新过期时间
        // 已入驻商索引计算是否重刷索引时间
        // 没有过期时间, 或者 过期时间小于 50 天
        // 则连续 10 天内操作过购物车, 缓存则一直在, 否则60天后过期
        // 或者新增商品也需要刷新购物车整体, 不然可能导致, 购物车一组redisKey
        /*String supplierListKey = RedisCarConstants.getSupplierListKey(carId.getBranchId(), carId.getType());
        Long expire = redisTemplate.getExpire(supplierListKey);
        if ( expire <= 0 || expire < (RedisCarConstants.CAT_EXPIRE - 86400 * 10) || newProduct) {
            // 选中索引
            redisTemplate.expire(RedisCarConstants.getBranchSelectKey(carId.getBranchId(), carId.getType()), RedisCarConstants.CAT_EXPIRE, TimeUnit.SECONDS);
            // 入驻商索引
            redisTemplate.expire(RedisCarConstants.getSupplierListKey(carId.getBranchId(), carId.getType()), RedisCarConstants.CAT_EXPIRE, TimeUnit.SECONDS);
            // 入驻商商品索引
            redisTemplate.expire(RedisCarConstants.getSupplierItemListKey(carId.getBranchId(), carId.getType(), carId.getSupplierId()), RedisCarConstants.CAT_EXPIRE, TimeUnit.SECONDS);
            // 商品
            redisTemplate.expire(RedisCarConstants.getSupplierItemKey(carId.getBranchId(), carId.getType(), carId.getSkuId()), RedisCarConstants.CAT_EXPIRE, TimeUnit.SECONDS);
            // 全部商品
            redisTemplate.expire(RedisCarConstants.getBranchTotalKey(carId.getBranchId(), carId.getType()), RedisCarConstants.CAT_EXPIRE, TimeUnit.SECONDS);
        }*/
    }

    private AppCarEventDTO getAppCarEventDTO(CarRemoveReqVO removeReqVO) {
        return new AppCarEventDTO(
                removeReqVO.getOpType() == NumberPool.INT_ZERO ? removeReqVO.getCarId().stream().map(AppCarIdDTO::build).collect(Collectors.toList()) : Collections.emptyList(),
                removeReqVO.getOpType() == NumberPool.INT_ZERO ? AppCarEventDTO.TYPE_STANDARD : AppCarEventDTO.TYPE_CLEAN_ALL,
                removeReqVO.getBranchId()
        );
    }

    /**
     * 验证商品是否上架状态
     * @param carId
     * @return
     */
    private boolean isRelease(String productType, Long areaItemId, Long supplierItemId, Integer unitSize) {
        return getShelfStatus(areaItemId, supplierItemId, productType, unitSize)  == NumberPool.INT_ONE;
    }

    /**
     * 封装购物车商品item, 普通商品
     * @param carId             购物车ID
     * @param storageDTO        购物车商品数据
     * @param branchDTO         门店
     * @param supplierGroupVO   购物车入驻商分组
     * @param pageReqVO         购物车请求参数
     * @param payConfigDTO      平台支付配置
     * @return  普通商品数据
     */
    public CarPageSupplierGroupItemVO normalSupplierGroupItemVO(AppCarIdDTO carId, AppCarStorageDTO storageDTO, BranchDTO branchDTO, CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO, CarPageReqVO pageReqVO, PayConfigDTO payConfigDTO) {
        ProductType productType = ProductType.formValue(pageReqVO.getProductType());
        // 加载carId 缺失参数
        carId.setType(pageReqVO.getProductType());
        carId.setBranchId(pageReqVO.getBranchId());
        SkuDTO sku = portalCacheService.getSkuDTO(carId.getSkuId());
        SpuDTO spu = portalCacheService.getSpuDTO(sku.getSpuId());
        // B2B支付不用管欠款
        boolean notCheckDebt = false;
        if (Objects.nonNull(payConfigDTO) && PayChannelEnum.isB2b(payConfigDTO.getStoreOrderPayPlatform())) {
            // 忽略欠款
            notCheckDebt = true;
        }

        CarPageSupplierGroupItemVO groupItemVO = new CarPageSupplierGroupItemVO();
        groupItemVO.setCarId(carId)
                .setSpuName(spu.getSpuName())
                .setSkuThumb(spu.getThumb())
                //.setSkuProperties(sku.getProperties())
                .setSkuUnit(spu.getMinUnit())
                .setStockQty(sku.getStock())
                .setMinOq(sku.getMinOq())
                .setJumpOq(sku.getJumpOq())
                .setMaxOq(sku.getMaxOq())
                .setProductNum(storageDTO.getProductNum())
                .setRecommendFlag(storageDTO.getRecommendFlag())
                .setSpecName(spu.getSpecName())
                .setMarkPrice(getSalePrice(carId, branchDTO, sku.getSkuId(), carId.getUnitSize()))
                .setSpecName(PropertyAndValDTO.getProperties(sku.getProperties()))
                .setUnit(carId.getUnit())
                .setUnitSize(carId.getUnitSize())
                .setAddTime(storageDTO.getAddTime())
                .setStockConvertRate(BigDecimal.ONE)
                .setMinUnit(spu.getMinUnit())
        ;
        if (UnitTypeEnum.M(carId.getUnitSize())) {
            // 设置大单位
            groupItemVO.setMinOq(sku.getMidMinOq())
                    .setJumpOq(sku.getMidJumpOq())
                    .setMaxOq(sku.getMidMaxOq())
                    .setSkuUnit(spu.getMidUnit())
                    .setStockConvertRate(spu.getMidSize());
        } else if (UnitTypeEnum.L(carId.getUnitSize())) {
            // 设置大单位
            groupItemVO.setMinOq(sku.getLargeMinOq())
                    .setJumpOq(sku.getLargeJumpOq())
                    .setMaxOq(sku.getLargeMaxOq())
                    .setSkuUnit(spu.getLargeUnit())
                    .setStockConvertRate(spu.getLargeSize());
        }
        // 渲染素材图片
        // MaterialCacheVO materialCacheVO = portalCacheService.getMaterial(MaterialCacheVO.getProductCacheKey(productType, carId.itemId()));
        // groupItemVO.setMaterialUrl(materialCacheVO.getValidateMaterial());
        // 获取上架商品状态
        groupItemVO.setShelfStatus(getShelfStatus(carId.getAreaItemId(), carId.getSupplierItemId(), carId.getType(), carId.getUnitSize()));
        // 设置库存
        groupItemVO.setStockQty(redisStockService.getSurplusSaleQtyBigDecimal(sku.getSkuId()));
        groupItemVO.setSelected(
                // 必须是选中
                redisCarService.isSelected(branchDTO.getBranchId(), carId.getType(), carId.itemId(), storageDTO.getUnitSize())
                && (
                        // 是管理模式
                        pageReqVO.getIgnoreDebt() ||
                        (
                                // 或者满赠条件, 没有欠款, 有库存, 是上架的
                                (!supplierGroupVO.getDebt() || notCheckDebt)
                                && (SupplierNegativeStockEnum.NEGATIVE_STOCK.getCode().equals(supplierGroupVO.getIsNegativeStock()) || (Objects.nonNull(groupItemVO.getStockQty()) && groupItemVO.getStockQty().compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO))
                                && isRelease(pageReqVO.getProductType(), carId.getAreaItemId(), carId.getSupplierItemId(), carId.getUnitSize())
                        )
                )

        );
        return groupItemVO;
    }

    /**
     * 封装购物车商品item, 组合商品
     * @param carId             购物车ID
     * @param storageDTO        购物车商品数据
     * @param branchDTO         门店
     * @param supplierGroupVO   购物车入驻商分组
     * @param pageReqVO         购物车请求参数
     * @param payConfigDTO      平台支付配置
     * @return  组合商品数据
     */
    private CarPageSupplierGroupItemVO combineSupplierGroupItemVO(AppCarIdDTO carId, AppCarStorageDTO storageDTO, BranchDTO branchDTO, CarPageRespVO.CarPageSupplierGroupVO supplierGroupVO, CarPageReqVO pageReqVO, PayConfigDTO payConfigDTO) {
        // 加载carId 缺失参数
        carId.setType(pageReqVO.getProductType());
        ProductType productType = ProductType.formValue(carId.getType());
        carId.setBranchId(pageReqVO.getBranchId());
        // B2B支付不用管欠款
        boolean notCheckDebt = false;
        if (Objects.nonNull(payConfigDTO) && PayChannelEnum.isB2b(payConfigDTO.getStoreOrderPayPlatform())) {
            // 忽略欠款
            notCheckDebt = true;
        }
        SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(storageDTO.getSpuCombineId());
        // 购物车商品信息富渲染
        CarPageSupplierGroupItemVO groupItemVO = new CarPageSupplierGroupItemVO();
        groupItemVO.setCarId(carId)
                .setStockQty(StockUtil.bigDecimal(spuCombineService.getSpuCombineMinStock(spuCombineDTO)))
                .setSpuName(spuCombineDTO.getSpuCombineName())
                .setSkuThumb(spuCombineDTO.getThumb())
                .setSkuUnit(Objects.nonNull(spuCombineDTO.getUnit()) ? spuCombineDTO.getUnit().longValue() : null)
                .setMinOq(Objects.nonNull(spuCombineDTO.getMinOq()) ? spuCombineDTO.getMinOq().longValue() : null)
                .setJumpOq(Objects.nonNull(spuCombineDTO.getJumpOq()) ? spuCombineDTO.getJumpOq().longValue() : null)
                .setMaxOq(Objects.nonNull(spuCombineDTO.getMaxOq()) ? spuCombineDTO.getMaxOq().longValue() : null)
                .setProductNum(storageDTO.getProductNum())
                .setRecommendFlag(storageDTO.getRecommendFlag())
                .setSpecName(spuCombineDTO.getSpecName())
                .setMarkPrice(skuPriceService.getSpuCombinePrice(branchDTO, spuCombineDTO, productType))
                .setUnit(carId.getUnit())
                .setUnitSize(carId.getUnitSize())
                .setStockConvertRate(BigDecimal.ONE)
                .setAddTime(storageDTO.getAddTime())
                .setItemType(NumberPool.INT_ONE)
        ;
        // 获取上架商品状态
        groupItemVO.setShelfStatus(getShelfStatus(carId.getAreaItemId(), carId.getSupplierItemId(), carId.getType(), carId.getUnitSize()));
        // 验证活动状态
        // 促销活动
        PrmActivityDTO activityDTO = portalCacheService.getActivityDto(storageDTO.getActivityId());
        // 验证是否在活动范围内
        // 活动不存在, 或者活动已过期, 或者活动已下架
        if (Objects.isNull(activityDTO) || activityDTO.getEndTime().getTime() < System.currentTimeMillis() || activityDTO.getPrmStatus() != NumberPool.INT_ONE) {
            // 组合促销活动已下架
            groupItemVO.setShelfStatus(NumberPool.INT_ZERO);
        }
        // 渲染素材图片
        // MaterialCacheVO materialCacheVO = portalCacheService.getMaterial(MaterialCacheVO.getProductCacheKey(productType, carId.itemId()));
        // groupItemVO.setMaterialUrl(materialCacheVO.getValidateMaterial());
        groupItemVO.setSelected(
                // 必须是选中
                redisCarService.isSelected(branchDTO.getBranchId(), carId.getType(), carId.itemId(), storageDTO.getUnitSize())
                        && (
                        // 是管理模式
                        pageReqVO.getIgnoreDebt() ||
                                (
                                        // 或者满赠条件, 没有欠款, 有库存, 是上架的
                                        (!supplierGroupVO.getDebt() || notCheckDebt)
                                                && (SupplierNegativeStockEnum.NEGATIVE_STOCK.getCode().equals(supplierGroupVO.getIsNegativeStock()) || (Objects.nonNull(groupItemVO.getStockQty()) && groupItemVO.getStockQty().compareTo(BigDecimal.ZERO) > NumberPool.INT_ZERO))
                                                && isRelease(pageReqVO.getProductType(), carId.getAreaItemId(), carId.getSupplierItemId(), carId.getUnitSize())
                                )
                )
        );

        // 验证组合促销商品是否有效
        if (!validateSpuCombineAcitvity(carId.itemId(), productType)) {
            groupItemVO.setSelected(Boolean.FALSE);
            groupItemVO.setShelfStatus(NumberPool.INT_ZERO);
        }
        return groupItemVO;
    }

    /**
     * 验证组合促销商品是否有效
     * @param itemId        上架商品ID
     * @param productType   商品类型
     * @return  false-无效, ture-有效
     */
    private boolean validateSpuCombineAcitvity(Long itemId, ProductType productType) {
        Long activityId = null;
        if (productType == ProductType.GLOBAL) {
            SupplierItemDTO releseItem = portalCacheService.getSupplierItemDTO(itemId);
            activityId = releseItem.getActivityId();
        } else {
            AreaItemDTO releseItem = portalCacheService.getAreaItemDTO(itemId);
            activityId = releseItem.getActivityId();
        }
        PrmActivityDTO activityDto = portalCacheService.getActivityDto(activityId);
        if (Objects.nonNull(activityDto)) {
           return activityDto.validate();
        }
        return false;
    }

    private Integer getShelfStatus(Long areaItemId, Long supplierItemId, String productType, Integer unitSize) {
        if (ProductType.isGlobal(productType)) {
            SupplierItemDTO releseItem = portalCacheService.getSupplierItemDTO(supplierItemId);
            if (Objects.isNull(releseItem)) {
                return NumberPool.INT_ZERO;
            }
            // 验证组合促销
            if (Objects.nonNull(releseItem.getActivityId())) {
                PrmActivityDTO activityDto = portalCacheService.getActivityDto(releseItem.getActivityId());
                if (Objects.nonNull(activityDto)) {
                    return activityDto.validate() ? NumberPool.INT_ONE : NumberPool.INT_ZERO;
                }
            }
            if (UnitTypeEnum.M(unitSize)) {
                return releseItem.getMidShelfStatus();
            } else if (UnitTypeEnum.L(unitSize)) {
                return releseItem.getLargeShelfStatus();
            } else if (UnitTypeEnum.S(unitSize)) {
                return releseItem.getMinShelfStatus();
            }
            return releseItem.getShelfStatus();
        } else {
            AreaItemDTO releseItem = portalCacheService.getAreaItemDTO(areaItemId);
            if (Objects.isNull(releseItem)) {
                return NumberPool.INT_ZERO;
            }
            // 验证组合促销
            if (Objects.nonNull(releseItem.getActivityId())) {
                PrmActivityDTO activityDto = portalCacheService.getActivityDto(releseItem.getActivityId());
                if (Objects.nonNull(activityDto)) {
                    return activityDto.validate() ? NumberPool.INT_ONE : NumberPool.INT_ZERO;
                }
            }
            if (UnitTypeEnum.M(unitSize)) {
                return releseItem.getMidShelfStatus();
            } else if (UnitTypeEnum.L(unitSize)) {
                return releseItem.getLargeShelfStatus();
            } else if (UnitTypeEnum.S(unitSize)) {
                return releseItem.getMinShelfStatus();
            }
            return releseItem.getShelfStatus();
        }
    }

    private void validateCarAdd(AppCarSaveReqDTO saveReq) {
        // 验证商品是否上架
        if (!isRelease(saveReq.getType(), saveReq.getAreaItemId(), saveReq.getSupplierItemId(), saveReq.getUnitSize())) {
            throw exception(TRD_CAR_PRODUCT_NOT_RELEASE);
        }
        // 验证商品是否存在
        SkuDTO skuDTO = portalCacheService.getSkuDTO(saveReq.getSkuId());
        if (Objects.nonNull(skuDTO)) {
            saveReq.setSpuId(skuDTO.getSpuId());
        }
        // 验证单位是否存在
        if (Objects.isNull(saveReq.getUnitSize())) {
            throw exception(TRD_CAR_UNIT_SIZE_NONE);
        }
        // 数量限制
        // 对于购物车里面已经存在的数据操作不限制
        String carItemKey = RedisCarConstants.getSupplierItemKey(saveReq.getBranchId(), saveReq.getType(), saveReq.getUnitSize(), saveReq.itemId());
        Boolean isAddItem = redisTemplate.hasKey(carItemKey);
        CarTotalRespVO serviceTotal = redisCarService.getTotal(saveReq.getBranchId());
        if (((ProductType.isGlobal(saveReq.getType()) && serviceTotal.getGlobalProductTypeNum() >= RedisCarConstants.CAT_MAX) ||
                (!ProductType.isGlobal(saveReq.getType()) && serviceTotal.getLocalProductTypeNum() >= RedisCarConstants.CAT_MAX)) && !isAddItem) {
            throw exception(TRD_CAR_MAX_NUMBER, RedisCarConstants.CAT_MAX);
        }

        // 门店
        BranchDTO branchDTO = portalCacheService.getBranchDto(saveReq.getBranchId());
        if (Objects.isNull(branchDTO)) {
            throw exception(TRD_CAR_BRANCH_NOT_EXIST);
        }

        if (!ProductType.isGlobal(saveReq.getType())) {
            // 区域验证
            AreaItemDTO releseItem = portalCacheService.getAreaItemDTO(saveReq.getAreaItemId());
            if ( Objects.isNull(branchDTO.getAreaId()) || !branchDTO.getAreaId().equals(releseItem.getAreaId())) {
                SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
                throw exception(TRD_CAR_AREA_OUTSIDE, spuDTO.getSpuName());
            }
            // 分类验证
            List<AreaClassDTO> areaClassDTOS = saleClassService.getAreaClassDTOS(branchDTO.getAreaId(), branchDTO.getChannelId(), branchDTO.getBranchId());
            Set<Long> areaSaleIds = areaClassDTOS.stream().map(AreaClassDTO::getAreaClassId).collect(Collectors.toSet());
            if (areaSaleIds.isEmpty() || !areaSaleIds.contains(releseItem.getAreaClassId())) {
                SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
                throw exception(TRD_CAR_SALE_OUTSIDE, spuDTO.getSpuName());
            }
            saveReq.setActivityId(releseItem.getActivityId());
            saveReq.setSpuCombineId(releseItem.getSpuCombineId());
        } else {
            SupplierItemDTO releseItem = portalCacheService.getSupplierItemDTO(saveReq.getSupplierItemId());
            // 分类验证
            List<SaleClassDTO> areaClassDTOS = saleClassService.getSaleClassDTOS(branchDTO.getSysCode(), branchDTO.getGroupId());
            Set<Long> areaSaleIds = areaClassDTOS.stream().map(SaleClassDTO::getSaleClassId).collect(Collectors.toSet());
            if (areaSaleIds.isEmpty() || !areaSaleIds.contains(releseItem.getSaleClassId())) {
                SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
                throw exception(TRD_CAR_SALE_OUTSIDE, spuDTO.getSpuName());
            }
            saveReq.setActivityId(releseItem.getActivityId());
            saveReq.setSpuCombineId(releseItem.getSpuCombineId());
        }
    }
}
