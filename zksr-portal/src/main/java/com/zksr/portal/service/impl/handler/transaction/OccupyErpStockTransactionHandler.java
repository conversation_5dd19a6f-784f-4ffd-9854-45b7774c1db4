package com.zksr.portal.service.impl.handler.transaction;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Maps;
import com.zksr.common.core.enums.SupplierNegativeStockEnum;
import com.zksr.common.core.enums.request.B2BRequestType;
import com.zksr.common.core.enums.request.OperationType;
import com.zksr.common.core.enums.request.RequestType;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.transaction.TransactionHandler;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.uuid.IdUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.system.api.openapi.AnntoErpApi;
import com.zksr.system.api.openapi.dto.AnntoErpRequestDTO;
import com.zksr.system.api.openapi.dto.AnntoErpResultDTO;
import com.zksr.system.api.openapi.dto.ErpStockRequestDTO;
import com.zksr.system.api.openapi.dto.ErpStockShortageDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderDtlSaveVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderSaveVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.OpenApiConstants.DEFAULT_ORG_CODE;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.trade.enums.ErrorCodeConstants.OCCUPY_ERP_STOCK_ERROR;

/**
 * 库存占用事务处理器
 */
@SuppressWarnings("rawtypes")
@Slf4j
public class OccupyErpStockTransactionHandler implements TransactionHandler<List<ErpStockShortageDTO>> {
    private static final String B2B_SHEET_SOURCE = "B2B";
    private static final String API_CREATE_STOCK_OCCUPY = "/createStockOccupy";
    private static final String API_ROLLBACK_STOCK_OCCUPY = "/rollbackStockOccupy";
    private final AnntoErpApi anntoErpApi = SpringUtils.getBean(AnntoErpApi.class);
    private final IPortalCacheService portalCacheService = SpringUtils.getBean(IPortalCacheService.class);
    private final RedisStockService redisStockService = SpringUtils.getBean(RedisStockService.class);
    private final RemoteSaveOrderVO orderVo;
    private final List<TrdSupplierOrderSaveVO> successOccupies = new ArrayList<>();
    private final List<ErpStockShortageDTO> failOccupies = new ArrayList<>();

    public OccupyErpStockTransactionHandler(RemoteSaveOrderVO orderVo) {
        this.orderVo = orderVo;
    }

    @Override
    public void doBusiness() {
        Map<String, List<TrdSupplierOrderDtlSaveVO>> dtlMap = orderVo.getSupplierOrderDtlSaveVOS().stream().collect(Collectors.groupingBy(TrdSupplierOrderDtlSaveVO::getSupplierOrderNo));
        try {
            orderVo.getSupplierOrderSaveVOs().stream()
                    .filter(supplierOrder -> dtlMap.containsKey(supplierOrder.getSupplierOrderNo()))
                    .forEach(supplierOrder ->  {
                        Map<String, Pair<Long, BigDecimal>> itemSkuMap = Maps.newHashMap();
                        ErpStockRequestDTO data = this.convertOccupyErpStockRequest(supplierOrder, dtlMap, itemSkuMap);
                        AnntoErpRequestDTO requestDTO = this.buildAnntoErpRequestDTO(API_CREATE_STOCK_OCCUPY,
                                supplierOrder.getSupplierId(),
                                data,
                                RequestType.B2B_STOCK_OCCUPY_CREATE,
                                B2BRequestType.B2B_STOCK_OCCUPY_CREATE);
                        log.info("占用ERP库存，入参: {}", JSON.toJSONString(requestDTO));
                        CommonResult<AnntoErpResultDTO<String>> commonResult = anntoErpApi.sendErp(requestDTO);
                        log.info("占用ERP库存，结果: {}", JSON.toJSONString(commonResult));

                        if (commonResult == null || commonResult.isError() || commonResult.getCheckedData() == null) {
                            throw new ServiceException("下单失败，预占库存失败！");
                        }

                        AnntoErpResultDTO<String> erpResult = commonResult.getCheckedData();
                        if (!erpResult.isOk()) {
                            // 检查是否为库存不足错误
                            if (this.isStockShortageError(erpResult)) {
                                ErpStockShortageDTO stockShortageInfo = this.parseStockShortageInfo(erpResult, itemSkuMap);
                                failOccupies.add(stockShortageInfo);
                            } else {
                                // 一般不会走到这里
                                throw new ServiceException("下单失败，预占库存失败：" + erpResult.getResponseMessage());
                            }
                        } else {
                            // 记录成功占用库存的订单，以便后面进行回滚
                            successOccupies.add(supplierOrder);
                        }
                    });
            if (CollectionUtils.isNotEmpty(failOccupies)) {
                throw exception(OCCUPY_ERP_STOCK_ERROR);
            }
        } catch (Exception e) {
            log.error("占用ERP库存失败, error: ", e);
            this.rollback();
            throw e;
        }
    }

    @Override
    public void rollback() {
        if (CollectionUtils.isEmpty(successOccupies)) {
            return;
        }
        successOccupies.forEach(supplierOrder -> {
            Integer stockShortFlag = Optional.ofNullable(portalCacheService.getSupplierDTO(supplierOrder.getSupplierId())).map(SupplierDTO::getIsNegativeStock).orElse(SupplierNegativeStockEnum.NOT_NEGATIVE_STOCK.getCode());
            ErpStockRequestDTO data = new ErpStockRequestDTO()
                    .setB2bSheetNo(supplierOrder.getSupplierOrderNo())
                    .setSheetSource(B2B_SHEET_SOURCE)
                    .setOrgCode(this.getOrgCode(supplierOrder.getSupplierId()))
                    .setStockShortFlag(stockShortFlag)
                    ;
            AnntoErpRequestDTO requestDTO = this.buildAnntoErpRequestDTO(API_ROLLBACK_STOCK_OCCUPY,
                    supplierOrder.getSupplierId(),
                    data,
                    RequestType.B2B_STOCK_OCCUPY_ROLLBACK,
                    B2BRequestType.B2B_STOCK_OCCUPY_ROLLBACK);
            log.info("回滚ERP库存占用，入参: {}", JSON.toJSONString(requestDTO));
            CommonResult<AnntoErpResultDTO<String>> commonResult = anntoErpApi.sendErp(requestDTO);
            log.info("回滚ERP库存占用，结果: {}", JSON.toJSONString(commonResult));
            if (commonResult == null || commonResult.isError() || commonResult.getCheckedData() == null || !commonResult.getCheckedData().isOk()) {
                log.error("回滚ERP库存占用失败，supplierOrderNo: {}", supplierOrder.getSupplierOrderNo());
            }
        });
    }

    private ErpStockRequestDTO convertOccupyErpStockRequest(TrdSupplierOrderSaveVO supplierOrder, Map<String, List<TrdSupplierOrderDtlSaveVO>> dtlMap, Map<String, Pair<Long, BigDecimal>> itemSkuMap) {
        Integer stockShortFlag = Optional.ofNullable(portalCacheService.getSupplierDTO(supplierOrder.getSupplierId())).map(SupplierDTO::getIsNegativeStock).orElse(SupplierNegativeStockEnum.NOT_NEGATIVE_STOCK.getCode());
        ErpStockRequestDTO erpStockRequestDTO = new ErpStockRequestDTO()
                .setB2bSheetNo(supplierOrder.getSupplierOrderNo())
                .setSheetSource(B2B_SHEET_SOURCE)
                .setOrgCode(this.getOrgCode(supplierOrder.getSupplierId()))
                .setSubList(new ArrayList<>())
                .setStockShortFlag(stockShortFlag)
                ;
        List<TrdSupplierOrderDtlSaveVO> dtlList = dtlMap.get(supplierOrder.getSupplierOrderNo());
        int lineNo = 1;
        for (TrdSupplierOrderDtlSaveVO dtl : dtlList) {
            SkuDTO skuDTO = portalCacheService.getSkuDTO(dtl.getSkuId());
            ErpStockRequestDTO.ErpStockDetailDTO detailDTO = new ErpStockRequestDTO.ErpStockDetailDTO()
                    .setItemNo(skuDTO.getSourceNo())
                    .setLineNo(lineNo++)
                    .setMinDetailQty(dtl.getOrderUnitSize().multiply(new BigDecimal(dtl.getOrderUnitQty())));
            erpStockRequestDTO.getSubList().add(detailDTO);
            // 记录itemNo和skuId的关系
            itemSkuMap.put(skuDTO.getSourceNo(), Pair.of(skuDTO.getSkuId(), dtl.getOrderUnitSize()));
        }
        return erpStockRequestDTO;
    }

    private AnntoErpRequestDTO buildAnntoErpRequestDTO(String api, Long supplierId, ErpStockRequestDTO data, RequestType requestType, B2BRequestType b2bRequestType) {
        return new AnntoErpRequestDTO()
                .setApi(api)
                .setData(data)
                .setReqId(IdUtils.fastSimpleUUID())
                .setSupplierId(supplierId)
                .setRequestType(requestType)
                .setB2bRequestType(b2bRequestType)
                .setOperationType(OperationType.UPDATE);
    }

    private String getOrgCode(Long supplierId) {
        // ERP默认组织编码为100
        String orgCode = DEFAULT_ORG_CODE;
        OpensourceDto opensourceDto = portalCacheService.getOpensourceByMerchantId(supplierId);
        if (opensourceDto != null && opensourceDto.getSendCode() != null) {
            String[] group = opensourceDto.getSendCode().split(StringPool.COLON);
            if (group.length > 1 && StringUtils.isNotBlank(group[1])) {
                orgCode = group[1].trim();
            }
        }
        return orgCode;
    }

    /**
     * 判断是否为库存不足错误
     */
    private boolean isStockShortageError(AnntoErpResultDTO<String> erpResult) {
        // 检查响应内容是否包含库存不足信息
        String responseContent = erpResult.getResponseContent();
        if (StringUtils.isBlank(responseContent)) {
            return false;
        }

        try {
            // 尝试解析为库存不足DTO
            ErpStockShortageDTO stockShortageDTO = JSON.parseObject(responseContent, ErpStockShortageDTO.class);
            return stockShortageDTO != null && CollectionUtils.isNotEmpty(stockShortageDTO.getDetailVos());
        } catch (Exception e) {
            log.warn("解析库存不足信息失败: {}", responseContent, e);
            return false;
        }
    }

    /**
     * 解析库存不足信息
     */
    private ErpStockShortageDTO parseStockShortageInfo(AnntoErpResultDTO<String> erpResult, Map<String, Pair<Long, BigDecimal>> itemSkuMap) {
        String responseContent = erpResult.getResponseContent();
        ErpStockShortageDTO stockShortageDTO = JSON.parseObject(responseContent, ErpStockShortageDTO.class);
        log.info("解析到库存不足信息: {}", JSON.toJSONString(stockShortageDTO));
        stockShortageDTO.getDetailVos().forEach(detail -> {
            Pair<Long, BigDecimal> pair = itemSkuMap.get(detail.getItemNo());
            detail.setSkuId(pair.getKey());
            detail.setOrderUnitSize(pair.getValue());
            // 根据ERP返回的可用库存更新stock
            log.info("更新库存不足商品库存, skuId={}, stock={}", detail.getSkuId(), detail.getEnableQty());
            redisStockService.setSkuStock(detail.getSkuId(), detail.getEnableQty());
        });
        return stockShortageDTO;
    }

    @Override
    public List<ErpStockShortageDTO> getResult() {
        return failOccupies;
    }
}
