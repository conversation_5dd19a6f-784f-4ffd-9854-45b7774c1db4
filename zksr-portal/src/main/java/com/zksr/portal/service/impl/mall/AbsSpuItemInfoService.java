package com.zksr.portal.service.impl.mall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import com.zksr.common.core.constant.PrdtConstants;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.IActivityService;
import com.zksr.portal.service.mall.ISkuPriceService;
import com.zksr.portal.service.mall.ISpuItemInfoService;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.property.dto.PropertyKeyDTO;
import com.zksr.product.api.property.dto.PropertyValueDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/30 9:02
 */
@SuppressWarnings("all")
public abstract class AbsSpuItemInfoService implements ISpuItemInfoService {

    @Autowired
    protected IPortalCacheService portalCacheService;

    @Autowired
    protected RedisStockService redisStockService;

    @Autowired
    protected ISkuPriceService skuPriceService;

    @Resource
    protected SkuApi skuApi;

    /**
     * 渲染商品数据日期格式化
     * @param product
     * @param spuDTO
     */
    protected void produceFormat(SkuPageRespVO product, SpuDTO spuDTO) {
        // 如果没有生产日期
        if (Objects.isNull(spuDTO.getLatestDate())) {
            product.setOldestDate(StringPool.EMPTY);
            product.setLatestDate(StringPool.EMPTY);
            return;
        }
        // 本地上架商品
        if (ProductType.LOCAL.getType().equals(product.getType())) {
            // 渲染展示分类生产日期格式
            AreaClassDTO classDTO = portalCacheService.getAreaClassDTO(product.getClassId());
            if (Objects.isNull(classDTO) || classDTO.getShowProduceDate() == NumberPool.INT_ZERO) {
                product.setLatestDate(StringPool.EMPTY);
                product.setOldestDate(StringPool.EMPTY);
                return;
            }
            if (Objects.isNull(classDTO)
                    || (classDTO.getShowProduceDate() == NumberPool.INT_ONE && StringUtils.isEmpty(classDTO.getProduceDateFormat()))
                    || (classDTO.getShowProduceDate() == NumberPool.INT_ZERO)
            ) {
                product.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                product.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
            } else {
                product.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), classDTO.getProduceDateFormat()));
                product.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), classDTO.getProduceDateFormat()));
            }
        } else {
            SaleClassDTO classDTO = portalCacheService.getSaleClassDTO(product.getClassId());
            if (Objects.isNull(classDTO) || classDTO.getShowProduceDate() == NumberPool.INT_ZERO) {
                product.setLatestDate(StringPool.EMPTY);
                product.setOldestDate(StringPool.EMPTY);
                return;
            }
            if (Objects.isNull(classDTO)
                    || (classDTO.getShowProduceDate() == NumberPool.INT_ONE && StringUtils.isEmpty(classDTO.getProduceDateFormat()))
                    || (classDTO.getShowProduceDate() == NumberPool.INT_ZERO)
            ) {
                product.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                product.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
            } else {
                product.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), classDTO.getProduceDateFormat()));
                product.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), classDTO.getProduceDateFormat()));
            }
        }
    }

    /**
     * 渲染商品数据日期格式化
     * @param product
     * @param spuDTO
     */
    protected void produceFormat(SpuDetailRespVO product, SpuDTO spuDTO) {
        // 如果没有生产日期
        if (Objects.isNull(spuDTO.getLatestDate())) {
            product.setOldestDate(StringPool.EMPTY);
            product.setLatestDate(StringPool.EMPTY);
            return;
        }
        // 本地上架商品
        if (ProductType.LOCAL.getType().equals(product.getType())) {
            // 渲染展示分类生产日期格式
            AreaClassDTO classDTO = portalCacheService.getAreaClassDTO(product.getClassId());
            if (Objects.isNull(classDTO) || classDTO.getShowProduceDate() == NumberPool.INT_ZERO) {
                product.setLatestDate(StringPool.EMPTY);
                product.setOldestDate(StringPool.EMPTY);
                return;
            }
            if (Objects.isNull(classDTO)
                    || (classDTO.getShowProduceDate() == NumberPool.INT_ONE && StringUtils.isEmpty(classDTO.getProduceDateFormat()))
                    || (classDTO.getShowProduceDate() == NumberPool.INT_ZERO)
            ) {
                product.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                product.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
            } else {
                product.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), classDTO.getProduceDateFormat()));
                product.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), classDTO.getProduceDateFormat()));
            }
        } else {
            SaleClassDTO classDTO = portalCacheService.getSaleClassDTO(product.getClassId());
            if (Objects.isNull(classDTO) || classDTO.getShowProduceDate() == NumberPool.INT_ZERO) {
                product.setLatestDate(StringPool.EMPTY);
                product.setOldestDate(StringPool.EMPTY);
                return;
            }
            if (Objects.isNull(classDTO)
                    || (classDTO.getShowProduceDate() == NumberPool.INT_ONE && StringUtils.isEmpty(classDTO.getProduceDateFormat()))
                    || (classDTO.getShowProduceDate() == NumberPool.INT_ZERO)
            ) {
                product.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                product.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
            } else {
                product.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), classDTO.getProduceDateFormat()));
                product.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), classDTO.getProduceDateFormat()));
            }
        }
    }

    protected static List<PropertyKeyDTO> getProperty(List<SkuDetailRespVO> skuList) {
        List<PropertyKeyDTO> result = new ArrayList<>();
        ArrayList<SkuPropertiesVO> skuPropertiesVOS = new ArrayList<>();
        skuList.stream().filter(item -> Objects.nonNull(item.getPropertieList())).map(SkuDetailRespVO::getPropertieList).forEach(skuPropertiesVOS::addAll);
        skuPropertiesVOS.forEach(item -> {
            if (Objects.isNull(item.getPropertyId())) {
                item.setPropertyId(NumberPool.LOWER_GROUND_LONG);
                item.setPropertyValId(NumberPool.LOWER_GROUND_LONG);
                item.setPropertyName("无");
            }
        });
        // 如果没有属性, 例如单规格, 只有大中小
        if (skuPropertiesVOS.isEmpty()) {
            return new ArrayList<>();
        }
        Map<Long, List<SkuPropertiesVO>> collect = skuPropertiesVOS.stream().collect(Collectors.groupingBy(SkuPropertiesVO::getPropertyId));
        ArrayList<Long> keyIdList = ListUtil.toList(collect.keySet());
        keyIdList.sort(Comparator.comparing(Long::longValue));
        for (Long propertyId : keyIdList) {
            List<SkuPropertiesVO> propertiesVOS = collect.get(propertyId);
            SkuPropertiesVO propertiesVO = propertiesVOS.get(0);
            List<PropertyValueDTO> propertyValueDTOS = propertiesVOS.stream().map(item -> new PropertyValueDTO(item.getPropertyValId(), item.getValName())).collect(Collectors.toList());
            Map<Long, List<PropertyValueDTO>> tempMap = propertyValueDTOS.stream().collect(Collectors.groupingBy(PropertyValueDTO::getPropertyValId));
            ArrayList<Long> propertyValIdList = ListUtil.toList(tempMap.keySet());
            propertyValIdList.sort(Comparator.comparing(Long::longValue));
            List<PropertyValueDTO> deList = new ArrayList<>();
            for (Long propertyValId : propertyValIdList) {
                deList.add(tempMap.get(propertyValId).get(0));
            }
            PropertyKeyDTO propertyKeyDTO = PropertyKeyDTO.builder()
                    .propertyId(propertyId)
                    .propertyName(propertiesVO.getPropertyName())
                    .propertyValueDTOS(deList)
                    .build();
            result.add(propertyKeyDTO);
        }
        return result;
    }

    protected SupplierRespVO getSupplierByid(Long supplierId){
        SupplierRespVO bean = HutoolBeanUtils.toBean(portalCacheService.getSupplierDTO(supplierId), SupplierRespVO.class);
        if (Objects.nonNull(bean)) {
            SupplierOtherSettingPolicyDTO settingPolicy = portalCacheService.getPartnerSupplierOtherSettingPolicy(supplierId);
            if (Objects.nonNull(settingPolicy)) {
                bean.setProductDistributionLabel(settingPolicy.getProductDistributionLabel());
            }
        }
        return bean;
    }

    protected IActivityService getActivityService() {
        return SpringUtils.getBean(IActivityService.class);
    }
}
