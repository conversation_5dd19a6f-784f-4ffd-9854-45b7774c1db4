package com.zksr.portal.service.mall;

import com.zksr.common.core.enums.ProductType;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.CategoryRespVO;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.promotion.api.activity.dto.CbRuleDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/7/10
 * @desc
 */
public interface ISaleClassService {

    /**
     * 获取本地展示分类
     * @param areaId    区域ID
     * @param channelId 渠道ID
     * @param branchId  门店ID
     * @return  本地展示分类集合
     */
    List<AreaClassDTO> getAreaClassDTOS(Long areaId, Long channelId, Long branchId);

    /**
     * 获取全国展示分类
     * @param sysCode   平台商ID
     * @param groupId   全国分组ID (全国的渠道)
     * @return  全国展示分类集合
     */
    List<SaleClassDTO> getSaleClassDTOS(Long sysCode, Long groupId);

    /**
     * 获取入驻商店铺详情管理分类列表
     * @param supplierId    入驻商Id
     * @return  管理分类集合
     */
    List<CategoryRespVO> getSupplierInfoCategoryList(Long supplierId);

    /**
     * 获取门店可见的组合促销商品
     * @param branchDTO     门店
     * @param productTypes  上架类型
     * @return  组合促销规则
     */
    List<CbRuleDTO> getBranchCbRules(BranchDTO branchDTO, ProductType... productTypes);
}
