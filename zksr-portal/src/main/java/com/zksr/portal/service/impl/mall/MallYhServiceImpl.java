package com.zksr.portal.service.impl.mall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.common.elasticsearch.service.EsYhProductService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.yh.*;
import com.zksr.portal.controller.mall.vo.yh.YhBatchSupplierSaleClassRespVO.SaleClass;
import com.zksr.portal.convert.prdt.YhDataConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.*;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.share.dto.PrdtProductSharePackageInfoRespDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.yhdata.YhDataApi;
import com.zksr.product.api.yhdata.vo.YhBatchItemVO;
import com.zksr.product.api.yhdata.vo.YhBatchListReqVO;
import com.zksr.product.api.yhdata.vo.YhBatchSaveReqVO;
import com.zksr.product.api.yhdata.vo.YhBatchSupplierSaleClassReqVO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/11 8:59
 */
@Service
@SuppressWarnings("all")
public class MallYhServiceImpl implements IMallYhService {

    @Resource
    private YhDataApi yhDataApi;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private ISkuPriceService skuPriceService;

    @Autowired
    private IActivityService activityService;

    @Autowired
    private EsYhProductService yhProductService;

    @Autowired
    private ISkuService skuService;

    @Override
    public YhBatchSupplierSaleClassRespVO yhSupplierSaleClass(YhBatchSupplierSaleClassReqVO reqVO) {
        Map<Long, List<Long>> supplierClassMap = yhDataApi.getBatchSupplierSaleClass(reqVO).getCheckedData();
        YhBatchSupplierSaleClassRespVO respVO = new YhBatchSupplierSaleClassRespVO();
        supplierClassMap.forEach((supplierId, saleClassList) -> {
            // 获取入驻商信息
            SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(supplierId);
            if (ObjectUtil.isNotEmpty(supplierDTO)) {
                YhBatchSupplierSaleClassRespVO.Supplier supplier = new YhBatchSupplierSaleClassRespVO.Supplier(supplierDTO);
                for (Long saleClassId : saleClassList) {
                    // 获取本地上架分类信息
                    AreaClassDTO areaClassDTO = portalCacheService.getAreaClassDTO(saleClassId);
                    if (Objects.nonNull(areaClassDTO)) {
                        supplier.getFirstSaleClassList().add(new SaleClass(areaClassDTO));
                    }
                }
                supplier.getFirstSaleClassList().sort(Comparator.comparing(SaleClass::getSort));
                respVO.getSupplierList().add(supplier);
            }
        });
        return respVO;
    }

    @Override
    public YhBatchListRespVO getBatchList(YhBatchListReqVO reqVO) {
        Integer pageSize = reqVO.getPageSize();
        // 门店数据
        BranchDTO branchDTO = portalCacheService.getBranchDto(reqVO.getBranchId());
        // ES分页数据
        // 因为需要计算促销, 所以需要查询全部数据, 然后做逻辑分页
        reqVO.setPageSize(NumberPool.INT_NUM5000);
        PageResult<EsYhProduct> pageResult = yhProductService.pageList(YhDataConvert.INSTANCE.convertESSearchDTO(reqVO));
        // 到这里再开始渲染丰富数据
        List<YhPageSupplierGroupItemVO> itemVOS = new ArrayList<>();
        // 图片, 限购等. 因为api调用可能会超时
        pageResult.getList().forEach(esItem -> {
            // 组装数据
            YhPageSupplierGroupItemVO itemVO = getSupplierGroupItemVO(esItem, branchDTO);
            // 添加到返回数据
            itemVOS.add(itemVO);
        });
        // 组装返回数据
        YhBatchListRespVO respVO = new YhBatchListRespVO(pageResult.getTotal(), itemVOS);
        // 渲染促销数据
        activityService.renderYhList(branchDTO, respVO);
        // 逻辑分页数据处理
        respVO.setItemList(ListUtil.page(reqVO.getPageNo() - 1, pageSize, respVO.getItemList()));
        // 标记商品是否支持负库存下单
        this.markNegativeStock(respVO.getItemList());
        // 大部分耗时操作是在数据返回阶段
        return respVO;
    }

    @Override
    public YhBatchCountRespVO batchCount(YhBatchCountReqVO countReqVO) {
        // 门店数据
        BranchDTO branchDTO = portalCacheService.getBranchDto(countReqVO.getBranchId());
        // 因为需要计算促销, 所以需要查询全部数据, 然后做逻辑分页
        YhBatchListReqVO listReqVO = YhDataConvert.INSTANCE.convertBatchListReq(countReqVO);
        listReqVO.setPageSize(NumberPool.INT_NUM5000);
        listReqVO.setChecked(NumberPool.INT_ONE);
        listReqVO.setBranchId(MallSecurityUtils.getBranchId());
        listReqVO.setSearchMode(NumberPool.INT_ONE);
        // ES分页数据
        PageResult<EsYhProduct> pageResult = yhProductService.pageList(YhDataConvert.INSTANCE.convertESSearchDTO(countReqVO));
        // 到这里再开始渲染丰富数据
        List<YhPageSupplierGroupItemVO> productList = new ArrayList<>();
        for (EsYhProduct esItem : pageResult.getList()) {
            // 构建数量
            YhPageSupplierGroupItemVO itemVO = getSupplierGroupItemVO(esItem, branchDTO);
            productList.add(itemVO);
        }
        YhBatchCountRespVO respVO = YhBatchCountRespVO
                .builder()
                .productTypeNum(productList.size())
                .productNumTotal(productList.stream().mapToInt(YhPageSupplierGroupItemVO::getProductNum).sum())
                .totalAmt(productList.stream().map(YhPageSupplierGroupItemVO::getPayAmt).reduce(BigDecimal.ZERO, BigDecimal::add))
                .build();
        // 渲染促销数据
        return activityService.renderYhCount(branchDTO, productList, respVO);
    }

    @Override
    public YhBatchStockCheckRespVO batchStockCheck(YhBatchCountReqVO countReqVO) {
        // 门店数据
        BranchDTO branchDTO = portalCacheService.getBranchDto(countReqVO.getBranchId());
        // 因为需要计算促销, 所以需要查询全部数据, 然后做逻辑分页
        YhBatchListReqVO listReqVO = YhDataConvert.INSTANCE.convertBatchListReq(countReqVO);
        listReqVO.setPageSize(NumberPool.INT_NUM5000);
        listReqVO.setChecked(NumberPool.INT_ONE);
        listReqVO.setBranchId(MallSecurityUtils.getBranchId());
        listReqVO.setSearchMode(NumberPool.INT_ONE);
        // ES分页数据
        PageResult<EsYhProduct> pageResult = yhProductService.pageList(YhDataConvert.INSTANCE.convertESSearchDTO(listReqVO));
        // 到这里再开始渲染丰富数据
        List<YhPageSupplierGroupItemVO> productList = new ArrayList<>();
        for (EsYhProduct esItem : pageResult.getList()) {
            // 构建数量
            YhPageSupplierGroupItemVO itemVO = getSupplierGroupItemVO(esItem, branchDTO);
            productList.add(itemVO);
        }
        YhBatchStockCheckRespVO respVO = new YhBatchStockCheckRespVO();
        // 计算商品大中小规格可加入购物车库存
        Map<Long, List<YhPageSupplierGroupItemVO>> skuMap = productList.stream().collect(Collectors.groupingBy(YhPageSupplierGroupItemVO::getSkuId));
        skuMap.forEach((skuId , itemList) -> {
            // 库存
            Long stockQty = itemList.get(0).getStockQty();
            if (Objects.isNull(stockQty)) {
                return;
            }
            // 先卖大单位
            // 单位类型, 1-最小单位, 2-中单位, 3-大单位
            itemList.sort(Comparator.comparing(YhPageSupplierGroupItemVO::getUnitSize).reversed());
            for (YhPageSupplierGroupItemVO groupItemVO : itemList) {
                Long stockConvertRate = groupItemVO.getStockConvertRate();
                Long maxQty = groupItemVO.getMaxQty();
                // 如果购买数量大于限购数量
                Long productNum = groupItemVO.getProductNum().longValue();
                if (Objects.nonNull(maxQty) && productNum > maxQty) {
                    productNum = maxQty;
                }
                if (Objects.nonNull(stockConvertRate)) {
                    // 有效可购买数
                    long validNum = stockQty / stockConvertRate;
                    if (validNum < productNum) {
                        // 可购买数 小于 购买数
                        productNum = validNum;
                    }
                    stockQty = stockQty - (productNum * stockConvertRate);
                }
                // 设置最大有效值
                // 有效商品数 < 购买数量
                if (productNum < groupItemVO.getProductNum() || groupItemVO.getShelfStatus() == NumberPool.INT_ZERO) {
                    YhBatchStockCheckRespVO.StockCheckItem checkItem = new YhBatchStockCheckRespVO.StockCheckItem();
                    checkItem.setRelease(groupItemVO.getShelfStatus() == NumberPool.INT_ONE);
                    checkItem.setProductNum(checkItem.getProductNum());
                    checkItem.setAvailableNum(productNum);
                    checkItem.setSpuName(groupItemVO.getSpuName());
                    checkItem.setSkuName(groupItemVO.getSpecName());
                    checkItem.setYhId(checkItem.getYhId());
                    respVO.getCheckItems().add(checkItem);
                }
            }
        });
        return respVO;
    }

    @Override
    public CommonResult<Boolean> batchYhSave(YhBatchSaveReqVO reqVO) {
        reqVO.setBranchId(MallSecurityUtils.getBranchId());
        return yhDataApi.branchBatchYhSave(reqVO);
    }

    private YhPageSupplierGroupItemVO getSupplierGroupItemVO(EsYhProduct esItem, BranchDTO branchDTO) {
        // 获取上架商品
        AreaItemDTO areaItemDTO = portalCacheService.getAreaItemDTO(esItem.getAreaItemId());
        // 获取商品内容
        ISpuItemInfoService infoService = skuService.spuItemInfoService(areaItemDTO.getItemType());
        return infoService.getYhItemInfoVO(areaItemDTO, esItem, branchDTO);
    }

    /**
     * 标记商品是否支持负库存下单
     * @param itemList
     */
    private void markNegativeStock(List<YhPageSupplierGroupItemVO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        itemList.forEach(item -> {
            Optional.ofNullable(portalCacheService.getSupplierDTO(item.getSupplierId())).ifPresent(supplierDTO -> {
                item.setIsNegativeStock(supplierDTO.getIsNegativeStock());
            });
        });
    }
}
