package com.zksr.portal.service.impl.mall;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.constant.PrdtConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.domain.dto.car.AppCarStorageDTO;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.SupplierNegativeStockEnum;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisCarConstants;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.portal.controller.mall.vo.ActivityVO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.impl.price.TradePriceCalculatorHelper;
import com.zksr.portal.service.mall.ISkuPriceService;
import com.zksr.portal.service.mall.ITradePriceService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.constant.ProductConstant;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import com.zksr.trade.api.order.vo.TrdOrderSaveVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderDtlSaveVO;
import com.zksr.trade.api.order.vo.TrdSupplierOrderSettleSaveVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zksr.common.core.constant.StatusConstants.PRICE_RESERVE_2;
import static com.zksr.common.core.constant.StatusConstants.PRICE_RESERVE_6;
import static com.zksr.common.core.enums.request.GiftPriceType.*;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.product.enums.ErrorCodeConstants.*;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_PRICE_CALCULATE_PROFIT_ERROR;

@Service
@Slf4j
public class TradePriceServiceImpl implements ITradePriceService {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Resource
    private List<TradePriceCalculatorService> priceCalculators;

    @Autowired
    private ISkuPriceService skuPriceService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private RedisService redisService;

    @Override
    public TradePriceCalculateResp calculatePrice(TradePriceCalculateRequest calculateRequest) {
        // 1.1 获得商品 SKU 数组
        List<SkuDTO> skuList = checkSkuList(calculateRequest);
        // 1.2 获得商品 SPU 数组
        List<SpuDTO> spuList = checkSpuList(skuList, calculateRequest);

        // 1.3 验证商品是否已上架 、 获取商品价格  、 获取商品上架信息
        Map<String, BigDecimal> priceMap = new HashMap<>();
        List<AreaItemDTO> areaItemList = new ArrayList<>();
        List<SupplierItemDTO> supplierItemList = new ArrayList<>();
        checkCarItemPublish(calculateRequest, priceMap, areaItemList, supplierItemList);

        // 验证组合商品 数组
        List<SpuCombineDTO> spuCobineList = checkSpuCobineList(calculateRequest);

        // 获取购物车Id存储信息
        getStroreCarList(calculateRequest);

        // 2.1 计算价格
        TradePriceCalculateResp calculateRespBO = TradePriceCalculatorHelper
                .buildCalculateResp(calculateRequest, spuList, skuList, spuCobineList, priceMap, areaItemList, supplierItemList);

        // 组合商品组装明细数据信息
        spuCobineAssembleSkuDtl(calculateRespBO);
        /**
         *  这部分应处理活动及促销价格
         *
         */
        priceCalculators.forEach(calculator -> calculator.calculate(calculateRequest, calculateRespBO));

        /**
         * 校验商品库存
         */
        checkItemInventory(calculateRespBO);

        // 重新计算下订单的优惠总金额
        TradePriceCalculatorHelper.recountAllPrice(calculateRespBO);

        // 2.2如果最终支付金额小于等于0
        if (calculateRespBO.getPrice().getPayPrice().compareTo(BigDecimal.ZERO) <= 0) {
            calculateRespBO.getPrice().setPayPrice(BigDecimal.ZERO);
        }
        return calculateRespBO;
    }

    @Override
    public TrdSupplierOrderSettleSaveVO calculateRateAmt(BranchDTO branchDto, TrdSupplierOrderDtlSaveVO dtlVo,
                                                         TrdOrderSaveVO orderSaveVO, ColonelDTO colonel, ColonelDTO pcolonel, String profitModel,
                                                         PrdtSkuSaleTotalRateVO skuSaleTotalRateVO) {
        TrdSupplierOrderSettleSaveVO sOrderSettleVo = new TrdSupplierOrderSettleSaveVO();
        sOrderSettleVo.setOrderNo(orderSaveVO.getOrderNo())
                .setSupplierOrderNo(dtlVo.getSupplierOrderNo())
                .setSupplierOrderDtlNo(dtlVo.getSupplierOrderDtlNo())
                .setSupplierId(dtlVo.getSupplierId())
                .setPayAmt(dtlVo.getTotalAmt())
        ;

        /**
         * 获取利润分成比例
         */
        CatgoryRateDTO catgoryRate = portalCacheService.getCatgoryByIdAndAreaId(dtlVo.getCategoryId(), branchDto.getAreaId());

        //软件商分润比例
        BigDecimal softwareRate = BigDecimal.ZERO;
        BigDecimal dcRate = BigDecimal.ZERO;
        BigDecimal partnerRate = BigDecimal.ZERO;
        BigDecimal colonel1Rate = BigDecimal.ZERO;
        BigDecimal colonel2Rate = BigDecimal.ZERO;
        if (StringUtils.isNotNull(catgoryRate)) {
            dcRate = StringUtils.isNull(catgoryRate.getDcRate()) ? BigDecimal.ZERO : catgoryRate.getDcRate();
            partnerRate = StringUtils.isNull(catgoryRate.getPartnerRate()) ? BigDecimal.ZERO : catgoryRate.getPartnerRate();
            //获取软件商分润比例
            PartnerDto partnerDto = portalCacheService.getPartnerDto(branchDto.getSysCode() + "");
            // 设置软件商分润比例  不为空或大于0
            softwareRate = ToolUtil.isEmpty(partnerDto.getSoftwareRate())
                    || partnerDto.getSoftwareRate().compareTo(BigDecimal.ZERO) <= NumberPool.INT_ZERO
                    ? BigDecimal.ZERO : partnerDto.getSoftwareRate();

            if (ToolUtil.isNotEmpty(orderSaveVO.getPcolonelId()))  // 当前单据号中存在业务员管理时进入
                colonel1Rate = StringUtils.isNull(catgoryRate.getColonel1Rate()) ? BigDecimal.ZERO : catgoryRate.getColonel1Rate();
            if (ToolUtil.isNotEmpty(orderSaveVO.getColonelId()))  // 当前单据号中存在业务员时进入
                colonel2Rate = StringUtils.isNull(catgoryRate.getColonel2Rate()) ? BigDecimal.ZERO : catgoryRate.getColonel2Rate();
        }

        /**
         * 获取SKU信息
         */
        SkuDTO skuDTO =portalCacheService.getSkuDTO(dtlVo.getSkuId());
        sOrderSettleVo.setItemQty(dtlVo.getTotalNum())
                .setItemPrice(dtlVo.getResPrice())
                .setItemAmt(dtlVo.getResAmt())
                // 入驻商成本价
                .setCostPrice(StringUtils.isNotNull(skuDTO.getCostPrice()) ? skuDTO.getCostPrice() : BigDecimal.ZERO)
                //入驻商金额
                .setSupplierAmt(sOrderSettleVo.getCostPrice().multiply(new BigDecimal(sOrderSettleVo.getItemQty() + "")).setScale(PRICE_RESERVE_2, RoundingMode.HALF_UP))
                // 运费
                .setTransAmt(BigDecimal.ZERO)
                // 利润 = 商品金额-入驻商金额-运费
                .setProfit(sOrderSettleVo.getItemAmt().subtract(sOrderSettleVo.getSupplierAmt()).subtract(sOrderSettleVo.getTransAmt()))
        ;

        // 当利润模式不为空，且为0时，则按利润模式比例计算利润
        if (profitModel.equals(StringPool.ZERO)) {
            BigDecimal relate = skuSaleTotalRateVO.getSkuRate(dtlVo.getSkuId()).getRate();
            if (NumberUtil.isGreaterOrEqual(relate, new BigDecimal("1"))) {
                throw exception(TRD_PRICE_CALCULATE_PROFIT_ERROR, skuDTO.getSkuId());
            }
            sOrderSettleVo.setProfit(
                    (sOrderSettleVo.getItemAmt().subtract(sOrderSettleVo.getTransAmt())).multiply(relate).setScale(PRICE_RESERVE_2, RoundingMode.DOWN)
            );
            sOrderSettleVo.setSaleTotalRate(relate);
        }
        // 如果商品是负毛利销售，则分成利润全都为0
        BigDecimal profit = NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, sOrderSettleVo.getProfit()) ? BigDecimal.ZERO : sOrderSettleVo.getProfit(); //利润总金额
        sOrderSettleVo.setProfitModel(profitModel)
                .setProfit(profit)
        ;

        /**
         *   分润计算说明
         *平台商设置的一级分润比例是30%；
         * 运营商商设置的二级分润比例分别是：
         * 运营商20%，业务员管理员30%，业务员50%。
         *
         * 假设商品利润为100元，则：
         * 平台商分润为：100 * 30% = 30元；
         * 运营商分润为：100 * （100%-30%）* 20% = 14元；
         * 业务员管理员分润为： 100 * （100%-30%）* 30% = 21元；
         * 业务员管分润为： 100 * （100%-30%）* 50% = 35元；
         *
         * 改：
         软件商分润规则说明：
         利润是100元，
         软件商10%，100*10%=10元；
         平台商30%，100*30%=30元
         运营商60%,100*（100%-40%）*60%=36元
         业务经理30%,100*（100%-40%）*30%=18元
         业务员10%，100*（100%-40%）*10%=6元
         *
         *
         * 特别说明：
         * 1.如果运营商没有设置二级分润比例，则剩余分润金额100 * （100%-30%）= 70元  全部归到运营商；
         * 2.如果订单没有业务员，则本该分给业务员的分润全部归到运营商下面；
         * 3.如果业务员有设置提成系数，假如是 提成系数为80%，按照上面的例子，实际业务员管理员分润为：100 * （100%-30%）* 30%  * 80% = 16.8元，剩余20%（21 * 20%=4.2元）业务员分润金额归到运营商下面， 如果业务员没有设置提成系数，按100% 处理；
         * 4.如果是业务员管理员产生的订单，按照上面的例子，该业务员管理员所得的分润金额为 21 + 35 = 56元
         */

        /**
         * 设置软件商、平台商 分润比例及金额
         */
        sOrderSettleVo.setSoftwareRate(softwareRate)
                // 软件商分润金额  向下取整
                .setSoftwareAmt(sOrderSettleVo.getSoftwareRate().multiply(profit).setScale(PRICE_RESERVE_2, RoundingMode.DOWN))
                // 平台商分润比例
                .setPartnerRate(partnerRate)
                // 平台商分润金额  向下取整
                .setPartnerAmt(sOrderSettleVo.getPartnerRate().multiply(profit).setScale(PRICE_RESERVE_2, RoundingMode.DOWN))
        ;

        // 剩余利润金额 = 商品利润 - 平台商分润金额 - 软件商分润金额
        profit = profit.subtract(sOrderSettleVo.getPartnerAmt()).subtract(sOrderSettleVo.getSoftwareAmt());

        /**
         * 设置运营商分润比例及金额
         */
        //总运营商分润比例 = 1 - 平台商分润比例 -软件商分润比例
        sOrderSettleVo.setAllDcRate(new BigDecimal("1").subtract(partnerRate).subtract(softwareRate))
                // 运营商设置的运营商分润比例
                .setSettingDcRate(NumberUtil.isGreater(dcRate, BigDecimal.ZERO) ? dcRate : sOrderSettleVo.getAllDcRate())
                // 运营商实际分润比例
                .setDcRate(dcRate)
        ;

        /**
         * 设置业务员管理员分润比例及金额
         */
        BigDecimal colonel1PercentagerRate = new BigDecimal("0");
        if (ToolUtil.isNotEmpty(pcolonel) && ToolUtil.isNotEmpty(pcolonel.getPercentageRate())){  // 当设置了提成系数时，还需要计算提成系数
            colonel1PercentagerRate = pcolonel.getPercentageRate().multiply(new BigDecimal("0.01"));
        }
        // 运营商设置的业务员管理员分润比例
        sOrderSettleVo.setSettingColonel1Rate(colonel1Rate)
                // 业务员管理员实际分润比例
                .setColonel1Rate(sOrderSettleVo.getAllDcRate().multiply(sOrderSettleVo.getSettingColonel1Rate()))
                // 业务员管理员提成系数
                .setColonel1Percentage(colonel1PercentagerRate)
                // 满额业务员管理员分润金额 = 订单剩余利润 * 运营商设置的业务员管理员分润比例
                .setAllColonel1Amt(profit.multiply(sOrderSettleVo.getSettingColonel1Rate()))
                // 业务员管理员分润金额  向下取整
                .setColonel1Amt(sOrderSettleVo.getAllColonel1Amt().multiply(colonel1PercentagerRate).setScale(PRICE_RESERVE_2, RoundingMode.DOWN))

        ;

        /**
         * 设置业务员分润比例及金额
         */
        BigDecimal colonel2PercentagerRate = new BigDecimal("0");
        if (ToolUtil.isNotEmpty(colonel) && ToolUtil.isNotEmpty(colonel.getPercentageRate())) {  // 当设置了提成系数时，还需要计算提成系数
            colonel2PercentagerRate = pcolonel.getPercentageRate().multiply(new BigDecimal("0.01"));
        }
        // 运营商设置的业务员分润比例
        sOrderSettleVo.setSettingColonel2Rate(colonel2Rate)
                // 业务员实际分润比例
                .setColonel2Rate(sOrderSettleVo.getAllDcRate().multiply(sOrderSettleVo.getSettingColonel2Rate()))
                // 业务员提成系数
                .setColonel2Percentage(colonel2PercentagerRate)
                // 满额业务员分润金额 = 订单剩余利润 * 运营商设置的业务员分润比例
                .setAllColonel2Amt(profit.multiply(sOrderSettleVo.getSettingColonel2Rate()))
                // 业务员分润金额  向下取整
                .setColonel2Amt(sOrderSettleVo.getAllColonel2Amt().multiply(colonel2PercentagerRate).setScale(PRICE_RESERVE_2, RoundingMode.DOWN))
        ;




        profit = profit.subtract(sOrderSettleVo.getColonel1Amt()).subtract(sOrderSettleVo.getColonel2Amt());  // 剩余利润金额 = 业务员管理员分润 - 业务员分润 - 平台商分润
        sOrderSettleVo.setDcAmt(profit);  // 运营商分润金额 = 剩下的分润金额

        return sOrderSettleVo;
    }

    @Override
    public void orderGiftShareCalculate(Long activityId, RemoteSaveOrderVO remoteSaveOrderVO) {
        // 得到这个营销活动下的所有商品（赠品以及正常品）
        List<TrdSupplierOrderDtlSaveVO> supplierOrderDtlSaveVOS = remoteSaveOrderVO.getSupplierOrderDtlSaveVOS()
                .stream().filter(dtl -> dtl.getActivityIdInfo().contains(activityId + "")).collect(Collectors.toList());

        // 得到赠品商品总金额
        BigDecimal giftTotalAmt = supplierOrderDtlSaveVOS.stream()
                .filter(dtlVo -> Objects.equals(dtlVo.getGiftFlag(), NumberPool.INT_ONE))
                .map(TrdSupplierOrderDtlSaveVO::getAfterGiftShareSubtotalAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 得到正常销售商品总金额
        BigDecimal totalAmt = supplierOrderDtlSaveVOS.stream()
                .filter(dtlVo -> Objects.equals(dtlVo.getGiftFlag(), NumberPool.INT_ZERO))
                .map(TrdSupplierOrderDtlSaveVO::getAfterGiftShareSubtotalAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal itemTotalAmt = totalAmt.add(giftTotalAmt); // 商品总金额 = 命中商品的总金额 + 赠品商品总金额
        final BigDecimal[] giftAmt = {BigDecimal.ZERO};
        AtomicInteger i = new AtomicInteger(); // 执行次数
        supplierOrderDtlSaveVOS.stream()
                .sorted(Comparator.comparing(TrdSupplierOrderDtlSaveVO::getGiftFlag))
                .forEach(supplierItem -> {
                    i.getAndIncrement();
                    // 赠品优惠分摊单价
                    BigDecimal exactGiftSharePrice = BigDecimal.ZERO;
                    // 赠品分摊价小计
                    BigDecimal giftShareSubtotal = BigDecimal.ZERO;
                    // 均摊赠品优惠后的成交价
                    BigDecimal afterGiftSharePrice = BigDecimal.ZERO;
                    // 均摊赠品优惠后的小计
                    BigDecimal afterGiftShareSubtotal = BigDecimal.ZERO;

                    if (i.get() == supplierOrderDtlSaveVOS.size()) { // 最后一个必然是赠品
                        afterGiftShareSubtotal =  totalAmt.subtract(giftAmt[0]).setScale(PRICE_RESERVE_2, RoundingMode.HALF_UP);
                        afterGiftSharePrice = afterGiftShareSubtotal.divide(supplierItem.getTotalNum(), PRICE_RESERVE_6, RoundingMode.HALF_UP);
                        exactGiftSharePrice = afterGiftSharePrice;
                    } else {
                        // 赠品优惠分摊单价（销售商品） = (商品最小单位当前单价 * 商品总金额) / 赠品总金额
                        exactGiftSharePrice = supplierItem.getAfterGiftSharePrice().divide(itemTotalAmt, PRICE_RESERVE_6, RoundingMode.HALF_UP).multiply(giftTotalAmt);
                        if (Objects.equals(supplierItem.getGiftFlag(), StatusConstants.FLAG_TRUE)) { // 赠品商品多加了一层计算
                            // 赠品优惠分摊单价（赠品商品） = 商品当前单价 - 赠品优惠分摊单价（销售商品）
                            exactGiftSharePrice = supplierItem.getSalePrice().subtract(exactGiftSharePrice);
                        }
                        // 赠品分摊价小计
                        giftShareSubtotal = exactGiftSharePrice.multiply(supplierItem.getTotalNum());



                        // 均摊赠品优惠后的成交价
                        afterGiftSharePrice = supplierItem.getAfterGiftSharePrice().subtract(exactGiftSharePrice).setScale(PRICE_RESERVE_6, RoundingMode.HALF_UP);
                        if (Objects.equals(supplierItem.getGiftFlag(), StatusConstants.FLAG_TRUE)) {
                            afterGiftSharePrice = exactGiftSharePrice;
                        }
                        afterGiftShareSubtotal = afterGiftSharePrice.multiply(supplierItem.getTotalNum()).setScale(PRICE_RESERVE_2, RoundingMode.HALF_UP);
                        if (Objects.equals(supplierItem.getGiftFlag(), StatusConstants.FLAG_TRUE)) {
                            afterGiftShareSubtotal = exactGiftSharePrice.multiply(supplierItem.getTotalNum()).setScale(PRICE_RESERVE_2, RoundingMode.HALF_UP);
                        }
                    }
                    giftAmt[0] = giftAmt[0].add(afterGiftShareSubtotal);
                    supplierItem.setExactGiftSharePrice(exactGiftSharePrice)
                            .setGiftShareSubtotalAmt(giftShareSubtotal)
                            .setAfterGiftSharePrice(afterGiftSharePrice)
                            .setAfterGiftShareSubtotalAmt(afterGiftShareSubtotal)
                            .setAfterGiftShareUnitPrice(afterGiftShareSubtotal.divide(BigDecimal.valueOf(supplierItem.getOrderUnitQty()), PRICE_RESERVE_6, RoundingMode.HALF_UP))
                    ;
                });
    }

    @Override
    public void calculateOrderDtlSkuAvgPrice(RemoteSaveOrderVO remoteSaveOrderVO) {
        remoteSaveOrderVO.getSupplierOrderDtlSaveVOS().stream()
                .collect(Collectors.groupingBy(TrdSupplierOrderDtlSaveVO::getSupplierId))  //按照入驻商分组，统计每个入驻商下的相同SKU的平均单价
                .forEach((supplierId, dtlList) -> {
                    SupplierOtherSettingPolicyDTO supplierOtherSettingPolicyDTO = portalCacheService.getPartnerSupplierOtherSettingPolicy(supplierId);
                    Integer giftPriceType;
                    if (ToolUtil.isNotEmpty(supplierOtherSettingPolicyDTO) && ToolUtil.isNotEmpty(supplierOtherSettingPolicyDTO.getGiftPriceType())) {
                        giftPriceType = Integer.valueOf(supplierOtherSettingPolicyDTO.getGiftPriceType());
                    } else {
                        giftPriceType = GIFT_PRICE_LJ.getType();
                    }
//                    OpensourceDto opensourceDto = portalCacheService.getOpensourceByMerchantId(supplierId);
                    dtlList.stream().collect(Collectors.groupingBy(TrdSupplierOrderDtlSaveVO::getSkuId)) // 入驻商下的商品信息，按照SKU分组
                            .forEach((skuId, skuDtlList) -> {
                                // 相同SKU的总金额
                                BigDecimal skuTotalAmt = skuDtlList.stream().map(TrdSupplierOrderDtlSaveVO::getAfterGiftShareSubtotalAmt).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                // 相同SKU的最小单位总数量（包括赠品数量）
                                BigDecimal skuMinTotalNum = skuDtlList.stream().map(TrdSupplierOrderDtlSaveVO::getTotalNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                BigDecimal avgPrice = skuTotalAmt.divide(skuMinTotalNum, 6, RoundingMode.HALF_UP);
                                AtomicInteger i = new AtomicInteger(); // 执行次数
                                final BigDecimal[] giftAmt = {BigDecimal.ZERO};
                                skuDtlList.forEach(dtl -> {
                                    i.getAndIncrement();
                                    dtl.setSkuAvgPrice(avgPrice)
                                            .setGiftPriceType(giftPriceType)
                                    ;

                                    if (Objects.equals(dtl.getGiftPriceType(), GIFT_PRICE_JJ.getType())) { // 平均价
                                        dtl.setResPrice(dtl.getSkuAvgPrice())
                                                // 当前商品数据的平均单价总金额 = 平均小单位单价*购买小单位数量
                                                .setResAmt(dtl.getSkuAvgPrice().multiply(dtl.getTotalNum()).setScale(PRICE_RESERVE_2, RoundingMode.HALF_UP))
                                        ;
                                        if (i.get() == skuDtlList.size()) { // 最后一个商品，得到剩余的金额
                                            dtl.setResAmt(skuTotalAmt.subtract(giftAmt[0]));
                                        }
                                        giftAmt[0] = giftAmt[0].add(dtl.getResAmt());
                                    } else if (Objects.equals(dtl.getGiftPriceType(), GIFT_PRICE_FTJ.getType())) { // 分摊价
                                        dtl.setResPrice(dtl.getAfterGiftSharePrice())
                                                .setResUnitPrice(dtl.getAfterGiftShareUnitPrice())
                                                .setResAmt(dtl.getAfterGiftShareSubtotalAmt())
                                        ;
                                    } else { // 默认取之前的价格 零价
                                        dtl.setResPrice(dtl.getExactPrice())
                                                .setResUnitPrice(dtl.getOrderSalesUnitPrice())
                                                .setResAmt(dtl.getTotalAmt())
                                        ;
                                    }
                                });

                            });
                });
    }

    /**
     * 校验商品SKU信息
     * @param reqBO
     * @return
     */
    private List<SkuDTO> checkSkuList(TradePriceCalculateRequest reqBO) {
        // 获得商品 SKU 数组
//        Map<Long, Long> skuIdCountMap = convertMap(reqBO.getItems(),
//                TradePriceCalculateRequest.Item::getSkuId, TradePriceCalculateRequest.Item::getCount);

        StringBuilder errorMsg = new StringBuilder();
        List<SkuDTO> skus = reqBO.getItems().stream()
                .filter(item -> Objects.equals(item.getGoodsType(), NumberPool.INT_ZERO)) // 这里只处理商品类型为普通商品的数据
                .map(item -> {
                    SkuDTO skuDTO = portalCacheService.getSkuDTO(item.getSkuId());
                    if (ToolUtil.isEmpty(skuDTO)) {
                        errorMsg.append(item.getSkuId()).append("不存在; ");
                        return skuDTO;
                    }
                    // 校验sku小单位 限购数量小于下单数量 （限购数量为 null或0时不校验）
                    if (UnitTypeEnum.S(item.getUnitSize()) && ToolUtil.isNotEmpty(skuDTO.getMaxOq()) && skuDTO.getMaxOq() > NumberPool.LONG_ZERO &&  item.getCount() > skuDTO.getMaxOq()) {
                        errorMsg.append(item.getSkuId()).append("小单位购买数量【").append(item.getCount()).append("】超出限购数量【").append(skuDTO.getMaxOq()).append("】; ");
                    }
                    if (UnitTypeEnum.M(item.getUnitSize()) && ToolUtil.isNotEmpty(skuDTO.getMidMaxOq()) && skuDTO.getMidMaxOq() > NumberPool.LONG_ZERO && item.getCount() > skuDTO.getMidMaxOq()) {
                        errorMsg.append(item.getSkuId()).append("中单位购买数量【").append(item.getCount()).append("】超出限购数量【").append(skuDTO.getMidMaxOq()).append("】; ");
                    }
                    if (UnitTypeEnum.L(item.getUnitSize()) && ToolUtil.isNotEmpty(skuDTO.getLargeMaxOq()) && skuDTO.getLargeMaxOq() > NumberPool.LONG_ZERO && item.getCount() > skuDTO.getLargeMaxOq()) {
                        errorMsg.append(item.getSkuId()).append("大单位购买数量【").append(item.getCount()).append("】超出限购数量【").append(skuDTO.getLargeMaxOq()).append("】; ");
                    }
                    return skuDTO;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
//        List<SkuDTO> skus = skuIdCountMap.keySet().stream().map(key -> {
//            return portalCacheService.getSkuDTO(key);
//        }).collect(Collectors.toList());

        // 校验商品 SKU
//        skus.forEach(sku -> {
//            if (sku == null) {
//                throw exception(PRDT_SKU_NOT_EXISTS);
//            }
//            Long count = skuIdCountMap.get(sku.getSkuId());
//            if (count == null) {
//                throw exception(PRDT_SKU_NOT_EXISTS);
//            }
//            Long stock = redisStockService.getSkuStock(sku.getSkuId());
//            if (count > stock) {
//                throw exception(PRDT_SKU_STOCK_NOT_ENOUGH);
//            }
//        });
        if (errorMsg.length() > 0)
            throw new ServiceException(errorMsg.toString());
        return skus;
    }

    /**
     * 校验商品SPU信息
     * @param skuList
     * @param reqBO
     * @return
     */
    private List<SpuDTO> checkSpuList(List<SkuDTO> skuList, TradePriceCalculateRequest reqBO) {
        /**
         * 多个SKU商品可能会存在相同的SPUID
         */
        Set<Long> spuSet = skuList.stream().map(SkuDTO::getSpuId).collect(Collectors.toSet());
        List<SpuDTO> spuList = spuSet.stream().map(key->{ return portalCacheService.getSpuDTO(key); }).collect(Collectors.toList());

        spuList.forEach(spu -> {
            if (StatusConstants.STATE_DISABLE.longValue() == spu.getStatus()) { //spu状态停用时进入
                throw exception(PRDT_SPU_STATE_DISABLE);
            }
        });
        return spuList;
    }

    /**
     *  验证组合商品信息
     * @param reqBO
     * @return
     */
    private List<SpuCombineDTO> checkSpuCobineList(TradePriceCalculateRequest reqBO) {
        StringBuilder errorMsg = new StringBuilder();
        List<SpuCombineDTO> spuCombineDTOList = reqBO.getItems().stream()
                .filter(item -> Objects.equals(item.getGoodsType(), NumberPool.INT_ONE))  // 这里只处理商品类型为组合商品的数据
                .map(item -> {
                    SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(item.getSpuCombineId());
                    if (ToolUtil.isEmpty(spuCombineDTO)) {
                        errorMsg.append(item.getSpuCombineId()).append("组合商品不存在; ");
                        return null;
                    }
                    item.setSpuCombineId(spuCombineDTO.getSpuCombineId());
                    return spuCombineDTO;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (errorMsg.length() > 0)
            throw new ServiceException(errorMsg.toString());
        return spuCombineDTOList;
    }

    /**
     * 组装组合商品信息
     * @param calculateRespBO
     * @return
     */
    private void spuCobineAssembleSkuDtl(TradePriceCalculateResp calculateRespBO) {
        // 组合商品组装 明细数据
        calculateRespBO.getItems().stream()
                .flatMap(item -> item.getSupplierItems().stream())
                .forEach(item -> {
                    SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(item.getSpuCombineId());
                    if (ToolUtil.isNotEmpty(spuCombineDTO) && !spuCombineDTO.getCombineDtls().isEmpty()) {
                        item.setSpuCombineSkuVOList(
                                spuCombineDTO.getCombineDtls().stream().map(combineDtl -> {
                                    SkuDTO skuDTO = portalCacheService.getSkuDTO(combineDtl.getSkuId());
                                    SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
                                    Long skuStock = redisStockService.getSurplusSaleQty(combineDtl.getSkuId());
                                    // 在当前组合条件下,能支持组合商品下单的次数
                                    Long unitSizeQty = spuDTO.getUnitSizeQty(combineDtl.getSkuUnitType()).longValue();
                                    // 最小库存 / (条件数量 * 转换比例)
                                    long maxQty = skuStock / (combineDtl.getQty() * unitSizeQty);
                                    // 组合商品详情组合数据
                                    SpuCombineSkuVO spuCombineSkuVO = ProductConvert.INSTANCE.builderCombineSkuVO(spuDTO, skuDTO, combineDtl);
                                    spuCombineSkuVO.setUnit(spuDTO.getUnit(combineDtl.getSkuUnitType()));
                                    spuCombineSkuVO.setSkuUnitType(combineDtl.getSkuUnitType());
                                    spuCombineSkuVO.setMaxQty(Integer.parseInt(String.valueOf(maxQty)));
                                    spuCombineSkuVO.setSuggestPrice(spuCombineSkuVO.getMarkPrice());
                                    spuCombineSkuVO.setSupplierItemId(combineDtl.getSupplierItemId());
                                    spuCombineSkuVO.setAreaItemId(combineDtl.getAreaItemId());
                                    spuCombineSkuVO.setDiscountAmt(BigDecimal.ZERO);
                                    spuCombineSkuVO.setOtherDiscountAmt(BigDecimal.ZERO);
                                    spuCombineSkuVO.setCouponDiscountAmt1(BigDecimal.ZERO);
                                    spuCombineSkuVO.setCouponDiscountAmt2(BigDecimal.ZERO);
                                    // 组合促销商品,子SKU生产日期调整
                                    if (Objects.nonNull(spuDTO.getLatestDate())) {
                                        spuCombineSkuVO.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                                        spuCombineSkuVO.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                                    }

                                    try {
                                        Assert.isTrue(spuDTO.isUnitExist(spuCombineSkuVO.getSkuUnitType()),
                                                "组合商品活动【{}】中的子商品【{}】所赠送的单位不存在",
                                                spuCombineDTO.getSpuCombineName(),
                                                spuDTO.getSpuName()
                                        );
                                    } catch (IllegalArgumentException e) {
                                        throw new ServiceException(e.getMessage());
                                    }

                                    return spuCombineSkuVO;
                                }).collect(Collectors.toList())
                        );
                    }
                });
    }

    /**
     * 获取商品价格 并 验证商品是否已上架
     * @param reqBO
     * @return
     */
    private Map<String, BigDecimal> checkCarItemPublish(TradePriceCalculateRequest reqBO, Map<String, BigDecimal> priceMap, List<AreaItemDTO> areaItemList, List<SupplierItemDTO> supplierItemList) {
        Map<String, TradePriceCalculateRequest.Item> cartMap =  convertMap(reqBO.getItems(), TradePriceCalculateRequest.Item::getCartId);
        cartMap.forEach( (key, value) -> {
            // 城市上架商品编号不为空时进入
            if (StringUtils.isNotNull(value.getAreaItemId()) && -1 != value.getAreaItemId()){
                AreaItemDTO areaItemDTO = portalCacheService.getAreaItemDTO(value.getAreaItemId());
                if (StringUtils.isNull(areaItemDTO))
                    throw exception(PRDT_AREA_ITEM_NOT_EXISTS);
                if (ProductConstant.PRDT_SHELF_STATUS_0 == areaItemDTO.getShelfStatus())
                    throw exception(PRDT_AREA_ITEM_NOT_SHELF_STATUS);

                BigDecimal price = BigDecimal.valueOf(9999); // 价格默认设置9999，防止价格错误导致用户付款。金额多用户不会付款
                if (Objects.equals(value.getGoodsType(), NumberPool.INT_ZERO)) { // 正常商品
                    price = skuPriceService.getAreaSkuPrice(reqBO.getBranchDto(), value.getUnitSize(), value.getSkuId());
                } else if (Objects.equals(value.getGoodsType(), NumberPool.INT_ONE)) { // 组合商品
                    SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(areaItemDTO.getSpuCombineId());
                    value.setSpuCombineId(spuCombineDTO.getSpuCombineId());
                    price = skuPriceService.getSpuCombinePrice(reqBO.getBranchDto(), spuCombineDTO, ProductType.LOCAL);
                    reqBO.getActivitys().add(
                            ActivityVO.builder()
                                    .activityId(areaItemDTO.getActivityId())
                                    .activityType(TrdDiscountTypeEnum.CB.getType())
                                    .build()
                    );
                }
                priceMap.put(value.getAreaItemId() + "-" + value.getUnitSize(), price);
                areaItemList.add(areaItemDTO);
            }
            // 入驻商上架商品编号不为空时进入
            if (StringUtils.isNotNull(value.getSupplierItemId()) && -1 != value.getSupplierItemId()) {
                SupplierItemDTO supplierItemDTO = portalCacheService.getSupplierItemDTO(value.getSupplierItemId());
                if (StringUtils.isNull(supplierItemDTO))
                    throw exception(PRDT_SUPPLIER_ITEM_NOT_EXISTS);
                if (ProductConstant.PRDT_SHELF_STATUS_0 == supplierItemDTO.getShelfStatus())
                    throw exception(PRDT_SUPPLIER_ITEM_NOT_SHELF_STATUS);

                BigDecimal price = BigDecimal.valueOf(9999); // 价格默认设置9999，防止价格错误导致用户付款。金额多用户不会付款
                if (Objects.equals(value.getGoodsType(), NumberPool.INT_ZERO)) { // 正常商品
                    price = skuPriceService.getSupplierSkuPrice(reqBO.getBranchDto(), value.getUnitSize(), value.getSkuId());
                } else if (Objects.equals(value.getGoodsType(), NumberPool.INT_ONE)) { // 组合商品
                    SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(supplierItemDTO.getSpuCombineId());
                    value.setSpuCombineId(spuCombineDTO.getSpuCombineId());
                    price = skuPriceService.getSpuCombinePrice(reqBO.getBranchDto(), spuCombineDTO, ProductType.LOCAL);
                    reqBO.getActivitys().add(
                            ActivityVO.builder()
                                    .activityId(supplierItemDTO.getActivityId())
                                    .activityType(TrdDiscountTypeEnum.CB.getType())
                                    .build()
                    );
                }
                priceMap.put(value.getSupplierItemId() + "-" + value.getUnitSize(), price);
                supplierItemList.add(supplierItemDTO);
            }
        });
        return priceMap;
    }

    /**
     * 校验下单商品库存
     */
    private void checkItemInventory(TradePriceCalculateResp calculateRespBO) {
        // 校验普通商品库存
        calculateRespBO.getItems().stream()
                .flatMap(supplierItem -> supplierItem.getSupplierItems().stream())
                .filter(supplierItem -> Objects.equals(supplierItem.getGoodsType(), NumberPool.INT_ZERO))
                .collect(Collectors.groupingBy(TradePriceCalculateResp.OrderItem.SupplierItem::getSkuId))
                .forEach((key, value) -> {
                    BigDecimal stock = redisStockService.getSurplusSaleQtyBigDecimal(key);
                    // 本次sku下单总数量
                    BigDecimal countNum = value.stream().map((item -> BigDecimal.valueOf(item.getCount()).multiply(item.getOrderUnitSize()))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    boolean isNegativeStock = value.stream().map(TradePriceCalculateResp.OrderItem.SupplierItem::getIsNegativeStock).allMatch(t -> SupplierNegativeStockEnum.NEGATIVE_STOCK.getCode().equals(t));
                    if (!isNegativeStock && countNum.compareTo(stock) > 0) {
                        throw exception(PRDT_SKU_STOCK_NOT_ENOUGH, key);
                    }
                });

        // 校验组合商品库存
        calculateRespBO.getItems().stream()
                .flatMap(supplierItem -> supplierItem.getSupplierItems().stream())
                .filter(supplierItem -> Objects.equals(supplierItem.getGoodsType(), NumberPool.INT_ONE))
                .forEach(item -> {
                    // 当前组合商品可用最大库存数量
                    int stock = item.getSpuCombineSkuVOList().stream().mapToInt(SpuCombineSkuVO::getMaxQty).min().getAsInt();
                    boolean isNegativeStock = SupplierNegativeStockEnum.NEGATIVE_STOCK.getCode().equals(item.getIsNegativeStock());
                    if (!isNegativeStock && item.getCount() > stock) {
                        throw exception(PRDT_CB_SKU_STOCK_NOT_ENOUGH, item.getSpuName());
                    }
                });
    }

    /**
     * 获取购物车Id存储信息
     * @param calculateRequest
     * @return
     */
    private void getStroreCarList(TradePriceCalculateRequest calculateRequest) {
        calculateRequest.getItems().forEach(item -> {
            AppCarIdDTO carIdDTO = AppCarIdDTO.build(item.getCartId());
            item.setCarStorageDTO(
                    AppCarStorageDTO.build(
                            redisService.getCacheMap(
                                    RedisCarConstants.getSupplierItemKey(carIdDTO.getBranchId(),
                                            carIdDTO.getType(),
                                            carIdDTO.getUnitSize(),
                                            Objects.nonNull(carIdDTO.getAreaItemId()) && carIdDTO.getAreaItemId() > 0 ? carIdDTO.getAreaItemId() :carIdDTO.getSupplierItemId())
                            ))
            );
        });
    }

}
