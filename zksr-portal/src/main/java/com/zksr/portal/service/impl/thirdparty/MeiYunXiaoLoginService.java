package com.zksr.portal.service.impl.thirdparty;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.utils.JwtUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.member.MemberApi;
import com.zksr.member.api.member.dto.MemMemberSaveReqVO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.portal.controller.mall.vo.MideaUserInfoDTO;
import com.zksr.portal.controller.mall.vo.AuthLoginResponse;
import com.zksr.portal.controller.mall.vo.UserLoginRequest;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.impl.MideaAuthCustomerService;
import com.zksr.portal.service.thirdparty.ThirdPartyLoginService;
import com.zksr.system.api.partner.dto.PartnerDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.BiFunction;

import static com.zksr.common.core.constant.StatusConstants.STATE_DISABLE;

@Service
public class MeiYunXiaoLoginService implements ThirdPartyLoginService {

    private static final Logger logger = LoggerFactory.getLogger(MeiYunXiaoLoginService.class);

    private static final String MEI_YUN_XIAO = "myx";

    @Value("${myx.apaas-base-url:https://mcsp-api-sit.midea.com/api/cms_api/mcsupply-auth}")
    private String apaasBaseUrl;

    @Value("${myx.user.query.uri:/noAccessToApaas/queryUserLoginInfoWithDataDimension}")
    private String queryMipUserUri;

    @Value("${myx.auth.jwt.key:20bdf3daa7a84c4e917e19e3109037de}")
    private String myxJWTKey;

    @Value("${myx.auth.jwt.secret:07c254b754914baca8bf8a8222ca4743}")
    private String myxJWTSecret;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private MemberApi remoteMemberApi;

    @Autowired
    private BranchApi remoteBranchApi;

    @Resource
    private MideaAuthCustomerService mideaAuthCustomerService;

    @Override
    public boolean support(String loginFrom) {
        return StringUtils.equals(MEI_YUN_XIAO, loginFrom);
    }

    @Override
    public AuthLoginResponse login(UserLoginRequest request, BiFunction<MemberDTO, BranchDTO, AuthLoginResponse> genTokenFunc) {
        PartnerDto partnerDto = portalCacheService.getPartnerDto(request.getSysSource());
        if (ToolUtil.isEmpty(partnerDto)) {
            throw new ServiceException("不存在的平台商信息");
        }
        // 解析token获取美云销用户信息
        JSONObject parseTokenResp = parseIscToken(request.getIdentity(), request.getIscToken(), Collections.emptyList());
        // 获取门店信息
        Long sysCode = partnerDto.getSysCode();
        BranchDTO branchDto = getBranchByOuterMerchantCode(sysCode, parseTokenResp);
        // 查用户信息
        MemberDTO memberDto = getMemberOrCreateMember(sysCode, parseTokenResp, branchDto);
        // 创建token
        return genTokenFunc.apply(memberDto, branchDto);
    }

    /**
     * 解析美云销传递过来的iscToken
     */
    protected @NotNull JSONObject parseIscToken(String identity, String iscToken, List<String> dimensionCodeList) {
        if (StringUtils.isAnyBlank(identity, iscToken)) {
            throw new ServiceException("登录失败，缺少身份/登录凭证");
        }
        // 组装第三方接口请求参数
        String url = apaasBaseUrl + queryMipUserUri;
        // 构造请求体
        JSONObject restParams = new JSONObject();
        restParams.put("identity", identity);
        restParams.put("iscToken", iscToken);
        if (CollUtil.isNotEmpty(dimensionCodeList)) {
            restParams.put("dataDimensionCodeList", dimensionCodeList);
        }
        try {
            // 组装请求头
            Map<String, String> header = new HashMap<>();
            header.put("Content-Type", MediaType.APPLICATION_JSON_VALUE);
            header.put("Authorization", "Bearer " + generateJwtToken());
            // 请求体
            JSONObject body = new JSONObject();
            body.put("restParams", restParams);
            String bodyStr = JSONUtil.toJsonStr(body);
            logger.info("解析美云销用户凭证接口: {}，header:{}，参数: {}", url, JSONUtil.toJsonStr(header), bodyStr);
            // 发送请求
            String postResp = HttpUtils.simplePost(url, header, bodyStr, HttpUtils.TIME_OUT_TEN);
            logger.info("解析美云销用户凭证接口响应: {}", postResp);
            if (StringUtils.isBlank(postResp)) {
                throw new ServiceException("解析用户凭证信息失败");
            }
            JSONObject respObj = JSON.parseObject(postResp);
            if (respObj == null || null == respObj.get("data")) {
                throw new ServiceException("读取用户凭证信息失败");
            }
            return JSON.parseObject(JSONUtil.toJsonStr(respObj.get("data")));
        } catch (HttpStatusCodeException e) {
            logger.error("解析美云销用户凭证接口返回错误码: {}，响应体: {}", e.getStatusCode(), e.getResponseBodyAsString(), e);
            throw new ServiceException("获取用户mip账号失败，" + e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("解析美云销用户凭证接口调用异常", e);
            throw new ServiceException("获取用户mip账号失败，" + e.getMessage());
        }
    }

    public BranchDTO getBranchByOuterMerchantCode(Long sysCode, JSONObject parseTokenResp) {
        String outerMerchantCode = parseTokenResp.getString("merchantCode");
        if (StringUtils.isBlank(outerMerchantCode)) {
            throw new ServiceException("解析登录凭证后的商户名称为空");
        }
        // 需要先维护商户和门店的绑定关系
        BranchDTO branchDto = remoteBranchApi.getBranchByOuterMerchantCode(sysCode, outerMerchantCode).getCheckedData();
        if (ObjectUtil.isNull(branchDto)) {
            throw new ServiceException("当前仅授权重庆、合肥、南昌、石家庄部分门店，其他区域敬请期待！");
        }
        return branchDto;
    }

    public MemberDTO getMemberOrCreateMember(Long sysCode, JSONObject parseTokenResp, BranchDTO branchDto) {
        String outerUserCode = parseTokenResp.getString("userCode");
        if (StringUtils.isBlank(outerUserCode)) {
            throw new ServiceException("解析登录凭证后用户为空");
        }
        // 查询用户信息
        MemberDTO memberDto = remoteMemberApi.getMemberByOuterUserCode(sysCode, outerUserCode).getCheckedData();
        // 如果用户信息为空，注册账号，并绑定当前门店
        if (ToolUtil.isEmpty(memberDto)) {
            memberDto = memberRegister(sysCode, parseTokenResp, branchDto);
        }
        // 账号校验
        if (ObjectUtil.equal(memberDto.getStatus(), STATE_DISABLE)) {
            throw new ServiceException("对不起，您的账号：" + memberDto.getUserName() + " 已停用，请联系相关人员");
        }
        // 如果没有绑定当前门店的记录，补偿新增一条
        remoteBranchApi.checkBranchUserOrCreate(sysCode, memberDto.getMemberId(), branchDto.getBranchId());
        return memberDto;
    }

    public MemberDTO memberRegister(Long sysCode, JSONObject parseTokenResp, BranchDTO branchDto) {
        // 用户编码和名称
        String outerUserCode = parseTokenResp.getString("userCode");
        String outerUserName = parseTokenResp.getString("userName");
        // 查询4A接口获取手机号
        String mobile = "";
        try {
            MideaUserInfoDTO mideaUserInfo = mideaAuthCustomerService.getUserInfoFrom4A(outerUserCode);
            if (null != mideaUserInfo) {
                mobile = mideaUserInfo.getMobile();
            }
        } catch (Exception e) {
            logger.error("用户编码查用户4a信息接口调用异常，{}", e.getMessage(), e);
        }
        // 调用注册方法(加锁)
        MemMemberSaveReqVO saveReq = new MemMemberSaveReqVO();
        saveReq.setUserName(StringUtils.defaultIfEmpty(mobile, outerUserCode));
        saveReq.setPassword(String.format("b2b@%06d", ThreadLocalRandom.current().nextInt(100000, 1000000)));
        saveReq.setMemberPhone(mobile);
        saveReq.setMemberName(outerUserName);
        saveReq.setBranchIds(Lists.newArrayList(branchDto.getBranchId()));
        saveReq.setSysCode(sysCode);
        saveReq.setOuterUserCode(outerUserCode);
        CommonResult<MemberDTO> registerResp = remoteMemberApi.createMemberCaseAutoLogin(saveReq);
        return registerResp.getCheckedData();
    }

    protected String generateJwtToken() throws Exception {
        String headerJson = "{\"alg\":\"HS256\",\"typ\":\"JWT\"}";
        // iss: 网关颁发的key, nbf: 生效时间/秒, exp: 过期时间/秒，exp-nbf <= 60s
        long startTimestamp = System.currentTimeMillis() / 1000 - 5;
        long endTimestamp = startTimestamp + 60L;
        String payloadJson = String.format("{\"iss\":\"%s\",\"nbf\":%s,\"exp\":%s}", myxJWTKey, startTimestamp, endTimestamp);
        return JwtUtils.sign(headerJson, payloadJson, myxJWTSecret);
    }
}
