package com.zksr.portal.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zksr.common.core.erpUtils.HttpUtils;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.portal.controller.mall.vo.MideaUserInfoDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Service
public class MideaAuthCustomerService {

    private static final Logger logger = LoggerFactory.getLogger(MideaAuthCustomerService.class);

    @Value("${annto.auth.base-url:https://anapi.annto.com/api-auth}")
    private String authBaseUrl;

    @Value("${annto.auth.api-uri:public/userInfo/queryFrom4A}")
    private String authApiUri;

    @Value("${annto.auth.app-id:0DzVhQort5lV}")
    private String authAppId;

    @Value("${annto.auth.app-secret:7db5355818b1d75a351bcb9219d3ec2497f76d7f}")
    private String authAppSecret;

    /**
     * 用户编码查用户4a信息接口
     */
    public MideaUserInfoDTO getUserInfoFrom4A(String userCode) {
        String url = String.format("%s/%s?userCode=%s", authBaseUrl, authApiUri, userCode);
        try {
            // 组装请求头
            Map<String, String> header = new HashMap<>();
            header.put("appid", authAppId);
            header.put("appsecret", authAppSecret);
            logger.info("用户编码查用户4a信息接口，url: {}，header:{}", url, JSONUtil.toJsonStr(header));
            // 发送请求
            String getResp = HttpUtils.sendGet(url, header, Collections.emptyMap(), HttpUtils.TIME_OUT_TEN);
            logger.info("用户编码查用户4a信息接口，结果: {}", getResp);
            if (StringUtils.isBlank(getResp)) {
                throw new ServiceException("查询4a信息异常");
            }
            JSONObject respObj = JSON.parseObject(getResp);
            if (respObj == null || null == respObj.get("data")) {
                throw new ServiceException("读取4a信息失败");
            }
            return respObj.getJSONObject("data").toJavaObject(MideaUserInfoDTO.class);
        } catch (Exception e) {
            logger.error("用户编码查用户4a信息接口调用异常，失败原因：{}", e.getMessage(), e);
            throw e;
        }
    }
}
