package com.zksr.portal.service.impl.handler.transaction;

import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.transaction.TransactionHandler;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.promotion.api.coupon.CouponApi;
import com.zksr.promotion.api.coupon.dto.CouponApplyDTO;
import com.zksr.promotion.api.coupon.dto.CouponReturnDTO;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 核销优惠券事务处理器
 */
@SuppressWarnings("rawtypes")
public class ApplyCouponTransactionHandler implements TransactionHandler {
    private final CouponApi couponApi = SpringUtils.getBean(CouponApi.class);

    private final RemoteSaveOrderVO orderVo;

    public ApplyCouponTransactionHandler(RemoteSaveOrderVO orderVo) {
        this.orderVo = orderVo;
    }

    @Override
    public void doBusiness() {
        // 核销优惠券
        if (CollectionUtils.isNotEmpty(orderVo.getOrderDiscountDtlSaveVOS())) {
            List<CouponApplyDTO> couponApplyDTOS = orderVo.getOrderDiscountDtlSaveVOS().stream()
                    .filter(orderDiscount -> orderDiscount.getDiscountType().equals(TrdDiscountTypeEnum.COUPON.getType()))
                    .map(orderDiscount -> {
                        CouponApplyDTO couponApplyDTO = new CouponApplyDTO();
                        couponApplyDTO.setCouponId(orderDiscount.getDiscountId());
                        couponApplyDTO.setRelateOrderNo(orderDiscount.getOrderNo());
                        return couponApplyDTO;
                    }).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(couponApplyDTOS)) {
                return;
            }
            // 优惠劵核销
            couponApi.applyCoupon(couponApplyDTOS).getCheckedData();
        }
    }

    @Override
    public void rollback() {
        // 退还优惠券
        if (CollectionUtils.isNotEmpty(orderVo.getOrderDiscountDtlSaveVOS())) {
            List<CouponApplyDTO> couponList = orderVo.getOrderDiscountDtlSaveVOS().stream()
                    .filter(orderDiscount -> orderDiscount.getDiscountType().equals(TrdDiscountTypeEnum.COUPON.getType()))
                    .map(orderDiscount -> {
                        CouponApplyDTO couponApplyDTO = new CouponApplyDTO();
                        couponApplyDTO.setCouponId(orderDiscount.getDiscountId());
                        couponApplyDTO.setRelateOrderNo(orderDiscount.getOrderNo());
                        return couponApplyDTO;
                    }).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(couponList)) {
                return;
            }
            CouponReturnDTO couponReturnDTO = new CouponReturnDTO();
            couponReturnDTO.setCouponList(couponList);
            couponReturnDTO.setMemo("下单失败退还优惠券");
            // 退还优惠券
            couponApi.returnCoupon(couponReturnDTO).getCheckedData();
        }
    }
}
