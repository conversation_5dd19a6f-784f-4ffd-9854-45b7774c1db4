package com.zksr.portal.service.impl.handler.transaction;

import com.zksr.common.core.transaction.TransactionHandler;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.service.RedisCacheService;
import com.zksr.trade.api.order.dto.TrdOrderResDto;
import com.zksr.trade.api.order.vo.TrdOrderInfo;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 添加待支付订单到缓存事务处理器
 */
@SuppressWarnings("rawtypes")
public class AddUnpayOrderTransactionHandler implements TransactionHandler {
    private final Supplier<TrdOrderResDto> supplier;

    private final RedisCacheService redisCacheService = SpringUtils.getBean(RedisCacheService.class);

    public AddUnpayOrderTransactionHandler(Supplier<TrdOrderResDto> supplier) {
        this.supplier = supplier;
    }

    @Override
    //!@订单 - 创建 - 5、后置处理 - 6、待支付订单到缓存
    public void doBusiness() {
        // 保存成功后，支付的订单添加到缓存中
        TrdOrderResDto orderResDto = supplier.get();
        // 如果订单金额应付金额为0
        if (Objects.nonNull(orderResDto.getPayAmt()) && orderResDto.getPayAmt().compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }
        long createTime = System.currentTimeMillis();
        if (ToolUtil.isNotEmpty(orderResDto.getCreateTime())) {
            createTime = orderResDto.getCreateTime().getTime();
        }
        // 订单保存成功，添加定时超时未支付缓存
        redisCacheService.addUnpayOrderList(new TrdOrderInfo(orderResDto.getOrderNo(), createTime, orderResDto.getSysCode(), orderResDto.getDcId()));
    }

    @Override
    public void rollback() {
        TrdOrderResDto orderResDto = supplier.get();
        long createTime = System.currentTimeMillis();
        if (ToolUtil.isNotEmpty(orderResDto.getCreateTime())) {
            createTime = orderResDto.getCreateTime().getTime();
        }
        // 删除超时未支付订单缓存
        redisCacheService.delUnpayOrderList(new TrdOrderInfo(orderResDto.getOrderNo(), createTime, orderResDto.getSysCode(), orderResDto.getDcId()));
    }
}
