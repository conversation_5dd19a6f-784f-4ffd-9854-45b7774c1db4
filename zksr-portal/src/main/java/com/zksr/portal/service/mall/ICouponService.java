package com.zksr.portal.service.mall;

import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.controller.mall.vo.coupon.*;
import com.zksr.promotion.api.coupon.dto.*;
import com.zksr.promotion.api.coupon.vo.CouponPageReqVO;
import com.zksr.promotion.api.coupon.vo.CouponSpuScopeValidReqVO;
import com.zksr.promotion.api.coupon.vo.OrderAvailableRespVO;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2024/4/1
 * @desc
 */
public interface ICouponService {

    /**
     * 获取有效优惠券列表
     * @param couponValidReq    request 请求
     * @return 返回可领取优惠券列表
     */
    List<CouponValidItemVO> getValidCouponList(CouponValidReqVO couponValidReq);

    /**
     * 获取商品可领取优惠券
     * @param couponValidReq
     * @return
     */
    List<CouponValidItemVO> getProductValidCouponList(ProductValidCouponReqVO couponValidReq);

    /**
     * 获取具体范围有效优惠券列表
     * @param spuScopeInfos 商品隔离属性
     * @param branch
     * @param supplierId
     * @param productType   商品类型 {@link com.zksr.common.core.enums.ProductType}
     * @return
     */
    List<CouponValidItemVO> getScopeValidCouponList(CouponSpuScopeValidReqVO spuScopeInfos, BranchDTO branch, Long supplierId, String productType);

    /**
     * 领取优惠券
     * @param receiveReq    request 请求
     * @return
     */
    List<CouponReceiveStatusDTO> saveReceiveCoupon(CouponReceiveReqVO receiveReq);

    /**
     * 领取优惠券
     * @param receiveReq    request 请求
     * @return
     */
    List<CouponReceiveStatusDTO> saveReceiveCoupon2(CouponReceiveReqVO receiveReq);

    /**
     * 获取一组优惠券领取结果
     * @param receiveReq
     * @return
     */
    List<CouponReceiveResultDTO> getReceiveCouponStatus(CouponReceiveResultReqVO receiveReq);

    /**
     * 获取我的优惠券列表
     * @param receiveReq    分页请求对象
     * @return  分页对象
     */
    PageResult<CouponRecordVO> getCouponList(CouponPageReqVO receiveReq);

    /**
     * 获取下单可用优惠券
     * @param req
     * @return
     */
    OrderAvailableRespVO getOrderAvailable(CouponOrderValidReqVO req);

    /**
     * 验证下单所选优惠券是否可用
     * @param orderItemList 订单商品列表
     * @param couponIdList  订单优惠券ID集合
     * @param branch     下单门店
     * @return  正常情况下方法用户下单优惠券列表
     */
    List<CouponDTO> validateCreatOrder(List<OrderValidItemDTO> orderItemList, List<Long> couponIdList, BranchDTO branch);

    /**
     * 领取优惠券
     * @param branchId  门店ID
     * @param memberId  会员ID
     * @param couponTemplateId  优惠券模版ID
     */
    Long receiveCoupon(Long branchId, Long memberId, Long couponTemplateId);

    /**
     * 加载商品参与的优惠券活动
     * @param branchDTO     门店
     * @param productType   商品类型
     * @param itemList      商品列表
     */
    void renderItemList(BranchDTO branchDTO, ProductType productType, List<SkuPageRespVO> itemList);

    /**
     * 验证优惠券命中了那些订单入驻商商品
     * @param supplierItems         入驻商商品
     * @param coupon                优惠券
     * @return  有效的商品数据
     */
    List<TradePriceCalculateResp.OrderItem.SupplierItem> validSupplierItems(List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierItems, CouponDTO coupon);

    /**
     * 过滤有效期
     * @param cacheCouponList   需要验证优惠券模版集合
     * @param branchDto         门店
     * @return  有效优惠券模版
     */
    List<CouponNormalCacheDTO> filterDate(List<CouponNormalCacheDTO> cacheCouponList, BranchDTO branchDto);

    /**
     * 过滤领取范围
     * @param cacheCouponList   需要验证优惠券模版集合
     * @param branchDto         门店
     * @return  有效优惠券模版
     */
    List<CouponNormalCacheDTO> filterReceiveScope(List<CouponNormalCacheDTO> cacheCouponList, BranchDTO branchDto);

    /**
     * 验证有效库存
     * @param cacheCouponList   需要验证优惠券模版集合
     * @param branchDto         门店
     * @return  有效优惠券模版
     */
    List<CouponNormalCacheDTO> filterStock(List<CouponNormalCacheDTO> cacheCouponList, BranchDTO branchDto);

    /**
     * 判断有没有达到领取限制
     * @param validItem     优惠券数据
     * @param branchDto     门店
     * @param ignoreDone    是否过滤已经领取完的
     * @return true-可以领取, false-不可领取
     */
    Boolean filterReceiveLimit(CouponValidItemVO validItem, BranchDTO branchDto, boolean ignoreDone);

    /**
     * 获取优惠券片段商品信息
     * @param req   优惠券信息列表
     * @return  优惠券商品片段信息
     */
    CouponTemplateFragmentRespVO getCouponSpuFragmentList(CouponTemplateFragmentReqVO req);

    /**
     * 获取优惠券详情信息
     * @param req   获取优惠券请求
     * @return  优惠券列表
     */
    List<CouponTemplateDetailRespVO> getCouponTemplateDetail(CouponTemplateDetailReqVO req);
}
