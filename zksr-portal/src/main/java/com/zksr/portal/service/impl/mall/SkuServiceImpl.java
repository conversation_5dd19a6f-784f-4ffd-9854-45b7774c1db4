package com.zksr.portal.service.impl.mall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.PrmFuncScopeEnum;
import com.zksr.common.core.enums.PrmNoEnum;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.SupplierNegativeStockEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.elasticsearch.domain.EsProduct;
import com.zksr.common.elasticsearch.domain.EsProductGroup;
import com.zksr.common.elasticsearch.model.dto.ProductSearchDTO;
import com.zksr.common.elasticsearch.model.dto.SearchRecommendRate;
import com.zksr.common.elasticsearch.service.EsProductService;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.searchHis.SearchHisApi;
import com.zksr.member.api.searchHis.vo.SearchHisReqVO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.prdt.ActivitySkuSearchReqVO;
import com.zksr.portal.controller.mall.vo.prdt.CouponSkuSearchReqVO;
import com.zksr.portal.controller.mall.vo.spu.GuessLikeReqVO;
import com.zksr.portal.controller.mall.vo.spu.SpuItemListReqVO;
import com.zksr.portal.controller.mall.vo.spu.SupplierListSpuReqVO;
import com.zksr.portal.controller.mall.vo.spu.SupplierListSpuRespVO;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.*;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.blockScheme.BlockSchemeApi;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.OrderSkuDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierClass.SupplierClassApi;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.product.constant.ProductConstant;
import com.zksr.product.utils.ReleaseItemUtil;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerPolicy.dto.AppletAgreementPolicyDTO;
import com.zksr.system.api.supplier.SupplierApi;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.supplier.vo.SysSupplierPageReqVO;
import com.zksr.trade.api.order.vo.StoreProductRequest;
import com.zksr.trade.api.order.vo.StoreProductRespVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_AREA_ITEM_NOT_EXISTS;
import static com.zksr.product.enums.ErrorCodeConstants.PRDT_SUPPLIER_ITEM_NOT_EXISTS;

@Service
@Slf4j
@SuppressWarnings("all")
public class SkuServiceImpl implements ISkuService {

    @Resource
    private ProductContentApi productContentApi;

    @Autowired
    private ISkuPriceService skuPriceService;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IActivityService activityService;

    @Autowired
    private ICouponService couponService;

    @Autowired
    private ISaleClassService saleClassService;

    @Resource
    private SupplierClassApi supplierClassApi;

    @Resource
    private SkuApi skuApi;

    @Resource
    private SupplierApi remoteSupplierApi;

    @Resource
    private BlockSchemeApi blockSchemeApi;

    @Resource
    private SearchHisApi searchHisApi;

    @Autowired
    private EsProductService esProductService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RedisActivityService redisActivityService;

    @Override
    public PageResult<SkuPageRespVO> areaSpuPage(@Valid SpuPageReqVO pageVO) {
        try {
            log.info(" SkuServiceImpl.areaSpuPage,LoginMember,{}", JsonUtils.toJsonString(MallSecurityUtils.getLoginMember()));
            //获取门店信息
            BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getLoginMember().getBranchId());
            //获取商品数据
            EsPageInfo<EsProductGroup> elasticSearchListResult = esProductService.search(buildLocalSpuPageReqVO(branchDTO, pageVO), false);
            log.debug("SkuServiceImpl.areaSpuPage,elasticSearchListResult,{}", JsonUtils.toJsonString(elasticSearchListResult));
            if (null == elasticSearchListResult) {
                return PageResult.result(0L, new ArrayList<SkuPageRespVO>());
            }
            List<SkuPageRespVO> pageList = ProductConvert.INSTANCE.convertSpuPageRespList(elasticSearchListResult.getList());
            if (CollectionUtils.isEmpty(pageList)) {
                return PageResult.result(0L, new ArrayList<SkuPageRespVO>());
            }
            List<SkuPageRespVO> deleteList = new ArrayList<>();
            //获取销售价和库存
            if (ToolUtil.isNotEmpty(pageList)) {
                for (SkuPageRespVO product : pageList) {
                    SkuDTO skuDTO = portalCacheService.getSkuDTO(product.getSkuId());
                    SpuDTO spuDTO = portalCacheService.getSpuDTO(product.getSpuId());
                    if (null == skuDTO || null == spuDTO) {
                        deleteList.add(product);
                        continue;
                    }
                    // 渲染商品详情信息
                    // 分为普通商品和组合商品
                    ISpuItemInfoService itemInfoService = this.spuItemInfoService(product.getItemType());
                    itemInfoService.renderItemList(product, branchDTO);
                }
            }
            if (CollectionUtils.isNotEmpty(deleteList)) {
                pageList.removeAll(deleteList);
                log.info(" 需要删除的数据,{}", JsonUtils.toJsonString(deleteList));
            }

            // 渲染促销活动
            activityService.renderItemList(branchDTO, ProductType.LOCAL, pageList);
            // 查看商品参与的优惠券
            couponService.renderItemList(branchDTO, ProductType.LOCAL, pageList);
            //经营屏蔽
            if (Objects.nonNull(branchDTO) && CollectionUtils.isNotEmpty(pageList)) {
                pageList = this.blockSkus(pageList, branchDTO.getBranchId());
            }

            //设置分页数据
            return PageResult.result(elasticSearchListResult.getTotal(), pageList);
        } catch (ServiceException e) {
            log.error(" areaSpuPage异常", e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error(" areaSpuPage异常1,", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public SpuDetailRespVO areaSpuDetail(Long areaItemId) {
        //获取门店信息
        BranchDTO branchDto = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        // 获取相关上架信息
        AreaItemDTO areaItemDTO = portalCacheService.getAreaItemDTO(areaItemId);
        if (ToolUtil.isEmpty(areaItemDTO)) {
            throw exception(PRDT_AREA_ITEM_NOT_EXISTS);
        }
        // 获取具体的商品数据
        ISpuItemInfoService infoService = this.spuItemInfoService(areaItemDTO.getItemType());
        SpuDetailRespVO detailReslut = infoService.getItemInfo(branchDto, areaItemDTO);
        // 查看当前商品主SKU 绑定的促销
        activityService.renderItemDetail(branchDto, ProductType.LOCAL, detailReslut);

        // 标记商品是否支持负库存下单
        Optional.ofNullable(portalCacheService.getSupplierDTO(detailReslut.getSupplierId())).ifPresent(supplierDTO -> {
            detailReslut.setIsNegativeStock(supplierDTO.getIsNegativeStock());
        });
        return detailReslut;
    }

    @Override
    public PageResult<SkuPageRespVO> supplierSpuPage(SpuPageReqVO pageVO) {
        //获取门店信息
        BranchDTO branchDTO = branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getLoginMember().getBranchId());
        PageResult<SkuPageRespVO> pageResult = new PageResult<>();
        //获取商品数据
        EsPageInfo<EsProductGroup> elasticSearchListResult = esProductService.search(buildGlobalSpuPageReqVO(branchDTO, pageVO), false);
        List<SkuPageRespVO> pageList = ProductConvert.INSTANCE.convertSpuPageRespList(elasticSearchListResult.getList());
        //获取销售价和库存
        if (ToolUtil.isNotEmpty(pageList)) {
            for (SkuPageRespVO product : pageList) {
                // 渲染商品详情信息
                // 分为普通商品和组合商品
                ISpuItemInfoService itemInfoService = this.spuItemInfoService(product.getItemType());
                itemInfoService.renderItemList(product, branchDTO);
            }
        }
        //经营屏蔽
        if (Objects.nonNull(branchDTO)) {
            pageList = this.blockSkus(pageList, branchDTO.getBranchId());
        }

        // 渲染促销活动
        activityService.renderItemList(branchDTO, ProductType.GLOBAL, pageList);
        // 查看商品参与的优惠券
        couponService.renderItemList(branchDTO, ProductType.GLOBAL, pageList);
        //设置分页数据
        return PageResult.result(elasticSearchListResult.getTotal(), pageList);
    }

    @Override
    public SpuDetailRespVO supplierSpuDetail(Long supplierItemId) {
        //获取门店信息
        BranchDTO branchDto = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        // 获取相关上架信息
        SupplierItemDTO supplierItemDTO = portalCacheService.getSupplierItemDTO(supplierItemId);
        if (ToolUtil.isEmpty(supplierItemDTO)) {
            throw exception(PRDT_SUPPLIER_ITEM_NOT_EXISTS);
        }
        // 获取具体的商品数据
        ISpuItemInfoService infoService = spuItemInfoService(supplierItemDTO.getItemType());
        SpuDetailRespVO detailReslut = infoService.getItemInfo(branchDto, supplierItemDTO);
        // 查看当前商品主SKU 绑定的促销
        activityService.renderItemDetail(branchDto, ProductType.GLOBAL, detailReslut);
        return detailReslut;
    }

    @Deprecated
    @Override
    public List<SkuPageRespVO> areaItemSpuDetailList(SpuItemListReqVO reqVO) {
        reqVO.setItemIds(reqVO.getAreaItemIds());
        reqVO.setAreaItemIds(null);
        return spuItemListDetailList(reqVO);
    }

    @Override
    @Deprecated
    public List<SkuPageRespVO> spuItemListDetailList(SpuItemListReqVO reqVO) {
        log.info(" SkuServiceImpl.spuItemListDetailList,LoginMember,{}", JsonUtils.toJsonString(MallSecurityUtils.getLoginMember()));
        //配置查询商品的参数
        ProductContentPageReqDTO pageReqDTO = HutoolBeanUtils.toBean(reqVO, ProductContentPageReqDTO.class);
        pageReqDTO.setSysCode(MallSecurityUtils.getLoginMember().getSysCode());
        if (Objects.isNull(reqVO.getItemIds()) || reqVO.getItemIds().isEmpty()) {
            return ListUtil.empty();
        }
        // 数据转换支持老接口
        List<SpuItemListReqVO.ItemUnitSize> itemUnitSizeList = reqVO.getItemIds().stream().map(itemId -> {
            SpuItemListReqVO.ItemUnitSize unitSize = new SpuItemListReqVO.ItemUnitSize();
            unitSize.setItemId(itemId);
            return unitSize;
        }).collect(Collectors.toList());
        reqVO.setItemIds(null);
        reqVO.setItemUnitList(itemUnitSizeList);
        return spuItemUnitListDetailList(reqVO);
    }


    @Override
    public List<SkuPageRespVO> spuItemUnitListDetailList(SpuItemListReqVO reqVO) {
        //配置查询商品的参数
        ProductContentPageReqDTO pageReqDTO = HutoolBeanUtils.toBean(reqVO, ProductContentPageReqDTO.class);
        pageReqDTO.setSysCode(MallSecurityUtils.getLoginMember().getSysCode());

        // 先查询上架发布商品
        Set<Long> itemIds = reqVO.getItemUnitList().stream().map(SpuItemListReqVO.ItemUnitSize::getItemId).collect(Collectors.toSet());
        pageReqDTO.setItemIds(new ArrayList<>(itemIds));
        if (Objects.isNull(pageReqDTO.getItemIds()) || pageReqDTO.getItemIds().isEmpty()) {
            return ListUtil.empty();
        }
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        // 渲染规格参数
        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, SysDictData> unitMap = (Objects.nonNull(dictCache) ? dictCache : new ArrayList<SysDictData>()).stream().collect(Collectors.toMap(SysDictData::getDictValue, item -> item));

        // 获取全国展示分类, 使用展示分类去隔离搜索结果
        if (Objects.nonNull(branchDTO)) {
            List<SaleClassDTO> saleClassDTOS = saleClassService.getSaleClassDTOS(branchDTO.getSysCode(), branchDTO.getGroupId());
            List<AreaClassDTO> areaClassDTOS = saleClassService.getAreaClassDTOS(branchDTO.getAreaId(), branchDTO.getChannelId(), branchDTO.getBranchId());
            ArrayList<Long> saleClassIdList = new ArrayList<>();
            if (Objects.nonNull(saleClassDTOS)) {
                saleClassIdList.addAll(saleClassDTOS.stream().map(SaleClassDTO::getSaleClassId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            if (Objects.nonNull(areaClassDTOS)) {
                saleClassIdList.addAll(areaClassDTOS.stream().map(AreaClassDTO::getAreaClassId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            // 默认限定
            saleClassIdList.add(NumberPool.LOWER_GROUND_LONG);
            pageReqDTO.setClass3Id(saleClassIdList);
        }

        //获取商品数据
        ProductSearchDTO searchDTO = ProductConvert.INSTANCE.convertProductSearchDTO(pageReqDTO);
        // 获取经营屏蔽的SkuIds
        if (Objects.nonNull(branchDTO)) {
            pageReqDTO.setBlockSkus(blockSchemeApi.getBlockSkusByBranchId(branchDTO.getBranchId()).getCheckedData());
        }
        List<EsProduct> contentDTOList = esProductService.searchFullList(searchDTO);
//        log.info(" contentDTOList,{}",JsonUtils.toJsonString(contentDTOList));
        List<SkuPageRespVO> itemList = new ArrayList<>();
        Map<Long, List<EsProduct>> productDTOMap = contentDTOList.stream().collect(Collectors.groupingBy(EsProduct::getItemId));

        for (SpuItemListReqVO.ItemUnitSize itemUnit : reqVO.getItemUnitList()) {
            if (itemUnit.getItemType() == NumberPool.INT_ZERO && productDTOMap.containsKey(itemUnit.getItemId())) {
                // 上架商品
                EsProduct contentDTO = productDTOMap.get(itemUnit.getItemId()).get(0);
                // 指定单位, 如果没有指定单位, 优先 小单位 > 中单位 > 大单位
                Integer unitSize = Objects.isNull(itemUnit.getUnitSize()) ? contentDTO.getUnitSize() : itemUnit.getUnitSize();

                SkuDTO skuDTO = portalCacheService.getSkuDTO(contentDTO.getSkuId());
                SpuDTO spuDTO = portalCacheService.getSpuDTO(contentDTO.getSpuId());
                // 数据转换
                SkuPageRespVO item = ProductConvert.INSTANCE.convertSpuPageResp(contentDTO);
                // 设置, 起订, 起订组数, 最大限购, 单位数据
                item.setMinOq(skuDTO.getMinOq(unitSize));
                item.setJumpOq(skuDTO.getJumpOq(unitSize));
                item.setMaxOq(skuDTO.getMaxOq(unitSize));
                item.setUnit(spuDTO.getUnit(unitSize));
                // 设置库存 / 除以当前商品比例
                item.setStock(
                        StockUtil.stockDivide(redisStockService.getSurplusSaleQtyBigDecimal(contentDTO.getSkuId()), spuDTO.stockConvert(unitSize))
                );
                item.setUnitSize(unitSize);
                if (ProductType.isGlobal(contentDTO.getType())) {
                    // 设置(全国)销售价
                    item.setMarkPrice(skuPriceService.getSupplierSkuPrice(branchDTO, unitSize, contentDTO.getSkuId()));
                    SupplierItemDTO supplierItemDTO = portalCacheService.getSupplierItemDTO(contentDTO.getItemId());
                    // 如果没有上架直接跳过
                    if (!ReleaseItemUtil.validateRelease(supplierItemDTO, unitSize)) {
                        continue;
                    }
                    item.setShelfStatus(supplierItemDTO.getShelfStatus(unitSize));
                } else {
                    // 设置(本地)销售价
                    item.setMarkPrice(skuPriceService.getAreaSkuPrice(branchDTO, unitSize, contentDTO.getSkuId()));
                    AreaItemDTO areaItemDTO = portalCacheService.getAreaItemDTO(contentDTO.getItemId());
                    // 如果没有上架直接跳过
                    if (!ReleaseItemUtil.validateRelease(areaItemDTO, unitSize)) {
                        continue;
                    }
                    item.setShelfStatus(areaItemDTO.getShelfStatus(unitSize));
                }
                // 判断是否多单位, 上架ID唯一已经排除多规格
                if (contentDTO.getMinShelfStatus() + contentDTO.getMidShelfStatus() + contentDTO.getLargeShelfStatus() > 0) {
                    item.setIsSpecs(NumberPool.LONG_ONE);
                } else {
                    item.setIsSpecs(NumberPool.LONG_ZERO);
                }
                // 渲染规格
                if (unitMap.containsKey(item.getUnit())) {
                    item.setUnitName(unitMap.get(item.getUnit()).getDictLabel());
                }
                itemList.add(item);
            }
        }

        // 组合促销商品获取
        // 查询组合商品
        List<Long> validateAcitivtyId = null;
        if (Objects.nonNull(branchDTO)) {
            List<CbRuleDTO> cbAcitivtyList = saleClassService.getBranchCbRules(branchDTO, ProductType.LOCAL, ProductType.GLOBAL);
            validateAcitivtyId = cbAcitivtyList.stream().map(CbRuleDTO::getActivityId).filter(Objects::nonNull).collect(Collectors.toList());
        }
        List<Long> requestActivityId = new ArrayList<>();
        for (SpuItemListReqVO.ItemUnitSize spuCombine : reqVO.getItemUnitList().stream().filter(item -> item.getItemType() == NumberPool.INT_ONE).collect(Collectors.toList())) {
            Long activityId = null;
            if (ProductType.LOCAL.getType().equals(spuCombine.getProductType())) {
                AreaItemDTO areaItemDTO = portalCacheService.getAreaItemDTO(spuCombine.getItemId());
                activityId = areaItemDTO.getActivityId();
            } else {
                SupplierItemDTO supplierItemDTO = portalCacheService.getSupplierItemDTO(spuCombine.getItemId());
                activityId = supplierItemDTO.getActivityId();
            }
            requestActivityId.add(activityId);
        }
        // 如果有门店登陆的, 那就需要过滤无效的活动
        if (Objects.nonNull(validateAcitivtyId)) {
            log.info(" validateAcitivtyId:{},requestActivityId:{}", JsonUtils.toJsonString(validateAcitivtyId), JsonUtils.toJsonString(requestActivityId));
            requestActivityId = validateAcitivtyId.stream().filter(requestActivityId::contains).collect(Collectors.toList());
        }
        // 还有有效的活动, 就取获取组合促销商品
        if (!requestActivityId.isEmpty()) {
            searchDTO.setActivityIdList(requestActivityId);
            searchDTO.setOrActivityId(requestActivityId);
            // 获取数据
            EsPageInfo<EsProductGroup> elasticSearchListResult = esProductService.search(searchDTO, false);
            // 转换数据实体
            List<SkuPageRespVO> pageList = ProductConvert.INSTANCE.convertSpuPageRespList(elasticSearchListResult.getList());
            // 渲染组合促销数据
            pageList.forEach(product -> spuCombineService.renderItemList(product, branchDTO));
            itemList.addAll(pageList);
        }

        log.info(" itemList,{}", JsonUtils.toJsonString(itemList));
        // 查看当前商品主SKU 绑定的促销
        itemList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getType)).forEach((productType, productList) -> {
            activityService.renderItemList(branchDTO, ProductType.formValue(productType), productList);
        });
        // 经营屏蔽：门店进入首页屏蔽指定商品
        if (Objects.nonNull(branchDTO)) {
            itemList = this.blockSkus(itemList, branchDTO.getBranchId());
        }
        // 标记商品是否支持负库存下单
        this.markNegativeStock(itemList);
        return itemList;
    }

    @Override
    public PageResult<SkuPageRespVO> searchSpuDetailList(ProductContentPageReqDTO pageReqDTO) {
        log.info(" SkuServiceImpl.searchSpuDetailList,LoginMember,{}", JsonUtils.toJsonString(MallSecurityUtils.getLoginMember()));
        log.debug(" SkuServiceImpl.searchSpuDetailList,pageReqDTO,{}", JsonUtils.toJsonString(pageReqDTO));
        //配置查询商品的参数
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getLoginMember().getBranchId());
        //没有门店或者城市就只查全国的
        if (ToolUtil.isEmpty(branchDTO) || ToolUtil.isEmpty(branchDTO.getAreaId())) {
            pageReqDTO.setProductType(ProductType.GLOBAL.getType());
        }
        //获取商品数据
        ProductSearchDTO searchDTO = buildSpuPageReqVO(branchDTO, ProductConvert.INSTANCE.convertProductSearchDTO(pageReqDTO));
        searchDTO.setDistinctBySpuId(true);
        EsPageInfo<EsProductGroup> elasticSearchListResult = esProductService.search(searchDTO, true);
        log.info("SkuServiceImpl.searchSpuDetailList.elasticSearchListResult,{}", JsonUtils.toJsonString(elasticSearchListResult));

        List<SkuPageRespVO> pageList = ProductConvert.INSTANCE.convertSpuPageRespList(elasticSearchListResult.getList());
        //获取销售价和库存
        if (ToolUtil.isNotEmpty(pageList)) {
            for (SkuPageRespVO product : pageList) {
                // 渲染商品详情信息
                // 分为普通商品和组合商品
                ISpuItemInfoService itemInfoService = this.spuItemInfoService(product.getItemType());
                itemInfoService.renderItemList(product, branchDTO);
            }
        }
        pageList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getType)).forEach((productType, itemList) -> {
            // 查看当前商品主SKU 绑定的促销
            activityService.renderItemList(branchDTO, ProductType.isGlobal(productType) ? ProductType.GLOBAL : ProductType.LOCAL, itemList);
        });

        //经营屏蔽
        if (Objects.nonNull(branchDTO)) {
            pageList = this.blockSkus(pageList, branchDTO.getBranchId());
        }

        // 记录搜索历史, 搜索的不大
        // 只记录第一次搜索, 下拉分页就不需要再记录了
        if (StringUtils.isNotEmpty(pageReqDTO.getCondition()) && Objects.nonNull(branchDTO) && pageReqDTO.getPageNo() == NumberPool.INT_ONE) {
            SearchHisReqVO searchHisReqVO = SearchHisReqVO.builder()
                    .words(StrUtil.maxLength(pageReqDTO.getCondition(), 20))
                    .sysCode(branchDTO.getSysCode())
                    .memberId(MallSecurityUtils.getMemberId())
                    .branchId(branchDTO.getBranchId()).build();
            searchHisApi.addSearchHis(searchHisReqVO);
        }
        log.info("SkuServiceImpl.searchSpuDetailList result: {}", JSON.toJSONString(pageList));

        // 标记商品是否支持负库存下单
        this.markNegativeStock(pageList);

        //设置分页数据
        return PageResult.result(elasticSearchListResult.getTotal(), pageList);
    }

    @Override
    public SpuSearchPropertiesVO searchSpuPropertiesList(ProductContentPageReqDTO pageReqDTO) {
        log.info(" SkuServiceImpl.searchSpuDetailList,LoginMember,{}", JsonUtils.toJsonString(MallSecurityUtils.getLoginMember()));

        //配置查询商品的参数
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getLoginMember().getBranchId());
        //没有门店或者城市就只查全国的
        if (ToolUtil.isEmpty(branchDTO) || ToolUtil.isEmpty(branchDTO.getAreaId())) {
            pageReqDTO.setProductType(ProductType.GLOBAL.getType());
        }
        //获取商品数据
        ProductSearchDTO searchDTO = buildSpuPageReqVO(branchDTO, ProductConvert.INSTANCE.convertProductSearchDTO(pageReqDTO));
        searchDTO.setDistinctBySpuId(true);
        List<EsProductGroup> esProductGroupList = esProductService.searchAll(searchDTO);
        if (ToolUtil.isEmpty(esProductGroupList)) {
            return new SpuSearchPropertiesVO();
        }
        log.debug("SkuServiceImpl.searchSpuDetailList esProductGroupList: {}", JSON.toJSONString(esProductGroupList));

        Map<Long, List<EsProductGroup>> brandMap = esProductGroupList.stream().filter(s -> ObjectUtil.isNotNull(s.getBrandId())).collect(Collectors.groupingBy(EsProductGroup::getBrandId));
        List<SpuSearchPropertiesVO.BrandProperties> brandPropertiesList = Lists.newArrayList();
        for (Map.Entry<Long, List<EsProductGroup>> entry : brandMap.entrySet()) {
            Long brandId = entry.getKey();
            List<EsProductGroup> bandList = brandMap.get(brandId);
            SpuSearchPropertiesVO.BrandProperties brandProperties = new SpuSearchPropertiesVO.BrandProperties();
            brandProperties.setBrandId(brandId);
            brandProperties.setBrandName(bandList.get(0).getBrandName());
            brandPropertiesList.add(brandProperties);
        }
        Map<Long, List<EsProductGroup>> classMap = esProductGroupList.stream().filter(s -> StringUtils.isNotNull(s.getClass1Id())).collect(Collectors.groupingBy(EsProductGroup::getClass1Id));
        List<SpuSearchPropertiesVO.ClassProperties> classPropertiesList = Lists.newArrayList();

        for (Map.Entry<Long, List<EsProductGroup>> entry : classMap.entrySet()) {
            Long classId = entry.getKey();
            List<EsProductGroup> classList = classMap.get(classId);
            SpuSearchPropertiesVO.ClassProperties classProperties = new SpuSearchPropertiesVO.ClassProperties();
            classProperties.setClassId(classId);
            classProperties.setClassName(classList.get(0).getClassName());
            classPropertiesList.add(classProperties);
        }
        Map<Long, List<EsProductGroup>> supplierMap = esProductGroupList.stream().filter(s -> StringUtils.isNotNull(s.getSupplierId())).collect(Collectors.groupingBy(EsProductGroup::getSupplierId));
        List<SpuSearchPropertiesVO.SupplierProperties> supplierPropertiesList = Lists.newArrayList();
        for (Map.Entry<Long, List<EsProductGroup>> entry : supplierMap.entrySet()) {
            Long supplierId = entry.getKey();
            List<EsProductGroup> supplierList = supplierMap.get(supplierId);
            SpuSearchPropertiesVO.SupplierProperties supplierProperties = new SpuSearchPropertiesVO.SupplierProperties();
            supplierProperties.setSupplierId(supplierId);
            supplierProperties.setSupplierName(supplierList.get(0).getSupplierName());
            supplierPropertiesList.add(supplierProperties);
        }
        SpuSearchPropertiesVO spuSearchPropertiesVO = new SpuSearchPropertiesVO();
        spuSearchPropertiesVO.setBrandList(brandPropertiesList);
        spuSearchPropertiesVO.setClassList(classPropertiesList);
        spuSearchPropertiesVO.setSupplierList(supplierPropertiesList);

        log.debug("SkuServiceImpl.searchSpuPropertiesList result: {}", JSON.toJSONString(spuSearchPropertiesVO));
        return spuSearchPropertiesVO;
    }

    /**
     * 根据门店和SKU集合过滤掉不符合当前传入门店的商品信息
     *
     * @param orderSkuDTOS
     * @param branchDto
     */
    @Override
    public List<OrderSkuDTO> branchItemFilter(List<OrderSkuDTO> orderSkuDTOS, BranchDTO branchDto, PartnerDto partnerDto) {
        /**
         * 根据门店获取本地分类
         */
        List<AreaClassDTO> areaClassDTOList = new ArrayList<>();
        List<AreaClassDTO> areaClassSupplier = new ArrayList<>();
        AreaDTO areaDto = portalCacheService.getAreaDto(branchDto.getAreaId());
        if (ObjectUtil.isNotNull(areaDto) || !ObjectUtil.equal(areaDto.getLocalFlag(), NumberPool.INT_ZERO)) {
            //根据门店区域和门店渠道获取城市展示分类
            areaClassDTOList = portalCacheService.getAreaClassAreaChannel(branchDto.getAreaId(), branchDto.getChannelId());
            if (ObjectUtil.isNull(areaClassDTOList))
                areaClassDTOList = new ArrayList<>();

            //根据门店经纬度匹配入驻商，查询入驻商查询所匹配到的电子围栏分类
            if (ObjectUtil.isNotNull(branchDto)) {
                areaClassSupplier = portalCacheService.getAreaClassBranch(branchDto.getBranchId());
                if (ToolUtil.isEmpty(areaClassSupplier))
                    areaClassSupplier = new ArrayList<>();
            }
        }
        List<AreaClassDTO> areaList = Stream.of(areaClassDTOList, areaClassSupplier)
                .flatMap(Collection::stream).distinct().sorted(Comparator.comparing(AreaClassDTO::getSort)).collect(Collectors.toList());


        /**
         *  根据平台商获取全国分类
         */
        List<SaleClassDTO> resList = portalCacheService.getSaleClassListBySysCode(partnerDto.getSysCode());


        Map<Long, AreaClassDTO> areaClassMap = convertMap(areaList, AreaClassDTO::getAreaClassId, AreaClassDTO -> AreaClassDTO);
        Map<Long, SaleClassDTO> saleClassMap = convertMap(resList, SaleClassDTO::getSaleClassId, SaleClassDTO -> SaleClassDTO);

        List<OrderSkuDTO> resultOrderSkuDtoList = new ArrayList<>();
        orderSkuDTOS.stream().forEach(orderSku -> {
            if (orderSku.getItemType().equals(1)) { //本地商品
                AreaItemDTO areaItemDTO = portalCacheService.getAreaItemDTO(orderSku.getAreaItemId());
                if (StringUtils.isNull(areaItemDTO) || ProductConstant.PRDT_SHELF_STATUS_0 == areaItemDTO.getShelfStatus()) // 当本地商品上架不存在或者已下架时进入
                    return;
                if (areaClassMap.containsKey(areaItemDTO.getAreaClassId())) {
                    resultOrderSkuDtoList.add(orderSku);
                    return;
                }
            }
            if (orderSku.getItemType().equals(0)) { //全国商品
                SupplierItemDTO supplierItemDTO = portalCacheService.getSupplierItemDTO(orderSku.getSupplierItemId());
                if (StringUtils.isNull(supplierItemDTO) || ProductConstant.PRDT_SHELF_STATUS_0 == supplierItemDTO.getShelfStatus()) // 当全国商品上架不存在或者已下架时进入
                    return;
                if (saleClassMap.containsKey(supplierItemDTO.getSaleClassId())) {
                    resultOrderSkuDtoList.add(orderSku);
                    return;
                }
            }
        });
        return resultOrderSkuDtoList;
    }

    /**
     * 根据商品类型获取商品列表
     *
     * @param pageVO 分页请求参数对象
     * @return 商品列表的分页结果
     */
    @Override
    public PageResult<SkuPageRespVO> getProductListByType(SpuPageReq2VO pageVO) {
        log.info(" SkuServiceImpl.searchSpuDetailList,LoginMember,{}", JsonUtils.toJsonString(MallSecurityUtils.getLoginMember()));

        ProductContentPageReqDTO pageReqDTO = HutoolBeanUtils.toBean(pageVO, ProductContentPageReqDTO.class);
        //获取门店信息
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        //获取商品数据
        EsPageInfo<EsProductGroup> elasticSearchListResult = esProductService.search(buildSpuPageReqVO(branchDTO, ProductConvert.INSTANCE.convertProductSearchDTO(pageReqDTO)), false);
        List<SkuPageRespVO> pageList = ProductConvert.INSTANCE.convertSpuPageRespList(elasticSearchListResult.getList());
        //获取销售价和库存
        if (ToolUtil.isNotEmpty(pageList)) {
            for (SkuPageRespVO product : pageList) {
                // 渲染商品详情信息
                // 分为普通商品和组合商品
                ISpuItemInfoService itemInfoService = this.spuItemInfoService(product.getItemType());
                itemInfoService.renderItemList(product, branchDTO);
            }
        }
        pageList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getType)).forEach((productType, renderItemList) -> {
            // 查看当前商品主SKU 绑定的促销
            activityService.renderItemList(branchDTO, ProductType.isGlobal(productType) ? ProductType.GLOBAL : ProductType.LOCAL, renderItemList);
        });

        //经营屏蔽
        if (Objects.nonNull(branchDTO)) {
            pageList = this.blockSkus(pageList, branchDTO.getBranchId());
        }

        //设置分页数据
        return PageResult.result(elasticSearchListResult.getTotal(), pageList);
    }

    @Override
    public PageResult<SkuPageRespVO> searchActivitySpuDetailList(ActivitySkuSearchReqVO reqVO) {
        ProductContentPageReqDTO pageReqDTO = ProductConvert.INSTANCE.convertProductContentPageReqDTO(reqVO);
        Long activityId = pageReqDTO.getActivityId();
        if (activityId == null) {
            return PageResult.empty();
        }
        // 获取平台商
        PartnerDto partnerDto = portalCacheService.getPartnerDto(reqVO.getSysSource());
        PrmActivityDTO activityDto = portalCacheService.getActivityDto(pageReqDTO.getActivityId());
        // 设置默认游客门店
        BranchDTO branchDTO = new BranchDTO();
        branchDTO.setBranchId(NumberPool.LOWER_GROUND_LONG);
        branchDTO.setBranchId(MallSecurityUtils.getBranchId());
        branchDTO.setAreaId(MallSecurityUtils.getLoginMember().getAreaId());
        branchDTO.setSysCode(partnerDto.getSysCode());

        // 如果能查出来已登陆的门店, 使用真实门店数据
        BranchDTO cacheBranch = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        if (Objects.nonNull(cacheBranch)) {
            branchDTO = cacheBranch;
        }

        // 验证门店是否可以参与活动
        // 验证商品参与范围
        if (!redisActivityService.validateActivity(ActivityConvert.INSTANCE.convertSupplierActivityDTO(activityDto), branchDTO)) {
            return PageResult.empty();
        }

        // 调整活动参与范围
        PrmFuncScopeEnum funcScopeEnum = PrmFuncScopeEnum.formValue(activityDto.getFuncScope());
        PrmNoEnum prmNoEnum = PrmNoEnum.formValue(activityDto.getPrmNo());
        pageReqDTO.setProductType(funcScopeEnum.getProductType());
        // 按照skuId折叠
        pageReqDTO.setDistinctBySpuId(true);
        List<Long> supplierIds = Lists.newArrayList();
        supplierIds.add(activityDto.getSupplierId());
        // 指定入驻商
        pageReqDTO.setSupplierId(supplierIds);

        // 秒杀或者特价
        if (PrmNoEnum.SK == prmNoEnum) {
            List<Long> spuIdList = portalCacheService.getActivitySkRuleList(activityId).stream().filter(skRuleDTO -> skRuleDTO.getSkStatus() == 1) // 过滤 skStatus 为 1 的记录
                    .map(SkRuleDTO::getSkuId).filter(Objects::nonNull).map(portalCacheService::getSkuDTO).map(SkuDTO::getSpuId).collect(Collectors.toList());
            pageReqDTO.setSpuId(spuIdList);
        }
        if (PrmNoEnum.SP == prmNoEnum) {
            List<Long> spuIdList = portalCacheService.getActivitySpRuleList(activityId).stream().map(SpRuleDTO::getSkuId).filter(Objects::nonNull).map(portalCacheService::getSkuDTO).map(SkuDTO::getSpuId).collect(Collectors.toList());
            pageReqDTO.setSpuId(spuIdList);
        }

        List<ActivitySpuScopeDTO> spuScopes = portalCacheService.getActivitySpuScopeList(activityId);
        // 设置黑白名单
        // 没有限制参与范围
        if (ObjectUtil.isNotEmpty(spuScopes)) {
            // 拦截
            // 获取商品使用范围
            // 全场 指定的是黑名单,
            // 秒杀和特价没有黑名单, 其他的有
            //spuScopes.get
            spuScopes.stream().collect(Collectors.groupingBy(ActivitySpuScopeDTO::getApplyType)).forEach((applyType, applyList) -> {
                // 1-白名单 0-黑名单
                Integer whiteOrBlack = applyList.get(0).getWhiteOrBlack();
                List<Long> applyIds = applyList.stream().map(ActivitySpuScopeDTO::getApplyId).collect(Collectors.toList());
                // 2-品类，3-品牌，4-商品
                if (applyType == 2L) {
                    // 管理分类
                    if (whiteOrBlack == 1) {
                        pageReqDTO.setCatgoryId(applyIds);
                    } else {
                        pageReqDTO.setBlackCatgoryId(applyIds);
                    }
                } else if (applyType == 3L) {
                    // 品牌
                    if (whiteOrBlack == 1) {
                        pageReqDTO.setBrandId(applyIds);
                    } else {
                        pageReqDTO.setBlackBrandId(applyIds);
                    }
                } else if (applyType == 4L) {
                    if (whiteOrBlack == 1) {
                        // productGourp 索引实际是以SPUID 上架分类唯一的, 所以需要查询出指定的SPU信息,
                        // 然后如果有秒杀的话, 秒杀的商品应该是会自动排在最前面
                        // 如果是秒杀或者特价的, 还有参与限制, 查询出来的商品还需要再过滤一次, 判断有没有命中规则
                        List<Long> spuIdList = applyIds.stream().map(portalCacheService::getSkuDTO).filter(Objects::nonNull).map(SkuDTO::getSpuId).collect(Collectors.toList());
                        pageReqDTO.setSpuId(spuIdList);
                    } else {
                        // 商品黑名单有点难搞
                        // 如果sku的spu只有一个, 那就spuId也不要, 如果有多个的话, 那就是要了
                        //portalCacheService.getSpuDTO()
                        pageReqDTO.setBlockSkus(applyIds);
                    }
                }
            });
        }

        // 如果是特价或者秒杀, 需要查询全部数据
        //获取商品数据
        EsPageInfo<EsProduct> elasticSearchListResult;
        if (PrmNoEnum.SK == prmNoEnum || PrmNoEnum.SP == prmNoEnum) {
            // 总不可能一个活动3000个品吧
            pageReqDTO.setPageNo(1);
            pageReqDTO.setPageSize(3000);
        }
        // 查询全国还是本地
        if (funcScopeEnum == PrmFuncScopeEnum.GLOBAL) {
            elasticSearchListResult = ProductConvert.INSTANCE.convertEsProductPageByGlobal(esProductService.searchGlobalFull(buildSpuPageReqVO(branchDTO, ProductConvert.INSTANCE.convertProductSearchDTO(pageReqDTO))));
        } else {
            elasticSearchListResult = ProductConvert.INSTANCE.convertEsProductPageByLocal(esProductService.searchLocalFull(buildSpuPageReqVO(branchDTO, ProductConvert.INSTANCE.convertProductSearchDTO(pageReqDTO))));
        }
        //获取商品数据
        List<SkuPageRespVO> pageList = ProductConvert.INSTANCE.convertSpuPageRespList02(elasticSearchListResult.getList());
        //获取销售价和库存
        if (ToolUtil.isNotEmpty(pageList)) {
            for (SkuPageRespVO product : pageList) {
                // 渲染商品详情信息
                // 分为普通商品和组合商品
                ISpuItemInfoService itemInfoService = this.spuItemInfoService(product.getItemType());
                itemInfoService.renderItemList(product, branchDTO);
            }
        }
        // 临时branchDTO
        BranchDTO finalBranchDTO = branchDTO;
        pageList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getType)).forEach((productType, itemList) -> {
            // 查看当前商品主SKU 绑定的促销
            activityService.renderItemList(finalBranchDTO, ProductType.isGlobal(productType) ? ProductType.GLOBAL : ProductType.LOCAL, itemList);
        });

        // 标记商品是否支持负库存下单
        this.markNegativeStock(pageList);

        // 如果是秒杀和特价, 需要过滤出有效的sku
        if (PrmNoEnum.SK == prmNoEnum || PrmNoEnum.SP == prmNoEnum) {
            List<SkuPageRespVO> temp = pageList.stream().filter(item -> {
                if (ObjectUtil.isEmpty(item.getSpuActivityLabelList())) {
                    return false;
                }
                return item.getSpuActivityLabelList().stream().anyMatch(activityLabelVO -> PrmNoEnum.isSk(activityLabelVO.getPrmNo()) || PrmNoEnum.isSp(activityLabelVO.getPrmNo()));
            }).collect(Collectors.toList());

            if (PrmNoEnum.SK == prmNoEnum) {
                // 获取秒杀规则列表
                List<SkRuleDTO> activitySkRuleList = portalCacheService.getActivitySkRuleList(activityId);

                // 将 activitySkRuleList 转换为 Map<skuId, sortOrder>
                Map<Long, Integer> skuIdToSortOrderMap = activitySkRuleList.stream()
                        .collect(Collectors.toMap(SkRuleDTO::getSkuId, SkRuleDTO::getSortOrder));

                // 根据 sortOrder 对 temp 集合进行排序
                temp.sort(Comparator.comparingInt(item -> skuIdToSortOrderMap.get(item.getSkuId())));
            }

            //设置分页数据
            return PageResult.result((long) temp.size(), ListUtil.page(reqVO.getPageNo() - 1, reqVO.getPageSize(), temp));
        }
        //设置分页数据
        return PageResult.result(elasticSearchListResult.getTotal(), pageList);
    }

    @Override
    public List<SupplierListSpuRespVO> getSupplierListSpu(SupplierListSpuReqVO reqVO) {
        List<SupplierListSpuRespVO> list = new ArrayList<>();
        reqVO.getSupplierIdList().forEach(supplierId -> {
            SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(supplierId);
            if (Objects.isNull(supplierDTO)) {
                return;
            }
            SpuPageReq2VO pageReq = new SpuPageReq2VO();
            pageReq.setSupplierId(supplierId);
            pageReq.setPageNo(1);
            pageReq.setPageSize(reqVO.getSize());
            pageReq.setProductType(ProductType.GROUP.getType());
            pageReq.setCondition(reqVO.getCondition());
            pageReq.setSysSource(reqVO.getSysSource());
            PageResult<SkuPageRespVO> listByType = this.getProductListByType(pageReq);
            if (Objects.isNull(listByType) || Objects.isNull(listByType.getList()) || listByType.getList().isEmpty()) {
                return;
            }
            list.add(new SupplierListSpuRespVO(
                    listByType.getList(),
                    supplierId,
                    supplierDTO.getSupplierName(),
                    supplierDTO.getAvatar()
            ));
        });
        return list;
    }

    /**
     * 获取优惠券商品列表
     *
     * @param reqVO 分页请求参数对象
     * @return 商品列表的分页结果
     */
    @Override
    public PageResult<SkuPageRespVO> searchCouponSpuDetailList(CouponSkuSearchReqVO reqVO) {
        ProductContentPageReqDTO pageReqDTO = ProductConvert.INSTANCE.convertProductContentPageReqDTO(reqVO);
        // 获取平台商
        PartnerDto partnerDto = portalCacheService.getPartnerDto(reqVO.getSysSource());
        // 优惠券模版
        CouponTemplateDTO couponTemplate = portalCacheService.getCouponTemplate(reqVO.getCouponTemplateId());
        // 如果能查出来已登陆的门店, 使用真实门店数据
        BranchDTO cacheBranch = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        // 设置默认游客门店
        BranchDTO branchDTO = Objects.isNull(cacheBranch) ? new BranchDTO() : cacheBranch;
        // 构建默认门店数据
        if (Objects.isNull(cacheBranch)) {
            branchDTO.setBranchId(NumberPool.LOWER_GROUND_LONG);
            branchDTO.setBranchId(MallSecurityUtils.getBranchId());
            branchDTO.setAreaId(MallSecurityUtils.getLoginMember().getAreaId());
            branchDTO.setSysCode(partnerDto.getSysCode());
        }

        // 开始隔离商品数据
        PrmFuncScopeEnum funcScopeEnum = PrmFuncScopeEnum.formValue(couponTemplate.getFuncScope());
        pageReqDTO.setProductType(funcScopeEnum.getProductType());
        // 按照skuId折叠
        pageReqDTO.setDistinctBySpuId(true);
        List<Long> supplierIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(couponTemplate.getSupplierIdList())) {
            supplierIds.addAll(couponTemplate.getSupplierIdList());
        } else {
            supplierIds.add(couponTemplate.getSupplierId());
        }
        // 指定入驻商
        pageReqDTO.setSupplierId(supplierIds);
        // 获取参与范围
        List<CouponSpuScopeDTO> couponSpuScopeList = portalCacheService.getCouponSpuScopeList(couponTemplate.getCouponTemplateId());
        // 设置黑白名单
        // 没有限制参与范围
        if (ObjectUtil.isNotEmpty(couponSpuScopeList)) {
            // 拦截
            // 获取商品使用范围
            //spuScopes.get
            couponSpuScopeList.stream().collect(Collectors.groupingBy(CouponSpuScopeDTO::getSpuScope)).forEach((spuScope, applyList) -> {
                // 1-白名单 0-黑名单
                Integer whiteOrBlack = applyList.get(0).getWhiteOrBlack();
                List<Long> applyIds = applyList.stream().map(CouponSpuScopeDTO::getApplyId).collect(Collectors.toList());
                // 商品适用范围(数据字典);0-所有商品可用（全场券） 1-指定入驻商可用（入驻商券），2-指定品类可用（品类券），3-指定品牌可用（品牌券），4-指定商品可用（商品券）
                if (spuScope == 2L) {
                    // 管理分类
                    if (whiteOrBlack == 1) {
                        pageReqDTO.setCatgoryId(applyIds);
                    } else {
                        pageReqDTO.setBlackCatgoryId(applyIds);
                    }
                } else if (spuScope == 3L) {
                    // 品牌
                    if (whiteOrBlack == 1) {
                        pageReqDTO.setBrandId(applyIds);
                    } else {
                        pageReqDTO.setBlackBrandId(applyIds);
                    }
                } else if (spuScope == 4L) {
                    if (whiteOrBlack == 1) {
                        // productGourp 索引实际是以SPUID 上架分类唯一的, 所以需要查询出指定的SPU信息,
                        // 然后如果有秒杀的话, 秒杀的商品应该是会自动排在最前面
                        // 如果是秒杀或者特价的, 还有参与限制, 查询出来的商品还需要再过滤一次, 判断有没有命中规则
                        List<Long> spuIdList = applyIds.stream().map(portalCacheService::getSkuDTO).filter(Objects::nonNull).map(SkuDTO::getSpuId).collect(Collectors.toList());
                        pageReqDTO.setSpuId(spuIdList);
                    } else {
                        // 商品黑名单有点难搞
                        // 如果sku的spu只有一个, 那就spuId也不要, 如果有多个的话, 那就是要了
                        //portalCacheService.getSpuDTO()
                        pageReqDTO.setBlockSkus(applyIds);
                    }
                }
            });
        }
        // 查询全国还是本地
        EsPageInfo<EsProduct> elasticSearchListResult;
        if (funcScopeEnum == PrmFuncScopeEnum.GLOBAL) {
            elasticSearchListResult = ProductConvert.INSTANCE.convertEsProductPageByGlobal(esProductService.searchGlobalFull(buildSpuPageReqVO(branchDTO, ProductConvert.INSTANCE.convertProductSearchDTO(pageReqDTO))));
        } else {
            elasticSearchListResult = ProductConvert.INSTANCE.convertEsProductPageByLocal(esProductService.searchLocalFull(buildSpuPageReqVO(branchDTO, ProductConvert.INSTANCE.convertProductSearchDTO(pageReqDTO))));
        }
        //获取商品数据
        List<SkuPageRespVO> pageList = ProductConvert.INSTANCE.convertSpuPageRespList02(elasticSearchListResult.getList());
        //获取销售价和库存
        if (ToolUtil.isNotEmpty(pageList)) {
            Iterator<SkuPageRespVO> iterator = pageList.iterator();
            while (iterator.hasNext()) {
                SkuPageRespVO product = iterator.next();
                // 渲染商品详情信息
                // 分为普通商品和组合商品
                ISpuItemInfoService itemInfoService = this.spuItemInfoService(product.getItemType());
                itemInfoService.renderItemList(product, branchDTO);
                if (Objects.nonNull(product.getMarkPrice()) && product.getMarkPrice().compareTo(NumberPool.BIGDECIMAL_GROUND) == 0) {
                    iterator.remove();
                }
            }
        }
        // 临时branchDTO
        BranchDTO finalBranchDTO = branchDTO;
        pageList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getType)).forEach((productType, itemList) -> {
            // 查看当前商品主SKU 绑定的促销
            activityService.renderItemList(finalBranchDTO, ProductType.isGlobal(productType) ? ProductType.GLOBAL : ProductType.LOCAL, itemList);
        });
        //设置分页数据
        return PageResult.result(elasticSearchListResult.getTotal(), pageList);
    }

    /**
     * 经营屏蔽商品
     *
     * @param itemList 商品列表
     * @param branchId 客户id
     * @return 经营屏蔽过滤后的商品列表
     */
    public List<SkuPageRespVO> blockSkus(List<SkuPageRespVO> itemList, Long branchId) {
        log.info(" SkuServiceImpl.blockSkus,branchid:{},LoginMember,{}", MallSecurityUtils.getBranchId(), JsonUtils.toJsonString(MallSecurityUtils.getLoginMember()));

        CommonResult<List<Long>> commonResult = null;
        try {
            commonResult = blockSchemeApi.getBlockSkusByBranchId(branchId);
            if (commonResult == null || !commonResult.isSuccess()) {
                log.warn("商城首页屏蔽商品 getBlockSkusByBranchId error, result: {}", JSON.toJSONString(commonResult));
                return itemList;
            }
        } catch (Exception e) {
            log.error("商城首页屏蔽商品 error: ", e);
            return itemList;
        } finally {
            log.info(" blockSchemeApi.getBlockSkusByBranchId入参:{},出参:{}", branchId, JsonUtils.toJsonString(commonResult));
        }
        List<Long> blockSkuIds = commonResult.getData();
        if (CollectionUtils.isEmpty(blockSkuIds)) {
            return itemList;
        }
        return itemList.stream().filter(skuPageRespVO -> !blockSkuIds.contains(skuPageRespVO.getSkuId())).collect(Collectors.toList());
    }

    /**
     * 创建全国商品分页数据请求
     *
     * @param branchDTO 门店
     * @param pageVO    原始请求
     * @return 商品搜索请求
     */
    private ProductSearchDTO buildGlobalSpuPageReqVO(BranchDTO branchDTO, SpuPageReqVO pageVO) {
        // 配置查询商品的参数
        ProductSearchDTO pageReqDTO = ProductConvert.INSTANCE.convertProductSearchDTO(pageVO);
        // 指定全国商品
        pageReqDTO.setProductType(ProductType.GLOBAL.getType());
        // 平台城市分组
        pageReqDTO.setGroupId(Objects.nonNull(branchDTO) ? branchDTO.getGroupId() : null);
        // 渠道ID
        pageReqDTO.setChannelId(Objects.nonNull(branchDTO) ? branchDTO.getChannelId() : null);
        //获取平台商ID
        pageReqDTO.setSysCode(MallSecurityUtils.getLoginMember().getSysCode());
        // 获取经营屏蔽的SkuIds
        if (StatusConstants.FLAG_TRUE.equals(pageVO.getEnableBlock()) && Objects.nonNull(branchDTO)) {
            pageReqDTO.setBlockSkus(blockSchemeApi.getBlockSkusByBranchId(branchDTO.getBranchId()).getCheckedData());
        }
        // 查询组合商品
        if (Objects.nonNull(branchDTO)) {
            List<CbRuleDTO> cbAcitivtyList = saleClassService.getBranchCbRules(branchDTO, ProductType.GLOBAL);
            Set<Long> activityIds = cbAcitivtyList.stream().map(CbRuleDTO::getActivityId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (!activityIds.isEmpty()) {
                pageReqDTO.setOrActivityId(ListUtil.toList(activityIds));
            }
        }
        // 判断是否过滤无库存商品, 过滤多久的
        AppletAgreementPolicyDTO appletAgreementPolicy = portalCacheService.getAppletAgreementPolicy(MallSecurityUtils.getLoginMember().getSysCode());
        if (Objects.nonNull(appletAgreementPolicy) && StringUtils.isNotEmpty(appletAgreementPolicy.getHideNoStockProductDay())) {
            if (StringPool.ZERO.equals(appletAgreementPolicy.getHideNoStockProductDay())) {
                // 如果隐藏无库存是0天, 那就是直接查询有库存的
                pageReqDTO.setNotStock(NumberPool.INT_ZERO);
            } else {
                // 如果隐藏无库存是0天, 那就是直接查询有库存的
                pageReqDTO.setHideNoStockProductDay(Integer.parseInt(appletAgreementPolicy.getHideNoStockProductDay()));
            }
        }
        return pageReqDTO;
    }

    /**
     * 创建本地商品分页数据请求
     *
     * @param branchDTO 门店
     * @param pageVO    原始请求
     * @return 商品搜索请求
     */
    private ProductSearchDTO buildLocalSpuPageReqVO(BranchDTO branchDTO, SpuPageReqVO pageVO) {
        // 配置查询商品的参数
        ProductSearchDTO pageReqDTO = ProductConvert.INSTANCE.convertProductSearchDTO(pageVO);
        // 指定本地商品
        pageReqDTO.setProductType(ProductType.LOCAL.getType());
        // 城市ID
        pageReqDTO.setAreaId(Objects.nonNull(branchDTO) ? branchDTO.getAreaId() : null);
        // 渠道ID
        pageReqDTO.setChannelId(Objects.nonNull(branchDTO) ? branchDTO.getChannelId() : null);
        // 获取平台商ID
        pageReqDTO.setSysCode(MallSecurityUtils.getLoginMember().getSysCode());
        // 获取经营屏蔽的SkuIds
        if (StatusConstants.FLAG_TRUE.equals(pageVO.getEnableBlock()) && Objects.nonNull(branchDTO)) {
            pageReqDTO.setBlockSkus(blockSchemeApi.getBlockSkusByBranchId(branchDTO.getBranchId()).getCheckedData());
        }
        // 没有门店的用户, 理论上就是游客, 设置游客默认城市
        if (Objects.isNull(branchDTO)) {
            LoginMember loginMember = MallSecurityUtils.getLoginMember();
            pageReqDTO.setAreaId(loginMember.getAreaId());
        }
        // 查询组合商品
        // 获取门店可查看的组合促销商品
        if (Objects.nonNull(branchDTO)) {
            List<CbRuleDTO> cbAcitivtyList = saleClassService.getBranchCbRules(branchDTO, ProductType.LOCAL);
            Set<Long> activityIds = cbAcitivtyList.stream().map(CbRuleDTO::getActivityId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (!activityIds.isEmpty()) {
                pageReqDTO.setOrActivityId(ListUtil.toList(activityIds));
            }
        }
        // 判断是否过滤无库存商品, 过滤多久的
        AppletAgreementPolicyDTO appletAgreementPolicy = portalCacheService.getAppletAgreementPolicy(MallSecurityUtils.getLoginMember().getSysCode());
        if (Objects.nonNull(appletAgreementPolicy) && StringUtils.isNotEmpty(appletAgreementPolicy.getHideNoStockProductDay())) {
            if (StringPool.ZERO.equals(appletAgreementPolicy.getHideNoStockProductDay())) {
                // 如果隐藏无库存是0天, 那就是直接查询有库存的
                pageReqDTO.setNotStock(NumberPool.INT_ZERO);
            } else {
                // 如果隐藏无库存是N天, 那就是直接查询有库存的或者隐藏N天内的无库存商品
                pageReqDTO.setHideNoStockProductDay(Integer.parseInt(appletAgreementPolicy.getHideNoStockProductDay()));
            }
        }

        // 获取支持负库存的入驻商列表
        List<Long> negativeStockSupplierIds = getNegativeStockSupplierIds(MallSecurityUtils.getLoginMember().getSysCode());
        if (CollectionUtils.isNotEmpty(negativeStockSupplierIds)) {
            // 设置支持负库存的入驻商ID列表，这些入驻商的商品即使无库存也显示
            pageReqDTO.setNegativeStockSupplierIds(negativeStockSupplierIds);
        }
        return pageReqDTO;
    }

    /**
     * 获取支持负库存的入驻商ID列表
     * @param sysCode 平台商ID
     * @return 支持负库存的入驻商ID列表
     */
    private List<Long> getNegativeStockSupplierIds(Long sysCode) {
        try {
            List<SupplierDTO> suppliers = new ArrayList<>();

            // 调用入驻商API获取支持负库存的入驻商列表
            SysSupplierPageReqVO pageReqVO = new SysSupplierPageReqVO();
            pageReqVO.setPageNo(1);
            pageReqVO.setPageSize(100);
            pageReqVO.setSysCode(sysCode);
            pageReqVO.setIsNegativeStock(SupplierNegativeStockEnum.NEGATIVE_STOCK.getCode());
            PageResult<SupplierDTO> pageResult;
            do {
                pageResult = remoteSupplierApi.getSupplierPage(pageReqVO).getCheckedData();
                if (CollectionUtils.isEmpty(pageResult.getList())) {
                    break;
                }
                suppliers.addAll(pageResult.getList());
                pageReqVO.setPageNo(pageReqVO.getPageNo() + 1);
            } while (pageResult != null && pageResult.getList() != null && pageResult.getList().size() >= 100);

            if (CollectionUtils.isNotEmpty(suppliers)) {
                return suppliers.stream()
                        .map(SupplierDTO::getSupplierId)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.warn("获取支持负库存的入驻商列表失败, sysCode: {}", sysCode, e);
        }
        return new ArrayList<>();
    }

    /**
     * 创建通用商品搜索
     *
     * @param branchDTO 门店
     * @param pageVO    原始请求
     * @return 商品搜索请求
     */
    private ProductSearchDTO buildSpuPageReqVO(BranchDTO branchDTO, ProductSearchDTO pageReqDTO) {
        pageReqDTO.setSysCode(MallSecurityUtils.getLoginMember().getSysCode());
        // 配置查询商品的参数
        if (Objects.nonNull(branchDTO)) {
            pageReqDTO.setAreaId(branchDTO.getAreaId());
            //pageReqDTO.setGroupId(branchDTO.getGroupId());
            pageReqDTO.setChannelId(branchDTO.getChannelId());
            pageReqDTO.setSysCode(branchDTO.getSysCode());
            // 获取全国展示分类, 使用展示分类去隔离搜索结果
            List<SaleClassDTO> saleClassDTOS = saleClassService.getSaleClassDTOS(branchDTO.getSysCode(), branchDTO.getGroupId());
            List<AreaClassDTO> areaClassDTOS = saleClassService.getAreaClassDTOS(branchDTO.getAreaId(), branchDTO.getChannelId(), branchDTO.getBranchId());
            ArrayList<Long> saleClassIdList = new ArrayList<>();
            if (Objects.nonNull(saleClassDTOS)) {
                saleClassIdList.addAll(saleClassDTOS.stream().map(SaleClassDTO::getSaleClassId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            if (Objects.nonNull(areaClassDTOS)) {
                saleClassIdList.addAll(areaClassDTOS.stream().map(AreaClassDTO::getAreaClassId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
            // 查询组合商品
            List<CbRuleDTO> cbAcitivtyList = saleClassService.getBranchCbRules(branchDTO, ProductType.LOCAL, ProductType.GLOBAL);
            Set<Long> activityIds = cbAcitivtyList.stream().map(CbRuleDTO::getActivityId).filter(Objects::nonNull).collect(Collectors.toSet());
            if (!activityIds.isEmpty()) {
                pageReqDTO.setOrActivityId(ListUtil.toList(activityIds));
            }
            // 默认限定
            saleClassIdList.add(NumberPool.LOWER_GROUND_LONG);
            pageReqDTO.setClass3Id(saleClassIdList);
        } else {
            // 没有门店的用户, 理论上就是游客, 设置游客默认城市
            LoginMember loginMember = MallSecurityUtils.getLoginMember();
            pageReqDTO.setAreaId(loginMember.getAreaId());
        }
        // 获取门店可查看的组合促销商品
        return pageReqDTO;
    }

    @Autowired
    @Qualifier("spuCombineService")
    private ISpuItemInfoService spuCombineService;

    @Autowired
    @Qualifier("spuNormalService")
    private ISpuItemInfoService spuNormalService;

    /**
     * @param itemType 0-普通商品, 1-组合商品
     */
    @Override
    public ISpuItemInfoService spuItemInfoService(Integer itemType) {
        // 直接通过条件判断最高效获取具体逻辑 service
        if (itemType == 1) {
            return spuCombineService;
        }
        return spuNormalService;
    }

    /**
     * 获取搜索推荐 - 猜你喜欢
     *
     * @param reqVO 猜你喜欢推荐指数
     * @return 推荐商品列表
     */
    @Override
    public PageResult<SkuPageRespVO> getGuessLikeList(GuessLikeReqVO reqVO) {
        // 当前用户门店
        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());
        List<Long> frequentCategoryList = new ArrayList<>();
        // 常购数据 && 必须是有门店登陆的场景下
        if (ObjectUtil.isNotEmpty(branchDTO) && ObjectUtil.isNotEmpty(reqVO.getFrequentlyRate())) {
            StoreProductRequest storeProductRequest = new StoreProductRequest();
            storeProductRequest.setBranchId(branchDTO.getBranchId());
            // 查询类型 1,购买次数 2,最近购买 3,购买数量 4,类别编号
            storeProductRequest.setType(NumberPool.INT_TWO);
            // 排序 默认0 降序 1升序
            storeProductRequest.setOrderBy(NumberPool.INT_ONE);
            // 只查询30个
            storeProductRequest.setPageSize(30);
            PageResult<StoreProductRespVO> pageResult = SpringUtils.getBean(OrderService.class).frequentSpuList(storeProductRequest);
            if (ObjectUtil.isNotEmpty(pageResult.getList())) {
                // 添加spu推荐比例
                List<Long> spuIdList = pageResult.getList().stream().map(StoreProductRespVO::getSpuId).collect(Collectors.toList());
                spuIdList.stream().map(spuId -> reqVO.getCategoryRecommendRateList().add(SearchRecommendRate.getInstanceSpu(spuId, reqVO.getFrequentlyRate())));
                frequentCategoryList = pageResult.getList().stream().map(StoreProductRespVO::getCatgoryId).collect(Collectors.toList());
            }
        }

        // 1. 构建搜索隔离参数, 写入推荐指数
        ProductSearchDTO searchDTO = buildSpuPageReqVO(branchDTO, new ProductSearchDTO());
        searchDTO.setPageSize(reqVO.getSize());
        searchDTO.setProductType(reqVO.getProductType());
        searchDTO.setCategoryRecommendRateList(reqVO.getCategoryRecommendRateList());

        // 2. 填充管理分类数据
        searchDTO.setCatgoryId(reqVO.getCategoryRecommendRateList().stream().filter(item -> item.getType().equals(NumberPool.INT_ONE)).map(SearchRecommendRate::getApplyId).collect(Collectors.toList()));
        searchDTO.getCatgoryId().addAll(frequentCategoryList);

        // 3. 执行查询搜索数据
        EsPageInfo<EsProductGroup> elasticSearchListResult = esProductService.search(searchDTO, false);
        List<SkuPageRespVO> pageList = ProductConvert.INSTANCE.convertSpuPageRespList(elasticSearchListResult.getList());

        // 4. 处理查询结果, 渲染促销信息
        if (ToolUtil.isNotEmpty(pageList)) {
            for (SkuPageRespVO product : pageList) {
                // 渲染商品详情信息
                // 分为普通商品和组合商品
                ISpuItemInfoService itemInfoService = this.spuItemInfoService(product.getItemType());
                itemInfoService.renderItemList(product, branchDTO);
            }
        }
        pageList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getType)).forEach((productType, itemList) -> {
            // 查看当前商品主SKU 绑定的促销
            activityService.renderItemList(branchDTO, ProductType.isGlobal(productType) ? ProductType.GLOBAL : ProductType.LOCAL, itemList);
        });

        // 设置分页数据
        return PageResult.result(elasticSearchListResult.getTotal(), pageList);
    }

    /**
     * 标记商品是否支持负库存下单
     *
     * @param itemList
     */
    private void markNegativeStock(List<SkuPageRespVO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        itemList.forEach(item -> {
            Optional.ofNullable(portalCacheService.getSupplierDTO(item.getSupplierId())).ifPresent(supplierDTO -> {
                item.setIsNegativeStock(supplierDTO.getIsNegativeStock());
            });
        });
    }
}
