package com.zksr.portal.service.impl.price;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.SpActivityTimesRuleEnum;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.collection.CollectionUtils;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.system.api.domain.SysDictData;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024年05月18日 10:35
 * @description: 特价活动价格计算
 */
@Component
@Order(TradePriceCalculatorService.ORDER_SP_ACTIVITY)
@Slf4j
public class TradeSpActivityPriceCalculatorServiceImpl implements TradePriceCalculatorService {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisActivityService redisActivityService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private RedisService redisService;

    @Override
    public void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result) {
        /**
         * 如果活动集合为NULL 或者 集合为空，不计算
         */
        if (ToolUtil.isEmpty(param.getActivitys()) || param.getActivitys().size() <= 0) {
            return;
        }

        List<PrmActivityDTO> prmActivityList = param.getActivitys().stream()
                .filter(activityVO -> activityVO.getActivityType().equalsIgnoreCase(TrdDiscountTypeEnum.SP.getType()))
                .map(activityVO -> { return portalCacheService.getActivityDto(activityVO.getActivityId()); })
                .filter(ToolUtil.distinctByKey(ActivityDTO::getActivityId)) // 过滤去重
                .collect(Collectors.toList());

        List<SysDictData> dictCache = DictUtils.getDictCache(DictTypeConstants.SYS_PRDT_UNIT);
        Map<String, String> unitMap = Optional.ofNullable(dictCache).orElseGet(ArrayList::new).stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (v1, v2) -> v2));

        prmActivityList.forEach(activityVO -> {
                    //1、获取促销活动和使用范围
                    PrmActivityDTO activityDTO = portalCacheService.getActivityDto(activityVO.getActivityId());
                    if (ToolUtil.isEmpty(activityDTO) || ToolUtil.isEmpty(activityDTO.getActivityId())) {
                        log.error("促销单ID未查到到数据！");
                        return;
                    }

                    // 2、获取活动适用范围
                    SupplierActivityDTO supplierActivity = ActivityConvert.INSTANCE.convert(activityDTO);
                    Boolean boo = redisActivityService.validateActivity(supplierActivity, param.getBranchDto());
                    if (!boo) {
                        log.error("促销单【{}】验证数据未通过！", activityDTO.getPrmSheetNo());
                        return;
                    }


                    // 3、获取特价促销活动规则
                    Set<SpRuleDTO> spRuleDTOS = portalCacheService.getActivityRuleDto(activityVO.getActivityId(), TrdDiscountTypeEnum.SP.getType());
                    Map<Long, SpRuleDTO> spRuleDTOMap = CollectionUtils.convertMap(spRuleDTOS, SpRuleDTO::getSkuId);


                    TradePriceCalculateResp.Promotion promotion = new TradePriceCalculateResp.Promotion();
                    promotion.setId(activityDTO.getActivityId())
                            .setName(activityDTO.getActivityName())
                            .setType(TrdDiscountTypeEnum.SP.getType())
                            .setSupplierId(activityDTO.getSupplierId())
                            .setPromotionItems(new ArrayList<>());

            List<Long> supplierScopes = portalCacheService.getActivitySupplierScopeList(activityVO.getActivityId()).stream().map(ActivitySupplierScopeDTO::getSupplierId).collect(Collectors.toList());

            result.getItems().stream().filter(supplier -> supplierScopes.contains(supplier.getSupplierId()))
                            .forEach(supplier -> {

                                supplier.getSupplierItems()
                                        .stream()
                                        .filter(supplierItem -> spRuleDTOMap.containsKey(supplierItem.getSkuId()) && supplierItem.getIsActivitySpSk() == 1)
                                        .collect(Collectors.groupingBy(TradePriceCalculateResp.OrderItem.SupplierItem::getSkuId))
                                        .forEach((key, value) -> {
                                            SpRuleDTO rule = spRuleDTOMap.get(key);
                                            // 门店已购数量
                                            BigDecimal branchSaleQty = redisActivityService.getSpSaleNum(param.getBranchDto().getBranchId(), rule.getActivityId(), rule.getSpRuleId());
                                            // 门店已购中单位数量
                                            BigDecimal branchMidSaleQty = redisActivityService.getSpMidSaleNum(param.getBranchDto().getBranchId(), rule.getActivityId(), rule.getSpRuleId());
                                            // 门店已购大单位数量
                                            BigDecimal branchLargeSaleQty = redisActivityService.getSpLargeSaleNum(param.getBranchDto().getBranchId(), rule.getActivityId(), rule.getSpRuleId());
                                            // 活动已购总数量
                                            BigDecimal totalSaleQty = redisActivityService.getSpSaleNum(rule.getActivityId(), rule.getSpRuleId());
                                            BigDecimal midTotalSaleQty = redisActivityService.getSpMidSaleNum(rule.getActivityId(), rule.getSpRuleId());
                                            BigDecimal largeTotalSaleQty = redisActivityService.getSpLargeSaleNum(rule.getActivityId(), rule.getSpRuleId());

                                            // 如果有门店限购就限购, 没有就无限购
                                            int onceBuyLimit = (Objects.nonNull(rule.getOnceBuyLimit()) ? rule.getOnceBuyLimit() : Integer.MAX_VALUE);
                                            int midLimit = (Objects.nonNull(rule.getMidLimit()) ? rule.getMidLimit() : Integer.MAX_VALUE);
                                            int largeLimit = (Objects.nonNull(rule.getLargeLimit()) ? rule.getLargeLimit() : Integer.MAX_VALUE);

                                            // 如果有最大活动库存就取最大活动库存, 否则就取SKU库存
                                            Integer totalLimitQty = rule.getTotalLimitQty();
                                            Integer midTotalLimitQty = rule.getMidTotalLimitQty();
                                            Integer largeTotalLimitQty = rule.getLargeTotalLimitQty();

                                            // 本次下单SKU购买的小单位数量
                                            BigDecimal smallBuyCount = value.stream().filter(item -> UnitTypeEnum.UNIT_SMALL.getType().equals(item.getUnitSize())).map(item -> BigDecimal.valueOf(item.getCount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                            // 本次下单SKU购买的中单位数量
                                            BigDecimal midBuyCount = value.stream().filter(item -> UnitTypeEnum.UNIT_MIDDLE.getType().equals(item.getUnitSize())).map(item -> BigDecimal.valueOf(item.getCount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                            // 本次下单SKU购买的大单位数量
                                            BigDecimal largeBuyCount = value.stream().filter(item -> UnitTypeEnum.UNIT_LARGE.getType().equals(item.getUnitSize())).map(item -> BigDecimal.valueOf(item.getCount())).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                                            String smallUnitName = value.stream().filter(item -> UnitTypeEnum.UNIT_SMALL.getType().equals(item.getUnitSize())).map(TradePriceCalculateResp.OrderItem.SupplierItem::getUnit).findFirst().map(unitMap::get).orElse("");
                                            String midUnitName = value.stream().filter(item -> UnitTypeEnum.UNIT_MIDDLE.getType().equals(item.getUnitSize())).map(TradePriceCalculateResp.OrderItem.SupplierItem::getUnit).findFirst().map(unitMap::get).orElse("");
                                            String largeUnitName = value.stream().filter(item -> UnitTypeEnum.UNIT_LARGE.getType().equals(item.getUnitSize())).map(TradePriceCalculateResp.OrderItem.SupplierItem::getUnit).findFirst().map(unitMap::get).orElse("");

                                            try {
                                                // 超出门店限购（小单位）
                                                Assert.isTrue(
                                                        BigDecimal.valueOf(onceBuyLimit).compareTo(branchSaleQty.add(smallBuyCount)) >= 0,
                                                        StringUtils.format("购买商品【{}】超出特价限购活动【{}】门店限购数量【{}{}】",
                                                                value.get(0).getSpuName(),
                                                                activityDTO.getActivityName(),
                                                                onceBuyLimit,
                                                                smallUnitName
                                                        ));
                                                // 超出门店限购（中单位）
                                                Assert.isTrue(
                                                        BigDecimal.valueOf(midLimit).compareTo(branchMidSaleQty.add(midBuyCount)) >= 0,
                                                        StringUtils.format("购买商品【{}】超出特价限购活动【{}】门店限购数量【{}{}】",
                                                                value.get(0).getSpuName(),
                                                                activityDTO.getActivityName(),
                                                                midLimit,
                                                                midUnitName
                                                        ));
                                                // 超出门店限购（大单位）
                                                Assert.isTrue(
                                                        BigDecimal.valueOf(largeLimit).compareTo(branchLargeSaleQty.add(largeBuyCount)) >= 0,
                                                        StringUtils.format("购买商品【{}】超出特价限购活动【{}】门店限购数量【{}{}】",
                                                                value.get(0).getSpuName(),
                                                                activityDTO.getActivityName(),
                                                                largeLimit,
                                                                largeUnitName
                                                        ));

                                                // 活动限量（小单位）
                                                if (Objects.nonNull(totalLimitQty)) {
                                                    // 验证是否超出特价活动库存
                                                    Assert.isTrue(
                                                            BigDecimal.valueOf(totalLimitQty).compareTo(totalSaleQty.add(smallBuyCount)) >= 0,
                                                            "购买商品【{}】超出特价限购活动【{}】剩余库存【{}{}】",
                                                            value.get(0).getSpuName(),
                                                            activityDTO.getActivityName(),
                                                            BigDecimal.valueOf(totalLimitQty).subtract(totalSaleQty),
                                                            smallUnitName);
                                                }
                                                // 活动限量（中单位）
                                                if (Objects.nonNull(midTotalLimitQty)) {
                                                    // 验证是否超出特价活动库存
                                                    Assert.isTrue(
                                                            BigDecimal.valueOf(midTotalLimitQty).compareTo(midTotalSaleQty.add(midBuyCount)) >= 0,
                                                            "购买商品【{}】超出特价限购活动【{}】剩余库存【{}{}】",
                                                            value.get(0).getSpuName(),
                                                            activityDTO.getActivityName(),
                                                            BigDecimal.valueOf(midTotalLimitQty).subtract(midTotalSaleQty),
                                                            midUnitName);
                                                }
                                                // 活动限量（大单位）
                                                if (Objects.nonNull(largeTotalLimitQty)) {
                                                    // 验证是否超出特价活动库存
                                                    Assert.isTrue(
                                                            BigDecimal.valueOf(largeTotalLimitQty).compareTo(largeTotalSaleQty.add(largeBuyCount)) >= 0,
                                                            "购买商品【{}】超出特价限购活动【{}】剩余库存【{}{}】",
                                                            value.get(0).getSpuName(),
                                                            activityDTO.getActivityName(),
                                                            BigDecimal.valueOf(largeTotalLimitQty).subtract(largeTotalSaleQty),
                                                            largeUnitName);
                                                }

                                                // 本次下单SKU所购买 的最小单位的总数量
                                                BigDecimal skuCount = value.stream()
                                                        .map((item -> BigDecimal.valueOf(item.getCount()).multiply(item.getOrderUnitSize()))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                                                // 可用库存限量
                                                BigDecimal surplusSaleQty = redisStockService.getSurplusSaleQtyBigDecimal(rule.getSkuId());
                                                Assert.isTrue(
                                                        surplusSaleQty.compareTo(skuCount) >= 0,
                                                        StringUtils.format("购买商品【{}】超出剩余库存【{}{}】",
                                                                value.get(0).getSpuName(),
                                                                surplusSaleQty,
                                                                smallUnitName
                                                        ));
                                            } catch (IllegalArgumentException e) {
                                                throw new ServiceException(e.getMessage());
                                            }


                                            // 验证是否超出秒杀库存
                                            /*Assert.isTrue((branchSaleQty + skuCount) <= onceBuyLimit && (totalSaleQty +skuCount) <= totalLimitQty,
                                                    "购买商品【{}】超出秒杀活动【{}】门店限购数量【{}】或活动总限购数量【{}】 ,门店已购数量【{}】、活动总已购数量【{}】, 本次购买数量【{}】",
                                                    value.get(0).getSpuName(), activityDTO.getActivityName(), onceBuyLimit, totalLimitQty, branchSaleQty, totalSaleQty, skuCount);*/


                                            value.forEach(supplierItem -> {
                                                // 秒杀价格
                                                BigDecimal activityPrice = rule.getSpPriceByUnitType(supplierItem.getUnitSize());

                                                // 优惠商品数量后商品总金额
                                                BigDecimal itemAmt = new BigDecimal(supplierItem.getCount() + "").multiply(activityPrice).setScale(StatusConstants.PRICE_RESERVE_6, RoundingMode.HALF_UP);
                                                // 商品优惠金额 = 商品原价总金额 - 优惠后商品总金额
                                                BigDecimal discountAmt = new BigDecimal(supplierItem.getCount() + "").multiply(supplierItem.getPrice()).subtract(itemAmt).setScale(StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_UP);
                                                // 优惠商品优惠金额
                                                supplierItem.setActivityDiscountAmt(supplierItem.getActivityDiscountAmt().add(discountAmt));
                                                supplierItem.setSalePrice(supplierItem.getPrice()); // 原销售价
                                                supplierItem.setPrice(activityPrice); // 现价
                                                supplierItem.setPayPrice(supplierItem.getPrice().multiply(new BigDecimal(supplierItem.getCount()+""))); // 现总金额
                                                supplierItem.setIsActivitySpSk(NumberPool.INT_ONE); // 参与促销

                                                // 营销信息
                                                TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
                                                promotion.getPromotionItems().add(promotionItem);
                                                promotionItem.setActivityRuleId(rule.getSpRuleId());
                                                promotionItem.setSkuId(supplierItem.getSkuId());
                                                promotionItem.setActivityDiscountAmt(discountAmt);
                                                promotionItem.setUuIdNo(supplierItem.getUuIdNo());
                                                promotionItem.setCount(supplierItem.getCount());
                                                promotionItem.setUnitSizeQty(supplierItem.getOrderUnitSize()); // 单位转换数量
                                                promotionItem.setUnitSize(supplierItem.getUnitSize());
                                            });
                                        });
                                // 验证活动参与SKU限定数
                                SpActivityTimesRuleEnum timesRule = SpActivityTimesRuleEnum.formValue(activityDTO.getTimesRule());
                                if (timesRule.validateSkuLimit() && Objects.nonNull(activityDTO.getLimitSkus()) && promotion.getPromotionItems().size() > activityDTO.getLimitSkus()) {
                                    throw new ServiceException(StringUtils.format("【{}】促销活动限购{}种SKU", activityDTO.getActivityName(), activityDTO.getLimitSkus()));
                                }
                            });
                    if (promotion.getPromotionItems().size() > 0) {
                        result.getPromotions().add(promotion);
                    }
                });
    }

    @Override
    public void updateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.SP.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    // 总活动缓存过期时间
                    long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    // 活动商品缓存过期时间-- 活动结束时间
                    long itemRuleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    // 特价促销活动使用数量
                    BigDecimal saleQty = BigDecimal.valueOf(orderDiscount.getSpSkCount()).multiply(orderDiscount.getSpSkUnitSizeQty());

                    if (Objects.nonNull(activity.getTimesRule())) {
                        switch (SpActivityTimesRuleEnum.formValue(activity.getTimesRule())){
                            case RULE_LEVEL_0: // 每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate()); // 当天23：59：59过期
                                redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS);
                                break;
                            case RULE_LEVEL_1: // 活动期间内仅一次
                                // 标记活动已经达到限制
                                redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS);
                                break;
                            case RULE_LEVEL_4: // 商品级别每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate()); // 商品活动缓存过期时间 调整为当天23：59：59过期
                                // 标记活动已经达到限制
                                redisService.setCacheObject(
                                        RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderVo.getOrderSaveVo().getBranchId()),
                                        StringPool.ONE,
                                        time,
                                        TimeUnit.SECONDS
                                );
                                break;
                            case RULE_LEVEL_5: // 商品级别仅一次
                                // 标记活动已经达到限制
                                redisService.setCacheObject(
                                        RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderVo.getOrderSaveVo().getBranchId()),
                                        StringPool.ONE,
                                        time,
                                        TimeUnit.SECONDS
                                );
                                break;
                        }
                    }

                    // set特价活动商品门店已购数量
//                    redisActivityService.setSpSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), saleQty, itemRuleTime, TimeUnit.SECONDS);

                    BigDecimal spSkCount = BigDecimal.valueOf(orderDiscount.getSpSkCount());
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        redisActivityService.setSpSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, itemRuleTime, TimeUnit.SECONDS);
                        // set特价活动商品已购总数量（小单位）
                        redisActivityService.setSpSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, itemRuleTime, TimeUnit.SECONDS);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        redisActivityService.setSpMidSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, itemRuleTime, TimeUnit.SECONDS);
                        // set特价活动商品已购总数量（中单位）
                        redisActivityService.setSpMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, itemRuleTime, TimeUnit.SECONDS);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        redisActivityService.setSpLargeSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, itemRuleTime, TimeUnit.SECONDS);
                        // set特价活动商品已购总数量（大单位）
                        redisActivityService.setSpLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount, itemRuleTime, TimeUnit.SECONDS);
                    }

                });
    }

    @Override
    public void undoUpdateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.SP.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    // 总活动缓存过期时间
                    long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    // 活动商品缓存过期时间-- 活动结束时间
                    long itemRuleTime = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    // 特价促销活动使用数量
                    BigDecimal saleQty = BigDecimal.valueOf(orderDiscount.getSpSkCount()).multiply(orderDiscount.getSpSkUnitSizeQty());

                    if (Objects.nonNull(activity.getTimesRule())) {
                        switch (SpActivityTimesRuleEnum.formValue(activity.getTimesRule())){
                            case RULE_LEVEL_0: // 每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate()); // 当天23：59：59过期
                                redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()));
                                break;
                            case RULE_LEVEL_1: // 活动期间内仅一次
                                // 删除标记活动已经达到限制
                                redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()));
                                break;
                            case RULE_LEVEL_4: // 商品级别每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate()); // 商品活动缓存过期时间 调整为当天23：59：59过期
                                // 删除标记活动已经达到限制
                                redisService.deleteObject(
                                        RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderVo.getOrderSaveVo().getBranchId())
                                );
                                break;
                            case RULE_LEVEL_5: // 商品级别仅一次
                                // 删除标记活动已经达到限制
                                redisService.deleteObject(
                                        RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), orderVo.getOrderSaveVo().getBranchId())
                                );
                                break;
                        }
                    }

                    // set特价活动商品门店已购数量
//                    redisActivityService.setSpSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), saleQty.negate(), null, null);

                    BigDecimal spSkCount = BigDecimal.valueOf(orderDiscount.getSpSkCount());
                    if (UnitTypeEnum.UNIT_SMALL.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        redisActivityService.setSpSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                        // set特价活动商品已购总数量（小单位）
                        redisActivityService.setSpSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                    } else if (UnitTypeEnum.UNIT_MIDDLE.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        redisActivityService.setSpMidSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                        // set特价活动商品已购总数量（中单位）
                        redisActivityService.setSpMidSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                    } else if (UnitTypeEnum.UNIT_LARGE.getType().equals(orderDiscount.getSpSkUnitSize())) {
                        redisActivityService.setSpLargeSaleNum(orderVo.getOrderSaveVo().getBranchId(), orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                        // set特价活动商品已购总数量（大单位）
                        redisActivityService.setSpLargeSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), spSkCount.negate(), null, null);
                    }

                });
    }
}
