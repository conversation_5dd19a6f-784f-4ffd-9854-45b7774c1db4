package com.zksr.portal.service.impl.mall;

import com.zksr.common.core.constant.DictTypeConstants;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.DictUtils;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.*;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.content.ProductContentApi;
import com.zksr.product.api.content.dto.ProductContentDTO;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.share.ProductShareApi;
import com.zksr.product.api.share.dto.PrdtProductSharePackageInfoRespDTO;
import com.zksr.product.api.share.dto.PrdtProductShareRespDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.system.api.domain.SysDictData;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @Date 2024/11/6 19:15
 * @注释
 */
@Service
@Slf4j
public class ProductShareServiceImpl implements IProductShareService {

    @Resource
    private ProductShareApi productShareApi;

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private RedisStockService redisStockService;

    @Autowired
    private IActivityService activityService;

    @Autowired
    private ISaleClassService saleClassService;

    @Resource
    private ProductContentApi productContentApi;

    @Autowired
    private ISkuPriceService skuPriceService;


    @Override
    public List<PrdtProductSharePackageInfoRespDTO> getShareProductInfo(String shareKey) {
        if (ToolUtil.isEmpty(shareKey)) {
            throw exception(SHARE_PRODUCT_NOT_NULL);
        }

        List<PrdtProductShareRespDTO> productShareInfo = productShareApi.getShareProductInfo(shareKey).getCheckedData();

        if (ToolUtil.isEmpty(productShareInfo)) {
            throw exception(SHARE_PRODUCT_NOT_EXISTS);
        }

        // 检查分享链接是否过期
        if (new Date().after(productShareInfo.get(0).getExpirationTime())) {
            throw exception(SHARE_PRODUCT_EXPIRE);
        }

        // 获取上架id集合
        List<Long> itemIdList = productShareInfo.stream()
                .map(PrdtProductShareRespDTO::getItemId)
                .collect(Collectors.toList());

        // 添加map 根据上架id分组 key为上架id, value为对应的PrdtProductShareRespDTO列表
        Map<Long, List<PrdtProductShareRespDTO>> itemIdMap = productShareInfo.stream()
                .collect(Collectors.groupingBy(PrdtProductShareRespDTO::getItemId));

        // 构建查询请求
        ProductContentPageReqDTO pageReqDTO = new ProductContentPageReqDTO();
        pageReqDTO.setItemIds(itemIdList);

        BranchDTO branchDTO = portalCacheService.getBranchDto(MallSecurityUtils.getBranchId());

        // 获取全国展示分类, 使用展示分类去隔离搜索结果
        if (branchDTO != null) {
            List<SaleClassDTO> saleClassDTOS = saleClassService.getSaleClassDTOS(branchDTO.getSysCode(), branchDTO.getGroupId());
            List<AreaClassDTO> areaClassDTOS = saleClassService.getAreaClassDTOS(branchDTO.getAreaId(), branchDTO.getChannelId(), branchDTO.getBranchId());
            List<Long> saleClassIdList = new ArrayList<>();

            if (saleClassDTOS != null) {
                saleClassIdList.addAll(saleClassDTOS.stream()
                        .map(SaleClassDTO::getSaleClassId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }

            if (areaClassDTOS != null) {
                saleClassIdList.addAll(areaClassDTOS.stream()
                        .map(AreaClassDTO::getAreaClassId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }

            // 默认限定
            saleClassIdList.add(NumberPool.LOWER_GROUND_LONG);
            pageReqDTO.setClass3Id(saleClassIdList);
        }
        // 获取商品数据
        List<ProductContentDTO> contentDTOList = productContentApi.getElasticSearchFullItemList(pageReqDTO).getCheckedData();

        List<PrdtProductSharePackageInfoRespDTO> itemList = new ArrayList<>();
        Map<Long, List<ProductContentDTO>> productDTOMap = contentDTOList.stream()
                .collect(Collectors.groupingBy(ProductContentDTO::getItemId));

        for (Long itemId : itemIdList) {
            if (productDTOMap.containsKey(itemId)) {
                ProductContentDTO contentDTO = productDTOMap.get(itemId).get(0);
                SkuDTO skuDTO = portalCacheService.getSkuDTO(contentDTO.getSkuId());
                SpuDTO spuDTO = portalCacheService.getSpuDTO(contentDTO.getSpuId());

                // 数据转换
                PrdtProductSharePackageInfoRespDTO item = HutoolBeanUtils.toBean(contentDTO, PrdtProductSharePackageInfoRespDTO.class);

                // 处理同一个itemId对应的不同unitSize
                List<PrdtProductShareRespDTO> shareRespDTOs = itemIdMap.get(itemId);
                for (PrdtProductShareRespDTO shareRespDTO : shareRespDTOs) {
                    setOrderQuantities(item, skuDTO, spuDTO, shareRespDTO.getUnitSize().intValue());
                    // 设置库存
                    item.setStock(redisStockService.getSurplusSaleQty(contentDTO.getSkuId()));

                    if (ProductType.isGlobal(contentDTO.getType())) {
                        // 设置(全国)销售价
                        item.setMarkPrice(skuPriceService.getSupplierSkuPrice(branchDTO, shareRespDTO.getUnitSize().intValue(), contentDTO.getSkuId()));
                    } else {
                        // 设置(本地)销售价
                        item.setMarkPrice(skuPriceService.getAreaSkuPrice(branchDTO, shareRespDTO.getUnitSize().intValue(), contentDTO.getSkuId()));
                    }
                }

                itemList.add(item);
            }
        }

        // 查看当前商品主SKU 绑定的促销
        activityService.renderItemList(branchDTO, ProductType.LOCAL, HutoolBeanUtils.toBean(itemList, SkuPageRespVO.class));

        // 标记商品是否支持负库存下单
        this.markNegativeStock(itemList);

        return itemList;
    }

    private void setOrderQuantities(PrdtProductSharePackageInfoRespDTO item, SkuDTO skuDTO, SpuDTO spuDTO, int unitSize) {
        if (unitSize == UnitTypeEnum.UNIT_LARGE.getType()) {
            item.setMinOq(skuDTO.getLargeMinOq());
            item.setJumpOq(skuDTO.getLargeJumpOq());
            item.setMaxOq(skuDTO.getLargeMaxOq());
            item.setUnit(spuDTO.getUnit(unitSize));
        } else if (unitSize == UnitTypeEnum.UNIT_MIDDLE.getType()) {
            item.setMinOq(skuDTO.getMidMinOq());
            item.setJumpOq(skuDTO.getMidJumpOq());
            item.setMaxOq(skuDTO.getMidMaxOq());
            item.setUnit(spuDTO.getUnit(unitSize));
        } else if (unitSize == UnitTypeEnum.UNIT_SMALL.getType()) {
            item.setMinOq(skuDTO.getMinOq());
            item.setJumpOq(skuDTO.getJumpOq());
            item.setMaxOq(skuDTO.getMaxOq());
            item.setUnit(spuDTO.getUnit(unitSize));
        } else {
            throw new ServiceException("Invalid unit size: " + unitSize);
        }
    }

    /**
     * 标记商品是否支持负库存下单
     * @param itemList
     */
    private void markNegativeStock(List<PrdtProductSharePackageInfoRespDTO> itemList) {
        if (CollectionUtils.isEmpty(itemList)) {
            return;
        }
        itemList.forEach(item -> {
            Optional.ofNullable(portalCacheService.getSupplierDTO(item.getSupplierId())).ifPresent(supplierDTO -> {
                item.setIsNegativeStock(supplierDTO.getIsNegativeStock());
            });
        });
    }
}
