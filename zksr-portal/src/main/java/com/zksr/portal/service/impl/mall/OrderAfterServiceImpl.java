package com.zksr.portal.service.impl.mall;

import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.enums.DeliveryStatusEnum;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.portal.controller.mall.vo.CreateOrderRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.convert.mall.TradeOrderConvert;
import com.zksr.portal.service.mall.ITradePriceService;
import com.zksr.portal.service.mall.OrderAfterService;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.trade.api.after.AfterApi;
import com.zksr.trade.api.after.vo.OrderAfterSaveRequest;
import com.zksr.trade.api.after.vo.RemoteSaveAfterOrderVO;
import com.zksr.trade.api.order.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2024年04月20日 15:54
 * @description: 售后单 实现类
 */
@Service
@Slf4j
public class OrderAfterServiceImpl implements OrderAfterService {

    @Autowired
    private ITradePriceService tradePriceService;
    @Autowired
    private AfterApi afterApi;


    @Override
    public void saveOrderAfter(OrderAfterSaveRequest request) {

    }
}
