package com.zksr.portal.service.impl.handler.transaction;

import com.zksr.common.core.transaction.TransactionHandler;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("rawtypes")
public class ActivitySaleQtyUpdateTransactionHandler implements TransactionHandler {
    private final List<TradePriceCalculatorService> priceCalculators;

    private final RemoteSaveOrderVO orderVo;
    private final TradePriceCalculateResp resp;

    public ActivitySaleQtyUpdateTransactionHandler(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        this.orderVo = orderVo;
        this.resp = resp;
        ConfigurableListableBeanFactory beanFactory = SpringUtils.getBeanFactory();
        priceCalculators = new ArrayList<>(BeanFactoryUtils.beansOfTypeIncludingAncestors(beanFactory, TradePriceCalculatorService.class).values());
    }

    @Override
    public void doBusiness() {
        priceCalculators.forEach(service -> service.updateRedisActivitySaleQty(orderVo, resp));
    }

    @Override
    public void rollback() {
        priceCalculators.forEach(service -> service.undoUpdateRedisActivitySaleQty(orderVo, resp));
    }
}
