package com.zksr.portal.service;

import com.zksr.account.api.account.dto.AccAccountDTO;
import com.zksr.common.core.enums.ProductType;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.member.api.member.dto.MemberDTO;
import com.zksr.product.api.areaClass.dto.AreaClassDTO;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.brand.dto.BrandDTO;
import com.zksr.product.api.catgory.dto.CatgoryDTO;
import com.zksr.product.api.catgory.dto.CatgoryRateDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.product.api.saleClass.dto.SaleClassDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.api.spu.dto.SkuUnitGroupDTO;
import com.zksr.product.api.spu.dto.SkuUnitGroupKeyDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivitySpuScopeDTO;
import com.zksr.promotion.api.activity.dto.SkRuleDTO;
import com.zksr.promotion.api.activity.dto.SpRuleDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.api.coupon.dto.CouponReceiveScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponSpuScopeDTO;
import com.zksr.promotion.api.coupon.dto.CouponTemplateDTO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.opensource.dto.OpensourceDto;
import com.zksr.system.api.page.dto.PagesConfigDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.AppletBaseConfigDTO;
import com.zksr.system.api.partnerConfig.dto.HeLiBaoPayConfigDTO;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.*;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.system.api.visual.dto.VisualSettingMasterDto;
import com.zksr.trade.api.hdfk.dto.HdfkNoPaymentTotalDTO;
import com.zksr.trade.api.order.dto.OrderCutAmtDTO;
import com.zksr.trade.api.order.vo.OrderStatusVO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@SuppressWarnings("all")
public interface IPortalCacheService {

    public void setWxSessionKey(String key, String sessionKey);

    public String getWxSessionKey(String key);

    public PartnerDto getPartnerDto(String key);

    /**
     * 查询运营商
     * @param key
     * @return
     */
    public DcDTO getDcDTO(Long key);

    public BranchDTO getBranchDto(Long branchId);

    public ChannelDTO getChannelDto(Long channelId);

    public AreaDTO getAreaDto(Long areaId);

    public SupplierDTO getSupplierDTO(Long supplierId);

    public BasicSettingPolicyDTO getBasicSettingPolicyDTO(Long dcId);

    /**
    * @Description: 获取平台展示分类
    * @Author: liuxingyu
    * @Date: 2024/3/26 20:29
    */
    List<SaleClassDTO> getSaleClassListBySysCode(Long sysCode);


    public AreaItemDTO getAreaItemDTO(Long areaItemId);

    public SupplierItemDTO getSupplierItemDTO(Long supplierItemId);

    public SpuDTO getSpuDTO(Long spuId);

    public SkuDTO getSkuDTO(Long skuId);

    /**
    * @Description: 获取门店绑定的分类
    * @Author: liuxingyu
    * @Date: 2024/3/28 17:36
    */
    List<AreaClassDTO> getAreaClassBranch(Long branchId);

    /**
     * 获取管理分类分润比例
     * @param catgoryId 管理分类ID
     * @param areaId    区域城市ID
     * @return
     */
    CatgoryRateDTO getCatgoryByIdAndAreaId(Long catgoryId, Long areaId);

    public AppletBaseConfigDTO getAppletBaseConfigDTO(Long sysCode);

    public Integer getAreaSalePriceCodeCache(String key);

    public Integer getSupplierSalePriceCodeCache(Long areaId, Long areaGourpId);

    /**
     *
     * @param areaId    城市ID
     * @param skuId     skuId
     * @param productType   {@link ProductType}
     * @return
     */
    public SkuPriceDTO getSkuPriceDTOByAreaTypeCache(Long areaId, Long skuId, Integer productType);

    /**
     * @Description: 获取业务员信息
     * @Author: chenmingqing
     * @Date: 2024/3/28 10:12
     */
    ColonelDTO getColonel(Long colonelId);

    public BrandDTO getBrandDTO(Long brandId);

    public CouponTemplateDTO getCouponTemplate(Long couponTemplateId);

    public List<CouponSpuScopeDTO> getCouponSpuScopeList(Long couponTemplateId);

    public List<CouponReceiveScopeDTO> getCouponReceiveScopeList(Long couponTemplateId);

    /**
    * @Description: 获取平台商商城小程序配置
    * @Author: liuxingyu
    * @Date: 2024/4/10 15:59
    */
    AppletAgreementPolicyDTO getAppletAgreementPolicy(Long sysCode);


    /**
     * 获取首页配置
     * @param sysCode   平台商编号
     * @param channel   渠道编号
     * @param areaId    区域编号
     * @return
     */
    List<PagesConfigDTO> getPagesConfigDTO(Long sysCode, Long channel, Long areaId);

    /**
    * @Description: 根据门店区域和门店渠道获取城市展示分类
    * @Author: liuxingyu
    * @Date: 2024/4/24 10:22
    */
    List<AreaClassDTO> getAreaClassAreaChannel(Long areaId, Long channelId);

    /**
     * @param sysCode   平台ID
     * @return  根据sysCode 获取支付配置
     */
    public PayConfigDTO getPayConfigDTO(Long sysCode);

    /**
     * 获取账户
     * @param merchantId    商户ID
     * @param merchantType  商户类型    {@link com.zksr.common.core.enums.MerchantTypeEnum}
     * @param platform      支付平台    {@link com.zksr.common.core.enums.PayChannelEnum}
     * @return  账户资金情况
     */
    public AccAccountDTO getAccount(Long merchantId, String merchantType, String platform);

    /**
    * @Description: 获取平台管理分类
    * @Author: liuxingyu
    * @Date: 2024/5/7 10:34
    */
    List<CatgoryDTO> getCatgoryBySysCode(Long sysCode);

    /**
     * 获取入驻商促销活动
     * @param supplierId    入驻商ID
     * @return
     */
    List<SupplierActivityDTO> getSupplierActivity(Long supplierId);

    /**
     * 获取促销活动spu
     * @param activityId
     * @return
     */
    List<ActivitySpuScopeDTO> getActivitySpuScopeList(Long activityId);

    /**
     * 获取促销活动门店
     * @param activityId
     * @return
     */
    List<ActivityBranchScopeDTO> getActivityBranchScopeList(Long activityId);

    /**
     * 获取促销活动城市
     * @param activityId
     * @return
     */
    List<ActivityCityScopeDTO> getActivityCityScopeList(Long activityId);

    /**
     * 获取促销活动spu
     * @param activityId
     * @return
     */
    List<ActivityChannelScopeDTO> getActivityChannelScopeList(Long activityId);

    List<ActivitySupplierScopeDTO> getActivitySupplierScopeList(Long activityId);


    /**
     * 获取促销活动 秒杀活动规则
     * @param activityId
     * @return
     */
    List<SkRuleDTO> getActivitySkRuleList(Long activityId);

    /**
     * 获取促销活动 特价活动规则
     * @param activityId
     * @return
     */
    List<SpRuleDTO> getActivitySpRuleList(Long activityId);

    /**
     * 获取买赠活动 特价活动规则
     * @param activityId
     * @return
     */
    List<BgRuleDTO> getActivityBgRuleList(Long activityId);

    /**
     * 获取满赠活动 特价活动规则
     * @param activityId
     * @return
     */
    List<FgRuleDTO> getActivityFgRuleList(Long activityId);

    /**
     * 获取促销活动信息
     * @param activityId
     * @return
     */
    PrmActivityDTO getActivityDto(Long activityId);

    /**
     * 获取促销活动规则信息
     * @param actitvityId 活动ID
     * @param activityType 活动类型 {@link com.zksr.common.core.enums.TrdDiscountTypeEnum}
     * @return
     */
    <T> Set<T> getActivityRuleDto(Long actitvityId, String activityType);

    /**
     * 获取货到付款未支付统计
     * @param memberId  用户ID
     * @return
     */
    HdfkNoPaymentTotalDTO getHdfkNoPaymentTotal(Long branchId);

    /**
    * @Description: 获取商家管理分类
    * @Author: liuxingyu
    * @Date: 2024/5/31 10:56
    */
    List<CatgoryDTO> getCatgoryBySupplierId(Long supplierId);


    /**
     * 获取商品SPU sku缓存
     * @param spuId          SPUID
     * @param areaId         城市ID
     * @param classId       三级展示类目
     * @param productType   商品类型
     * @return
     */
    List<SkuUnitGroupDTO> getSkuUnitGroupDTO(Long spuId, Long areaId, Long classId, ProductType productType);

    List<SkuUnitGroupDTO> getSkuUnitGroupDTO(SkuUnitGroupKeyDTO spuGroupKey);

    /**
     * 获取平台商商城小程序配置
     * @param sysCode 平台商编号
     * @return
     */
    PartnerMiniSettingPolicyDTO getPartnerMiniSettingPolicy(Long sysCode);

    /**
     * 获取订单角标数据统计
     * @param branchId 门店ID
     * @return
     */
    OrderStatusVO getOrderStatusTotal(Long branchId);

    /**
     * 移除订单角标统计
     * @param branchId 门店ID
     */
    void incrPendingPaymentOrderTotal(Long branchId);

    /**
     * 获取member缓存
     * @param memberId
     * @return
     */
    MemberDTO getMemberDTO(Long memberId);

    VisualSettingMasterDto getVisualMasterBySupplierId(Long supplierId);

    /**
     * 获取合利宝配置
     * @param sysCode
     * @return
     */
    HeLiBaoPayConfigDTO getHeLiBaoConfig(Long sysCode);

    /**
     * 根据DcId查询出运营商订单参数配置
     *
     * @param dcId
     * @return
     */
    public OrderSettingPolicyDTO getOrderSettingPolicyInfo(Long dcId);

    /**
     * 根据入驻商ID查询出商户的开放资源信息
     *
     * @param merchantId
     * @return
     */
    OpensourceDto getOpensourceByMerchantId(Long merchantId);

    /**
     * 获取入驻商其他配置
     * @param supplierId 入驻商编号
     * @return
     */
    SupplierOtherSettingPolicyDTO getPartnerSupplierOtherSettingPolicy(Long supplierId);

    /**
     * 本地上架分类
     */
    AreaClassDTO getAreaClassDTO(Long saleClassId);

    /**
     * 获取组合商品缓存
     */
    SpuCombineDTO getSpuCombineDTO(Long spuCombineId);

    /**
     * 获取素材数量
     * @param cacheKey  {@link MaterialCacheVO#getCacheKey(Integer, Long)} 通过静态方法获取缓存key
     * @return  返回素材集合, 里面会有多个素材
     */
    MaterialCacheVO getMaterial(String cacheKey);

    /**
     * 获取组搜索推荐配置
     */
    SearchConfigDTO getSearchConfigDTO(Long areaId);

    /**
     * 获取全国上架展示分类
     */
    SaleClassDTO getSaleClassDTO(Long saleClassId);

    /**
     * 获取订单截团金额
     * @param cacheKey  缓存key信息
     */
    boolean checkOrderCutAmt(OrderCutAmtDTO.CacheKey cacheKey);

    /**
     * 移除订单截团金额缓存
     */
    void removeOrderCutAmt(OrderCutAmtDTO.CacheKey cacheKey);
}
