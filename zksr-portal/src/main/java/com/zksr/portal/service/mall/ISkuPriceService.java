package com.zksr.portal.service.mall;

import com.zksr.common.core.enums.ProductType;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.product.api.combine.SpuCombineDTO;

import java.math.BigDecimal;

public interface ISkuPriceService {

    /**
     * 获取全国配送销售价
     * @param branchDTO
     * @param skuId
     * @return
     */
    public BigDecimal getAreaSkuPrice(BranchDTO branchDTO, Integer unitSize, Long skuId);


    /**
     * 获取全国配送销售价
     * @param branchDTO
     * @param skuId
     * @return
     */
    public BigDecimal getSupplierSkuPrice(BranchDTO branchDTO, Integer unitSize, Long skuId);

    /**
     * 获取组合商品门店价格
     * @param branchDTO         门店
     * @param spuCombineDTO     组合促销商品
     * @param productType       商品类型区分全国本地
     * @return  千店千价
     */
    BigDecimal getSpuCombinePrice(BranchDTO branchDTO, SpuCombineDTO spuCombineDTO, ProductType productType);
}
