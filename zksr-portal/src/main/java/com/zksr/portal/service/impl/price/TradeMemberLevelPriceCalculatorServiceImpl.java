package com.zksr.portal.service.impl.price;

import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024年03月26日 10:48
 * @description: 门店正常价格计算
 */
@Component
@Order(TradePriceCalculatorService.ORDER_MEMBER_LEVEL)
public class TradeMemberLevelPriceCalculatorServiceImpl  implements  TradePriceCalculatorService{
    @Override
    public void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result) {

    }
}
