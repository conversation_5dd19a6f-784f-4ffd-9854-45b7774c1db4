package com.zksr.portal.service.mall;

import com.zksr.member.api.branch.vo.MemberBranchReqVO;
import com.zksr.member.api.member.vo.MemberUpdatePwdVO;
import com.zksr.portal.controller.mall.vo.ChildUserListRespVO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.system.api.sms.dto.SmsCodeValidRespDTO;

import java.util.List;

public interface IIndexService {

    /**
     * 获取用户openid,unionid
     * @param request
     */
    public GetWxInfoResponse getWxInfo(GetWxInfoRequest request) throws Exception;

    /**
     * 获取支付宝openid
     * @param request
     * @return
     */
    GetWxInfoResponse getZfbInfo(GetWxInfoRequest request);

    /**
     * 登录
     * @param request
     * @return
     */
    public AuthLoginResponse authLogin(AuthLoginRequest request);

    /**
     * 游客登录
     * @param request
     * @return
     */
    public VisitorLoginResponse visitorLogin(VisitorLoginRequest request);


    /**
     * 注册
     * @param request
     * @return
     */
    public RegisterResp register(RegisterReq request);

    /**
     * 简易注册
     * @param request
     * @return
     */
    public RegisterResp registerSimple(RegisterSimpleReq request);

    /**
     * 获取区域城市接口
     * @param sysCode
     * @return
     */
    public List<AreaRespVO> getcityList(Long sysCode);

    /**
     * 获取门店列表
     * @param memberBranchReqVO
     * @return
     */
    public List<BranchReqVo> getBranchList(MemberBranchReqVO memberBranchReqVO);

    /**
     * 获取用户的未审核门店列表
     * @param memberBranchReqVO
     * @return
     */
    public List<BranchReqVo> getUnauditedBranchList(MemberBranchReqVO memberBranchReqVO);

    /**
     * 切换门店
     * @param memberId
     * @param branchId
     * @return
     */
    public AuthLoginResponse cutBranch(Long memberId,Long branchId);


    /**
     * 账号登录
     * @param request
     * @return
     */
    public AuthLoginResponse userLogin(UserLoginRequest request);

    /**
     * 增加子用户
     * @param reqVO
     */
    void insertChildUser(ChildUserReqVO reqVO);


    /**
     * 获取用户的子用户列表
     * @param memberId
     * @return
     */
    List<ChildUserListRespVO> childUserList(Long memberId);

    /**
     * @Description: 获取短信验证码
     * @Author: liuxingyu
     * @Date: 2024/5/10 15:41
     */
    String getSmsCode(LoginSmsReq loginSmsReq);

    /**
     * @Description: 用户获取验证码
     * @Author: liuxingyu
     * @Date: 2024/5/10 15:45
     */
    AuthLoginResponse loginSms(LoginSmsReq loginSmsReq);

    /**
     * 获取微信公众号openid
     * @param request
     * @return
     */
    Boolean bindPublishCode(GetWxInfoRequest request);

    /**
     * 解除公众号绑定
     * @return
     */
    Boolean unbindPublishCode();

    /**
     * 校验短信验证码
     * @param loginSmsReq
     * @return
     */
    SmsCodeValidRespDTO checkSmsCode(LoginSmsReq loginSmsReq);

    /**
     *  更新用户密码
     * @param request
     * @return
     */
    Boolean updateMemberPassword(MemberUpdatePwdVO request);

    /**
     * 获取用户信息
     * @return
     */
    LoginUserInfoVO getUserinfo();

    /**
     * 业务员APP登录商城接口
     * @param request
     * @return
     */
    ColonelAppMallLoginResponse colonelAppMallLogin(ColonelAppMallLoginRequest request);
}
