package com.zksr.portal.service.mall;

import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.prdt.ActivitySkuSearchReqVO;
import com.zksr.portal.controller.mall.vo.prdt.CouponSkuSearchReqVO;
import com.zksr.portal.controller.mall.vo.spu.GuessLikeReqVO;
import com.zksr.portal.controller.mall.vo.spu.SpuItemListReqVO;
import com.zksr.portal.controller.mall.vo.spu.SupplierListSpuReqVO;
import com.zksr.portal.controller.mall.vo.spu.SupplierListSpuRespVO;
import com.zksr.product.api.content.dto.ProductContentPageReqDTO;
import com.zksr.product.api.sku.dto.OrderSkuDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

public interface ISkuService {

    /**
     * 获得商品 SPU 分页 - 本地配送商品
     *
     * @param pageVO
     * @return
     */
    public PageResult<SkuPageRespVO> areaSpuPage(@Valid SpuPageReqVO pageVO);

    /**
     * 获得商品 SPU 详情-本地配送商品
     *
     * @param areaItemId
     * @return
     */
    public SpuDetailRespVO areaSpuDetail(@RequestParam("areaItemId") Long areaItemId);

    /**
     * 获得商品 SPU 分页 - 全国
     *
     * @param pageVO
     * @return
     */
    public PageResult<SkuPageRespVO> supplierSpuPage(@Valid SpuPageReqVO pageVO);

    /**
     * 获得商品 SPU 详情-全国
     *
     * @param supplierItemId
     * @return
     */
    public SpuDetailRespVO supplierSpuDetail(@RequestParam("supplierItemId") Long supplierItemId);


    /**
     * 获得城市上架商品列表
     *
     * @param reqVO
     * @return
     */
    public List<SkuPageRespVO> areaItemSpuDetailList(SpuItemListReqVO reqVO);

    /**
     * 通过上架ID 获取本地, 全国商品集合
     */
    List<SkuPageRespVO> spuItemListDetailList(SpuItemListReqVO reqVO);

    /**
     * 通过上架ID 和上架单位 获取本地, 全国商品集合
     */
    List<SkuPageRespVO> spuItemUnitListDetailList(SpuItemListReqVO reqVO);

    /**
     * 商品搜索
     *
     * @param pageReqDTO
     * @return PageResult
     */
    public PageResult<SkuPageRespVO> searchSpuDetailList(ProductContentPageReqDTO pageReqDTO);


    /**
     * 搜索当前关键字的属性
     *
     * @param pageReqDTO
     * @return SpuSearchPropertiesVO
     */
    public SpuSearchPropertiesVO searchSpuPropertiesList(ProductContentPageReqDTO pageReqDTO);


    /**
     * 根据门店和平台商获取本地和全国分类，过滤掉不符合要求的sku信息
     *
     * @param orderSkuDTOS
     * @param branchDto
     * @param partnerDto
     * @return
     */
    public List<OrderSkuDTO> branchItemFilter(List<OrderSkuDTO> orderSkuDTOS, BranchDTO branchDto, PartnerDto partnerDto);

    /**
     * 根据商品类型获取商品列表
     *
     * @param pageVO 分页请求参数对象
     * @return 商品列表的分页结果
     */
    PageResult<SkuPageRespVO> getProductListByType(SpuPageReq2VO pageVO);

    /**
     * 获取促销商品列表
     *
     * @param reqVO 分页请求参数对象
     * @return 商品列表的分页结果
     */
    PageResult<SkuPageRespVO> searchActivitySpuDetailList(ActivitySkuSearchReqVO reqVO);

    /**
     * 获取入驻商列表简略
     *
     * @param reqVO
     * @return
     */
    List<SupplierListSpuRespVO> getSupplierListSpu(SupplierListSpuReqVO reqVO);

    /**
     * 获取优惠券商品列表
     *
     * @param reqVO 分页请求参数对象
     * @return 商品列表的分页结果
     */
    PageResult<SkuPageRespVO> searchCouponSpuDetailList(CouponSkuSearchReqVO reqVO);

    /**
     * 获取产品详情内容处理
     *
     * @param itemType 0-普通商品, 1-组合商品
     */
    ISpuItemInfoService spuItemInfoService(Integer itemType);

    /**
     * 获取搜索推荐 - 猜你喜欢
     *
     * @param reqVO 猜你喜欢推荐指数
     * @return 推荐商品列表
     */
    PageResult<SkuPageRespVO> getGuessLikeList(GuessLikeReqVO reqVO);

    /**
     * 经营屏蔽商品
     *
     * @param itemList
     * @param branchId
     * @return
     */
    List<SkuPageRespVO> blockSkus(List<SkuPageRespVO> itemList, Long branchId);

}
