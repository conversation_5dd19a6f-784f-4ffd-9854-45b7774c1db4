package com.zksr.portal.service.impl.price;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.enums.FgActivityTimesRuleEnum;
import com.zksr.common.core.enums.FgGiftGroupType;
import com.zksr.common.core.enums.TrdDiscountTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.utils.DateUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.redis.enums.RedisActivityConstants;
import com.zksr.common.redis.service.RedisActivityService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.portal.controller.mall.vo.ActivityVO;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateRequest;
import com.zksr.portal.controller.mall.vo.TradePriceCalculateResp;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.mall.TradeOrderConvert;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.promotion.api.activity.dto.*;
import com.zksr.promotion.utils.ActivityScopeUtil;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.promotion.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @date 2024年05月18日 10:22
 * @description: 满赠活动价格计算
 */
@Component
@Order(TradePriceCalculatorService.ORDER_FG_ACTIVITY)
@Slf4j
public class TradeFgActivityPriceCalculatorServiceImpl implements TradePriceCalculatorService {
    @Autowired
    private IPortalCacheService portalCacheService;
    @Autowired
    private RedisActivityService redisActivityService;
    @Autowired
    private RedisService redisService;

    @Override
    public void calculate(TradePriceCalculateRequest param, TradePriceCalculateResp result) {
        /**
         * 如果活动集合为NULL 或者 集合为空，不计算
         */
        if (ToolUtil.isEmpty(param.getActivitys()) || param.getActivitys().size() <= 0){
            return;
        }

        // 查询满赠促销活动信息并按照 全场 > 品类 > 品牌 > 商品 排序
        List<PrmActivityDTO> prmActivityList = param.getActivitys().stream()
                .filter(activityVO -> activityVO.getActivityType().equalsIgnoreCase(TrdDiscountTypeEnum.FG.getType()))
                .map(activityVO -> { return portalCacheService.getActivityDto(activityVO.getActivityId()); })
                .filter(ToolUtil.distinctByKey(ActivityDTO::getActivityId)) // 过滤去重
                .sorted(Comparator.comparing(PrmActivityDTO::getSpuScope, Comparator.nullsFirst(Integer::compareTo)))
                .collect(Collectors.toList());

        StringBuffer tipsInfo = new StringBuffer("");
        prmActivityList.forEach(prmActivity -> {
            // 2、获取活动适用范围  城市, 渠道, 门店
            SupplierActivityDTO supplierActivity = ActivityConvert.INSTANCE.convert(prmActivity);
            Boolean boo = redisActivityService.validateActivity(supplierActivity, param.getBranchDto());
            if (!boo) {
                log.error("促销单【{}】验证数据未通过！", prmActivity.getPrmSheetNo());
                return;
            }
            // 3、获取买赠促销活动规则
            Set<FgRuleDTO> fgRuleList = portalCacheService.getActivityRuleDto(prmActivity.getActivityId(), TrdDiscountTypeEnum.FG.getType());

            TradePriceCalculateResp.Promotion promotion = new TradePriceCalculateResp.Promotion();
            promotion.setId(prmActivity.getActivityId())
                    .setName(prmActivity.getActivityName())
                    .setType(TrdDiscountTypeEnum.FG.getType())
                    .setSupplierId(prmActivity.getSupplierId())
                    .setPromotionItems(new ArrayList<>());


            List<ActivityVO> activityVOList = param.getActivitys().stream().filter(activityVO -> activityVO.getActivityId().equals(prmActivity.getActivityId())).collect(Collectors.toList());
            Map<Long, Long> processedMap = new HashMap();
            result.getItems().stream()
                    .filter(supplierOrder -> supplierOrder.getSupplierId().equals(prmActivity.getSupplierId()))
                    .forEach(supplierOrder -> {
                        // 商品验证满赠规则 并写入赠品信息
                        activityScope(prmActivity, fgRuleList, supplierOrder, processedMap, promotion, activityVOList, tipsInfo,param.getInterfaceType());
                    });
            if (promotion.getPromotionItems().size() > 0) {
                result.getPromotions().add(promotion);
            }
        });
        result.setActivityTipsInfo(result.getActivityTipsInfo() + tipsInfo.toString());
    }

    @Override
    public void updateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.FG.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    if (Objects.nonNull(activity.getTimesRule())) {
                        switch (FgActivityTimesRuleEnum.formValue(activity.getTimesRule())){
                            case RULE_LEVEL_0: // 每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
                                break;
                            case RULE_LEVEL_1: // 活动期间内仅一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());
                                break;
                        }
                        // 标记活动已经达到限制
                        redisService.setCacheObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()), StringPool.ONE, time, TimeUnit.SECONDS);
                    }
                    // 更新 缓存满赠活动已购数量
                    redisActivityService.setFgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), BigDecimal.valueOf(orderDiscount.getGiftQty()), time, TimeUnit.SECONDS);
                });
    }

    @Override
    public void undoUpdateRedisActivitySaleQty(RemoteSaveOrderVO orderVo, TradePriceCalculateResp resp) {
        orderVo.getOrderDiscountDtlSaveVOS().stream().filter(orderDiscount -> orderDiscount.getDiscountType().equalsIgnoreCase(TrdDiscountTypeEnum.FG.getType()))
                .forEach(orderDiscount -> {
                    PrmActivityDTO activity = portalCacheService.getActivityDto(orderDiscount.getDiscountId());
                    long time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());

                    if (Objects.nonNull(activity.getTimesRule())) {
                        switch (FgActivityTimesRuleEnum.formValue(activity.getTimesRule())){
                            case RULE_LEVEL_0: // 每日一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), DateUtils.getDayEndDate());
                                break;
                            case RULE_LEVEL_1: // 活动期间内仅一次
                                time = DateUtils.getRemainSecond(DateUtils.getNowDate(), activity.getEndTime());
                                break;
                        }
                        // 删除标记活动已经达到限制
                        redisService.deleteObject(RedisActivityConstants.getTimesRuleKey(orderDiscount.getDiscountId(), orderVo.getOrderSaveVo().getBranchId()));
                    }
                    // 退还 缓存满赠活动已购数量
                    redisActivityService.setFgSaleNum(orderDiscount.getDiscountId(), orderDiscount.getDiscountRuleId(), BigDecimal.valueOf(orderDiscount.getGiftQty()).negate(), null, null);
                });
    }

    /**
     *  商品验证满赠规则
     * @param prmActivity 活动信息
     * @param ruleList 活动规则信息
     * @param supplierOrder 入驻商商品信息
     * @param processedMap 已验证过满减的商品信息
     * @param interfaceType 接口类型 0 购物车跳结算页  1 创建订单
     */
    private void activityScope(PrmActivityDTO prmActivity, Set<FgRuleDTO> ruleList, TradePriceCalculateResp.OrderItem supplierOrder, Map<Long, Long> processedMap,
                               TradePriceCalculateResp.Promotion promotion, List<ActivityVO> activityVOList, StringBuffer tipsInfo,Integer interfaceType) {

        List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList = supplierOrder.getSupplierItems();
        // 获取促销活动参与商品范围限定
        List<ActivitySpuScopeDTO> scopeList = portalCacheService.getActivitySpuScopeList(prmActivity.getActivityId());

        // 判断商品是否在活动参与范围内
        supplierOrderList = supplierOrderList.stream().filter(item -> ActivityScopeUtil.validateSpuScope(TradeOrderConvert.INSTANCE.convertActivityValidVO(item), scopeList)).collect(Collectors.toList());

        if (ToolUtil.isEmpty(supplierOrderList)) // 没有满足条件的数据
            return;

        // 命中的总金额
        BigDecimal totalAmt = supplierOrderList.stream()
                .map((TradePriceCalculateResp.OrderItem.SupplierItem::getPayPrice))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);


        // 满足条件的买赠规则
        List<FgRuleDTO> fgRuleDTOList = getSatisfyConditionRule(ruleList, totalAmt, activityVOList);

        if (ToolUtil.isEmpty(fgRuleDTOList) || fgRuleDTOList.size() <= NumberPool.INT_ZERO){// 没有满足条件的规则
            return;
        }

        //校验是否符合满赠规则 如果是保存订单 需要校验
        if(interfaceType.equals(NumberPool.INT_ONE)){
            checkConformFgRule(supplierOrderList,fgRuleDTOList,activityVOList);
        }



        //写入优惠赠品信息
        setSupplierItemActivity(prmActivity, fgRuleDTOList, promotion, tipsInfo, totalAmt, interfaceType);

        // 更新processedMap以标记已处理的项
        updateProcessedMap(supplierOrderList, processedMap, prmActivity);
    }

    /**
     * 根据条件查询出满足条件的满赠规则
     * @param ruleList
     * @param totalAmt
     * @return
     */
    private List<FgRuleDTO> getSatisfyConditionRule(Set<FgRuleDTO> ruleList, BigDecimal totalAmt, List<ActivityVO> activityVOList){
        List<FgRuleDTO> fgRuleList = null;
        Set<Long> activityRuleIds = activityVOList.stream().filter(activityVO -> ToolUtil.isNotEmpty(activityVO.getActivityRuleId())).map(activityVO -> activityVO.getActivityRuleId()).collect(Collectors.toSet());
        // 命中的总金额 大于等于 满赠规则按照金额 倒序排序
        fgRuleList = ruleList.stream()
                .filter(rule -> (NumberUtil.isGreaterOrEqual(totalAmt, rule.getFullAmt()) && (ToolUtil.isEmpty(activityRuleIds) || activityRuleIds.contains(rule.getFgRuleId()))))
                .sorted(Comparator.comparing(FgRuleDTO::getFullAmt, Comparator.nullsFirst(BigDecimal::compareTo)).reversed())
                .collect(Collectors.toList());
        return fgRuleList;
    }


    /**
     * 写入订单营销信息
     * @param fgRuleDTOList 活动规则
     * @param promotion 营销信息
     */
    private void setSupplierItemActivity (PrmActivityDTO prmActivity, List<FgRuleDTO> fgRuleDTOList, TradePriceCalculateResp.Promotion promotion, StringBuffer tipsInfo, BigDecimal totalAmt, Integer interfaceType) {
        // 买赠:(1-满赠 0-每赠)
        boolean forCondition = Objects.nonNull(prmActivity.getLadderFlag()) && NumberPool.INT_ZERO == prmActivity.getLadderFlag();

        List<TradePriceCalculateResp.PromotionItem> promotionItemList =  fgRuleDTOList.stream().map(rule -> {
            TradePriceCalculateResp.PromotionItem promotionItem = new TradePriceCalculateResp.PromotionItem();
            promotionItem.setDiscountCondition(String.valueOf(rule.getFullAmt())); // 活动当前选择规则的满赠条件金额
            // 如果是每赠, 使用总金额/赠品数量, 如果不是那就是送一次
            int giftNum = forCondition ? (totalAmt.divide(rule.getFullAmt(), 0, RoundingMode.DOWN).intValue()) * rule.getOnceGiftQty() : rule.getOnceGiftQty();

            promotionItem.setGiftType(rule.getGiftType());
            promotionItem.setActivityRuleId(rule.getFgRuleId());
            promotionItem.setGiftQty(giftNum);
            // 商品赠品
            if (rule.getGiftType() == NumberPool.INT_ZERO) {
                promotionItem.setGiftSkuId(rule.getSkuId());
                promotionItem.setGiftSkuUnitType(rule.getGiftSkuUnitType());
                SkuDTO skuDTO = portalCacheService.getSkuDTO(rule.getSkuId());
                if (UnitTypeEnum.S(rule.getGiftSkuUnitType())) { // 小单位
                    promotionItem.setGiftSkuUnitPrice(skuDTO.getMarkPrice());
                } else if(UnitTypeEnum.M(rule.getGiftSkuUnitType())) { // 中单位
                    promotionItem.setGiftSkuUnitPrice(skuDTO.getMidMarkPrice());
                } else if (UnitTypeEnum.L(rule.getGiftSkuUnitType())) { // 大单位
                    promotionItem.setGiftSkuUnitPrice(skuDTO.getLargeMarkPrice());
                }

                // 保存订单 验证所选赠品单位是否存在
                if (Objects.equals(interfaceType, NumberPool.INT_ONE)) {
                    SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
                    try {
                        Assert.isTrue(spuDTO.isUnitExist(rule.getGiftSkuUnitType()),
                                "满赠活动【{}】中的赠品商品【{}】所赠送的单位不存在",
                                prmActivity.getActivityName(),
                                spuDTO.getSpuName()
                        );
                    } catch (IllegalArgumentException e) {
                        throw new ServiceException(e.getMessage());
                    }
                }
            }
            // 优惠劵赠品
            if (rule.getGiftType() == NumberPool.INT_ONE) {
                promotionItem.setGiftCouponTemplateId(rule.getCouponTemplateId());
            }

            // 验证赠品库存
            if (!redisActivityService.validateRuleStock(rule, giftNum)) {
                tipsInfo.append("满赠活动【" + prmActivity.getActivityName() + "】起送金额【" + rule.getFullAmt() + "】规则赠品库存不足！;");
                log.info("满赠活动【" + prmActivity.getActivityName() + "】起送金额【" + rule.getFullAmt() + "】规则赠品库存不足！");
                return null;
            }
            promotionItem.setUuIdNo("");
            return promotionItem;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        // 营销信息
        promotion.getPromotionItems().addAll(promotionItemList);
    }


    /**
     * 更新processedMap以标记已处理的项
     * @param supplierOrderList
     * @param processedMap
     */
    private void updateProcessedMap(List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList, Map<Long, Long> processedMap, PrmActivityDTO prmActivity) {
        supplierOrderList.forEach(supplierItem -> {
            supplierItem.setActivityIdInfo(supplierItem.getActivityIdInfo()+";"+prmActivity.getActivityId()); // 商品参与过的活动ID
            // 当前数据已处理处理过，跳过本次操作
            if (processedMap.containsKey(supplierItem.getCategoryId()) || processedMap.containsKey(supplierItem.getBrandId()) || processedMap.containsKey(supplierItem.getSkuId())) {
                return;
            }
            processedMap.put(supplierItem.getSkuId(), supplierItem.getSkuId());
            processedMap.put(supplierItem.getCategoryId(), supplierItem.getCategoryId());
            processedMap.put(supplierItem.getBrandId(), supplierItem.getBrandId());
        });
    }

    /**
     * 检查满赠规则是否满足条件
     * @param supplierOrderList 参与满赠的商品
     * @param fgRuleDTOList 满足条件的满赠规则
     * @param activityVOList 前端传递的已选择赠品信息
     */
    private void checkConformFgRule(List<TradePriceCalculateResp.OrderItem.SupplierItem> supplierOrderList,List<FgRuleDTO> fgRuleDTOList,List<ActivityVO> activityVOList){
        //存在前端传递的已选择赠品信息时  需要校验规则是否满赠
        if(ToolUtil.isNotEmpty(activityVOList)){
            //校验已选择的赠品信息 全部满足满赠规则
            Set<Long> activityVoIds = activityVOList.stream().map(ActivityVO::getActivityRuleId).filter(ToolUtil::isNotEmpty).collect(Collectors.toSet());

            Set<Long> fgRuleIds = fgRuleDTOList.stream().map(FgRuleDTO::getFgRuleId).filter(ToolUtil::isNotEmpty).collect(Collectors.toSet());
            if(!fgRuleIds.containsAll(activityVoIds)){
                throw exception(FULL_ORDER_SAVE_CHECK1);
            }

            //参与满赠的商品数量
            Integer itemSkuNum = supplierOrderList.stream().map(TradePriceCalculateResp.OrderItem.SupplierItem::getSkuId).collect(Collectors.toSet()).size();

            //匹配赠品信息
            Set<FgRuleDTO> orderFgDtoList = fgRuleDTOList.stream().filter(x -> activityVoIds.contains(x.getFgRuleId())).collect(Collectors.toSet());
            //校验满赠规则项
            FgRuleDTO firstRule = orderFgDtoList.iterator().next();
            //购买品项数(sku种类数)
            Integer buySkuNum = firstRule.getBuySkuNum();
            //赠送方式(默认为全赠 0仅一种，1任选，2全赠)
            Integer giftGroupType = firstRule.getGiftGroupType();
            //赠送单位数量
            Integer giftSkuUnitQty = firstRule.getGiftSkuUnitQty();


            //校验购买品项数（sku种类数）
            if(itemSkuNum.compareTo(buySkuNum) < NumberPool.INT_ZERO){
                throw exception(FULL_ORDER_SAVE_CHECK2);
            }

            //校验赠送方式
            if(giftGroupType == FgGiftGroupType.ONCE.getType()){
                //仅一种 校验：
                if(ToolUtil.isNotEmpty(orderFgDtoList) && orderFgDtoList.size() > NumberPool.INT_ONE){
                    throw exception(FULL_ORDER_SAVE_CHECK3);
                }
            }else if(giftGroupType == FgGiftGroupType.MORE.getType()){
                //任选 校验：
                //获取赠品信息的总数量
                Integer onceGiftSumQty = orderFgDtoList.stream().mapToInt(FgRuleDTO::getOnceGiftQty).sum();
                if(giftSkuUnitQty.compareTo(onceGiftSumQty) < NumberPool.INT_ZERO){
                    throw exception(FULL_ORDER_SAVE_CHECK4);
                }
            }
                //全赠无需校验

        }
    }
}
