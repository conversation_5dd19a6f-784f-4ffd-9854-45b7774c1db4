package com.zksr.portal.service.impl.mall;

import cn.hutool.core.util.NumberUtil;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.enums.SkuSaleTypeEnum;
import com.zksr.common.core.enums.UnitTypeEnum;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.mall.ISkuPriceService;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.skuPrice.dto.SkuPriceDTO;
import com.zksr.product.constant.ProductConstant;
import com.zksr.product.enums.PrdtSkuPriceOptionType;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlbeans.impl.xb.ltgfmt.Code;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.StringJoiner;

@Service
@Slf4j
public class SkuPriceServiceImpl implements ISkuPriceService {

    @Autowired
    private IPortalCacheService portalCacheService;

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    //获取本地配送销售价
    @Override
    public BigDecimal getAreaSkuPrice(BranchDTO branchDTO, Integer unitSize, Long skuId) {
        if (Objects.isNull(unitSize)) {
            unitSize = UnitTypeEnum.UNIT_SMALL.getType();
        }
        SkuDTO skuDTO = portalCacheService.getSkuDTO(skuId);
        BigDecimal skuPrice = skuDTO.getMarkPrice();
        //游客登陆的话 不展示销售价
        //获取登陆用户信息
        //如果 memberId 为空 则为游客登陆 不展示价格

        //校验SkuId
        if(ToolUtil.isEmpty(skuId)){
            logger.error("获取本地配送销售价时，sku不能为空{}"+skuId);
            return skuPrice;
        }
        if(ToolUtil.isEmpty(skuDTO)){
            logger.error("获取本地配送销售价时，sku不存在{}"+skuId);
            return skuPrice;
        }
        // 如果没有门店
        if(Objects.isNull(branchDTO)) {
            return skuPrice;
        }

        //优先级高
        /************************************/

        //默认取标准价
        Integer saleType = skuDTO.getSaleType();
        //零售商品取零售价格，否则取标准价
        if(ToolUtil.isNotEmpty(saleType) && SkuSaleTypeEnum.RETAIL.getCode() == saleType) {
            skuPrice = skuDTO.getRetailPrice();
            if (UnitTypeEnum.M(unitSize)) {
                skuPrice = skuDTO.getMidRetailPrice();
            }
            if (UnitTypeEnum.L(unitSize)) {
                skuPrice = skuDTO.getLargeMarkPrice();
            }
        }else{
            skuPrice = skuDTO.getMarkPrice();
            if (UnitTypeEnum.M(unitSize)) {
                skuPrice = skuDTO.getMidMarkPrice();
            }
            if (UnitTypeEnum.L(unitSize)) {
                skuPrice = skuDTO.getLargeMarkPrice();
            }
        }
        
        if (Objects.isNull(skuPrice)) {
            return NumberPool.MAX_PRICE;
        }

        //优先级低
        if(ToolUtil.isEmpty(branchDTO)){
            return skuPrice;
        }else{
            Integer priceCode;
            //优先级高
            //获取单店单价 价格码
            if(ToolUtil.isNotEmpty(branchDTO.getSalePriceCode())){
                priceCode =  Math.toIntExact(branchDTO.getSalePriceCode());
            }else {
                //优先级中
                if(ToolUtil.isEmpty(branchDTO.getAreaId()) || ToolUtil.isEmpty(branchDTO.getChannelId())){
                    return skuPrice;
                }else{
                    //从缓存中获取对应的价格码
                    StringJoiner priceCodeKey = new StringJoiner("-");
                    priceCodeKey.add(branchDTO.getAreaId().toString());
                    priceCodeKey.add(branchDTO.getChannelId().toString());
                    priceCode = portalCacheService.getAreaSalePriceCodeCache(priceCodeKey.toString());
                }

                if (ToolUtil.isEmpty(priceCode) || (priceCode == Code.LESS_THAN)) {
                    //没有设置价格码 默认取标准价
                    return skuPrice;
                }
            }
            //从缓存中获取sku价格
            SkuPriceDTO skuPriceDTO = portalCacheService.getSkuPriceDTOByAreaTypeCache(branchDTO.getAreaId(), skuId, ProductType.LOCAL.getCode());
            if(ToolUtil.isEmpty(skuPriceDTO) || (ToolUtil.isNotEmpty(skuPriceDTO) && skuPriceDTO.getSkuPriceId() == null)){
                //该商品未设置全国价格方案 默认取标准价
                return skuPrice;
            }
            if (UnitTypeEnum.S(unitSize)) {
                if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_1.getType() && Objects.nonNull(skuPriceDTO.getSalePrice1())){
                    skuPrice = skuPriceDTO.getSalePrice1();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_2.getType() && Objects.nonNull(skuPriceDTO.getSalePrice2())){
                    skuPrice = skuPriceDTO.getSalePrice2();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_3.getType() && Objects.nonNull(skuPriceDTO.getSalePrice3())){
                    skuPrice = skuPriceDTO.getSalePrice3();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_4.getType() && Objects.nonNull(skuPriceDTO.getSalePrice4())){
                    skuPrice = skuPriceDTO.getSalePrice4();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_5.getType() && Objects.nonNull(skuPriceDTO.getSalePrice5())){
                    skuPrice = skuPriceDTO.getSalePrice5();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_6.getType() && Objects.nonNull(skuPriceDTO.getSalePrice6())){
                    skuPrice = skuPriceDTO.getSalePrice6();
                }
            } else if (UnitTypeEnum.M(unitSize)) {
                if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_1.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice1())){
                    skuPrice = skuPriceDTO.getMidSalePrice1();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_2.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice2())){
                    skuPrice = skuPriceDTO.getMidSalePrice2();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_3.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice3())){
                    skuPrice = skuPriceDTO.getMidSalePrice3();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_4.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice4())){
                    skuPrice = skuPriceDTO.getMidSalePrice4();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_5.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice5())){
                    skuPrice = skuPriceDTO.getMidSalePrice5();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_6.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice6())){
                    skuPrice = skuPriceDTO.getMidSalePrice6();
                }
            } else if (UnitTypeEnum.L(unitSize)) {
                if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_1.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice1())){
                    skuPrice = skuPriceDTO.getLargeSalePrice1();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_2.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice2())){
                    skuPrice = skuPriceDTO.getLargeSalePrice2();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_3.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice3())){
                    skuPrice = skuPriceDTO.getLargeSalePrice3();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_4.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice4())){
                    skuPrice = skuPriceDTO.getLargeSalePrice4();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_5.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice5())){
                    skuPrice = skuPriceDTO.getLargeSalePrice5();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_6.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice6())){
                    skuPrice = skuPriceDTO.getLargeSalePrice6();
                }
            }

        }
        //1.优先级低，直接SKUDTO的 mark_price 标准价
        //2.优先级中，根据 branchDTO 的 area_id（城市id）和 channel_id（渠道id） 获取价格码（如果 branchDTO area_id或者channel_id 则跳过这一步）
        //  2.1 即根据area_id和channel_id 查询prdt_area_branch_price 获取sale_price_code（价格码），这里做缓存，
        //  缓存格式 Cache<String, Integer> ,key为area_id和channel_id 拼接而成的字符串，value则为 sale_price_code 的值
        //  2.2 根据 branchDTO 的 area_id（城市id） 和 sku_id 和 type=1 获取 prdt_sku_price(SkuPriceDto)缓存，根据对应价格码获取价格
        //  缓存格式  Cache<String, SkuPriceDto>

        //3.优先级高  单店单价 如果门店单独设置了价格方案 则 优先获取门店价格

        //（暂不做）4.优先级最高，城市门店价格方案，即根据brancId获取城市价格方案id（area_price_id），再根据 area_price_id 和 sku查询价格，有价格则最终返回这个价格

        return skuPrice;
    }

    //获取全国配送销售价
    @Override
    public BigDecimal getSupplierSkuPrice(BranchDTO branchDTO, Integer unitSize, Long skuId) {
        if (Objects.isNull(unitSize)) {
            unitSize = UnitTypeEnum.UNIT_SMALL.getType();
        }
        SkuDTO skuDTO = portalCacheService.getSkuDTO(skuId);
        BigDecimal skuPrice = skuDTO.getMarkPrice();

        //游客登陆的话 不展示销售价
        //获取登陆用户信息
        //如果 memberId 为空 则为游客登陆 不展示价格
        //校验SkuId
        if(ToolUtil.isEmpty(skuId)){
            logger.error("获取全国配送销售价时，sku不能为空{}"+skuId);
            return skuPrice;
        }
        if(ToolUtil.isEmpty(skuDTO)){
            logger.error("获取全国配送销售价时，sku不存在{}"+skuId);
            return skuPrice;
        }
        // 如果没有门店
        if(Objects.isNull(branchDTO)) {
            return skuPrice;
        }

        //优先级高
        /************************************/

        //默认取标准价
        skuPrice = skuDTO.getMarkPrice();
        if (UnitTypeEnum.M(unitSize)) {
            skuPrice = skuDTO.getMidMarkPrice();
        }
        if (UnitTypeEnum.L(unitSize)) {
            skuPrice = skuDTO.getLargeMarkPrice();
        }
        if (Objects.isNull(skuPrice)) {
            return NumberPool.MAX_PRICE;
        }

        //优先级低
        if(ToolUtil.isEmpty(branchDTO)){
            return skuPrice;
        }else{
            Integer priceCode;
            //优先级高
            //获取单店单价 价格码
            if(ToolUtil.isNotEmpty(branchDTO.getSalePriceCode())){
                priceCode =  Math.toIntExact(branchDTO.getSalePriceCode());
            }else {
                //优先级中
                if(ToolUtil.isEmpty(branchDTO.getAreaId()) || ToolUtil.isEmpty(branchDTO.getGroupId())){
                    return skuPrice;
                }else{
                    //从缓存中获取对应的价格码
                    priceCode = portalCacheService.getSupplierSalePriceCodeCache(branchDTO.getAreaId(), branchDTO.getGroupId());
                }

                if (ToolUtil.isEmpty(priceCode) || (priceCode == Code.LESS_THAN)) {
                    //没有设置价格码 默认取标准价
                    return skuPrice;
                }
            }
            //从缓存中获取sku价格
            SkuPriceDTO skuPriceDTO = portalCacheService.getSkuPriceDTOByAreaTypeCache(branchDTO.getAreaId(), skuId, ProductType.GLOBAL.getCode());
            if(ToolUtil.isEmpty(skuPriceDTO) || (ToolUtil.isNotEmpty(skuPriceDTO) && skuPriceDTO.getSkuPriceId() == null)){
                //该商品未设置全国价格方案 默认取标准价
                return skuPrice;
            }

            if (UnitTypeEnum.S(unitSize)) {
                if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_1.getType() && Objects.nonNull(skuPriceDTO.getSalePrice1())){
                    skuPrice = skuPriceDTO.getSalePrice1();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_2.getType() && Objects.nonNull(skuPriceDTO.getSalePrice2())){
                    skuPrice = skuPriceDTO.getSalePrice2();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_3.getType() && Objects.nonNull(skuPriceDTO.getSalePrice3())){
                    skuPrice = skuPriceDTO.getSalePrice3();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_4.getType() && Objects.nonNull(skuPriceDTO.getSalePrice4())){
                    skuPrice = skuPriceDTO.getSalePrice4();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_5.getType() && Objects.nonNull(skuPriceDTO.getSalePrice5())){
                    skuPrice = skuPriceDTO.getSalePrice5();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_6.getType() && Objects.nonNull(skuPriceDTO.getSalePrice6())){
                    skuPrice = skuPriceDTO.getSalePrice6();
                }
            } else if (UnitTypeEnum.M(unitSize)) {
                if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_1.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice1())){
                    skuPrice = skuPriceDTO.getMidSalePrice1();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_2.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice2())){
                    skuPrice = skuPriceDTO.getMidSalePrice2();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_3.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice3())){
                    skuPrice = skuPriceDTO.getMidSalePrice3();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_4.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice4())){
                    skuPrice = skuPriceDTO.getMidSalePrice4();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_5.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice5())){
                    skuPrice = skuPriceDTO.getMidSalePrice5();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_6.getType() && Objects.nonNull(skuPriceDTO.getMidSalePrice6())){
                    skuPrice = skuPriceDTO.getMidSalePrice6();
                }
            } else if (UnitTypeEnum.L(unitSize)) {
                if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_1.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice1())){
                    skuPrice = skuPriceDTO.getLargeSalePrice1();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_2.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice2())){
                    skuPrice = skuPriceDTO.getLargeSalePrice2();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_3.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice3())){
                    skuPrice = skuPriceDTO.getLargeSalePrice3();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_4.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice4())){
                    skuPrice = skuPriceDTO.getLargeSalePrice4();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_5.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice5())){
                    skuPrice = skuPriceDTO.getLargeSalePrice5();
                }else if(priceCode == PrdtSkuPriceOptionType.SALE_PRICE_6.getType() && Objects.nonNull(skuPriceDTO.getLargeSalePrice6())){
                    skuPrice = skuPriceDTO.getLargeSalePrice6();
                }
            }
        }

        //1.优先级低，直接SKUDTO的 mark_price 标准价


        //2.优先级中，根据 branchDTO 的 area_id（城市id）和 group_id（平台商城市分组id） 获取价格码（如果 branchDTO area_id或者group_id 为空 则跳过这一步）
        //  2.1 即根据area_id和group_id 查询prdt_supplier_group_price 获取sale_price_code（价格码），这里做缓存，
        //  缓存格式 Cache<String, Integer> ,key为area_id和group_id 拼接而成的字符串，value则为 sale_price_code 的值
        //  2.2 根据 branchDTO 的 area_id（城市id） 和 sku_id 和 type=0 获取 prdt_sku_price(SkuPriceDto)缓存，根据对应价格码获取价格
        //  缓存格式  Cache<String, SkuPriceDto>

        //3.优先级高  单店单价 如果门店单独设置了价格方案 则 优先获取门店价格

        //（暂不做）4.优先级最高，城市门店价格方案，即根据brancId获取城市价格方案id（area_price_id），再根据 area_price_id 和 sku查询价格，有价格则最终返回这个价格

        return skuPrice;
    }

    @Override
    public BigDecimal getSpuCombinePrice(BranchDTO branchDTO, SpuCombineDTO spuCombineDTO, ProductType productType) {
        if (Objects.isNull(branchDTO)) {
            return spuCombineDTO.getMarkPrice();
        }
        Integer priceCode;
        if (Objects.nonNull(branchDTO.getSalePriceCode())) {
            priceCode = branchDTO.getSalePriceCode().intValue();
        } else {
            if (Objects.isNull(branchDTO.getChannelId()) || Objects.isNull(branchDTO.getAreaId())) {
                return spuCombineDTO.getMarkPrice();
            }
            if (productType == ProductType.LOCAL) {
                //从缓存中获取对应的价格码
                StringJoiner priceCodeKey = new StringJoiner("-");
                priceCodeKey.add(branchDTO.getAreaId().toString());
                priceCodeKey.add(branchDTO.getChannelId().toString());
                priceCode = portalCacheService.getAreaSalePriceCodeCache(priceCodeKey.toString());
            } else {
                //从缓存中获取对应的价格码
                priceCode = portalCacheService.getSupplierSalePriceCodeCache(branchDTO.getAreaId(), branchDTO.getGroupId());
            }
            // 还是没有价格方式
            if (Objects.isNull(priceCode)) {
                return spuCombineDTO.getMarkPrice();
            }
        }
        switch (priceCode) {
            case 1:
                if (Objects.isNull(spuCombineDTO.getSalePrice1()) || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, spuCombineDTO.getSalePrice1())) {
                    return spuCombineDTO.getMarkPrice();
                }
                return spuCombineDTO.getSalePrice1();
            case 2:
                if (Objects.isNull(spuCombineDTO.getSalePrice2()) || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, spuCombineDTO.getSalePrice2())) {
                    return spuCombineDTO.getMarkPrice();
                }
                return spuCombineDTO.getSalePrice2();
            case 3:
                if (Objects.isNull(spuCombineDTO.getSalePrice3()) || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, spuCombineDTO.getSalePrice3())) {
                    return spuCombineDTO.getMarkPrice();
                }
                return spuCombineDTO.getSalePrice3();
            case 4:
                if (Objects.isNull(spuCombineDTO.getSalePrice4()) || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, spuCombineDTO.getSalePrice4())) {
                    return spuCombineDTO.getMarkPrice();
                }
                return spuCombineDTO.getSalePrice4();
            case 5:
                if (Objects.isNull(spuCombineDTO.getSalePrice5()) || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, spuCombineDTO.getSalePrice5())) {
                    return spuCombineDTO.getMarkPrice();
                }
                return spuCombineDTO.getSalePrice5();
            case 6:
                if (Objects.isNull(spuCombineDTO.getSalePrice6()) || NumberUtil.isGreaterOrEqual(BigDecimal.ZERO, spuCombineDTO.getSalePrice6())) {
                    return spuCombineDTO.getMarkPrice();
                }
                return spuCombineDTO.getSalePrice6();
            default:
                return spuCombineDTO.getMarkPrice();
        }
    }
}
