package com.zksr.portal.service.impl.mall;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.zksr.account.api.account.AccountApi;
import com.zksr.account.api.account.vo.BranchBalanceRespVO;
import com.zksr.common.core.constant.SheetTypeConstants;
import com.zksr.common.core.constant.StatusConstants;
import com.zksr.common.core.domain.PropertyAndValDTO;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.enums.*;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.transaction.BasicTransactionHandler;
import com.zksr.common.core.utils.*;
import com.zksr.common.core.utils.bean.HutoolBeanUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.core.web.pojo.PageResult;
import com.zksr.common.redis.annotation.DistributedLock;
import com.zksr.common.redis.enums.RedisConstants;
import com.zksr.common.redis.enums.RedisLockConstants;
import com.zksr.common.redis.service.RedisCacheService;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.redis.service.RedisStockService;
import com.zksr.common.security.utils.MallSecurityUtils;
import com.zksr.member.api.LoginMember;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.colonel.dto.ColonelDTO;
import com.zksr.portal.controller.mall.OrderPayWayRespDTO;
import com.zksr.portal.controller.mall.vo.*;
import com.zksr.portal.controller.mall.vo.activity.ActivityFgBgDetailVO;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.order.OrderVO;
import com.zksr.portal.controller.mall.vo.pay.PayPreSuccessReqVO;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import com.zksr.portal.controller.mall.vo.yh.YhBatchListRespVO;
import com.zksr.portal.controller.mall.vo.yh.YhCreateOrderRequest;
import com.zksr.portal.controller.mall.vo.yh.YhSettleOrderRequest;
import com.zksr.portal.convert.mall.SkuConvert;
import com.zksr.portal.convert.mall.TradeOrderConvert;
import com.zksr.portal.mq.mall.MallMqProducer;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.portal.service.handler.TradeOrderHandlerService;
import com.zksr.portal.service.impl.handler.transaction.*;
import com.zksr.portal.service.mall.*;
import com.zksr.portal.service.price.TradePriceCalculatorService;
import com.zksr.product.api.blockScheme.BlockSchemeApi;
import com.zksr.product.api.sku.SkuApi;
import com.zksr.product.api.sku.dto.OrderSkuDTO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateReqVO;
import com.zksr.product.api.sku.vo.PrdtSkuSaleTotalRateVO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.yhdata.YhDataApi;
import com.zksr.product.api.yhdata.vo.YhBatchListReqVO;
import com.zksr.product.api.yhdata.vo.YhBatchRemoveReqVO;
import com.zksr.system.api.area.dto.AreaDTO;
import com.zksr.system.api.channel.dto.ChannelDTO;
import com.zksr.system.api.dc.dto.DcDTO;
import com.zksr.system.api.openapi.dto.ErpStockShortageDTO;
import com.zksr.system.api.partner.dto.PartnerDto;
import com.zksr.system.api.partnerConfig.dto.PayConfigDTO;
import com.zksr.system.api.partnerPolicy.dto.SupplierOtherSettingPolicyDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.trade.api.order.dto.OrderCutAmtDTO;
import com.zksr.trade.api.order.dto.TrdOrderPageReqDTO;
import com.zksr.trade.api.order.dto.TrdOrderResDto;
import com.zksr.trade.api.order.dto.TrdOrderRespDTO;
import com.zksr.trade.api.order.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.enums.GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR;
import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.common.core.utils.collection.CollectionUtils.convertMap;
import static com.zksr.trade.enums.ErrorCodeConstants.TRD_ORDER_SAVE_FAIL;
import static com.zksr.trade.enums.ErrorCodeConstants.OCCUPY_ERP_STOCK_ERROR;

@Service
@Slf4j
public class OrderServiceImpl implements OrderService {

    @Autowired
    private IPortalCacheService portalCacheService;

    @Autowired
    private ITradePriceService tradePriceService;

    @Resource
    private OrderApi orderApi;

    @Resource
    private SkuApi skuApi;

    @Autowired
    private RedisService redisService;

    @Autowired
    private List<TradeOrderHandlerService> tradeOrderHandlerServices;
    @Autowired
    private RedisCacheService redisCacheService;

    @Autowired
    private ISkuPriceService skuPriceService;

    @Autowired
    private RedisStockService redisStockService;
    @Autowired
    private ISkuService skuService;

    @Autowired
    private IActivityService activityService;

    @Resource
    private BlockSchemeApi blockSchemeApi;

    @Resource
    private List<TradePriceCalculatorService> priceCalculators;

    @Autowired
    private IMallYhService mallYhService;

    @Autowired
    private YhDataApi yhDataApi;

    @Autowired
    private AccountApi accountApi;

    @Autowired
    private MallMqProducer mallMqProducer;


    @Override
    public SettleOrderResp settleOrder(Long memberId, Long branchId, SettleOrderRequest request) {
        // 1. 获得收货地址和门店渠道信息
        BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
        //  获取城市信息
        AreaDTO areaDto = portalCacheService.getAreaDto(branchDto.getAreaId());
        if (ToolUtil.isEmpty(areaDto) || ToolUtil.isEmpty(areaDto.getDcId())) {
            throw new ServiceException("门店未绑定区域城市或城市区域未绑定主运营商");
        }


        ChannelDTO channelDTO = new ChannelDTO();
        if (ToolUtil.isNotEmpty(branchDto) && ToolUtil.isNotEmpty(branchDto.getChannelId()))
            channelDTO = portalCacheService.getChannelDto(branchDto.getChannelId());

        // 2. 计算价格
        //设置请求计算价格方法的接口类型
        request.setInterfaceType(NumberPool.INT_ZERO);
        TradePriceCalculateResp resp = calculatePrice(branchDto, request);

        // 组装赠品活动数据
        List<ActivityFgBgDetailVO> fgBgDetailVOList = getFgBgDetails(resp);

        // 获取支付配置信息
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(MallSecurityUtils.getLoginMember().getSysCode());

        // 获取门店储值账户余额
        BranchBalanceRespVO branchBalanceRespVO = accountApi.getBranchBalance(branchDto.getBranchId()).getCheckedData();

        // 3. 拼接返回 TODO
        SettleOrderResp settleResp = TradeOrderConvert.INSTANCE.convert(resp, branchDto, channelDTO, fgBgDetailVOList, payConfigDTO, branchBalanceRespVO);

        // 4.经营屏蔽:验证客户是否购买了被屏蔽的商品
        checkSkusIfBlock(branchId, request);
        return settleResp;


    }

    @Override
    @DistributedLock(lockName = RedisLockConstants.LOCK_SAVE_ORDER, condition = "#branchId")
    public CommonResult<CreateOrderRespVO> saveOrder(Long memberId, Long branchId, CreateOrderRequest request) {
        /**
         * 1. 获得收货地址
         */
        BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
        /**
         * 2、计算价格
         */
        //设置请求计算价格方法的接口类型
        request.setInterfaceType(NumberPool.INT_ONE);
        TradePriceCalculateResp resp = calculatePrice(branchDto, request);

        /**
         * 3、构建订单
         */
        RemoteSaveOrderVO orderVo = buildTradeOrder(branchDto, request, resp);

        /**
         * 4、保存订单
         */
        TrdOrderResDto orderResult;
        OccupyErpStockTransactionHandler occupyErpStockTransactionHandler = null;
        try{
            /**
             * 订单保存前的逻辑--存在订单创建时，支付金额为0的情况
             */
            tradeOrderHandlerServices.forEach(handler -> handler.beforeOrderCreate(orderVo));

            /**
             * 事务处理器执行
             *  占用B2B库存
             *  占用ERP库存
             *  保存订单
             *  核销优惠券
             *  更新活动已售数量信息
             *  更新门店信息
             *  添加待支付订单到缓存
             */
            SaveOrderTransactionHandler saveOrderTransactionHandler = new SaveOrderTransactionHandler(orderVo);
            occupyErpStockTransactionHandler = new OccupyErpStockTransactionHandler(orderVo);
            BasicTransactionHandler.of(
                    new OccupyB2bStockTransactionHandler(orderVo),
                    occupyErpStockTransactionHandler,
                    saveOrderTransactionHandler,
                    new ApplyCouponTransactionHandler(orderVo),
                    new ActivitySaleQtyUpdateTransactionHandler(orderVo, resp),
                    new OrderSuccessEditBranchTransactionHandler(orderVo),
                    new AddUnpayOrderTransactionHandler(saveOrderTransactionHandler::getResult)
            ).execute();
            orderResult = saveOrderTransactionHandler.getResult();
        } catch (ServiceException e) {
            log.error("订单创建失败,error: ", e);
            if (OCCUPY_ERP_STOCK_ERROR.getCode().equals(e.getCode()) && occupyErpStockTransactionHandler != null) {
                // 库存不足
                List<ErpStockShortageDTO> stockShortageDTOS = occupyErpStockTransactionHandler.getResult();
                StockShortageRespVO stockShortageRespVO = convertToStockShortageResp(stockShortageDTOS);
                CreateOrderRespVO createOrderRespVO = new CreateOrderRespVO();
                createOrderRespVO.setStockShortageRespVO(stockShortageRespVO);
                return CommonResult.error(INTERNAL_SERVER_ERROR.getCode(), "预占ERP库存失败", createOrderRespVO);
            }
            throw e;
        } catch (Exception e) {
            log.error("订单创建失败,error: ", e);
            throw exception(TRD_ORDER_SAVE_FAIL);
        }

        /**
         * 订单保存成功后的逻辑
         */
        try {
            tradeOrderHandlerServices.forEach(handler -> handler.afterOrderCreate(request, orderResult, orderVo, resp));
        } catch (Exception e) {
            log.error("订单保存成功后执行失败, error: ", e);
        }

        return CommonResult.success(HutoolBeanUtils.toBean(orderResult, CreateOrderRespVO.class));
    }

    @Override
    public SettleOrderRequest orderMorePurchase(TrdOrderPageReqDTO orderPageReqDto) {
        TrdOrderRespDTO orderResp = orderApi.pageOrderList(orderPageReqDto).getList().get(0);

        List<OrderSkuDTO> orderSkuDTOS = new ArrayList<>();
        orderResp.getSupplierOrderList().stream().flatMap(orderDtl -> orderDtl.getSupplierOrderDtlDTOList().stream())
                .filter(orderDtl -> Objects.equals(orderDtl.getGiftFlag(), 0))
                .forEach(orderDtl -> {
                    OrderSkuDTO skuDTO = HutoolBeanUtils.toBean(portalCacheService.getSkuDTO(orderDtl.getSkuId()), OrderSkuDTO.class);
                    skuDTO.setAreaItemId(orderDtl.getAreaItemId());
                    skuDTO.setSupplierItemId(orderDtl.getSupplierItemId());
                    skuDTO.setItemType(orderDtl.getItemType());
                    orderSkuDTOS.add(skuDTO);
                });

        LoginMember loginMember = MallSecurityUtils.getLoginMember();
        BranchDTO branchDto = portalCacheService.getBranchDto(loginMember.getBranchId());     // 当前切换的门店
        BranchDTO orderBranchDto = portalCacheService.getBranchDto(orderResp.getBranchId());     // 当前订单下单的门店

        // 当前门店和选择再次购买订单门店 的 区域不一致时不允许下单
        if (ToolUtil.isEmpty(branchDto) || ToolUtil.isEmpty(orderBranchDto) || !orderBranchDto.getAreaId().equals(branchDto.getAreaId()))
            throw new ServiceException("当前门店【" + branchDto.getBranchName() + "】与订单门店【" + orderBranchDto.getBranchName() + "】的所在区域不一致，不支持再次购买！");


        PartnerDto partnerDto = portalCacheService.getPartnerDto(loginMember.getSysCode() + "");
        // 从订单明细中筛选出可以使用的商品sku信息，用于数据组装时的数据过滤
        List<OrderSkuDTO> resultOrderSkuDTOS = skuService.branchItemFilter(orderSkuDTOS, branchDto, partnerDto);

        SettleOrderRequest request = TradeOrderConvert.INSTANCE.convertOrderMorePurchase(orderResp, resultOrderSkuDTOS);
        if (request.getItems().size() <= 0)
            throw new ServiceException("当前订单经筛选后无商品，不支持再次购买！");
        return request;
    }


    @Override
    public SettleOrderResp yhSettleOrder(YhSettleOrderRequest request) {
        // 因为需要计算促销, 所以需要查询全部数据, 然后做逻辑分页
        YhBatchListReqVO listReqVO = new YhBatchListReqVO();
        listReqVO.setPageSize(NumberPool.INT_NUM5000);
        listReqVO.setChecked(NumberPool.INT_ONE)
                .setBranchId(request.getBranchId())
                .setSupplierId(request.getSupplierId())
                .setBatchDate(request.getBatchDate());
        ;
        YhBatchListRespVO itemVOS = mallYhService.getBatchList(listReqVO);
        return settleOrder(request.getMemberId(), request.getBranchId(), TradeOrderConvert.INSTANCE.convertOrderSettlePosYhData(request, itemVOS));
    }

    @Override
    public CreateOrderRespVO yhCreateOrder(YhCreateOrderRequest request) {
        // 因为需要计算促销, 所以需要查询全部数据, 然后做逻辑分页
        YhBatchListReqVO listReqVO = new YhBatchListReqVO();
        listReqVO.setPageSize(NumberPool.INT_NUM5000);
        listReqVO.setChecked(NumberPool.INT_ONE)
                .setBranchId(request.getBranchId())
                .setSupplierId(request.getSupplierId())
                .setBatchDate(request.getBatchDate());
        ;
        YhBatchListRespVO itemVOS = mallYhService.getBatchList(listReqVO);
        CreateOrderRespVO createOrderRespVO = saveOrder(request.getMemberId(), request.getBranchId(), TradeOrderConvert.INSTANCE.convertOrderCreatePosYhData(request, itemVOS)).getCheckedData();
        // 当订单创建成功时，删除要货单
        if (ToolUtil.isNotEmpty(createOrderRespVO) && ToolUtil.isNotEmpty(createOrderRespVO.getOrderNo())) {
            YhBatchRemoveReqVO removeReqVO = new YhBatchRemoveReqVO();
            removeReqVO.setDelType(NumberPool.INT_ZERO);
            removeReqVO.setBusinessNo(createOrderRespVO.getOrderNo());
            removeReqVO.setYhIdList(
                    itemVOS.getItemList().stream().map(YhPageSupplierGroupItemVO::getYhId).collect(Collectors.toList())
            );
            // 删除已下单的要货数据
            yhDataApi.removeBatchYh(removeReqVO);
        }
        return createOrderRespVO;
    }

    /**
     * 价格计算
     *
     * @param branchDto
     * @param request
     * @return
     */
    private TradePriceCalculateResp calculatePrice(BranchDTO branchDto, SettleOrderRequest request) {
        /**
         * 1、从请求数据中获取出购物车信息
         */
        List<AppCarIdDTO> carList = new ArrayList<>(); //购物车数组
        List<SupplierDTO> suppliers = new ArrayList<>(); // 入驻商信息数组

        request.getItems().forEach(item -> {
            SupplierDTO supplier = portalCacheService.getSupplierDTO(item.getSupplierId());
            suppliers.add(supplier);

            item.getSupplierItems().forEach(supplierItem -> {
                carList.add(AppCarIdDTO.build(supplierItem.getCarId()));
            });
        });

        //是否校验运营商本地起送价
        boolean isCheckDcMinAmt;
        //是否校验运营商全国起送价
        boolean isCheckDcGlobalMinAmt;
        DcDTO dcDTO = null;
        if(null != branchDto && null != branchDto.getAreaId()){
            AreaDTO areaDto = portalCacheService.getAreaDto(branchDto.getAreaId());
            if(null != areaDto && null != areaDto.getDcId()){
                dcDTO = portalCacheService.getDcDTO(areaDto.getDcId());
            }

            //配置了运营商本地起送价，按运营商起送价校验
            if(null != dcDTO && Objects.nonNull(dcDTO.getMinAmt()) && dcDTO.getMinAmt().compareTo(BigDecimal.ZERO) > 0){
                isCheckDcMinAmt = true;
            } else {
                isCheckDcMinAmt = false;
            }

            //配置了运营商全国起送价，按运营商起送价校验
            if(null != dcDTO && Objects.nonNull(dcDTO.getGlobalMinAmt()) && dcDTO.getGlobalMinAmt().compareTo(BigDecimal.ZERO) > 0){
                isCheckDcGlobalMinAmt = true;
            } else {
                isCheckDcGlobalMinAmt = false;
            }

        } else {
            isCheckDcMinAmt = false;
            isCheckDcGlobalMinAmt = false;
        }

        //按入驻商Id查找对应入驻商单据信息
        Map<Long, SettleOrderRequest.Item> supplierMap = request.getItems().stream()
                .collect(Collectors.toMap(
                        SettleOrderRequest.Item::getSupplierId, // key 是 supplierId
                        item -> item,        // value 是 item
                        (existing, replacement) -> existing // 如果 key 重复，保留现有的
                ));

        TradePriceCalculateRequest calculateRequest = TradeOrderConvert.INSTANCE.convert(branchDto, request, carList, suppliers);
        try {
            calculateRequest.getItems().forEach(item -> Assert.isTrue(item.getSelected(), // 防御性编程，保证都是选中的
                    "商品({}) 未设置为选中", item.getSkuId()));
        } catch (IllegalArgumentException e) {
            throw new ServiceException(e.getMessage());
        }

        TradePriceCalculateResp resp = tradePriceService.calculatePrice(calculateRequest);

        AtomicReference<Integer> orderType = new AtomicReference<>();
        resp.getItems().forEach(supplier -> {
            orderType.set(supplier.getSupplierItems().get(0).getItemType());
            SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(supplier.getSupplierId());
            boolean isCheck = portalCacheService.checkOrderCutAmt(
                    OrderCutAmtDTO.CacheKey
                            .builder()
                            .supplierId(supplier.getSupplierId())
                            .branchId(branchDto.getBranchId())
                            .productType(orderType.get() == NumberPool.INT_ONE ? ProductType.LOCAL.getType() : ProductType.GLOBAL.getType())
                            .build()
            );

            if (isCheck) {  // 当缓存中去除的金额小于等于0是进入验证最低配送价

                try {
                    if (Objects.equals(orderType.get(), NumberPool.INT_ZERO) && !isCheckDcGlobalMinAmt) { // 验证全国起送价
                        Assert.isTrue(supplier.getTotalAmt().compareTo(ToolUtil.isEmptyReturn(supplierDTO.getGlobalMinAmt(), BigDecimal.ZERO)) >= 0,
                                "入驻商({})全国最低配送金额({}),本次购买金额({}) ", supplier.getSupplierName(), supplierDTO.getGlobalMinAmt(), supplier.getTotalAmt());
                    } else if (Objects.equals(orderType.get(), NumberPool.INT_ONE) && !isCheckDcMinAmt) { // 验证本地起送价
                        Assert.isTrue(supplier.getTotalAmt().compareTo(ToolUtil.isEmptyReturn(supplierDTO.getMinAmt(), BigDecimal.ZERO)) >= 0,
                                "入驻商({})本地最低配送金额({}),本次购买金额({}) ", supplier.getSupplierName(), supplierDTO.getMinAmt(), supplier.getTotalAmt());
                    }
                } catch (IllegalArgumentException e) {
                    throw new ServiceException(e.getMessage());
                }
            }

            //获取入驻商其他配置数据
            SupplierOtherSettingPolicyDTO policyDTO = portalCacheService.getPartnerSupplierOtherSettingPolicy(supplier.getSupplierId());
            if (ToolUtil.isNotEmpty(policyDTO) && ToolUtil.isNotEmpty(policyDTO.getShuttingStartTime()) && ToolUtil.isNotEmpty(policyDTO.getShuttingEndTime())) {
                try {
                    // 验证入驻商是否已打烊
                    Assert.isTrue(!DateUtils.isBetween(policyDTO.getShuttingStartTime(), policyDTO.getShuttingEndTime(), DateUtils.getTime()),
                            "入驻商【{}】已打烊！打烊时间为：{}—{}",
                            supplier.getSupplierName(),
                            policyDTO.getShuttingStartTime(),
                            policyDTO.getShuttingEndTime()
                    );
                } catch (IllegalArgumentException e) {
                    throw new ServiceException(e.getMessage());
                }
            }
            if (Objects.nonNull(policyDTO)) {
                supplier.setProductDistributionLabel(policyDTO.getProductDistributionLabel());
                supplier.setSwitchWalletPay(ToolUtil.isEmptyReturn(policyDTO.getSwitchWalletPay(), StringPool.ZERO));
            }

            if(null != supplierMap && supplierMap.containsKey(supplier.getSupplierId())){
                SettleOrderRequest.Item supplierItem = supplierMap.get(supplier.getSupplierId());
                //设置子单备注
                supplier.setMemo(supplierItem.getMemo());
            }

        });
        //订单总金额
        BigDecimal orderTotalAmt = resp.getItems().stream()
                .map(TradePriceCalculateResp.OrderItem :: getTotalAmt)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        log.info(" 起送价校验信息dc[{}],orderTotalAmt[{}],isCheckDcGlobalMinAmt[{}],isCheckDcMinAmt[{}],orderType[{}],resp[{}]", JsonUtils.toJsonString(dcDTO),orderTotalAmt,isCheckDcGlobalMinAmt,isCheckDcMinAmt,orderType,JsonUtils.toJsonString(resp));
        if(null != dcDTO){
            boolean checkDcGlobalMinAmt = Objects.equals(orderType.get(), NumberPool.INT_ZERO) && isCheckDcGlobalMinAmt;
            boolean checkDcMinAmt = Objects.equals(orderType.get(), NumberPool.INT_ONE) && isCheckDcMinAmt;
            log.info("起送价校验信息checkDcGlobalMinAmt[{}],checkDcMinAmt[{}]", checkDcGlobalMinAmt, checkDcMinAmt);
            try {
                //运营商起送价校验
                if (checkDcGlobalMinAmt) { // 验证全国起送价
                    log.info(" 验证全国起送价,[{}]",orderTotalAmt.compareTo(ToolUtil.isEmptyReturn(dcDTO.getGlobalMinAmt(), BigDecimal.ZERO)) >= 0);
                    Assert.isTrue(orderTotalAmt.compareTo(ToolUtil.isEmptyReturn(dcDTO.getGlobalMinAmt(), BigDecimal.ZERO)) >= 0,
                            "运营商({})全国最低配送金额({}),本次购买金额({}) ", dcDTO.getDcName(), dcDTO.getGlobalMinAmt(), orderTotalAmt);
                } else if (checkDcMinAmt) { // 验证本地起送价
                    log.info(" 验证本地起送价,[{}]",orderTotalAmt.compareTo(ToolUtil.isEmptyReturn(dcDTO.getMinAmt(), BigDecimal.ZERO)) >= 0);
                    Assert.isTrue(orderTotalAmt.compareTo(ToolUtil.isEmptyReturn(dcDTO.getMinAmt(), BigDecimal.ZERO)) >= 0,
                            "运营商({})本地最低配送金额({}),本次购买金额({}) ", dcDTO.getDcName(), dcDTO.getMinAmt(), orderTotalAmt);
                }
            } catch (IllegalArgumentException e) {
                throw new ServiceException(e.getMessage());
            }
        }

        return resp;
    }

    /**
     * 构建订单
     *
     * @param branchDto
     * @param request
     * @param resp
     * @return
     */
    private RemoteSaveOrderVO buildTradeOrder(BranchDTO branchDto, CreateOrderRequest request, TradePriceCalculateResp resp) {

        RemoteSaveOrderVO remoteSaveOrderVO = new RemoteSaveOrderVO(); //返回数据实体
        remoteSaveOrderVO.setSupplierOrderSaveVOs(new ArrayList<>(resp.getItems().size()));
        remoteSaveOrderVO.setSupplierOrderDtlSaveVOS(new ArrayList<>());
        remoteSaveOrderVO.setSupplierOrderSettleSaveVOS(new ArrayList<>());
        remoteSaveOrderVO.setOrderLogSaveVOS(new ArrayList<>());
        remoteSaveOrderVO.setOrderDiscountDtlSaveVOS(new ArrayList<>());

        ColonelDTO colonel = new ColonelDTO();
        ColonelDTO pcolonel = new ColonelDTO();
        Integer colonelFlag = NumberPool.INT_ZERO;
        /**
         *  判定当前登录是不是 业务员身份登录，若是，业务员取当前业务员信息，若否， 则取门店绑定业务员
         */
        if (ToolUtil.isNotEmpty(MallSecurityUtils.getColonelId())) {
            colonel = portalCacheService.getColonel(MallSecurityUtils.getColonelId());
            colonelFlag = NumberPool.INT_ONE;
        }
        else if (StringUtils.isNotNull(branchDto) && StringUtils.isNotNull(branchDto.getColonelId())) {
            colonel = portalCacheService.getColonel(branchDto.getColonelId());
        }

        /**
         * 1.2 获取门店绑定业务员的父级业务员信息
         */
        pcolonel = colonel;
        if (StringUtils.isNotNull(colonel) && StringUtils.isNotNull(colonel.getPcolonelId()) && !"Y".equalsIgnoreCase(colonel.getIsColonelAdmin()))
            pcolonel = portalCacheService.getColonel(colonel.getPcolonelId());

        /**
         * 1.3 获取城市信息
         */
        AreaDTO areaDto = portalCacheService.getAreaDto(branchDto.getAreaId());

        // 获取门店储值账户余额
        BranchBalanceRespVO branchBalanceRespVO = accountApi.getBranchBalance(branchDto.getBranchId()).getCheckedData();

        /**
         * 1、生成总订单数据
         */
        TrdOrderSaveVO orderSaveVO = TradeOrderConvert.INSTANCE.convertOrderDataAssemble(branchDto, request, resp, colonel, pcolonel, areaDto, colonelFlag, branchBalanceRespVO);
        orderSaveVO.setOrderNo(SheetTypeConstants.XS + redisService.getUniqueNumber());
        remoteSaveOrderVO.setOrderSaveVo(orderSaveVO);

        /**
         * 2、生成入驻商订单数据
         */
        ColonelDTO finalColonel = colonel;
        ColonelDTO finalPcolonel = pcolonel;
        resp.getItems().forEach(item -> {
            /**
             * 入驻商主表数据
             */
            TrdSupplierOrderSaveVO supplierOrderVo = TradeOrderConvert.INSTANCE.convertSupplierOrder(item);
            //前端修改为入驻商维度的备注
            supplierOrderVo.setMemo(item.getMemo());
            //supplierOrderVo.setMemo(orderSaveVO.getMemo());
            supplierOrderVo.setSupplierOrderNo(SheetTypeConstants.XSS + redisService.getUniqueNumber());
            supplierOrderVo.setOrderNo(orderSaveVO.getOrderNo());
            supplierOrderVo.setPayState(orderSaveVO.getPayState());
            // 设置是否支持负库存下单
            Optional.ofNullable(portalCacheService.getSupplierDTO(supplierOrderVo.getSupplierId()))
                    .map(SupplierDTO::getIsNegativeStock)
                    .ifPresent(supplierOrderVo::setStockShortFlag);

            /**
             * 入驻商明细数据
             */
            AtomicInteger i = new AtomicInteger();

            // 普通商品处理操作
            handleNormalItem(item, supplierOrderVo, orderSaveVO, remoteSaveOrderVO, i);

            // 组合商品处理操作
            handleCombinationItem(item, supplierOrderVo, orderSaveVO, remoteSaveOrderVO, resp, i);


            remoteSaveOrderVO.getSupplierOrderSaveVOs().add(supplierOrderVo);
        });

        /**
         * 构建订单优惠信息
         */
        if (ToolUtil.isNotEmpty(resp.getPromotions()) && !resp.getPromotions().isEmpty()) {

            Map<Long, TrdSupplierOrderSaveVO> supplierVoMap = convertMap(remoteSaveOrderVO.getSupplierOrderSaveVOs(), TrdSupplierOrderSaveVO::getSupplierId);

            resp.getPromotions().forEach(promotion -> {
                promotion.getPromotionItems().forEach(promotionItem -> {
                    List<TrdSupplierOrderDtlSaveVO> orderDtlSaveVOS = remoteSaveOrderVO.getSupplierOrderDtlSaveVOS().stream()
                            .filter(supplierDtl -> (supplierDtl.getSupplierId().equals(promotion.getSupplierId()) && supplierDtl.getUuIdNo().equals(promotionItem.getUuIdNo())))
                            .collect(Collectors.toList());

                    TrdOrderDiscountDtlSaveVO orderDiscountDtlSaveVO = TradeOrderConvert.INSTANCE.convertOrderDiscountDtl(promotion, promotionItem);
                    orderDiscountDtlSaveVO.setBranchId(orderSaveVO.getBranchId());
                    // 入驻商订单明细单号
                    if (!promotion.getType().equals(TrdDiscountTypeEnum.BG.getType()) && !promotion.getType().equals(TrdDiscountTypeEnum.FG.getType()) && ToolUtil.isNotEmpty(orderDtlSaveVOS)) {
                        orderDiscountDtlSaveVO.setSupplierOrderDtlNo(orderDtlSaveVOS.get(0).getSupplierOrderDtlNo());
                    }
                    // 入驻商订单单号
                    if (supplierVoMap.containsKey(promotion.getSupplierId())) {
                        orderDiscountDtlSaveVO.setSupplierOrderNo(supplierVoMap.get(promotion.getSupplierId()).getSupplierOrderNo());
                    }
                    orderDiscountDtlSaveVO.setOrderNo(orderSaveVO.getOrderNo());
                    orderDiscountDtlSaveVO.setDiscountCondition(promotionItem.getDiscountCondition());
                    remoteSaveOrderVO.getOrderDiscountDtlSaveVOS().add(orderDiscountDtlSaveVO);

                     // 当前营销信息为商品赠品时进入
                     if ((promotion.getType().equals(TrdDiscountTypeEnum.BG.getType()) || promotion.getType().equals(TrdDiscountTypeEnum.FG.getType())) && promotionItem.getGiftType() == NumberPool.INT_ZERO){
                         supplierDtlSaveAddItemGifts(remoteSaveOrderVO,  promotionItem, promotion, orderDiscountDtlSaveVO);
                     }
                 });
                // 当前营销信息为买赠、满赠时， 重新计算赠品优惠金额分摊
                if (promotion.getType().equals(TrdDiscountTypeEnum.BG.getType()) || promotion.getType().equals(TrdDiscountTypeEnum.FG.getType())) {
                    // 订单赠品分摊计算
                    tradePriceService.orderGiftShareCalculate(promotion.getId(), remoteSaveOrderVO);
                    // 赠品优惠券绑定赠送条件商品行号处理， 用于记录赠品优惠券是否满足条件发送
                    giftCouponHandle(promotion.getId(), promotion.getType(), remoteSaveOrderVO);
                }
             });

        }

        /**
         * 订单明细平均单价计算（计算按入驻商订单区分、SKU最小单位平均价）
         */
        tradePriceService.calculateOrderDtlSkuAvgPrice(remoteSaveOrderVO);

        // 平台利润模式
        String profitModel;  // 按售价- 成本价
        PrdtSkuSaleTotalRateVO skuSaleTotalRateVO;
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(MallSecurityUtils.getLoginMember().getSysCode());
        if (ToolUtil.isNotEmpty(payConfigDTO) && ToolUtil.isNotEmpty(payConfigDTO.getProfitModel()) && payConfigDTO.getProfitModel().equals(StringPool.ZERO)) {
            profitModel = payConfigDTO.getProfitModel();
            List<Long> skuIds = remoteSaveOrderVO.getSupplierOrderDtlSaveVOS().stream()
                    .map(TrdSupplierOrderDtlSaveVO::getSkuId)
                    .collect(Collectors.toList());
            skuSaleTotalRateVO = skuApi.getSkuSaleTotalRate(
                    PrdtSkuSaleTotalRateReqVO.builder()
                            .sysCode(MallSecurityUtils.getLoginMember().getSysCode())
                            .skuIdList(skuIds)
                            .build()
            ).getCheckedData();
        } else {
            skuSaleTotalRateVO = null;
            profitModel = StringPool.ONE;
        }
        /**
         * 计算入驻商明细结算流水数据
         */
        remoteSaveOrderVO.getSupplierOrderDtlSaveVOS()
                .forEach(orderItem -> {
                    TrdSupplierOrderSettleSaveVO settleSaveVO = tradePriceService.calculateRateAmt(branchDto, orderItem, orderSaveVO, finalColonel, finalPcolonel, profitModel, skuSaleTotalRateVO);
                    remoteSaveOrderVO.getSupplierOrderSettleSaveVOS().add(settleSaveVO);

                    /**
                     * 新增订单明细流水日志
                     */
                    TrdOrderLogSaveVO orderLogSaveVO = new TrdOrderLogSaveVO();
                    orderLogSaveVO.setSupplierOrderDtlNo(orderItem.getSupplierOrderDtlNo());
                    orderLogSaveVO.setAfterState(DeliveryStatusEnum.WAIT_PH.getCode());
                    orderLogSaveVO.setOperateType(StatusConstants.OPER_TYPE_ADD);
                    orderLogSaveVO.setContent(DeliveryStatusEnum.WAIT_PH.getContent());
                    remoteSaveOrderVO.getOrderLogSaveVOS().add(orderLogSaveVO);
                });

        return remoteSaveOrderVO;
    }

    /**
     * 普通商品处理操作
     * @param item
     * @param supplierOrderVo
     * @param orderSaveVO
     * @param remoteSaveOrderVO
     * @param i
     */
    private void handleNormalItem(TradePriceCalculateResp.OrderItem item, TrdSupplierOrderSaveVO supplierOrderVo, TrdOrderSaveVO orderSaveVO,
                                  RemoteSaveOrderVO remoteSaveOrderVO, AtomicInteger i) {
        item.getSupplierItems().stream().filter(supplierItem -> Objects.equals(supplierItem.getGoodsType(), NumberPool.INT_ZERO)) // 处理普通商品
                .forEach(supplierItem -> {
                    TrdSupplierOrderDtlSaveVO dtlVo = TradeOrderConvert.INSTANCE.convertSupplierOrderDtl(item, supplierItem);
                    i.getAndIncrement();
                    dtlVo.setSupplierOrderDtlNo(supplierOrderVo.getSupplierOrderNo() + "_" + i)
                            .setLineNum(i.longValue())
                            .setSupplierOrderNo(supplierOrderVo.getSupplierOrderNo())
                            .setSyncFlag(StatusConstants.FLAG_FALSE) // 同步状态
                            .setPayState(PayStateEnum.getNotPayState())
                            .setDeliveryState(DeliveryStatusEnum.WAIT_PH.getCode())
                            .setStockShortFlag(supplierOrderVo.getStockShortFlag());

                    // 订单明细单价金额转换计算
                    TradeOrderConvert.INSTANCE.convertOrderDtlPrice(dtlVo);

                    remoteSaveOrderVO.getSupplierOrderDtlSaveVOS().add(dtlVo);
                });
    }

    /**
     * 组合商品处理操作
     * @param item
     * @param supplierOrderVo
     * @param orderSaveVO
     * @param remoteSaveOrderVO
     * @param i
     */
    private void handleCombinationItem(TradePriceCalculateResp.OrderItem item, TrdSupplierOrderSaveVO supplierOrderVo, TrdOrderSaveVO orderSaveVO,
                                  RemoteSaveOrderVO remoteSaveOrderVO, TradePriceCalculateResp resp,  AtomicInteger i) {
        item.getSupplierItems().stream().filter(supplierItem -> Objects.equals(supplierItem.getGoodsType(), NumberPool.INT_ONE)) // 处理组合商品
                .forEach(supplierItem -> {
                    // 获取到当前组合商品的促销明细计算信息
                    List<TradePriceCalculateResp.PromotionItem> promotionItemList = resp.getPromotions().stream()
                            .filter(promotion -> Objects.equals(promotion.getType(), TrdDiscountTypeEnum.CB.getType()))
                            .flatMap(promotion -> promotion.getPromotionItems().stream())
                            .filter(promotionItem -> promotionItem.getUuIdNo().contains(supplierItem.getUuIdNo()))
                            .collect(Collectors.toList());

                    // 获取到当前组合商品的促销信息
                    TradePriceCalculateResp.Promotion promotionDto =  resp.getPromotions().stream()
                            .filter(promotion -> Objects.equals(promotion.getType(), TrdDiscountTypeEnum.CB.getType()) && Objects.equals(promotion.getId(), promotionItemList.get(NumberPool.INT_ZERO).getId()))
                            .collect(Collectors.toList()).get(NumberPool.INT_ZERO);


                    supplierItem.getSpuCombineSkuVOList().forEach(spuCombineSkuVO -> {
                        // 过滤出当前组合商品下子商品对应的促销信息
                        TradePriceCalculateResp.PromotionItem promotionItemDto = promotionItemList.stream()
                                .filter(promotionItem -> Objects.equals(promotionItem.getUuIdNo(), StringUtils.format("{}_{}_{}", supplierItem.getUuIdNo(), spuCombineSkuVO.getSkuId(), spuCombineSkuVO.getSkuUnitType())))
                                .collect(Collectors.toList()).get(NumberPool.INT_ZERO);
                        // 组装商品明细数据
                        TrdSupplierOrderDtlSaveVO dtlVo = supplierDtlSaveAddCbActiviyItem(supplierOrderVo, orderSaveVO, promotionItemDto, promotionDto, spuCombineSkuVO, supplierItem);

                        i.getAndIncrement();
                        dtlVo.setSupplierOrderDtlNo(supplierOrderVo.getSupplierOrderNo() + "_" + i)
                                .setLineNum(i.longValue())
                                .setSupplierOrderNo(supplierOrderVo.getSupplierOrderNo())
                                .setSyncFlag(StatusConstants.FLAG_FALSE) // 同步状态
                                .setPayState(PayStateEnum.getNotPayState())
                                .setDeliveryState(DeliveryStatusEnum.WAIT_PH.getCode())
                                .setStockShortFlag(supplierOrderVo.getStockShortFlag());

                        // 订单明细单价金额转换计算
                        TradeOrderConvert.INSTANCE.convertOrderDtlPrice(dtlVo);

                        remoteSaveOrderVO.getSupplierOrderDtlSaveVOS().add(dtlVo);
                    });
                });
    }



//    private

    /**
     * 获取本次订单买赠、满赠满足规则的赠品列表
     * @param resp
     * @return
     */
    private List<ActivityFgBgDetailVO> getFgBgDetails(TradePriceCalculateResp resp){
        return resp.getPromotions().stream()
                .filter(promotion -> (promotion.getType().equalsIgnoreCase(TrdDiscountTypeEnum.BG.getType()) || promotion.getType().equalsIgnoreCase(TrdDiscountTypeEnum.FG.getType())))
                .map(promotion -> {
                    List<Long> activityRuleIds = promotion.getPromotionItems().stream().map(TradePriceCalculateResp.PromotionItem::getActivityRuleId).collect(Collectors.toList());
                    ActivityFgBgDetailVO activityFgBgDetail = activityService.getActivityFgBgDetailVO(promotion.getId(), activityRuleIds);
                    // 计算促销数据结果
                    Map<Long, List<TradePriceCalculateResp.PromotionItem>> ruleMap = promotion.getPromotionItems().stream().collect(Collectors.groupingBy(TradePriceCalculateResp.PromotionItem::getActivityRuleId));
                    // 促销渲染结果
                    for (ActivityFgBgDetailVO.Rule rule : activityFgBgDetail.getRuleList()) {
                        for (ActivityFgBgDetailVO.Gift gift : rule.getGiftList()) {
                            if (ruleMap.containsKey(gift.getRuleId())) {
                                List<TradePriceCalculateResp.PromotionItem> promotionItems = ruleMap.get(gift.getRuleId());
                                Integer giftQty = promotionItems.get(0).getGiftQty();
                                gift.setOnceGiftQty(giftQty);
                            }
                        }
                    }
                    // 查询入驻商信息，取入驻商名称给前端展示
                    SupplierDTO supplier = portalCacheService.getSupplierDTO(activityFgBgDetail.getSupplierId());
                    if (ToolUtil.isNotEmpty(supplier))
                        activityFgBgDetail.setSupplierName(supplier.getSupplierName());
                    return activityFgBgDetail;
                }).collect(Collectors.toList());
    }

    /**
     * 订单明细数据添加商品赠品
     */
    private void supplierDtlSaveAddItemGifts(RemoteSaveOrderVO remoteSaveOrderVO, TradePriceCalculateResp.PromotionItem promotionItem,
                                             TradePriceCalculateResp.Promotion promotion, TrdOrderDiscountDtlSaveVO orderDiscountDtlSaveVO){
        SkuDTO skuDTO = portalCacheService.getSkuDTO(promotionItem.getGiftSkuId());
        if (ToolUtil.isEmpty(skuDTO))
            throw new ServiceException("赠品SKU【"+promotionItem.getGiftSkuId()+"】未查找到数据！");

        SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
        if (ToolUtil.isEmpty(spuDTO))
            throw new ServiceException("赠品SKU【"+skuDTO.getSkuId()+"】未查找到对应的SPU商品数据！");
        // 参与此赠品优惠的销售商品行号信息， 用于记录赠品商品条件
        orderDiscountDtlSaveVO.setOrderDtlNumStr(
                remoteSaveOrderVO.getSupplierOrderDtlSaveVOS().stream()
                        .filter(dtl -> dtl.getActivityIdInfo().contains(promotion.getId() + "") && Objects.equals(dtl.getGiftFlag(), NumberPool.INT_ZERO))
                        .map(dtl -> dtl.getLineNum() + "")
                        .collect(Collectors.joining(";"))
        );

        // 过滤出活动入驻商的下单商品信息
        List<TrdSupplierOrderDtlSaveVO> supplierOrderDtlSaveVoList = remoteSaveOrderVO.getSupplierOrderDtlSaveVOS().stream()
                .filter(dtl -> dtl.getSupplierId().equals(promotion.getSupplierId()))
                .collect(Collectors.toList());
        TrdSupplierOrderDtlSaveVO supplierOrderDtlSaveVo = supplierOrderDtlSaveVoList.get(NumberPool.INT_ZERO);
        // 赠品数据
        TrdSupplierOrderDtlSaveVO dtlVo = TradeOrderConvert.INSTANCE.supplierDtlSaveAddItemGifts(supplierOrderDtlSaveVo, promotionItem, skuDTO, spuDTO);
        dtlVo.setSupplierId(promotion.getSupplierId());
        dtlVo.setMemo("活动【"+promotion.getId()+"】赠送商品");
        dtlVo.setActivityIdInfo(promotion.getId()+"");
        dtlVo.setPayState(supplierOrderDtlSaveVo.getPayState());



        String supplierOrderNo = supplierOrderDtlSaveVo.getSupplierOrderNo();
        long lineNum = supplierOrderDtlSaveVoList.size() + 1L;

        dtlVo.setSupplierOrderNo(supplierOrderNo);
        dtlVo.setSupplierOrderDtlNo(supplierOrderNo+"_"+ lineNum);
        dtlVo.setLineNum(lineNum);
        dtlVo.setCategoryId(spuDTO.getCatgoryId()); // 平台管理分类ID
        remoteSaveOrderVO.getSupplierOrderDtlSaveVOS().add(dtlVo);

        // 商品赠品 大中小信息
        orderDiscountDtlSaveVO.setGiftUnitType(dtlVo.getOrderUnitType());
        orderDiscountDtlSaveVO.setGiftUnit(dtlVo.getOrderUnit());
        orderDiscountDtlSaveVO.setGiftUnitSize(dtlVo.getOrderUnitSize());
        orderDiscountDtlSaveVO.setSupplierOrderDtlNo(dtlVo.getSupplierOrderDtlNo());
        orderDiscountDtlSaveVO.setActivityDiscountAmt(dtlVo.getActivityDiscountAmt());
        orderDiscountDtlSaveVO.setDiscountName(promotion.getName());

        // 更新入驻商订单优化金额字段
        remoteSaveOrderVO.getSupplierOrderSaveVOs().stream()
                .filter(supplierOrderSaveVO -> supplierOrderSaveVO.getSupplierId().equals(promotion.getSupplierId()))
                .forEach(supplierOrderSaveVO -> {
                    supplierOrderSaveVO.setSubDiscountAmt(supplierOrderSaveVO.getSubDiscountAmt().add(dtlVo.getActivityDiscountAmt()));
                    supplierOrderSaveVO.setSubOrderAmt(supplierOrderSaveVO.getSubOrderAmt().add(dtlVo.getActivityDiscountAmt()));
                });

        // 更新订单优惠金额字段
        remoteSaveOrderVO.getOrderSaveVo().setDiscountAmt(remoteSaveOrderVO.getOrderSaveVo().getDiscountAmt().add(dtlVo.getActivityDiscountAmt()));
        remoteSaveOrderVO.getOrderSaveVo().setOrderAmt(remoteSaveOrderVO.getOrderSaveVo().getOrderAmt().add(dtlVo.getActivityDiscountAmt()));
    }

    /**
     * 订单明细数据添加组合商品明细商品数据
     * @param supplierOrderVo  入驻商订单数据
     * @param orderSaveVO 总订单数据
     * @param promotionItem 促销数据详情
     * @param promotion 促销数据主数据
     * @param spuCombineSkuVO 组合促销子商品数据
     * @return  返回入驻商明细数据详情
     */
    private TrdSupplierOrderDtlSaveVO supplierDtlSaveAddCbActiviyItem(TrdSupplierOrderSaveVO supplierOrderVo, TrdOrderSaveVO orderSaveVO, TradePriceCalculateResp.PromotionItem promotionItem,
                                                 TradePriceCalculateResp.Promotion promotion, SpuCombineSkuVO spuCombineSkuVO, TradePriceCalculateResp.OrderItem.SupplierItem supplierItem) {
        SkuDTO skuDTO = portalCacheService.getSkuDTO(spuCombineSkuVO.getSkuId());
        if (ToolUtil.isEmpty(skuDTO))
            throw new ServiceException("赠品SKU【" + spuCombineSkuVO.getSkuId() + "】未查找到数据！");

        SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
        if (ToolUtil.isEmpty(spuDTO))
            throw new ServiceException("赠品SKU【" + skuDTO.getSkuId() + "】未查找到对应的SPU商品数据！");

        TrdSupplierOrderDtlSaveVO dtlVo = TradeOrderConvert.INSTANCE.convertCbSupplierOrderDtl(spuCombineSkuVO, promotionItem, spuDTO);
        dtlVo.setSupplierId(supplierOrderVo.getSupplierId())
                        .setTotalNum(BigDecimal.valueOf(spuCombineSkuVO.getQty()))
                .setGiftFlag(NumberPool.INT_ZERO)
                .setMemo("活动【" + promotion.getName() + "】绑定商品")
                .setCouponDiscountAmt(BigDecimal.ZERO)
                .setCouponDiscountAmt2(BigDecimal.ZERO)
                .setItemType(orderSaveVO.getOrderType())
                .setOrderUnitSize(spuDTO.getUnitSizeQty(spuCombineSkuVO.getSkuUnitType()))
                .setActivityIdInfo(promotion.getId() + "")


                .setActivityDiscountAmt(dtlVo.getActivityDiscountAmt().add(spuCombineSkuVO.getOtherDiscountAmt()))
                .setOrderUnitQty((long) spuCombineSkuVO.getQty() * supplierItem.getCount())
                .setOrderUnitPrice(spuCombineSkuVO.getSuggestPrice())
                .setSalePrice(spuCombineSkuVO.getSuggestPrice().divide(dtlVo.getOrderUnitSize(), StatusConstants.PRICE_RESERVE_2, RoundingMode.HALF_EVEN))
                .setSubOrderAmt(dtlVo.getOrderUnitPrice().multiply(BigDecimal.valueOf(dtlVo.getOrderUnitQty())))

                ;


        // 更新入驻商订单优化金额字段
        supplierOrderVo.setSubDiscountAmt(supplierOrderVo.getSubDiscountAmt().add(dtlVo.getActivityDiscountAmt()).subtract(spuCombineSkuVO.getOtherDiscountAmt()));
        supplierOrderVo.setSubOrderAmt(supplierOrderVo.getSubOrderAmt().add(dtlVo.getActivityDiscountAmt()).subtract(spuCombineSkuVO.getOtherDiscountAmt()));

        // 更新订单优惠金额字段
        orderSaveVO.setDiscountAmt(orderSaveVO.getDiscountAmt().add(dtlVo.getActivityDiscountAmt()).subtract(spuCombineSkuVO.getOtherDiscountAmt()));
        orderSaveVO.setOrderAmt(orderSaveVO.getOrderAmt().add(dtlVo.getActivityDiscountAmt()).subtract(spuCombineSkuVO.getOtherDiscountAmt()));
        return dtlVo;
    }

    /**
     * 赠品优惠券绑定赠送条件商品行号处理
     * @param activityId
     * @param activityType
     * @param remoteSaveOrderVO
     */
    private void giftCouponHandle(Long activityId, String activityType, RemoteSaveOrderVO remoteSaveOrderVO) {
        // 得到这个营销活动下的所有商品（赠品以及正常品）
        List<TrdSupplierOrderDtlSaveVO> supplierOrderDtlSaveVOS = remoteSaveOrderVO.getSupplierOrderDtlSaveVOS()
                .stream().filter(dtl -> dtl.getActivityIdInfo().contains(activityId + "")).collect(Collectors.toList());

        // 这里处理下赠品优惠券需要绑定商品明细的行号数据，用于发券时验证是否可以发送
        String orderDtlNumStr;
        if (Objects.equals(activityType, TrdDiscountTypeEnum.BG.getType())) { // 买赠，只需要绑定原商品的行号
            orderDtlNumStr = supplierOrderDtlSaveVOS.stream()
                    .filter(dtl2 -> Objects.equals(dtl2.getGiftFlag(), NumberPool.INT_ZERO))
                    .map(dtl2 -> dtl2.getLineNum() + "")
                    .collect(Collectors.joining(";"));
        } else { // 满赠，需要绑定原商品和赠品的行号
            orderDtlNumStr = supplierOrderDtlSaveVOS.stream()
                    .map(dtl2 -> dtl2.getLineNum() + "")
                    .collect(Collectors.joining(";"));
        }
        // 参与此赠品优惠的销售商品行号信息， 用于记录赠品优惠券是否满足条件发送
        remoteSaveOrderVO.getOrderDiscountDtlSaveVOS().stream()
                .filter(dtl -> dtl.getDiscountId().equals(activityId) && Objects.equals(dtl.getGiftType(), NumberPool.LONG_ONE))
                .forEach(dtl -> dtl.setOrderDtlNumStr(orderDtlNumStr));
    }

    /**
     * @Description: 分页获取订单数据
     * @Author: liuxingyu
     * @Date: 2024/3/30 10:14
     */
    @Override
    public PageResult<OrderVO> pageOrderList(TrdOrderPageReqDTO orderPageReqDto) {
        return HutoolBeanUtils.toBean(orderApi.pageOrderList(orderPageReqDto), OrderVO.class);
    }

    /**
     * 获取我的订单的但前状态数量
     *
     * @return
     */
    @Override
    public OrderStatusVO getOrderStatus() {
        /*Long userId = MallSecurityUtils.getMemberId();
        Long branchId = MallSecurityUtils.getBranchId();
        //先查询redis中是否有缓存
        String redisKey = RedisConstants.TRD_ORDER_STATUS + userId;
        //根据用户id查询
        OrderStatusVO orderStatusVO = redisService.getCacheMapValue(redisKey, userId.toString());
        if (orderStatusVO != null) {
            return orderStatusVO;
        }
        orderStatusVO = orderApi.getOrderStatus(userId);
        if (ObjectUtils.isNotEmpty(orderStatusVO)) {
            redisService.setCacheMapValue(redisKey, userId.toString(), orderStatusVO);
            // 设置过期时间为1天
            redisService.expire(redisKey, 60 * 60 * 24);
        }*/
        return null;
    }

    /**
     * 统计单月的订单金额
     *
     * @return
     */
    @Override
    public OrderAmountStatisticsVO getMonthOrderAmountStatistics() {

        Long branchId = MallSecurityUtils.getBranchId();
        //先查询redis中是否有缓存
//        String redisKey = RedisConstants.TRD_ORDER_AMOUNT_STATISTICS + branchId;
//        OrderAmountStatisticsVO orderAmountStatisticsVO = redisService.getCacheObject(redisKey);
//        if (orderAmountStatisticsVO != null) {
//            return orderAmountStatisticsVO;
//        }
        OrderAmountStatisticsVO orderAmountStatisticsVO = orderApi.getOrderAmountStatisticsVO(branchId);

//        if (ObjectUtils.isNotEmpty(orderAmountStatisticsVO)) {
//            redisService.setCacheObject(redisKey, orderAmountStatisticsVO, 60 * 60L, TimeUnit.SECONDS);
//        }
        return orderAmountStatisticsVO;
    }

    /**
     * @Description: 获取常购商品
     * @Author: liuxingyu
     * @Date: 2024/5/7 11:30
     */
    @Override
    public PageResult<StoreProductRespVO> frequentSpuList(StoreProductRequest storeProductRequest) {
        //获取门店ID
        Long branchId = MallSecurityUtils.getLoginMember().getBranchId();
        if (ObjectUtil.isNull(branchId)){
            throw new ServiceException("门店编号异常");
        }
        storeProductRequest.setBranchId(branchId);
        //根据门店ID获取ES中门店常购商品信息
        PageResult<StoreProductRespVO> esStorePrs =  orderApi.getEsStoreProductList(storeProductRequest);
        //获取门店信息
        BranchDTO branchDto = portalCacheService.getBranchDto(branchId);
        //经营屏蔽
        if (Objects.nonNull(branchDto)) {
            esStorePrs =  this.splitSku(esStorePrs, branchDto);
        }

        esStorePrs.getList().forEach(x->{
            SkuDTO skuDTO = portalCacheService.getSkuDTO(x.getSkuId());
            SpuDTO spuDTO = portalCacheService.getSpuDTO(x.getSpuId());
            if (Objects.isNull(spuDTO) || Objects.isNull(skuDTO)) {
                return;
            }
            // 填充商品名称
            x.setSpuName(spuDTO.getSpuName());
            //填充图片
            x.setThumb(spuDTO.getThumb());
            x.setCatgoryId(spuDTO.getCatgoryId());
            //填充标准价
            x.setMarkPrice(skuDTO.getMarkPrice());
            //填充方案价格
            Integer unitSize = x.getUnitSize();
            if (ObjectUtil.equal(x.getItemType(), NumberPool.INT_ZERO)){
                x.setMarkPrice(skuPriceService.getSupplierSkuPrice(branchDto, unitSize, x.getSkuId()));
            }else {
                x.setMarkPrice(skuPriceService.getAreaSkuPrice(branchDto, unitSize, x.getSkuId()));
            }
            // 规格单位
            BigDecimal stockConvert = spuDTO.stockConvert(unitSize);
            //实时库存
            x.setSurplusSaleQty(StockUtil.stockDivide(redisStockService.getSurplusSaleQtyBigDecimal(x.getSkuId()), stockConvert).longValue());
            //属性
            x.setProperties(PropertyAndValDTO.getProperties(skuDTO.getProperties()));
            //起订
            //起订组数
            //限购
            x.setJumpOq(skuDTO.getJumpOq(unitSize));
            x.setMinOq(skuDTO.getMinOq(unitSize));
            x.setMaxOq(skuDTO.getMaxOq(unitSize));
            // 规格单位
            x.setUnit(spuDTO.getUnit(unitSize));
            // 与最小单位换算
            if (Objects.nonNull(x.getRecentlyPurchasedNumber())) {
                x.setRecentlyPurchasedNumber(StockUtil.stockDivide(x.getRecentlyPurchasedNumber(), stockConvert).longValue());
            }
            if (Objects.nonNull(x.getWithinPurchasedNumberTotal())) {
                x.setRecentlyPurchasedNumber(StockUtil.stockDivide(x.getWithinPurchasedNumberTotal(), stockConvert).longValue());
            }
            if (Objects.nonNull(x.getPurchasedNumberTotal())) {
                x.setRecentlyPurchasedNumber(StockUtil.stockDivide(x.getPurchasedNumberTotal(), stockConvert).longValue());
            }

            // 标记商品是否支持负库存下单
            Optional.ofNullable(portalCacheService.getSupplierDTO(x.getSupplierId())).ifPresent(supplierDTO -> {
                x.setIsNegativeStock(supplierDTO.getIsNegativeStock());
            });
        });
        return esStorePrs;
    }

    //经营屏蔽结果转换
    public PageResult<StoreProductRespVO> splitSku(PageResult<StoreProductRespVO> pageResult, BranchDTO branchDto){
        if(null == pageResult){
            return pageResult;
        }
        List<StoreProductRespVO> data = pageResult.getList();
        if(CollectionUtils.isEmpty(data)){
            return pageResult;
        }

        //经营屏蔽接口调用,兼容原接口，做数据转换
        List<SkuPageRespVO> pageList = SkuConvert.INSTANCE.convert2SkuPageRespVO(data);
        pageList = skuService.blockSkus(pageList, branchDto.getBranchId());
        log.info(" OrderServiceImpl.splitSku, 过滤前记录数[{}],过滤后记录数[{}]", data.size(), pageList.size());
        // 获取pageList中所有skuId的集合
        Set<Long> pageSkuIds = pageList.stream()
                .map(SkuPageRespVO::getSkuId)
                .collect(Collectors.toSet());

        // 过滤data中只存在于pageList的skuId的数据
        data = data.stream()
                .filter(product -> pageSkuIds.contains(product.getSkuId()))
                .collect(Collectors.toList());
        pageResult.setList(data);
        return pageResult;
    }

    @Override
    public void payPreSuccess(PayPreSuccessReqVO reqVO) {
        // 20 分钟校验有效期
        redisService.setCacheObject(
                RedisConstants.getPreSuccessPayOrderNo(reqVO.getBranchId(), reqVO.getOrderNo()),
                StringPool.ONE,
                60L,
                TimeUnit.MINUTES
        );
    }

    @Override
    public boolean validatePreSuccess(Long branchId, String orderNo) {
        return redisService.hasKey(RedisConstants.getPreSuccessPayOrderNo(branchId, orderNo));
    }

    @Override
    public OrderPayWayRespDTO getOrderPayWay(String orderNo) {
        TrdOrderRespDTO orderResp = orderApi.getOrderInfo(TrdOrderPageReqDTO.builder()
                        .keyWords(orderNo)
                        .branchId(MallSecurityUtils.getBranchId())
                .build()).getCheckedData();
        // 获取门店信息
        BranchDTO branch = portalCacheService.getBranchDto(orderResp.getBranchId());
        // 获取渠道信息
        ChannelDTO channel = new ChannelDTO();
        if (ToolUtil.isNotEmpty(branch) && ToolUtil.isNotEmpty(branch.getChannelId()))
            channel = portalCacheService.getChannelDto(branch.getChannelId());

        // 获取平台配置信息
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(MallSecurityUtils.getLoginMember().getSysCode());

        // 获取门店储值账户余额
        BranchBalanceRespVO branchBalanceRespVO = accountApi.getBranchBalance(branch.getBranchId()).getCheckedData();
        return TradeOrderConvert.INSTANCE.convertOrderPayWayResp(orderResp, branch, channel, payConfigDTO, branchBalanceRespVO);
    }

    @DistributedLock(prefix = RedisLockConstants.LOCK_ORDER_PAY, condition = "#orderNo", tryLock = true)
    @Override
    public void hdfkPaySuccess(String orderNo, String payWay) {
        if (ObjectUtil.isNull(orderNo) || ObjectUtil.isNull(payWay) || !payWay.equals(OrderPayWayEnum.HDFK.getPayWay())) {
            throw new ServiceException("订单编号为空或者订单支付方式不是货到付款");
        }
        TrdOrderRespDTO orderResp = orderApi.getOrderInfo(TrdOrderPageReqDTO.builder()
                .keyWords(orderNo)
                .branchId(MallSecurityUtils.getBranchId())
                .build()).getCheckedData();
        if (ObjectUtil.isNull(orderResp)) {
            throw new ServiceException("订单不存在");
        }
        if (!Objects.equals(orderResp.getPayState(), PayStateEnum.PAY_NOT_ONLINE.getCode())) {
            throw new ServiceException("订单状态异常！");
        }

        TrdPayOrderPageVO pageVo = new TrdPayOrderPageVO();
        pageVo.setOrderNo(orderResp.getOrderNo());
        pageVo.setPayWay(OrderPayWayEnum.HDFK.getPayWay());
        pageVo.setSuccessTime(DateUtils.getNowDate());
        // 获取支付平台
        PayConfigDTO payConfigDTO = portalCacheService.getPayConfigDTO(MallSecurityUtils.getLoginMember().getSysCode());
        pageVo.setPayPlatform(payConfigDTO.getStoreOrderPayPlatform());  // 订单支付平台

        // 发送货到付款订单支付成功消息 执行订单支付成功回调流程
        mallMqProducer.sendOrderHdfkSuccessEvent(pageVo);
    }

    /**
     * 验证客户是否购买了被屏蔽的商品
     * @param branchId
     * @param request
     */
    private void checkSkusIfBlock(Long branchId, SettleOrderRequest request) {
        CommonResult<List<Long>> commonResult = null;
        try {
            // 获取客户被屏蔽的商品
            commonResult = blockSchemeApi.getBlockSkusByBranchId(branchId);
            if (commonResult == null || !commonResult.isSuccess()) {
                log.warn("getBlockSkusByBranchId error, result: {}", JSON.toJSONString(commonResult));
                return;
            }
        } catch (Exception e) {
            log.error("check skus if block when settle order error: ", e);
            return;
        }
        List<Long> blockSkuIds = commonResult.getData();
        if (CollectionUtils.isEmpty(blockSkuIds)) {
            return;
        }
        List<SettleOrderRequest.Item.SupplierItem> blockedItems = request.getItems().stream()
                .flatMap(item -> item.getSupplierItems().stream())
                .filter(supplierItem -> blockSkuIds.contains(supplierItem.getSkuId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(blockedItems)) {
            String blockedSpuName = "";
            SpuDTO spuDTO = blockedItems.stream().map(SettleOrderRequest.Item.SupplierItem::getSpuId).filter(Objects::nonNull).findFirst().map(portalCacheService::getSpuDTO).orElse(null);
            if (spuDTO != null) {
                blockedSpuName = spuDTO.getSpuName();
            }
            throw new ServiceException(String.format("无权限购买%s商品，请刷新页面", blockedSpuName));
        }
    }

    /**
     * 将ERP库存不足信息转换为前端响应格式
     */
    private StockShortageRespVO convertToStockShortageResp(List<ErpStockShortageDTO> stockShortageDTOS) {
        StockShortageRespVO respVO = new StockShortageRespVO();
        if (CollectionUtils.isEmpty(stockShortageDTOS)) {
            return respVO;
        }

        List<StockShortageRespVO.StockShortageItemVO> shortageItems = stockShortageDTOS.stream().map(ErpStockShortageDTO::getDetailVos).flatMap(Collection::stream)
                .map(detail -> {
                    SkuDTO skuDTO = portalCacheService.getSkuDTO(detail.getSkuId());
                    SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
                    return new StockShortageRespVO.StockShortageItemVO()
                            .setItemNo(detail.getItemNo())
                            .setSkuId(String.valueOf(detail.getSkuId()))
                            .setSpuId(String.valueOf(spuDTO.getSpuId()))
                            .setSpuName(spuDTO.getSpuName())
                            .setRequestQty(detail.getMinDetailQty().divide(detail.getOrderUnitSize(), 0, RoundingMode.DOWN))
                            .setAvailableQty(detail.getEnableQty().divide(detail.getOrderUnitSize(), 0, RoundingMode.DOWN))
                            .setThumb(spuDTO.getThumb())
                            ;
                }).collect(Collectors.toList());

        respVO.setShortageItems(shortageItems);
        return respVO;
    }

}
