package com.zksr.portal.service.impl.handler.transaction;

import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.pool.StringPool;
import com.zksr.common.core.transaction.TransactionHandler;
import com.zksr.common.core.utils.SpringUtils;
import com.zksr.member.api.branch.BranchApi;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.member.api.branch.dto.MemBranchSaveReqVO;
import com.zksr.portal.service.IPortalCacheService;
import com.zksr.trade.api.order.vo.RemoteSaveOrderVO;

import java.util.Objects;

/**
 * 下单成功后更新门店信息事务处理器
 */
@SuppressWarnings("rawtypes")
public class OrderSuccessEditBranchTransactionHandler implements TransactionHandler {
    private final BranchApi branchApi = SpringUtils.getBean(BranchApi.class);

    private final IPortalCacheService portalCacheService = SpringUtils.getBean(IPortalCacheService.class);

    private final RemoteSaveOrderVO orderVo;

    private Integer firstOrderFlag;

    public OrderSuccessEditBranchTransactionHandler(RemoteSaveOrderVO orderVo) {
        this.orderVo = orderVo;
    }

    @Override
    //!@订单 - 创建 - 5、后置处理 - 6、更新门店信息
    public void doBusiness() {
        BranchDTO branchDTO = portalCacheService.getBranchDto(orderVo.getOrderSaveVo().getBranchId());
        firstOrderFlag = branchDTO.getFirstOrderFlag();
        if (Objects.isNull(firstOrderFlag) || firstOrderFlag == NumberPool.INT_ZERO) {
            // 系统首单, 更新已下单
            MemBranchSaveReqVO updateBranch = new MemBranchSaveReqVO();
            updateBranch.setBranchId(orderVo.getOrderSaveVo().getBranchId());
            updateBranch.setFirstOrderFlag(NumberPool.INT_ONE);
            updateBranch.setFirstOrderNo(orderVo.getOrderSaveVo().getOrderNo());
            branchApi.edit(updateBranch);
        }
    }

    @Override
    public void rollback() {
        if (Objects.isNull(firstOrderFlag) || firstOrderFlag == NumberPool.INT_ZERO) {
            // 回滚门店首单信息
            MemBranchSaveReqVO updateBranch = new MemBranchSaveReqVO();
            updateBranch.setBranchId(orderVo.getOrderSaveVo().getBranchId());
            updateBranch.setFirstOrderFlag(NumberPool.INT_ZERO);
            updateBranch.setFirstOrderNo(StringPool.EMPTY);
            branchApi.edit(updateBranch);
        }
    }
}
