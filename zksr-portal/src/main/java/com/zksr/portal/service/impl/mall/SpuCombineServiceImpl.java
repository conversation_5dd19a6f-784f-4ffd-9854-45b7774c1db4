package com.zksr.portal.service.impl.mall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.zksr.common.core.constant.PrdtConstants;
import com.zksr.common.core.domain.dto.car.AppCarIdDTO;
import com.zksr.common.core.enums.ProductType;
import com.zksr.common.core.pool.NumberPool;
import com.zksr.common.core.utils.StockUtil;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.elasticsearch.domain.EsYhProduct;
import com.zksr.member.api.branch.dto.BranchDTO;
import com.zksr.portal.controller.mall.vo.SkuPageRespVO;
import com.zksr.portal.controller.mall.vo.SpuDetailRespVO;
import com.zksr.portal.controller.mall.vo.car.YhPageSupplierGroupItemVO;
import com.zksr.portal.controller.mall.vo.spu.SpuActivityLabelVO;
import com.zksr.portal.controller.mall.vo.spu.SpuCombineSkuVO;
import com.zksr.portal.convert.activity.ActivityConvert;
import com.zksr.portal.convert.prdt.ProductConvert;
import com.zksr.product.api.areaItem.dto.AreaItemDTO;
import com.zksr.product.api.combine.SpuCombineDTO;
import com.zksr.product.api.combine.SpuCombineDtlDTO;
import com.zksr.product.api.material.vo.MaterialCacheVO;
import com.zksr.product.api.sku.dto.SkuDTO;
import com.zksr.product.api.spu.dto.SpuDTO;
import com.zksr.product.api.supplierItem.dto.SupplierItemDTO;
import com.zksr.promotion.api.activity.dto.ActivityVerifyItemDTO;
import com.zksr.promotion.api.activity.dto.PrmActivityDTO;
import com.zksr.promotion.api.activity.dto.SupplierActivityDTO;
import com.zksr.promotion.api.coupon.dto.OrderValidItemDTO;
import com.zksr.system.api.supplier.dto.SupplierDTO;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.zksr.common.core.exception.util.ServiceExceptionUtil.exception;
import static com.zksr.product.enums.ErrorCodeConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/12/30 8:47
 */
@SuppressWarnings("all")
@Service("spuCombineService")
public class SpuCombineServiceImpl extends AbsSpuItemInfoService {

    @Override
    public void renderItemList(SkuPageRespVO product, BranchDTO branchDTO) {
        SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(product.getSpuCombineId());
        if (ObjectUtil.isNotEmpty(spuCombineDTO)) {
            // 获取最小库存
            product.setStock(StockUtil.bigDecimal(getSpuCombineMinStock(spuCombineDTO)));
            product.setMarkPrice(skuPriceService.getSpuCombinePrice(branchDTO, spuCombineDTO, ProductType.formValue(product.getType())));
            product.setSkuId(NumberPool.LOWER_GROUND_LONG);
            product.setSpuId(spuCombineDTO.getSpuCombineId());
        }
        // 渲染入驻商标签
        SupplierDTO supplierDTO = portalCacheService.getSupplierDTO(product.getSupplierId());
        ProductConvert.INSTANCE.buildSetSupplier(product, portalCacheService.getPartnerSupplierOtherSettingPolicy(product.getSupplierId()));
        // 是否支持负库存下单
        product.setIsNegativeStock(supplierDTO.getIsNegativeStock());
    }

    @Override
    public void renderOddRate(SpuDetailRespVO detailReslut, SpuDTO spuDTO, BranchDTO branchDto) {
        // 组合商品暂时不管利润
    }

    @Override
    public SpuDetailRespVO getItemInfo(BranchDTO branchDto, AreaItemDTO areaItemDTO) {
        SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(areaItemDTO.getSpuCombineId());
        // 组合促销商品验证
        validate(spuCombineDTO, areaItemDTO.getActivityId());
        // 创建上架商品详情数据
        SpuDetailRespVO detailRespVO = getSpuDetailRespVO(spuCombineDTO);
        detailRespVO.setType(ProductType.LOCAL.getType())
                    .setItemId(areaItemDTO.getAreaItemId())
                    .setClassId(areaItemDTO.getAreaClassId())
                    .setSpuCombineId(areaItemDTO.getSpuCombineId())
                    // 前端判断逻辑是有促销价, 才会有划线价, 所以这里使用真实价格作为促销价, 然后计算一个套餐内SKU价格, 作为划线价使用
                    .setPromotionPrice(skuPriceService.getSpuCombinePrice(branchDto, spuCombineDTO, ProductType.LOCAL));
        // 获取有效库存
        List<SpuCombineSkuVO> combineSkuList = getCombineSkuList(spuCombineDTO, detailRespVO);
        detailRespVO.setStock(combineSkuList.stream().mapToInt(SpuCombineSkuVO::getMaxQty).min().getAsInt());
        detailRespVO.setCombineSkuList(combineSkuList);
        return detailRespVO;
    }

    @Override
    public SpuDetailRespVO getItemInfo(BranchDTO branchDto, SupplierItemDTO supplierItemDTO) {
        SpuCombineDTO spuCombineDTO = portalCacheService.getSpuCombineDTO(supplierItemDTO.getSpuCombineId());
        // 组合促销商品验证
        validate(spuCombineDTO, supplierItemDTO.getActivityId());
        // 创建上架商品详情数据
        SpuDetailRespVO detailRespVO = getSpuDetailRespVO(spuCombineDTO);
        detailRespVO.setType(ProductType.GLOBAL.getType())
                    .setItemId(supplierItemDTO.getSupplierItemId())
                    .setClassId(supplierItemDTO.getSaleClassId())
                    .setSpuCombineId(spuCombineDTO.getSpuCombineId())
                    // 前端判断逻辑是有促销价, 才会有划线价, 所以这里使用真实价格作为促销价, 然后计算一个套餐内SKU价格, 作为划线价使用
                    .setPromotionPrice(skuPriceService.getSpuCombinePrice(branchDto, spuCombineDTO, ProductType.GLOBAL));
        // 获取有效库存, 组合商品明细数据
        List<SpuCombineSkuVO> combineSkuList = getCombineSkuList(spuCombineDTO, detailRespVO);
        detailRespVO.setStock(combineSkuList.stream().mapToInt(SpuCombineSkuVO::getMaxQty).min().getAsInt());
        detailRespVO.setCombineSkuList(combineSkuList);
        return detailRespVO;
    }

    @Override
    public void renderActivityItemList(BranchDTO branchDTO, List<SkuPageRespVO> itemList, List<SupplierActivityDTO> supplierActivityList) {
        // 拆上架, 区分全国本地
        itemList.stream().collect(Collectors.groupingBy(SkuPageRespVO::getType)).forEach((productTypeStr, productList) -> {
            // 上架类型
            ProductType productType = ProductType.formValue(productTypeStr);
            // 组合商品
            List<SkuPageRespVO> combineProductList = productList.stream().filter(SkuPageRespVO::spuCombine).collect(Collectors.toList());
            // 获取spu 和 促销活动的绑定关系
            Map<Long, List<SupplierActivityDTO>> skuActivity = this.getActivityService().applySpuActivity(branchDTO, productType, ActivityConvert.INSTANCE.convertVerifyItemListBySpuCombine(combineProductList));
            // 组合促销商品绑定的促销活动
            combineProductList.forEach((pageRespVO) -> {
                // sku 绑定活动列表
                List<SupplierActivityDTO> activityDTOS = skuActivity.get(pageRespVO.getSpuId());
                // 渲染活动素材
                List<SpuActivityLabelVO> labelVOList = ActivityConvert.INSTANCE.convertSpuListActivity(activityDTOS);
                if (Objects.nonNull(labelVOList)) {
                    for (SpuActivityLabelVO labelVO : labelVOList) {
                        MaterialCacheVO material = portalCacheService.getMaterial(MaterialCacheVO.getActivityCacheKey(labelVO.getActivityId()));
                        if (StringUtils.isNotEmpty(material.getValidateMaterial())) {
                            pageRespVO.setMaterialUrl(material.getValidateMaterial());
                        }
                    }
                }
                pageRespVO.setSpuActivityLabelList(labelVOList);
            });
        });
    }

    @Override
    public void renderActivityItemDetail(BranchDTO branch, SpuDetailRespVO detailReslut) {
        ProductType productType = ProductType.formValue(detailReslut.getType());
        Long spuCombineId = detailReslut.getSpuCombineId();

        SpuCombineDTO combineDTO = portalCacheService.getSpuCombineDTO(detailReslut.getSpuCombineId());

        // 创建组合促销商品验证VO
        ActivityVerifyItemDTO verifyItemDTO = new ActivityVerifyItemDTO();
        verifyItemDTO.setType(productType.getType());
        verifyItemDTO.setSupplierId(detailReslut.getSupplierId());
        verifyItemDTO.setCategoryId(combineDTO.getCategoryId());
        verifyItemDTO.setSpuId(spuCombineId);

        // 获取spu 和 促销活动的绑定关系
        Map<Long, List<SupplierActivityDTO>> skuActivity = this.getActivityService().applySpuActivity(branch, productType, ListUtil.toList(verifyItemDTO));
        List<SupplierActivityDTO> activityDTOS = skuActivity.get(spuCombineId);
        detailReslut.setActivityList(this.getActivityService().getSpuDetailActivityLabelVOS(activityDTOS));
    }

    @Override
    public YhPageSupplierGroupItemVO getYhItemInfoVO(AreaItemDTO areaItem, EsYhProduct esItem, BranchDTO branchDTO) {
        SpuCombineDTO combineDTO = portalCacheService.getSpuCombineDTO(areaItem.getSpuCombineId());

        YhPageSupplierGroupItemVO itemVO = YhPageSupplierGroupItemVO.builder()
                .yhId(esItem.getYhId())
                .itemId(esItem.getAreaItemId())
                .spuId(esItem.getSpuId())
                .skuId(esItem.getSkuId())
                .supplierId(combineDTO.getSupplierId())
                .categoryId(combineDTO.getCategoryId())
                .markPrice(skuPriceService.getSpuCombinePrice(branchDTO, combineDTO, ProductType.LOCAL))
                .productNum(esItem.getPosSuggestQty())
                .selected(esItem.getChecked() == NumberPool.INT_ONE)
                .spuName(combineDTO.getSpuCombineName())
                .spuThumb(combineDTO.getThumb())
                .specName(combineDTO.getSpecName())
                .skuUnit(String.valueOf(combineDTO.getUnit()))
                .minOq(Objects.nonNull(combineDTO.getMinOq()) ? combineDTO.getMinOq().longValue() : null)
                .jumpOq(Objects.nonNull(combineDTO.getJumpOq()) ? combineDTO.getJumpOq().longValue() : null)
                .maxOq(Objects.nonNull(combineDTO.getMaxOq()) ? combineDTO.getMaxOq().longValue() : null)
                .shelfStatus(areaItem.getShelfStatus(esItem.getMallUnitType()))
                .stockConvertRate(NumberPool.LONG_ONE)
                .unitSize(esItem.getMallUnitType())
                .yhInfo(esItem)
                .itemType(NumberPool.INT_ONE)
                .build();

        // 剩余库存
        Long balanceStock = this.getSpuCombineMinStock(combineDTO);
        itemVO.setStockQty(balanceStock);
        // 剩余库存数量 / 转换比例 = 当前最大可购买
        itemVO.setMaxQty(balanceStock / itemVO.getStockConvertRate());
        // 如有有提供单位信息, 回显单位
        itemVO.setUnit(String.valueOf(combineDTO.getUnit()));
        // 如果商品下架了, 那就取消选中
        if (itemVO.getShelfStatus() == NumberPool.INT_ZERO) {
            itemVO.setSelected(false);
        }
        // 设置应付金额
        itemVO.setPayAmt(itemVO.getMarkPrice().multiply(new BigDecimal(esItem.getPosSuggestQty())));
        return itemVO;
    }

    @Override
    public ActivityVerifyItemDTO buildActivityVerifyItemByCouponOrderValidate(BranchDTO branch, SupplierItemDTO supplierItem, OrderValidItemDTO item) {
        AppCarIdDTO carId = AppCarIdDTO.build(item.getCarId());
        SpuCombineDTO combineDTO = portalCacheService.getSpuCombineDTO(supplierItem.getSpuCombineId());
        // 全国商品价格
        BigDecimal salePrice = skuPriceService.getSpuCombinePrice(branch, combineDTO, supplierItem.getProductType());
        // 组装订单商品验证信息
        item.setAmt(salePrice.multiply(new BigDecimal(item.getNum())));
        item.setProductType(supplierItem.getProductType().getType());
        ActivityConvert.INSTANCE.buildSetOrderValid(item, combineDTO);
        // 组装促销验证信息
        ActivityVerifyItemDTO verifyItemDTO = ActivityConvert.INSTANCE.convertActivityDTO(item, salePrice);
        verifyItemDTO.setStockConvertRate(BigDecimal.ONE);
        verifyItemDTO.setItemId(supplierItem.getSupplierItemId());
        verifyItemDTO.setDiscountAmt(BigDecimal.ZERO);
        // 计算应付金额
        verifyItemDTO.setAdequateList(new ArrayList<>());
        verifyItemDTO.setPayAmt(NumberUtil.toBigDecimal(verifyItemDTO.getNum()).multiply(verifyItemDTO.getItemPrice()));
        return verifyItemDTO;
    }

    @Override
    public ActivityVerifyItemDTO buildActivityVerifyItemByCouponOrderValidate(BranchDTO branch, AreaItemDTO areaItem, OrderValidItemDTO item) {
        AppCarIdDTO carId = AppCarIdDTO.build(item.getCarId());
        SpuCombineDTO combineDTO = portalCacheService.getSpuCombineDTO(areaItem.getSpuCombineId());
        // 全国商品价格
        BigDecimal salePrice = skuPriceService.getSpuCombinePrice(branch, combineDTO, areaItem.getProductType());
        // 组装订单商品验证信息
        item.setAmt(salePrice.multiply(new BigDecimal(item.getNum())));
        item.setProductType(areaItem.getProductType().getType());
        ActivityConvert.INSTANCE.buildSetOrderValid(item, combineDTO);
        // 组装促销验证信息
        ActivityVerifyItemDTO verifyItemDTO = ActivityConvert.INSTANCE.convertActivityDTO(item, salePrice);
        verifyItemDTO.setStockConvertRate(BigDecimal.ONE);
        verifyItemDTO.setItemId(areaItem.getAreaItemId());
        verifyItemDTO.setDiscountAmt(BigDecimal.ZERO);
        // 计算应付金额
        verifyItemDTO.setAdequateList(new ArrayList<>());
        verifyItemDTO.setPayAmt(NumberUtil.toBigDecimal(verifyItemDTO.getNum()).multiply(verifyItemDTO.getItemPrice()));
        return verifyItemDTO;
    }

    /**
     * 验证组合促销商品活动
     * @param spuCombineDTO 组合促销
     * @param acitivtyId    促销活动ID
     */
    public void validate(SpuCombineDTO spuCombineDTO, Long acitivtyId) {
        if (Objects.isNull(spuCombineDTO)) {
            throw exception(PRDT_COMBINE_NOT_EXISTS);
        }
        PrmActivityDTO activityDTO = portalCacheService.getActivityDto(acitivtyId);
        if (Objects.isNull(activityDTO)) {
            throw exception(PRDT_COMBINE_ACTIVITY_NOT_EXISTS);
        }
        // 促销状态;0-未启用 1-启用 2-停用 3-已失效
        if (activityDTO.getPrmStatus() != 1) {
            throw exception(PRDT_COMBINE_ACTIVITY_NOT_START);
        }
        // 活动已结束
        if (activityDTO.getEndTime().getTime() < System.currentTimeMillis()) {
            throw exception(PRDT_COMBINE_ACTIVITY_ALREADY_FINISH);
        }
    }

    /**
     * 获取组合促销商品详情数据
     */
    private List<SpuCombineSkuVO> getCombineSkuList(SpuCombineDTO spuCombineDTO, SpuDetailRespVO spuDetailRespVO) {
        spuDetailRespVO.setMarkPrice(BigDecimal.ZERO);
        List<SpuCombineSkuVO> combineSkuList = new ArrayList<>();
        for (SpuCombineDtlDTO combineDtl : spuCombineDTO.getCombineDtls()) {
            SkuDTO skuDTO = portalCacheService.getSkuDTO(combineDtl.getSkuId());
            SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
            Long skuStock = redisStockService.getSurplusSaleQty(combineDtl.getSkuId());
            // 在当前组合条件下,能支持组合商品下单的次数
            Long unitSizeQty = spuDTO.getUnitSizeQty(combineDtl.getSkuUnitType()).longValue();
            // 最小库存 / (条件数量 * 转换比例)
            long maxQty = skuStock / (combineDtl.getQty() * unitSizeQty);
            // 组合商品详情组合数据
            SpuCombineSkuVO spuCombineSkuVO = ProductConvert.INSTANCE.builderCombineSkuVO(spuDTO, skuDTO, combineDtl);
            spuCombineSkuVO.setUnit(spuDTO.getUnit(combineDtl.getSkuUnitType()));
            spuCombineSkuVO.setMaxQty(Integer.parseInt(String.valueOf(maxQty)));
            spuCombineSkuVO.setMarkPrice(skuDTO.getMarkPrice(combineDtl.getSkuUnitType()));
            spuCombineSkuVO.setSuggestPrice(skuDTO.getSuggestPrice(combineDtl.getSkuUnitType()));

            // 组合促销商品,子SKU生产日期调整
            if (Objects.nonNull(spuDTO.getLatestDate())) {
                spuCombineSkuVO.setLatestDate(DateUtil.format(spuDTO.getLatestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
                spuCombineSkuVO.setOldestDate(DateUtil.format(spuDTO.getOldestDate(), PrdtConstants.PRODUCE_DATE_FORMAT));
            }

            // 返回集合
            combineSkuList.add(spuCombineSkuVO);
            // 合计套餐sku价格为划线价
            spuDetailRespVO.setMarkPrice(
                    spuDetailRespVO.getMarkPrice().add(spuCombineSkuVO.getMarkPrice().multiply(new BigDecimal(combineDtl.getQty())).setScale(2, RoundingMode.HALF_UP))
            );
        }
        return combineSkuList;
    }

    /**
     * 获取组合促销主商品信息
     */
    private SpuDetailRespVO getSpuDetailRespVO(SpuCombineDTO spuCombineDTO) {
        SpuDetailRespVO detailReslut = new SpuDetailRespVO();
        detailReslut.setSpuName(spuCombineDTO.getSpuCombineName())
                .setSupplier(getSupplierByid(spuCombineDTO.getSupplierId()))
                .setSpuNo(spuCombineDTO.getSpuCombineNo())
                .setDetails(spuCombineDTO.getDetails())
                .setThumb(spuCombineDTO.getThumb())
                .setImages(spuCombineDTO.getImages())
                .setItemType(NumberPool.INT_ONE)
                .setIsSpecs(NumberPool.LONG_ZERO)
                .setMarkPrice(spuCombineDTO.getMarkPrice())
                .setSpecName(spuCombineDTO.getSpecName())
                .setSupplierId(spuCombineDTO.getSupplierId())
                .setMinOq(spuCombineDTO.getMinOq())
                .setJumpOq(spuCombineDTO.getJumpOq())
                .setMaxOq(spuCombineDTO.getMaxOq())
                .setSpuId(spuCombineDTO.getSpuCombineId())
                .setUnit(String.valueOf(spuCombineDTO.getUnit()))
        ;
        return detailReslut;
    }

    /**
     * 获取组合商品最小库存数
     * @param spuCombineDTO
     * @return  组合商品最小库存
     */
    public Long getSpuCombineMinStock(SpuCombineDTO spuCombineDTO) {
        ArrayList<Long> stockList = new ArrayList<>();
        for (SpuCombineDtlDTO combineDtl : spuCombineDTO.getCombineDtls()) {
            SkuDTO skuDTO = portalCacheService.getSkuDTO(combineDtl.getSkuId());
            SpuDTO spuDTO = portalCacheService.getSpuDTO(skuDTO.getSpuId());
            Long skuStock = redisStockService.getSurplusSaleQty(combineDtl.getSkuId());
            // 在当前组合条件下,能支持组合商品下单的次数
            Long unitSizeQty = spuDTO.getUnitSizeQty(combineDtl.getSkuUnitType()).longValue();
            // 最小库存 / (条件数量 * 转换比例)
            long maxQty = skuStock / (combineDtl.getQty() * unitSizeQty);
            stockList.add(maxQty);
        }
        if (stockList.isEmpty()) {
            return NumberPool.LONG_ZERO;
        }
        // 获取最小的库存
        stockList.sort(Comparator.comparing(Long::longValue));
        return stockList.get(0);
    }
}
