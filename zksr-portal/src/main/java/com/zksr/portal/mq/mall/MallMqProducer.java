package com.zksr.portal.mq.mall;

import com.zksr.common.elasticsearch.model.dto.ColonelAppBranchDTO;
import com.zksr.common.rocketmq.constant.MessageConstant;
import com.zksr.trade.api.order.vo.TrdPayOrderPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.GenericMessage;

import java.util.HashMap;
import java.util.Map;

import static com.zksr.member.constant.MemberConstant.ES_COLONEL_APP_BRANCH_TYPE_1;
import static com.zksr.member.constant.MemberConstant.ES_COLONEL_APP_BRANCH_TYPE_4;

/**
*
 * 商城模块 生产者
* <AUTHOR>
* @date 2024/5/9 11:39
*/
@Configuration
@Slf4j
public class MallMqProducer {
    @Autowired
    private StreamBridge streamBridge;

    /**
     * 业务员APP统计客户信息
     * 消费者 {@link com.zksr.member.mq.MemberAppMqConsumer#colonelAppBranchEvent()}
     * @param branchDTO
     */
    public void sendEsColonelAppBranchEvent(ColonelAppBranchDTO branchDTO){
        log.info("业务员APP统计客户信息类型:更新登陆时间,门店编号:{},平台商编号{}",branchDTO.getBranchId(),branchDTO.getSysCode());
        branchDTO.setType(ES_COLONEL_APP_BRANCH_TYPE_1);
        streamBridge.send(
                MessageConstant.COLONEL_APP_BRANCH_TOPIC_OUT_PUT,
                MessageBuilder.withPayload(branchDTO).build());

    }

    /**
     * 发送货到付款订单成功消息
     * @param data
     */
    public void sendOrderHdfkSuccessEvent(TrdPayOrderPageVO data){
        log.info("发送货到付款订单支付成功消息，订单信息{}", data);
        Map<String, Object> headers = new HashMap<>();
        headers.put(MessageConst.PROPERTY_DELAY_TIME_LEVEL, 2);
        Message<Object> msg = new GenericMessage<>(data, headers);
        streamBridge.send(
                MessageConstant.ORDER_HDFK_SUCCESS_TOPIC_OUT_PUT,
                msg);
    }

}
