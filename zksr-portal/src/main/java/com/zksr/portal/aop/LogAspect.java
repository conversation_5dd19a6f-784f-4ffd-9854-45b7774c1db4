package com.zksr.portal.aop;

import com.alibaba.fastjson.JSON;
import com.zksr.common.core.utils.ToolUtil;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.Map;

@Aspect
@Component
public class LogAspect {

	private static final Logger logger = LoggerFactory.getLogger(LogAspect.class);


	// 存在SQL注入风险
	private static final String IS_SQL_INJECTION = "输入参数存在SQL注入风险";

	private static final String UNVALIDATED_INPUT = "输入参数含有非法字符";

	private static final String ERORR_INPUT = "输入的参数非法";

	@Pointcut("(execution(public * com.zksr.portal.controller.*.*.*(..)))")
	private void controllerAspect() {

	}

	/**
	 *
	 * @param joinPoint
	 */
	@Before(value = "controllerAspect()")
	public void methedBefore(JoinPoint joinPoint) {
		// 通过RequestContextHolder 获取request
		ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request = requestAttributes.getRequest();

		try {
			String url = request.getRequestURL().toString(); // 统一资源定位符
			String method = request.getMethod(); // 请求方式
			String queryString = request.getQueryString(); // 获取查询字符串 请求类型为GET可以获取所有参数
			Object[] args = joinPoint.getArgs(); // 获取连接点方法运行时的入参列表

			String params = StringUtils.EMPTY;

			if (args != null && args.length > 0) {
				if (StringUtils.equals(RequestMethod.GET.name(), method)) {
					params = queryString;
				} else if (StringUtils.equals(RequestMethod.GET.name(), method)) {
					StringBuffer sb = new StringBuffer();
					// 获取所有参数
					Enumeration<String> paramter = request.getParameterNames();
					while (paramter.hasMoreElements()) {
						String str = (String) paramter.nextElement();
						sb.append(str + "={" + request.getParameter(str) + "}");
					}
					params = sb.toString();
				}
				if(ToolUtil.isNotEmpty(params)){
					params = URLDecoder.decode(params, "UTF-8");
				}

				if(HttpMethod.PUT.name().equals(method) || HttpMethod.POST.name().equals(method)){
					params = argsArrayToString(joinPoint.getArgs());
					params = com.zksr.common.core.utils.StringUtils.substring(params, 0, 2000);
				}
			}

			logger.info("统一资源定位符={} 请求参数={} IP={}", url, params, request.getRemoteAddr());
			logger.info("请求参数={}", params);
		} catch (Exception e) {
			logger.error("### LogAspectService.class methodBefore() ### ERROR={}", e);
		}
	}

	/**
	 * 参数拼装
	 */
	private String argsArrayToString(Object[] paramsArray)
	{
		String params = "";
		if (paramsArray != null && paramsArray.length > 0)
		{
			for (int i = 0; i < paramsArray.length; i++)
			{
				if (!isFilterObject(paramsArray[i]))
				{
					try
					{
						Object jsonObj = JSON.toJSON(paramsArray[i]);
						params += jsonObj.toString() + " ";
					}
					catch (Exception e)
					{
					}
				}
			}
		}
		return params.trim();
	}

	/**
	 * 判断是否需要过滤的对象。
	 *
	 * @param o 对象信息。
	 * @return 如果是需要过滤的对象，则返回true；否则返回false。
	 */
	@SuppressWarnings("rawtypes")
	public boolean isFilterObject(final Object o)
	{
		Class<?> clazz = o.getClass();
		if (clazz.isArray())
		{
			return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
		}
		else if (Collection.class.isAssignableFrom(clazz))
		{
			Collection collection = (Collection) o;
			for (Iterator iter = collection.iterator(); iter.hasNext();)
			{
				return iter.next() instanceof MultipartFile;
			}
		}
		else if (Map.class.isAssignableFrom(clazz))
		{
			Map map = (Map) o;
			for (Iterator iter = map.entrySet().iterator(); iter.hasNext();)
			{
				Map.Entry entry = (Map.Entry) iter.next();
				return entry.getValue() instanceof MultipartFile;
			}
		}
		return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse || o instanceof BindingResult;
	}

	@Around("controllerAspect()")
	public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
		// 获取目标Logger
		Logger logger = LoggerFactory.getLogger(joinPoint.getTarget().getClass());

		// 获取目标类名称
		String clazzName = joinPoint.getTarget().getClass().getName();

		// 获取目标类方法名称
		String methodName = joinPoint.getSignature().getName();

		long start = System.currentTimeMillis();
		Object o =  joinPoint.proceed();

		long time = System.currentTimeMillis() - start;
		logger.info( "{}: {}: end... cost time: {} ms", clazzName, methodName, time);

		return o;
	}

}
