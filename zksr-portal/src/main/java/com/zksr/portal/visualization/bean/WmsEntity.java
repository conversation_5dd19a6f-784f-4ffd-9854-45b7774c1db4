package com.zksr.portal.visualization.bean;


import lombok.Data;
import org.springframework.boot.actuate.endpoint.OperationType;

import java.beans.Transient;

@Data
public class WmsEntity extends BaseEntity {

    private OperationType operationType;

    private String wmsSyncShipperNo;  // 用于wms4.0传递时数据时使用

    private String wmsBranchNo;

    @Transient
    public String getWmsSyncShipperNo() {
        return wmsSyncShipperNo;
    }

    public void setWmsSyncShipperNo(String wmsSyncShipperNo) {
        this.wmsSyncShipperNo = wmsSyncShipperNo;
    }




    @Transient
    public OperationType getOperationType() {
        return operationType;
    }

    public void setOperationType(OperationType operationType) {
        this.operationType = operationType;
    }

    @Transient
    public String getWmsBranchNo() {
        return wmsBranchNo;
    }

    public void setWmsBranchNo(String wmsBranchNo) {
        this.wmsBranchNo = wmsBranchNo;
    }
}
