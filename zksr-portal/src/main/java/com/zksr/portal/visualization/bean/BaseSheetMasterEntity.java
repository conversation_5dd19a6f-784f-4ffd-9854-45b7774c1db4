package com.zksr.portal.visualization.bean;

import java.math.BigDecimal;
import java.util.Date;


import lombok.Data;

/**
  	*单据 - 基类
	* 
    * <AUTHOR>
    */  
@Data
public class BaseSheetMasterEntity extends WmsEntity {
	private static final long serialVersionUID = 1L;
	protected String sheetNo;			//单号
	protected String branchNo;			//门店编号
	protected String transNo;			//单据类型
	protected String operId;			//操作员
	protected String confirmMan;		//审核人
	protected Date workDate;			//审核日期
	protected String approveFlag;		//审核标志
	protected String memo;				//备注
	protected BigDecimal sheetAmt;		//单据总金额
	protected BigDecimal sheetQty;		//单据商品总数量 realQty之和
	protected BigDecimal sheetTaxAmt;  	//单据税额
	protected String appointBranchNo;  // 指定仓库

	}
