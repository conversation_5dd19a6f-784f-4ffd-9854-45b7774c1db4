package com.zksr.portal.visualization.bean;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;


/**
 * 实体类 - 基类
 */

@Data
public class BaseEntity implements Serializable {

	private static final long serialVersionUID = -6718838800112233445L;
	
	public static final String CREATE_DATE_PROPERTY_NAME = "createDate";// "创建日期"属性名称
	public static final String MODIFY_DATE_PROPERTY_NAME = "modifyDate";// "修改日期"属性名称
	public static final String ON_SAVE_METHOD_NAME = "onSave";// "保存"方法名称
	public static final String ON_UPDATE_METHOD_NAME = "onUpdate";// "更新"方法名称
	public static final String STATE_MODIFY = "modified";// 更新
	public static final String STATE_ADD = "added";//增加
	public static final String STATE_REMOVE = "removed";// 删除
	protected String id;// ID   len=35
	protected Date createDate;// 创建日期
	protected Date modifyDate;// 修改日期
	protected Integer _index;
	protected Integer _uid;
	protected String _state;

	
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		return result;
	}

}