package com.zksr.portal.visualization.bean;

import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 配送退货单单据主表
 * <AUTHOR> 20170331
 * @version 1.0
 */
@Data
public class DrSheetDetail extends BaseEntity {
	private static final long serialVersionUID = 4405666539700482148L;
    
	private String voucherId = null;                    // 引入 原始ID
	
	private String origionId = null;                    //第三方外部Id;
	
	private String voucherNo = null;                    // 引入 单据SheetNo
	
    private String voucherYHNo = null;                  // 原始要货单号
	
    private String sheetNo = null;                      // 单据号
    
    private String itemNo = null;                      // 货号

    private BigDecimal realQty = null;                 // 实际数量
    
    private BigDecimal cyQty = null;                   // 仓库实收数量

	private BigDecimal actualQty = null;               // 可退数量
    
    private BigDecimal shouldQty = null;               // 已收数量
    
    private BigDecimal alreadyQty = null;              // 已退数量
    
    private BigDecimal stockQty;				       //审核时的库存 出库仓库库存
    
    private BigDecimal orgiPrice = null;                // 原价
    
    private BigDecimal validPrice = null;               // 实价
    
    private BigDecimal halvePrice = null;               // 平摊实价
    
    private BigDecimal inPrice;                         // 配送进价

    private BigDecimal price = BigDecimal.ZERO;         // 减去优惠金额之前的价格
    
    private BigDecimal amount  = BigDecimal.ZERO;       // 减去优惠金额之前的金额
    
    private BigDecimal mjAmt = BigDecimal.ZERO;         // 单品分摊后满减的优惠金额
    
    private BigDecimal couponsAmt = BigDecimal.ZERO;    // 单品分摊后优惠券的优惠金额
    
    private BigDecimal subAmt = null;                   // 金额
    
    private BigDecimal halveAmt = null;                   //平摊金额
    
    private BigDecimal saleTax = null;                   // 销项税率
    
    private BigDecimal purchaseTax = null;                // 进项税率
    
    private BigDecimal validDay = new BigDecimal(0);	//商品有效期
   
    private String memo = null;                         // 备注
    
    private Date validDate = null;                     	// 商品有效期
    
    
    private BigDecimal proposeRate;                     //提成率
    
    private String shelf;                               // 货架
    
    private String batchNo = null;						// 批次号
    
    private String branchBatchNo;					    // 门店批次编号    
    
    private BigDecimal specQty;                         //商品规格数量（最小单位数量）
	
	private BigDecimal itemSpec;                        //商品规格
	
	private String specNo;                              //规格编号
	
    private String bindItemNo = "";					    // 衍生商品编号
	
    private BigDecimal bindQty = BigDecimal.ZERO;       // 衍生商品数量
    
  	private String deriveItemNo;                        // 衍生商品子商品编号
	
  	private BigDecimal deriveQty= BigDecimal.ZERO;      // 衍生商品子商品数量
  	
  	private BigDecimal deviverPrice= BigDecimal.ZERO;   // 衍生商品子商品单价
  	
	private BigDecimal specPrice;                       //商品规格价格（最小单位单价）
	
	private String orderMan = null;                     // 业务员
	
	private BigDecimal weight;		//商品重量
	
	private String refundWay;								//退款方式   0-退现金   1-退储值 2-原路退货
    private String refundState="0";							//退款状态 0-未退款 1-已退款  2-退款中
    private BigDecimal refundAmt;							//退款金额
    private Date refundTime;								//退款时间
    private String refundMan;								//退款人
	private Integer doLineNum;								//出库单行号
	
    private String receiveFlag;      //wms系统配置   收货标识   0未收货    1部分收货   2完全收货
    private BigDecimal leaveQty;     //wms剩余可收数量        
    
	private String itemType;//商品类型   0 捆绑促销  1 单品    2 赠品  
	/**商品类型   1 单品    (2 赠品   0 捆绑促销)*/
	public static String itemType_danpin="1";
	/**商品类型  2 赠品     (1 单品  0 捆绑促销)*/
	public static String itemType_zengpin="2";
	/**商品类型   0 捆绑促销    (1 单品 2 赠品  )*/
	public static String itemType_kunbangcuxiao="0";
	
	private String platformOrderMan = null;          // 平台业务员
	private String shipperOrderMan = null;           // 货主业务员
	private String itemProcessType;                  //商品处理方式   配送中心/仓库
    private String payWay = null;                    // 付款方式     0：货到付款 1：在线支付 2：预付款支付 3:积分支付 4:混合支付 5:兑换券支付 
	private Date productionDate; 					 // 生产日期
	private String returnType; 		                 // 退货状态   1：临期  2:滞销 3:过期
    private String imgName;							 // 退货商品图片
	private String doSheetNo;			//出库单，用于退货单打印显示
	
	private String wareLocator;			//库位
	private String itemBrandname;
	
	private BigDecimal volume; 				 	//商品体积 volume * realQty
	private Integer line;

}
