package com.zksr.portal.visualization.bean;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
*
 *财务系统工具类
* <AUTHOR>
* @date 2023/3/14 17:25
*/
public class FinancialGatewayUtil {
    /*自定义接口业务类型商品基本信息*/
    public static String MODEL_TYPE_ITEM = "ITEM";
    /*自定义接口业务类型商品基本信息*/
    public static String MODEL_TYPE_BRANCH = "BRANCH";
    /*自定义接口业务类型资金账户*/
    public static String MODEL_TYPE_ACCPAY = "ACCPAY";
    /*自定义接口业务类型商品基本信息*/
    public static String MODEL_TYPE_YH = "YH";
    /*自定义接口业务类型供应商信息*/
    public static String MODEL_TYPE_SUPCUST = "SUPCUST";
    /*自定义接口业务类型采购计划单信息*/
    public static String MODEL_TYPE_PO = "PO";
    /*自定义接口业务类型采购退货单信息*/
    public static String MODEL_TYPE_RO = "RO";
    /*自定义接口业务类型货主采购计划单信息*/
    public static String MODEL_TYPE_PL = "PL";
    /*自定义接口业务类型采购计划单信息*/
    public static String MODEL_TYPE_IO = "IO";
    /*自定义接口业务类型货主出库单信息*/
    public static String MODEL_TYPE_SH = "SH";
    /*自定义接口业务类型配送退货单*/
    public static String MODEL_TYPE_DR = "DR";
    /*自定义接口业务类型财务*/
    public static String MODEL_TYPE_FINANCIAL = "FINANCIAL";
    /*财务接口同步商品基本信息*/
    public static String SYN_BAISC = "financial.synBaisc";
    /*财务接口同步配送中心商品*/
    public static String SYN_ITEM_INFO = "financial.synItemInfo";
    /*财务接口同步供应商*/
    public static String SYN_SUPCUST_INFO = "financial.synSupcustInfo";
    /*财务接口同步门店信息*/
    public static String SYN_BRANCH_INFO = "financial.synBranchInfo";
    public static String FINANCIAL_DO = "financial.do";
    /*财务接口同步采购计划单*/
    public static String SYN_PO_SHEET = "financial.synPoSheet";
    /*财务接口同步采购收货单*/
    public static String SYN_PI_SHEET = "financial.synPiSheet";
    /*财务接口同步要货单*/
    public static String SYN_YH_SHEET = "financial.synYhSheet";
    /*财务接口同步采购退货单*/
    public static String SYN_RO_SHEET = "financial.synRoSheet";
    /*财务接口同步配出库单*/
    public static String SYN_DO_SHEET = "financial.synDoSheet";
    /*财务接口同步配送退货单*/
    public static String SYN_DR_SHEET = "financial.synDrSheet";
    /*财务接口同步报损报溢*/
    public static String SYN_LOSS_AND_OVER_FLOW = "financial.synLossAndOverflow";
    /*财务接口同步转仓单*/
    public static String SYN_IO_SHEET = "financial.synIoSheet";

    /*财务接口同步货主采购计划单*/
    public static String SYN_PL_SHEET = "financial.synPlSheet";
    /*财务接口同步货主出库单*/
    public static String SYN_SH_SHEET = "financial.synShSheet";

    /*天壹接口同步资金账户*/
    public static String BANK_ACCOUNT = "bankAccount";
    /*天壹接口同步门店信息*/
    public static String CLIENT_INFO = "clientInfo";
    /*同步商品类别*/
    public static String SYN_BD_ITEM_CLS = "financial.synBdItemCls";
    /*同步商品品牌*/
    public static String SYN_BD_ITEM_BRAND = "financial.synBdItemBrand";
    /*同步库存接口地址*/
    public static String SYN_IM_BRANCH_STOCK = "financial.synImBranchStock";
    /*同步订单状态接口地址*/
    public static String SYN_IM_SHEET_STATUS = "financial.synImSheetStatus";
    /*同步出库单金额接口地址*/
    public static String SYN_DO_AMT = "financial.syncDoAmt";

    /*默认工厂调用财务系统渠道*/
    public static String FINANCIAL_TYPE_DEFAULT = "default";

    /*SAP财务系统渠道*/
    public static String FINANCIAL_TYPE_SAP = "SAP";

    /*西宁财务系统渠道*/
    public static String FINANCIAL_TYPE_XN = "XN";

    /*迁徙系统渠道*/
    public static String FINANCIAL_TYPE_QX = "QX";

    /*每一天系统渠道*/
    public static String FINANCIAL_TYPE_MYT = "MYT";

    /*同福系统*/
    public static String FINANCIAL_TYPE_TF = "TF";

    /*天壹系统渠道*/
    public static String FINANCIAL_TYPE_TY = "TY";

    /*富勒系统渠道*/
    public static String FINANCIAL_TYPE_FlUX = "FLUX";

    /*可视化接口渠道*/
    public static String FINANCIAL_TYPE_CUSTOM = "CUSTOM";

    /*富勒系统门店供应商同步方法*/
    public static String FINANCIAL_FLUX_METHOD_PUT_CUSTOMER = "putCustomer";

    /*富勒系统配送中心商品同步方法*/
    public static String FINANCIAL_FLUX_METHOD_PUT_SKU = "putSKU";

    /*富勒系统采购计划单/退货入库单同步方法*/

    public static String FINANCIAL_FLUX_METHOD_PUT_ASN = "putASN";

    /*富勒系统退货出库单同步方法*/
    public static String FINANCIAL_FLUX_METHOD_PUT_SO_ORDER = "putSalesOrder";
    /*富勒系统出库单相关金额同步方法*/
    public static String FINANCIAL_FLUX_METHOD_AMOUNT = "AMOUNT";

    /*迁徙系统门店同步接口方法*/
    public static String FINANCIAL_METHOD_STORE = "wmsSyncStore";
    /*迁徙系统商品同步接口方法*/
    public static String FINANCIAL_METHOD_ITEM = "wmsSyncSku";
    /*迁徙系统订单同步接口方法*/
    public static String FINANCIAL_METHOD_DOORDER = "wmsPurchInOrder";
    /*迁徙系统退单同步接口方法*/
    public static String FINANCIAL_METHOD_DRORDER = "wmsPurchOutOrder";

    /*迁徙系统同步商品品牌方法名*/
    public static String WMS_SYNC_BRAND = "wmsSyncBrand";

    /*迁徙系统同步商品类别方法名*/
    public static String WMS_SYNC_ICAT = "wmsSyncIcat";

    /*迁徙系统门店同步日志类型*/
    public static String FINANCIAL_SYNC_STORE = "syncStore";
    /*迁徙系统商品同步日志类型*/
    public static String FINANCIAL_SYNC_ITEM = "syncSku";
    /*迁徙系统订单同步日志类型*/
    public static String FINANCIAL_SYNC_DOORDER = "syncPurchInOrder";
    /*迁徙系统退单同步日志类型*/
    public static String FINANCIAL_SYNC_DRORDER = "syncPurchOutOrder";

    /*迁徙系统同步商品品牌日志类型*/
    public static String SYNC_BRAND = "syncBrand";

    /*迁徙系统同步商品类别日志类型*/
    public static String SYNC_ICAT = "syncIcat";

    /*同福系统配送中心库存同步接口方法*/
    public static String FINANCIAL_METHOD_IMBRANCHSTOCK = "syncImBranchStock";
    /*同福系统订单状态同步接口方法*/
    public static String FINANCIAL_METHOD_IMSHEETSTATUS = "syncImSheetStatus";

    public static final String HEADER_ENCODING = "UTF-8";
    public static final boolean HEADER_NO_CACHE = true;
    public static final String HEADER_JSON_CONTENT_TYPE = "json/plain";

    public static Date StringForDate(String date) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.parse(date);
    }
    public static String dateForString(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    public static String getYYMMDDDateTime() {
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        return format.format(new Date(System.currentTimeMillis()));
    }

    /**
     * 数据分组 1000条分组
     * yangzc
     * 方法名:returnGruopList
     * 2019年4月10日
     * @param list
     * @return
     * returnType List<List>
     */
    public static List<List> returnGruopList(List list ){
        List<List> returnList = new ArrayList<List>();
        List groupList = new ArrayList();
        if(list.size()<=1000){
            returnList.add(list);
            return returnList;
        }else{
            for(int i=0;i<list.size() ; i++){
                groupList.add(list.get(i));
                if(groupList.size()==1000){
                    returnList.add(groupList);
                    groupList = new ArrayList();
                }else if(groupList.size()<1000&&groupList.size()==i+1){
                    returnList.add(groupList);
                    groupList = new ArrayList();
                }
            }
        }
        return returnList;
    }



}
