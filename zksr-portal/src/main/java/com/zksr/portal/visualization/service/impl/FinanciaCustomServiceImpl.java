package com.zksr.portal.visualization.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.zksr.common.core.constant.OpenApiConstants;
import com.zksr.common.core.utils.HttpClienUtils;
import com.zksr.common.core.utils.ZkRSAUtils;
import com.zksr.common.core.domain.vo.openapi.CustomApiDetail;
import com.zksr.common.core.domain.vo.openapi.CustomApiMaster;
import com.zksr.common.core.utils.JsonUtil;
import com.zksr.portal.visualization.service.FinanciaService;
import com.zksr.trade.api.order.OrderApi;
import com.zksr.common.core.domain.vo.openapi.TradeOutErpRequest;
import com.zksr.common.core.domain.vo.openapi.TrdSupAfterDtlRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.security.interfaces.RSAPublicKey;
import java.util.*;


/**
 * <AUTHOR>
 * @create 2024-03-16
 */
@Slf4j
@Service
public class FinanciaCustomServiceImpl implements FinanciaService {


    @Resource
    private OrderApi orderApi;


    @Override
    public String synErpThSheet(Object data) {
        if(data instanceof TrdSupAfterDtlRequest){
            TrdSupAfterDtlRequest trdSupAfterDtlRequest=(TrdSupAfterDtlRequest)data;
            try {
                CustomApiMaster apiMaster = new CustomApiMaster();
                apiMaster.setModelType(OpenApiConstants.SHEET_MODEL_TYPE_TH);
                apiMaster.setApiStatus(1);
                apiMaster.setSystemType(OpenApiConstants.SYSTEM_TYPE_WMS);
                CustomApiMaster master = orderApi.getCustomApiMaster(apiMaster);
                if (Objects.isNull(master)) {
                    return null;
                }
                List<CustomApiDetail> apiDetails = orderApi.getCustomApiDetail(master.getApiNo());
                if (OpenApiConstants.SYSTEM_TYPE_WMS.equals(apiMaster.getSystemType())){
                    for (CustomApiDetail apiDetail : apiDetails) {
                        if ("bizData".equals(apiDetail.getFieldName())){
                            String requestData = JSON.toJSONString(trdSupAfterDtlRequest);
                            RSAPublicKey key = ZkRSAUtils.getPublicKey(apiDetail.getDefaultValue());
                            apiDetail.setDefaultValue(ZkRSAUtils.publicEncrypt(requestData, key));
                        }
                    }
                }
                JSONObject jsonRoot = JsonUtil.loadJson(data, Collections.singletonList(trdSupAfterDtlRequest.getDetailList()), apiDetails);
                String url = master.getApiAddress();
                String s = HttpClienUtils.doPost(url, jsonRoot);
                return s;
            } catch (Exception e) {

                log.error("下发可视化要货单 接口报错：", e);
            }
        }
        return null;
    }

    @Override
    public String synErpXhSheet(Object data) {
        if (data instanceof TradeOutErpRequest){
            TradeOutErpRequest tradeOutErpRequest=(TradeOutErpRequest) data;
            try {
                CustomApiMaster apiMaster = new CustomApiMaster();
                apiMaster.setModelType(OpenApiConstants.SHEET_MODEL_TYPE_XS);
                apiMaster.setApiStatus(1); //启用
                apiMaster.setSystemType(OpenApiConstants.SYSTEM_TYPE_ERP);
                CustomApiMaster master = orderApi.getCustomApiMaster(apiMaster);
                if (Objects.isNull(master)) {
                    return null;
                }
                List<CustomApiDetail> apiDetails = orderApi.getCustomApiDetail(master.getApiNo());
                if (OpenApiConstants.SYSTEM_TYPE_ERP.equals(apiMaster.getSystemType())){
                    for (CustomApiDetail apiDetail : apiDetails) {
                        if ("bizData".equals(apiDetail.getFieldName())){
                            String requestData = JSON.toJSONString(tradeOutErpRequest);
                            RSAPublicKey key = ZkRSAUtils.getPublicKey(apiDetail.getDefaultValue());
                            apiDetail.setDefaultValue(ZkRSAUtils.publicEncrypt(requestData, key));
                        }
                    }
                }
                JSONObject jsonRoot = JsonUtil.loadJson(data, Collections.singletonList(tradeOutErpRequest.getDetailList()), apiDetails);
                String url = master.getApiAddress();
                String s = HttpClienUtils.doPost(url, jsonRoot);
                return s;
            } catch (Exception e) {

                log.error("下发可视化要货单 接口报错：", e);
            }
        }
//        else if () {
//
//        }


        return null;
    }


}
