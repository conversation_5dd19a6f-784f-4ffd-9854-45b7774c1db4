<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zksr</artifactId>
        <groupId>com.zksr</groupId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zksr-portal</artifactId>

    <description>
        zksr-portal 门户
    </description>

    <dependencies>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Security-->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-third</artifactId>
        </dependency>

        <!-- Zksr-Mall Common Swagger-->
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-file</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-common-rocketmq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-system</artifactId>
        </dependency>


        <!--单元测试-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.zksr</groupId>
            <artifactId>zksr-api-portal</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
