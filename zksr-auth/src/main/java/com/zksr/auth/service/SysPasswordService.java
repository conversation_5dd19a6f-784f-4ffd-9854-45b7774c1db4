package com.zksr.auth.service;

import java.util.concurrent.TimeUnit;

import com.zksr.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.Constants;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysUser;

/**
 * 登录密码方法
 * 
 * <AUTHOR>
 */
@Component
public class SysPasswordService
{
    @Autowired
    private RedisService redisService;

    private int maxRetryCount = CacheConstants.PASSWORD_MAX_RETRY_COUNT;

    private Long lockTime = CacheConstants.PASSWORD_LOCK_TIME;

    @Autowired
    private SysRecordLogService recordLogService;

    /**
     * 登录账户密码错误次数缓存键名
     * 
     * @param username 用户名
     * @return 缓存键key
     */
    private String getCacheKey(String username)
    {
        return CacheConstants.PWD_ERR_CNT_KEY + username;
    }

    public void validate(SysUser user, String password)
    {
        String username = user.getUserName();

        Integer retryCount = redisService.getCacheObject(getCacheKey(username));

        if (retryCount == null)
        {
            retryCount = 0;
        }

        if (retryCount >= Integer.valueOf(maxRetryCount).intValue())
        {
            String errMsg = String.format("密码输入错误%s次，帐户锁定%s分钟", maxRetryCount, lockTime);
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL,errMsg);
            throw new ServiceException(errMsg);
        }

        if (!matches(user, password))
        {
            retryCount = retryCount + 1;
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, String.format("密码输入错误%s次", retryCount));
            redisService.setCacheObject(getCacheKey(username), retryCount, lockTime, TimeUnit.MINUTES);
            String errMsg;
            if (maxRetryCount - retryCount == 0) {
                errMsg = StringUtils.format("已错误{}次, 锁定账户{}分钟", maxRetryCount, lockTime);
            } else {
                errMsg = StringUtils.format("还有{}次机会, 将锁定账户{}分钟", maxRetryCount - retryCount, lockTime);
            }
            throw new ServiceException("用户不存在/密码错误, " + errMsg);
        }
        else
        {
            clearLoginRecordCache(username);
        }
    }

    public boolean matches(SysUser user, String rawPassword)
    {
        return SecurityUtils.matchesPassword(rawPassword, user.getPassword());
    }

    public void clearLoginRecordCache(String loginName)
    {
        if (redisService.hasKey(getCacheKey(loginName)))
        {
            redisService.deleteObject(getCacheKey(loginName));
        }
    }
}
