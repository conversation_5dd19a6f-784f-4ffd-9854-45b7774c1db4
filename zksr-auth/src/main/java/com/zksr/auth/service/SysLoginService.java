package com.zksr.auth.service;

import cn.hutool.core.util.ObjectUtil;
import com.zksr.auth.form.LoginSmsRequest;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.Constants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.constant.UserConstants;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.enums.UserStatus;
import com.zksr.common.core.exception.ServiceException;
import com.zksr.common.core.text.Convert;
import com.zksr.common.core.utils.ServletUtils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.utils.ToolUtil;
import com.zksr.common.core.utils.ip.IpUtils;
import com.zksr.common.redis.service.RedisService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.RemoteUserService;
import com.zksr.system.api.domain.SysUser;
import com.zksr.system.api.domain.SysUserUpdatePwdVO;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.sms.SmsApi;
import com.zksr.system.api.sms.dto.SmsCodeReqDTO;
import com.zksr.system.api.sms.dto.SmsCodeRespDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidDTO;
import com.zksr.system.api.sms.dto.SmsCodeValidRespDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 登录校验方法
 * 
 * <AUTHOR>
 */
@Component
public class SysLoginService
{
    @Autowired
    private RemoteUserService remoteUserService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired
    private SysRecordLogService recordLogService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SmsApi smsApi;

    @Value("${b2b.saas.auth.switch:false}")
    private Boolean saasAuthSwitch;

    /**
     * 登录
     */
    public LoginUser login(String username, String password)
    {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户/密码必须填写");
            throw new ServiceException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
//        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
//                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
//        {
//            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户密码不在指定范围");
//            throw new ServiceException("用户密码不在指定范围");
//        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户名不在指定范围");
            throw new ServiceException("用户名不在指定范围");
        }
        // IP黑名单校验
        String blackStr = Convert.toStr(redisService.getCacheObject(CacheConstants.SYS_LOGIN_BLACKIPLIST));
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "很遗憾，访问IP已被列入系统黑名单");
            throw new ServiceException("很遗憾，访问IP已被列入系统黑名单");
        }
        // 查询用户信息
        R<LoginUser> userResult = remoteUserService.getUserInfo(username, SecurityConstants.INNER);

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            // 用戶不存在也记录密码错误次数，用于多次错误后登录界面需要输入验证码判断
            Integer retryCount = redisService.getCacheObject(CacheConstants.PWD_ERR_CNT_KEY + username);
            if (ObjectUtil.isEmpty(retryCount)) {
                retryCount = 0;
            }
            retryCount++;
            redisService.setCacheObject(CacheConstants.PWD_ERR_CNT_KEY + username, retryCount, CacheConstants.PASSWORD_LOCK_TIME, TimeUnit.MINUTES);
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "登录用户不存在");
            throw new ServiceException("用户不存在/密码错误");
        }

        if (R.FAIL == userResult.getCode())
        {
            throw new ServiceException(userResult.getMsg());
        }
        
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "对不起，您的账号已被删除");
            throw new ServiceException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus()))
        {
            recordLogService.recordLogininfor(username, Constants.LOGIN_FAIL, "用户已停用，请联系管理员");
            throw new ServiceException("对不起，您的账号：" + username + " 已停用");
        }
        passwordService.validate(user, password);
        recordLogService.recordLogininfor(username, Constants.LOGIN_SUCCESS, "登录成功");
        return userInfo;
    }

    public void logout(String loginName)
    {
        recordLogService.recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String username, String password)
    {
        if(!saasAuthSwitch){
            // 用户名或密码为空 错误
            if (StringUtils.isAnyBlank(username, password))
            {
                throw new ServiceException("用户/密码必须填写");
            }
            if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                    || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
            {
                throw new ServiceException("密码长度必须在8到16个字符之间");
            }
            if ( !Pattern.matches(UserConstants.PASSWORD_PATTERN, password)) {
                throw new ServiceException("必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
            }
        }else {
            // 用户名或密码为空 错误
            if (StringUtils.isAnyBlank(username, password))
            {
                throw new ServiceException("用户必须填写");
            }
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)
        {
            throw new ServiceException("账户长度必须在2到20个字符之间");
        }
        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(saasAuthSwitch ? "" : SecurityUtils.encryptPassword(password));
        R<?> registerResult = remoteUserService.registerUserInfo(sysUser, SecurityConstants.INNER);

        if (R.FAIL == registerResult.getCode())
        {
            throw new ServiceException(registerResult.getMsg());
        }
        recordLogService.recordLogininfor(username, Constants.REGISTER, "注册成功");
    }

    public void passwordCheck(String password){
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)
        {
            throw new ServiceException("密码长度必须在8到16个字符之间");
        }
        if (!Pattern.matches(UserConstants.PASSWORD_PATTERN, password)) {
            throw new ServiceException("必须包含至少一个字母、一个数字和一个特殊字符，长度在8到16个字符之间");
        }
    }

    /**
     * 获取验证码
     * @param loginSmsReq
     * @return
     */
    public String sendSmsCode(LoginSmsRequest loginSmsReq){
        String ip = ServletUtils.getClientIP();
        String key = "login_sms_" + loginSmsReq.getPhone() + "_" + ip;
        //校验缓存信息,缓存时间一分钟
        Object smsCountObj = redisService.getCacheObject(key);
        if (ObjectUtil.isNotNull(smsCountObj)) {
            throw new ServiceException("请勿频繁获取验证码");
        }
        // 根据手机号获取用户账户信息
        SysUser userResult = getUserByPhone(loginSmsReq.getPhone(), loginSmsReq.getSource());

        //获取Sms验证码
        SmsCodeReqDTO smsCodeReqDTO = new SmsCodeReqDTO(userResult.getSysCode(), loginSmsReq.getPhone(), ip);
        SmsCodeRespDTO checkedData = smsApi.sendSmsCode(smsCodeReqDTO).getCheckedData();
        if (Objects.isNull(checkedData) || org.apache.commons.lang3.StringUtils.isBlank(checkedData.getCode())) {
            throw new ServiceException("获取验证码失败");
        }
        redisService.setCacheObject(key, checkedData.getCode(), 60L, TimeUnit.SECONDS);
        return "获取验证码成功";
    }

    /**
     * 校验验证码
     * @param loginSmsReq
     * @return
     */
    public SmsCodeValidRespDTO checkSmsCode(LoginSmsRequest loginSmsReq){
        SmsCodeValidDTO smsCodeValidDTO = new SmsCodeValidDTO(loginSmsReq.getPhone(), loginSmsReq.getCode(), ServletUtils.getClientIP());
        SmsCodeValidRespDTO result = smsApi.validateSmsCode(smsCodeValidDTO).getCheckedData();
        if (Objects.equals(result.getSuccess(), Boolean.FALSE)){
            throw new ServiceException(result.getErrMsg());
        }
        return result;
    }

    /**
     * 更新用户密码
     * @param updatePwd
     */
    public Boolean updatePwd(SysUserUpdatePwdVO updatePwd){
        // 根据手机号获取用户账户信息
        SysUser userResult = getUserByPhone(updatePwd.getPhone(), updatePwd.getSource());
        //密码规则校验
        passwordCheck(updatePwd.getPassword());
        updatePwd.setUserName(userResult.getUserName());
        updatePwd.setPassword(SecurityUtils.encryptPassword(updatePwd.getPassword()));
        return remoteUserService.updateUserPwd(updatePwd).getCheckedData() > 0;
    }

    /**
     * 根据手机号获取用户账户信息
     * @param phone
     * @param source
     * @return
     */
    public SysUser getUserByPhone(String phone, String source){
        SysUser userResult = remoteUserService.getSysUserByPhone(phone).getCheckedData();
        if (ToolUtil.isEmpty(userResult) ||
                (ToolUtil.isNotEmpty(source) && Objects.equals(source, "colonel") && ToolUtil.isEmpty(userResult.getColonelId())) ||
                (ToolUtil.isNotEmpty(source) && Objects.equals(source, "supplier") && ToolUtil.isEmpty(userResult.getSupplierId())) ) {
            throw new ServiceException("手机号对应账户不存在！");
        }
        return userResult;
    }
}
