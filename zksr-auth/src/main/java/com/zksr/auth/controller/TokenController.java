package com.zksr.auth.controller;

import com.zksr.auth.form.LoginBody;
import com.zksr.auth.form.LoginSmsRequest;
import com.zksr.auth.form.RegisterBody;
import com.zksr.auth.service.SysLoginService;
import com.zksr.common.core.constant.CacheConstants;
import com.zksr.common.core.constant.SecurityConstants;
import com.zksr.common.core.domain.R;
import com.zksr.common.core.utils.JwtUtils;
import com.zksr.common.core.utils.Md5Utils;
import com.zksr.common.core.utils.StringUtils;
import com.zksr.common.core.web.pojo.CommonResult;
import com.zksr.common.security.auth.AuthUtil;
import com.zksr.common.security.service.TokenService;
import com.zksr.common.security.utils.SecurityUtils;
import com.zksr.system.api.domain.SysUserUpdatePwdVO;
import com.zksr.system.api.model.LoginUser;
import com.zksr.system.api.sms.dto.SmsCodeValidRespDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import static com.zksr.common.core.web.pojo.CommonResult.success;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
public class TokenController
{
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @PostMapping("login")
    public R<?> login(@RequestBody LoginBody form)
    {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username);
            return R.ok();
        }

        String userKey = request.getHeader(SecurityConstants.USER_KEY);
        if(StringUtils.isNotEmpty(userKey)){
            // 删除用户缓存记录
            AuthUtil.logoutByUserKey(userKey);
        }

        String saasToken = SecurityUtils.getSaasToken();
        if(StringUtils.isNotEmpty(saasToken)){
            String b2bUserCacheKey = String.format("%s%s", CacheConstants.LOGIN_TOKEN_KEY, Md5Utils.md5(saasToken));
            tokenService.delRedisByKey(b2bUserCacheKey);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request)
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser))
        {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<?> register(@RequestBody RegisterBody registerBody)
    {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }

    @ApiOperation("用户获取验证码")
    @PostMapping("/sendSmsCode")
    public CommonResult<String> sendSmsCode(@Valid @RequestBody LoginSmsRequest loginSmsReq) {
        return success(sysLoginService.sendSmsCode(loginSmsReq));
    }

    @ApiOperation("校验验证码")
    @PostMapping("/checkSmsCode")
    public CommonResult<SmsCodeValidRespDTO> checkSmsCode(@Valid @RequestBody LoginSmsRequest loginSmsReq) {
        return success(sysLoginService.checkSmsCode(loginSmsReq));
    }

    @ApiOperation("更新密码")
    @PostMapping(value = "/updatePwd")
    public CommonResult<Boolean> updatePwd(@RequestBody @Valid SysUserUpdatePwdVO request) {
        return success(sysLoginService.updatePwd(request));
    }
}
